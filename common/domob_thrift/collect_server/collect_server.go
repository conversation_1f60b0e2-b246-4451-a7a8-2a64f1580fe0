// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package collect_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/airport_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/p2p_server"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = airport_types.GoUnusedProtection__
var _ = p2p_server.GoUnusedProtection__

type CollectServer interface {
	p2p_server.P2pServer

	// airport心跳，并获取信号
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Host: hostname
	//  - RunningVersion: 正在运行的版本
	//  - CurrentVersion: 当前机器上的版本
	//  - StartupTime: 启动时间
	//  - RunDirSize: 运行目录大小
	AirportBeatHeart(requestHeader *common.RequestHeader, host string, runningVersion string, currentVersion string, startupTime int32, runDirSize int64) (r *airport_types.ResultSignal, err error)
	// terminal心跳，并获取信号
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Host: hostname
	//  - Account: account
	//  - RunningVersion: 正在运行的版本
	//  - CurrentVersion: 当前机器上的版本
	//  - StartupTime: 启动时间
	TerminalBeatHeart(requestHeader *common.RequestHeader, host string, account string, runningVersion string, currentVersion string, startupTime int32) (r *airport_types.ResultSignal, err error)
	// 获取flight信息
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - FlightId: Flight ID
	GetFlightInfo(requestHeader *common.RequestHeader, flightId int32) (r *airport_types.ResultFlightInfo, err error)
	// 汇报状态
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - FlightId: flight ID
	//  - FileStatus: 文件状态
	//  - ExtInfo: 额外信息
	ReportFlightFileStatus(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, flightId int32, fileStatus airport_types.FlightFileStatus, extInfo string) (r *airport_types.ResultBool, err error)
	// 是否能删除airport侧的文件
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Host: hostname
	//  - TopicInfo: topic信息
	CanDeleteAirportFile(requestHeader *common.RequestHeader, host string, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 我的airport侧的文件是否已完成
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Host: hostname
	//  - TopicInfo: topic信息
	IsMyAirportFileFinished(requestHeader *common.RequestHeader, host string, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 重置未处理完的航班文件状态
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Host: hostname
	ResetNotFinishFlightFile(requestHeader *common.RequestHeader, host string) (r *airport_types.ResultBool, err error)
	// 添加文件，terminal server已拉完
	// 仅供airport server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	AddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
}

type CollectServerClient struct {
	*p2p_server.P2pServerClient
}

func NewCollectServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *CollectServerClient {
	return &CollectServerClient{P2pServerClient: p2p_server.NewP2pServerClientFactory(t, f)}
}

func NewCollectServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *CollectServerClient {
	return &CollectServerClient{P2pServerClient: p2p_server.NewP2pServerClientProtocol(t, iprot, oprot)}
}

// airport心跳，并获取信号
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Host: hostname
//  - RunningVersion: 正在运行的版本
//  - CurrentVersion: 当前机器上的版本
//  - StartupTime: 启动时间
//  - RunDirSize: 运行目录大小
func (p *CollectServerClient) AirportBeatHeart(requestHeader *common.RequestHeader, host string, runningVersion string, currentVersion string, startupTime int32, runDirSize int64) (r *airport_types.ResultSignal, err error) {
	if err = p.sendAirportBeatHeart(requestHeader, host, runningVersion, currentVersion, startupTime, runDirSize); err != nil {
		return
	}
	return p.recvAirportBeatHeart()
}

func (p *CollectServerClient) sendAirportBeatHeart(requestHeader *common.RequestHeader, host string, runningVersion string, currentVersion string, startupTime int32, runDirSize int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("airportBeatHeart", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAirportBeatHeartArgs()
	args0.RequestHeader = requestHeader
	args0.Host = host
	args0.RunningVersion = runningVersion
	args0.CurrentVersion = currentVersion
	args0.StartupTime = startupTime
	args0.RunDirSize = runDirSize
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvAirportBeatHeart() (value *airport_types.ResultSignal, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAirportBeatHeartResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// terminal心跳，并获取信号
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Host: hostname
//  - Account: account
//  - RunningVersion: 正在运行的版本
//  - CurrentVersion: 当前机器上的版本
//  - StartupTime: 启动时间
func (p *CollectServerClient) TerminalBeatHeart(requestHeader *common.RequestHeader, host string, account string, runningVersion string, currentVersion string, startupTime int32) (r *airport_types.ResultSignal, err error) {
	if err = p.sendTerminalBeatHeart(requestHeader, host, account, runningVersion, currentVersion, startupTime); err != nil {
		return
	}
	return p.recvTerminalBeatHeart()
}

func (p *CollectServerClient) sendTerminalBeatHeart(requestHeader *common.RequestHeader, host string, account string, runningVersion string, currentVersion string, startupTime int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("terminalBeatHeart", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewTerminalBeatHeartArgs()
	args4.RequestHeader = requestHeader
	args4.Host = host
	args4.Account = account
	args4.RunningVersion = runningVersion
	args4.CurrentVersion = currentVersion
	args4.StartupTime = startupTime
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvTerminalBeatHeart() (value *airport_types.ResultSignal, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewTerminalBeatHeartResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// 获取flight信息
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - FlightId: Flight ID
func (p *CollectServerClient) GetFlightInfo(requestHeader *common.RequestHeader, flightId int32) (r *airport_types.ResultFlightInfo, err error) {
	if err = p.sendGetFlightInfo(requestHeader, flightId); err != nil {
		return
	}
	return p.recvGetFlightInfo()
}

func (p *CollectServerClient) sendGetFlightInfo(requestHeader *common.RequestHeader, flightId int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFlightInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetFlightInfoArgs()
	args8.RequestHeader = requestHeader
	args8.FlightId = flightId
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvGetFlightInfo() (value *airport_types.ResultFlightInfo, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetFlightInfoResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 汇报状态
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - FlightId: flight ID
//  - FileStatus: 文件状态
//  - ExtInfo: 额外信息
func (p *CollectServerClient) ReportFlightFileStatus(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, flightId int32, fileStatus airport_types.FlightFileStatus, extInfo string) (r *airport_types.ResultBool, err error) {
	if err = p.sendReportFlightFileStatus(requestHeader, topicInfo, flightId, fileStatus, extInfo); err != nil {
		return
	}
	return p.recvReportFlightFileStatus()
}

func (p *CollectServerClient) sendReportFlightFileStatus(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, flightId int32, fileStatus airport_types.FlightFileStatus, extInfo string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("reportFlightFileStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewReportFlightFileStatusArgs()
	args12.RequestHeader = requestHeader
	args12.TopicInfo = topicInfo
	args12.FlightId = flightId
	args12.FileStatus = fileStatus
	args12.ExtInfo = extInfo
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvReportFlightFileStatus() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewReportFlightFileStatusResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// 是否能删除airport侧的文件
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Host: hostname
//  - TopicInfo: topic信息
func (p *CollectServerClient) CanDeleteAirportFile(requestHeader *common.RequestHeader, host string, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendCanDeleteAirportFile(requestHeader, host, topicInfo); err != nil {
		return
	}
	return p.recvCanDeleteAirportFile()
}

func (p *CollectServerClient) sendCanDeleteAirportFile(requestHeader *common.RequestHeader, host string, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("canDeleteAirportFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewCanDeleteAirportFileArgs()
	args16.RequestHeader = requestHeader
	args16.Host = host
	args16.TopicInfo = topicInfo
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvCanDeleteAirportFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewCanDeleteAirportFileResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

// 我的airport侧的文件是否已完成
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Host: hostname
//  - TopicInfo: topic信息
func (p *CollectServerClient) IsMyAirportFileFinished(requestHeader *common.RequestHeader, host string, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendIsMyAirportFileFinished(requestHeader, host, topicInfo); err != nil {
		return
	}
	return p.recvIsMyAirportFileFinished()
}

func (p *CollectServerClient) sendIsMyAirportFileFinished(requestHeader *common.RequestHeader, host string, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("isMyAirportFileFinished", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewIsMyAirportFileFinishedArgs()
	args20.RequestHeader = requestHeader
	args20.Host = host
	args20.TopicInfo = topicInfo
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvIsMyAirportFileFinished() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewIsMyAirportFileFinishedResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 重置未处理完的航班文件状态
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Host: hostname
func (p *CollectServerClient) ResetNotFinishFlightFile(requestHeader *common.RequestHeader, host string) (r *airport_types.ResultBool, err error) {
	if err = p.sendResetNotFinishFlightFile(requestHeader, host); err != nil {
		return
	}
	return p.recvResetNotFinishFlightFile()
}

func (p *CollectServerClient) sendResetNotFinishFlightFile(requestHeader *common.RequestHeader, host string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("resetNotFinishFlightFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewResetNotFinishFlightFileArgs()
	args24.RequestHeader = requestHeader
	args24.Host = host
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvResetNotFinishFlightFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewResetNotFinishFlightFileResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	return
}

// 添加文件，terminal server已拉完
// 仅供airport server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *CollectServerClient) AddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendAddFile(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvAddFile()
}

func (p *CollectServerClient) sendAddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewAddFileArgs()
	args28.RequestHeader = requestHeader
	args28.TopicInfo = topicInfo
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CollectServerClient) recvAddFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewAddFileResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	return
}

type CollectServerProcessor struct {
	*p2p_server.P2pServerProcessor
}

func NewCollectServerProcessor(handler CollectServer) *CollectServerProcessor {
	self32 := &CollectServerProcessor{p2p_server.NewP2pServerProcessor(handler)}
	self32.AddToProcessorMap("airportBeatHeart", &collectServerProcessorAirportBeatHeart{handler: handler})
	self32.AddToProcessorMap("terminalBeatHeart", &collectServerProcessorTerminalBeatHeart{handler: handler})
	self32.AddToProcessorMap("getFlightInfo", &collectServerProcessorGetFlightInfo{handler: handler})
	self32.AddToProcessorMap("reportFlightFileStatus", &collectServerProcessorReportFlightFileStatus{handler: handler})
	self32.AddToProcessorMap("canDeleteAirportFile", &collectServerProcessorCanDeleteAirportFile{handler: handler})
	self32.AddToProcessorMap("isMyAirportFileFinished", &collectServerProcessorIsMyAirportFileFinished{handler: handler})
	self32.AddToProcessorMap("resetNotFinishFlightFile", &collectServerProcessorResetNotFinishFlightFile{handler: handler})
	self32.AddToProcessorMap("addFile", &collectServerProcessorAddFile{handler: handler})
	return self32
}

type collectServerProcessorAirportBeatHeart struct {
	handler CollectServer
}

func (p *collectServerProcessorAirportBeatHeart) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAirportBeatHeartArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("airportBeatHeart", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAirportBeatHeartResult()
	if result.Success, err = p.handler.AirportBeatHeart(args.RequestHeader, args.Host, args.RunningVersion, args.CurrentVersion, args.StartupTime, args.RunDirSize); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing airportBeatHeart: "+err.Error())
		oprot.WriteMessageBegin("airportBeatHeart", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("airportBeatHeart", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type collectServerProcessorTerminalBeatHeart struct {
	handler CollectServer
}

func (p *collectServerProcessorTerminalBeatHeart) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewTerminalBeatHeartArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("terminalBeatHeart", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewTerminalBeatHeartResult()
	if result.Success, err = p.handler.TerminalBeatHeart(args.RequestHeader, args.Host, args.Account, args.RunningVersion, args.CurrentVersion, args.StartupTime); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing terminalBeatHeart: "+err.Error())
		oprot.WriteMessageBegin("terminalBeatHeart", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("terminalBeatHeart", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type collectServerProcessorGetFlightInfo struct {
	handler CollectServer
}

func (p *collectServerProcessorGetFlightInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFlightInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFlightInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFlightInfoResult()
	if result.Success, err = p.handler.GetFlightInfo(args.RequestHeader, args.FlightId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFlightInfo: "+err.Error())
		oprot.WriteMessageBegin("getFlightInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFlightInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type collectServerProcessorReportFlightFileStatus struct {
	handler CollectServer
}

func (p *collectServerProcessorReportFlightFileStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewReportFlightFileStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("reportFlightFileStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewReportFlightFileStatusResult()
	if result.Success, err = p.handler.ReportFlightFileStatus(args.RequestHeader, args.TopicInfo, args.FlightId, args.FileStatus, args.ExtInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing reportFlightFileStatus: "+err.Error())
		oprot.WriteMessageBegin("reportFlightFileStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("reportFlightFileStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type collectServerProcessorCanDeleteAirportFile struct {
	handler CollectServer
}

func (p *collectServerProcessorCanDeleteAirportFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCanDeleteAirportFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("canDeleteAirportFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCanDeleteAirportFileResult()
	if result.Success, err = p.handler.CanDeleteAirportFile(args.RequestHeader, args.Host, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing canDeleteAirportFile: "+err.Error())
		oprot.WriteMessageBegin("canDeleteAirportFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("canDeleteAirportFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type collectServerProcessorIsMyAirportFileFinished struct {
	handler CollectServer
}

func (p *collectServerProcessorIsMyAirportFileFinished) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewIsMyAirportFileFinishedArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("isMyAirportFileFinished", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewIsMyAirportFileFinishedResult()
	if result.Success, err = p.handler.IsMyAirportFileFinished(args.RequestHeader, args.Host, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing isMyAirportFileFinished: "+err.Error())
		oprot.WriteMessageBegin("isMyAirportFileFinished", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("isMyAirportFileFinished", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type collectServerProcessorResetNotFinishFlightFile struct {
	handler CollectServer
}

func (p *collectServerProcessorResetNotFinishFlightFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewResetNotFinishFlightFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("resetNotFinishFlightFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewResetNotFinishFlightFileResult()
	if result.Success, err = p.handler.ResetNotFinishFlightFile(args.RequestHeader, args.Host); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing resetNotFinishFlightFile: "+err.Error())
		oprot.WriteMessageBegin("resetNotFinishFlightFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("resetNotFinishFlightFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type collectServerProcessorAddFile struct {
	handler CollectServer
}

func (p *collectServerProcessorAddFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddFileResult()
	if result.Success, err = p.handler.AddFile(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addFile: "+err.Error())
		oprot.WriteMessageBegin("addFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AirportBeatHeartArgs struct {
	RequestHeader  *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Host           string                `thrift:"host,2" json:"host"`
	RunningVersion string                `thrift:"runningVersion,3" json:"runningVersion"`
	CurrentVersion string                `thrift:"currentVersion,4" json:"currentVersion"`
	StartupTime    int32                 `thrift:"startupTime,5" json:"startupTime"`
	RunDirSize     int64                 `thrift:"runDirSize,6" json:"runDirSize"`
}

func NewAirportBeatHeartArgs() *AirportBeatHeartArgs {
	return &AirportBeatHeartArgs{}
}

func (p *AirportBeatHeartArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AirportBeatHeartArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *AirportBeatHeartArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *AirportBeatHeartArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RunningVersion = v
	}
	return nil
}

func (p *AirportBeatHeartArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CurrentVersion = v
	}
	return nil
}

func (p *AirportBeatHeartArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartupTime = v
	}
	return nil
}

func (p *AirportBeatHeartArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.RunDirSize = v
	}
	return nil
}

func (p *AirportBeatHeartArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("airportBeatHeart_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AirportBeatHeartArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *AirportBeatHeartArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *AirportBeatHeartArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runningVersion", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:runningVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RunningVersion)); err != nil {
		return fmt.Errorf("%T.runningVersion (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:runningVersion: %s", p, err)
	}
	return err
}

func (p *AirportBeatHeartArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currentVersion", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:currentVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrentVersion)); err != nil {
		return fmt.Errorf("%T.currentVersion (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:currentVersion: %s", p, err)
	}
	return err
}

func (p *AirportBeatHeartArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startupTime", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startupTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartupTime)); err != nil {
		return fmt.Errorf("%T.startupTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startupTime: %s", p, err)
	}
	return err
}

func (p *AirportBeatHeartArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runDirSize", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:runDirSize: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RunDirSize)); err != nil {
		return fmt.Errorf("%T.runDirSize (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:runDirSize: %s", p, err)
	}
	return err
}

func (p *AirportBeatHeartArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AirportBeatHeartArgs(%+v)", *p)
}

type AirportBeatHeartResult struct {
	Success *airport_types.ResultSignal `thrift:"success,0" json:"success"`
}

func NewAirportBeatHeartResult() *AirportBeatHeartResult {
	return &AirportBeatHeartResult{}
}

func (p *AirportBeatHeartResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AirportBeatHeartResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultSignal()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AirportBeatHeartResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("airportBeatHeart_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AirportBeatHeartResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AirportBeatHeartResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AirportBeatHeartResult(%+v)", *p)
}

type TerminalBeatHeartArgs struct {
	RequestHeader  *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Host           string                `thrift:"host,2" json:"host"`
	Account        string                `thrift:"account,3" json:"account"`
	RunningVersion string                `thrift:"runningVersion,4" json:"runningVersion"`
	CurrentVersion string                `thrift:"currentVersion,5" json:"currentVersion"`
	StartupTime    int32                 `thrift:"startupTime,6" json:"startupTime"`
}

func NewTerminalBeatHeartArgs() *TerminalBeatHeartArgs {
	return &TerminalBeatHeartArgs{}
}

func (p *TerminalBeatHeartArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TerminalBeatHeartArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *TerminalBeatHeartArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *TerminalBeatHeartArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *TerminalBeatHeartArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RunningVersion = v
	}
	return nil
}

func (p *TerminalBeatHeartArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CurrentVersion = v
	}
	return nil
}

func (p *TerminalBeatHeartArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StartupTime = v
	}
	return nil
}

func (p *TerminalBeatHeartArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("terminalBeatHeart_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TerminalBeatHeartArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *TerminalBeatHeartArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *TerminalBeatHeartArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account: %s", p, err)
	}
	return err
}

func (p *TerminalBeatHeartArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runningVersion", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:runningVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RunningVersion)); err != nil {
		return fmt.Errorf("%T.runningVersion (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:runningVersion: %s", p, err)
	}
	return err
}

func (p *TerminalBeatHeartArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currentVersion", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:currentVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrentVersion)); err != nil {
		return fmt.Errorf("%T.currentVersion (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:currentVersion: %s", p, err)
	}
	return err
}

func (p *TerminalBeatHeartArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startupTime", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:startupTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartupTime)); err != nil {
		return fmt.Errorf("%T.startupTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:startupTime: %s", p, err)
	}
	return err
}

func (p *TerminalBeatHeartArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TerminalBeatHeartArgs(%+v)", *p)
}

type TerminalBeatHeartResult struct {
	Success *airport_types.ResultSignal `thrift:"success,0" json:"success"`
}

func NewTerminalBeatHeartResult() *TerminalBeatHeartResult {
	return &TerminalBeatHeartResult{}
}

func (p *TerminalBeatHeartResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TerminalBeatHeartResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultSignal()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *TerminalBeatHeartResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("terminalBeatHeart_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TerminalBeatHeartResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *TerminalBeatHeartResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TerminalBeatHeartResult(%+v)", *p)
}

type GetFlightInfoArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	FlightId      int32                 `thrift:"flightId,2" json:"flightId"`
}

func NewGetFlightInfoArgs() *GetFlightInfoArgs {
	return &GetFlightInfoArgs{}
}

func (p *GetFlightInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFlightInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFlightInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *GetFlightInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFlightInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFlightInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFlightInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:flightId: %s", p, err)
	}
	return err
}

func (p *GetFlightInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFlightInfoArgs(%+v)", *p)
}

type GetFlightInfoResult struct {
	Success *airport_types.ResultFlightInfo `thrift:"success,0" json:"success"`
}

func NewGetFlightInfoResult() *GetFlightInfoResult {
	return &GetFlightInfoResult{}
}

func (p *GetFlightInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFlightInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultFlightInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFlightInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFlightInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFlightInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFlightInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFlightInfoResult(%+v)", *p)
}

type ReportFlightFileStatusArgs struct {
	RequestHeader *common.RequestHeader          `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo       `thrift:"topicInfo,2" json:"topicInfo"`
	FlightId      int32                          `thrift:"flightId,3" json:"flightId"`
	FileStatus    airport_types.FlightFileStatus `thrift:"fileStatus,4" json:"fileStatus"`
	ExtInfo       string                         `thrift:"extInfo,5" json:"extInfo"`
}

func NewReportFlightFileStatusArgs() *ReportFlightFileStatusArgs {
	return &ReportFlightFileStatusArgs{
		FileStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ReportFlightFileStatusArgs) IsSetFileStatus() bool {
	return int64(p.FileStatus) != math.MinInt32-1
}

func (p *ReportFlightFileStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FileStatus = airport_types.FlightFileStatus(v)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("reportFlightFileStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:flightId: %s", p, err)
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileStatus() {
		if err := oprot.WriteFieldBegin("fileStatus", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:fileStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileStatus)); err != nil {
			return fmt.Errorf("%T.fileStatus (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:fileStatus: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:extInfo: %s", p, err)
	}
	return err
}

func (p *ReportFlightFileStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportFlightFileStatusArgs(%+v)", *p)
}

type ReportFlightFileStatusResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewReportFlightFileStatusResult() *ReportFlightFileStatusResult {
	return &ReportFlightFileStatusResult{}
}

func (p *ReportFlightFileStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ReportFlightFileStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("reportFlightFileStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ReportFlightFileStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ReportFlightFileStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ReportFlightFileStatusResult(%+v)", *p)
}

type CanDeleteAirportFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	Host          string                   `thrift:"host,2" json:"host"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,3" json:"topicInfo"`
}

func NewCanDeleteAirportFileArgs() *CanDeleteAirportFileArgs {
	return &CanDeleteAirportFileArgs{}
}

func (p *CanDeleteAirportFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CanDeleteAirportFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *CanDeleteAirportFileArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *CanDeleteAirportFileArgs) readField3(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *CanDeleteAirportFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("canDeleteAirportFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CanDeleteAirportFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *CanDeleteAirportFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *CanDeleteAirportFileArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *CanDeleteAirportFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CanDeleteAirportFileArgs(%+v)", *p)
}

type CanDeleteAirportFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewCanDeleteAirportFileResult() *CanDeleteAirportFileResult {
	return &CanDeleteAirportFileResult{}
}

func (p *CanDeleteAirportFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CanDeleteAirportFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CanDeleteAirportFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("canDeleteAirportFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CanDeleteAirportFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CanDeleteAirportFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CanDeleteAirportFileResult(%+v)", *p)
}

type IsMyAirportFileFinishedArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	Host          string                   `thrift:"host,2" json:"host"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,3" json:"topicInfo"`
}

func NewIsMyAirportFileFinishedArgs() *IsMyAirportFileFinishedArgs {
	return &IsMyAirportFileFinishedArgs{}
}

func (p *IsMyAirportFileFinishedArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsMyAirportFileFinishedArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *IsMyAirportFileFinishedArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *IsMyAirportFileFinishedArgs) readField3(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *IsMyAirportFileFinishedArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isMyAirportFileFinished_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsMyAirportFileFinishedArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *IsMyAirportFileFinishedArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *IsMyAirportFileFinishedArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *IsMyAirportFileFinishedArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsMyAirportFileFinishedArgs(%+v)", *p)
}

type IsMyAirportFileFinishedResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewIsMyAirportFileFinishedResult() *IsMyAirportFileFinishedResult {
	return &IsMyAirportFileFinishedResult{}
}

func (p *IsMyAirportFileFinishedResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IsMyAirportFileFinishedResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *IsMyAirportFileFinishedResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("isMyAirportFileFinished_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IsMyAirportFileFinishedResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *IsMyAirportFileFinishedResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IsMyAirportFileFinishedResult(%+v)", *p)
}

type ResetNotFinishFlightFileArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Host          string                `thrift:"host,2" json:"host"`
}

func NewResetNotFinishFlightFileArgs() *ResetNotFinishFlightFileArgs {
	return &ResetNotFinishFlightFileArgs{}
}

func (p *ResetNotFinishFlightFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetNotFinishFlightFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResetNotFinishFlightFileArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *ResetNotFinishFlightFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetNotFinishFlightFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetNotFinishFlightFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResetNotFinishFlightFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *ResetNotFinishFlightFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetNotFinishFlightFileArgs(%+v)", *p)
}

type ResetNotFinishFlightFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewResetNotFinishFlightFileResult() *ResetNotFinishFlightFileResult {
	return &ResetNotFinishFlightFileResult{}
}

func (p *ResetNotFinishFlightFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetNotFinishFlightFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ResetNotFinishFlightFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetNotFinishFlightFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetNotFinishFlightFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ResetNotFinishFlightFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetNotFinishFlightFileResult(%+v)", *p)
}

type AddFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewAddFileArgs() *AddFileArgs {
	return &AddFileArgs{}
}

func (p *AddFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *AddFileArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *AddFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *AddFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *AddFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFileArgs(%+v)", *p)
}

type AddFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewAddFileResult() *AddFileResult {
	return &AddFileResult{}
}

func (p *AddFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFileResult(%+v)", *p)
}
