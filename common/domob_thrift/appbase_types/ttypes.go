// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appbase_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

type HouseAdPosition int64

const (
	HouseAdPosition_HAP_UNKNOWN HouseAdPosition = 0
	HouseAdPosition_HAP_TOP     HouseAdPosition = 1
	HouseAdPosition_HAP_BOTTOM  HouseAdPosition = 2
	HouseAdPosition_HAP_RANDOM  HouseAdPosition = 3
)

func (p HouseAdPosition) String() string {
	switch p {
	case HouseAdPosition_HAP_UNKNOWN:
		return "HouseAdPosition_HAP_UNKNOWN"
	case HouseAdPosition_HAP_TOP:
		return "HouseAdPosition_HAP_TOP"
	case HouseAdPosition_HAP_BOTTOM:
		return "HouseAdPosition_HAP_BOTTOM"
	case HouseAdPosition_HAP_RANDOM:
		return "HouseAdPosition_HAP_RANDOM"
	}
	return "<UNSET>"
}

func HouseAdPositionFromString(s string) (HouseAdPosition, error) {
	switch s {
	case "HouseAdPosition_HAP_UNKNOWN":
		return HouseAdPosition_HAP_UNKNOWN, nil
	case "HouseAdPosition_HAP_TOP":
		return HouseAdPosition_HAP_TOP, nil
	case "HouseAdPosition_HAP_BOTTOM":
		return HouseAdPosition_HAP_BOTTOM, nil
	case "HouseAdPosition_HAP_RANDOM":
		return HouseAdPosition_HAP_RANDOM, nil
	}
	return HouseAdPosition(math.MinInt32 - 1), fmt.Errorf("not a valid HouseAdPosition string")
}

type AppPublisher struct {
	PublisherId common.UidInt `thrift:"publisherId,1" json:"publisherId"`
	Uid         common.UidInt `thrift:"uid,2" json:"uid"`
	Name        string        `thrift:"name,3" json:"name"`
	FullName    string        `thrift:"fullName,4" json:"fullName"`
	Copyright   string        `thrift:"copyright,5" json:"copyright"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime common.TimeInt `thrift:"createTime,20" json:"createTime"`
	LastUpdate common.TimeInt `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewAppPublisher() *AppPublisher {
	return &AppPublisher{}
}

func (p *AppPublisher) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppPublisher) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PublisherId = common.UidInt(v)
	}
	return nil
}

func (p *AppPublisher) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *AppPublisher) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AppPublisher) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FullName = v
	}
	return nil
}

func (p *AppPublisher) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Copyright = v
	}
	return nil
}

func (p *AppPublisher) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = common.TimeInt(v)
	}
	return nil
}

func (p *AppPublisher) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = common.TimeInt(v)
	}
	return nil
}

func (p *AppPublisher) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppPublisher"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppPublisher) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("publisherId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:publisherId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PublisherId)); err != nil {
		return fmt.Errorf("%T.publisherId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:publisherId: %s", p, err)
	}
	return err
}

func (p *AppPublisher) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AppPublisher) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *AppPublisher) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fullName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:fullName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FullName)); err != nil {
		return fmt.Errorf("%T.fullName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:fullName: %s", p, err)
	}
	return err
}

func (p *AppPublisher) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("copyright", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:copyright: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Copyright)); err != nil {
		return fmt.Errorf("%T.copyright (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:copyright: %s", p, err)
	}
	return err
}

func (p *AppPublisher) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *AppPublisher) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AppPublisher) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppPublisher(%+v)", *p)
}

type AppVersionDetail struct {
	AppId           common.IdInt `thrift:"appId,1" json:"appId"`
	AppVersionId    common.IdInt `thrift:"appVersionId,2" json:"appVersionId"`
	Brief           string       `thrift:"brief,3" json:"brief"`
	Intro           string       `thrift:"intro,4" json:"intro"`
	Features        []string     `thrift:"features,5" json:"features"`
	PreviewImageUrl []string     `thrift:"previewImageUrl,6" json:"previewImageUrl"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	ExtInfo map[string]string `thrift:"extInfo,50" json:"extInfo"`
}

func NewAppVersionDetail() *AppVersionDetail {
	return &AppVersionDetail{}
}

func (p *AppVersionDetail) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.MAP {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppVersionDetail) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppId = common.IdInt(v)
	}
	return nil
}

func (p *AppVersionDetail) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppVersionId = common.IdInt(v)
	}
	return nil
}

func (p *AppVersionDetail) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Brief = v
	}
	return nil
}

func (p *AppVersionDetail) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Intro = v
	}
	return nil
}

func (p *AppVersionDetail) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Features = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Features = append(p.Features, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppVersionDetail) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PreviewImageUrl = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.PreviewImageUrl = append(p.PreviewImageUrl, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppVersionDetail) readField50(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = v
		}
		var _val3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = v
		}
		p.ExtInfo[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AppVersionDetail) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppVersionDetail"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppVersionDetail) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appId: %s", p, err)
	}
	return err
}

func (p *AppVersionDetail) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appVersionId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appVersionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppVersionId)); err != nil {
		return fmt.Errorf("%T.appVersionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appVersionId: %s", p, err)
	}
	return err
}

func (p *AppVersionDetail) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brief", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:brief: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Brief)); err != nil {
		return fmt.Errorf("%T.brief (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:brief: %s", p, err)
	}
	return err
}

func (p *AppVersionDetail) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("intro", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:intro: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Intro)); err != nil {
		return fmt.Errorf("%T.intro (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:intro: %s", p, err)
	}
	return err
}

func (p *AppVersionDetail) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Features != nil {
		if err := oprot.WriteFieldBegin("features", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:features: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Features)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Features {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:features: %s", p, err)
		}
	}
	return err
}

func (p *AppVersionDetail) writeField6(oprot thrift.TProtocol) (err error) {
	if p.PreviewImageUrl != nil {
		if err := oprot.WriteFieldBegin("previewImageUrl", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:previewImageUrl: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.PreviewImageUrl)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PreviewImageUrl {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:previewImageUrl: %s", p, err)
		}
	}
	return err
}

func (p *AppVersionDetail) writeField50(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 50); err != nil {
			return fmt.Errorf("%T write field begin error 50:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 50:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *AppVersionDetail) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppVersionDetail(%+v)", *p)
}

type AppVersion struct {
	AppVersionId  common.IdInt      `thrift:"appVersionId,1" json:"appVersionId"`
	AppId         common.IdInt      `thrift:"appId,2" json:"appId"`
	Publisher     *AppPublisher     `thrift:"publisher,3" json:"publisher"`
	OwnerMediaUid common.UidInt     `thrift:"ownerMediaUid,4" json:"ownerMediaUid"`
	AdPlanId      common.IdInt      `thrift:"adPlanId,5" json:"adPlanId"`
	AdStrategyId  common.IdInt      `thrift:"adStrategyId,6" json:"adStrategyId"`
	AdCreativeId  common.IdInt      `thrift:"adCreativeId,7" json:"adCreativeId"`
	Version       string            `thrift:"version,8" json:"version"`
	Name          string            `thrift:"name,9" json:"name"`
	Brief         string            `thrift:"brief,10" json:"brief"`
	IconUrl       string            `thrift:"iconUrl,11" json:"iconUrl"`
	PackageUrl    string            `thrift:"packageUrl,12" json:"packageUrl"`
	PackageSize   int32             `thrift:"packageSize,13" json:"packageSize"`
	Price         common.Amount     `thrift:"price,14" json:"price"`
	Detail        *AppVersionDetail `thrift:"detail,15" json:"detail"`
	Sequence      int32             `thrift:"sequence,16" json:"sequence"`
	PkgName       string            `thrift:"pkgName,17" json:"pkgName"`
	Screenshots   []string          `thrift:"screenshots,18" json:"screenshots"`
	Filename      string            `thrift:"filename,19" json:"filename"`
	CreateTime    common.TimeInt    `thrift:"createTime,20" json:"createTime"`
	LastUpdate    common.TimeInt    `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewAppVersion() *AppVersion {
	return &AppVersion{}
}

func (p *AppVersion) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppVersion) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppVersionId = common.IdInt(v)
	}
	return nil
}

func (p *AppVersion) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = common.IdInt(v)
	}
	return nil
}

func (p *AppVersion) readField3(iprot thrift.TProtocol) error {
	p.Publisher = NewAppPublisher()
	if err := p.Publisher.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Publisher)
	}
	return nil
}

func (p *AppVersion) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OwnerMediaUid = common.UidInt(v)
	}
	return nil
}

func (p *AppVersion) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AdPlanId = common.IdInt(v)
	}
	return nil
}

func (p *AppVersion) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AdStrategyId = common.IdInt(v)
	}
	return nil
}

func (p *AppVersion) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AdCreativeId = common.IdInt(v)
	}
	return nil
}

func (p *AppVersion) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *AppVersion) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AppVersion) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Brief = v
	}
	return nil
}

func (p *AppVersion) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.IconUrl = v
	}
	return nil
}

func (p *AppVersion) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PackageUrl = v
	}
	return nil
}

func (p *AppVersion) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.PackageSize = v
	}
	return nil
}

func (p *AppVersion) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Price = common.Amount(v)
	}
	return nil
}

func (p *AppVersion) readField15(iprot thrift.TProtocol) error {
	p.Detail = NewAppVersionDetail()
	if err := p.Detail.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Detail)
	}
	return nil
}

func (p *AppVersion) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Sequence = v
	}
	return nil
}

func (p *AppVersion) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *AppVersion) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Screenshots = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.Screenshots = append(p.Screenshots, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppVersion) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Filename = v
	}
	return nil
}

func (p *AppVersion) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = common.TimeInt(v)
	}
	return nil
}

func (p *AppVersion) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = common.TimeInt(v)
	}
	return nil
}

func (p *AppVersion) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppVersion"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppVersion) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appVersionId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appVersionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppVersionId)); err != nil {
		return fmt.Errorf("%T.appVersionId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appVersionId: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appId: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Publisher != nil {
		if err := oprot.WriteFieldBegin("publisher", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:publisher: %s", p, err)
		}
		if err := p.Publisher.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Publisher)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:publisher: %s", p, err)
		}
	}
	return err
}

func (p *AppVersion) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ownerMediaUid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ownerMediaUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OwnerMediaUid)); err != nil {
		return fmt.Errorf("%T.ownerMediaUid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ownerMediaUid: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:adPlanId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanId)); err != nil {
		return fmt.Errorf("%T.adPlanId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:adPlanId: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adStrategyId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:adStrategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdStrategyId)); err != nil {
		return fmt.Errorf("%T.adStrategyId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:adStrategyId: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adCreativeId", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:adCreativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdCreativeId)); err != nil {
		return fmt.Errorf("%T.adCreativeId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:adCreativeId: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:version: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:name: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brief", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:brief: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Brief)); err != nil {
		return fmt.Errorf("%T.brief (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:brief: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iconUrl", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:iconUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IconUrl)); err != nil {
		return fmt.Errorf("%T.iconUrl (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:iconUrl: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageUrl", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:packageUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageUrl)); err != nil {
		return fmt.Errorf("%T.packageUrl (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:packageUrl: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageSize", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:packageSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PackageSize)); err != nil {
		return fmt.Errorf("%T.packageSize (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:packageSize: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:price: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField15(oprot thrift.TProtocol) (err error) {
	if p.Detail != nil {
		if err := oprot.WriteFieldBegin("detail", thrift.STRUCT, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:detail: %s", p, err)
		}
		if err := p.Detail.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Detail)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:detail: %s", p, err)
		}
	}
	return err
}

func (p *AppVersion) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sequence", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sequence: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sequence)); err != nil {
		return fmt.Errorf("%T.sequence (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sequence: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgName", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:pkgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkgName (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:pkgName: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField18(oprot thrift.TProtocol) (err error) {
	if p.Screenshots != nil {
		if err := oprot.WriteFieldBegin("screenshots", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:screenshots: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Screenshots)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Screenshots {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:screenshots: %s", p, err)
		}
	}
	return err
}

func (p *AppVersion) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filename", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:filename: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Filename)); err != nil {
		return fmt.Errorf("%T.filename (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:filename: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *AppVersion) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AppVersion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppVersion(%+v)", *p)
}

type App struct {
	AppId          common.IdInt   `thrift:"appId,1" json:"appId"`
	Name           string         `thrift:"name,2" json:"name"`
	PublisherId    common.UidInt  `thrift:"publisherId,3" json:"publisherId"`
	OsTarget       []common.IdInt `thrift:"osTarget,4" json:"osTarget"`
	CurrentVersion *AppVersion    `thrift:"currentVersion,5" json:"currentVersion"`
	PkgName        string         `thrift:"pkgName,6" json:"pkgName"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime common.TimeInt `thrift:"createTime,20" json:"createTime"`
	LastUpdate common.TimeInt `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewApp() *App {
	return &App{}
}

func (p *App) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *App) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppId = common.IdInt(v)
	}
	return nil
}

func (p *App) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *App) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PublisherId = common.UidInt(v)
	}
	return nil
}

func (p *App) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OsTarget = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = common.IdInt(v)
		}
		p.OsTarget = append(p.OsTarget, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *App) readField5(iprot thrift.TProtocol) error {
	p.CurrentVersion = NewAppVersion()
	if err := p.CurrentVersion.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CurrentVersion)
	}
	return nil
}

func (p *App) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *App) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = common.TimeInt(v)
	}
	return nil
}

func (p *App) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = common.TimeInt(v)
	}
	return nil
}

func (p *App) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("App"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *App) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appId: %s", p, err)
	}
	return err
}

func (p *App) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *App) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("publisherId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:publisherId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PublisherId)); err != nil {
		return fmt.Errorf("%T.publisherId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:publisherId: %s", p, err)
	}
	return err
}

func (p *App) writeField4(oprot thrift.TProtocol) (err error) {
	if p.OsTarget != nil {
		if err := oprot.WriteFieldBegin("osTarget", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:osTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OsTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OsTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:osTarget: %s", p, err)
		}
	}
	return err
}

func (p *App) writeField5(oprot thrift.TProtocol) (err error) {
	if p.CurrentVersion != nil {
		if err := oprot.WriteFieldBegin("currentVersion", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:currentVersion: %s", p, err)
		}
		if err := p.CurrentVersion.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CurrentVersion)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:currentVersion: %s", p, err)
		}
	}
	return err
}

func (p *App) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgName", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pkgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkgName (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pkgName: %s", p, err)
	}
	return err
}

func (p *App) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *App) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *App) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("App(%+v)", *p)
}

type MediaUserSetting struct {
	Uid                       common.UidInt   `thrift:"uid,1" json:"uid"`
	CompetetorPublisherIdList []common.UidInt `thrift:"competetorPublisherIdList,2" json:"competetorPublisherIdList"`
}

func NewMediaUserSetting() *MediaUserSetting {
	return &MediaUserSetting{}
}

func (p *MediaUserSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaUserSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *MediaUserSetting) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CompetetorPublisherIdList = make([]common.UidInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 common.UidInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = common.UidInt(v)
		}
		p.CompetetorPublisherIdList = append(p.CompetetorPublisherIdList, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaUserSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaUserSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaUserSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaUserSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if p.CompetetorPublisherIdList != nil {
		if err := oprot.WriteFieldBegin("competetorPublisherIdList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:competetorPublisherIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CompetetorPublisherIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CompetetorPublisherIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:competetorPublisherIdList: %s", p, err)
		}
	}
	return err
}

func (p *MediaUserSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaUserSetting(%+v)", *p)
}

type MediaSetting struct {
	MediaId             common.IdInt    `thrift:"mediaId,1" json:"mediaId"`
	AppsNum             int32           `thrift:"appsNum,2" json:"appsNum"`
	HouseAdNum          int32           `thrift:"houseAdNum,3" json:"houseAdNum"`
	MinRefreshInterval  int32           `thrift:"minRefreshInterval,4" json:"minRefreshInterval"`
	HouseAdPosition     HouseAdPosition `thrift:"houseAdPosition,5" json:"houseAdPosition"`
	PreferedAppIdList   []common.IdInt  `thrift:"preferedAppIdList,6" json:"preferedAppIdList"`
	CompetetorAppIdList []common.IdInt  `thrift:"competetorAppIdList,7" json:"competetorAppIdList"`
}

func NewMediaSetting() *MediaSetting {
	return &MediaSetting{
		HouseAdPosition: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaSetting) IsSetHouseAdPosition() bool {
	return int64(p.HouseAdPosition) != math.MinInt32-1
}

func (p *MediaSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MediaId = common.IdInt(v)
	}
	return nil
}

func (p *MediaSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppsNum = v
	}
	return nil
}

func (p *MediaSetting) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.HouseAdNum = v
	}
	return nil
}

func (p *MediaSetting) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MinRefreshInterval = v
	}
	return nil
}

func (p *MediaSetting) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.HouseAdPosition = HouseAdPosition(v)
	}
	return nil
}

func (p *MediaSetting) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PreferedAppIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = common.IdInt(v)
		}
		p.PreferedAppIdList = append(p.PreferedAppIdList, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaSetting) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CompetetorAppIdList = make([]common.IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 common.IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = common.IdInt(v)
		}
		p.CompetetorAppIdList = append(p.CompetetorAppIdList, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mediaId: %s", p, err)
	}
	return err
}

func (p *MediaSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appsNum", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appsNum: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppsNum)); err != nil {
		return fmt.Errorf("%T.appsNum (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appsNum: %s", p, err)
	}
	return err
}

func (p *MediaSetting) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("houseAdNum", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:houseAdNum: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.HouseAdNum)); err != nil {
		return fmt.Errorf("%T.houseAdNum (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:houseAdNum: %s", p, err)
	}
	return err
}

func (p *MediaSetting) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("minRefreshInterval", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:minRefreshInterval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MinRefreshInterval)); err != nil {
		return fmt.Errorf("%T.minRefreshInterval (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:minRefreshInterval: %s", p, err)
	}
	return err
}

func (p *MediaSetting) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetHouseAdPosition() {
		if err := oprot.WriteFieldBegin("houseAdPosition", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:houseAdPosition: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.HouseAdPosition)); err != nil {
			return fmt.Errorf("%T.houseAdPosition (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:houseAdPosition: %s", p, err)
		}
	}
	return err
}

func (p *MediaSetting) writeField6(oprot thrift.TProtocol) (err error) {
	if p.PreferedAppIdList != nil {
		if err := oprot.WriteFieldBegin("preferedAppIdList", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:preferedAppIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PreferedAppIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PreferedAppIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:preferedAppIdList: %s", p, err)
		}
	}
	return err
}

func (p *MediaSetting) writeField7(oprot thrift.TProtocol) (err error) {
	if p.CompetetorAppIdList != nil {
		if err := oprot.WriteFieldBegin("competetorAppIdList", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:competetorAppIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CompetetorAppIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CompetetorAppIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:competetorAppIdList: %s", p, err)
		}
	}
	return err
}

func (p *MediaSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaSetting(%+v)", *p)
}

type MediaSummary struct {
	MediaId      common.IdInt      `thrift:"mediaId,1" json:"mediaId"`
	Uid          common.UidInt     `thrift:"uid,2" json:"uid"`
	PublishId    string            `thrift:"publishId,3" json:"publishId"`
	Name         string            `thrift:"name,4" json:"name"`
	MediaSetting *MediaSetting     `thrift:"mediaSetting,5" json:"mediaSetting"`
	UserSetting  *MediaUserSetting `thrift:"userSetting,6" json:"userSetting"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime common.TimeInt `thrift:"createTime,20" json:"createTime"`
	LastUpdate common.TimeInt `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewMediaSummary() *MediaSummary {
	return &MediaSummary{}
}

func (p *MediaSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MediaId = common.IdInt(v)
	}
	return nil
}

func (p *MediaSummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *MediaSummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PublishId = v
	}
	return nil
}

func (p *MediaSummary) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *MediaSummary) readField5(iprot thrift.TProtocol) error {
	p.MediaSetting = NewMediaSetting()
	if err := p.MediaSetting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaSetting)
	}
	return nil
}

func (p *MediaSummary) readField6(iprot thrift.TProtocol) error {
	p.UserSetting = NewMediaUserSetting()
	if err := p.UserSetting.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserSetting)
	}
	return nil
}

func (p *MediaSummary) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = common.TimeInt(v)
	}
	return nil
}

func (p *MediaSummary) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = common.TimeInt(v)
	}
	return nil
}

func (p *MediaSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mediaId: %s", p, err)
	}
	return err
}

func (p *MediaSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *MediaSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("publishId", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:publishId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PublishId)); err != nil {
		return fmt.Errorf("%T.publishId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:publishId: %s", p, err)
	}
	return err
}

func (p *MediaSummary) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *MediaSummary) writeField5(oprot thrift.TProtocol) (err error) {
	if p.MediaSetting != nil {
		if err := oprot.WriteFieldBegin("mediaSetting", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:mediaSetting: %s", p, err)
		}
		if err := p.MediaSetting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaSetting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:mediaSetting: %s", p, err)
		}
	}
	return err
}

func (p *MediaSummary) writeField6(oprot thrift.TProtocol) (err error) {
	if p.UserSetting != nil {
		if err := oprot.WriteFieldBegin("userSetting", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:userSetting: %s", p, err)
		}
		if err := p.UserSetting.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserSetting)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:userSetting: %s", p, err)
		}
	}
	return err
}

func (p *MediaSummary) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *MediaSummary) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *MediaSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaSummary(%+v)", *p)
}

type MediaInfo struct {
	Summary              *MediaSummary   `thrift:"summary,1" json:"summary"`
	HouseAds             []*AppVersion   `thrift:"houseAds,2" json:"houseAds"`
	RefusedApps          []*App          `thrift:"refusedApps,3" json:"refusedApps"`
	CompetetorPublishers []*AppPublisher `thrift:"competetorPublishers,4" json:"competetorPublishers"`
}

func NewMediaInfo() *MediaInfo {
	return &MediaInfo{}
}

func (p *MediaInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaInfo) readField1(iprot thrift.TProtocol) error {
	p.Summary = NewMediaSummary()
	if err := p.Summary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Summary)
	}
	return nil
}

func (p *MediaInfo) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.HouseAds = make([]*AppVersion, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewAppVersion()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.HouseAds = append(p.HouseAds, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaInfo) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RefusedApps = make([]*App, 0, size)
	for i := 0; i < size; i++ {
		_elem10 := NewApp()
		if err := _elem10.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem10)
		}
		p.RefusedApps = append(p.RefusedApps, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaInfo) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CompetetorPublishers = make([]*AppPublisher, 0, size)
	for i := 0; i < size; i++ {
		_elem11 := NewAppPublisher()
		if err := _elem11.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem11)
		}
		p.CompetetorPublishers = append(p.CompetetorPublishers, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Summary != nil {
		if err := oprot.WriteFieldBegin("summary", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:summary: %s", p, err)
		}
		if err := p.Summary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Summary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:summary: %s", p, err)
		}
	}
	return err
}

func (p *MediaInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.HouseAds != nil {
		if err := oprot.WriteFieldBegin("houseAds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:houseAds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.HouseAds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.HouseAds {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:houseAds: %s", p, err)
		}
	}
	return err
}

func (p *MediaInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.RefusedApps != nil {
		if err := oprot.WriteFieldBegin("refusedApps", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:refusedApps: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RefusedApps)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RefusedApps {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:refusedApps: %s", p, err)
		}
	}
	return err
}

func (p *MediaInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.CompetetorPublishers != nil {
		if err := oprot.WriteFieldBegin("competetorPublishers", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:competetorPublishers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CompetetorPublishers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CompetetorPublishers {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:competetorPublishers: %s", p, err)
		}
	}
	return err
}

func (p *MediaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaInfo(%+v)", *p)
}
