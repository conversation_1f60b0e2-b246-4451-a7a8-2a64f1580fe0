// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/vico_finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = vico_finance_types.GoUnusedProtection__
var GoUnusedProtection__ int

//@Description("FT信息流发布频道,用于自运营信息流分类") *
type FTChannelType int64

const (
	FTChannelType_FTCT_UNKNOWN        FTChannelType = 0
	FTChannelType_FTCT_WHAT_TO_WEAR   FTChannelType = 1
	FTChannelType_FTCT_TRENDING       FTChannelType = 2
	FTChannelType_FTCT_LOOK_FOR_DEALS FTChannelType = 3
	FTChannelType_FTCT_BRANDS         FTChannelType = 4
	FTChannelType_FTCT_CELEBRITY      FTChannelType = 5
	FTChannelType_FTCT_HOLIDAY        FTChannelType = 6
)

func (p FTChannelType) String() string {
	switch p {
	case FTChannelType_FTCT_UNKNOWN:
		return "FTChannelType_FTCT_UNKNOWN"
	case FTChannelType_FTCT_WHAT_TO_WEAR:
		return "FTChannelType_FTCT_WHAT_TO_WEAR"
	case FTChannelType_FTCT_TRENDING:
		return "FTChannelType_FTCT_TRENDING"
	case FTChannelType_FTCT_LOOK_FOR_DEALS:
		return "FTChannelType_FTCT_LOOK_FOR_DEALS"
	case FTChannelType_FTCT_BRANDS:
		return "FTChannelType_FTCT_BRANDS"
	case FTChannelType_FTCT_CELEBRITY:
		return "FTChannelType_FTCT_CELEBRITY"
	case FTChannelType_FTCT_HOLIDAY:
		return "FTChannelType_FTCT_HOLIDAY"
	}
	return "<UNSET>"
}

func FTChannelTypeFromString(s string) (FTChannelType, error) {
	switch s {
	case "FTChannelType_FTCT_UNKNOWN":
		return FTChannelType_FTCT_UNKNOWN, nil
	case "FTChannelType_FTCT_WHAT_TO_WEAR":
		return FTChannelType_FTCT_WHAT_TO_WEAR, nil
	case "FTChannelType_FTCT_TRENDING":
		return FTChannelType_FTCT_TRENDING, nil
	case "FTChannelType_FTCT_LOOK_FOR_DEALS":
		return FTChannelType_FTCT_LOOK_FOR_DEALS, nil
	case "FTChannelType_FTCT_BRANDS":
		return FTChannelType_FTCT_BRANDS, nil
	case "FTChannelType_FTCT_CELEBRITY":
		return FTChannelType_FTCT_CELEBRITY, nil
	case "FTChannelType_FTCT_HOLIDAY":
		return FTChannelType_FTCT_HOLIDAY, nil
	}
	return FTChannelType(math.MinInt32 - 1), fmt.Errorf("not a valid FTChannelType string")
}

//模板类型
type FtLandingTemplateType int64

const (
	FtLandingTemplateType_FLTT_UNKNOWN  FtLandingTemplateType = 0
	FtLandingTemplateType_FLTT_NORMAL   FtLandingTemplateType = 1
	FtLandingTemplateType_FLTT_FESTIVAL FtLandingTemplateType = 2
	FtLandingTemplateType_FLTT_CATEGORY FtLandingTemplateType = 3
)

func (p FtLandingTemplateType) String() string {
	switch p {
	case FtLandingTemplateType_FLTT_UNKNOWN:
		return "FtLandingTemplateType_FLTT_UNKNOWN"
	case FtLandingTemplateType_FLTT_NORMAL:
		return "FtLandingTemplateType_FLTT_NORMAL"
	case FtLandingTemplateType_FLTT_FESTIVAL:
		return "FtLandingTemplateType_FLTT_FESTIVAL"
	case FtLandingTemplateType_FLTT_CATEGORY:
		return "FtLandingTemplateType_FLTT_CATEGORY"
	}
	return "<UNSET>"
}

func FtLandingTemplateTypeFromString(s string) (FtLandingTemplateType, error) {
	switch s {
	case "FtLandingTemplateType_FLTT_UNKNOWN":
		return FtLandingTemplateType_FLTT_UNKNOWN, nil
	case "FtLandingTemplateType_FLTT_NORMAL":
		return FtLandingTemplateType_FLTT_NORMAL, nil
	case "FtLandingTemplateType_FLTT_FESTIVAL":
		return FtLandingTemplateType_FLTT_FESTIVAL, nil
	case "FtLandingTemplateType_FLTT_CATEGORY":
		return FtLandingTemplateType_FLTT_CATEGORY, nil
	}
	return FtLandingTemplateType(math.MinInt32 - 1), fmt.Errorf("not a valid FtLandingTemplateType string")
}

//@Description("ft landing类型")
type FTLandingType int64

const (
	FTLandingType_FTLT_UNKNOWN    FTLandingType = 0
	FTLandingType_FTLT_AD         FTLandingType = 1
	FTLandingType_FTLT_SELF_MEDIA FTLandingType = 2
)

func (p FTLandingType) String() string {
	switch p {
	case FTLandingType_FTLT_UNKNOWN:
		return "FTLandingType_FTLT_UNKNOWN"
	case FTLandingType_FTLT_AD:
		return "FTLandingType_FTLT_AD"
	case FTLandingType_FTLT_SELF_MEDIA:
		return "FTLandingType_FTLT_SELF_MEDIA"
	}
	return "<UNSET>"
}

func FTLandingTypeFromString(s string) (FTLandingType, error) {
	switch s {
	case "FTLandingType_FTLT_UNKNOWN":
		return FTLandingType_FTLT_UNKNOWN, nil
	case "FTLandingType_FTLT_AD":
		return FTLandingType_FTLT_AD, nil
	case "FTLandingType_FTLT_SELF_MEDIA":
		return FTLandingType_FTLT_SELF_MEDIA, nil
	}
	return FTLandingType(math.MinInt32 - 1), fmt.Errorf("not a valid FTLandingType string")
}

//@Description("电商平台") *
type MallPlatform int64

const (
	MallPlatform_MP_UNKNOWN    MallPlatform = 0
	MallPlatform_MP_AMAZON     MallPlatform = 1
	MallPlatform_MP_ALIEXPRESS MallPlatform = 2
	MallPlatform_MP_EBAY       MallPlatform = 3
	MallPlatform_MP_WISH       MallPlatform = 4
	MallPlatform_MP_LAZADA     MallPlatform = 5
	MallPlatform_MP_DHGATE     MallPlatform = 6
	MallPlatform_MP_CDISCOUNT  MallPlatform = 7
	MallPlatform_MP_SHOPIFY    MallPlatform = 8
)

func (p MallPlatform) String() string {
	switch p {
	case MallPlatform_MP_UNKNOWN:
		return "MallPlatform_MP_UNKNOWN"
	case MallPlatform_MP_AMAZON:
		return "MallPlatform_MP_AMAZON"
	case MallPlatform_MP_ALIEXPRESS:
		return "MallPlatform_MP_ALIEXPRESS"
	case MallPlatform_MP_EBAY:
		return "MallPlatform_MP_EBAY"
	case MallPlatform_MP_WISH:
		return "MallPlatform_MP_WISH"
	case MallPlatform_MP_LAZADA:
		return "MallPlatform_MP_LAZADA"
	case MallPlatform_MP_DHGATE:
		return "MallPlatform_MP_DHGATE"
	case MallPlatform_MP_CDISCOUNT:
		return "MallPlatform_MP_CDISCOUNT"
	case MallPlatform_MP_SHOPIFY:
		return "MallPlatform_MP_SHOPIFY"
	}
	return "<UNSET>"
}

func MallPlatformFromString(s string) (MallPlatform, error) {
	switch s {
	case "MallPlatform_MP_UNKNOWN":
		return MallPlatform_MP_UNKNOWN, nil
	case "MallPlatform_MP_AMAZON":
		return MallPlatform_MP_AMAZON, nil
	case "MallPlatform_MP_ALIEXPRESS":
		return MallPlatform_MP_ALIEXPRESS, nil
	case "MallPlatform_MP_EBAY":
		return MallPlatform_MP_EBAY, nil
	case "MallPlatform_MP_WISH":
		return MallPlatform_MP_WISH, nil
	case "MallPlatform_MP_LAZADA":
		return MallPlatform_MP_LAZADA, nil
	case "MallPlatform_MP_DHGATE":
		return MallPlatform_MP_DHGATE, nil
	case "MallPlatform_MP_CDISCOUNT":
		return MallPlatform_MP_CDISCOUNT, nil
	case "MallPlatform_MP_SHOPIFY":
		return MallPlatform_MP_SHOPIFY, nil
	}
	return MallPlatform(math.MinInt32 - 1), fmt.Errorf("not a valid MallPlatform string")
}

//@Description("商品投放渠道") *
type AdChannelType int64

const (
	AdChannelType_ACT_ALL       AdChannelType = 0
	AdChannelType_ACT_FACEBOOK  AdChannelType = 1
	AdChannelType_ACT_GOOGLE    AdChannelType = 2
	AdChannelType_ACT_INSTAGRAM AdChannelType = 3
)

func (p AdChannelType) String() string {
	switch p {
	case AdChannelType_ACT_ALL:
		return "AdChannelType_ACT_ALL"
	case AdChannelType_ACT_FACEBOOK:
		return "AdChannelType_ACT_FACEBOOK"
	case AdChannelType_ACT_GOOGLE:
		return "AdChannelType_ACT_GOOGLE"
	case AdChannelType_ACT_INSTAGRAM:
		return "AdChannelType_ACT_INSTAGRAM"
	}
	return "<UNSET>"
}

func AdChannelTypeFromString(s string) (AdChannelType, error) {
	switch s {
	case "AdChannelType_ACT_ALL":
		return AdChannelType_ACT_ALL, nil
	case "AdChannelType_ACT_FACEBOOK":
		return AdChannelType_ACT_FACEBOOK, nil
	case "AdChannelType_ACT_GOOGLE":
		return AdChannelType_ACT_GOOGLE, nil
	case "AdChannelType_ACT_INSTAGRAM":
		return AdChannelType_ACT_INSTAGRAM, nil
	}
	return AdChannelType(math.MinInt32 - 1), fmt.Errorf("not a valid AdChannelType string")
}

//@Description("商品投放设备") *
type AdDeviceType int64

const (
	AdDeviceType_ADT_ALL    AdDeviceType = 0
	AdDeviceType_ADT_PC     AdDeviceType = 1
	AdDeviceType_ADT_MOBILE AdDeviceType = 2
)

func (p AdDeviceType) String() string {
	switch p {
	case AdDeviceType_ADT_ALL:
		return "AdDeviceType_ADT_ALL"
	case AdDeviceType_ADT_PC:
		return "AdDeviceType_ADT_PC"
	case AdDeviceType_ADT_MOBILE:
		return "AdDeviceType_ADT_MOBILE"
	}
	return "<UNSET>"
}

func AdDeviceTypeFromString(s string) (AdDeviceType, error) {
	switch s {
	case "AdDeviceType_ADT_ALL":
		return AdDeviceType_ADT_ALL, nil
	case "AdDeviceType_ADT_PC":
		return AdDeviceType_ADT_PC, nil
	case "AdDeviceType_ADT_MOBILE":
		return AdDeviceType_ADT_MOBILE, nil
	}
	return AdDeviceType(math.MinInt32 - 1), fmt.Errorf("not a valid AdDeviceType string")
}

//@Description("商品投放类型") *
type AdType int64

const (
	AdType_AT_UNKNOWN AdType = 0
	AdType_AT_CPC_0   AdType = 1
	AdType_AT_CPC_1   AdType = 2
	AdType_AT_CPM_0   AdType = 3
	AdType_AT_CPM_1   AdType = 4
)

func (p AdType) String() string {
	switch p {
	case AdType_AT_UNKNOWN:
		return "AdType_AT_UNKNOWN"
	case AdType_AT_CPC_0:
		return "AdType_AT_CPC_0"
	case AdType_AT_CPC_1:
		return "AdType_AT_CPC_1"
	case AdType_AT_CPM_0:
		return "AdType_AT_CPM_0"
	case AdType_AT_CPM_1:
		return "AdType_AT_CPM_1"
	}
	return "<UNSET>"
}

func AdTypeFromString(s string) (AdType, error) {
	switch s {
	case "AdType_AT_UNKNOWN":
		return AdType_AT_UNKNOWN, nil
	case "AdType_AT_CPC_0":
		return AdType_AT_CPC_0, nil
	case "AdType_AT_CPC_1":
		return AdType_AT_CPC_1, nil
	case "AdType_AT_CPM_0":
		return AdType_AT_CPM_0, nil
	case "AdType_AT_CPM_1":
		return AdType_AT_CPM_1, nil
	}
	return AdType(math.MinInt32 - 1), fmt.Errorf("not a valid AdType string")
}

//@Description("订单对外显示的状态") *
type AdDisplayStatus int64

const (
	AdDisplayStatus_ADS_UNKNOWN           AdDisplayStatus = 0
	AdDisplayStatus_ADS_IN_AUDIT          AdDisplayStatus = 1
	AdDisplayStatus_ADS_AUDIT_REJECT      AdDisplayStatus = 2
	AdDisplayStatus_ADS_RUNNING           AdDisplayStatus = 3
	AdDisplayStatus_ADS_ENDED             AdDisplayStatus = 4
	AdDisplayStatus_ADS_DAILY_BUDGET_OVER AdDisplayStatus = 5
	AdDisplayStatus_ADS_ACCOUNT_ERROR     AdDisplayStatus = 6
)

func (p AdDisplayStatus) String() string {
	switch p {
	case AdDisplayStatus_ADS_UNKNOWN:
		return "AdDisplayStatus_ADS_UNKNOWN"
	case AdDisplayStatus_ADS_IN_AUDIT:
		return "AdDisplayStatus_ADS_IN_AUDIT"
	case AdDisplayStatus_ADS_AUDIT_REJECT:
		return "AdDisplayStatus_ADS_AUDIT_REJECT"
	case AdDisplayStatus_ADS_RUNNING:
		return "AdDisplayStatus_ADS_RUNNING"
	case AdDisplayStatus_ADS_ENDED:
		return "AdDisplayStatus_ADS_ENDED"
	case AdDisplayStatus_ADS_DAILY_BUDGET_OVER:
		return "AdDisplayStatus_ADS_DAILY_BUDGET_OVER"
	case AdDisplayStatus_ADS_ACCOUNT_ERROR:
		return "AdDisplayStatus_ADS_ACCOUNT_ERROR"
	}
	return "<UNSET>"
}

func AdDisplayStatusFromString(s string) (AdDisplayStatus, error) {
	switch s {
	case "AdDisplayStatus_ADS_UNKNOWN":
		return AdDisplayStatus_ADS_UNKNOWN, nil
	case "AdDisplayStatus_ADS_IN_AUDIT":
		return AdDisplayStatus_ADS_IN_AUDIT, nil
	case "AdDisplayStatus_ADS_AUDIT_REJECT":
		return AdDisplayStatus_ADS_AUDIT_REJECT, nil
	case "AdDisplayStatus_ADS_RUNNING":
		return AdDisplayStatus_ADS_RUNNING, nil
	case "AdDisplayStatus_ADS_ENDED":
		return AdDisplayStatus_ADS_ENDED, nil
	case "AdDisplayStatus_ADS_DAILY_BUDGET_OVER":
		return AdDisplayStatus_ADS_DAILY_BUDGET_OVER, nil
	case "AdDisplayStatus_ADS_ACCOUNT_ERROR":
		return AdDisplayStatus_ADS_ACCOUNT_ERROR, nil
	}
	return AdDisplayStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdDisplayStatus string")
}

//@Description("审核状态") *
type AuditStatus int64

const (
	AuditStatus_AS_UNKNOWN      AuditStatus = 0
	AuditStatus_AS_IN_AUDIT     AuditStatus = 1
	AuditStatus_AS_AUDIT_REJECT AuditStatus = 3
	AuditStatus_AS_AUDIT_PASS   AuditStatus = 4
)

func (p AuditStatus) String() string {
	switch p {
	case AuditStatus_AS_UNKNOWN:
		return "AuditStatus_AS_UNKNOWN"
	case AuditStatus_AS_IN_AUDIT:
		return "AuditStatus_AS_IN_AUDIT"
	case AuditStatus_AS_AUDIT_REJECT:
		return "AuditStatus_AS_AUDIT_REJECT"
	case AuditStatus_AS_AUDIT_PASS:
		return "AuditStatus_AS_AUDIT_PASS"
	}
	return "<UNSET>"
}

func AuditStatusFromString(s string) (AuditStatus, error) {
	switch s {
	case "AuditStatus_AS_UNKNOWN":
		return AuditStatus_AS_UNKNOWN, nil
	case "AuditStatus_AS_IN_AUDIT":
		return AuditStatus_AS_IN_AUDIT, nil
	case "AuditStatus_AS_AUDIT_REJECT":
		return AuditStatus_AS_AUDIT_REJECT, nil
	case "AuditStatus_AS_AUDIT_PASS":
		return AuditStatus_AS_AUDIT_PASS, nil
	}
	return AuditStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AuditStatus string")
}

//@Description("广告活动状态") *
type CampaignStatus int64

const (
	CampaignStatus_CS_UNKNOWN      CampaignStatus = 0
	CampaignStatus_CS_IN_AUDIT     CampaignStatus = 1
	CampaignStatus_CS_REJECT       CampaignStatus = 2
	CampaignStatus_CS_PENDING      CampaignStatus = 3
	CampaignStatus_CS_RUNNING      CampaignStatus = 4
	CampaignStatus_CS_PAUSED       CampaignStatus = 5
	CampaignStatus_CS_ENDED        CampaignStatus = 6
	CampaignStatus_CS_BUDGET_LIMIT CampaignStatus = 7
	CampaignStatus_CS_DELETED      CampaignStatus = 8
)

func (p CampaignStatus) String() string {
	switch p {
	case CampaignStatus_CS_UNKNOWN:
		return "CampaignStatus_CS_UNKNOWN"
	case CampaignStatus_CS_IN_AUDIT:
		return "CampaignStatus_CS_IN_AUDIT"
	case CampaignStatus_CS_REJECT:
		return "CampaignStatus_CS_REJECT"
	case CampaignStatus_CS_PENDING:
		return "CampaignStatus_CS_PENDING"
	case CampaignStatus_CS_RUNNING:
		return "CampaignStatus_CS_RUNNING"
	case CampaignStatus_CS_PAUSED:
		return "CampaignStatus_CS_PAUSED"
	case CampaignStatus_CS_ENDED:
		return "CampaignStatus_CS_ENDED"
	case CampaignStatus_CS_BUDGET_LIMIT:
		return "CampaignStatus_CS_BUDGET_LIMIT"
	case CampaignStatus_CS_DELETED:
		return "CampaignStatus_CS_DELETED"
	}
	return "<UNSET>"
}

func CampaignStatusFromString(s string) (CampaignStatus, error) {
	switch s {
	case "CampaignStatus_CS_UNKNOWN":
		return CampaignStatus_CS_UNKNOWN, nil
	case "CampaignStatus_CS_IN_AUDIT":
		return CampaignStatus_CS_IN_AUDIT, nil
	case "CampaignStatus_CS_REJECT":
		return CampaignStatus_CS_REJECT, nil
	case "CampaignStatus_CS_PENDING":
		return CampaignStatus_CS_PENDING, nil
	case "CampaignStatus_CS_RUNNING":
		return CampaignStatus_CS_RUNNING, nil
	case "CampaignStatus_CS_PAUSED":
		return CampaignStatus_CS_PAUSED, nil
	case "CampaignStatus_CS_ENDED":
		return CampaignStatus_CS_ENDED, nil
	case "CampaignStatus_CS_BUDGET_LIMIT":
		return CampaignStatus_CS_BUDGET_LIMIT, nil
	case "CampaignStatus_CS_DELETED":
		return CampaignStatus_CS_DELETED, nil
	}
	return CampaignStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CampaignStatus string")
}

//商品分类
type ProductCategory int64

const (
	ProductCategory_PC_UNKNOWN                  ProductCategory = 0
	ProductCategory_PC_SHOES                    ProductCategory = 1
	ProductCategory_PC_CLOTHING                 ProductCategory = 2
	ProductCategory_PC_HANDBAGS_WATCHES_JEWELRY ProductCategory = 3
	ProductCategory_PC_BABY                     ProductCategory = 4
	ProductCategory_PC_TOYS                     ProductCategory = 5
	ProductCategory_PC_BEAUTY_HEALTH            ProductCategory = 6
	ProductCategory_PC_HOME_FOOD                ProductCategory = 7
	ProductCategory_PC_ELECTRONICS              ProductCategory = 8
	ProductCategory_PC_SPORTS_OUTDOORS          ProductCategory = 9
	ProductCategory_PC_OFFICE                   ProductCategory = 10
)

func (p ProductCategory) String() string {
	switch p {
	case ProductCategory_PC_UNKNOWN:
		return "ProductCategory_PC_UNKNOWN"
	case ProductCategory_PC_SHOES:
		return "ProductCategory_PC_SHOES"
	case ProductCategory_PC_CLOTHING:
		return "ProductCategory_PC_CLOTHING"
	case ProductCategory_PC_HANDBAGS_WATCHES_JEWELRY:
		return "ProductCategory_PC_HANDBAGS_WATCHES_JEWELRY"
	case ProductCategory_PC_BABY:
		return "ProductCategory_PC_BABY"
	case ProductCategory_PC_TOYS:
		return "ProductCategory_PC_TOYS"
	case ProductCategory_PC_BEAUTY_HEALTH:
		return "ProductCategory_PC_BEAUTY_HEALTH"
	case ProductCategory_PC_HOME_FOOD:
		return "ProductCategory_PC_HOME_FOOD"
	case ProductCategory_PC_ELECTRONICS:
		return "ProductCategory_PC_ELECTRONICS"
	case ProductCategory_PC_SPORTS_OUTDOORS:
		return "ProductCategory_PC_SPORTS_OUTDOORS"
	case ProductCategory_PC_OFFICE:
		return "ProductCategory_PC_OFFICE"
	}
	return "<UNSET>"
}

func ProductCategoryFromString(s string) (ProductCategory, error) {
	switch s {
	case "ProductCategory_PC_UNKNOWN":
		return ProductCategory_PC_UNKNOWN, nil
	case "ProductCategory_PC_SHOES":
		return ProductCategory_PC_SHOES, nil
	case "ProductCategory_PC_CLOTHING":
		return ProductCategory_PC_CLOTHING, nil
	case "ProductCategory_PC_HANDBAGS_WATCHES_JEWELRY":
		return ProductCategory_PC_HANDBAGS_WATCHES_JEWELRY, nil
	case "ProductCategory_PC_BABY":
		return ProductCategory_PC_BABY, nil
	case "ProductCategory_PC_TOYS":
		return ProductCategory_PC_TOYS, nil
	case "ProductCategory_PC_BEAUTY_HEALTH":
		return ProductCategory_PC_BEAUTY_HEALTH, nil
	case "ProductCategory_PC_HOME_FOOD":
		return ProductCategory_PC_HOME_FOOD, nil
	case "ProductCategory_PC_ELECTRONICS":
		return ProductCategory_PC_ELECTRONICS, nil
	case "ProductCategory_PC_SPORTS_OUTDOORS":
		return ProductCategory_PC_SPORTS_OUTDOORS, nil
	case "ProductCategory_PC_OFFICE":
		return ProductCategory_PC_OFFICE, nil
	}
	return ProductCategory(math.MinInt32 - 1), fmt.Errorf("not a valid ProductCategory string")
}

//专题Tag
type PackTags int64

const (
	PackTags_PT_UNKNOWN       PackTags = 0
	PackTags_PT_OUTFIT        PackTags = 1
	PackTags_PT_STREETSTYLE   PackTags = 2
	PackTags_PT_JEWELRY       PackTags = 3
	PackTags_PT_KIDS          PackTags = 4
	PackTags_PT_OUTDOORS      PackTags = 5
	PackTags_PT_DESIGN        PackTags = 6
	PackTags_PT_HOME          PackTags = 7
	PackTags_PT_TOY           PackTags = 8
	PackTags_PT_MAKEUP        PackTags = 9
	PackTags_PT_SPORT         PackTags = 10
	PackTags_PT_HOLIDAY       PackTags = 11
	PackTags_PT_BAGS          PackTags = 12
	PackTags_PT_DIY           PackTags = 13
	PackTags_PT_LOVE          PackTags = 14
	PackTags_PT_DAILYGOODS    PackTags = 15
	PackTags_PT_PARTY         PackTags = 16
	PackTags_PT_ART           PackTags = 17
	PackTags_PT_TRAVEL        PackTags = 18
	PackTags_PT_VINTAGE       PackTags = 19
	PackTags_PT_WORK          PackTags = 20
	PackTags_PT_CASUAL        PackTags = 21
	PackTags_PT_MEN_STYLE     PackTags = 22
	PackTags_PT_WOMEN_FASHION PackTags = 23
)

func (p PackTags) String() string {
	switch p {
	case PackTags_PT_UNKNOWN:
		return "PackTags_PT_UNKNOWN"
	case PackTags_PT_OUTFIT:
		return "PackTags_PT_OUTFIT"
	case PackTags_PT_STREETSTYLE:
		return "PackTags_PT_STREETSTYLE"
	case PackTags_PT_JEWELRY:
		return "PackTags_PT_JEWELRY"
	case PackTags_PT_KIDS:
		return "PackTags_PT_KIDS"
	case PackTags_PT_OUTDOORS:
		return "PackTags_PT_OUTDOORS"
	case PackTags_PT_DESIGN:
		return "PackTags_PT_DESIGN"
	case PackTags_PT_HOME:
		return "PackTags_PT_HOME"
	case PackTags_PT_TOY:
		return "PackTags_PT_TOY"
	case PackTags_PT_MAKEUP:
		return "PackTags_PT_MAKEUP"
	case PackTags_PT_SPORT:
		return "PackTags_PT_SPORT"
	case PackTags_PT_HOLIDAY:
		return "PackTags_PT_HOLIDAY"
	case PackTags_PT_BAGS:
		return "PackTags_PT_BAGS"
	case PackTags_PT_DIY:
		return "PackTags_PT_DIY"
	case PackTags_PT_LOVE:
		return "PackTags_PT_LOVE"
	case PackTags_PT_DAILYGOODS:
		return "PackTags_PT_DAILYGOODS"
	case PackTags_PT_PARTY:
		return "PackTags_PT_PARTY"
	case PackTags_PT_ART:
		return "PackTags_PT_ART"
	case PackTags_PT_TRAVEL:
		return "PackTags_PT_TRAVEL"
	case PackTags_PT_VINTAGE:
		return "PackTags_PT_VINTAGE"
	case PackTags_PT_WORK:
		return "PackTags_PT_WORK"
	case PackTags_PT_CASUAL:
		return "PackTags_PT_CASUAL"
	case PackTags_PT_MEN_STYLE:
		return "PackTags_PT_MEN_STYLE"
	case PackTags_PT_WOMEN_FASHION:
		return "PackTags_PT_WOMEN_FASHION"
	}
	return "<UNSET>"
}

func PackTagsFromString(s string) (PackTags, error) {
	switch s {
	case "PackTags_PT_UNKNOWN":
		return PackTags_PT_UNKNOWN, nil
	case "PackTags_PT_OUTFIT":
		return PackTags_PT_OUTFIT, nil
	case "PackTags_PT_STREETSTYLE":
		return PackTags_PT_STREETSTYLE, nil
	case "PackTags_PT_JEWELRY":
		return PackTags_PT_JEWELRY, nil
	case "PackTags_PT_KIDS":
		return PackTags_PT_KIDS, nil
	case "PackTags_PT_OUTDOORS":
		return PackTags_PT_OUTDOORS, nil
	case "PackTags_PT_DESIGN":
		return PackTags_PT_DESIGN, nil
	case "PackTags_PT_HOME":
		return PackTags_PT_HOME, nil
	case "PackTags_PT_TOY":
		return PackTags_PT_TOY, nil
	case "PackTags_PT_MAKEUP":
		return PackTags_PT_MAKEUP, nil
	case "PackTags_PT_SPORT":
		return PackTags_PT_SPORT, nil
	case "PackTags_PT_HOLIDAY":
		return PackTags_PT_HOLIDAY, nil
	case "PackTags_PT_BAGS":
		return PackTags_PT_BAGS, nil
	case "PackTags_PT_DIY":
		return PackTags_PT_DIY, nil
	case "PackTags_PT_LOVE":
		return PackTags_PT_LOVE, nil
	case "PackTags_PT_DAILYGOODS":
		return PackTags_PT_DAILYGOODS, nil
	case "PackTags_PT_PARTY":
		return PackTags_PT_PARTY, nil
	case "PackTags_PT_ART":
		return PackTags_PT_ART, nil
	case "PackTags_PT_TRAVEL":
		return PackTags_PT_TRAVEL, nil
	case "PackTags_PT_VINTAGE":
		return PackTags_PT_VINTAGE, nil
	case "PackTags_PT_WORK":
		return PackTags_PT_WORK, nil
	case "PackTags_PT_CASUAL":
		return PackTags_PT_CASUAL, nil
	case "PackTags_PT_MEN_STYLE":
		return PackTags_PT_MEN_STYLE, nil
	case "PackTags_PT_WOMEN_FASHION":
		return PackTags_PT_WOMEN_FASHION, nil
	}
	return PackTags(math.MinInt32 - 1), fmt.Errorf("not a valid PackTags string")
}

//广告推广类型
type AdPromotionType int64

const (
	AdPromotionType_APT_UNKNOWN AdPromotionType = 0
	AdPromotionType_APT_PRODUCT AdPromotionType = 1
	AdPromotionType_APT_PACK    AdPromotionType = 2
)

func (p AdPromotionType) String() string {
	switch p {
	case AdPromotionType_APT_UNKNOWN:
		return "AdPromotionType_APT_UNKNOWN"
	case AdPromotionType_APT_PRODUCT:
		return "AdPromotionType_APT_PRODUCT"
	case AdPromotionType_APT_PACK:
		return "AdPromotionType_APT_PACK"
	}
	return "<UNSET>"
}

func AdPromotionTypeFromString(s string) (AdPromotionType, error) {
	switch s {
	case "AdPromotionType_APT_UNKNOWN":
		return AdPromotionType_APT_UNKNOWN, nil
	case "AdPromotionType_APT_PRODUCT":
		return AdPromotionType_APT_PRODUCT, nil
	case "AdPromotionType_APT_PACK":
		return AdPromotionType_APT_PACK, nil
	}
	return AdPromotionType(math.MinInt32 - 1), fmt.Errorf("not a valid AdPromotionType string")
}

//商品状态
type ProductStatus int64

const (
	ProductStatus_PS_PENDING  ProductStatus = 0
	ProductStatus_PS_SUCCESS  ProductStatus = 1
	ProductStatus_PS_FAILURE  ProductStatus = 2
	ProductStatus_PS_COMPLETE ProductStatus = 3
	ProductStatus_PS_DELETED  ProductStatus = 4
)

func (p ProductStatus) String() string {
	switch p {
	case ProductStatus_PS_PENDING:
		return "ProductStatus_PS_PENDING"
	case ProductStatus_PS_SUCCESS:
		return "ProductStatus_PS_SUCCESS"
	case ProductStatus_PS_FAILURE:
		return "ProductStatus_PS_FAILURE"
	case ProductStatus_PS_COMPLETE:
		return "ProductStatus_PS_COMPLETE"
	case ProductStatus_PS_DELETED:
		return "ProductStatus_PS_DELETED"
	}
	return "<UNSET>"
}

func ProductStatusFromString(s string) (ProductStatus, error) {
	switch s {
	case "ProductStatus_PS_PENDING":
		return ProductStatus_PS_PENDING, nil
	case "ProductStatus_PS_SUCCESS":
		return ProductStatus_PS_SUCCESS, nil
	case "ProductStatus_PS_FAILURE":
		return ProductStatus_PS_FAILURE, nil
	case "ProductStatus_PS_COMPLETE":
		return ProductStatus_PS_COMPLETE, nil
	case "ProductStatus_PS_DELETED":
		return ProductStatus_PS_DELETED, nil
	}
	return ProductStatus(math.MinInt32 - 1), fmt.Errorf("not a valid ProductStatus string")
}

//商品&专题 发布状态
type PublishStatus int64

const (
	PublishStatus_PS_UNPUBLISH PublishStatus = 0
	PublishStatus_PS_PUBLISHED PublishStatus = 1
)

func (p PublishStatus) String() string {
	switch p {
	case PublishStatus_PS_UNPUBLISH:
		return "PublishStatus_PS_UNPUBLISH"
	case PublishStatus_PS_PUBLISHED:
		return "PublishStatus_PS_PUBLISHED"
	}
	return "<UNSET>"
}

func PublishStatusFromString(s string) (PublishStatus, error) {
	switch s {
	case "PublishStatus_PS_UNPUBLISH":
		return PublishStatus_PS_UNPUBLISH, nil
	case "PublishStatus_PS_PUBLISHED":
		return PublishStatus_PS_PUBLISHED, nil
	}
	return PublishStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PublishStatus string")
}

//标签库对应标签类型
type TagLibraryType int64

const (
	TagLibraryType_TLT_UNKNOWN            TagLibraryType = 0
	TagLibraryType_TLT_PRODUCT_DIY        TagLibraryType = 1
	TagLibraryType_TLT_PRODUCT_CATEGORY   TagLibraryType = 2
	TagLibraryType_TLT_UGC_HASHTAG        TagLibraryType = 3
	TagLibraryType_TLT_UGC_CORRECTION     TagLibraryType = 4
	TagLibraryType_TLT_COLLECTION         TagLibraryType = 5
	TagLibraryType_TLT_MODLE_WORD         TagLibraryType = 6
	TagLibraryType_TLT_MODLE_TAG          TagLibraryType = 7
	TagLibraryType_TLT_PRODUCT_AZURE_TAGS TagLibraryType = 8
	TagLibraryType_TLT_UGC_AZURE_TAGS     TagLibraryType = 9
)

func (p TagLibraryType) String() string {
	switch p {
	case TagLibraryType_TLT_UNKNOWN:
		return "TagLibraryType_TLT_UNKNOWN"
	case TagLibraryType_TLT_PRODUCT_DIY:
		return "TagLibraryType_TLT_PRODUCT_DIY"
	case TagLibraryType_TLT_PRODUCT_CATEGORY:
		return "TagLibraryType_TLT_PRODUCT_CATEGORY"
	case TagLibraryType_TLT_UGC_HASHTAG:
		return "TagLibraryType_TLT_UGC_HASHTAG"
	case TagLibraryType_TLT_UGC_CORRECTION:
		return "TagLibraryType_TLT_UGC_CORRECTION"
	case TagLibraryType_TLT_COLLECTION:
		return "TagLibraryType_TLT_COLLECTION"
	case TagLibraryType_TLT_MODLE_WORD:
		return "TagLibraryType_TLT_MODLE_WORD"
	case TagLibraryType_TLT_MODLE_TAG:
		return "TagLibraryType_TLT_MODLE_TAG"
	case TagLibraryType_TLT_PRODUCT_AZURE_TAGS:
		return "TagLibraryType_TLT_PRODUCT_AZURE_TAGS"
	case TagLibraryType_TLT_UGC_AZURE_TAGS:
		return "TagLibraryType_TLT_UGC_AZURE_TAGS"
	}
	return "<UNSET>"
}

func TagLibraryTypeFromString(s string) (TagLibraryType, error) {
	switch s {
	case "TagLibraryType_TLT_UNKNOWN":
		return TagLibraryType_TLT_UNKNOWN, nil
	case "TagLibraryType_TLT_PRODUCT_DIY":
		return TagLibraryType_TLT_PRODUCT_DIY, nil
	case "TagLibraryType_TLT_PRODUCT_CATEGORY":
		return TagLibraryType_TLT_PRODUCT_CATEGORY, nil
	case "TagLibraryType_TLT_UGC_HASHTAG":
		return TagLibraryType_TLT_UGC_HASHTAG, nil
	case "TagLibraryType_TLT_UGC_CORRECTION":
		return TagLibraryType_TLT_UGC_CORRECTION, nil
	case "TagLibraryType_TLT_COLLECTION":
		return TagLibraryType_TLT_COLLECTION, nil
	case "TagLibraryType_TLT_MODLE_WORD":
		return TagLibraryType_TLT_MODLE_WORD, nil
	case "TagLibraryType_TLT_MODLE_TAG":
		return TagLibraryType_TLT_MODLE_TAG, nil
	case "TagLibraryType_TLT_PRODUCT_AZURE_TAGS":
		return TagLibraryType_TLT_PRODUCT_AZURE_TAGS, nil
	case "TagLibraryType_TLT_UGC_AZURE_TAGS":
		return TagLibraryType_TLT_UGC_AZURE_TAGS, nil
	}
	return TagLibraryType(math.MinInt32 - 1), fmt.Errorf("not a valid TagLibraryType string")
}

//社交媒体平台
type SocialMediaPlatform int64

const (
	SocialMediaPlatform_SMP_UNKNOWN  SocialMediaPlatform = 0
	SocialMediaPlatform_SMP_FACEBOOK SocialMediaPlatform = 1
)

func (p SocialMediaPlatform) String() string {
	switch p {
	case SocialMediaPlatform_SMP_UNKNOWN:
		return "SocialMediaPlatform_SMP_UNKNOWN"
	case SocialMediaPlatform_SMP_FACEBOOK:
		return "SocialMediaPlatform_SMP_FACEBOOK"
	}
	return "<UNSET>"
}

func SocialMediaPlatformFromString(s string) (SocialMediaPlatform, error) {
	switch s {
	case "SocialMediaPlatform_SMP_UNKNOWN":
		return SocialMediaPlatform_SMP_UNKNOWN, nil
	case "SocialMediaPlatform_SMP_FACEBOOK":
		return SocialMediaPlatform_SMP_FACEBOOK, nil
	}
	return SocialMediaPlatform(math.MinInt32 - 1), fmt.Errorf("not a valid SocialMediaPlatform string")
}

//帖文发布状态
type SocialMediaPostPublishStatus int64

const (
	SocialMediaPostPublishStatus_SMPS_UNKNOWN        SocialMediaPostPublishStatus = 0
	SocialMediaPostPublishStatus_SMPS_PUBLISHED      SocialMediaPostPublishStatus = 1
	SocialMediaPostPublishStatus_SMPS_PUBLISH_FAILED SocialMediaPostPublishStatus = 2
)

func (p SocialMediaPostPublishStatus) String() string {
	switch p {
	case SocialMediaPostPublishStatus_SMPS_UNKNOWN:
		return "SocialMediaPostPublishStatus_SMPS_UNKNOWN"
	case SocialMediaPostPublishStatus_SMPS_PUBLISHED:
		return "SocialMediaPostPublishStatus_SMPS_PUBLISHED"
	case SocialMediaPostPublishStatus_SMPS_PUBLISH_FAILED:
		return "SocialMediaPostPublishStatus_SMPS_PUBLISH_FAILED"
	}
	return "<UNSET>"
}

func SocialMediaPostPublishStatusFromString(s string) (SocialMediaPostPublishStatus, error) {
	switch s {
	case "SocialMediaPostPublishStatus_SMPS_UNKNOWN":
		return SocialMediaPostPublishStatus_SMPS_UNKNOWN, nil
	case "SocialMediaPostPublishStatus_SMPS_PUBLISHED":
		return SocialMediaPostPublishStatus_SMPS_PUBLISHED, nil
	case "SocialMediaPostPublishStatus_SMPS_PUBLISH_FAILED":
		return SocialMediaPostPublishStatus_SMPS_PUBLISH_FAILED, nil
	}
	return SocialMediaPostPublishStatus(math.MinInt32 - 1), fmt.Errorf("not a valid SocialMediaPostPublishStatus string")
}

//帖文状态
type SocialMediaPostStatus int64

const (
	SocialMediaPostStatus_SMPS_UNKNOWN        SocialMediaPostStatus = 0
	SocialMediaPostStatus_SMPS_DRAFT          SocialMediaPostStatus = 1
	SocialMediaPostStatus_SMPS_PUBLISHING     SocialMediaPostStatus = 2
	SocialMediaPostStatus_SMPS_PUBLISHED      SocialMediaPostStatus = 3
	SocialMediaPostStatus_SMPS_PUBLISH_FAILED SocialMediaPostStatus = 4
	SocialMediaPostStatus_SMPS_RESERVED       SocialMediaPostStatus = 5
	SocialMediaPostStatus_SMPS_DELETED        SocialMediaPostStatus = 9
)

func (p SocialMediaPostStatus) String() string {
	switch p {
	case SocialMediaPostStatus_SMPS_UNKNOWN:
		return "SocialMediaPostStatus_SMPS_UNKNOWN"
	case SocialMediaPostStatus_SMPS_DRAFT:
		return "SocialMediaPostStatus_SMPS_DRAFT"
	case SocialMediaPostStatus_SMPS_PUBLISHING:
		return "SocialMediaPostStatus_SMPS_PUBLISHING"
	case SocialMediaPostStatus_SMPS_PUBLISHED:
		return "SocialMediaPostStatus_SMPS_PUBLISHED"
	case SocialMediaPostStatus_SMPS_PUBLISH_FAILED:
		return "SocialMediaPostStatus_SMPS_PUBLISH_FAILED"
	case SocialMediaPostStatus_SMPS_RESERVED:
		return "SocialMediaPostStatus_SMPS_RESERVED"
	case SocialMediaPostStatus_SMPS_DELETED:
		return "SocialMediaPostStatus_SMPS_DELETED"
	}
	return "<UNSET>"
}

func SocialMediaPostStatusFromString(s string) (SocialMediaPostStatus, error) {
	switch s {
	case "SocialMediaPostStatus_SMPS_UNKNOWN":
		return SocialMediaPostStatus_SMPS_UNKNOWN, nil
	case "SocialMediaPostStatus_SMPS_DRAFT":
		return SocialMediaPostStatus_SMPS_DRAFT, nil
	case "SocialMediaPostStatus_SMPS_PUBLISHING":
		return SocialMediaPostStatus_SMPS_PUBLISHING, nil
	case "SocialMediaPostStatus_SMPS_PUBLISHED":
		return SocialMediaPostStatus_SMPS_PUBLISHED, nil
	case "SocialMediaPostStatus_SMPS_PUBLISH_FAILED":
		return SocialMediaPostStatus_SMPS_PUBLISH_FAILED, nil
	case "SocialMediaPostStatus_SMPS_RESERVED":
		return SocialMediaPostStatus_SMPS_RESERVED, nil
	case "SocialMediaPostStatus_SMPS_DELETED":
		return SocialMediaPostStatus_SMPS_DELETED, nil
	}
	return SocialMediaPostStatus(math.MinInt32 - 1), fmt.Errorf("not a valid SocialMediaPostStatus string")
}

//社交媒体物料类型
type SocialMediaMaterialType int64

const (
	SocialMediaMaterialType_SMMT_UNKNOWN SocialMediaMaterialType = 0
	SocialMediaMaterialType_SMMT_IMAGE   SocialMediaMaterialType = 1
	SocialMediaMaterialType_SMMT_TEXT    SocialMediaMaterialType = 2
	SocialMediaMaterialType_SMMT_LINK    SocialMediaMaterialType = 3
)

func (p SocialMediaMaterialType) String() string {
	switch p {
	case SocialMediaMaterialType_SMMT_UNKNOWN:
		return "SocialMediaMaterialType_SMMT_UNKNOWN"
	case SocialMediaMaterialType_SMMT_IMAGE:
		return "SocialMediaMaterialType_SMMT_IMAGE"
	case SocialMediaMaterialType_SMMT_TEXT:
		return "SocialMediaMaterialType_SMMT_TEXT"
	case SocialMediaMaterialType_SMMT_LINK:
		return "SocialMediaMaterialType_SMMT_LINK"
	}
	return "<UNSET>"
}

func SocialMediaMaterialTypeFromString(s string) (SocialMediaMaterialType, error) {
	switch s {
	case "SocialMediaMaterialType_SMMT_UNKNOWN":
		return SocialMediaMaterialType_SMMT_UNKNOWN, nil
	case "SocialMediaMaterialType_SMMT_IMAGE":
		return SocialMediaMaterialType_SMMT_IMAGE, nil
	case "SocialMediaMaterialType_SMMT_TEXT":
		return SocialMediaMaterialType_SMMT_TEXT, nil
	case "SocialMediaMaterialType_SMMT_LINK":
		return SocialMediaMaterialType_SMMT_LINK, nil
	}
	return SocialMediaMaterialType(math.MinInt32 - 1), fmt.Errorf("not a valid SocialMediaMaterialType string")
}

//帖文发布对应的时区
type PostPublishTimeZone int64

const (
	PostPublishTimeZone_PPTZ_UNKNOWN             PostPublishTimeZone = 0
	PostPublishTimeZone_PPTZ_BEIJING             PostPublishTimeZone = 1
	PostPublishTimeZone_PPTZ_YIDEKE_ISLAND       PostPublishTimeZone = 2
	PostPublishTimeZone_PPTZ_ANCHORAGE           PostPublishTimeZone = 3
	PostPublishTimeZone_PPTZ_CHICAGO             PostPublishTimeZone = 4
	PostPublishTimeZone_PPTZ_DENVER              PostPublishTimeZone = 5
	PostPublishTimeZone_PPTZ_INDIANA             PostPublishTimeZone = 6
	PostPublishTimeZone_PPTZ_LOS_ANGELES         PostPublishTimeZone = 7
	PostPublishTimeZone_PPTZ_MEXICO_CITY         PostPublishTimeZone = 8
	PostPublishTimeZone_PPTZ_NEW_YORK            PostPublishTimeZone = 9
	PostPublishTimeZone_PPTZ_PHOENIX             PostPublishTimeZone = 10
	PostPublishTimeZone_PPTZ_KAMCHATKA_PENINSULA PostPublishTimeZone = 11
	PostPublishTimeZone_PPTZ_KRASNOYARSK         PostPublishTimeZone = 12
	PostPublishTimeZone_PPTZ_IRKUTSK             PostPublishTimeZone = 13
	PostPublishTimeZone_PPTZ_MAGADAN             PostPublishTimeZone = 14
	PostPublishTimeZone_PPTZ_NOVOSIBIRSK         PostPublishTimeZone = 15
	PostPublishTimeZone_PPTZ_YAKUTSK             PostPublishTimeZone = 16
	PostPublishTimeZone_PPTZ_EKATERINBURG        PostPublishTimeZone = 17
	PostPublishTimeZone_PPTZ_MOSCOW              PostPublishTimeZone = 18
	PostPublishTimeZone_PPTZ_MINSK               PostPublishTimeZone = 19
	PostPublishTimeZone_PPTZ_VOLGOGRAD           PostPublishTimeZone = 20
)

func (p PostPublishTimeZone) String() string {
	switch p {
	case PostPublishTimeZone_PPTZ_UNKNOWN:
		return "PostPublishTimeZone_PPTZ_UNKNOWN"
	case PostPublishTimeZone_PPTZ_BEIJING:
		return "PostPublishTimeZone_PPTZ_BEIJING"
	case PostPublishTimeZone_PPTZ_YIDEKE_ISLAND:
		return "PostPublishTimeZone_PPTZ_YIDEKE_ISLAND"
	case PostPublishTimeZone_PPTZ_ANCHORAGE:
		return "PostPublishTimeZone_PPTZ_ANCHORAGE"
	case PostPublishTimeZone_PPTZ_CHICAGO:
		return "PostPublishTimeZone_PPTZ_CHICAGO"
	case PostPublishTimeZone_PPTZ_DENVER:
		return "PostPublishTimeZone_PPTZ_DENVER"
	case PostPublishTimeZone_PPTZ_INDIANA:
		return "PostPublishTimeZone_PPTZ_INDIANA"
	case PostPublishTimeZone_PPTZ_LOS_ANGELES:
		return "PostPublishTimeZone_PPTZ_LOS_ANGELES"
	case PostPublishTimeZone_PPTZ_MEXICO_CITY:
		return "PostPublishTimeZone_PPTZ_MEXICO_CITY"
	case PostPublishTimeZone_PPTZ_NEW_YORK:
		return "PostPublishTimeZone_PPTZ_NEW_YORK"
	case PostPublishTimeZone_PPTZ_PHOENIX:
		return "PostPublishTimeZone_PPTZ_PHOENIX"
	case PostPublishTimeZone_PPTZ_KAMCHATKA_PENINSULA:
		return "PostPublishTimeZone_PPTZ_KAMCHATKA_PENINSULA"
	case PostPublishTimeZone_PPTZ_KRASNOYARSK:
		return "PostPublishTimeZone_PPTZ_KRASNOYARSK"
	case PostPublishTimeZone_PPTZ_IRKUTSK:
		return "PostPublishTimeZone_PPTZ_IRKUTSK"
	case PostPublishTimeZone_PPTZ_MAGADAN:
		return "PostPublishTimeZone_PPTZ_MAGADAN"
	case PostPublishTimeZone_PPTZ_NOVOSIBIRSK:
		return "PostPublishTimeZone_PPTZ_NOVOSIBIRSK"
	case PostPublishTimeZone_PPTZ_YAKUTSK:
		return "PostPublishTimeZone_PPTZ_YAKUTSK"
	case PostPublishTimeZone_PPTZ_EKATERINBURG:
		return "PostPublishTimeZone_PPTZ_EKATERINBURG"
	case PostPublishTimeZone_PPTZ_MOSCOW:
		return "PostPublishTimeZone_PPTZ_MOSCOW"
	case PostPublishTimeZone_PPTZ_MINSK:
		return "PostPublishTimeZone_PPTZ_MINSK"
	case PostPublishTimeZone_PPTZ_VOLGOGRAD:
		return "PostPublishTimeZone_PPTZ_VOLGOGRAD"
	}
	return "<UNSET>"
}

func PostPublishTimeZoneFromString(s string) (PostPublishTimeZone, error) {
	switch s {
	case "PostPublishTimeZone_PPTZ_UNKNOWN":
		return PostPublishTimeZone_PPTZ_UNKNOWN, nil
	case "PostPublishTimeZone_PPTZ_BEIJING":
		return PostPublishTimeZone_PPTZ_BEIJING, nil
	case "PostPublishTimeZone_PPTZ_YIDEKE_ISLAND":
		return PostPublishTimeZone_PPTZ_YIDEKE_ISLAND, nil
	case "PostPublishTimeZone_PPTZ_ANCHORAGE":
		return PostPublishTimeZone_PPTZ_ANCHORAGE, nil
	case "PostPublishTimeZone_PPTZ_CHICAGO":
		return PostPublishTimeZone_PPTZ_CHICAGO, nil
	case "PostPublishTimeZone_PPTZ_DENVER":
		return PostPublishTimeZone_PPTZ_DENVER, nil
	case "PostPublishTimeZone_PPTZ_INDIANA":
		return PostPublishTimeZone_PPTZ_INDIANA, nil
	case "PostPublishTimeZone_PPTZ_LOS_ANGELES":
		return PostPublishTimeZone_PPTZ_LOS_ANGELES, nil
	case "PostPublishTimeZone_PPTZ_MEXICO_CITY":
		return PostPublishTimeZone_PPTZ_MEXICO_CITY, nil
	case "PostPublishTimeZone_PPTZ_NEW_YORK":
		return PostPublishTimeZone_PPTZ_NEW_YORK, nil
	case "PostPublishTimeZone_PPTZ_PHOENIX":
		return PostPublishTimeZone_PPTZ_PHOENIX, nil
	case "PostPublishTimeZone_PPTZ_KAMCHATKA_PENINSULA":
		return PostPublishTimeZone_PPTZ_KAMCHATKA_PENINSULA, nil
	case "PostPublishTimeZone_PPTZ_KRASNOYARSK":
		return PostPublishTimeZone_PPTZ_KRASNOYARSK, nil
	case "PostPublishTimeZone_PPTZ_IRKUTSK":
		return PostPublishTimeZone_PPTZ_IRKUTSK, nil
	case "PostPublishTimeZone_PPTZ_MAGADAN":
		return PostPublishTimeZone_PPTZ_MAGADAN, nil
	case "PostPublishTimeZone_PPTZ_NOVOSIBIRSK":
		return PostPublishTimeZone_PPTZ_NOVOSIBIRSK, nil
	case "PostPublishTimeZone_PPTZ_YAKUTSK":
		return PostPublishTimeZone_PPTZ_YAKUTSK, nil
	case "PostPublishTimeZone_PPTZ_EKATERINBURG":
		return PostPublishTimeZone_PPTZ_EKATERINBURG, nil
	case "PostPublishTimeZone_PPTZ_MOSCOW":
		return PostPublishTimeZone_PPTZ_MOSCOW, nil
	case "PostPublishTimeZone_PPTZ_MINSK":
		return PostPublishTimeZone_PPTZ_MINSK, nil
	case "PostPublishTimeZone_PPTZ_VOLGOGRAD":
		return PostPublishTimeZone_PPTZ_VOLGOGRAD, nil
	}
	return PostPublishTimeZone(math.MinInt32 - 1), fmt.Errorf("not a valid PostPublishTimeZone string")
}

type QueryResult struct {
	Total    int64   `thrift:"total,1" json:"total"`
	Offset   int64   `thrift:"offset,2" json:"offset"`
	Maxlimit int64   `thrift:"maxlimit,3" json:"maxlimit"`
	Ids      []int64 `thrift:"ids,4" json:"ids"`
}

func NewQueryResult() *QueryResult {
	return &QueryResult{}
}

func (p *QueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *QueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Maxlimit = v
	}
	return nil
}

func (p *QueryResult) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Total)); err != nil {
		return fmt.Errorf("%T.total (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total: %s", p, err)
	}
	return err
}

func (p *QueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:offset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:offset: %s", p, err)
	}
	return err
}

func (p *QueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxlimit", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:maxlimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Maxlimit)); err != nil {
		return fmt.Errorf("%T.maxlimit (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:maxlimit: %s", p, err)
	}
	return err
}

func (p *QueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ids: %s", p, err)
		}
	}
	return err
}

func (p *QueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryResult(%+v)", *p)
}

type AdOrderParams struct {
	Status int32  `thrift:"status,1" json:"status"`
	Name   string `thrift:"name,2" json:"name"`
	Offset int32  `thrift:"offset,3" json:"offset"`
	Limit  int32  `thrift:"limit,4" json:"limit"`
}

func NewAdOrderParams() *AdOrderParams {
	return &AdOrderParams{}
}

func (p *AdOrderParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdOrderParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AdOrderParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdOrderParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AdOrderParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AdOrderParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdOrderParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdOrderParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *AdOrderParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdOrderParams(%+v)", *p)
}

type ProductParams struct {
	Url       string        `thrift:"url,1" json:"url"`
	Name      string        `thrift:"name,2" json:"name"`
	AccountId int64         `thrift:"accountId,3" json:"accountId"`
	ProductId int64         `thrift:"productId,4" json:"productId"`
	StartTime int64         `thrift:"startTime,5" json:"startTime"`
	EndTime   int64         `thrift:"endTime,6" json:"endTime"`
	Status    ProductStatus `thrift:"status,7" json:"status"`
}

func NewProductParams() *ProductParams {
	return &ProductParams{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ProductParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ProductParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProductParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *ProductParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ProductParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *ProductParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *ProductParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *ProductParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *ProductParams) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Status = ProductStatus(v)
	}
	return nil
}

func (p *ProductParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProductParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProductParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:url: %s", p, err)
	}
	return err
}

func (p *ProductParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *ProductParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:accountId: %s", p, err)
	}
	return err
}

func (p *ProductParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:productId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ProductId)); err != nil {
		return fmt.Errorf("%T.productId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:productId: %s", p, err)
	}
	return err
}

func (p *ProductParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startTime: %s", p, err)
	}
	return err
}

func (p *ProductParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:endTime: %s", p, err)
	}
	return err
}

func (p *ProductParams) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:status: %s", p, err)
		}
	}
	return err
}

func (p *ProductParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductParams(%+v)", *p)
}

type ProductPackParams struct {
	AccountId int64  `thrift:"accountId,1" json:"accountId"`
	Name      string `thrift:"name,2" json:"name"`
	Id        int64  `thrift:"id,3" json:"id"`
	StartTime int64  `thrift:"startTime,4" json:"startTime"`
	EndTime   int64  `thrift:"endTime,5" json:"endTime"`
}

func NewProductPackParams() *ProductPackParams {
	return &ProductPackParams{}
}

func (p *ProductPackParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProductPackParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *ProductPackParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ProductPackParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ProductPackParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *ProductPackParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *ProductPackParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProductPackParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProductPackParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:accountId: %s", p, err)
	}
	return err
}

func (p *ProductPackParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *ProductPackParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:id: %s", p, err)
	}
	return err
}

func (p *ProductPackParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startTime: %s", p, err)
	}
	return err
}

func (p *ProductPackParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:endTime: %s", p, err)
	}
	return err
}

func (p *ProductPackParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductPackParams(%+v)", *p)
}

type VicoAccount struct {
	Id int64 `thrift:"id,1" json:"id"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Logo        string `thrift:"logo,10" json:"logo"`
	NickName    string `thrift:"nickName,11" json:"nickName"`
	Description string `thrift:"description,12" json:"description"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64 `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewVicoAccount() *VicoAccount {
	return &VicoAccount{}
}

func (p *VicoAccount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VicoAccount) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *VicoAccount) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *VicoAccount) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.NickName = v
	}
	return nil
}

func (p *VicoAccount) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *VicoAccount) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *VicoAccount) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *VicoAccount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VicoAccount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VicoAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:logo: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickName", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:nickName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NickName)); err != nil {
		return fmt.Errorf("%T.nickName (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:nickName: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:description: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *VicoAccount) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *VicoAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VicoAccount(%+v)", *p)
}

type VicoComment struct {
	Id int64 `thrift:"id,1" json:"id"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Logo     string `thrift:"logo,10" json:"logo"`
	NickName string `thrift:"nickName,11" json:"nickName"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Body           string `thrift:"body,20" json:"body"`
	ImgUrl         string `thrift:"imgUrl,21" json:"imgUrl"`
	VideoUrl       string `thrift:"videoUrl,22" json:"videoUrl"`
	VideoThumbnail string `thrift:"videoThumbnail,23" json:"videoThumbnail"`
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewVicoComment() *VicoComment {
	return &VicoComment{}
}

func (p *VicoComment) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VicoComment) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *VicoComment) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *VicoComment) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.NickName = v
	}
	return nil
}

func (p *VicoComment) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Body = v
	}
	return nil
}

func (p *VicoComment) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *VicoComment) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.VideoUrl = v
	}
	return nil
}

func (p *VicoComment) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.VideoThumbnail = v
	}
	return nil
}

func (p *VicoComment) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *VicoComment) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *VicoComment) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VicoComment"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VicoComment) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:logo: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nickName", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:nickName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NickName)); err != nil {
		return fmt.Errorf("%T.nickName (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:nickName: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("body", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:body: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Body)); err != nil {
		return fmt.Errorf("%T.body (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:body: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:imgUrl: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoUrl", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:videoUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoUrl)); err != nil {
		return fmt.Errorf("%T.videoUrl (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:videoUrl: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoThumbnail", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:videoThumbnail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoThumbnail)); err != nil {
		return fmt.Errorf("%T.videoThumbnail (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:videoThumbnail: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *VicoComment) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *VicoComment) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VicoComment(%+v)", *p)
}

type Product struct {
	Id           int64           `thrift:"id,1" json:"id"`
	Platform     MallPlatform    `thrift:"platform,2" json:"platform"`
	AccountId    int64           `thrift:"accountId,3" json:"accountId"`
	UgcId        int64           `thrift:"ugcId,4" json:"ugcId"`
	Category     ProductCategory `thrift:"category,5" json:"category"`
	OutProductId string          `thrift:"outProductId,6" json:"outProductId"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name       string `thrift:"name,10" json:"name"`
	Url        string `thrift:"url,11" json:"url"`
	Text       string `thrift:"text,12" json:"text"`
	Unit       int32  `thrift:"unit,13" json:"unit"`
	UnitSymbol string `thrift:"unitSymbol,14" json:"unitSymbol"`
	Price      int64  `thrift:"price,15" json:"price"`
	ShortUrl   string `thrift:"shortUrl,16" json:"shortUrl"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	ImgUrl    string `thrift:"imgUrl,20" json:"imgUrl"`
	StoreId   string `thrift:"storeId,21" json:"storeId"`
	StoreName string `thrift:"storeName,22" json:"storeName"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Tags          []string       `thrift:"tags,30" json:"tags"`
	Categories    []string       `thrift:"categories,31" json:"categories"`
	Title         string         `thrift:"title,32" json:"title"`
	Content       string         `thrift:"content,33" json:"content"`
	Comments      []*VicoComment `thrift:"comments,34" json:"comments"`
	UgcText       string         `thrift:"ugcText,35" json:"ugcText"`
	PublishStatus PublishStatus  `thrift:"publishStatus,36" json:"publishStatus"`
	Status        ProductStatus  `thrift:"status,37" json:"status"`
	// unused field # 38
	// unused field # 39
	CreateTime int64 `thrift:"createTime,40" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,41" json:"lastUpdate"`
}

func NewProduct() *Product {
	return &Product{
		Platform: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value

		PublishStatus: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Product) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *Product) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *Product) IsSetPublishStatus() bool {
	return int64(p.PublishStatus) != math.MinInt32-1
}

func (p *Product) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Product) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.LIST {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.LIST {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.LIST {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I64 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Product) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Product) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = MallPlatform(v)
	}
	return nil
}

func (p *Product) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *Product) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *Product) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Category = ProductCategory(v)
	}
	return nil
}

func (p *Product) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OutProductId = v
	}
	return nil
}

func (p *Product) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Product) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Product) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *Product) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Unit = v
	}
	return nil
}

func (p *Product) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.UnitSymbol = v
	}
	return nil
}

func (p *Product) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *Product) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ShortUrl = v
	}
	return nil
}

func (p *Product) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *Product) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.StoreId = v
	}
	return nil
}

func (p *Product) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.StoreName = v
	}
	return nil
}

func (p *Product) readField30(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tags = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.Tags = append(p.Tags, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Product) readField31(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Categories = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.Categories = append(p.Categories, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Product) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *Product) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *Product) readField34(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Comments = make([]*VicoComment, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewVicoComment()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Comments = append(p.Comments, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Product) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.UgcText = v
	}
	return nil
}

func (p *Product) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.PublishStatus = PublishStatus(v)
	}
	return nil
}

func (p *Product) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Status = ProductStatus(v)
	}
	return nil
}

func (p *Product) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Product) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Product) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Product"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Product) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Product) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *Product) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:accountId: %s", p, err)
	}
	return err
}

func (p *Product) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ugcId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ugcId: %s", p, err)
	}
	return err
}

func (p *Product) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:category: %s", p, err)
		}
	}
	return err
}

func (p *Product) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("outProductId", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:outProductId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OutProductId)); err != nil {
		return fmt.Errorf("%T.outProductId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:outProductId: %s", p, err)
	}
	return err
}

func (p *Product) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *Product) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:url: %s", p, err)
	}
	return err
}

func (p *Product) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:text: %s", p, err)
	}
	return err
}

func (p *Product) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unit", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:unit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Unit)); err != nil {
		return fmt.Errorf("%T.unit (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:unit: %s", p, err)
	}
	return err
}

func (p *Product) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unitSymbol", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:unitSymbol: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UnitSymbol)); err != nil {
		return fmt.Errorf("%T.unitSymbol (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:unitSymbol: %s", p, err)
	}
	return err
}

func (p *Product) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:price: %s", p, err)
	}
	return err
}

func (p *Product) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shortUrl", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:shortUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShortUrl)); err != nil {
		return fmt.Errorf("%T.shortUrl (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:shortUrl: %s", p, err)
	}
	return err
}

func (p *Product) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:imgUrl: %s", p, err)
	}
	return err
}

func (p *Product) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("storeId", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:storeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StoreId)); err != nil {
		return fmt.Errorf("%T.storeId (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:storeId: %s", p, err)
	}
	return err
}

func (p *Product) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("storeName", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:storeName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StoreName)); err != nil {
		return fmt.Errorf("%T.storeName (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:storeName: %s", p, err)
	}
	return err
}

func (p *Product) writeField30(oprot thrift.TProtocol) (err error) {
	if p.Tags != nil {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:tags: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Tags)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tags {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:tags: %s", p, err)
		}
	}
	return err
}

func (p *Product) writeField31(oprot thrift.TProtocol) (err error) {
	if p.Categories != nil {
		if err := oprot.WriteFieldBegin("categories", thrift.LIST, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:categories: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Categories)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Categories {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:categories: %s", p, err)
		}
	}
	return err
}

func (p *Product) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:title: %s", p, err)
	}
	return err
}

func (p *Product) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:content: %s", p, err)
	}
	return err
}

func (p *Product) writeField34(oprot thrift.TProtocol) (err error) {
	if p.Comments != nil {
		if err := oprot.WriteFieldBegin("comments", thrift.LIST, 34); err != nil {
			return fmt.Errorf("%T write field begin error 34:comments: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Comments)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Comments {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 34:comments: %s", p, err)
		}
	}
	return err
}

func (p *Product) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcText", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:ugcText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcText)); err != nil {
		return fmt.Errorf("%T.ugcText (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:ugcText: %s", p, err)
	}
	return err
}

func (p *Product) writeField36(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishStatus() {
		if err := oprot.WriteFieldBegin("publishStatus", thrift.I32, 36); err != nil {
			return fmt.Errorf("%T write field begin error 36:publishStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PublishStatus)); err != nil {
			return fmt.Errorf("%T.publishStatus (36) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 36:publishStatus: %s", p, err)
		}
	}
	return err
}

func (p *Product) writeField37(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 37); err != nil {
			return fmt.Errorf("%T write field begin error 37:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (37) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 37:status: %s", p, err)
		}
	}
	return err
}

func (p *Product) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:createTime: %s", p, err)
	}
	return err
}

func (p *Product) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Product) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Product(%+v)", *p)
}

type ProductPack struct {
	Id         int64           `thrift:"id,1" json:"id"`
	ProductIds []int64         `thrift:"productIds,2" json:"productIds"`
	AccountId  int64           `thrift:"accountId,3" json:"accountId"`
	Category   ProductCategory `thrift:"category,4" json:"category"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name          string        `thrift:"name,10" json:"name"`
	Title         string        `thrift:"title,11" json:"title"`
	Body          string        `thrift:"body,12" json:"body"`
	IsTop         int16         `thrift:"isTop,13" json:"isTop"`
	ImgUrl        string        `thrift:"imgUrl,14" json:"imgUrl"`
	PublishStatus PublishStatus `thrift:"publishStatus,15" json:"publishStatus"`
	Tags          []string      `thrift:"tags,16" json:"tags"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	SeoTitle       string `thrift:"seoTitle,20" json:"seoTitle"`
	SeoDescription string `thrift:"seoDescription,21" json:"seoDescription"`
	SeoKeywords    string `thrift:"seoKeywords,22" json:"seoKeywords"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	Status     enums.StatusWhetherAvailable `thrift:"status,29" json:"status"`
	CreateTime int64                        `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64                        `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewProductPack() *ProductPack {
	return &ProductPack{
		Category: math.MinInt32 - 1, // unset sentinal value

		PublishStatus: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ProductPack) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *ProductPack) IsSetPublishStatus() bool {
	return int64(p.PublishStatus) != math.MinInt32-1
}

func (p *ProductPack) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ProductPack) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I16 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProductPack) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ProductPack) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProductIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.ProductIds = append(p.ProductIds, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProductPack) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *ProductPack) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Category = ProductCategory(v)
	}
	return nil
}

func (p *ProductPack) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ProductPack) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *ProductPack) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Body = v
	}
	return nil
}

func (p *ProductPack) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.IsTop = v
	}
	return nil
}

func (p *ProductPack) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *ProductPack) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PublishStatus = PublishStatus(v)
	}
	return nil
}

func (p *ProductPack) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tags = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.Tags = append(p.Tags, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProductPack) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.SeoTitle = v
	}
	return nil
}

func (p *ProductPack) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.SeoDescription = v
	}
	return nil
}

func (p *ProductPack) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.SeoKeywords = v
	}
	return nil
}

func (p *ProductPack) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *ProductPack) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *ProductPack) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *ProductPack) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProductPack"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProductPack) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ProductIds != nil {
		if err := oprot.WriteFieldBegin("productIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:productIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.ProductIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProductIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:productIds: %s", p, err)
		}
	}
	return err
}

func (p *ProductPack) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:accountId: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:category: %s", p, err)
		}
	}
	return err
}

func (p *ProductPack) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:title: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("body", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:body: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Body)); err != nil {
		return fmt.Errorf("%T.body (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:body: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isTop", thrift.I16, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:isTop: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.IsTop)); err != nil {
		return fmt.Errorf("%T.isTop (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:isTop: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:imgUrl: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetPublishStatus() {
		if err := oprot.WriteFieldBegin("publishStatus", thrift.I32, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:publishStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PublishStatus)); err != nil {
			return fmt.Errorf("%T.publishStatus (15) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:publishStatus: %s", p, err)
		}
	}
	return err
}

func (p *ProductPack) writeField16(oprot thrift.TProtocol) (err error) {
	if p.Tags != nil {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:tags: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Tags)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tags {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:tags: %s", p, err)
		}
	}
	return err
}

func (p *ProductPack) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seoTitle", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:seoTitle: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SeoTitle)); err != nil {
		return fmt.Errorf("%T.seoTitle (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:seoTitle: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seoDescription", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:seoDescription: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SeoDescription)); err != nil {
		return fmt.Errorf("%T.seoDescription (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:seoDescription: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seoKeywords", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:seoKeywords: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SeoKeywords)); err != nil {
		return fmt.Errorf("%T.seoKeywords (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:seoKeywords: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField29(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 29); err != nil {
			return fmt.Errorf("%T write field begin error 29:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (29) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 29:status: %s", p, err)
		}
	}
	return err
}

func (p *ProductPack) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *ProductPack) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *ProductPack) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProductPack(%+v)", *p)
}

type RelatedAdCampaign struct {
	Channel   AdChannelType  `thrift:"channel,1" json:"channel"`
	Id        int64          `thrift:"id,2" json:"id"`
	Status    CampaignStatus `thrift:"status,3" json:"status"`
	AccountId string         `thrift:"accountId,4" json:"accountId"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	CreateTime int64 `thrift:"createTime,10" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,11" json:"lastUpdate"`
}

func NewRelatedAdCampaign() *RelatedAdCampaign {
	return &RelatedAdCampaign{
		Channel: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RelatedAdCampaign) IsSetChannel() bool {
	return int64(p.Channel) != math.MinInt32-1
}

func (p *RelatedAdCampaign) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *RelatedAdCampaign) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RelatedAdCampaign) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Channel = AdChannelType(v)
	}
	return nil
}

func (p *RelatedAdCampaign) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *RelatedAdCampaign) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = CampaignStatus(v)
	}
	return nil
}

func (p *RelatedAdCampaign) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *RelatedAdCampaign) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *RelatedAdCampaign) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *RelatedAdCampaign) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RelatedAdCampaign"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RelatedAdCampaign) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannel() {
		if err := oprot.WriteFieldBegin("channel", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:channel: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Channel)); err != nil {
			return fmt.Errorf("%T.channel (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:channel: %s", p, err)
		}
	}
	return err
}

func (p *RelatedAdCampaign) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *RelatedAdCampaign) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:status: %s", p, err)
		}
	}
	return err
}

func (p *RelatedAdCampaign) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:accountId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:accountId: %s", p, err)
	}
	return err
}

func (p *RelatedAdCampaign) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *RelatedAdCampaign) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastUpdate: %s", p, err)
	}
	return err
}

func (p *RelatedAdCampaign) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RelatedAdCampaign(%+v)", *p)
}

type AdOrder struct {
	Id            int64           `thrift:"id,1" json:"id"`
	PackId        int64           `thrift:"packId,2" json:"packId"`
	ProductId     int64           `thrift:"productId,3" json:"productId"`
	PromotionType AdPromotionType `thrift:"promotionType,4" json:"promotionType"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name string `thrift:"name,10" json:"name"`
	// unused field # 11
	Channel  []AdChannelType `thrift:"channel,12" json:"channel"`
	Device   AdDeviceType    `thrift:"device,13" json:"device"`
	AdType   AdType          `thrift:"adType,14" json:"adType"`
	Language []string        `thrift:"language,15" json:"language"`
	Country  []string        `thrift:"country,16" json:"country"`
	Postcode string          `thrift:"postcode,17" json:"postcode"`
	// unused field # 18
	Budget      int64 `thrift:"budget,19" json:"budget"`
	TotalBudget int64 `thrift:"totalBudget,20" json:"totalBudget"`
	// unused field # 21
	StartTime int64  `thrift:"startTime,22" json:"startTime"`
	EndTime   int64  `thrift:"endTime,23" json:"endTime"`
	Source    string `thrift:"source,24" json:"source"`
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Paused      enums.PauseStatus            `thrift:"paused,30" json:"paused"`
	Status      enums.StatusWhetherAvailable `thrift:"status,31" json:"status"`
	AuditStatus AuditStatus                  `thrift:"auditStatus,32" json:"auditStatus"`
	Note        string                       `thrift:"note,33" json:"note"`
	Ext         string                       `thrift:"ext,34" json:"ext"`
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	RelatedCampaigns  []*RelatedAdCampaign `thrift:"relatedCampaigns,40" json:"relatedCampaigns"`
	DisplayStatus     AdDisplayStatus      `thrift:"displayStatus,41" json:"displayStatus"`
	FbTotalBudget     int64                `thrift:"FbTotalBudget,42" json:"FbTotalBudget"`
	IgTotalBudget     int64                `thrift:"IgTotalBudget,43" json:"IgTotalBudget"`
	GoogleTotalBudget int64                `thrift:"GoogleTotalBudget,44" json:"GoogleTotalBudget"`
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	CreateTime int64 `thrift:"createTime,50" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,51" json:"lastUpdate"`
}

func NewAdOrder() *AdOrder {
	return &AdOrder{
		PromotionType: math.MinInt32 - 1, // unset sentinal value

		Device: math.MinInt32 - 1, // unset sentinal value

		AdType: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		AuditStatus: math.MinInt32 - 1, // unset sentinal value

		DisplayStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdOrder) IsSetPromotionType() bool {
	return int64(p.PromotionType) != math.MinInt32-1
}

func (p *AdOrder) IsSetDevice() bool {
	return int64(p.Device) != math.MinInt32-1
}

func (p *AdOrder) IsSetAdType() bool {
	return int64(p.AdType) != math.MinInt32-1
}

func (p *AdOrder) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *AdOrder) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdOrder) IsSetAuditStatus() bool {
	return int64(p.AuditStatus) != math.MinInt32-1
}

func (p *AdOrder) IsSetDisplayStatus() bool {
	return int64(p.DisplayStatus) != math.MinInt32-1
}

func (p *AdOrder) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.LIST {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I64 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I64 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I64 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I64 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdOrder) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdOrder) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PackId = v
	}
	return nil
}

func (p *AdOrder) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *AdOrder) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PromotionType = AdPromotionType(v)
	}
	return nil
}

func (p *AdOrder) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdOrder) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Channel = make([]AdChannelType, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 AdChannelType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = AdChannelType(v)
		}
		p.Channel = append(p.Channel, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdOrder) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Device = AdDeviceType(v)
	}
	return nil
}

func (p *AdOrder) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.AdType = AdType(v)
	}
	return nil
}

func (p *AdOrder) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Language = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.Language = append(p.Language, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdOrder) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Country = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.Country = append(p.Country, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdOrder) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Postcode = v
	}
	return nil
}

func (p *AdOrder) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *AdOrder) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *AdOrder) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *AdOrder) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *AdOrder) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *AdOrder) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Paused = enums.PauseStatus(v)
	}
	return nil
}

func (p *AdOrder) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *AdOrder) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.AuditStatus = AuditStatus(v)
	}
	return nil
}

func (p *AdOrder) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AdOrder) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Ext = v
	}
	return nil
}

func (p *AdOrder) readField40(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RelatedCampaigns = make([]*RelatedAdCampaign, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewRelatedAdCampaign()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.RelatedCampaigns = append(p.RelatedCampaigns, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdOrder) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.DisplayStatus = AdDisplayStatus(v)
	}
	return nil
}

func (p *AdOrder) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.FbTotalBudget = v
	}
	return nil
}

func (p *AdOrder) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.IgTotalBudget = v
	}
	return nil
}

func (p *AdOrder) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.GoogleTotalBudget = v
	}
	return nil
}

func (p *AdOrder) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AdOrder) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *AdOrder) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdOrder"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdOrder) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:packId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PackId)); err != nil {
		return fmt.Errorf("%T.packId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:packId: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:productId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ProductId)); err != nil {
		return fmt.Errorf("%T.productId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:productId: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromotionType() {
		if err := oprot.WriteFieldBegin("promotionType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:promotionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PromotionType)); err != nil {
			return fmt.Errorf("%T.promotionType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:promotionType: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField12(oprot thrift.TProtocol) (err error) {
	if p.Channel != nil {
		if err := oprot.WriteFieldBegin("channel", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:channel: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Channel)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Channel {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:channel: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetDevice() {
		if err := oprot.WriteFieldBegin("device", thrift.I32, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:device: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Device)); err != nil {
			return fmt.Errorf("%T.device (13) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:device: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdType() {
		if err := oprot.WriteFieldBegin("adType", thrift.I32, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:adType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdType)); err != nil {
			return fmt.Errorf("%T.adType (14) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:adType: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField15(oprot thrift.TProtocol) (err error) {
	if p.Language != nil {
		if err := oprot.WriteFieldBegin("language", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:language: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Language)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Language {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:language: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField16(oprot thrift.TProtocol) (err error) {
	if p.Country != nil {
		if err := oprot.WriteFieldBegin("country", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:country: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Country)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Country {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:country: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("postcode", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:postcode: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Postcode)); err != nil {
		return fmt.Errorf("%T.postcode (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:postcode: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:budget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:totalBudget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:startTime: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:endTime: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:source: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField30(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (30) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:paused: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:status: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField32(oprot thrift.TProtocol) (err error) {
	if p.IsSetAuditStatus() {
		if err := oprot.WriteFieldBegin("auditStatus", thrift.I32, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:auditStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AuditStatus)); err != nil {
			return fmt.Errorf("%T.auditStatus (32) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:auditStatus: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:note: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:ext: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ext)); err != nil {
		return fmt.Errorf("%T.ext (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:ext: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField40(oprot thrift.TProtocol) (err error) {
	if p.RelatedCampaigns != nil {
		if err := oprot.WriteFieldBegin("relatedCampaigns", thrift.LIST, 40); err != nil {
			return fmt.Errorf("%T write field begin error 40:relatedCampaigns: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RelatedCampaigns)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RelatedCampaigns {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 40:relatedCampaigns: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField41(oprot thrift.TProtocol) (err error) {
	if p.IsSetDisplayStatus() {
		if err := oprot.WriteFieldBegin("displayStatus", thrift.I32, 41); err != nil {
			return fmt.Errorf("%T write field begin error 41:displayStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DisplayStatus)); err != nil {
			return fmt.Errorf("%T.displayStatus (41) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 41:displayStatus: %s", p, err)
		}
	}
	return err
}

func (p *AdOrder) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("FbTotalBudget", thrift.I64, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:FbTotalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbTotalBudget)); err != nil {
		return fmt.Errorf("%T.FbTotalBudget (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:FbTotalBudget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("IgTotalBudget", thrift.I64, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:IgTotalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.IgTotalBudget)); err != nil {
		return fmt.Errorf("%T.IgTotalBudget (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:IgTotalBudget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("GoogleTotalBudget", thrift.I64, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:GoogleTotalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.GoogleTotalBudget)); err != nil {
		return fmt.Errorf("%T.GoogleTotalBudget (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:GoogleTotalBudget: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:createTime: %s", p, err)
	}
	return err
}

func (p *AdOrder) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdOrder) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdOrder(%+v)", *p)
}

type AdProductOrderInfo struct {
	Order   *AdOrder     `thrift:"order,1" json:"order"`
	Product *Product     `thrift:"product,2" json:"product"`
	Pack    *ProductPack `thrift:"pack,3" json:"pack"`
}

func NewAdProductOrderInfo() *AdProductOrderInfo {
	return &AdProductOrderInfo{}
}

func (p *AdProductOrderInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdProductOrderInfo) readField1(iprot thrift.TProtocol) error {
	p.Order = NewAdOrder()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *AdProductOrderInfo) readField2(iprot thrift.TProtocol) error {
	p.Product = NewProduct()
	if err := p.Product.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Product)
	}
	return nil
}

func (p *AdProductOrderInfo) readField3(iprot thrift.TProtocol) error {
	p.Pack = NewProductPack()
	if err := p.Pack.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pack)
	}
	return nil
}

func (p *AdProductOrderInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdProductOrderInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdProductOrderInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:order: %s", p, err)
		}
	}
	return err
}

func (p *AdProductOrderInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Product != nil {
		if err := oprot.WriteFieldBegin("product", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:product: %s", p, err)
		}
		if err := p.Product.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Product)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:product: %s", p, err)
		}
	}
	return err
}

func (p *AdProductOrderInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Pack != nil {
		if err := oprot.WriteFieldBegin("pack", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:pack: %s", p, err)
		}
		if err := p.Pack.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pack)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:pack: %s", p, err)
		}
	}
	return err
}

func (p *AdProductOrderInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdProductOrderInfo(%+v)", *p)
}

type Ugc struct {
	Id    int64  `thrift:"id,1" json:"id"`
	UgcId string `thrift:"ugcId,2" json:"ugcId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Username string `thrift:"username,10" json:"username"`
	IconUrl  string `thrift:"iconUrl,11" json:"iconUrl"`
	ImgUrl   string `thrift:"imgUrl,12" json:"imgUrl"`
	Content  string `thrift:"content,13" json:"content"`
	VideoUrl string `thrift:"videoUrl,14" json:"videoUrl"`
	KeyWord  string `thrift:"keyWord,15" json:"keyWord"`
	Likes    int32  `thrift:"likes,16" json:"likes"`
	ImgSize  int32  `thrift:"imgSize,17" json:"imgSize"`
	// unused field # 18
	// unused field # 19
	CreateTime int64 `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewUgc() *Ugc {
	return &Ugc{}
}

func (p *Ugc) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Ugc) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Ugc) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *Ugc) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *Ugc) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.IconUrl = v
	}
	return nil
}

func (p *Ugc) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *Ugc) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *Ugc) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.VideoUrl = v
	}
	return nil
}

func (p *Ugc) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.KeyWord = v
	}
	return nil
}

func (p *Ugc) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Likes = v
	}
	return nil
}

func (p *Ugc) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ImgSize = v
	}
	return nil
}

func (p *Ugc) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Ugc) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Ugc) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Ugc"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Ugc) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ugcId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ugcId: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:username: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iconUrl", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:iconUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IconUrl)); err != nil {
		return fmt.Errorf("%T.iconUrl (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:iconUrl: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:imgUrl: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:content: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videoUrl", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:videoUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoUrl)); err != nil {
		return fmt.Errorf("%T.videoUrl (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:videoUrl: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyWord", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:keyWord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.KeyWord)); err != nil {
		return fmt.Errorf("%T.keyWord (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:keyWord: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("likes", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:likes: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Likes)); err != nil {
		return fmt.Errorf("%T.likes (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:likes: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgSize", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:imgSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImgSize)); err != nil {
		return fmt.Errorf("%T.imgSize (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:imgSize: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *Ugc) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Ugc) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Ugc(%+v)", *p)
}

type FtLandingQueryParams struct {
	AccountId int64  `thrift:"accountId,1" json:"accountId"`
	LandingId int64  `thrift:"landingId,2" json:"landingId"`
	Name      string `thrift:"name,3" json:"name"`
	StartTime int64  `thrift:"startTime,4" json:"startTime"`
	EndTime   int64  `thrift:"endTime,5" json:"endTime"`
}

func NewFtLandingQueryParams() *FtLandingQueryParams {
	return &FtLandingQueryParams{}
}

func (p *FtLandingQueryParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FtLandingQueryParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *FtLandingQueryParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.LandingId = v
	}
	return nil
}

func (p *FtLandingQueryParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *FtLandingQueryParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *FtLandingQueryParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *FtLandingQueryParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FtLandingQueryParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FtLandingQueryParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:accountId: %s", p, err)
	}
	return err
}

func (p *FtLandingQueryParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landingId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:landingId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LandingId)); err != nil {
		return fmt.Errorf("%T.landingId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:landingId: %s", p, err)
	}
	return err
}

func (p *FtLandingQueryParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *FtLandingQueryParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startTime: %s", p, err)
	}
	return err
}

func (p *FtLandingQueryParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:endTime: %s", p, err)
	}
	return err
}

func (p *FtLandingQueryParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FtLandingQueryParams(%+v)", *p)
}

type FtLandingTemplate struct {
	Id           int64                 `thrift:"id,1" json:"id"`
	TemplateType FtLandingTemplateType `thrift:"templateType,2" json:"templateType"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name        string `thrift:"name,10" json:"name"`
	Description string `thrift:"description,11" json:"description"`
	Thumbnail   string `thrift:"thumbnail,12" json:"thumbnail"`
	CssUrl      string `thrift:"cssUrl,13" json:"cssUrl"`
	Owner       string `thrift:"owner,14" json:"owner"`
	Price       int64  `thrift:"price,15" json:"price"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Status     vico_finance_types.ServiceProductStatus `thrift:"status,20" json:"status"`
	CreateTime int64                                   `thrift:"createTime,21" json:"createTime"`
	LastUpdate int64                                   `thrift:"lastUpdate,22" json:"lastUpdate"`
}

func NewFtLandingTemplate() *FtLandingTemplate {
	return &FtLandingTemplate{
		TemplateType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FtLandingTemplate) IsSetTemplateType() bool {
	return int64(p.TemplateType) != math.MinInt32-1
}

func (p *FtLandingTemplate) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FtLandingTemplate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FtLandingTemplate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FtLandingTemplate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TemplateType = FtLandingTemplateType(v)
	}
	return nil
}

func (p *FtLandingTemplate) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *FtLandingTemplate) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *FtLandingTemplate) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Thumbnail = v
	}
	return nil
}

func (p *FtLandingTemplate) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.CssUrl = v
	}
	return nil
}

func (p *FtLandingTemplate) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Owner = v
	}
	return nil
}

func (p *FtLandingTemplate) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *FtLandingTemplate) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Status = vico_finance_types.ServiceProductStatus(v)
	}
	return nil
}

func (p *FtLandingTemplate) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *FtLandingTemplate) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *FtLandingTemplate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FtLandingTemplate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FtLandingTemplate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTemplateType() {
		if err := oprot.WriteFieldBegin("templateType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:templateType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TemplateType)); err != nil {
			return fmt.Errorf("%T.templateType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:templateType: %s", p, err)
		}
	}
	return err
}

func (p *FtLandingTemplate) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:description: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thumbnail", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:thumbnail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Thumbnail)); err != nil {
		return fmt.Errorf("%T.thumbnail (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:thumbnail: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cssUrl", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:cssUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CssUrl)); err != nil {
		return fmt.Errorf("%T.cssUrl (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:cssUrl: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("owner", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:owner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Owner)); err != nil {
		return fmt.Errorf("%T.owner (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:owner: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:price: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:status: %s", p, err)
		}
	}
	return err
}

func (p *FtLandingTemplate) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:createTime: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:lastUpdate: %s", p, err)
	}
	return err
}

func (p *FtLandingTemplate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FtLandingTemplate(%+v)", *p)
}

type FtLanding struct {
	Id            int64           `thrift:"id,1" json:"id"`
	UgcId         int64           `thrift:"ugcId,2" json:"ugcId"`
	PackId        int64           `thrift:"packId,3" json:"packId"`
	OrderId       int64           `thrift:"orderId,4" json:"orderId"`
	TypeA1        FTLandingType   `thrift:"type,5" json:"type"`
	ProductId     int64           `thrift:"productId,6" json:"productId"`
	PromotionType AdPromotionType `thrift:"promotionType,7" json:"promotionType"`
	TemplateId    int64           `thrift:"templateId,8" json:"templateId"`
	AccountId     int64           `thrift:"accountId,9" json:"accountId"`
	Channel       int16           `thrift:"channel,10" json:"channel"`
	ImgUrl        string          `thrift:"imgUrl,11" json:"imgUrl"`
	Name          string          `thrift:"name,12" json:"name"`
	Comments      []*VicoComment  `thrift:"comments,13" json:"comments"`
	UgcText       string          `thrift:"ugcText,14" json:"ugcText"`
	Description   string          `thrift:"description,15" json:"description"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Status int32 `thrift:"status,20" json:"status"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewFtLanding() *FtLanding {
	return &FtLanding{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		PromotionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FtLanding) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *FtLanding) IsSetPromotionType() bool {
	return int64(p.PromotionType) != math.MinInt32-1
}

func (p *FtLanding) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I16 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FtLanding) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FtLanding) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *FtLanding) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PackId = v
	}
	return nil
}

func (p *FtLanding) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *FtLanding) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = FTLandingType(v)
	}
	return nil
}

func (p *FtLanding) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *FtLanding) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PromotionType = AdPromotionType(v)
	}
	return nil
}

func (p *FtLanding) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *FtLanding) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *FtLanding) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *FtLanding) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *FtLanding) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *FtLanding) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Comments = make([]*VicoComment, 0, size)
	for i := 0; i < size; i++ {
		_elem10 := NewVicoComment()
		if err := _elem10.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem10)
		}
		p.Comments = append(p.Comments, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *FtLanding) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.UgcText = v
	}
	return nil
}

func (p *FtLanding) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *FtLanding) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *FtLanding) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *FtLanding) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *FtLanding) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FtLanding"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FtLanding) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ugcId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ugcId: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:packId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PackId)); err != nil {
		return fmt.Errorf("%T.packId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:packId: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:orderId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:orderId: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:type: %s", p, err)
		}
	}
	return err
}

func (p *FtLanding) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productId", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:productId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ProductId)); err != nil {
		return fmt.Errorf("%T.productId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:productId: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromotionType() {
		if err := oprot.WriteFieldBegin("promotionType", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:promotionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PromotionType)); err != nil {
			return fmt.Errorf("%T.promotionType (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:promotionType: %s", p, err)
		}
	}
	return err
}

func (p *FtLanding) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("templateId", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:templateId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.templateId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:templateId: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:accountId: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.I16, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:channel: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:channel: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:imgUrl: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:name: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField13(oprot thrift.TProtocol) (err error) {
	if p.Comments != nil {
		if err := oprot.WriteFieldBegin("comments", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:comments: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Comments)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Comments {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:comments: %s", p, err)
		}
	}
	return err
}

func (p *FtLanding) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcText", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:ugcText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcText)); err != nil {
		return fmt.Errorf("%T.ugcText (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:ugcText: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:description: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:status: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *FtLanding) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *FtLanding) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FtLanding(%+v)", *p)
}

type SocialMediaPostParams struct {
	Status       SocialMediaPostStatus   `thrift:"status,1" json:"status"`
	Id           int64                   `thrift:"id,2" json:"id"`
	Name         string                  `thrift:"name,3" json:"name"`
	StartTime    int64                   `thrift:"startTime,4" json:"startTime"`
	EndTime      int64                   `thrift:"endTime,5" json:"endTime"`
	MaterialType SocialMediaMaterialType `thrift:"materialType,6" json:"materialType"`
	AccountId    int64                   `thrift:"accountId,7" json:"accountId"`
}

func NewSocialMediaPostParams() *SocialMediaPostParams {
	return &SocialMediaPostParams{
		Status: math.MinInt32 - 1, // unset sentinal value

		MaterialType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SocialMediaPostParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *SocialMediaPostParams) IsSetMaterialType() bool {
	return int64(p.MaterialType) != math.MinInt32-1
}

func (p *SocialMediaPostParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaPostParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = SocialMediaPostStatus(v)
	}
	return nil
}

func (p *SocialMediaPostParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SocialMediaPostParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *SocialMediaPostParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *SocialMediaPostParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *SocialMediaPostParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MaterialType = SocialMediaMaterialType(v)
	}
	return nil
}

func (p *SocialMediaPostParams) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *SocialMediaPostParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SocialMediaPostParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaPostParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPostParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startTime: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:endTime: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostParams) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetMaterialType() {
		if err := oprot.WriteFieldBegin("materialType", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:materialType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MaterialType)); err != nil {
			return fmt.Errorf("%T.materialType (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:materialType: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPostParams) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:accountId: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SocialMediaPostParams(%+v)", *p)
}

type SocialMediaMaterialLibrary struct {
	Id        int64                   `thrift:"id,1" json:"id"`
	AccountId int64                   `thrift:"accountId,2" json:"accountId"`
	TypeA1    SocialMediaMaterialType `thrift:"type,3" json:"type"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name string `thrift:"name,10" json:"name"`
	Url  string `thrift:"url,11" json:"url"`
	Text string `thrift:"text,12" json:"text"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Hash   string                       `thrift:"hash,20" json:"hash"`
	Status enums.StatusWhetherAvailable `thrift:"status,21" json:"status"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewSocialMediaMaterialLibrary() *SocialMediaMaterialLibrary {
	return &SocialMediaMaterialLibrary{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SocialMediaMaterialLibrary) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SocialMediaMaterialLibrary) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *SocialMediaMaterialLibrary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = SocialMediaMaterialType(v)
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Hash = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Status = enums.StatusWhetherAvailable(v)
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SocialMediaMaterialLibrary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaMaterialLibrary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:url: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:text: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hash", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:hash: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hash)); err != nil {
		return fmt.Errorf("%T.hash (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:hash: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (21) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:status: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *SocialMediaMaterialLibrary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SocialMediaMaterialLibrary(%+v)", *p)
}

type SocialMediaPostPublish struct {
	Id         int64                        `thrift:"id,1" json:"id"`
	PostId     int64                        `thrift:"postId,2" json:"postId"`
	Platform   SocialMediaPlatform          `thrift:"platform,3" json:"platform"`
	MediaId    int64                        `thrift:"mediaId,4" json:"mediaId"`
	Status     SocialMediaPostPublishStatus `thrift:"status,5" json:"status"`
	Message    string                       `thrift:"message,6" json:"message"`
	OutMediaId string                       `thrift:"OutMediaId,7" json:"OutMediaId"`
	// unused field # 8
	// unused field # 9
	CreateTime int64 `thrift:"createTime,10" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,11" json:"lastUpdate"`
}

func NewSocialMediaPostPublish() *SocialMediaPostPublish {
	return &SocialMediaPostPublish{
		Platform: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SocialMediaPostPublish) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *SocialMediaPostPublish) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *SocialMediaPostPublish) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaPostPublish) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SocialMediaPostPublish) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PostId = v
	}
	return nil
}

func (p *SocialMediaPostPublish) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Platform = SocialMediaPlatform(v)
	}
	return nil
}

func (p *SocialMediaPostPublish) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MediaId = v
	}
	return nil
}

func (p *SocialMediaPostPublish) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Status = SocialMediaPostPublishStatus(v)
	}
	return nil
}

func (p *SocialMediaPostPublish) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *SocialMediaPostPublish) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.OutMediaId = v
	}
	return nil
}

func (p *SocialMediaPostPublish) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *SocialMediaPostPublish) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *SocialMediaPostPublish) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SocialMediaPostPublish"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaPostPublish) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostPublish) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("postId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:postId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PostId)); err != nil {
		return fmt.Errorf("%T.postId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:postId: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostPublish) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:platform: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPostPublish) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mediaId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaId)); err != nil {
		return fmt.Errorf("%T.mediaId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mediaId: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostPublish) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:status: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPostPublish) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:message: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostPublish) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("OutMediaId", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:OutMediaId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OutMediaId)); err != nil {
		return fmt.Errorf("%T.OutMediaId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:OutMediaId: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostPublish) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostPublish) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastUpdate: %s", p, err)
	}
	return err
}

func (p *SocialMediaPostPublish) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SocialMediaPostPublish(%+v)", *p)
}

type SocialMediaPost struct {
	Id        int64 `thrift:"id,1" json:"id"`
	AccountId int64 `thrift:"accountId,2" json:"accountId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name   string   `thrift:"name,10" json:"name"`
	Text   string   `thrift:"text,11" json:"text"`
	Tags   string   `thrift:"tags,12" json:"tags"`
	Images []string `thrift:"images,13" json:"images"`
	Links  []string `thrift:"links,14" json:"links"`
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Timezone PostPublishTimeZone       `thrift:"timezone,20" json:"timezone"`
	PubTime  int64                     `thrift:"pubTime,21" json:"pubTime"`
	Status   SocialMediaPostStatus     `thrift:"status,22" json:"status"`
	Publishs []*SocialMediaPostPublish `thrift:"publishs,23" json:"publishs"`
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewSocialMediaPost() *SocialMediaPost {
	return &SocialMediaPost{
		Timezone: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SocialMediaPost) IsSetTimezone() bool {
	return int64(p.Timezone) != math.MinInt32-1
}

func (p *SocialMediaPost) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *SocialMediaPost) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.LIST {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaPost) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SocialMediaPost) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *SocialMediaPost) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *SocialMediaPost) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *SocialMediaPost) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Tags = v
	}
	return nil
}

func (p *SocialMediaPost) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Images = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.Images = append(p.Images, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SocialMediaPost) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Links = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.Links = append(p.Links, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SocialMediaPost) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Timezone = PostPublishTimeZone(v)
	}
	return nil
}

func (p *SocialMediaPost) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.PubTime = v
	}
	return nil
}

func (p *SocialMediaPost) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = SocialMediaPostStatus(v)
	}
	return nil
}

func (p *SocialMediaPost) readField23(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Publishs = make([]*SocialMediaPostPublish, 0, size)
	for i := 0; i < size; i++ {
		_elem13 := NewSocialMediaPostPublish()
		if err := _elem13.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem13)
		}
		p.Publishs = append(p.Publishs, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SocialMediaPost) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *SocialMediaPost) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *SocialMediaPost) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SocialMediaPost"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SocialMediaPost) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:text: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tags", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:tags: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tags)); err != nil {
		return fmt.Errorf("%T.tags (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:tags: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) writeField13(oprot thrift.TProtocol) (err error) {
	if p.Images != nil {
		if err := oprot.WriteFieldBegin("images", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:images: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Images)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Images {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:images: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPost) writeField14(oprot thrift.TProtocol) (err error) {
	if p.Links != nil {
		if err := oprot.WriteFieldBegin("links", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:links: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Links)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Links {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:links: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPost) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetTimezone() {
		if err := oprot.WriteFieldBegin("timezone", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:timezone: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Timezone)); err != nil {
			return fmt.Errorf("%T.timezone (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:timezone: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPost) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubTime", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:pubTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PubTime)); err != nil {
		return fmt.Errorf("%T.pubTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:pubTime: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPost) writeField23(oprot thrift.TProtocol) (err error) {
	if p.Publishs != nil {
		if err := oprot.WriteFieldBegin("publishs", thrift.LIST, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:publishs: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Publishs)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Publishs {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:publishs: %s", p, err)
		}
	}
	return err
}

func (p *SocialMediaPost) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *SocialMediaPost) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SocialMediaPost(%+v)", *p)
}
