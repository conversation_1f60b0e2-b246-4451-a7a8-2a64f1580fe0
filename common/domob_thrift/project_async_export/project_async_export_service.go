// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package project_async_export

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/project_async_export_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = project_async_export_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type ProjectAsyncExportService interface { // * Decisionplus导出数据Service
	//*

	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 收件人的Email*
	//  - Param: 查询参数 *
	ExportIncomeSysReceive(header *common.RequestHeader, email string, param *project_async_export_types.IncomeSysReceiveQueryParam) (r bool, de *ProjectAsyncExportException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 收件人的Email*
	//  - Param: 查询参数 *
	ExportIncomeMyDeliveryUnconfirmed(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMyDeliveryQueryParam) (r bool, de *ProjectAsyncExportException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 收件人的Email*
	//  - Param: 查询参数 *
	ExportIncomeMyDeliveryConfirmed(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMyDeliveryQueryParam) (r bool, de *ProjectAsyncExportException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 收件人的Email*
	//  - Param: 查询参数 *
	ExportIncomeFinanceSettle(header *common.RequestHeader, email string, param *project_async_export_types.IncomeFinanceSettleQueryParam) (r bool, de *ProjectAsyncExportException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 收件人的Email*
	//  - Param: 查询参数 *
	ExportIncomePayback(header *common.RequestHeader, email string, param *project_async_export_types.IncomePaybackQueryParam) (r bool, de *ProjectAsyncExportException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 收件人的Email*
	//  - Param: 查询参数 *
	ExportIncomeNonAssociatedInvoice(header *common.RequestHeader, email string, param *project_async_export_types.IncomeNonAssociatedInvoiceQueryParam) (r bool, de *ProjectAsyncExportException, err error)
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Email: 收件人的Email*
	//  - Param: 查询参数 *
	ExportIncomeMonthReport(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMonthReportQueryParam) (r bool, de *ProjectAsyncExportException, err error)
}

// * Decisionplus导出数据Service
//*
type ProjectAsyncExportServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewProjectAsyncExportServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ProjectAsyncExportServiceClient {
	return &ProjectAsyncExportServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewProjectAsyncExportServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ProjectAsyncExportServiceClient {
	return &ProjectAsyncExportServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 收件人的Email*
//  - Param: 查询参数 *
func (p *ProjectAsyncExportServiceClient) ExportIncomeSysReceive(header *common.RequestHeader, email string, param *project_async_export_types.IncomeSysReceiveQueryParam) (r bool, de *ProjectAsyncExportException, err error) {
	if err = p.sendExportIncomeSysReceive(header, email, param); err != nil {
		return
	}
	return p.recvExportIncomeSysReceive()
}

func (p *ProjectAsyncExportServiceClient) sendExportIncomeSysReceive(header *common.RequestHeader, email string, param *project_async_export_types.IncomeSysReceiveQueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exportIncomeSysReceive", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewExportIncomeSysReceiveArgs()
	args0.Header = header
	args0.Email = email
	args0.Param = param
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectAsyncExportServiceClient) recvExportIncomeSysReceive() (value bool, de *ProjectAsyncExportException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewExportIncomeSysReceiveResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.De != nil {
		de = result1.De
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 收件人的Email*
//  - Param: 查询参数 *
func (p *ProjectAsyncExportServiceClient) ExportIncomeMyDeliveryUnconfirmed(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMyDeliveryQueryParam) (r bool, de *ProjectAsyncExportException, err error) {
	if err = p.sendExportIncomeMyDeliveryUnconfirmed(header, email, param); err != nil {
		return
	}
	return p.recvExportIncomeMyDeliveryUnconfirmed()
}

func (p *ProjectAsyncExportServiceClient) sendExportIncomeMyDeliveryUnconfirmed(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMyDeliveryQueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exportIncomeMyDeliveryUnconfirmed", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewExportIncomeMyDeliveryUnconfirmedArgs()
	args4.Header = header
	args4.Email = email
	args4.Param = param
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectAsyncExportServiceClient) recvExportIncomeMyDeliveryUnconfirmed() (value bool, de *ProjectAsyncExportException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewExportIncomeMyDeliveryUnconfirmedResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.De != nil {
		de = result5.De
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 收件人的Email*
//  - Param: 查询参数 *
func (p *ProjectAsyncExportServiceClient) ExportIncomeMyDeliveryConfirmed(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMyDeliveryQueryParam) (r bool, de *ProjectAsyncExportException, err error) {
	if err = p.sendExportIncomeMyDeliveryConfirmed(header, email, param); err != nil {
		return
	}
	return p.recvExportIncomeMyDeliveryConfirmed()
}

func (p *ProjectAsyncExportServiceClient) sendExportIncomeMyDeliveryConfirmed(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMyDeliveryQueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exportIncomeMyDeliveryConfirmed", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewExportIncomeMyDeliveryConfirmedArgs()
	args8.Header = header
	args8.Email = email
	args8.Param = param
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectAsyncExportServiceClient) recvExportIncomeMyDeliveryConfirmed() (value bool, de *ProjectAsyncExportException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewExportIncomeMyDeliveryConfirmedResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.De != nil {
		de = result9.De
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 收件人的Email*
//  - Param: 查询参数 *
func (p *ProjectAsyncExportServiceClient) ExportIncomeFinanceSettle(header *common.RequestHeader, email string, param *project_async_export_types.IncomeFinanceSettleQueryParam) (r bool, de *ProjectAsyncExportException, err error) {
	if err = p.sendExportIncomeFinanceSettle(header, email, param); err != nil {
		return
	}
	return p.recvExportIncomeFinanceSettle()
}

func (p *ProjectAsyncExportServiceClient) sendExportIncomeFinanceSettle(header *common.RequestHeader, email string, param *project_async_export_types.IncomeFinanceSettleQueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exportIncomeFinanceSettle", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewExportIncomeFinanceSettleArgs()
	args12.Header = header
	args12.Email = email
	args12.Param = param
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectAsyncExportServiceClient) recvExportIncomeFinanceSettle() (value bool, de *ProjectAsyncExportException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewExportIncomeFinanceSettleResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.De != nil {
		de = result13.De
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 收件人的Email*
//  - Param: 查询参数 *
func (p *ProjectAsyncExportServiceClient) ExportIncomePayback(header *common.RequestHeader, email string, param *project_async_export_types.IncomePaybackQueryParam) (r bool, de *ProjectAsyncExportException, err error) {
	if err = p.sendExportIncomePayback(header, email, param); err != nil {
		return
	}
	return p.recvExportIncomePayback()
}

func (p *ProjectAsyncExportServiceClient) sendExportIncomePayback(header *common.RequestHeader, email string, param *project_async_export_types.IncomePaybackQueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exportIncomePayback", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewExportIncomePaybackArgs()
	args16.Header = header
	args16.Email = email
	args16.Param = param
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectAsyncExportServiceClient) recvExportIncomePayback() (value bool, de *ProjectAsyncExportException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewExportIncomePaybackResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.De != nil {
		de = result17.De
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 收件人的Email*
//  - Param: 查询参数 *
func (p *ProjectAsyncExportServiceClient) ExportIncomeNonAssociatedInvoice(header *common.RequestHeader, email string, param *project_async_export_types.IncomeNonAssociatedInvoiceQueryParam) (r bool, de *ProjectAsyncExportException, err error) {
	if err = p.sendExportIncomeNonAssociatedInvoice(header, email, param); err != nil {
		return
	}
	return p.recvExportIncomeNonAssociatedInvoice()
}

func (p *ProjectAsyncExportServiceClient) sendExportIncomeNonAssociatedInvoice(header *common.RequestHeader, email string, param *project_async_export_types.IncomeNonAssociatedInvoiceQueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exportIncomeNonAssociatedInvoice", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewExportIncomeNonAssociatedInvoiceArgs()
	args20.Header = header
	args20.Email = email
	args20.Param = param
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectAsyncExportServiceClient) recvExportIncomeNonAssociatedInvoice() (value bool, de *ProjectAsyncExportException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewExportIncomeNonAssociatedInvoiceResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.De != nil {
		de = result21.De
	}
	return
}

// Parameters:
//  - Header: 请求消息头结构体
//  - Email: 收件人的Email*
//  - Param: 查询参数 *
func (p *ProjectAsyncExportServiceClient) ExportIncomeMonthReport(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMonthReportQueryParam) (r bool, de *ProjectAsyncExportException, err error) {
	if err = p.sendExportIncomeMonthReport(header, email, param); err != nil {
		return
	}
	return p.recvExportIncomeMonthReport()
}

func (p *ProjectAsyncExportServiceClient) sendExportIncomeMonthReport(header *common.RequestHeader, email string, param *project_async_export_types.IncomeMonthReportQueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("exportIncomeMonthReport", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewExportIncomeMonthReportArgs()
	args24.Header = header
	args24.Email = email
	args24.Param = param
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProjectAsyncExportServiceClient) recvExportIncomeMonthReport() (value bool, de *ProjectAsyncExportException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewExportIncomeMonthReportResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.De != nil {
		de = result25.De
	}
	return
}

type ProjectAsyncExportServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      ProjectAsyncExportService
}

func (p *ProjectAsyncExportServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *ProjectAsyncExportServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *ProjectAsyncExportServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewProjectAsyncExportServiceProcessor(handler ProjectAsyncExportService) *ProjectAsyncExportServiceProcessor {

	self28 := &ProjectAsyncExportServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self28.processorMap["exportIncomeSysReceive"] = &projectAsyncExportServiceProcessorExportIncomeSysReceive{handler: handler}
	self28.processorMap["exportIncomeMyDeliveryUnconfirmed"] = &projectAsyncExportServiceProcessorExportIncomeMyDeliveryUnconfirmed{handler: handler}
	self28.processorMap["exportIncomeMyDeliveryConfirmed"] = &projectAsyncExportServiceProcessorExportIncomeMyDeliveryConfirmed{handler: handler}
	self28.processorMap["exportIncomeFinanceSettle"] = &projectAsyncExportServiceProcessorExportIncomeFinanceSettle{handler: handler}
	self28.processorMap["exportIncomePayback"] = &projectAsyncExportServiceProcessorExportIncomePayback{handler: handler}
	self28.processorMap["exportIncomeNonAssociatedInvoice"] = &projectAsyncExportServiceProcessorExportIncomeNonAssociatedInvoice{handler: handler}
	self28.processorMap["exportIncomeMonthReport"] = &projectAsyncExportServiceProcessorExportIncomeMonthReport{handler: handler}
	return self28
}

func (p *ProjectAsyncExportServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x29 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x29.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x29

}

type projectAsyncExportServiceProcessorExportIncomeSysReceive struct {
	handler ProjectAsyncExportService
}

func (p *projectAsyncExportServiceProcessorExportIncomeSysReceive) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExportIncomeSysReceiveArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exportIncomeSysReceive", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExportIncomeSysReceiveResult()
	if result.Success, result.De, err = p.handler.ExportIncomeSysReceive(args.Header, args.Email, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exportIncomeSysReceive: "+err.Error())
		oprot.WriteMessageBegin("exportIncomeSysReceive", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exportIncomeSysReceive", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectAsyncExportServiceProcessorExportIncomeMyDeliveryUnconfirmed struct {
	handler ProjectAsyncExportService
}

func (p *projectAsyncExportServiceProcessorExportIncomeMyDeliveryUnconfirmed) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExportIncomeMyDeliveryUnconfirmedArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exportIncomeMyDeliveryUnconfirmed", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExportIncomeMyDeliveryUnconfirmedResult()
	if result.Success, result.De, err = p.handler.ExportIncomeMyDeliveryUnconfirmed(args.Header, args.Email, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exportIncomeMyDeliveryUnconfirmed: "+err.Error())
		oprot.WriteMessageBegin("exportIncomeMyDeliveryUnconfirmed", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exportIncomeMyDeliveryUnconfirmed", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectAsyncExportServiceProcessorExportIncomeMyDeliveryConfirmed struct {
	handler ProjectAsyncExportService
}

func (p *projectAsyncExportServiceProcessorExportIncomeMyDeliveryConfirmed) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExportIncomeMyDeliveryConfirmedArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exportIncomeMyDeliveryConfirmed", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExportIncomeMyDeliveryConfirmedResult()
	if result.Success, result.De, err = p.handler.ExportIncomeMyDeliveryConfirmed(args.Header, args.Email, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exportIncomeMyDeliveryConfirmed: "+err.Error())
		oprot.WriteMessageBegin("exportIncomeMyDeliveryConfirmed", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exportIncomeMyDeliveryConfirmed", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectAsyncExportServiceProcessorExportIncomeFinanceSettle struct {
	handler ProjectAsyncExportService
}

func (p *projectAsyncExportServiceProcessorExportIncomeFinanceSettle) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExportIncomeFinanceSettleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exportIncomeFinanceSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExportIncomeFinanceSettleResult()
	if result.Success, result.De, err = p.handler.ExportIncomeFinanceSettle(args.Header, args.Email, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exportIncomeFinanceSettle: "+err.Error())
		oprot.WriteMessageBegin("exportIncomeFinanceSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exportIncomeFinanceSettle", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectAsyncExportServiceProcessorExportIncomePayback struct {
	handler ProjectAsyncExportService
}

func (p *projectAsyncExportServiceProcessorExportIncomePayback) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExportIncomePaybackArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exportIncomePayback", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExportIncomePaybackResult()
	if result.Success, result.De, err = p.handler.ExportIncomePayback(args.Header, args.Email, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exportIncomePayback: "+err.Error())
		oprot.WriteMessageBegin("exportIncomePayback", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exportIncomePayback", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectAsyncExportServiceProcessorExportIncomeNonAssociatedInvoice struct {
	handler ProjectAsyncExportService
}

func (p *projectAsyncExportServiceProcessorExportIncomeNonAssociatedInvoice) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExportIncomeNonAssociatedInvoiceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exportIncomeNonAssociatedInvoice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExportIncomeNonAssociatedInvoiceResult()
	if result.Success, result.De, err = p.handler.ExportIncomeNonAssociatedInvoice(args.Header, args.Email, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exportIncomeNonAssociatedInvoice: "+err.Error())
		oprot.WriteMessageBegin("exportIncomeNonAssociatedInvoice", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exportIncomeNonAssociatedInvoice", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type projectAsyncExportServiceProcessorExportIncomeMonthReport struct {
	handler ProjectAsyncExportService
}

func (p *projectAsyncExportServiceProcessorExportIncomeMonthReport) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExportIncomeMonthReportArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("exportIncomeMonthReport", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExportIncomeMonthReportResult()
	if result.Success, result.De, err = p.handler.ExportIncomeMonthReport(args.Header, args.Email, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing exportIncomeMonthReport: "+err.Error())
		oprot.WriteMessageBegin("exportIncomeMonthReport", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("exportIncomeMonthReport", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ExportIncomeSysReceiveArgs struct {
	Header *common.RequestHeader                                  `thrift:"header,1" json:"header"`
	Email  string                                                 `thrift:"email,2" json:"email"`
	Param  *project_async_export_types.IncomeSysReceiveQueryParam `thrift:"param,3" json:"param"`
}

func NewExportIncomeSysReceiveArgs() *ExportIncomeSysReceiveArgs {
	return &ExportIncomeSysReceiveArgs{}
}

func (p *ExportIncomeSysReceiveArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeSysReceiveArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExportIncomeSysReceiveArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ExportIncomeSysReceiveArgs) readField3(iprot thrift.TProtocol) error {
	p.Param = project_async_export_types.NewIncomeSysReceiveQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *ExportIncomeSysReceiveArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeSysReceive_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeSysReceiveArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeSysReceiveArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *ExportIncomeSysReceiveArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:param: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeSysReceiveArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeSysReceiveArgs(%+v)", *p)
}

type ExportIncomeSysReceiveResult struct {
	Success bool                         `thrift:"success,0" json:"success"`
	De      *ProjectAsyncExportException `thrift:"de,1" json:"de"`
}

func NewExportIncomeSysReceiveResult() *ExportIncomeSysReceiveResult {
	return &ExportIncomeSysReceiveResult{}
}

func (p *ExportIncomeSysReceiveResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeSysReceiveResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExportIncomeSysReceiveResult) readField1(iprot thrift.TProtocol) error {
	p.De = NewProjectAsyncExportException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ExportIncomeSysReceiveResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeSysReceive_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeSysReceiveResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExportIncomeSysReceiveResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeSysReceiveResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeSysReceiveResult(%+v)", *p)
}

type ExportIncomeMyDeliveryUnconfirmedArgs struct {
	Header *common.RequestHeader                                  `thrift:"header,1" json:"header"`
	Email  string                                                 `thrift:"email,2" json:"email"`
	Param  *project_async_export_types.IncomeMyDeliveryQueryParam `thrift:"param,3" json:"param"`
}

func NewExportIncomeMyDeliveryUnconfirmedArgs() *ExportIncomeMyDeliveryUnconfirmedArgs {
	return &ExportIncomeMyDeliveryUnconfirmedArgs{}
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) readField3(iprot thrift.TProtocol) error {
	p.Param = project_async_export_types.NewIncomeMyDeliveryQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeMyDeliveryUnconfirmed_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:param: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMyDeliveryUnconfirmedArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeMyDeliveryUnconfirmedArgs(%+v)", *p)
}

type ExportIncomeMyDeliveryUnconfirmedResult struct {
	Success bool                         `thrift:"success,0" json:"success"`
	De      *ProjectAsyncExportException `thrift:"de,1" json:"de"`
}

func NewExportIncomeMyDeliveryUnconfirmedResult() *ExportIncomeMyDeliveryUnconfirmedResult {
	return &ExportIncomeMyDeliveryUnconfirmedResult{}
}

func (p *ExportIncomeMyDeliveryUnconfirmedResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedResult) readField1(iprot thrift.TProtocol) error {
	p.De = NewProjectAsyncExportException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeMyDeliveryUnconfirmed_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryUnconfirmedResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExportIncomeMyDeliveryUnconfirmedResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMyDeliveryUnconfirmedResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeMyDeliveryUnconfirmedResult(%+v)", *p)
}

type ExportIncomeMyDeliveryConfirmedArgs struct {
	Header *common.RequestHeader                                  `thrift:"header,1" json:"header"`
	Email  string                                                 `thrift:"email,2" json:"email"`
	Param  *project_async_export_types.IncomeMyDeliveryQueryParam `thrift:"param,3" json:"param"`
}

func NewExportIncomeMyDeliveryConfirmedArgs() *ExportIncomeMyDeliveryConfirmedArgs {
	return &ExportIncomeMyDeliveryConfirmedArgs{}
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) readField3(iprot thrift.TProtocol) error {
	p.Param = project_async_export_types.NewIncomeMyDeliveryQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeMyDeliveryConfirmed_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:param: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMyDeliveryConfirmedArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeMyDeliveryConfirmedArgs(%+v)", *p)
}

type ExportIncomeMyDeliveryConfirmedResult struct {
	Success bool                         `thrift:"success,0" json:"success"`
	De      *ProjectAsyncExportException `thrift:"de,1" json:"de"`
}

func NewExportIncomeMyDeliveryConfirmedResult() *ExportIncomeMyDeliveryConfirmedResult {
	return &ExportIncomeMyDeliveryConfirmedResult{}
}

func (p *ExportIncomeMyDeliveryConfirmedResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedResult) readField1(iprot thrift.TProtocol) error {
	p.De = NewProjectAsyncExportException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeMyDeliveryConfirmed_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMyDeliveryConfirmedResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExportIncomeMyDeliveryConfirmedResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMyDeliveryConfirmedResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeMyDeliveryConfirmedResult(%+v)", *p)
}

type ExportIncomeFinanceSettleArgs struct {
	Header *common.RequestHeader                                     `thrift:"header,1" json:"header"`
	Email  string                                                    `thrift:"email,2" json:"email"`
	Param  *project_async_export_types.IncomeFinanceSettleQueryParam `thrift:"param,3" json:"param"`
}

func NewExportIncomeFinanceSettleArgs() *ExportIncomeFinanceSettleArgs {
	return &ExportIncomeFinanceSettleArgs{}
}

func (p *ExportIncomeFinanceSettleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeFinanceSettleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExportIncomeFinanceSettleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ExportIncomeFinanceSettleArgs) readField3(iprot thrift.TProtocol) error {
	p.Param = project_async_export_types.NewIncomeFinanceSettleQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *ExportIncomeFinanceSettleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeFinanceSettle_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeFinanceSettleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeFinanceSettleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *ExportIncomeFinanceSettleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:param: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeFinanceSettleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeFinanceSettleArgs(%+v)", *p)
}

type ExportIncomeFinanceSettleResult struct {
	Success bool                         `thrift:"success,0" json:"success"`
	De      *ProjectAsyncExportException `thrift:"de,1" json:"de"`
}

func NewExportIncomeFinanceSettleResult() *ExportIncomeFinanceSettleResult {
	return &ExportIncomeFinanceSettleResult{}
}

func (p *ExportIncomeFinanceSettleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeFinanceSettleResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExportIncomeFinanceSettleResult) readField1(iprot thrift.TProtocol) error {
	p.De = NewProjectAsyncExportException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ExportIncomeFinanceSettleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeFinanceSettle_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeFinanceSettleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExportIncomeFinanceSettleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeFinanceSettleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeFinanceSettleResult(%+v)", *p)
}

type ExportIncomePaybackArgs struct {
	Header *common.RequestHeader                               `thrift:"header,1" json:"header"`
	Email  string                                              `thrift:"email,2" json:"email"`
	Param  *project_async_export_types.IncomePaybackQueryParam `thrift:"param,3" json:"param"`
}

func NewExportIncomePaybackArgs() *ExportIncomePaybackArgs {
	return &ExportIncomePaybackArgs{}
}

func (p *ExportIncomePaybackArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomePaybackArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExportIncomePaybackArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ExportIncomePaybackArgs) readField3(iprot thrift.TProtocol) error {
	p.Param = project_async_export_types.NewIncomePaybackQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *ExportIncomePaybackArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomePayback_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomePaybackArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomePaybackArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *ExportIncomePaybackArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:param: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomePaybackArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomePaybackArgs(%+v)", *p)
}

type ExportIncomePaybackResult struct {
	Success bool                         `thrift:"success,0" json:"success"`
	De      *ProjectAsyncExportException `thrift:"de,1" json:"de"`
}

func NewExportIncomePaybackResult() *ExportIncomePaybackResult {
	return &ExportIncomePaybackResult{}
}

func (p *ExportIncomePaybackResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomePaybackResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExportIncomePaybackResult) readField1(iprot thrift.TProtocol) error {
	p.De = NewProjectAsyncExportException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ExportIncomePaybackResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomePayback_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomePaybackResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExportIncomePaybackResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomePaybackResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomePaybackResult(%+v)", *p)
}

type ExportIncomeNonAssociatedInvoiceArgs struct {
	Header *common.RequestHeader                                            `thrift:"header,1" json:"header"`
	Email  string                                                           `thrift:"email,2" json:"email"`
	Param  *project_async_export_types.IncomeNonAssociatedInvoiceQueryParam `thrift:"param,3" json:"param"`
}

func NewExportIncomeNonAssociatedInvoiceArgs() *ExportIncomeNonAssociatedInvoiceArgs {
	return &ExportIncomeNonAssociatedInvoiceArgs{}
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) readField3(iprot thrift.TProtocol) error {
	p.Param = project_async_export_types.NewIncomeNonAssociatedInvoiceQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeNonAssociatedInvoice_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:param: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeNonAssociatedInvoiceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeNonAssociatedInvoiceArgs(%+v)", *p)
}

type ExportIncomeNonAssociatedInvoiceResult struct {
	Success bool                         `thrift:"success,0" json:"success"`
	De      *ProjectAsyncExportException `thrift:"de,1" json:"de"`
}

func NewExportIncomeNonAssociatedInvoiceResult() *ExportIncomeNonAssociatedInvoiceResult {
	return &ExportIncomeNonAssociatedInvoiceResult{}
}

func (p *ExportIncomeNonAssociatedInvoiceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceResult) readField1(iprot thrift.TProtocol) error {
	p.De = NewProjectAsyncExportException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeNonAssociatedInvoice_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeNonAssociatedInvoiceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExportIncomeNonAssociatedInvoiceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeNonAssociatedInvoiceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeNonAssociatedInvoiceResult(%+v)", *p)
}

type ExportIncomeMonthReportArgs struct {
	Header *common.RequestHeader                                   `thrift:"header,1" json:"header"`
	Email  string                                                  `thrift:"email,2" json:"email"`
	Param  *project_async_export_types.IncomeMonthReportQueryParam `thrift:"param,3" json:"param"`
}

func NewExportIncomeMonthReportArgs() *ExportIncomeMonthReportArgs {
	return &ExportIncomeMonthReportArgs{}
}

func (p *ExportIncomeMonthReportArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMonthReportArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExportIncomeMonthReportArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ExportIncomeMonthReportArgs) readField3(iprot thrift.TProtocol) error {
	p.Param = project_async_export_types.NewIncomeMonthReportQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *ExportIncomeMonthReportArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeMonthReport_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMonthReportArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMonthReportArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *ExportIncomeMonthReportArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:param: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMonthReportArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeMonthReportArgs(%+v)", *p)
}

type ExportIncomeMonthReportResult struct {
	Success bool                         `thrift:"success,0" json:"success"`
	De      *ProjectAsyncExportException `thrift:"de,1" json:"de"`
}

func NewExportIncomeMonthReportResult() *ExportIncomeMonthReportResult {
	return &ExportIncomeMonthReportResult{}
}

func (p *ExportIncomeMonthReportResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMonthReportResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExportIncomeMonthReportResult) readField1(iprot thrift.TProtocol) error {
	p.De = NewProjectAsyncExportException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *ExportIncomeMonthReportResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("exportIncomeMonthReport_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExportIncomeMonthReportResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExportIncomeMonthReportResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *ExportIncomeMonthReportResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExportIncomeMonthReportResult(%+v)", *p)
}
