// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"project_async_export"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  bool exportIncomeSysReceive(RequestHeader header, string email, IncomeSysReceiveQueryParam param)")
	fmt.Fprintln(os.Stderr, "  bool exportIncomeMyDeliveryUnconfirmed(RequestHeader header, string email, IncomeMyDeliveryQueryParam param)")
	fmt.Fprintln(os.Stderr, "  bool exportIncomeMyDeliveryConfirmed(RequestHeader header, string email, IncomeMyDeliveryQueryParam param)")
	fmt.Fprintln(os.Stderr, "  bool exportIncomeFinanceSettle(RequestHeader header, string email, IncomeFinanceSettleQueryParam param)")
	fmt.Fprintln(os.Stderr, "  bool exportIncomePayback(RequestHeader header, string email, IncomePaybackQueryParam param)")
	fmt.Fprintln(os.Stderr, "  bool exportIncomeNonAssociatedInvoice(RequestHeader header, string email, IncomeNonAssociatedInvoiceQueryParam param)")
	fmt.Fprintln(os.Stderr, "  bool exportIncomeMonthReport(RequestHeader header, string email, IncomeMonthReportQueryParam param)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := project_async_export.NewProjectAsyncExportServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "exportIncomeSysReceive":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportIncomeSysReceive requires 3 args")
			flag.Usage()
		}
		arg30 := flag.Arg(1)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue0 := project_async_export.NewRequestHeader()
		err35 := argvalue0.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg37 := flag.Arg(3)
		mbTrans38 := thrift.NewTMemoryBufferLen(len(arg37))
		defer mbTrans38.Close()
		_, err39 := mbTrans38.WriteString(arg37)
		if err39 != nil {
			Usage()
			return
		}
		factory40 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt41 := factory40.GetProtocol(mbTrans38)
		argvalue2 := project_async_export.NewIncomeSysReceiveQueryParam()
		err42 := argvalue2.Read(jsProt41)
		if err42 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ExportIncomeSysReceive(value0, value1, value2))
		fmt.Print("\n")
		break
	case "exportIncomeMyDeliveryUnconfirmed":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportIncomeMyDeliveryUnconfirmed requires 3 args")
			flag.Usage()
		}
		arg43 := flag.Arg(1)
		mbTrans44 := thrift.NewTMemoryBufferLen(len(arg43))
		defer mbTrans44.Close()
		_, err45 := mbTrans44.WriteString(arg43)
		if err45 != nil {
			Usage()
			return
		}
		factory46 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt47 := factory46.GetProtocol(mbTrans44)
		argvalue0 := project_async_export.NewRequestHeader()
		err48 := argvalue0.Read(jsProt47)
		if err48 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg50 := flag.Arg(3)
		mbTrans51 := thrift.NewTMemoryBufferLen(len(arg50))
		defer mbTrans51.Close()
		_, err52 := mbTrans51.WriteString(arg50)
		if err52 != nil {
			Usage()
			return
		}
		factory53 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt54 := factory53.GetProtocol(mbTrans51)
		argvalue2 := project_async_export.NewIncomeMyDeliveryQueryParam()
		err55 := argvalue2.Read(jsProt54)
		if err55 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ExportIncomeMyDeliveryUnconfirmed(value0, value1, value2))
		fmt.Print("\n")
		break
	case "exportIncomeMyDeliveryConfirmed":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportIncomeMyDeliveryConfirmed requires 3 args")
			flag.Usage()
		}
		arg56 := flag.Arg(1)
		mbTrans57 := thrift.NewTMemoryBufferLen(len(arg56))
		defer mbTrans57.Close()
		_, err58 := mbTrans57.WriteString(arg56)
		if err58 != nil {
			Usage()
			return
		}
		factory59 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt60 := factory59.GetProtocol(mbTrans57)
		argvalue0 := project_async_export.NewRequestHeader()
		err61 := argvalue0.Read(jsProt60)
		if err61 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg63 := flag.Arg(3)
		mbTrans64 := thrift.NewTMemoryBufferLen(len(arg63))
		defer mbTrans64.Close()
		_, err65 := mbTrans64.WriteString(arg63)
		if err65 != nil {
			Usage()
			return
		}
		factory66 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt67 := factory66.GetProtocol(mbTrans64)
		argvalue2 := project_async_export.NewIncomeMyDeliveryQueryParam()
		err68 := argvalue2.Read(jsProt67)
		if err68 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ExportIncomeMyDeliveryConfirmed(value0, value1, value2))
		fmt.Print("\n")
		break
	case "exportIncomeFinanceSettle":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportIncomeFinanceSettle requires 3 args")
			flag.Usage()
		}
		arg69 := flag.Arg(1)
		mbTrans70 := thrift.NewTMemoryBufferLen(len(arg69))
		defer mbTrans70.Close()
		_, err71 := mbTrans70.WriteString(arg69)
		if err71 != nil {
			Usage()
			return
		}
		factory72 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt73 := factory72.GetProtocol(mbTrans70)
		argvalue0 := project_async_export.NewRequestHeader()
		err74 := argvalue0.Read(jsProt73)
		if err74 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg76 := flag.Arg(3)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue2 := project_async_export.NewIncomeFinanceSettleQueryParam()
		err81 := argvalue2.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ExportIncomeFinanceSettle(value0, value1, value2))
		fmt.Print("\n")
		break
	case "exportIncomePayback":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportIncomePayback requires 3 args")
			flag.Usage()
		}
		arg82 := flag.Arg(1)
		mbTrans83 := thrift.NewTMemoryBufferLen(len(arg82))
		defer mbTrans83.Close()
		_, err84 := mbTrans83.WriteString(arg82)
		if err84 != nil {
			Usage()
			return
		}
		factory85 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt86 := factory85.GetProtocol(mbTrans83)
		argvalue0 := project_async_export.NewRequestHeader()
		err87 := argvalue0.Read(jsProt86)
		if err87 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg89 := flag.Arg(3)
		mbTrans90 := thrift.NewTMemoryBufferLen(len(arg89))
		defer mbTrans90.Close()
		_, err91 := mbTrans90.WriteString(arg89)
		if err91 != nil {
			Usage()
			return
		}
		factory92 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt93 := factory92.GetProtocol(mbTrans90)
		argvalue2 := project_async_export.NewIncomePaybackQueryParam()
		err94 := argvalue2.Read(jsProt93)
		if err94 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ExportIncomePayback(value0, value1, value2))
		fmt.Print("\n")
		break
	case "exportIncomeNonAssociatedInvoice":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportIncomeNonAssociatedInvoice requires 3 args")
			flag.Usage()
		}
		arg95 := flag.Arg(1)
		mbTrans96 := thrift.NewTMemoryBufferLen(len(arg95))
		defer mbTrans96.Close()
		_, err97 := mbTrans96.WriteString(arg95)
		if err97 != nil {
			Usage()
			return
		}
		factory98 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt99 := factory98.GetProtocol(mbTrans96)
		argvalue0 := project_async_export.NewRequestHeader()
		err100 := argvalue0.Read(jsProt99)
		if err100 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg102 := flag.Arg(3)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue2 := project_async_export.NewIncomeNonAssociatedInvoiceQueryParam()
		err107 := argvalue2.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ExportIncomeNonAssociatedInvoice(value0, value1, value2))
		fmt.Print("\n")
		break
	case "exportIncomeMonthReport":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportIncomeMonthReport requires 3 args")
			flag.Usage()
		}
		arg108 := flag.Arg(1)
		mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
		defer mbTrans109.Close()
		_, err110 := mbTrans109.WriteString(arg108)
		if err110 != nil {
			Usage()
			return
		}
		factory111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt112 := factory111.GetProtocol(mbTrans109)
		argvalue0 := project_async_export.NewRequestHeader()
		err113 := argvalue0.Read(jsProt112)
		if err113 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg115 := flag.Arg(3)
		mbTrans116 := thrift.NewTMemoryBufferLen(len(arg115))
		defer mbTrans116.Close()
		_, err117 := mbTrans116.WriteString(arg115)
		if err117 != nil {
			Usage()
			return
		}
		factory118 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt119 := factory118.GetProtocol(mbTrans116)
		argvalue2 := project_async_export.NewIncomeMonthReportQueryParam()
		err120 := argvalue2.Read(jsProt119)
		if err120 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ExportIncomeMonthReport(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
