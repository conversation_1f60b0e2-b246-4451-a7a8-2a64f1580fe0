// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_stats_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/vico_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = vico_types.GoUnusedProtection__
var GoUnusedProtection__ int

type StatsData struct {
	Dt         int64                    `thrift:"dt,1" json:"dt"`
	Hr         int64                    `thrift:"hr,2" json:"hr"`
	ProductId  int64                    `thrift:"productId,3" json:"productId"`
	PackId     int64                    `thrift:"packId,4" json:"packId"`
	OrderId    int64                    `thrift:"orderId,5" json:"orderId"`
	Channel    vico_types.AdChannelType `thrift:"channel,6" json:"channel"`
	CampaignId int64                    `thrift:"campaignId,7" json:"campaignId"`
	// unused field # 8
	// unused field # 9
	Imp     int64 `thrift:"imp,10" json:"imp"`
	Clk     int64 `thrift:"clk,11" json:"clk"`
	Consume int64 `thrift:"consume,12" json:"consume"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	ExchangeRatio int64 `thrift:"exchangeRatio,20" json:"exchangeRatio"`
}

func NewStatsData() *StatsData {
	return &StatsData{
		Channel: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *StatsData) IsSetChannel() bool {
	return int64(p.Channel) != math.MinInt32-1
}

func (p *StatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *StatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *StatsData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *StatsData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PackId = v
	}
	return nil
}

func (p *StatsData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *StatsData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Channel = vico_types.AdChannelType(v)
	}
	return nil
}

func (p *StatsData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *StatsData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Imp = v
	}
	return nil
}

func (p *StatsData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Clk = v
	}
	return nil
}

func (p *StatsData) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Consume = v
	}
	return nil
}

func (p *StatsData) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ExchangeRatio = v
	}
	return nil
}

func (p *StatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:productId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ProductId)); err != nil {
		return fmt.Errorf("%T.productId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:productId: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:packId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PackId)); err != nil {
		return fmt.Errorf("%T.packId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:packId: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:orderId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:orderId: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannel() {
		if err := oprot.WriteFieldBegin("channel", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:channel: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Channel)); err != nil {
			return fmt.Errorf("%T.channel (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:channel: %s", p, err)
		}
	}
	return err
}

func (p *StatsData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:campaignId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:campaignId: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:imp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imp)); err != nil {
		return fmt.Errorf("%T.imp (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:imp: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:clk: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clk)); err != nil {
		return fmt.Errorf("%T.clk (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:clk: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consume", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:consume: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consume)); err != nil {
		return fmt.Errorf("%T.consume (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:consume: %s", p, err)
	}
	return err
}

func (p *StatsData) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeRatio", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:exchangeRatio: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExchangeRatio)); err != nil {
		return fmt.Errorf("%T.exchangeRatio (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:exchangeRatio: %s", p, err)
	}
	return err
}

func (p *StatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsData(%+v)", *p)
}

type StatsConditionParam struct {
	StartTime   int64                      `thrift:"startTime,1" json:"startTime"`
	EndTime     int64                      `thrift:"endTime,2" json:"endTime"`
	ProductIds  []int64                    `thrift:"productIds,3" json:"productIds"`
	PackIds     []int64                    `thrift:"packIds,4" json:"packIds"`
	OrderIds    []int64                    `thrift:"orderIds,5" json:"orderIds"`
	Channels    []vico_types.AdChannelType `thrift:"channels,6" json:"channels"`
	CampaignIds []int64                    `thrift:"campaignIds,7" json:"campaignIds"`
}

func NewStatsConditionParam() *StatsConditionParam {
	return &StatsConditionParam{}
}

func (p *StatsConditionParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProductIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.ProductIds = append(p.ProductIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PackIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.PackIds = append(p.PackIds, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.OrderIds = append(p.OrderIds, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Channels = make([]vico_types.AdChannelType, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 vico_types.AdChannelType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = vico_types.AdChannelType(v)
		}
		p.Channels = append(p.Channels, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.CampaignIds = append(p.CampaignIds, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsConditionParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ProductIds != nil {
		if err := oprot.WriteFieldBegin("productIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:productIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.ProductIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProductIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:productIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.PackIds != nil {
		if err := oprot.WriteFieldBegin("packIds", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:packIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.PackIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PackIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:packIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Channels != nil {
		if err := oprot.WriteFieldBegin("channels", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:channels: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Channels)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Channels {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:channels: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.CampaignIds != nil {
		if err := oprot.WriteFieldBegin("campaignIds", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:campaignIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.CampaignIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:campaignIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsConditionParam(%+v)", *p)
}

type StatsGroupByParam struct {
	Dt         bool `thrift:"dt,1" json:"dt"`
	Hr         bool `thrift:"hr,2" json:"hr"`
	ProductId  bool `thrift:"productId,3" json:"productId"`
	PackId     bool `thrift:"packId,4" json:"packId"`
	OrderId    bool `thrift:"orderId,5" json:"orderId"`
	Channel    bool `thrift:"channel,6" json:"channel"`
	CampaignId bool `thrift:"campaignId,7" json:"campaignId"`
}

func NewStatsGroupByParam() *StatsGroupByParam {
	return &StatsGroupByParam{}
}

func (p *StatsGroupByParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *StatsGroupByParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *StatsGroupByParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PackId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *StatsGroupByParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *StatsGroupByParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsGroupByParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("productId", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:productId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ProductId)); err != nil {
		return fmt.Errorf("%T.productId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:productId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packId", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:packId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.PackId)); err != nil {
		return fmt.Errorf("%T.packId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:packId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:orderId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:orderId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:channel: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:channel: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:campaignId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:campaignId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsGroupByParam(%+v)", *p)
}

type StatsQueryResult struct {
	TotalCount int64        `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int64        `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32        `thrift:"offset,3" json:"offset"`
	Limit      int32        `thrift:"limit,4" json:"limit"`
	Result     []*StatsData `thrift:"result,5" json:"result"`
}

func NewStatsQueryResult() *StatsQueryResult {
	return &StatsQueryResult{}
}

func (p *StatsQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *StatsQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *StatsQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *StatsQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *StatsQueryResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*StatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewStatsData()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Result = append(p.Result, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *StatsQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsQueryResult(%+v)", *p)
}
