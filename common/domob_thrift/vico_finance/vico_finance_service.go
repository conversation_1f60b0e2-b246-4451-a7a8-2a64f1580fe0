// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/vico_exception"
	"rtb_model_server/common/domob_thrift/vico_finance_types"
	"rtb_model_server/common/domob_thrift/vico_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = vico_finance_types.GoUnusedProtection__
var _ = vico_types.GoUnusedProtection__
var _ = vico_exception.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type VicoFinanceService interface { //vico finance 服务接口定义

	// 查询财务账户的设置信息
	// 财务账户对应的各模块的权限,使用量等信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	GetFinanceAccountSetting(header *common.RequestHeader, accountId int64) (r map[vico_finance_types.VicoModuleId]*vico_finance_types.FinanceAccountSetting, e *vico_exception.VicoException, err error)
	// 检查账户对某一功能模块是否还有操作权限
	//  @return: true/false
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - ModuleId: 模块对应的ID *
	//  - TemplateId: 模板ID, 当moduleId=TEMPLATE时有效, 0表示普通模板 *
	CheckModuleOperatable(header *common.RequestHeader, accountId int64, moduleId vico_finance_types.VicoModuleId, templateId int64) (r bool, e *vico_exception.VicoException, err error)
	// 创建订单
	// 购买服务产品或者套装
	//  - 对于免费的产品服务或者套装的订单,处理逻辑如下
	//    - 状态直接默认置为已完成
	//    - 默认给账户添加相应订单下服务列表设置
	//  - 对于付费产品服务,处理逻辑如下
	//    - 状态置为等待付款
	//  @return: orderId
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - Info: 订单信息 *
	AddFinanceOrder(header *common.RequestHeader, accountId int64, info *vico_finance_types.FinanceOrder) (r int64, e *vico_exception.VicoException, err error)
	// 提交订单付款信息
	// 业务侧对接支付平台,callback验证成功之后,提交财务订单到已付款完成的状态
	// 处理逻辑
	//  - 订单状态置为已付款完成
	//  - 给相应的账户下发模块服务功能权限
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - OrderId: 内部订单ID *
	//  - PaymentType: 支付平台类型, 免费订单类型填unknown *
	//  - OutTradeNo: 对应的支付平台的订单ID, 免费订单类型填空 *
	SumitOrderPayment(header *common.RequestHeader, accountId int64, orderId int64, paymentType vico_finance_types.FinanceOrderPaymentType, outTradeNo string) (e *vico_exception.VicoException, err error)
	// 用户手动取消订单
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - OrderIds: 内部订单ID列表 *
	CancelOrderByIds(header *common.RequestHeader, accountId int64, orderIds []int64) (e *vico_exception.VicoException, err error)
	// 用户手动删除订单
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	//  - OrderIds: 内部订单ID列表 *
	DeleteOrderByIds(header *common.RequestHeader, accountId int64, orderIds []int64) (e *vico_exception.VicoException, err error)
	// 获取订单信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Ids: 内部订单ID列表 *
	GetOrderInfoByIds(header *common.RequestHeader, ids []int64) (r map[int64]*vico_finance_types.FinanceOrder, e *vico_exception.VicoException, err error)
	// 查询订单列表
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Params: 查询参数 *
	//  - Offset: 查询偏移量,默认从0开始查询 *
	//  - Limit: 查询数量,默认20 *
	QueryOrderByParams(header *common.RequestHeader, params *vico_finance_types.FinanceOrderQueryParams, offset int32, limit int32) (r *vico_types.QueryResult, e *vico_exception.VicoException, err error)
	// 获取服务产品信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Ids: 模块服务产品定义的ID列表 *
	GetFinanceServiceProductsByIds(header *common.RequestHeader, ids []int64) (r map[int64]*vico_finance_types.FinanceServiceProduct, e *vico_exception.VicoException, err error)
	// 搜索服务产品列表
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Params: 查询参数 *
	//  - Offset: 查询偏移量,默认从0开始查询 *
	//  - Limit: 查询数量,默认20 *
	QueryFinanceServiceProductByParams(header *common.RequestHeader, params *vico_finance_types.FinanceServiceProductParams, offset int32, limit int32) (r *vico_types.QueryResult, e *vico_exception.VicoException, err error)
	// 添加模块服务产品信息
	//  @return: 模块服务产品ID
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Info: 模块服务产品结构体 *
	AddFinanceServiceProduct(header *common.RequestHeader, info *vico_finance_types.FinanceServiceProduct) (r int64, e *vico_exception.VicoException, err error)
	// 编辑模块服务产品信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Info: 模块服务产品结构体 *
	EditFinanceServiceProduct(header *common.RequestHeader, info *vico_finance_types.FinanceServiceProduct) (e *vico_exception.VicoException, err error)
	// 添加模块服务产品信息
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - Ids: 模块服务产品id列表 *
	DeleteFinanceServiceProductByIds(header *common.RequestHeader, ids []int64) (e *vico_exception.VicoException, err error)
	// 判断用户是否拥有购买加油包的权限
	//
	// Parameters:
	//  - Header: 请求头部信息 *
	//  - AccountId: 账户ID *
	CheckUserServiceProductAvailable(header *common.RequestHeader, accountId int64) (r bool, e *vico_exception.VicoException, err error)
}

//vico finance 服务接口定义
type VicoFinanceServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewVicoFinanceServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *VicoFinanceServiceClient {
	return &VicoFinanceServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewVicoFinanceServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *VicoFinanceServiceClient {
	return &VicoFinanceServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 查询财务账户的设置信息
// 财务账户对应的各模块的权限,使用量等信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
func (p *VicoFinanceServiceClient) GetFinanceAccountSetting(header *common.RequestHeader, accountId int64) (r map[vico_finance_types.VicoModuleId]*vico_finance_types.FinanceAccountSetting, e *vico_exception.VicoException, err error) {
	if err = p.sendGetFinanceAccountSetting(header, accountId); err != nil {
		return
	}
	return p.recvGetFinanceAccountSetting()
}

func (p *VicoFinanceServiceClient) sendGetFinanceAccountSetting(header *common.RequestHeader, accountId int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFinanceAccountSetting", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetFinanceAccountSettingArgs()
	args0.Header = header
	args0.AccountId = accountId
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvGetFinanceAccountSetting() (value map[vico_finance_types.VicoModuleId]*vico_finance_types.FinanceAccountSetting, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetFinanceAccountSettingResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// 检查账户对某一功能模块是否还有操作权限
//  @return: true/false
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - ModuleId: 模块对应的ID *
//  - TemplateId: 模板ID, 当moduleId=TEMPLATE时有效, 0表示普通模板 *
func (p *VicoFinanceServiceClient) CheckModuleOperatable(header *common.RequestHeader, accountId int64, moduleId vico_finance_types.VicoModuleId, templateId int64) (r bool, e *vico_exception.VicoException, err error) {
	if err = p.sendCheckModuleOperatable(header, accountId, moduleId, templateId); err != nil {
		return
	}
	return p.recvCheckModuleOperatable()
}

func (p *VicoFinanceServiceClient) sendCheckModuleOperatable(header *common.RequestHeader, accountId int64, moduleId vico_finance_types.VicoModuleId, templateId int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkModuleOperatable", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewCheckModuleOperatableArgs()
	args4.Header = header
	args4.AccountId = accountId
	args4.ModuleId = moduleId
	args4.TemplateId = templateId
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvCheckModuleOperatable() (value bool, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewCheckModuleOperatableResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

// 创建订单
// 购买服务产品或者套装
//  - 对于免费的产品服务或者套装的订单,处理逻辑如下
//    - 状态直接默认置为已完成
//    - 默认给账户添加相应订单下服务列表设置
//  - 对于付费产品服务,处理逻辑如下
//    - 状态置为等待付款
//  @return: orderId
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - Info: 订单信息 *
func (p *VicoFinanceServiceClient) AddFinanceOrder(header *common.RequestHeader, accountId int64, info *vico_finance_types.FinanceOrder) (r int64, e *vico_exception.VicoException, err error) {
	if err = p.sendAddFinanceOrder(header, accountId, info); err != nil {
		return
	}
	return p.recvAddFinanceOrder()
}

func (p *VicoFinanceServiceClient) sendAddFinanceOrder(header *common.RequestHeader, accountId int64, info *vico_finance_types.FinanceOrder) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addFinanceOrder", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewAddFinanceOrderArgs()
	args8.Header = header
	args8.AccountId = accountId
	args8.Info = info
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvAddFinanceOrder() (value int64, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewAddFinanceOrderResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

// 提交订单付款信息
// 业务侧对接支付平台,callback验证成功之后,提交财务订单到已付款完成的状态
// 处理逻辑
//  - 订单状态置为已付款完成
//  - 给相应的账户下发模块服务功能权限
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - OrderId: 内部订单ID *
//  - PaymentType: 支付平台类型, 免费订单类型填unknown *
//  - OutTradeNo: 对应的支付平台的订单ID, 免费订单类型填空 *
func (p *VicoFinanceServiceClient) SumitOrderPayment(header *common.RequestHeader, accountId int64, orderId int64, paymentType vico_finance_types.FinanceOrderPaymentType, outTradeNo string) (e *vico_exception.VicoException, err error) {
	if err = p.sendSumitOrderPayment(header, accountId, orderId, paymentType, outTradeNo); err != nil {
		return
	}
	return p.recvSumitOrderPayment()
}

func (p *VicoFinanceServiceClient) sendSumitOrderPayment(header *common.RequestHeader, accountId int64, orderId int64, paymentType vico_finance_types.FinanceOrderPaymentType, outTradeNo string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sumitOrderPayment", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewSumitOrderPaymentArgs()
	args12.Header = header
	args12.AccountId = accountId
	args12.OrderId = orderId
	args12.PaymentType = paymentType
	args12.OutTradeNo = outTradeNo
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvSumitOrderPayment() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewSumitOrderPaymentResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result13.E != nil {
		e = result13.E
	}
	return
}

// 用户手动取消订单
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - OrderIds: 内部订单ID列表 *
func (p *VicoFinanceServiceClient) CancelOrderByIds(header *common.RequestHeader, accountId int64, orderIds []int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendCancelOrderByIds(header, accountId, orderIds); err != nil {
		return
	}
	return p.recvCancelOrderByIds()
}

func (p *VicoFinanceServiceClient) sendCancelOrderByIds(header *common.RequestHeader, accountId int64, orderIds []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("cancelOrderByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewCancelOrderByIdsArgs()
	args16.Header = header
	args16.AccountId = accountId
	args16.OrderIds = orderIds
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvCancelOrderByIds() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewCancelOrderByIdsResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result17.E != nil {
		e = result17.E
	}
	return
}

// 用户手动删除订单
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
//  - OrderIds: 内部订单ID列表 *
func (p *VicoFinanceServiceClient) DeleteOrderByIds(header *common.RequestHeader, accountId int64, orderIds []int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendDeleteOrderByIds(header, accountId, orderIds); err != nil {
		return
	}
	return p.recvDeleteOrderByIds()
}

func (p *VicoFinanceServiceClient) sendDeleteOrderByIds(header *common.RequestHeader, accountId int64, orderIds []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteOrderByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewDeleteOrderByIdsArgs()
	args20.Header = header
	args20.AccountId = accountId
	args20.OrderIds = orderIds
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvDeleteOrderByIds() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewDeleteOrderByIdsResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result21.E != nil {
		e = result21.E
	}
	return
}

// 获取订单信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Ids: 内部订单ID列表 *
func (p *VicoFinanceServiceClient) GetOrderInfoByIds(header *common.RequestHeader, ids []int64) (r map[int64]*vico_finance_types.FinanceOrder, e *vico_exception.VicoException, err error) {
	if err = p.sendGetOrderInfoByIds(header, ids); err != nil {
		return
	}
	return p.recvGetOrderInfoByIds()
}

func (p *VicoFinanceServiceClient) sendGetOrderInfoByIds(header *common.RequestHeader, ids []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getOrderInfoByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetOrderInfoByIdsArgs()
	args24.Header = header
	args24.Ids = ids
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvGetOrderInfoByIds() (value map[int64]*vico_finance_types.FinanceOrder, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetOrderInfoByIdsResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.E != nil {
		e = result25.E
	}
	return
}

// 查询订单列表
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Params: 查询参数 *
//  - Offset: 查询偏移量,默认从0开始查询 *
//  - Limit: 查询数量,默认20 *
func (p *VicoFinanceServiceClient) QueryOrderByParams(header *common.RequestHeader, params *vico_finance_types.FinanceOrderQueryParams, offset int32, limit int32) (r *vico_types.QueryResult, e *vico_exception.VicoException, err error) {
	if err = p.sendQueryOrderByParams(header, params, offset, limit); err != nil {
		return
	}
	return p.recvQueryOrderByParams()
}

func (p *VicoFinanceServiceClient) sendQueryOrderByParams(header *common.RequestHeader, params *vico_finance_types.FinanceOrderQueryParams, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryOrderByParams", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewQueryOrderByParamsArgs()
	args28.Header = header
	args28.Params = params
	args28.Offset = offset
	args28.Limit = limit
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvQueryOrderByParams() (value *vico_types.QueryResult, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewQueryOrderByParamsResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.E != nil {
		e = result29.E
	}
	return
}

// 获取服务产品信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Ids: 模块服务产品定义的ID列表 *
func (p *VicoFinanceServiceClient) GetFinanceServiceProductsByIds(header *common.RequestHeader, ids []int64) (r map[int64]*vico_finance_types.FinanceServiceProduct, e *vico_exception.VicoException, err error) {
	if err = p.sendGetFinanceServiceProductsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetFinanceServiceProductsByIds()
}

func (p *VicoFinanceServiceClient) sendGetFinanceServiceProductsByIds(header *common.RequestHeader, ids []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFinanceServiceProductsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetFinanceServiceProductsByIdsArgs()
	args32.Header = header
	args32.Ids = ids
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvGetFinanceServiceProductsByIds() (value map[int64]*vico_finance_types.FinanceServiceProduct, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetFinanceServiceProductsByIdsResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.E != nil {
		e = result33.E
	}
	return
}

// 搜索服务产品列表
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Params: 查询参数 *
//  - Offset: 查询偏移量,默认从0开始查询 *
//  - Limit: 查询数量,默认20 *
func (p *VicoFinanceServiceClient) QueryFinanceServiceProductByParams(header *common.RequestHeader, params *vico_finance_types.FinanceServiceProductParams, offset int32, limit int32) (r *vico_types.QueryResult, e *vico_exception.VicoException, err error) {
	if err = p.sendQueryFinanceServiceProductByParams(header, params, offset, limit); err != nil {
		return
	}
	return p.recvQueryFinanceServiceProductByParams()
}

func (p *VicoFinanceServiceClient) sendQueryFinanceServiceProductByParams(header *common.RequestHeader, params *vico_finance_types.FinanceServiceProductParams, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryFinanceServiceProductByParams", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewQueryFinanceServiceProductByParamsArgs()
	args36.Header = header
	args36.Params = params
	args36.Offset = offset
	args36.Limit = limit
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvQueryFinanceServiceProductByParams() (value *vico_types.QueryResult, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewQueryFinanceServiceProductByParamsResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.E != nil {
		e = result37.E
	}
	return
}

// 添加模块服务产品信息
//  @return: 模块服务产品ID
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Info: 模块服务产品结构体 *
func (p *VicoFinanceServiceClient) AddFinanceServiceProduct(header *common.RequestHeader, info *vico_finance_types.FinanceServiceProduct) (r int64, e *vico_exception.VicoException, err error) {
	if err = p.sendAddFinanceServiceProduct(header, info); err != nil {
		return
	}
	return p.recvAddFinanceServiceProduct()
}

func (p *VicoFinanceServiceClient) sendAddFinanceServiceProduct(header *common.RequestHeader, info *vico_finance_types.FinanceServiceProduct) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addFinanceServiceProduct", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewAddFinanceServiceProductArgs()
	args40.Header = header
	args40.Info = info
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvAddFinanceServiceProduct() (value int64, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewAddFinanceServiceProductResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.E != nil {
		e = result41.E
	}
	return
}

// 编辑模块服务产品信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Info: 模块服务产品结构体 *
func (p *VicoFinanceServiceClient) EditFinanceServiceProduct(header *common.RequestHeader, info *vico_finance_types.FinanceServiceProduct) (e *vico_exception.VicoException, err error) {
	if err = p.sendEditFinanceServiceProduct(header, info); err != nil {
		return
	}
	return p.recvEditFinanceServiceProduct()
}

func (p *VicoFinanceServiceClient) sendEditFinanceServiceProduct(header *common.RequestHeader, info *vico_finance_types.FinanceServiceProduct) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editFinanceServiceProduct", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewEditFinanceServiceProductArgs()
	args44.Header = header
	args44.Info = info
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvEditFinanceServiceProduct() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewEditFinanceServiceProductResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result45.E != nil {
		e = result45.E
	}
	return
}

// 添加模块服务产品信息
//
// Parameters:
//  - Header: 请求头部信息 *
//  - Ids: 模块服务产品id列表 *
func (p *VicoFinanceServiceClient) DeleteFinanceServiceProductByIds(header *common.RequestHeader, ids []int64) (e *vico_exception.VicoException, err error) {
	if err = p.sendDeleteFinanceServiceProductByIds(header, ids); err != nil {
		return
	}
	return p.recvDeleteFinanceServiceProductByIds()
}

func (p *VicoFinanceServiceClient) sendDeleteFinanceServiceProductByIds(header *common.RequestHeader, ids []int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteFinanceServiceProductByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewDeleteFinanceServiceProductByIdsArgs()
	args48.Header = header
	args48.Ids = ids
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvDeleteFinanceServiceProductByIds() (e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewDeleteFinanceServiceProductByIdsResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result49.E != nil {
		e = result49.E
	}
	return
}

// 判断用户是否拥有购买加油包的权限
//
// Parameters:
//  - Header: 请求头部信息 *
//  - AccountId: 账户ID *
func (p *VicoFinanceServiceClient) CheckUserServiceProductAvailable(header *common.RequestHeader, accountId int64) (r bool, e *vico_exception.VicoException, err error) {
	if err = p.sendCheckUserServiceProductAvailable(header, accountId); err != nil {
		return
	}
	return p.recvCheckUserServiceProductAvailable()
}

func (p *VicoFinanceServiceClient) sendCheckUserServiceProductAvailable(header *common.RequestHeader, accountId int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkUserServiceProductAvailable", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewCheckUserServiceProductAvailableArgs()
	args52.Header = header
	args52.AccountId = accountId
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VicoFinanceServiceClient) recvCheckUserServiceProductAvailable() (value bool, e *vico_exception.VicoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewCheckUserServiceProductAvailableResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.E != nil {
		e = result53.E
	}
	return
}

type VicoFinanceServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      VicoFinanceService
}

func (p *VicoFinanceServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *VicoFinanceServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *VicoFinanceServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewVicoFinanceServiceProcessor(handler VicoFinanceService) *VicoFinanceServiceProcessor {

	self56 := &VicoFinanceServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self56.processorMap["getFinanceAccountSetting"] = &vicoFinanceServiceProcessorGetFinanceAccountSetting{handler: handler}
	self56.processorMap["checkModuleOperatable"] = &vicoFinanceServiceProcessorCheckModuleOperatable{handler: handler}
	self56.processorMap["addFinanceOrder"] = &vicoFinanceServiceProcessorAddFinanceOrder{handler: handler}
	self56.processorMap["sumitOrderPayment"] = &vicoFinanceServiceProcessorSumitOrderPayment{handler: handler}
	self56.processorMap["cancelOrderByIds"] = &vicoFinanceServiceProcessorCancelOrderByIds{handler: handler}
	self56.processorMap["deleteOrderByIds"] = &vicoFinanceServiceProcessorDeleteOrderByIds{handler: handler}
	self56.processorMap["getOrderInfoByIds"] = &vicoFinanceServiceProcessorGetOrderInfoByIds{handler: handler}
	self56.processorMap["queryOrderByParams"] = &vicoFinanceServiceProcessorQueryOrderByParams{handler: handler}
	self56.processorMap["getFinanceServiceProductsByIds"] = &vicoFinanceServiceProcessorGetFinanceServiceProductsByIds{handler: handler}
	self56.processorMap["queryFinanceServiceProductByParams"] = &vicoFinanceServiceProcessorQueryFinanceServiceProductByParams{handler: handler}
	self56.processorMap["addFinanceServiceProduct"] = &vicoFinanceServiceProcessorAddFinanceServiceProduct{handler: handler}
	self56.processorMap["editFinanceServiceProduct"] = &vicoFinanceServiceProcessorEditFinanceServiceProduct{handler: handler}
	self56.processorMap["deleteFinanceServiceProductByIds"] = &vicoFinanceServiceProcessorDeleteFinanceServiceProductByIds{handler: handler}
	self56.processorMap["checkUserServiceProductAvailable"] = &vicoFinanceServiceProcessorCheckUserServiceProductAvailable{handler: handler}
	return self56
}

func (p *VicoFinanceServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x57 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x57.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x57

}

type vicoFinanceServiceProcessorGetFinanceAccountSetting struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorGetFinanceAccountSetting) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFinanceAccountSettingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFinanceAccountSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFinanceAccountSettingResult()
	if result.Success, result.E, err = p.handler.GetFinanceAccountSetting(args.Header, args.AccountId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFinanceAccountSetting: "+err.Error())
		oprot.WriteMessageBegin("getFinanceAccountSetting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFinanceAccountSetting", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorCheckModuleOperatable struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorCheckModuleOperatable) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckModuleOperatableArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkModuleOperatable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckModuleOperatableResult()
	if result.Success, result.E, err = p.handler.CheckModuleOperatable(args.Header, args.AccountId, args.ModuleId, args.TemplateId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkModuleOperatable: "+err.Error())
		oprot.WriteMessageBegin("checkModuleOperatable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkModuleOperatable", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorAddFinanceOrder struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorAddFinanceOrder) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddFinanceOrderArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addFinanceOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddFinanceOrderResult()
	if result.Success, result.E, err = p.handler.AddFinanceOrder(args.Header, args.AccountId, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addFinanceOrder: "+err.Error())
		oprot.WriteMessageBegin("addFinanceOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addFinanceOrder", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorSumitOrderPayment struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorSumitOrderPayment) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSumitOrderPaymentArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sumitOrderPayment", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSumitOrderPaymentResult()
	if result.E, err = p.handler.SumitOrderPayment(args.Header, args.AccountId, args.OrderId, args.PaymentType, args.OutTradeNo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sumitOrderPayment: "+err.Error())
		oprot.WriteMessageBegin("sumitOrderPayment", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sumitOrderPayment", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorCancelOrderByIds struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorCancelOrderByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCancelOrderByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("cancelOrderByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCancelOrderByIdsResult()
	if result.E, err = p.handler.CancelOrderByIds(args.Header, args.AccountId, args.OrderIds); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing cancelOrderByIds: "+err.Error())
		oprot.WriteMessageBegin("cancelOrderByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("cancelOrderByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorDeleteOrderByIds struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorDeleteOrderByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteOrderByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteOrderByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteOrderByIdsResult()
	if result.E, err = p.handler.DeleteOrderByIds(args.Header, args.AccountId, args.OrderIds); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteOrderByIds: "+err.Error())
		oprot.WriteMessageBegin("deleteOrderByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteOrderByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorGetOrderInfoByIds struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorGetOrderInfoByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetOrderInfoByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getOrderInfoByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetOrderInfoByIdsResult()
	if result.Success, result.E, err = p.handler.GetOrderInfoByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getOrderInfoByIds: "+err.Error())
		oprot.WriteMessageBegin("getOrderInfoByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getOrderInfoByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorQueryOrderByParams struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorQueryOrderByParams) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryOrderByParamsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryOrderByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryOrderByParamsResult()
	if result.Success, result.E, err = p.handler.QueryOrderByParams(args.Header, args.Params, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryOrderByParams: "+err.Error())
		oprot.WriteMessageBegin("queryOrderByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryOrderByParams", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorGetFinanceServiceProductsByIds struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorGetFinanceServiceProductsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFinanceServiceProductsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFinanceServiceProductsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFinanceServiceProductsByIdsResult()
	if result.Success, result.E, err = p.handler.GetFinanceServiceProductsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFinanceServiceProductsByIds: "+err.Error())
		oprot.WriteMessageBegin("getFinanceServiceProductsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFinanceServiceProductsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorQueryFinanceServiceProductByParams struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorQueryFinanceServiceProductByParams) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryFinanceServiceProductByParamsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryFinanceServiceProductByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryFinanceServiceProductByParamsResult()
	if result.Success, result.E, err = p.handler.QueryFinanceServiceProductByParams(args.Header, args.Params, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryFinanceServiceProductByParams: "+err.Error())
		oprot.WriteMessageBegin("queryFinanceServiceProductByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryFinanceServiceProductByParams", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorAddFinanceServiceProduct struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorAddFinanceServiceProduct) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddFinanceServiceProductArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addFinanceServiceProduct", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddFinanceServiceProductResult()
	if result.Success, result.E, err = p.handler.AddFinanceServiceProduct(args.Header, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addFinanceServiceProduct: "+err.Error())
		oprot.WriteMessageBegin("addFinanceServiceProduct", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addFinanceServiceProduct", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorEditFinanceServiceProduct struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorEditFinanceServiceProduct) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditFinanceServiceProductArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editFinanceServiceProduct", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditFinanceServiceProductResult()
	if result.E, err = p.handler.EditFinanceServiceProduct(args.Header, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editFinanceServiceProduct: "+err.Error())
		oprot.WriteMessageBegin("editFinanceServiceProduct", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editFinanceServiceProduct", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorDeleteFinanceServiceProductByIds struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorDeleteFinanceServiceProductByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteFinanceServiceProductByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteFinanceServiceProductByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteFinanceServiceProductByIdsResult()
	if result.E, err = p.handler.DeleteFinanceServiceProductByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteFinanceServiceProductByIds: "+err.Error())
		oprot.WriteMessageBegin("deleteFinanceServiceProductByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteFinanceServiceProductByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type vicoFinanceServiceProcessorCheckUserServiceProductAvailable struct {
	handler VicoFinanceService
}

func (p *vicoFinanceServiceProcessorCheckUserServiceProductAvailable) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckUserServiceProductAvailableArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkUserServiceProductAvailable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckUserServiceProductAvailableResult()
	if result.Success, result.E, err = p.handler.CheckUserServiceProductAvailable(args.Header, args.AccountId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkUserServiceProductAvailable: "+err.Error())
		oprot.WriteMessageBegin("checkUserServiceProductAvailable", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkUserServiceProductAvailable", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetFinanceAccountSettingArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
}

func NewGetFinanceAccountSettingArgs() *GetFinanceAccountSettingArgs {
	return &GetFinanceAccountSettingArgs{}
}

func (p *GetFinanceAccountSettingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceAccountSettingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFinanceAccountSettingArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *GetFinanceAccountSettingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceAccountSetting_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceAccountSettingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceAccountSettingArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *GetFinanceAccountSettingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceAccountSettingArgs(%+v)", *p)
}

type GetFinanceAccountSettingResult struct {
	Success map[vico_finance_types.VicoModuleId]*vico_finance_types.FinanceAccountSetting `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException                                                 `thrift:"e,1" json:"e"`
}

func NewGetFinanceAccountSettingResult() *GetFinanceAccountSettingResult {
	return &GetFinanceAccountSettingResult{}
}

func (p *GetFinanceAccountSettingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceAccountSettingResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[vico_finance_types.VicoModuleId]*vico_finance_types.FinanceAccountSetting, size)
	for i := 0; i < size; i++ {
		var _key58 vico_finance_types.VicoModuleId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key58 = vico_finance_types.VicoModuleId(v)
		}
		_val59 := vico_finance_types.NewFinanceAccountSetting()
		if err := _val59.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val59)
		}
		p.Success[_key58] = _val59
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetFinanceAccountSettingResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetFinanceAccountSettingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceAccountSetting_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceAccountSettingResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceAccountSettingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceAccountSettingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceAccountSettingResult(%+v)", *p)
}

type CheckModuleOperatableArgs struct {
	Header     *common.RequestHeader           `thrift:"header,1" json:"header"`
	AccountId  int64                           `thrift:"accountId,2" json:"accountId"`
	ModuleId   vico_finance_types.VicoModuleId `thrift:"moduleId,3" json:"moduleId"`
	TemplateId int64                           `thrift:"templateId,4" json:"templateId"`
}

func NewCheckModuleOperatableArgs() *CheckModuleOperatableArgs {
	return &CheckModuleOperatableArgs{
		ModuleId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CheckModuleOperatableArgs) IsSetModuleId() bool {
	return int64(p.ModuleId) != math.MinInt32-1
}

func (p *CheckModuleOperatableArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckModuleOperatableArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CheckModuleOperatableArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *CheckModuleOperatableArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ModuleId = vico_finance_types.VicoModuleId(v)
	}
	return nil
}

func (p *CheckModuleOperatableArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *CheckModuleOperatableArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkModuleOperatable_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckModuleOperatableArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CheckModuleOperatableArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *CheckModuleOperatableArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetModuleId() {
		if err := oprot.WriteFieldBegin("moduleId", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:moduleId: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ModuleId)); err != nil {
			return fmt.Errorf("%T.moduleId (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:moduleId: %s", p, err)
		}
	}
	return err
}

func (p *CheckModuleOperatableArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("templateId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:templateId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.templateId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:templateId: %s", p, err)
	}
	return err
}

func (p *CheckModuleOperatableArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckModuleOperatableArgs(%+v)", *p)
}

type CheckModuleOperatableResult struct {
	Success bool                          `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewCheckModuleOperatableResult() *CheckModuleOperatableResult {
	return &CheckModuleOperatableResult{}
}

func (p *CheckModuleOperatableResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckModuleOperatableResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *CheckModuleOperatableResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *CheckModuleOperatableResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkModuleOperatable_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckModuleOperatableResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *CheckModuleOperatableResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *CheckModuleOperatableResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckModuleOperatableResult(%+v)", *p)
}

type AddFinanceOrderArgs struct {
	Header    *common.RequestHeader            `thrift:"header,1" json:"header"`
	AccountId int64                            `thrift:"accountId,2" json:"accountId"`
	Info      *vico_finance_types.FinanceOrder `thrift:"info,3" json:"info"`
}

func NewAddFinanceOrderArgs() *AddFinanceOrderArgs {
	return &AddFinanceOrderArgs{}
}

func (p *AddFinanceOrderArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceOrderArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddFinanceOrderArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *AddFinanceOrderArgs) readField3(iprot thrift.TProtocol) error {
	p.Info = vico_finance_types.NewFinanceOrder()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *AddFinanceOrderArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFinanceOrder_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceOrderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddFinanceOrderArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *AddFinanceOrderArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:info: %s", p, err)
		}
	}
	return err
}

func (p *AddFinanceOrderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFinanceOrderArgs(%+v)", *p)
}

type AddFinanceOrderResult struct {
	Success int64                         `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewAddFinanceOrderResult() *AddFinanceOrderResult {
	return &AddFinanceOrderResult{}
}

func (p *AddFinanceOrderResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceOrderResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddFinanceOrderResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddFinanceOrderResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFinanceOrder_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceOrderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddFinanceOrderResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddFinanceOrderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFinanceOrderResult(%+v)", *p)
}

type SumitOrderPaymentArgs struct {
	Header      *common.RequestHeader                      `thrift:"header,1" json:"header"`
	AccountId   int64                                      `thrift:"accountId,2" json:"accountId"`
	OrderId     int64                                      `thrift:"orderId,3" json:"orderId"`
	PaymentType vico_finance_types.FinanceOrderPaymentType `thrift:"paymentType,4" json:"paymentType"`
	OutTradeNo  string                                     `thrift:"outTradeNo,5" json:"outTradeNo"`
}

func NewSumitOrderPaymentArgs() *SumitOrderPaymentArgs {
	return &SumitOrderPaymentArgs{
		PaymentType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SumitOrderPaymentArgs) IsSetPaymentType() bool {
	return int64(p.PaymentType) != math.MinInt32-1
}

func (p *SumitOrderPaymentArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SumitOrderPaymentArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SumitOrderPaymentArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *SumitOrderPaymentArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *SumitOrderPaymentArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PaymentType = vico_finance_types.FinanceOrderPaymentType(v)
	}
	return nil
}

func (p *SumitOrderPaymentArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.OutTradeNo = v
	}
	return nil
}

func (p *SumitOrderPaymentArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sumitOrderPayment_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SumitOrderPaymentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SumitOrderPaymentArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *SumitOrderPaymentArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:orderId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:orderId: %s", p, err)
	}
	return err
}

func (p *SumitOrderPaymentArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaymentType() {
		if err := oprot.WriteFieldBegin("paymentType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:paymentType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PaymentType)); err != nil {
			return fmt.Errorf("%T.paymentType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:paymentType: %s", p, err)
		}
	}
	return err
}

func (p *SumitOrderPaymentArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("outTradeNo", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:outTradeNo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OutTradeNo)); err != nil {
		return fmt.Errorf("%T.outTradeNo (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:outTradeNo: %s", p, err)
	}
	return err
}

func (p *SumitOrderPaymentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SumitOrderPaymentArgs(%+v)", *p)
}

type SumitOrderPaymentResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewSumitOrderPaymentResult() *SumitOrderPaymentResult {
	return &SumitOrderPaymentResult{}
}

func (p *SumitOrderPaymentResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SumitOrderPaymentResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SumitOrderPaymentResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sumitOrderPayment_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SumitOrderPaymentResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SumitOrderPaymentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SumitOrderPaymentResult(%+v)", *p)
}

type CancelOrderByIdsArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
	OrderIds  []int64               `thrift:"orderIds,3" json:"orderIds"`
}

func NewCancelOrderByIdsArgs() *CancelOrderByIdsArgs {
	return &CancelOrderByIdsArgs{}
}

func (p *CancelOrderByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CancelOrderByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CancelOrderByIdsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *CancelOrderByIdsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem60 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem60 = v
		}
		p.OrderIds = append(p.OrderIds, _elem60)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CancelOrderByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("cancelOrderByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CancelOrderByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CancelOrderByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *CancelOrderByIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *CancelOrderByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CancelOrderByIdsArgs(%+v)", *p)
}

type CancelOrderByIdsResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewCancelOrderByIdsResult() *CancelOrderByIdsResult {
	return &CancelOrderByIdsResult{}
}

func (p *CancelOrderByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CancelOrderByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *CancelOrderByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("cancelOrderByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CancelOrderByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *CancelOrderByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CancelOrderByIdsResult(%+v)", *p)
}

type DeleteOrderByIdsArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
	OrderIds  []int64               `thrift:"orderIds,3" json:"orderIds"`
}

func NewDeleteOrderByIdsArgs() *DeleteOrderByIdsArgs {
	return &DeleteOrderByIdsArgs{}
}

func (p *DeleteOrderByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteOrderByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeleteOrderByIdsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *DeleteOrderByIdsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OrderIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem61 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem61 = v
		}
		p.OrderIds = append(p.OrderIds, _elem61)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeleteOrderByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteOrderByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteOrderByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeleteOrderByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *DeleteOrderByIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.OrderIds != nil {
		if err := oprot.WriteFieldBegin("orderIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:orderIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.OrderIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OrderIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:orderIds: %s", p, err)
		}
	}
	return err
}

func (p *DeleteOrderByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteOrderByIdsArgs(%+v)", *p)
}

type DeleteOrderByIdsResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewDeleteOrderByIdsResult() *DeleteOrderByIdsResult {
	return &DeleteOrderByIdsResult{}
}

func (p *DeleteOrderByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteOrderByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *DeleteOrderByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteOrderByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteOrderByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *DeleteOrderByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteOrderByIdsResult(%+v)", *p)
}

type GetOrderInfoByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int64               `thrift:"ids,2" json:"ids"`
}

func NewGetOrderInfoByIdsArgs() *GetOrderInfoByIdsArgs {
	return &GetOrderInfoByIdsArgs{}
}

func (p *GetOrderInfoByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOrderInfoByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetOrderInfoByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem62 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem62 = v
		}
		p.Ids = append(p.Ids, _elem62)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetOrderInfoByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOrderInfoByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOrderInfoByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetOrderInfoByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetOrderInfoByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOrderInfoByIdsArgs(%+v)", *p)
}

type GetOrderInfoByIdsResult struct {
	Success map[int64]*vico_finance_types.FinanceOrder `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException              `thrift:"e,1" json:"e"`
}

func NewGetOrderInfoByIdsResult() *GetOrderInfoByIdsResult {
	return &GetOrderInfoByIdsResult{}
}

func (p *GetOrderInfoByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOrderInfoByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int64]*vico_finance_types.FinanceOrder, size)
	for i := 0; i < size; i++ {
		var _key63 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key63 = v
		}
		_val64 := vico_finance_types.NewFinanceOrder()
		if err := _val64.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val64)
		}
		p.Success[_key63] = _val64
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetOrderInfoByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetOrderInfoByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOrderInfoByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOrderInfoByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI64(int64(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetOrderInfoByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetOrderInfoByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOrderInfoByIdsResult(%+v)", *p)
}

type QueryOrderByParamsArgs struct {
	Header *common.RequestHeader                       `thrift:"header,1" json:"header"`
	Params *vico_finance_types.FinanceOrderQueryParams `thrift:"params,2" json:"params"`
	Offset int32                                       `thrift:"offset,3" json:"offset"`
	Limit  int32                                       `thrift:"limit,4" json:"limit"`
}

func NewQueryOrderByParamsArgs() *QueryOrderByParamsArgs {
	return &QueryOrderByParamsArgs{}
}

func (p *QueryOrderByParamsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryOrderByParamsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryOrderByParamsArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = vico_finance_types.NewFinanceOrderQueryParams()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *QueryOrderByParamsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryOrderByParamsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryOrderByParamsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryOrderByParams_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryOrderByParamsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryOrderByParamsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *QueryOrderByParamsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *QueryOrderByParamsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *QueryOrderByParamsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryOrderByParamsArgs(%+v)", *p)
}

type QueryOrderByParamsResult struct {
	Success *vico_types.QueryResult       `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewQueryOrderByParamsResult() *QueryOrderByParamsResult {
	return &QueryOrderByParamsResult{}
}

func (p *QueryOrderByParamsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryOrderByParamsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = vico_types.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryOrderByParamsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryOrderByParamsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryOrderByParams_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryOrderByParamsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryOrderByParamsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryOrderByParamsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryOrderByParamsResult(%+v)", *p)
}

type GetFinanceServiceProductsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int64               `thrift:"ids,2" json:"ids"`
}

func NewGetFinanceServiceProductsByIdsArgs() *GetFinanceServiceProductsByIdsArgs {
	return &GetFinanceServiceProductsByIdsArgs{}
}

func (p *GetFinanceServiceProductsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem65 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem65 = v
		}
		p.Ids = append(p.Ids, _elem65)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceServiceProductsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceServiceProductsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceServiceProductsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceServiceProductsByIdsArgs(%+v)", *p)
}

type GetFinanceServiceProductsByIdsResult struct {
	Success map[int64]*vico_finance_types.FinanceServiceProduct `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException                       `thrift:"e,1" json:"e"`
}

func NewGetFinanceServiceProductsByIdsResult() *GetFinanceServiceProductsByIdsResult {
	return &GetFinanceServiceProductsByIdsResult{}
}

func (p *GetFinanceServiceProductsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int64]*vico_finance_types.FinanceServiceProduct, size)
	for i := 0; i < size; i++ {
		var _key66 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key66 = v
		}
		_val67 := vico_finance_types.NewFinanceServiceProduct()
		if err := _val67.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val67)
		}
		p.Success[_key66] = _val67
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinanceServiceProductsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinanceServiceProductsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI64(int64(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceServiceProductsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetFinanceServiceProductsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinanceServiceProductsByIdsResult(%+v)", *p)
}

type QueryFinanceServiceProductByParamsArgs struct {
	Header *common.RequestHeader                           `thrift:"header,1" json:"header"`
	Params *vico_finance_types.FinanceServiceProductParams `thrift:"params,2" json:"params"`
	Offset int32                                           `thrift:"offset,3" json:"offset"`
	Limit  int32                                           `thrift:"limit,4" json:"limit"`
}

func NewQueryFinanceServiceProductByParamsArgs() *QueryFinanceServiceProductByParamsArgs {
	return &QueryFinanceServiceProductByParamsArgs{}
}

func (p *QueryFinanceServiceProductByParamsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = vico_finance_types.NewFinanceServiceProductParams()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryFinanceServiceProductByParams_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryFinanceServiceProductByParamsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *QueryFinanceServiceProductByParamsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *QueryFinanceServiceProductByParamsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *QueryFinanceServiceProductByParamsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFinanceServiceProductByParamsArgs(%+v)", *p)
}

type QueryFinanceServiceProductByParamsResult struct {
	Success *vico_types.QueryResult       `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewQueryFinanceServiceProductByParamsResult() *QueryFinanceServiceProductByParamsResult {
	return &QueryFinanceServiceProductByParamsResult{}
}

func (p *QueryFinanceServiceProductByParamsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = vico_types.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryFinanceServiceProductByParams_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryFinanceServiceProductByParamsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryFinanceServiceProductByParamsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryFinanceServiceProductByParamsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryFinanceServiceProductByParamsResult(%+v)", *p)
}

type AddFinanceServiceProductArgs struct {
	Header *common.RequestHeader                     `thrift:"header,1" json:"header"`
	Info   *vico_finance_types.FinanceServiceProduct `thrift:"info,2" json:"info"`
}

func NewAddFinanceServiceProductArgs() *AddFinanceServiceProductArgs {
	return &AddFinanceServiceProductArgs{}
}

func (p *AddFinanceServiceProductArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceServiceProductArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddFinanceServiceProductArgs) readField2(iprot thrift.TProtocol) error {
	p.Info = vico_finance_types.NewFinanceServiceProduct()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *AddFinanceServiceProductArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFinanceServiceProduct_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceServiceProductArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddFinanceServiceProductArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:info: %s", p, err)
		}
	}
	return err
}

func (p *AddFinanceServiceProductArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFinanceServiceProductArgs(%+v)", *p)
}

type AddFinanceServiceProductResult struct {
	Success int64                         `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewAddFinanceServiceProductResult() *AddFinanceServiceProductResult {
	return &AddFinanceServiceProductResult{}
}

func (p *AddFinanceServiceProductResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceServiceProductResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddFinanceServiceProductResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddFinanceServiceProductResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFinanceServiceProduct_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFinanceServiceProductResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddFinanceServiceProductResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddFinanceServiceProductResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFinanceServiceProductResult(%+v)", *p)
}

type EditFinanceServiceProductArgs struct {
	Header *common.RequestHeader                     `thrift:"header,1" json:"header"`
	Info   *vico_finance_types.FinanceServiceProduct `thrift:"info,2" json:"info"`
}

func NewEditFinanceServiceProductArgs() *EditFinanceServiceProductArgs {
	return &EditFinanceServiceProductArgs{}
}

func (p *EditFinanceServiceProductArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditFinanceServiceProductArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditFinanceServiceProductArgs) readField2(iprot thrift.TProtocol) error {
	p.Info = vico_finance_types.NewFinanceServiceProduct()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *EditFinanceServiceProductArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editFinanceServiceProduct_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditFinanceServiceProductArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditFinanceServiceProductArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:info: %s", p, err)
		}
	}
	return err
}

func (p *EditFinanceServiceProductArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditFinanceServiceProductArgs(%+v)", *p)
}

type EditFinanceServiceProductResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewEditFinanceServiceProductResult() *EditFinanceServiceProductResult {
	return &EditFinanceServiceProductResult{}
}

func (p *EditFinanceServiceProductResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditFinanceServiceProductResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditFinanceServiceProductResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editFinanceServiceProduct_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditFinanceServiceProductResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditFinanceServiceProductResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditFinanceServiceProductResult(%+v)", *p)
}

type DeleteFinanceServiceProductByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int64               `thrift:"ids,2" json:"ids"`
}

func NewDeleteFinanceServiceProductByIdsArgs() *DeleteFinanceServiceProductByIdsArgs {
	return &DeleteFinanceServiceProductByIdsArgs{}
}

func (p *DeleteFinanceServiceProductByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteFinanceServiceProductByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeleteFinanceServiceProductByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem68 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem68 = v
		}
		p.Ids = append(p.Ids, _elem68)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeleteFinanceServiceProductByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteFinanceServiceProductByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteFinanceServiceProductByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeleteFinanceServiceProductByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *DeleteFinanceServiceProductByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFinanceServiceProductByIdsArgs(%+v)", *p)
}

type DeleteFinanceServiceProductByIdsResult struct {
	E *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewDeleteFinanceServiceProductByIdsResult() *DeleteFinanceServiceProductByIdsResult {
	return &DeleteFinanceServiceProductByIdsResult{}
}

func (p *DeleteFinanceServiceProductByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteFinanceServiceProductByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *DeleteFinanceServiceProductByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteFinanceServiceProductByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteFinanceServiceProductByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *DeleteFinanceServiceProductByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteFinanceServiceProductByIdsResult(%+v)", *p)
}

type CheckUserServiceProductAvailableArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	AccountId int64                 `thrift:"accountId,2" json:"accountId"`
}

func NewCheckUserServiceProductAvailableArgs() *CheckUserServiceProductAvailableArgs {
	return &CheckUserServiceProductAvailableArgs{}
}

func (p *CheckUserServiceProductAvailableArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckUserServiceProductAvailableArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CheckUserServiceProductAvailableArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *CheckUserServiceProductAvailableArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkUserServiceProductAvailable_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckUserServiceProductAvailableArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CheckUserServiceProductAvailableArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *CheckUserServiceProductAvailableArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckUserServiceProductAvailableArgs(%+v)", *p)
}

type CheckUserServiceProductAvailableResult struct {
	Success bool                          `thrift:"success,0" json:"success"`
	E       *vico_exception.VicoException `thrift:"e,1" json:"e"`
}

func NewCheckUserServiceProductAvailableResult() *CheckUserServiceProductAvailableResult {
	return &CheckUserServiceProductAvailableResult{}
}

func (p *CheckUserServiceProductAvailableResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckUserServiceProductAvailableResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *CheckUserServiceProductAvailableResult) readField1(iprot thrift.TProtocol) error {
	p.E = vico_exception.NewVicoException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *CheckUserServiceProductAvailableResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkUserServiceProductAvailable_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckUserServiceProductAvailableResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *CheckUserServiceProductAvailableResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *CheckUserServiceProductAvailableResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckUserServiceProductAvailableResult(%+v)", *p)
}
