// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/vico_exception"
	"rtb_model_server/common/domob_thrift/vico_finance_types"
	"rtb_model_server/common/domob_thrift/vico_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = vico_finance_types.GoUnusedProtection__
var _ = vico_types.GoUnusedProtection__
var _ = vico_exception.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int
