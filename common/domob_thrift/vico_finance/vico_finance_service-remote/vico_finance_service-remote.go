// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"vico_finance"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   getFinanceAccountSetting(RequestHeader header, i64 accountId)")
	fmt.Fprintln(os.Stderr, "  bool checkModuleOperatable(RequestHeader header, i64 accountId, VicoModuleId moduleId, i64 templateId)")
	fmt.Fprintln(os.Stderr, "  i64 addFinanceOrder(RequestHeader header, i64 accountId, FinanceOrder info)")
	fmt.Fprintln(os.Stderr, "  void sumitOrderPayment(RequestHeader header, i64 accountId, i64 orderId, FinanceOrderPaymentType paymentType, string outTradeNo)")
	fmt.Fprintln(os.Stderr, "  void cancelOrderByIds(RequestHeader header, i64 accountId,  orderIds)")
	fmt.Fprintln(os.Stderr, "  void deleteOrderByIds(RequestHeader header, i64 accountId,  orderIds)")
	fmt.Fprintln(os.Stderr, "   getOrderInfoByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryOrderByParams(RequestHeader header, FinanceOrderQueryParams params, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getFinanceServiceProductsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryFinanceServiceProductByParams(RequestHeader header, FinanceServiceProductParams params, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  i64 addFinanceServiceProduct(RequestHeader header, FinanceServiceProduct info)")
	fmt.Fprintln(os.Stderr, "  void editFinanceServiceProduct(RequestHeader header, FinanceServiceProduct info)")
	fmt.Fprintln(os.Stderr, "  void deleteFinanceServiceProductByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  bool checkUserServiceProductAvailable(RequestHeader header, i64 accountId)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := vico_finance.NewVicoFinanceServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getFinanceAccountSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinanceAccountSetting requires 2 args")
			flag.Usage()
		}
		arg69 := flag.Arg(1)
		mbTrans70 := thrift.NewTMemoryBufferLen(len(arg69))
		defer mbTrans70.Close()
		_, err71 := mbTrans70.WriteString(arg69)
		if err71 != nil {
			Usage()
			return
		}
		factory72 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt73 := factory72.GetProtocol(mbTrans70)
		argvalue0 := vico_finance.NewRequestHeader()
		err74 := argvalue0.Read(jsProt73)
		if err74 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err75 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err75 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFinanceAccountSetting(value0, value1))
		fmt.Print("\n")
		break
	case "checkModuleOperatable":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "CheckModuleOperatable requires 4 args")
			flag.Usage()
		}
		arg76 := flag.Arg(1)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue0 := vico_finance.NewRequestHeader()
		err81 := argvalue0.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err82 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err82 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := vico_finance.VicoModuleId(tmp2)
		value2 := argvalue2
		argvalue3, err83 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err83 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.CheckModuleOperatable(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addFinanceOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddFinanceOrder requires 3 args")
			flag.Usage()
		}
		arg84 := flag.Arg(1)
		mbTrans85 := thrift.NewTMemoryBufferLen(len(arg84))
		defer mbTrans85.Close()
		_, err86 := mbTrans85.WriteString(arg84)
		if err86 != nil {
			Usage()
			return
		}
		factory87 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt88 := factory87.GetProtocol(mbTrans85)
		argvalue0 := vico_finance.NewRequestHeader()
		err89 := argvalue0.Read(jsProt88)
		if err89 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err90 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err90 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg91 := flag.Arg(3)
		mbTrans92 := thrift.NewTMemoryBufferLen(len(arg91))
		defer mbTrans92.Close()
		_, err93 := mbTrans92.WriteString(arg91)
		if err93 != nil {
			Usage()
			return
		}
		factory94 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt95 := factory94.GetProtocol(mbTrans92)
		argvalue2 := vico_finance.NewFinanceOrder()
		err96 := argvalue2.Read(jsProt95)
		if err96 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddFinanceOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "sumitOrderPayment":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "SumitOrderPayment requires 5 args")
			flag.Usage()
		}
		arg97 := flag.Arg(1)
		mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
		defer mbTrans98.Close()
		_, err99 := mbTrans98.WriteString(arg97)
		if err99 != nil {
			Usage()
			return
		}
		factory100 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt101 := factory100.GetProtocol(mbTrans98)
		argvalue0 := vico_finance.NewRequestHeader()
		err102 := argvalue0.Read(jsProt101)
		if err102 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err103 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err103 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err104 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err104 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := vico_finance.FinanceOrderPaymentType(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.SumitOrderPayment(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "cancelOrderByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CancelOrderByIds requires 3 args")
			flag.Usage()
		}
		arg106 := flag.Arg(1)
		mbTrans107 := thrift.NewTMemoryBufferLen(len(arg106))
		defer mbTrans107.Close()
		_, err108 := mbTrans107.WriteString(arg106)
		if err108 != nil {
			Usage()
			return
		}
		factory109 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt110 := factory109.GetProtocol(mbTrans107)
		argvalue0 := vico_finance.NewRequestHeader()
		err111 := argvalue0.Read(jsProt110)
		if err111 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err112 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err112 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg113 := flag.Arg(3)
		mbTrans114 := thrift.NewTMemoryBufferLen(len(arg113))
		defer mbTrans114.Close()
		_, err115 := mbTrans114.WriteString(arg113)
		if err115 != nil {
			Usage()
			return
		}
		factory116 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt117 := factory116.GetProtocol(mbTrans114)
		containerStruct2 := vico_finance.NewCancelOrderByIdsArgs()
		err118 := containerStruct2.ReadField3(jsProt117)
		if err118 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.OrderIds
		value2 := argvalue2
		fmt.Print(client.CancelOrderByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteOrderByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteOrderByIds requires 3 args")
			flag.Usage()
		}
		arg119 := flag.Arg(1)
		mbTrans120 := thrift.NewTMemoryBufferLen(len(arg119))
		defer mbTrans120.Close()
		_, err121 := mbTrans120.WriteString(arg119)
		if err121 != nil {
			Usage()
			return
		}
		factory122 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt123 := factory122.GetProtocol(mbTrans120)
		argvalue0 := vico_finance.NewRequestHeader()
		err124 := argvalue0.Read(jsProt123)
		if err124 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err125 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err125 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg126 := flag.Arg(3)
		mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
		defer mbTrans127.Close()
		_, err128 := mbTrans127.WriteString(arg126)
		if err128 != nil {
			Usage()
			return
		}
		factory129 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt130 := factory129.GetProtocol(mbTrans127)
		containerStruct2 := vico_finance.NewDeleteOrderByIdsArgs()
		err131 := containerStruct2.ReadField3(jsProt130)
		if err131 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.OrderIds
		value2 := argvalue2
		fmt.Print(client.DeleteOrderByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getOrderInfoByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOrderInfoByIds requires 2 args")
			flag.Usage()
		}
		arg132 := flag.Arg(1)
		mbTrans133 := thrift.NewTMemoryBufferLen(len(arg132))
		defer mbTrans133.Close()
		_, err134 := mbTrans133.WriteString(arg132)
		if err134 != nil {
			Usage()
			return
		}
		factory135 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt136 := factory135.GetProtocol(mbTrans133)
		argvalue0 := vico_finance.NewRequestHeader()
		err137 := argvalue0.Read(jsProt136)
		if err137 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg138 := flag.Arg(2)
		mbTrans139 := thrift.NewTMemoryBufferLen(len(arg138))
		defer mbTrans139.Close()
		_, err140 := mbTrans139.WriteString(arg138)
		if err140 != nil {
			Usage()
			return
		}
		factory141 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt142 := factory141.GetProtocol(mbTrans139)
		containerStruct1 := vico_finance.NewGetOrderInfoByIdsArgs()
		err143 := containerStruct1.ReadField2(jsProt142)
		if err143 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetOrderInfoByIds(value0, value1))
		fmt.Print("\n")
		break
	case "queryOrderByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QueryOrderByParams requires 4 args")
			flag.Usage()
		}
		arg144 := flag.Arg(1)
		mbTrans145 := thrift.NewTMemoryBufferLen(len(arg144))
		defer mbTrans145.Close()
		_, err146 := mbTrans145.WriteString(arg144)
		if err146 != nil {
			Usage()
			return
		}
		factory147 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt148 := factory147.GetProtocol(mbTrans145)
		argvalue0 := vico_finance.NewRequestHeader()
		err149 := argvalue0.Read(jsProt148)
		if err149 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg150 := flag.Arg(2)
		mbTrans151 := thrift.NewTMemoryBufferLen(len(arg150))
		defer mbTrans151.Close()
		_, err152 := mbTrans151.WriteString(arg150)
		if err152 != nil {
			Usage()
			return
		}
		factory153 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt154 := factory153.GetProtocol(mbTrans151)
		argvalue1 := vico_finance.NewFinanceOrderQueryParams()
		err155 := argvalue1.Read(jsProt154)
		if err155 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err156 := (strconv.Atoi(flag.Arg(3)))
		if err156 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err157 := (strconv.Atoi(flag.Arg(4)))
		if err157 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QueryOrderByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getFinanceServiceProductsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinanceServiceProductsByIds requires 2 args")
			flag.Usage()
		}
		arg158 := flag.Arg(1)
		mbTrans159 := thrift.NewTMemoryBufferLen(len(arg158))
		defer mbTrans159.Close()
		_, err160 := mbTrans159.WriteString(arg158)
		if err160 != nil {
			Usage()
			return
		}
		factory161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt162 := factory161.GetProtocol(mbTrans159)
		argvalue0 := vico_finance.NewRequestHeader()
		err163 := argvalue0.Read(jsProt162)
		if err163 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg164 := flag.Arg(2)
		mbTrans165 := thrift.NewTMemoryBufferLen(len(arg164))
		defer mbTrans165.Close()
		_, err166 := mbTrans165.WriteString(arg164)
		if err166 != nil {
			Usage()
			return
		}
		factory167 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt168 := factory167.GetProtocol(mbTrans165)
		containerStruct1 := vico_finance.NewGetFinanceServiceProductsByIdsArgs()
		err169 := containerStruct1.ReadField2(jsProt168)
		if err169 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetFinanceServiceProductsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "queryFinanceServiceProductByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QueryFinanceServiceProductByParams requires 4 args")
			flag.Usage()
		}
		arg170 := flag.Arg(1)
		mbTrans171 := thrift.NewTMemoryBufferLen(len(arg170))
		defer mbTrans171.Close()
		_, err172 := mbTrans171.WriteString(arg170)
		if err172 != nil {
			Usage()
			return
		}
		factory173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt174 := factory173.GetProtocol(mbTrans171)
		argvalue0 := vico_finance.NewRequestHeader()
		err175 := argvalue0.Read(jsProt174)
		if err175 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg176 := flag.Arg(2)
		mbTrans177 := thrift.NewTMemoryBufferLen(len(arg176))
		defer mbTrans177.Close()
		_, err178 := mbTrans177.WriteString(arg176)
		if err178 != nil {
			Usage()
			return
		}
		factory179 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt180 := factory179.GetProtocol(mbTrans177)
		argvalue1 := vico_finance.NewFinanceServiceProductParams()
		err181 := argvalue1.Read(jsProt180)
		if err181 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err182 := (strconv.Atoi(flag.Arg(3)))
		if err182 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err183 := (strconv.Atoi(flag.Arg(4)))
		if err183 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QueryFinanceServiceProductByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addFinanceServiceProduct":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddFinanceServiceProduct requires 2 args")
			flag.Usage()
		}
		arg184 := flag.Arg(1)
		mbTrans185 := thrift.NewTMemoryBufferLen(len(arg184))
		defer mbTrans185.Close()
		_, err186 := mbTrans185.WriteString(arg184)
		if err186 != nil {
			Usage()
			return
		}
		factory187 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt188 := factory187.GetProtocol(mbTrans185)
		argvalue0 := vico_finance.NewRequestHeader()
		err189 := argvalue0.Read(jsProt188)
		if err189 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg190 := flag.Arg(2)
		mbTrans191 := thrift.NewTMemoryBufferLen(len(arg190))
		defer mbTrans191.Close()
		_, err192 := mbTrans191.WriteString(arg190)
		if err192 != nil {
			Usage()
			return
		}
		factory193 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt194 := factory193.GetProtocol(mbTrans191)
		argvalue1 := vico_finance.NewFinanceServiceProduct()
		err195 := argvalue1.Read(jsProt194)
		if err195 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddFinanceServiceProduct(value0, value1))
		fmt.Print("\n")
		break
	case "editFinanceServiceProduct":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditFinanceServiceProduct requires 2 args")
			flag.Usage()
		}
		arg196 := flag.Arg(1)
		mbTrans197 := thrift.NewTMemoryBufferLen(len(arg196))
		defer mbTrans197.Close()
		_, err198 := mbTrans197.WriteString(arg196)
		if err198 != nil {
			Usage()
			return
		}
		factory199 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt200 := factory199.GetProtocol(mbTrans197)
		argvalue0 := vico_finance.NewRequestHeader()
		err201 := argvalue0.Read(jsProt200)
		if err201 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg202 := flag.Arg(2)
		mbTrans203 := thrift.NewTMemoryBufferLen(len(arg202))
		defer mbTrans203.Close()
		_, err204 := mbTrans203.WriteString(arg202)
		if err204 != nil {
			Usage()
			return
		}
		factory205 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt206 := factory205.GetProtocol(mbTrans203)
		argvalue1 := vico_finance.NewFinanceServiceProduct()
		err207 := argvalue1.Read(jsProt206)
		if err207 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditFinanceServiceProduct(value0, value1))
		fmt.Print("\n")
		break
	case "deleteFinanceServiceProductByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteFinanceServiceProductByIds requires 2 args")
			flag.Usage()
		}
		arg208 := flag.Arg(1)
		mbTrans209 := thrift.NewTMemoryBufferLen(len(arg208))
		defer mbTrans209.Close()
		_, err210 := mbTrans209.WriteString(arg208)
		if err210 != nil {
			Usage()
			return
		}
		factory211 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt212 := factory211.GetProtocol(mbTrans209)
		argvalue0 := vico_finance.NewRequestHeader()
		err213 := argvalue0.Read(jsProt212)
		if err213 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg214 := flag.Arg(2)
		mbTrans215 := thrift.NewTMemoryBufferLen(len(arg214))
		defer mbTrans215.Close()
		_, err216 := mbTrans215.WriteString(arg214)
		if err216 != nil {
			Usage()
			return
		}
		factory217 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt218 := factory217.GetProtocol(mbTrans215)
		containerStruct1 := vico_finance.NewDeleteFinanceServiceProductByIdsArgs()
		err219 := containerStruct1.ReadField2(jsProt218)
		if err219 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteFinanceServiceProductByIds(value0, value1))
		fmt.Print("\n")
		break
	case "checkUserServiceProductAvailable":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CheckUserServiceProductAvailable requires 2 args")
			flag.Usage()
		}
		arg220 := flag.Arg(1)
		mbTrans221 := thrift.NewTMemoryBufferLen(len(arg220))
		defer mbTrans221.Close()
		_, err222 := mbTrans221.WriteString(arg220)
		if err222 != nil {
			Usage()
			return
		}
		factory223 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt224 := factory223.GetProtocol(mbTrans221)
		argvalue0 := vico_finance.NewRequestHeader()
		err225 := argvalue0.Read(jsProt224)
		if err225 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err226 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err226 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.CheckUserServiceProductAvailable(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
