// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_info_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/offerwall_info_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = offerwall_info_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type InfoServer interface { //Info Server服务

	// @Description("Get media info by mid")
	//
	// Parameters:
	//  - RequestHeader
	//  - Mid
	GetMediaInfo(requestHeader *OwRequestHeader, mid int32) (r *OwMediaInfoResponse, e *OWMediaNotFoundException, err error)
	// @Description("Get android media info by mid")
	//
	// Parameters:
	//  - RequestHeader
	//  - Mid
	GetAndroidMediaInfo(requestHeader *OwRequestHeader, mid int32) (r *OwMediaInfoResponse, e *OWMediaNotFoundException, err error)
	// @Description("Get offer list. Use mid & uid when isSr == TRUE to get self-recommend offer.")
	//
	// Parameters:
	//  - RequestHeader
	//  - Criteria
	GetOffers(requestHeader *OwRequestHeader, criteria *OwOfferInfoCriteria) (r *OwOfferInfoResponse, err error)
	// @Description("Get offer list by offerID(cid) list")
	//
	// Parameters:
	//  - RequestHeader
	//  - OfferIds
	//  - OnlineOnly
	GetSpecificOffers(requestHeader *OwRequestHeader, offerIds []int32, onlineOnly bool) (r *OwOfferInfoResponse, err error)
	// @Description("Get act record of current request device")
	//
	// Parameters:
	//  - RequestHeader
	//  - Platform
	//  - DeviceInfo
	GetActedInfo(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo) (r *OwActedInfoResponse, err error)
	// @Description("Get owUserId of current user")
	//
	// Parameters:
	//  - RequestHeader
	//  - Platform
	//  - DeviceInfo
	//  - UserId
	//  - Mid
	GetOwUserId(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (r *OwUserIdResponse, err error)
	// @Description("Check point for current user on this media")
	//
	// Parameters:
	//  - RequestHeader
	//  - OwUserId
	CheckPointByOwUserId(requestHeader *OwRequestHeader, owUserId string) (r *OwPointResponse, err error)
	// @Description("Check point for current user on this media")
	//
	// Parameters:
	//  - RequestHeader
	//  - Platform
	//  - DeviceInfo
	//  - UserId
	//  - Mid
	CheckPointByDetail(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (r *OwPointResponse, err error)
	// @Description("Try to consume specific point")
	//
	// Parameters:
	//  - RequestHeader
	//  - Platform
	//  - DeviceInfo
	//  - UserId
	//  - Mid
	//  - Uid
	//  - OrderId
	//  - ConsumeNum
	ConsumePoint(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32, uid int32, orderId string, consumeNum int64) (r *OwPointResponse, err error)
	// @Description("get task status on this media + user")
	//
	// Parameters:
	//  - RequestHeader
	//  - Platform
	//  - DeviceInfo
	//  - UserId
	//  - Mid
	GetTaskStatusList(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (r *OwTaskStatusResponse, err error)
	// @Description("get task status on all domob medias")
	//
	// Parameters:
	//  - RequestHeader
	//  - Platform
	//  - DeviceInfo
	GetAllTaskStatusList(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo) (r *OwTaskStatusResponse, err error)
	// @Description("process user appeal")
	//
	// Parameters:
	//  - RequestHeader
	//  - RequestData
	Appeal(requestHeader *OwRequestHeader, requestData *OwAppealRequest) (r *OwAppealResponse, err error)
	// @Description("Check bundle point for current domob user")
	//
	// Parameters:
	//  - RequestHeader
	//  - DmUid
	CheckBundlePoint(requestHeader *OwRequestHeader, dmUid int64) (r *OwPointResponse, err error)
	// @Description("consume bundle point for current domob user")
	//
	// Parameters:
	//  - RequestHeader
	//  - DmUid
	//  - OrderId
	//  - ConsumeNum
	ConsumeBundlePoint(requestHeader *OwRequestHeader, dmUid int64, orderId string, consumeNum int64) (r *OwPointResponse, err error)
	// @Description("获取请求中campaignID所绑定的积分墙广告计划id列表")
	//
	// Parameters:
	//  - ReqHeader
	//  - CampaignID
	GetPlanIDByCampaignID(reqHeader *OwRequestHeader, campaignID int32) (r *OwIdListResponse, err error)
	// @Description("根据plan id列表，获取相应的plan详情信息列表")
	//
	// Parameters:
	//  - ReqHeader
	//  - PlanIDs
	GetPlanInfoByPlanIDs(reqHeader *OwRequestHeader, planIDs []int32) (r *OwPlanInfoResponse, err error)
	// @Description("根据dt、hr，获取所有绑定了campaign的计划的计划级统计数据")
	//
	// Parameters:
	//  - ReqHeader
	//  - Dt
	//  - Hr
	GetStatData(reqHeader *OwRequestHeader, dt int32, hr int8) (r *OwAdPlanStatDataResponse, err error)
	// @Description("根据查询条件，获取绑定了campaign的计划的计划级统计数据")
	//
	// Parameters:
	//  - ReqHeader
	//  - Criteria
	GetStatsDataByCriteria(reqHeader *OwRequestHeader, criteria *OwStatsCriteria) (r *OwAdPlanStatDataResponse, err error)
}

//Info Server服务
type InfoServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewInfoServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *InfoServerClient {
	return &InfoServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewInfoServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *InfoServerClient {
	return &InfoServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// @Description("Get media info by mid")
//
// Parameters:
//  - RequestHeader
//  - Mid
func (p *InfoServerClient) GetMediaInfo(requestHeader *OwRequestHeader, mid int32) (r *OwMediaInfoResponse, e *OWMediaNotFoundException, err error) {
	if err = p.sendGetMediaInfo(requestHeader, mid); err != nil {
		return
	}
	return p.recvGetMediaInfo()
}

func (p *InfoServerClient) sendGetMediaInfo(requestHeader *OwRequestHeader, mid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMediaInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args10 := NewGetMediaInfoArgs()
	args10.RequestHeader = requestHeader
	args10.Mid = mid
	if err = args10.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetMediaInfo() (value *OwMediaInfoResponse, e *OWMediaNotFoundException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error12 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error13 error
		error13, err = error12.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error13
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result11 := NewGetMediaInfoResult()
	if err = result11.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result11.Success
	if result11.E != nil {
		e = result11.E
	}
	return
}

// @Description("Get android media info by mid")
//
// Parameters:
//  - RequestHeader
//  - Mid
func (p *InfoServerClient) GetAndroidMediaInfo(requestHeader *OwRequestHeader, mid int32) (r *OwMediaInfoResponse, e *OWMediaNotFoundException, err error) {
	if err = p.sendGetAndroidMediaInfo(requestHeader, mid); err != nil {
		return
	}
	return p.recvGetAndroidMediaInfo()
}

func (p *InfoServerClient) sendGetAndroidMediaInfo(requestHeader *OwRequestHeader, mid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAndroidMediaInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args14 := NewGetAndroidMediaInfoArgs()
	args14.RequestHeader = requestHeader
	args14.Mid = mid
	if err = args14.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetAndroidMediaInfo() (value *OwMediaInfoResponse, e *OWMediaNotFoundException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error16 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error17 error
		error17, err = error16.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error17
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result15 := NewGetAndroidMediaInfoResult()
	if err = result15.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result15.Success
	if result15.E != nil {
		e = result15.E
	}
	return
}

// @Description("Get offer list. Use mid & uid when isSr == TRUE to get self-recommend offer.")
//
// Parameters:
//  - RequestHeader
//  - Criteria
func (p *InfoServerClient) GetOffers(requestHeader *OwRequestHeader, criteria *OwOfferInfoCriteria) (r *OwOfferInfoResponse, err error) {
	if err = p.sendGetOffers(requestHeader, criteria); err != nil {
		return
	}
	return p.recvGetOffers()
}

func (p *InfoServerClient) sendGetOffers(requestHeader *OwRequestHeader, criteria *OwOfferInfoCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getOffers", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args18 := NewGetOffersArgs()
	args18.RequestHeader = requestHeader
	args18.Criteria = criteria
	if err = args18.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetOffers() (value *OwOfferInfoResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error20 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error21 error
		error21, err = error20.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error21
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result19 := NewGetOffersResult()
	if err = result19.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result19.Success
	return
}

// @Description("Get offer list by offerID(cid) list")
//
// Parameters:
//  - RequestHeader
//  - OfferIds
//  - OnlineOnly
func (p *InfoServerClient) GetSpecificOffers(requestHeader *OwRequestHeader, offerIds []int32, onlineOnly bool) (r *OwOfferInfoResponse, err error) {
	if err = p.sendGetSpecificOffers(requestHeader, offerIds, onlineOnly); err != nil {
		return
	}
	return p.recvGetSpecificOffers()
}

func (p *InfoServerClient) sendGetSpecificOffers(requestHeader *OwRequestHeader, offerIds []int32, onlineOnly bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSpecificOffers", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args22 := NewGetSpecificOffersArgs()
	args22.RequestHeader = requestHeader
	args22.OfferIds = offerIds
	args22.OnlineOnly = onlineOnly
	if err = args22.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetSpecificOffers() (value *OwOfferInfoResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error24 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error25 error
		error25, err = error24.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error25
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result23 := NewGetSpecificOffersResult()
	if err = result23.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result23.Success
	return
}

// @Description("Get act record of current request device")
//
// Parameters:
//  - RequestHeader
//  - Platform
//  - DeviceInfo
func (p *InfoServerClient) GetActedInfo(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo) (r *OwActedInfoResponse, err error) {
	if err = p.sendGetActedInfo(requestHeader, platform, deviceInfo); err != nil {
		return
	}
	return p.recvGetActedInfo()
}

func (p *InfoServerClient) sendGetActedInfo(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getActedInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args26 := NewGetActedInfoArgs()
	args26.RequestHeader = requestHeader
	args26.Platform = platform
	args26.DeviceInfo = deviceInfo
	if err = args26.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetActedInfo() (value *OwActedInfoResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error28 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error29 error
		error29, err = error28.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error29
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result27 := NewGetActedInfoResult()
	if err = result27.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result27.Success
	return
}

// @Description("Get owUserId of current user")
//
// Parameters:
//  - RequestHeader
//  - Platform
//  - DeviceInfo
//  - UserId
//  - Mid
func (p *InfoServerClient) GetOwUserId(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (r *OwUserIdResponse, err error) {
	if err = p.sendGetOwUserId(requestHeader, platform, deviceInfo, userId, mid); err != nil {
		return
	}
	return p.recvGetOwUserId()
}

func (p *InfoServerClient) sendGetOwUserId(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getOwUserId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args30 := NewGetOwUserIdArgs()
	args30.RequestHeader = requestHeader
	args30.Platform = platform
	args30.DeviceInfo = deviceInfo
	args30.UserId = userId
	args30.Mid = mid
	if err = args30.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetOwUserId() (value *OwUserIdResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error32 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error33 error
		error33, err = error32.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error33
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result31 := NewGetOwUserIdResult()
	if err = result31.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result31.Success
	return
}

// @Description("Check point for current user on this media")
//
// Parameters:
//  - RequestHeader
//  - OwUserId
func (p *InfoServerClient) CheckPointByOwUserId(requestHeader *OwRequestHeader, owUserId string) (r *OwPointResponse, err error) {
	if err = p.sendCheckPointByOwUserId(requestHeader, owUserId); err != nil {
		return
	}
	return p.recvCheckPointByOwUserId()
}

func (p *InfoServerClient) sendCheckPointByOwUserId(requestHeader *OwRequestHeader, owUserId string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkPointByOwUserId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args34 := NewCheckPointByOwUserIdArgs()
	args34.RequestHeader = requestHeader
	args34.OwUserId = owUserId
	if err = args34.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvCheckPointByOwUserId() (value *OwPointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error36 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error37 error
		error37, err = error36.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error37
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result35 := NewCheckPointByOwUserIdResult()
	if err = result35.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result35.Success
	return
}

// @Description("Check point for current user on this media")
//
// Parameters:
//  - RequestHeader
//  - Platform
//  - DeviceInfo
//  - UserId
//  - Mid
func (p *InfoServerClient) CheckPointByDetail(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (r *OwPointResponse, err error) {
	if err = p.sendCheckPointByDetail(requestHeader, platform, deviceInfo, userId, mid); err != nil {
		return
	}
	return p.recvCheckPointByDetail()
}

func (p *InfoServerClient) sendCheckPointByDetail(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkPointByDetail", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args38 := NewCheckPointByDetailArgs()
	args38.RequestHeader = requestHeader
	args38.Platform = platform
	args38.DeviceInfo = deviceInfo
	args38.UserId = userId
	args38.Mid = mid
	if err = args38.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvCheckPointByDetail() (value *OwPointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error40 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error41 error
		error41, err = error40.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error41
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result39 := NewCheckPointByDetailResult()
	if err = result39.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result39.Success
	return
}

// @Description("Try to consume specific point")
//
// Parameters:
//  - RequestHeader
//  - Platform
//  - DeviceInfo
//  - UserId
//  - Mid
//  - Uid
//  - OrderId
//  - ConsumeNum
func (p *InfoServerClient) ConsumePoint(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32, uid int32, orderId string, consumeNum int64) (r *OwPointResponse, err error) {
	if err = p.sendConsumePoint(requestHeader, platform, deviceInfo, userId, mid, uid, orderId, consumeNum); err != nil {
		return
	}
	return p.recvConsumePoint()
}

func (p *InfoServerClient) sendConsumePoint(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32, uid int32, orderId string, consumeNum int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("consumePoint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args42 := NewConsumePointArgs()
	args42.RequestHeader = requestHeader
	args42.Platform = platform
	args42.DeviceInfo = deviceInfo
	args42.UserId = userId
	args42.Mid = mid
	args42.Uid = uid
	args42.OrderId = orderId
	args42.ConsumeNum = consumeNum
	if err = args42.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvConsumePoint() (value *OwPointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error44 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error45 error
		error45, err = error44.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error45
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result43 := NewConsumePointResult()
	if err = result43.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result43.Success
	return
}

// @Description("get task status on this media + user")
//
// Parameters:
//  - RequestHeader
//  - Platform
//  - DeviceInfo
//  - UserId
//  - Mid
func (p *InfoServerClient) GetTaskStatusList(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (r *OwTaskStatusResponse, err error) {
	if err = p.sendGetTaskStatusList(requestHeader, platform, deviceInfo, userId, mid); err != nil {
		return
	}
	return p.recvGetTaskStatusList()
}

func (p *InfoServerClient) sendGetTaskStatusList(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo, userId string, mid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTaskStatusList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args46 := NewGetTaskStatusListArgs()
	args46.RequestHeader = requestHeader
	args46.Platform = platform
	args46.DeviceInfo = deviceInfo
	args46.UserId = userId
	args46.Mid = mid
	if err = args46.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetTaskStatusList() (value *OwTaskStatusResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error48 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error49 error
		error49, err = error48.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error49
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result47 := NewGetTaskStatusListResult()
	if err = result47.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result47.Success
	return
}

// @Description("get task status on all domob medias")
//
// Parameters:
//  - RequestHeader
//  - Platform
//  - DeviceInfo
func (p *InfoServerClient) GetAllTaskStatusList(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo) (r *OwTaskStatusResponse, err error) {
	if err = p.sendGetAllTaskStatusList(requestHeader, platform, deviceInfo); err != nil {
		return
	}
	return p.recvGetAllTaskStatusList()
}

func (p *InfoServerClient) sendGetAllTaskStatusList(requestHeader *OwRequestHeader, platform Platform, deviceInfo *OwDeviceInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllTaskStatusList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args50 := NewGetAllTaskStatusListArgs()
	args50.RequestHeader = requestHeader
	args50.Platform = platform
	args50.DeviceInfo = deviceInfo
	if err = args50.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetAllTaskStatusList() (value *OwTaskStatusResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error52 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error53 error
		error53, err = error52.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error53
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result51 := NewGetAllTaskStatusListResult()
	if err = result51.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result51.Success
	return
}

// @Description("process user appeal")
//
// Parameters:
//  - RequestHeader
//  - RequestData
func (p *InfoServerClient) Appeal(requestHeader *OwRequestHeader, requestData *OwAppealRequest) (r *OwAppealResponse, err error) {
	if err = p.sendAppeal(requestHeader, requestData); err != nil {
		return
	}
	return p.recvAppeal()
}

func (p *InfoServerClient) sendAppeal(requestHeader *OwRequestHeader, requestData *OwAppealRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("appeal", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args54 := NewAppealArgs()
	args54.RequestHeader = requestHeader
	args54.RequestData = requestData
	if err = args54.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvAppeal() (value *OwAppealResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error56 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error57 error
		error57, err = error56.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error57
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result55 := NewAppealResult()
	if err = result55.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result55.Success
	return
}

// @Description("Check bundle point for current domob user")
//
// Parameters:
//  - RequestHeader
//  - DmUid
func (p *InfoServerClient) CheckBundlePoint(requestHeader *OwRequestHeader, dmUid int64) (r *OwPointResponse, err error) {
	if err = p.sendCheckBundlePoint(requestHeader, dmUid); err != nil {
		return
	}
	return p.recvCheckBundlePoint()
}

func (p *InfoServerClient) sendCheckBundlePoint(requestHeader *OwRequestHeader, dmUid int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkBundlePoint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args58 := NewCheckBundlePointArgs()
	args58.RequestHeader = requestHeader
	args58.DmUid = dmUid
	if err = args58.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvCheckBundlePoint() (value *OwPointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error60 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error61 error
		error61, err = error60.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error61
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result59 := NewCheckBundlePointResult()
	if err = result59.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result59.Success
	return
}

// @Description("consume bundle point for current domob user")
//
// Parameters:
//  - RequestHeader
//  - DmUid
//  - OrderId
//  - ConsumeNum
func (p *InfoServerClient) ConsumeBundlePoint(requestHeader *OwRequestHeader, dmUid int64, orderId string, consumeNum int64) (r *OwPointResponse, err error) {
	if err = p.sendConsumeBundlePoint(requestHeader, dmUid, orderId, consumeNum); err != nil {
		return
	}
	return p.recvConsumeBundlePoint()
}

func (p *InfoServerClient) sendConsumeBundlePoint(requestHeader *OwRequestHeader, dmUid int64, orderId string, consumeNum int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("consumeBundlePoint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args62 := NewConsumeBundlePointArgs()
	args62.RequestHeader = requestHeader
	args62.DmUid = dmUid
	args62.OrderId = orderId
	args62.ConsumeNum = consumeNum
	if err = args62.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvConsumeBundlePoint() (value *OwPointResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error64 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error65 error
		error65, err = error64.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error65
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result63 := NewConsumeBundlePointResult()
	if err = result63.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result63.Success
	return
}

// @Description("获取请求中campaignID所绑定的积分墙广告计划id列表")
//
// Parameters:
//  - ReqHeader
//  - CampaignID
func (p *InfoServerClient) GetPlanIDByCampaignID(reqHeader *OwRequestHeader, campaignID int32) (r *OwIdListResponse, err error) {
	if err = p.sendGetPlanIDByCampaignID(reqHeader, campaignID); err != nil {
		return
	}
	return p.recvGetPlanIDByCampaignID()
}

func (p *InfoServerClient) sendGetPlanIDByCampaignID(reqHeader *OwRequestHeader, campaignID int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlanIDByCampaignID", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args66 := NewGetPlanIDByCampaignIDArgs()
	args66.ReqHeader = reqHeader
	args66.CampaignID = campaignID
	if err = args66.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetPlanIDByCampaignID() (value *OwIdListResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error68 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error69 error
		error69, err = error68.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error69
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result67 := NewGetPlanIDByCampaignIDResult()
	if err = result67.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result67.Success
	return
}

// @Description("根据plan id列表，获取相应的plan详情信息列表")
//
// Parameters:
//  - ReqHeader
//  - PlanIDs
func (p *InfoServerClient) GetPlanInfoByPlanIDs(reqHeader *OwRequestHeader, planIDs []int32) (r *OwPlanInfoResponse, err error) {
	if err = p.sendGetPlanInfoByPlanIDs(reqHeader, planIDs); err != nil {
		return
	}
	return p.recvGetPlanInfoByPlanIDs()
}

func (p *InfoServerClient) sendGetPlanInfoByPlanIDs(reqHeader *OwRequestHeader, planIDs []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlanInfoByPlanIDs", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args70 := NewGetPlanInfoByPlanIDsArgs()
	args70.ReqHeader = reqHeader
	args70.PlanIDs = planIDs
	if err = args70.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetPlanInfoByPlanIDs() (value *OwPlanInfoResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error72 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error73 error
		error73, err = error72.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error73
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result71 := NewGetPlanInfoByPlanIDsResult()
	if err = result71.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result71.Success
	return
}

// @Description("根据dt、hr，获取所有绑定了campaign的计划的计划级统计数据")
//
// Parameters:
//  - ReqHeader
//  - Dt
//  - Hr
func (p *InfoServerClient) GetStatData(reqHeader *OwRequestHeader, dt int32, hr int8) (r *OwAdPlanStatDataResponse, err error) {
	if err = p.sendGetStatData(reqHeader, dt, hr); err != nil {
		return
	}
	return p.recvGetStatData()
}

func (p *InfoServerClient) sendGetStatData(reqHeader *OwRequestHeader, dt int32, hr int8) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getStatData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args74 := NewGetStatDataArgs()
	args74.ReqHeader = reqHeader
	args74.Dt = dt
	args74.Hr = hr
	if err = args74.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetStatData() (value *OwAdPlanStatDataResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error76 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error77 error
		error77, err = error76.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error77
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result75 := NewGetStatDataResult()
	if err = result75.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result75.Success
	return
}

// @Description("根据查询条件，获取绑定了campaign的计划的计划级统计数据")
//
// Parameters:
//  - ReqHeader
//  - Criteria
func (p *InfoServerClient) GetStatsDataByCriteria(reqHeader *OwRequestHeader, criteria *OwStatsCriteria) (r *OwAdPlanStatDataResponse, err error) {
	if err = p.sendGetStatsDataByCriteria(reqHeader, criteria); err != nil {
		return
	}
	return p.recvGetStatsDataByCriteria()
}

func (p *InfoServerClient) sendGetStatsDataByCriteria(reqHeader *OwRequestHeader, criteria *OwStatsCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getStatsDataByCriteria", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args78 := NewGetStatsDataByCriteriaArgs()
	args78.ReqHeader = reqHeader
	args78.Criteria = criteria
	if err = args78.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *InfoServerClient) recvGetStatsDataByCriteria() (value *OwAdPlanStatDataResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error80 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error81 error
		error81, err = error80.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error81
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result79 := NewGetStatsDataByCriteriaResult()
	if err = result79.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result79.Success
	return
}

type InfoServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      InfoServer
}

func (p *InfoServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *InfoServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *InfoServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewInfoServerProcessor(handler InfoServer) *InfoServerProcessor {

	self82 := &InfoServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self82.processorMap["getMediaInfo"] = &infoServerProcessorGetMediaInfo{handler: handler}
	self82.processorMap["getAndroidMediaInfo"] = &infoServerProcessorGetAndroidMediaInfo{handler: handler}
	self82.processorMap["getOffers"] = &infoServerProcessorGetOffers{handler: handler}
	self82.processorMap["getSpecificOffers"] = &infoServerProcessorGetSpecificOffers{handler: handler}
	self82.processorMap["getActedInfo"] = &infoServerProcessorGetActedInfo{handler: handler}
	self82.processorMap["getOwUserId"] = &infoServerProcessorGetOwUserId{handler: handler}
	self82.processorMap["checkPointByOwUserId"] = &infoServerProcessorCheckPointByOwUserId{handler: handler}
	self82.processorMap["checkPointByDetail"] = &infoServerProcessorCheckPointByDetail{handler: handler}
	self82.processorMap["consumePoint"] = &infoServerProcessorConsumePoint{handler: handler}
	self82.processorMap["getTaskStatusList"] = &infoServerProcessorGetTaskStatusList{handler: handler}
	self82.processorMap["getAllTaskStatusList"] = &infoServerProcessorGetAllTaskStatusList{handler: handler}
	self82.processorMap["appeal"] = &infoServerProcessorAppeal{handler: handler}
	self82.processorMap["checkBundlePoint"] = &infoServerProcessorCheckBundlePoint{handler: handler}
	self82.processorMap["consumeBundlePoint"] = &infoServerProcessorConsumeBundlePoint{handler: handler}
	self82.processorMap["getPlanIDByCampaignID"] = &infoServerProcessorGetPlanIDByCampaignID{handler: handler}
	self82.processorMap["getPlanInfoByPlanIDs"] = &infoServerProcessorGetPlanInfoByPlanIDs{handler: handler}
	self82.processorMap["getStatData"] = &infoServerProcessorGetStatData{handler: handler}
	self82.processorMap["getStatsDataByCriteria"] = &infoServerProcessorGetStatsDataByCriteria{handler: handler}
	return self82
}

func (p *InfoServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x83 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x83.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x83

}

type infoServerProcessorGetMediaInfo struct {
	handler InfoServer
}

func (p *infoServerProcessorGetMediaInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMediaInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMediaInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMediaInfoResult()
	if result.Success, result.E, err = p.handler.GetMediaInfo(args.RequestHeader, args.Mid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMediaInfo: "+err.Error())
		oprot.WriteMessageBegin("getMediaInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMediaInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetAndroidMediaInfo struct {
	handler InfoServer
}

func (p *infoServerProcessorGetAndroidMediaInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAndroidMediaInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAndroidMediaInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAndroidMediaInfoResult()
	if result.Success, result.E, err = p.handler.GetAndroidMediaInfo(args.RequestHeader, args.Mid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAndroidMediaInfo: "+err.Error())
		oprot.WriteMessageBegin("getAndroidMediaInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAndroidMediaInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetOffers struct {
	handler InfoServer
}

func (p *infoServerProcessorGetOffers) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetOffersArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetOffersResult()
	if result.Success, err = p.handler.GetOffers(args.RequestHeader, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getOffers: "+err.Error())
		oprot.WriteMessageBegin("getOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getOffers", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetSpecificOffers struct {
	handler InfoServer
}

func (p *infoServerProcessorGetSpecificOffers) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSpecificOffersArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSpecificOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSpecificOffersResult()
	if result.Success, err = p.handler.GetSpecificOffers(args.RequestHeader, args.OfferIds, args.OnlineOnly); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSpecificOffers: "+err.Error())
		oprot.WriteMessageBegin("getSpecificOffers", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSpecificOffers", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetActedInfo struct {
	handler InfoServer
}

func (p *infoServerProcessorGetActedInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetActedInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getActedInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetActedInfoResult()
	if result.Success, err = p.handler.GetActedInfo(args.RequestHeader, args.Platform, args.DeviceInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getActedInfo: "+err.Error())
		oprot.WriteMessageBegin("getActedInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getActedInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetOwUserId struct {
	handler InfoServer
}

func (p *infoServerProcessorGetOwUserId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetOwUserIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getOwUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetOwUserIdResult()
	if result.Success, err = p.handler.GetOwUserId(args.RequestHeader, args.Platform, args.DeviceInfo, args.UserId, args.Mid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getOwUserId: "+err.Error())
		oprot.WriteMessageBegin("getOwUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getOwUserId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorCheckPointByOwUserId struct {
	handler InfoServer
}

func (p *infoServerProcessorCheckPointByOwUserId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckPointByOwUserIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkPointByOwUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckPointByOwUserIdResult()
	if result.Success, err = p.handler.CheckPointByOwUserId(args.RequestHeader, args.OwUserId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkPointByOwUserId: "+err.Error())
		oprot.WriteMessageBegin("checkPointByOwUserId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkPointByOwUserId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorCheckPointByDetail struct {
	handler InfoServer
}

func (p *infoServerProcessorCheckPointByDetail) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckPointByDetailArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkPointByDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckPointByDetailResult()
	if result.Success, err = p.handler.CheckPointByDetail(args.RequestHeader, args.Platform, args.DeviceInfo, args.UserId, args.Mid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkPointByDetail: "+err.Error())
		oprot.WriteMessageBegin("checkPointByDetail", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkPointByDetail", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorConsumePoint struct {
	handler InfoServer
}

func (p *infoServerProcessorConsumePoint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConsumePointArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("consumePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConsumePointResult()
	if result.Success, err = p.handler.ConsumePoint(args.RequestHeader, args.Platform, args.DeviceInfo, args.UserId, args.Mid, args.Uid, args.OrderId, args.ConsumeNum); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing consumePoint: "+err.Error())
		oprot.WriteMessageBegin("consumePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("consumePoint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetTaskStatusList struct {
	handler InfoServer
}

func (p *infoServerProcessorGetTaskStatusList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTaskStatusListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTaskStatusList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTaskStatusListResult()
	if result.Success, err = p.handler.GetTaskStatusList(args.RequestHeader, args.Platform, args.DeviceInfo, args.UserId, args.Mid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTaskStatusList: "+err.Error())
		oprot.WriteMessageBegin("getTaskStatusList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTaskStatusList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetAllTaskStatusList struct {
	handler InfoServer
}

func (p *infoServerProcessorGetAllTaskStatusList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllTaskStatusListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllTaskStatusList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllTaskStatusListResult()
	if result.Success, err = p.handler.GetAllTaskStatusList(args.RequestHeader, args.Platform, args.DeviceInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllTaskStatusList: "+err.Error())
		oprot.WriteMessageBegin("getAllTaskStatusList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllTaskStatusList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorAppeal struct {
	handler InfoServer
}

func (p *infoServerProcessorAppeal) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAppealArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("appeal", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAppealResult()
	if result.Success, err = p.handler.Appeal(args.RequestHeader, args.RequestData); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing appeal: "+err.Error())
		oprot.WriteMessageBegin("appeal", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("appeal", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorCheckBundlePoint struct {
	handler InfoServer
}

func (p *infoServerProcessorCheckBundlePoint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckBundlePointArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkBundlePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckBundlePointResult()
	if result.Success, err = p.handler.CheckBundlePoint(args.RequestHeader, args.DmUid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkBundlePoint: "+err.Error())
		oprot.WriteMessageBegin("checkBundlePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkBundlePoint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorConsumeBundlePoint struct {
	handler InfoServer
}

func (p *infoServerProcessorConsumeBundlePoint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConsumeBundlePointArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("consumeBundlePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConsumeBundlePointResult()
	if result.Success, err = p.handler.ConsumeBundlePoint(args.RequestHeader, args.DmUid, args.OrderId, args.ConsumeNum); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing consumeBundlePoint: "+err.Error())
		oprot.WriteMessageBegin("consumeBundlePoint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("consumeBundlePoint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetPlanIDByCampaignID struct {
	handler InfoServer
}

func (p *infoServerProcessorGetPlanIDByCampaignID) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlanIDByCampaignIDArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlanIDByCampaignID", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlanIDByCampaignIDResult()
	if result.Success, err = p.handler.GetPlanIDByCampaignID(args.ReqHeader, args.CampaignID); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlanIDByCampaignID: "+err.Error())
		oprot.WriteMessageBegin("getPlanIDByCampaignID", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlanIDByCampaignID", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetPlanInfoByPlanIDs struct {
	handler InfoServer
}

func (p *infoServerProcessorGetPlanInfoByPlanIDs) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlanInfoByPlanIDsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlanInfoByPlanIDs", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlanInfoByPlanIDsResult()
	if result.Success, err = p.handler.GetPlanInfoByPlanIDs(args.ReqHeader, args.PlanIDs); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlanInfoByPlanIDs: "+err.Error())
		oprot.WriteMessageBegin("getPlanInfoByPlanIDs", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlanInfoByPlanIDs", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetStatData struct {
	handler InfoServer
}

func (p *infoServerProcessorGetStatData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetStatDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getStatData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetStatDataResult()
	if result.Success, err = p.handler.GetStatData(args.ReqHeader, args.Dt, args.Hr); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStatData: "+err.Error())
		oprot.WriteMessageBegin("getStatData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getStatData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type infoServerProcessorGetStatsDataByCriteria struct {
	handler InfoServer
}

func (p *infoServerProcessorGetStatsDataByCriteria) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetStatsDataByCriteriaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getStatsDataByCriteria", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetStatsDataByCriteriaResult()
	if result.Success, err = p.handler.GetStatsDataByCriteria(args.ReqHeader, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStatsDataByCriteria: "+err.Error())
		oprot.WriteMessageBegin("getStatsDataByCriteria", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getStatsDataByCriteria", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetMediaInfoArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Mid           int32            `thrift:"mid,2" json:"mid"`
}

func NewGetMediaInfoArgs() *GetMediaInfoArgs {
	return &GetMediaInfoArgs{}
}

func (p *GetMediaInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetMediaInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *GetMediaInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *GetMediaInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaInfoArgs(%+v)", *p)
}

type GetMediaInfoResult struct {
	Success *OwMediaInfoResponse      `thrift:"success,0" json:"success"`
	E       *OWMediaNotFoundException `thrift:"e,1" json:"e"`
}

func NewGetMediaInfoResult() *GetMediaInfoResult {
	return &GetMediaInfoResult{}
}

func (p *GetMediaInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMediaInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwMediaInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetMediaInfoResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewOWMediaNotFoundException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetMediaInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMediaInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMediaInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetMediaInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMediaInfoResult(%+v)", *p)
}

type GetAndroidMediaInfoArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Mid           int32            `thrift:"mid,2" json:"mid"`
}

func NewGetAndroidMediaInfoArgs() *GetAndroidMediaInfoArgs {
	return &GetAndroidMediaInfoArgs{}
}

func (p *GetAndroidMediaInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAndroidMediaInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetAndroidMediaInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *GetAndroidMediaInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAndroidMediaInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAndroidMediaInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetAndroidMediaInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *GetAndroidMediaInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAndroidMediaInfoArgs(%+v)", *p)
}

type GetAndroidMediaInfoResult struct {
	Success *OwMediaInfoResponse      `thrift:"success,0" json:"success"`
	E       *OWMediaNotFoundException `thrift:"e,1" json:"e"`
}

func NewGetAndroidMediaInfoResult() *GetAndroidMediaInfoResult {
	return &GetAndroidMediaInfoResult{}
}

func (p *GetAndroidMediaInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAndroidMediaInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwMediaInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAndroidMediaInfoResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewOWMediaNotFoundException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetAndroidMediaInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAndroidMediaInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAndroidMediaInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAndroidMediaInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetAndroidMediaInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAndroidMediaInfoResult(%+v)", *p)
}

type GetOffersArgs struct {
	RequestHeader *OwRequestHeader     `thrift:"requestHeader,1" json:"requestHeader"`
	Criteria      *OwOfferInfoCriteria `thrift:"criteria,2" json:"criteria"`
}

func NewGetOffersArgs() *GetOffersArgs {
	return &GetOffersArgs{}
}

func (p *GetOffersArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOffersArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetOffersArgs) readField2(iprot thrift.TProtocol) error {
	p.Criteria = NewOwOfferInfoCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *GetOffersArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOffers_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOffersArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetOffersArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:criteria: %s", p, err)
		}
	}
	return err
}

func (p *GetOffersArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOffersArgs(%+v)", *p)
}

type GetOffersResult struct {
	Success *OwOfferInfoResponse `thrift:"success,0" json:"success"`
}

func NewGetOffersResult() *GetOffersResult {
	return &GetOffersResult{}
}

func (p *GetOffersResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOffersResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwOfferInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetOffersResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOffers_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOffersResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetOffersResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOffersResult(%+v)", *p)
}

type GetSpecificOffersArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	OfferIds      []int32          `thrift:"offerIds,2" json:"offerIds"`
	OnlineOnly    bool             `thrift:"onlineOnly,3" json:"onlineOnly"`
}

func NewGetSpecificOffersArgs() *GetSpecificOffersArgs {
	return &GetSpecificOffersArgs{}
}

func (p *GetSpecificOffersArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetSpecificOffersArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OfferIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem84 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem84 = v
		}
		p.OfferIds = append(p.OfferIds, _elem84)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetSpecificOffersArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OnlineOnly = v
	}
	return nil
}

func (p *GetSpecificOffersArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificOffers_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificOffersArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OfferIds != nil {
		if err := oprot.WriteFieldBegin("offerIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:offerIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OfferIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OfferIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:offerIds: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificOffersArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("onlineOnly", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:onlineOnly: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.OnlineOnly)); err != nil {
		return fmt.Errorf("%T.onlineOnly (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:onlineOnly: %s", p, err)
	}
	return err
}

func (p *GetSpecificOffersArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificOffersArgs(%+v)", *p)
}

type GetSpecificOffersResult struct {
	Success *OwOfferInfoResponse `thrift:"success,0" json:"success"`
}

func NewGetSpecificOffersResult() *GetSpecificOffersResult {
	return &GetSpecificOffersResult{}
}

func (p *GetSpecificOffersResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwOfferInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetSpecificOffersResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSpecificOffers_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSpecificOffersResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSpecificOffersResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSpecificOffersResult(%+v)", *p)
}

type GetActedInfoArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Platform      Platform         `thrift:"platform,2" json:"platform"`
	DeviceInfo    *OwDeviceInfo    `thrift:"deviceInfo,3" json:"deviceInfo"`
}

func NewGetActedInfoArgs() *GetActedInfoArgs {
	return &GetActedInfoArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetActedInfoArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetActedInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetActedInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetActedInfoArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *GetActedInfoArgs) readField3(iprot thrift.TProtocol) error {
	p.DeviceInfo = NewOwDeviceInfo()
	if err := p.DeviceInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeviceInfo)
	}
	return nil
}

func (p *GetActedInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getActedInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetActedInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetActedInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *GetActedInfoArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeviceInfo != nil {
		if err := oprot.WriteFieldBegin("deviceInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deviceInfo: %s", p, err)
		}
		if err := p.DeviceInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeviceInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deviceInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetActedInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetActedInfoArgs(%+v)", *p)
}

type GetActedInfoResult struct {
	Success *OwActedInfoResponse `thrift:"success,0" json:"success"`
}

func NewGetActedInfoResult() *GetActedInfoResult {
	return &GetActedInfoResult{}
}

func (p *GetActedInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetActedInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwActedInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetActedInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getActedInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetActedInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetActedInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetActedInfoResult(%+v)", *p)
}

type GetOwUserIdArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Platform      Platform         `thrift:"platform,2" json:"platform"`
	DeviceInfo    *OwDeviceInfo    `thrift:"deviceInfo,3" json:"deviceInfo"`
	UserId        string           `thrift:"userId,4" json:"userId"`
	Mid           int32            `thrift:"mid,5" json:"mid"`
}

func NewGetOwUserIdArgs() *GetOwUserIdArgs {
	return &GetOwUserIdArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetOwUserIdArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetOwUserIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOwUserIdArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetOwUserIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *GetOwUserIdArgs) readField3(iprot thrift.TProtocol) error {
	p.DeviceInfo = NewOwDeviceInfo()
	if err := p.DeviceInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeviceInfo)
	}
	return nil
}

func (p *GetOwUserIdArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *GetOwUserIdArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *GetOwUserIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOwUserId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOwUserIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetOwUserIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *GetOwUserIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeviceInfo != nil {
		if err := oprot.WriteFieldBegin("deviceInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deviceInfo: %s", p, err)
		}
		if err := p.DeviceInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeviceInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deviceInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetOwUserIdArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:userId: %s", p, err)
	}
	return err
}

func (p *GetOwUserIdArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mid: %s", p, err)
	}
	return err
}

func (p *GetOwUserIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOwUserIdArgs(%+v)", *p)
}

type GetOwUserIdResult struct {
	Success *OwUserIdResponse `thrift:"success,0" json:"success"`
}

func NewGetOwUserIdResult() *GetOwUserIdResult {
	return &GetOwUserIdResult{}
}

func (p *GetOwUserIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOwUserIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwUserIdResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetOwUserIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOwUserId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOwUserIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetOwUserIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOwUserIdResult(%+v)", *p)
}

type CheckPointByOwUserIdArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	OwUserId      string           `thrift:"owUserId,2" json:"owUserId"`
}

func NewCheckPointByOwUserIdArgs() *CheckPointByOwUserIdArgs {
	return &CheckPointByOwUserIdArgs{}
}

func (p *CheckPointByOwUserIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByOwUserIdArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *CheckPointByOwUserIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OwUserId = v
	}
	return nil
}

func (p *CheckPointByOwUserIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkPointByOwUserId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByOwUserIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *CheckPointByOwUserIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("owUserId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:owUserId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OwUserId)); err != nil {
		return fmt.Errorf("%T.owUserId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:owUserId: %s", p, err)
	}
	return err
}

func (p *CheckPointByOwUserIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckPointByOwUserIdArgs(%+v)", *p)
}

type CheckPointByOwUserIdResult struct {
	Success *OwPointResponse `thrift:"success,0" json:"success"`
}

func NewCheckPointByOwUserIdResult() *CheckPointByOwUserIdResult {
	return &CheckPointByOwUserIdResult{}
}

func (p *CheckPointByOwUserIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByOwUserIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CheckPointByOwUserIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkPointByOwUserId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByOwUserIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckPointByOwUserIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckPointByOwUserIdResult(%+v)", *p)
}

type CheckPointByDetailArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Platform      Platform         `thrift:"platform,2" json:"platform"`
	DeviceInfo    *OwDeviceInfo    `thrift:"deviceInfo,3" json:"deviceInfo"`
	UserId        string           `thrift:"userId,4" json:"userId"`
	Mid           int32            `thrift:"mid,5" json:"mid"`
}

func NewCheckPointByDetailArgs() *CheckPointByDetailArgs {
	return &CheckPointByDetailArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CheckPointByDetailArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *CheckPointByDetailArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByDetailArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *CheckPointByDetailArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *CheckPointByDetailArgs) readField3(iprot thrift.TProtocol) error {
	p.DeviceInfo = NewOwDeviceInfo()
	if err := p.DeviceInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeviceInfo)
	}
	return nil
}

func (p *CheckPointByDetailArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *CheckPointByDetailArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *CheckPointByDetailArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkPointByDetail_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByDetailArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *CheckPointByDetailArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *CheckPointByDetailArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeviceInfo != nil {
		if err := oprot.WriteFieldBegin("deviceInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deviceInfo: %s", p, err)
		}
		if err := p.DeviceInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeviceInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deviceInfo: %s", p, err)
		}
	}
	return err
}

func (p *CheckPointByDetailArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:userId: %s", p, err)
	}
	return err
}

func (p *CheckPointByDetailArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mid: %s", p, err)
	}
	return err
}

func (p *CheckPointByDetailArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckPointByDetailArgs(%+v)", *p)
}

type CheckPointByDetailResult struct {
	Success *OwPointResponse `thrift:"success,0" json:"success"`
}

func NewCheckPointByDetailResult() *CheckPointByDetailResult {
	return &CheckPointByDetailResult{}
}

func (p *CheckPointByDetailResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByDetailResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CheckPointByDetailResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkPointByDetail_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckPointByDetailResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckPointByDetailResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckPointByDetailResult(%+v)", *p)
}

type ConsumePointArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Platform      Platform         `thrift:"platform,2" json:"platform"`
	DeviceInfo    *OwDeviceInfo    `thrift:"deviceInfo,3" json:"deviceInfo"`
	UserId        string           `thrift:"userId,4" json:"userId"`
	Mid           int32            `thrift:"mid,5" json:"mid"`
	Uid           int32            `thrift:"uid,6" json:"uid"`
	OrderId       string           `thrift:"orderId,7" json:"orderId"`
	ConsumeNum    int64            `thrift:"consumeNum,8" json:"consumeNum"`
}

func NewConsumePointArgs() *ConsumePointArgs {
	return &ConsumePointArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ConsumePointArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *ConsumePointArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ConsumePointArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *ConsumePointArgs) readField3(iprot thrift.TProtocol) error {
	p.DeviceInfo = NewOwDeviceInfo()
	if err := p.DeviceInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeviceInfo)
	}
	return nil
}

func (p *ConsumePointArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *ConsumePointArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *ConsumePointArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ConsumePointArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *ConsumePointArgs) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ConsumeNum = v
	}
	return nil
}

func (p *ConsumePointArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consumePoint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *ConsumePointArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeviceInfo != nil {
		if err := oprot.WriteFieldBegin("deviceInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deviceInfo: %s", p, err)
		}
		if err := p.DeviceInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeviceInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deviceInfo: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:userId: %s", p, err)
	}
	return err
}

func (p *ConsumePointArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mid: %s", p, err)
	}
	return err
}

func (p *ConsumePointArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:uid: %s", p, err)
	}
	return err
}

func (p *ConsumePointArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:orderId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:orderId: %s", p, err)
	}
	return err
}

func (p *ConsumePointArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumeNum", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:consumeNum: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConsumeNum)); err != nil {
		return fmt.Errorf("%T.consumeNum (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:consumeNum: %s", p, err)
	}
	return err
}

func (p *ConsumePointArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumePointArgs(%+v)", *p)
}

type ConsumePointResult struct {
	Success *OwPointResponse `thrift:"success,0" json:"success"`
}

func NewConsumePointResult() *ConsumePointResult {
	return &ConsumePointResult{}
}

func (p *ConsumePointResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConsumePointResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consumePoint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumePointResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConsumePointResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumePointResult(%+v)", *p)
}

type GetTaskStatusListArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Platform      Platform         `thrift:"platform,2" json:"platform"`
	DeviceInfo    *OwDeviceInfo    `thrift:"deviceInfo,3" json:"deviceInfo"`
	UserId        string           `thrift:"userId,4" json:"userId"`
	Mid           int32            `thrift:"mid,5" json:"mid"`
}

func NewGetTaskStatusListArgs() *GetTaskStatusListArgs {
	return &GetTaskStatusListArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetTaskStatusListArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetTaskStatusListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusListArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetTaskStatusListArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *GetTaskStatusListArgs) readField3(iprot thrift.TProtocol) error {
	p.DeviceInfo = NewOwDeviceInfo()
	if err := p.DeviceInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeviceInfo)
	}
	return nil
}

func (p *GetTaskStatusListArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *GetTaskStatusListArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *GetTaskStatusListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskStatusList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskStatusListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *GetTaskStatusListArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeviceInfo != nil {
		if err := oprot.WriteFieldBegin("deviceInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deviceInfo: %s", p, err)
		}
		if err := p.DeviceInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeviceInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deviceInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskStatusListArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:userId: %s", p, err)
	}
	return err
}

func (p *GetTaskStatusListArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:mid: %s", p, err)
	}
	return err
}

func (p *GetTaskStatusListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskStatusListArgs(%+v)", *p)
}

type GetTaskStatusListResult struct {
	Success *OwTaskStatusResponse `thrift:"success,0" json:"success"`
}

func NewGetTaskStatusListResult() *GetTaskStatusListResult {
	return &GetTaskStatusListResult{}
}

func (p *GetTaskStatusListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusListResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwTaskStatusResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTaskStatusListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTaskStatusList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTaskStatusListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTaskStatusListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTaskStatusListResult(%+v)", *p)
}

type GetAllTaskStatusListArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Platform      Platform         `thrift:"platform,2" json:"platform"`
	DeviceInfo    *OwDeviceInfo    `thrift:"deviceInfo,3" json:"deviceInfo"`
}

func NewGetAllTaskStatusListArgs() *GetAllTaskStatusListArgs {
	return &GetAllTaskStatusListArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetAllTaskStatusListArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetAllTaskStatusListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllTaskStatusListArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetAllTaskStatusListArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *GetAllTaskStatusListArgs) readField3(iprot thrift.TProtocol) error {
	p.DeviceInfo = NewOwDeviceInfo()
	if err := p.DeviceInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeviceInfo)
	}
	return nil
}

func (p *GetAllTaskStatusListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllTaskStatusList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllTaskStatusListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetAllTaskStatusListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *GetAllTaskStatusListArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeviceInfo != nil {
		if err := oprot.WriteFieldBegin("deviceInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deviceInfo: %s", p, err)
		}
		if err := p.DeviceInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeviceInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deviceInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetAllTaskStatusListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllTaskStatusListArgs(%+v)", *p)
}

type GetAllTaskStatusListResult struct {
	Success *OwTaskStatusResponse `thrift:"success,0" json:"success"`
}

func NewGetAllTaskStatusListResult() *GetAllTaskStatusListResult {
	return &GetAllTaskStatusListResult{}
}

func (p *GetAllTaskStatusListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllTaskStatusListResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwTaskStatusResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAllTaskStatusListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllTaskStatusList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllTaskStatusListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllTaskStatusListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllTaskStatusListResult(%+v)", *p)
}

type AppealArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	RequestData   *OwAppealRequest `thrift:"requestData,2" json:"requestData"`
}

func NewAppealArgs() *AppealArgs {
	return &AppealArgs{}
}

func (p *AppealArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppealArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *AppealArgs) readField2(iprot thrift.TProtocol) error {
	p.RequestData = NewOwAppealRequest()
	if err := p.RequestData.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestData)
	}
	return nil
}

func (p *AppealArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("appeal_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppealArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *AppealArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RequestData != nil {
		if err := oprot.WriteFieldBegin("requestData", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:requestData: %s", p, err)
		}
		if err := p.RequestData.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestData)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:requestData: %s", p, err)
		}
	}
	return err
}

func (p *AppealArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppealArgs(%+v)", *p)
}

type AppealResult struct {
	Success *OwAppealResponse `thrift:"success,0" json:"success"`
}

func NewAppealResult() *AppealResult {
	return &AppealResult{}
}

func (p *AppealResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppealResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAppealResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AppealResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("appeal_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppealResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AppealResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppealResult(%+v)", *p)
}

type CheckBundlePointArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	DmUid         int64            `thrift:"dmUid,2" json:"dmUid"`
}

func NewCheckBundlePointArgs() *CheckBundlePointArgs {
	return &CheckBundlePointArgs{}
}

func (p *CheckBundlePointArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckBundlePointArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *CheckBundlePointArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DmUid = v
	}
	return nil
}

func (p *CheckBundlePointArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkBundlePoint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckBundlePointArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *CheckBundlePointArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmUid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dmUid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DmUid)); err != nil {
		return fmt.Errorf("%T.dmUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dmUid: %s", p, err)
	}
	return err
}

func (p *CheckBundlePointArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckBundlePointArgs(%+v)", *p)
}

type CheckBundlePointResult struct {
	Success *OwPointResponse `thrift:"success,0" json:"success"`
}

func NewCheckBundlePointResult() *CheckBundlePointResult {
	return &CheckBundlePointResult{}
}

func (p *CheckBundlePointResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckBundlePointResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CheckBundlePointResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkBundlePoint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckBundlePointResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckBundlePointResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckBundlePointResult(%+v)", *p)
}

type ConsumeBundlePointArgs struct {
	RequestHeader *OwRequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	DmUid         int64            `thrift:"dmUid,2" json:"dmUid"`
	OrderId       string           `thrift:"orderId,3" json:"orderId"`
	ConsumeNum    int64            `thrift:"consumeNum,4" json:"consumeNum"`
}

func NewConsumeBundlePointArgs() *ConsumeBundlePointArgs {
	return &ConsumeBundlePointArgs{}
}

func (p *ConsumeBundlePointArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumeBundlePointArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = NewOwRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ConsumeBundlePointArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DmUid = v
	}
	return nil
}

func (p *ConsumeBundlePointArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *ConsumeBundlePointArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ConsumeNum = v
	}
	return nil
}

func (p *ConsumeBundlePointArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consumeBundlePoint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumeBundlePointArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ConsumeBundlePointArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmUid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dmUid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DmUid)); err != nil {
		return fmt.Errorf("%T.dmUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dmUid: %s", p, err)
	}
	return err
}

func (p *ConsumeBundlePointArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:orderId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:orderId: %s", p, err)
	}
	return err
}

func (p *ConsumeBundlePointArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumeNum", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:consumeNum: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConsumeNum)); err != nil {
		return fmt.Errorf("%T.consumeNum (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:consumeNum: %s", p, err)
	}
	return err
}

func (p *ConsumeBundlePointArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeBundlePointArgs(%+v)", *p)
}

type ConsumeBundlePointResult struct {
	Success *OwPointResponse `thrift:"success,0" json:"success"`
}

func NewConsumeBundlePointResult() *ConsumeBundlePointResult {
	return &ConsumeBundlePointResult{}
}

func (p *ConsumeBundlePointResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConsumeBundlePointResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwPointResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConsumeBundlePointResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("consumeBundlePoint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConsumeBundlePointResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConsumeBundlePointResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConsumeBundlePointResult(%+v)", *p)
}

type GetPlanIDByCampaignIDArgs struct {
	ReqHeader  *OwRequestHeader `thrift:"reqHeader,1" json:"reqHeader"`
	CampaignID int32            `thrift:"campaignID,2" json:"campaignID"`
}

func NewGetPlanIDByCampaignIDArgs() *GetPlanIDByCampaignIDArgs {
	return &GetPlanIDByCampaignIDArgs{}
}

func (p *GetPlanIDByCampaignIDArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanIDByCampaignIDArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwRequestHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *GetPlanIDByCampaignIDArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignID = v
	}
	return nil
}

func (p *GetPlanIDByCampaignIDArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanIDByCampaignID_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanIDByCampaignIDArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanIDByCampaignIDArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignID", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaignID: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignID)); err != nil {
		return fmt.Errorf("%T.campaignID (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaignID: %s", p, err)
	}
	return err
}

func (p *GetPlanIDByCampaignIDArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanIDByCampaignIDArgs(%+v)", *p)
}

type GetPlanIDByCampaignIDResult struct {
	Success *OwIdListResponse `thrift:"success,0" json:"success"`
}

func NewGetPlanIDByCampaignIDResult() *GetPlanIDByCampaignIDResult {
	return &GetPlanIDByCampaignIDResult{}
}

func (p *GetPlanIDByCampaignIDResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanIDByCampaignIDResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwIdListResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetPlanIDByCampaignIDResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanIDByCampaignID_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanIDByCampaignIDResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanIDByCampaignIDResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanIDByCampaignIDResult(%+v)", *p)
}

type GetPlanInfoByPlanIDsArgs struct {
	ReqHeader *OwRequestHeader `thrift:"reqHeader,1" json:"reqHeader"`
	PlanIDs   []int32          `thrift:"planIDs,2" json:"planIDs"`
}

func NewGetPlanInfoByPlanIDsArgs() *GetPlanInfoByPlanIDsArgs {
	return &GetPlanInfoByPlanIDsArgs{}
}

func (p *GetPlanInfoByPlanIDsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanInfoByPlanIDsArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwRequestHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *GetPlanInfoByPlanIDsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlanIDs = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem85 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem85 = v
		}
		p.PlanIDs = append(p.PlanIDs, _elem85)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPlanInfoByPlanIDsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanInfoByPlanIDs_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanInfoByPlanIDsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanInfoByPlanIDsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.PlanIDs != nil {
		if err := oprot.WriteFieldBegin("planIDs", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:planIDs: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PlanIDs)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlanIDs {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:planIDs: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanInfoByPlanIDsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanInfoByPlanIDsArgs(%+v)", *p)
}

type GetPlanInfoByPlanIDsResult struct {
	Success *OwPlanInfoResponse `thrift:"success,0" json:"success"`
}

func NewGetPlanInfoByPlanIDsResult() *GetPlanInfoByPlanIDsResult {
	return &GetPlanInfoByPlanIDsResult{}
}

func (p *GetPlanInfoByPlanIDsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlanInfoByPlanIDsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwPlanInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetPlanInfoByPlanIDsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlanInfoByPlanIDs_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlanInfoByPlanIDsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlanInfoByPlanIDsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlanInfoByPlanIDsResult(%+v)", *p)
}

type GetStatDataArgs struct {
	ReqHeader *OwRequestHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Dt        int32            `thrift:"dt,2" json:"dt"`
	Hr        int8             `thrift:"hr,3" json:"hr"`
}

func NewGetStatDataArgs() *GetStatDataArgs {
	return &GetStatDataArgs{}
}

func (p *GetStatDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetStatDataArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwRequestHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *GetStatDataArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *GetStatDataArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Hr = int8(v)
	}
	return nil
}

func (p *GetStatDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getStatData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetStatDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetStatDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dt: %s", p, err)
	}
	return err
}

func (p *GetStatDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.BYTE, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:hr: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:hr: %s", p, err)
	}
	return err
}

func (p *GetStatDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetStatDataArgs(%+v)", *p)
}

type GetStatDataResult struct {
	Success *OwAdPlanStatDataResponse `thrift:"success,0" json:"success"`
}

func NewGetStatDataResult() *GetStatDataResult {
	return &GetStatDataResult{}
}

func (p *GetStatDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetStatDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAdPlanStatDataResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetStatDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getStatData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetStatDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetStatDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetStatDataResult(%+v)", *p)
}

type GetStatsDataByCriteriaArgs struct {
	ReqHeader *OwRequestHeader `thrift:"reqHeader,1" json:"reqHeader"`
	Criteria  *OwStatsCriteria `thrift:"criteria,2" json:"criteria"`
}

func NewGetStatsDataByCriteriaArgs() *GetStatsDataByCriteriaArgs {
	return &GetStatsDataByCriteriaArgs{}
}

func (p *GetStatsDataByCriteriaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetStatsDataByCriteriaArgs) readField1(iprot thrift.TProtocol) error {
	p.ReqHeader = NewOwRequestHeader()
	if err := p.ReqHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ReqHeader)
	}
	return nil
}

func (p *GetStatsDataByCriteriaArgs) readField2(iprot thrift.TProtocol) error {
	p.Criteria = NewOwStatsCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *GetStatsDataByCriteriaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getStatsDataByCriteria_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetStatsDataByCriteriaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ReqHeader != nil {
		if err := oprot.WriteFieldBegin("reqHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:reqHeader: %s", p, err)
		}
		if err := p.ReqHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ReqHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:reqHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetStatsDataByCriteriaArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:criteria: %s", p, err)
		}
	}
	return err
}

func (p *GetStatsDataByCriteriaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetStatsDataByCriteriaArgs(%+v)", *p)
}

type GetStatsDataByCriteriaResult struct {
	Success *OwAdPlanStatDataResponse `thrift:"success,0" json:"success"`
}

func NewGetStatsDataByCriteriaResult() *GetStatsDataByCriteriaResult {
	return &GetStatsDataByCriteriaResult{}
}

func (p *GetStatsDataByCriteriaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetStatsDataByCriteriaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewOwAdPlanStatDataResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetStatsDataByCriteriaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getStatsDataByCriteria_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetStatsDataByCriteriaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetStatsDataByCriteriaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetStatsDataByCriteriaResult(%+v)", *p)
}
