// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"offerwall_info_server"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  OwMediaInfoResponse getMediaInfo(OwRequestHeader requestHeader, i32 mid)")
	fmt.Fprintln(os.Stderr, "  OwMediaInfoResponse getAndroidMediaInfo(OwRequestHeader requestHeader, i32 mid)")
	fmt.Fprintln(os.Stderr, "  OwOfferInfoResponse getOffers(OwRequestHeader requestHeader, OwOfferInfoCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  OwOfferInfoResponse getSpecificOffers(OwRequestHeader requestHeader,  offerIds, bool onlineOnly)")
	fmt.Fprintln(os.Stderr, "  OwActedInfoResponse getActedInfo(OwRequestHeader requestHeader, Platform platform, OwDeviceInfo deviceInfo)")
	fmt.Fprintln(os.Stderr, "  OwUserIdResponse getOwUserId(OwRequestHeader requestHeader, Platform platform, OwDeviceInfo deviceInfo, string userId, i32 mid)")
	fmt.Fprintln(os.Stderr, "  OwPointResponse checkPointByOwUserId(OwRequestHeader requestHeader, string owUserId)")
	fmt.Fprintln(os.Stderr, "  OwPointResponse checkPointByDetail(OwRequestHeader requestHeader, Platform platform, OwDeviceInfo deviceInfo, string userId, i32 mid)")
	fmt.Fprintln(os.Stderr, "  OwPointResponse consumePoint(OwRequestHeader requestHeader, Platform platform, OwDeviceInfo deviceInfo, string userId, i32 mid, i32 uid, string orderId, i64 consumeNum)")
	fmt.Fprintln(os.Stderr, "  OwTaskStatusResponse getTaskStatusList(OwRequestHeader requestHeader, Platform platform, OwDeviceInfo deviceInfo, string userId, i32 mid)")
	fmt.Fprintln(os.Stderr, "  OwTaskStatusResponse getAllTaskStatusList(OwRequestHeader requestHeader, Platform platform, OwDeviceInfo deviceInfo)")
	fmt.Fprintln(os.Stderr, "  OwAppealResponse appeal(OwRequestHeader requestHeader, OwAppealRequest requestData)")
	fmt.Fprintln(os.Stderr, "  OwPointResponse checkBundlePoint(OwRequestHeader requestHeader, i64 dmUid)")
	fmt.Fprintln(os.Stderr, "  OwPointResponse consumeBundlePoint(OwRequestHeader requestHeader, i64 dmUid, string orderId, i64 consumeNum)")
	fmt.Fprintln(os.Stderr, "  OwIdListResponse getPlanIDByCampaignID(OwRequestHeader reqHeader, i32 campaignID)")
	fmt.Fprintln(os.Stderr, "  OwPlanInfoResponse getPlanInfoByPlanIDs(OwRequestHeader reqHeader,  planIDs)")
	fmt.Fprintln(os.Stderr, "  OwAdPlanStatDataResponse getStatData(OwRequestHeader reqHeader, i32 dt, byte hr)")
	fmt.Fprintln(os.Stderr, "  OwAdPlanStatDataResponse getStatsDataByCriteria(OwRequestHeader reqHeader, OwStatsCriteria criteria)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := offerwall_info_server.NewInfoServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getMediaInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaInfo requires 2 args")
			flag.Usage()
		}
		arg86 := flag.Arg(1)
		mbTrans87 := thrift.NewTMemoryBufferLen(len(arg86))
		defer mbTrans87.Close()
		_, err88 := mbTrans87.WriteString(arg86)
		if err88 != nil {
			Usage()
			return
		}
		factory89 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt90 := factory89.GetProtocol(mbTrans87)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err91 := argvalue0.Read(jsProt90)
		if err91 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err92 := (strconv.Atoi(flag.Arg(2)))
		if err92 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetMediaInfo(value0, value1))
		fmt.Print("\n")
		break
	case "getAndroidMediaInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAndroidMediaInfo requires 2 args")
			flag.Usage()
		}
		arg93 := flag.Arg(1)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err98 := argvalue0.Read(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err99 := (strconv.Atoi(flag.Arg(2)))
		if err99 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetAndroidMediaInfo(value0, value1))
		fmt.Print("\n")
		break
	case "getOffers":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOffers requires 2 args")
			flag.Usage()
		}
		arg100 := flag.Arg(1)
		mbTrans101 := thrift.NewTMemoryBufferLen(len(arg100))
		defer mbTrans101.Close()
		_, err102 := mbTrans101.WriteString(arg100)
		if err102 != nil {
			Usage()
			return
		}
		factory103 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt104 := factory103.GetProtocol(mbTrans101)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err105 := argvalue0.Read(jsProt104)
		if err105 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg106 := flag.Arg(2)
		mbTrans107 := thrift.NewTMemoryBufferLen(len(arg106))
		defer mbTrans107.Close()
		_, err108 := mbTrans107.WriteString(arg106)
		if err108 != nil {
			Usage()
			return
		}
		factory109 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt110 := factory109.GetProtocol(mbTrans107)
		argvalue1 := offerwall_info_server.NewOwOfferInfoCriteria()
		err111 := argvalue1.Read(jsProt110)
		if err111 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetOffers(value0, value1))
		fmt.Print("\n")
		break
	case "getSpecificOffers":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetSpecificOffers requires 3 args")
			flag.Usage()
		}
		arg112 := flag.Arg(1)
		mbTrans113 := thrift.NewTMemoryBufferLen(len(arg112))
		defer mbTrans113.Close()
		_, err114 := mbTrans113.WriteString(arg112)
		if err114 != nil {
			Usage()
			return
		}
		factory115 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt116 := factory115.GetProtocol(mbTrans113)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err117 := argvalue0.Read(jsProt116)
		if err117 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg118 := flag.Arg(2)
		mbTrans119 := thrift.NewTMemoryBufferLen(len(arg118))
		defer mbTrans119.Close()
		_, err120 := mbTrans119.WriteString(arg118)
		if err120 != nil {
			Usage()
			return
		}
		factory121 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt122 := factory121.GetProtocol(mbTrans119)
		containerStruct1 := offerwall_info_server.NewGetSpecificOffersArgs()
		err123 := containerStruct1.ReadField2(jsProt122)
		if err123 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.OfferIds
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.GetSpecificOffers(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getActedInfo":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetActedInfo requires 3 args")
			flag.Usage()
		}
		arg125 := flag.Arg(1)
		mbTrans126 := thrift.NewTMemoryBufferLen(len(arg125))
		defer mbTrans126.Close()
		_, err127 := mbTrans126.WriteString(arg125)
		if err127 != nil {
			Usage()
			return
		}
		factory128 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt129 := factory128.GetProtocol(mbTrans126)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err130 := argvalue0.Read(jsProt129)
		if err130 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := offerwall_info_server.Platform(tmp1)
		value1 := offerwall_info_server.Platform(argvalue1)
		arg131 := flag.Arg(3)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue2 := offerwall_info_server.NewOwDeviceInfo()
		err136 := argvalue2.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetActedInfo(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getOwUserId":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetOwUserId requires 5 args")
			flag.Usage()
		}
		arg137 := flag.Arg(1)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err142 := argvalue0.Read(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := offerwall_info_server.Platform(tmp1)
		value1 := offerwall_info_server.Platform(argvalue1)
		arg143 := flag.Arg(3)
		mbTrans144 := thrift.NewTMemoryBufferLen(len(arg143))
		defer mbTrans144.Close()
		_, err145 := mbTrans144.WriteString(arg143)
		if err145 != nil {
			Usage()
			return
		}
		factory146 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt147 := factory146.GetProtocol(mbTrans144)
		argvalue2 := offerwall_info_server.NewOwDeviceInfo()
		err148 := argvalue2.Read(jsProt147)
		if err148 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err150 := (strconv.Atoi(flag.Arg(5)))
		if err150 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetOwUserId(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "checkPointByOwUserId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CheckPointByOwUserId requires 2 args")
			flag.Usage()
		}
		arg151 := flag.Arg(1)
		mbTrans152 := thrift.NewTMemoryBufferLen(len(arg151))
		defer mbTrans152.Close()
		_, err153 := mbTrans152.WriteString(arg151)
		if err153 != nil {
			Usage()
			return
		}
		factory154 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt155 := factory154.GetProtocol(mbTrans152)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err156 := argvalue0.Read(jsProt155)
		if err156 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.CheckPointByOwUserId(value0, value1))
		fmt.Print("\n")
		break
	case "checkPointByDetail":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "CheckPointByDetail requires 5 args")
			flag.Usage()
		}
		arg158 := flag.Arg(1)
		mbTrans159 := thrift.NewTMemoryBufferLen(len(arg158))
		defer mbTrans159.Close()
		_, err160 := mbTrans159.WriteString(arg158)
		if err160 != nil {
			Usage()
			return
		}
		factory161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt162 := factory161.GetProtocol(mbTrans159)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err163 := argvalue0.Read(jsProt162)
		if err163 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := offerwall_info_server.Platform(tmp1)
		value1 := offerwall_info_server.Platform(argvalue1)
		arg164 := flag.Arg(3)
		mbTrans165 := thrift.NewTMemoryBufferLen(len(arg164))
		defer mbTrans165.Close()
		_, err166 := mbTrans165.WriteString(arg164)
		if err166 != nil {
			Usage()
			return
		}
		factory167 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt168 := factory167.GetProtocol(mbTrans165)
		argvalue2 := offerwall_info_server.NewOwDeviceInfo()
		err169 := argvalue2.Read(jsProt168)
		if err169 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err171 := (strconv.Atoi(flag.Arg(5)))
		if err171 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.CheckPointByDetail(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "consumePoint":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "ConsumePoint requires 8 args")
			flag.Usage()
		}
		arg172 := flag.Arg(1)
		mbTrans173 := thrift.NewTMemoryBufferLen(len(arg172))
		defer mbTrans173.Close()
		_, err174 := mbTrans173.WriteString(arg172)
		if err174 != nil {
			Usage()
			return
		}
		factory175 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt176 := factory175.GetProtocol(mbTrans173)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err177 := argvalue0.Read(jsProt176)
		if err177 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := offerwall_info_server.Platform(tmp1)
		value1 := offerwall_info_server.Platform(argvalue1)
		arg178 := flag.Arg(3)
		mbTrans179 := thrift.NewTMemoryBufferLen(len(arg178))
		defer mbTrans179.Close()
		_, err180 := mbTrans179.WriteString(arg178)
		if err180 != nil {
			Usage()
			return
		}
		factory181 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt182 := factory181.GetProtocol(mbTrans179)
		argvalue2 := offerwall_info_server.NewOwDeviceInfo()
		err183 := argvalue2.Read(jsProt182)
		if err183 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err185 := (strconv.Atoi(flag.Arg(5)))
		if err185 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err186 := (strconv.Atoi(flag.Arg(6)))
		if err186 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		argvalue7, err188 := (strconv.ParseInt(flag.Arg(8), 10, 64))
		if err188 != nil {
			Usage()
			return
		}
		value7 := argvalue7
		fmt.Print(client.ConsumePoint(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getTaskStatusList":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetTaskStatusList requires 5 args")
			flag.Usage()
		}
		arg189 := flag.Arg(1)
		mbTrans190 := thrift.NewTMemoryBufferLen(len(arg189))
		defer mbTrans190.Close()
		_, err191 := mbTrans190.WriteString(arg189)
		if err191 != nil {
			Usage()
			return
		}
		factory192 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt193 := factory192.GetProtocol(mbTrans190)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err194 := argvalue0.Read(jsProt193)
		if err194 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := offerwall_info_server.Platform(tmp1)
		value1 := offerwall_info_server.Platform(argvalue1)
		arg195 := flag.Arg(3)
		mbTrans196 := thrift.NewTMemoryBufferLen(len(arg195))
		defer mbTrans196.Close()
		_, err197 := mbTrans196.WriteString(arg195)
		if err197 != nil {
			Usage()
			return
		}
		factory198 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt199 := factory198.GetProtocol(mbTrans196)
		argvalue2 := offerwall_info_server.NewOwDeviceInfo()
		err200 := argvalue2.Read(jsProt199)
		if err200 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err202 := (strconv.Atoi(flag.Arg(5)))
		if err202 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetTaskStatusList(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getAllTaskStatusList":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAllTaskStatusList requires 3 args")
			flag.Usage()
		}
		arg203 := flag.Arg(1)
		mbTrans204 := thrift.NewTMemoryBufferLen(len(arg203))
		defer mbTrans204.Close()
		_, err205 := mbTrans204.WriteString(arg203)
		if err205 != nil {
			Usage()
			return
		}
		factory206 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt207 := factory206.GetProtocol(mbTrans204)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err208 := argvalue0.Read(jsProt207)
		if err208 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := offerwall_info_server.Platform(tmp1)
		value1 := offerwall_info_server.Platform(argvalue1)
		arg209 := flag.Arg(3)
		mbTrans210 := thrift.NewTMemoryBufferLen(len(arg209))
		defer mbTrans210.Close()
		_, err211 := mbTrans210.WriteString(arg209)
		if err211 != nil {
			Usage()
			return
		}
		factory212 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt213 := factory212.GetProtocol(mbTrans210)
		argvalue2 := offerwall_info_server.NewOwDeviceInfo()
		err214 := argvalue2.Read(jsProt213)
		if err214 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetAllTaskStatusList(value0, value1, value2))
		fmt.Print("\n")
		break
	case "appeal":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "Appeal requires 2 args")
			flag.Usage()
		}
		arg215 := flag.Arg(1)
		mbTrans216 := thrift.NewTMemoryBufferLen(len(arg215))
		defer mbTrans216.Close()
		_, err217 := mbTrans216.WriteString(arg215)
		if err217 != nil {
			Usage()
			return
		}
		factory218 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt219 := factory218.GetProtocol(mbTrans216)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err220 := argvalue0.Read(jsProt219)
		if err220 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg221 := flag.Arg(2)
		mbTrans222 := thrift.NewTMemoryBufferLen(len(arg221))
		defer mbTrans222.Close()
		_, err223 := mbTrans222.WriteString(arg221)
		if err223 != nil {
			Usage()
			return
		}
		factory224 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt225 := factory224.GetProtocol(mbTrans222)
		argvalue1 := offerwall_info_server.NewOwAppealRequest()
		err226 := argvalue1.Read(jsProt225)
		if err226 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.Appeal(value0, value1))
		fmt.Print("\n")
		break
	case "checkBundlePoint":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CheckBundlePoint requires 2 args")
			flag.Usage()
		}
		arg227 := flag.Arg(1)
		mbTrans228 := thrift.NewTMemoryBufferLen(len(arg227))
		defer mbTrans228.Close()
		_, err229 := mbTrans228.WriteString(arg227)
		if err229 != nil {
			Usage()
			return
		}
		factory230 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt231 := factory230.GetProtocol(mbTrans228)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err232 := argvalue0.Read(jsProt231)
		if err232 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err233 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err233 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.CheckBundlePoint(value0, value1))
		fmt.Print("\n")
		break
	case "consumeBundlePoint":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ConsumeBundlePoint requires 4 args")
			flag.Usage()
		}
		arg234 := flag.Arg(1)
		mbTrans235 := thrift.NewTMemoryBufferLen(len(arg234))
		defer mbTrans235.Close()
		_, err236 := mbTrans235.WriteString(arg234)
		if err236 != nil {
			Usage()
			return
		}
		factory237 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt238 := factory237.GetProtocol(mbTrans235)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err239 := argvalue0.Read(jsProt238)
		if err239 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err240 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err240 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3, err242 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err242 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.ConsumeBundlePoint(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getPlanIDByCampaignID":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlanIDByCampaignID requires 2 args")
			flag.Usage()
		}
		arg243 := flag.Arg(1)
		mbTrans244 := thrift.NewTMemoryBufferLen(len(arg243))
		defer mbTrans244.Close()
		_, err245 := mbTrans244.WriteString(arg243)
		if err245 != nil {
			Usage()
			return
		}
		factory246 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt247 := factory246.GetProtocol(mbTrans244)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err248 := argvalue0.Read(jsProt247)
		if err248 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err249 := (strconv.Atoi(flag.Arg(2)))
		if err249 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetPlanIDByCampaignID(value0, value1))
		fmt.Print("\n")
		break
	case "getPlanInfoByPlanIDs":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlanInfoByPlanIDs requires 2 args")
			flag.Usage()
		}
		arg250 := flag.Arg(1)
		mbTrans251 := thrift.NewTMemoryBufferLen(len(arg250))
		defer mbTrans251.Close()
		_, err252 := mbTrans251.WriteString(arg250)
		if err252 != nil {
			Usage()
			return
		}
		factory253 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt254 := factory253.GetProtocol(mbTrans251)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err255 := argvalue0.Read(jsProt254)
		if err255 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg256 := flag.Arg(2)
		mbTrans257 := thrift.NewTMemoryBufferLen(len(arg256))
		defer mbTrans257.Close()
		_, err258 := mbTrans257.WriteString(arg256)
		if err258 != nil {
			Usage()
			return
		}
		factory259 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt260 := factory259.GetProtocol(mbTrans257)
		containerStruct1 := offerwall_info_server.NewGetPlanInfoByPlanIDsArgs()
		err261 := containerStruct1.ReadField2(jsProt260)
		if err261 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.PlanIDs
		value1 := argvalue1
		fmt.Print(client.GetPlanInfoByPlanIDs(value0, value1))
		fmt.Print("\n")
		break
	case "getStatData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetStatData requires 3 args")
			flag.Usage()
		}
		arg262 := flag.Arg(1)
		mbTrans263 := thrift.NewTMemoryBufferLen(len(arg262))
		defer mbTrans263.Close()
		_, err264 := mbTrans263.WriteString(arg262)
		if err264 != nil {
			Usage()
			return
		}
		factory265 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt266 := factory265.GetProtocol(mbTrans263)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err267 := argvalue0.Read(jsProt266)
		if err267 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err268 := (strconv.Atoi(flag.Arg(2)))
		if err268 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err269 := (strconv.Atoi(flag.Arg(3)))
		if err269 != nil {
			Usage()
			return
		}
		argvalue2 := byte(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetStatData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getStatsDataByCriteria":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetStatsDataByCriteria requires 2 args")
			flag.Usage()
		}
		arg270 := flag.Arg(1)
		mbTrans271 := thrift.NewTMemoryBufferLen(len(arg270))
		defer mbTrans271.Close()
		_, err272 := mbTrans271.WriteString(arg270)
		if err272 != nil {
			Usage()
			return
		}
		factory273 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt274 := factory273.GetProtocol(mbTrans271)
		argvalue0 := offerwall_info_server.NewOwRequestHeader()
		err275 := argvalue0.Read(jsProt274)
		if err275 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg276 := flag.Arg(2)
		mbTrans277 := thrift.NewTMemoryBufferLen(len(arg276))
		defer mbTrans277.Close()
		_, err278 := mbTrans277.WriteString(arg276)
		if err278 != nil {
			Usage()
			return
		}
		factory279 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt280 := factory279.GetProtocol(mbTrans277)
		argvalue1 := offerwall_info_server.NewOwStatsCriteria()
		err281 := argvalue1.Read(jsProt280)
		if err281 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetStatsDataByCriteria(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
