// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package offerwall_info_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/offerwall_info_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = offerwall_info_types.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//消费请求处理状态
type OwConsumeStatus int64

const (
	OwConsumeStatus_NOT_A_CONSUME      OwConsumeStatus = 0
	OwConsumeStatus_CONSUME_SUCCESS    OwConsumeStatus = 1
	OwConsumeStatus_INSUFFICIENT_POINT OwConsumeStatus = 2
	OwConsumeStatus_REDUPLICATE_ORDER  OwConsumeStatus = 3
	OwConsumeStatus_REQUEST_ERROR      OwConsumeStatus = 4
	OwConsumeStatus_SERVER_ERROR       OwConsumeStatus = 5
)

func (p OwConsumeStatus) String() string {
	switch p {
	case OwConsumeStatus_NOT_A_CONSUME:
		return "OwConsumeStatus_NOT_A_CONSUME"
	case OwConsumeStatus_CONSUME_SUCCESS:
		return "OwConsumeStatus_CONSUME_SUCCESS"
	case OwConsumeStatus_INSUFFICIENT_POINT:
		return "OwConsumeStatus_INSUFFICIENT_POINT"
	case OwConsumeStatus_REDUPLICATE_ORDER:
		return "OwConsumeStatus_REDUPLICATE_ORDER"
	case OwConsumeStatus_REQUEST_ERROR:
		return "OwConsumeStatus_REQUEST_ERROR"
	case OwConsumeStatus_SERVER_ERROR:
		return "OwConsumeStatus_SERVER_ERROR"
	}
	return "<UNSET>"
}

func OwConsumeStatusFromString(s string) (OwConsumeStatus, error) {
	switch s {
	case "OwConsumeStatus_NOT_A_CONSUME":
		return OwConsumeStatus_NOT_A_CONSUME, nil
	case "OwConsumeStatus_CONSUME_SUCCESS":
		return OwConsumeStatus_CONSUME_SUCCESS, nil
	case "OwConsumeStatus_INSUFFICIENT_POINT":
		return OwConsumeStatus_INSUFFICIENT_POINT, nil
	case "OwConsumeStatus_REDUPLICATE_ORDER":
		return OwConsumeStatus_REDUPLICATE_ORDER, nil
	case "OwConsumeStatus_REQUEST_ERROR":
		return OwConsumeStatus_REQUEST_ERROR, nil
	case "OwConsumeStatus_SERVER_ERROR":
		return OwConsumeStatus_SERVER_ERROR, nil
	}
	return OwConsumeStatus(math.MinInt32 - 1), fmt.Errorf("not a valid OwConsumeStatus string")
}

//积分墙广告积分状态
type OwOfferPointStatus int64

const (
	OwOfferPointStatus_ALL       OwOfferPointStatus = 0
	OwOfferPointStatus_HAS_POINT OwOfferPointStatus = 1
	OwOfferPointStatus_NO_POINT  OwOfferPointStatus = 2
)

func (p OwOfferPointStatus) String() string {
	switch p {
	case OwOfferPointStatus_ALL:
		return "OwOfferPointStatus_ALL"
	case OwOfferPointStatus_HAS_POINT:
		return "OwOfferPointStatus_HAS_POINT"
	case OwOfferPointStatus_NO_POINT:
		return "OwOfferPointStatus_NO_POINT"
	}
	return "<UNSET>"
}

func OwOfferPointStatusFromString(s string) (OwOfferPointStatus, error) {
	switch s {
	case "OwOfferPointStatus_ALL":
		return OwOfferPointStatus_ALL, nil
	case "OwOfferPointStatus_HAS_POINT":
		return OwOfferPointStatus_HAS_POINT, nil
	case "OwOfferPointStatus_NO_POINT":
		return OwOfferPointStatus_NO_POINT, nil
	}
	return OwOfferPointStatus(math.MinInt32 - 1), fmt.Errorf("not a valid OwOfferPointStatus string")
}

type OwErrorCode int64

const (
	OwErrorCode_COMMON_REQ_PARAMS_ERROR OwErrorCode = 1
	OwErrorCode_COMMON_SERVER_ERROR     OwErrorCode = 2
)

func (p OwErrorCode) String() string {
	switch p {
	case OwErrorCode_COMMON_REQ_PARAMS_ERROR:
		return "OwErrorCode_COMMON_REQ_PARAMS_ERROR"
	case OwErrorCode_COMMON_SERVER_ERROR:
		return "OwErrorCode_COMMON_SERVER_ERROR"
	}
	return "<UNSET>"
}

func OwErrorCodeFromString(s string) (OwErrorCode, error) {
	switch s {
	case "OwErrorCode_COMMON_REQ_PARAMS_ERROR":
		return OwErrorCode_COMMON_REQ_PARAMS_ERROR, nil
	case "OwErrorCode_COMMON_SERVER_ERROR":
		return OwErrorCode_COMMON_SERVER_ERROR, nil
	}
	return OwErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid OwErrorCode string")
}

type OwMediaInfo *offerwall_info_types.OwMediaInfo

type OwOffer *offerwall_info_types.OwOffer

type Platform offerwall_info_types.Platform

type OwRequestHeader struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
}

func NewOwRequestHeader() *OwRequestHeader {
	return &OwRequestHeader{}
}

func (p *OwRequestHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwRequestHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwRequestHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwRequestHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwRequestHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwRequestHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwRequestHeader(%+v)", *p)
}

type OwResponseHeader struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
	// unused field # 2
	ErrorCode    OwErrorCode `thrift:"errorCode,3" json:"errorCode"`
	ErrorMessage string      `thrift:"errorMessage,4" json:"errorMessage"`
}

func NewOwResponseHeader() *OwResponseHeader {
	return &OwResponseHeader{
		ErrorCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwResponseHeader) IsSetErrorCode() bool {
	return int64(p.ErrorCode) != math.MinInt32-1
}

func (p *OwResponseHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwResponseHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwResponseHeader) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ErrorCode = OwErrorCode(v)
	}
	return nil
}

func (p *OwResponseHeader) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ErrorMessage = v
	}
	return nil
}

func (p *OwResponseHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwResponseHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwResponseHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwResponseHeader) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrorCode() {
		if err := oprot.WriteFieldBegin("errorCode", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:errorCode: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ErrorCode)); err != nil {
			return fmt.Errorf("%T.errorCode (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:errorCode: %s", p, err)
		}
	}
	return err
}

func (p *OwResponseHeader) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("errorMessage", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:errorMessage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrorMessage)); err != nil {
		return fmt.Errorf("%T.errorMessage (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:errorMessage: %s", p, err)
	}
	return err
}

func (p *OwResponseHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwResponseHeader(%+v)", *p)
}

type OwDeviceInfo struct {
	Mac       string `thrift:"mac,1" json:"mac"`
	Macmd5    string `thrift:"macmd5,2" json:"macmd5"`
	Idfa      string `thrift:"idfa,3" json:"idfa"`
	Oid       string `thrift:"oid,4" json:"oid"`
	Udid      string `thrift:"udid,5" json:"udid"`
	Imei      string `thrift:"imei,6" json:"imei"`
	AndroidId string `thrift:"androidId,7" json:"androidId"`
	Odin1     string `thrift:"odin1,8" json:"odin1"`
}

func NewOwDeviceInfo() *OwDeviceInfo {
	return &OwDeviceInfo{}
}

func (p *OwDeviceInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwDeviceInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mac = v
	}
	return nil
}

func (p *OwDeviceInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *OwDeviceInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *OwDeviceInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *OwDeviceInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Udid = v
	}
	return nil
}

func (p *OwDeviceInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *OwDeviceInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *OwDeviceInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *OwDeviceInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwDeviceInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwDeviceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mac", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mac)); err != nil {
		return fmt.Errorf("%T.mac (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mac: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:macmd5: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:idfa: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:oid: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("udid", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:udid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Udid)); err != nil {
		return fmt.Errorf("%T.udid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:udid: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:imei: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("androidId", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:androidId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.androidId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:androidId: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:odin1: %s", p, err)
	}
	return err
}

func (p *OwDeviceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwDeviceInfo(%+v)", *p)
}

type OwMediaInfoResponse struct {
	SearchId  int64                             `thrift:"searchId,1" json:"searchId"`
	MediaInfo *offerwall_info_types.OwMediaInfo `thrift:"mediaInfo,2" json:"mediaInfo"`
}

func NewOwMediaInfoResponse() *OwMediaInfoResponse {
	return &OwMediaInfoResponse{}
}

func (p *OwMediaInfoResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwMediaInfoResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwMediaInfoResponse) readField2(iprot thrift.TProtocol) error {
	p.MediaInfo = offerwall_info_types.NewOwMediaInfo()
	if err := p.MediaInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.MediaInfo)
	}
	return nil
}

func (p *OwMediaInfoResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwMediaInfoResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwMediaInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwMediaInfoResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.MediaInfo != nil {
		if err := oprot.WriteFieldBegin("mediaInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:mediaInfo: %s", p, err)
		}
		if err := p.MediaInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.MediaInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:mediaInfo: %s", p, err)
		}
	}
	return err
}

func (p *OwMediaInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwMediaInfoResponse(%+v)", *p)
}

type OWMediaNotFoundException struct {
	ErrorCode int32  `thrift:"error_code,1" json:"error_code"`
	ErrorMsg  string `thrift:"error_msg,2" json:"error_msg"`
}

func NewOWMediaNotFoundException() *OWMediaNotFoundException {
	return &OWMediaNotFoundException{}
}

func (p *OWMediaNotFoundException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OWMediaNotFoundException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ErrorCode = v
	}
	return nil
}

func (p *OWMediaNotFoundException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ErrorMsg = v
	}
	return nil
}

func (p *OWMediaNotFoundException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OWMediaNotFoundException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OWMediaNotFoundException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("error_code", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:error_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ErrorCode)); err != nil {
		return fmt.Errorf("%T.error_code (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:error_code: %s", p, err)
	}
	return err
}

func (p *OWMediaNotFoundException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("error_msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:error_msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrorMsg)); err != nil {
		return fmt.Errorf("%T.error_msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:error_msg: %s", p, err)
	}
	return err
}

func (p *OWMediaNotFoundException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OWMediaNotFoundException(%+v)", *p)
}

type OwOfferInfoCriteria struct {
	Platform       Platform           `thrift:"platform,1" json:"platform"`
	OfferTypes     []int16            `thrift:"offerTypes,2" json:"offerTypes"`
	IsSr           bool               `thrift:"isSr,3" json:"isSr"`
	Mid            int32              `thrift:"mid,4" json:"mid"`
	Uid            int32              `thrift:"uid,5" json:"uid"`
	PointStatus    OwOfferPointStatus `thrift:"pointStatus,6" json:"pointStatus"`
	StartTimeRange int64              `thrift:"startTimeRange,7" json:"startTimeRange"`
}

func NewOwOfferInfoCriteria() *OwOfferInfoCriteria {
	return &OwOfferInfoCriteria{
		Platform: math.MinInt32 - 1, // unset sentinal value

		PointStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwOfferInfoCriteria) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *OwOfferInfoCriteria) IsSetPointStatus() bool {
	return int64(p.PointStatus) != math.MinInt32-1
}

func (p *OwOfferInfoCriteria) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwOfferInfoCriteria) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Platform = Platform(v)
	}
	return nil
}

func (p *OwOfferInfoCriteria) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OfferTypes = make([]int16, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int16
		if v, err := iprot.ReadI16(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.OfferTypes = append(p.OfferTypes, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwOfferInfoCriteria) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IsSr = v
	}
	return nil
}

func (p *OwOfferInfoCriteria) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *OwOfferInfoCriteria) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *OwOfferInfoCriteria) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PointStatus = OwOfferPointStatus(v)
	}
	return nil
}

func (p *OwOfferInfoCriteria) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.StartTimeRange = v
	}
	return nil
}

func (p *OwOfferInfoCriteria) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwOfferInfoCriteria"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwOfferInfoCriteria) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:platform: %s", p, err)
	}
	return err
}

func (p *OwOfferInfoCriteria) writeField2(oprot thrift.TProtocol) (err error) {
	if p.OfferTypes != nil {
		if err := oprot.WriteFieldBegin("offerTypes", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:offerTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I16, len(p.OfferTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OfferTypes {
			if err := oprot.WriteI16(int16(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:offerTypes: %s", p, err)
		}
	}
	return err
}

func (p *OwOfferInfoCriteria) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isSr", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:isSr: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsSr)); err != nil {
		return fmt.Errorf("%T.isSr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:isSr: %s", p, err)
	}
	return err
}

func (p *OwOfferInfoCriteria) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mid: %s", p, err)
	}
	return err
}

func (p *OwOfferInfoCriteria) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:uid: %s", p, err)
	}
	return err
}

func (p *OwOfferInfoCriteria) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetPointStatus() {
		if err := oprot.WriteFieldBegin("pointStatus", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:pointStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PointStatus)); err != nil {
			return fmt.Errorf("%T.pointStatus (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:pointStatus: %s", p, err)
		}
	}
	return err
}

func (p *OwOfferInfoCriteria) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTimeRange", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:startTimeRange: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTimeRange)); err != nil {
		return fmt.Errorf("%T.startTimeRange (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:startTimeRange: %s", p, err)
	}
	return err
}

func (p *OwOfferInfoCriteria) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwOfferInfoCriteria(%+v)", *p)
}

type OwOfferInfoResponse struct {
	SearchId int64                           `thrift:"searchId,1" json:"searchId"`
	Offers   []*offerwall_info_types.OwOffer `thrift:"offers,2" json:"offers"`
}

func NewOwOfferInfoResponse() *OwOfferInfoResponse {
	return &OwOfferInfoResponse{}
}

func (p *OwOfferInfoResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwOfferInfoResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwOfferInfoResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Offers = make([]*offerwall_info_types.OwOffer, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := offerwall_info_types.NewOwOffer()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Offers = append(p.Offers, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwOfferInfoResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwOfferInfoResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwOfferInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwOfferInfoResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Offers != nil {
		if err := oprot.WriteFieldBegin("offers", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:offers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Offers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Offers {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:offers: %s", p, err)
		}
	}
	return err
}

func (p *OwOfferInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwOfferInfoResponse(%+v)", *p)
}

type OwActedInfoResponse struct {
	SearchId int64 `thrift:"searchId,1" json:"searchId"`
	// unused field # 2
	Offers string `thrift:"offers,3" json:"offers"`
	Appids string `thrift:"appids,4" json:"appids"`
	Videos string `thrift:"videos,5" json:"videos"`
}

func NewOwActedInfoResponse() *OwActedInfoResponse {
	return &OwActedInfoResponse{}
}

func (p *OwActedInfoResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwActedInfoResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwActedInfoResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offers = v
	}
	return nil
}

func (p *OwActedInfoResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Appids = v
	}
	return nil
}

func (p *OwActedInfoResponse) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Videos = v
	}
	return nil
}

func (p *OwActedInfoResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwActedInfoResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwActedInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwActedInfoResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offers", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offers: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Offers)); err != nil {
		return fmt.Errorf("%T.offers (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offers: %s", p, err)
	}
	return err
}

func (p *OwActedInfoResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appids", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:appids: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appids)); err != nil {
		return fmt.Errorf("%T.appids (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:appids: %s", p, err)
	}
	return err
}

func (p *OwActedInfoResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("videos", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:videos: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Videos)); err != nil {
		return fmt.Errorf("%T.videos (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:videos: %s", p, err)
	}
	return err
}

func (p *OwActedInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwActedInfoResponse(%+v)", *p)
}

type OwUserIdResponse struct {
	SearchId int64  `thrift:"searchId,1" json:"searchId"`
	OwUserId string `thrift:"owUserId,2" json:"owUserId"`
}

func NewOwUserIdResponse() *OwUserIdResponse {
	return &OwUserIdResponse{}
}

func (p *OwUserIdResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwUserIdResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwUserIdResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OwUserId = v
	}
	return nil
}

func (p *OwUserIdResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwUserIdResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwUserIdResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwUserIdResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("owUserId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:owUserId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OwUserId)); err != nil {
		return fmt.Errorf("%T.owUserId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:owUserId: %s", p, err)
	}
	return err
}

func (p *OwUserIdResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwUserIdResponse(%+v)", *p)
}

type OwPointResponse struct {
	SearchId      int64           `thrift:"searchId,1" json:"searchId"`
	TotalPoint    int64           `thrift:"totalPoint,2" json:"totalPoint"`
	ConsumedPoint int64           `thrift:"consumedPoint,3" json:"consumedPoint"`
	ConsumeStatus OwConsumeStatus `thrift:"consumeStatus,4" json:"consumeStatus"`
}

func NewOwPointResponse() *OwPointResponse {
	return &OwPointResponse{
		ConsumeStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwPointResponse) IsSetConsumeStatus() bool {
	return int64(p.ConsumeStatus) != math.MinInt32-1
}

func (p *OwPointResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwPointResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwPointResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TotalPoint = v
	}
	return nil
}

func (p *OwPointResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ConsumedPoint = v
	}
	return nil
}

func (p *OwPointResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ConsumeStatus = OwConsumeStatus(v)
	}
	return nil
}

func (p *OwPointResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwPointResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwPointResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwPointResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalPoint", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:totalPoint: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalPoint)); err != nil {
		return fmt.Errorf("%T.totalPoint (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:totalPoint: %s", p, err)
	}
	return err
}

func (p *OwPointResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumedPoint", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:consumedPoint: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConsumedPoint)); err != nil {
		return fmt.Errorf("%T.consumedPoint (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:consumedPoint: %s", p, err)
	}
	return err
}

func (p *OwPointResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetConsumeStatus() {
		if err := oprot.WriteFieldBegin("consumeStatus", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:consumeStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ConsumeStatus)); err != nil {
			return fmt.Errorf("%T.consumeStatus (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:consumeStatus: %s", p, err)
		}
	}
	return err
}

func (p *OwPointResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwPointResponse(%+v)", *p)
}

type OwTaskStatusResponse struct {
	SearchId     int64             `thrift:"searchId,1" json:"searchId"`
	ClickRecords map[string]string `thrift:"clickRecords,2" json:"clickRecords"`
	ActRecords   map[string]string `thrift:"actRecords,3" json:"actRecords"`
}

func NewOwTaskStatusResponse() *OwTaskStatusResponse {
	return &OwTaskStatusResponse{}
}

func (p *OwTaskStatusResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwTaskStatusResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwTaskStatusResponse) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ClickRecords = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = v
		}
		var _val3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = v
		}
		p.ClickRecords[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *OwTaskStatusResponse) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ActRecords = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key4 = v
		}
		var _val5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val5 = v
		}
		p.ActRecords[_key4] = _val5
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *OwTaskStatusResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwTaskStatusResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwTaskStatusResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwTaskStatusResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ClickRecords != nil {
		if err := oprot.WriteFieldBegin("clickRecords", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:clickRecords: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ClickRecords)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ClickRecords {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:clickRecords: %s", p, err)
		}
	}
	return err
}

func (p *OwTaskStatusResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ActRecords != nil {
		if err := oprot.WriteFieldBegin("actRecords", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:actRecords: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ActRecords)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ActRecords {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:actRecords: %s", p, err)
		}
	}
	return err
}

func (p *OwTaskStatusResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwTaskStatusResponse(%+v)", *p)
}

type OwAppealRequest struct {
	Mid        common.IdInt `thrift:"mid,1" json:"mid"`
	Appid      string       `thrift:"appid,2" json:"appid"`
	AppName    string       `thrift:"appName,3" json:"appName"`
	Macmd5     string       `thrift:"macmd5,4" json:"macmd5"`
	Dmac       string       `thrift:"dmac,5" json:"dmac"`
	Idfa       string       `thrift:"idfa,6" json:"idfa"`
	Odin1      string       `thrift:"odin1,7" json:"odin1"`
	Uuid       string       `thrift:"uuid,8" json:"uuid"`
	Oid        string       `thrift:"oid,9" json:"oid"`
	UserId     string       `thrift:"userId,10" json:"userId"`
	Email      string       `thrift:"email,11" json:"email"`
	Sv         int32        `thrift:"sv,12" json:"sv"`
	Ip         string       `thrift:"ip,13" json:"ip"`
	RegionCode int32        `thrift:"regionCode,14" json:"regionCode"`
	DeviceCode int32        `thrift:"deviceCode,15" json:"deviceCode"`
	AccessCode int32        `thrift:"accessCode,16" json:"accessCode"`
}

func NewOwAppealRequest() *OwAppealRequest {
	return &OwAppealRequest{}
}

func (p *OwAppealRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAppealRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mid = common.IdInt(v)
	}
	return nil
}

func (p *OwAppealRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *OwAppealRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *OwAppealRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *OwAppealRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *OwAppealRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *OwAppealRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *OwAppealRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *OwAppealRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *OwAppealRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *OwAppealRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *OwAppealRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *OwAppealRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *OwAppealRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *OwAppealRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *OwAppealRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.AccessCode = v
	}
	return nil
}

func (p *OwAppealRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAppealRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAppealRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mid: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.appName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appName: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:macmd5: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:dmac: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:idfa: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:odin1: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:uuid: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:oid: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userId", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:userId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserId)); err != nil {
		return fmt.Errorf("%T.userId (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:userId: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:email: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sv: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ip: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regionCode", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:regionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.regionCode (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:regionCode: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceCode", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:deviceCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.deviceCode (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:deviceCode: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accessCode", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:accessCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.accessCode (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:accessCode: %s", p, err)
	}
	return err
}

func (p *OwAppealRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAppealRequest(%+v)", *p)
}

type OwAppealResponse struct {
	SearchId int64  `thrift:"searchId,1" json:"searchId"`
	Status   int32  `thrift:"status,2" json:"status"`
	Message  string `thrift:"message,3" json:"message"`
}

func NewOwAppealResponse() *OwAppealResponse {
	return &OwAppealResponse{}
}

func (p *OwAppealResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAppealResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *OwAppealResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *OwAppealResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *OwAppealResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAppealResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAppealResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *OwAppealResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:status: %s", p, err)
	}
	return err
}

func (p *OwAppealResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:message: %s", p, err)
	}
	return err
}

func (p *OwAppealResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAppealResponse(%+v)", *p)
}

type OwIdListResponse struct {
	RespHeader *OwResponseHeader `thrift:"respHeader,1" json:"respHeader"`
	Ids        []int32           `thrift:"ids,2" json:"ids"`
}

func NewOwIdListResponse() *OwIdListResponse {
	return &OwIdListResponse{}
}

func (p *OwIdListResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwIdListResponse) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwResponseHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwIdListResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.Ids = append(p.Ids, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwIdListResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwIdListResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwIdListResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwIdListResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *OwIdListResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwIdListResponse(%+v)", *p)
}

type OwPlanInfoResponse struct {
	RespHeader *OwResponseHeader                        `thrift:"respHeader,1" json:"respHeader"`
	PlanMap    map[int32]*offerwall_info_types.OwAdPlan `thrift:"planMap,2" json:"planMap"`
}

func NewOwPlanInfoResponse() *OwPlanInfoResponse {
	return &OwPlanInfoResponse{}
}

func (p *OwPlanInfoResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwPlanInfoResponse) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwResponseHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwPlanInfoResponse) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.PlanMap = make(map[int32]*offerwall_info_types.OwAdPlan, size)
	for i := 0; i < size; i++ {
		var _key7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key7 = v
		}
		_val8 := offerwall_info_types.NewOwAdPlan()
		if err := _val8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val8)
		}
		p.PlanMap[_key7] = _val8
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *OwPlanInfoResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwPlanInfoResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwPlanInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwPlanInfoResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.PlanMap != nil {
		if err := oprot.WriteFieldBegin("planMap", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:planMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.PlanMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.PlanMap {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:planMap: %s", p, err)
		}
	}
	return err
}

func (p *OwPlanInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwPlanInfoResponse(%+v)", *p)
}

type OwAdPlanStatDataResponse struct {
	RespHeader   *OwResponseHeader                        `thrift:"respHeader,1" json:"respHeader"`
	PlanStatList []*offerwall_info_types.OwAdPlanStatInfo `thrift:"planStatList,2" json:"planStatList"`
}

func NewOwAdPlanStatDataResponse() *OwAdPlanStatDataResponse {
	return &OwAdPlanStatDataResponse{}
}

func (p *OwAdPlanStatDataResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlanStatDataResponse) readField1(iprot thrift.TProtocol) error {
	p.RespHeader = NewOwResponseHeader()
	if err := p.RespHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RespHeader)
	}
	return nil
}

func (p *OwAdPlanStatDataResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlanStatList = make([]*offerwall_info_types.OwAdPlanStatInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := offerwall_info_types.NewOwAdPlanStatInfo()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.PlanStatList = append(p.PlanStatList, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OwAdPlanStatDataResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwAdPlanStatDataResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwAdPlanStatDataResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RespHeader != nil {
		if err := oprot.WriteFieldBegin("respHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:respHeader: %s", p, err)
		}
		if err := p.RespHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RespHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:respHeader: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlanStatDataResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.PlanStatList != nil {
		if err := oprot.WriteFieldBegin("planStatList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:planStatList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PlanStatList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlanStatList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:planStatList: %s", p, err)
		}
	}
	return err
}

func (p *OwAdPlanStatDataResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwAdPlanStatDataResponse(%+v)", *p)
}

type OwStatsCriteria struct {
	Dt          int32                            `thrift:"dt,1" json:"dt"`
	Hr          int8                             `thrift:"hr,2" json:"hr"`
	ChannelType offerwall_info_types.ChannelType `thrift:"channelType,3" json:"channelType"`
}

func NewOwStatsCriteria() *OwStatsCriteria {
	return &OwStatsCriteria{
		ChannelType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OwStatsCriteria) IsSetChannelType() bool {
	return int64(p.ChannelType) != math.MinInt32-1
}

func (p *OwStatsCriteria) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OwStatsCriteria) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *OwStatsCriteria) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = int8(v)
	}
	return nil
}

func (p *OwStatsCriteria) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ChannelType = offerwall_info_types.ChannelType(v)
	}
	return nil
}

func (p *OwStatsCriteria) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OwStatsCriteria"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OwStatsCriteria) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *OwStatsCriteria) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.BYTE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *OwStatsCriteria) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannelType() {
		if err := oprot.WriteFieldBegin("channelType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:channelType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
			return fmt.Errorf("%T.channelType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:channelType: %s", p, err)
		}
	}
	return err
}

func (p *OwStatsCriteria) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OwStatsCriteria(%+v)", *p)
}
