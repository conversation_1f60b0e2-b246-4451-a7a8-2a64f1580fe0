// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"eventidgenerator"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  EventIDResponse generateEventId(RequestHeader header, EventIDRequest request)")
	fmt.Fprintln(os.Stderr, "  EventIDResponse getLastEventId(RequestHeader header, EventIDRequest request)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := eventidgenerator.NewEventIDServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "generateEventId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GenerateEventId requires 2 args")
			flag.Usage()
		}
		arg12 := flag.Arg(1)
		mbTrans13 := thrift.NewTMemoryBufferLen(len(arg12))
		defer mbTrans13.Close()
		_, err14 := mbTrans13.WriteString(arg12)
		if err14 != nil {
			Usage()
			return
		}
		factory15 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt16 := factory15.GetProtocol(mbTrans13)
		argvalue0 := eventidgenerator.NewRequestHeader()
		err17 := argvalue0.Read(jsProt16)
		if err17 != nil {
			Usage()
			return
		}
		value0 := eventidgenerator.RequestHeader(argvalue0)
		arg18 := flag.Arg(2)
		mbTrans19 := thrift.NewTMemoryBufferLen(len(arg18))
		defer mbTrans19.Close()
		_, err20 := mbTrans19.WriteString(arg18)
		if err20 != nil {
			Usage()
			return
		}
		factory21 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt22 := factory21.GetProtocol(mbTrans19)
		argvalue1 := eventidgenerator.NewEventIDRequest()
		err23 := argvalue1.Read(jsProt22)
		if err23 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GenerateEventId(value0, value1))
		fmt.Print("\n")
		break
	case "getLastEventId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLastEventId requires 2 args")
			flag.Usage()
		}
		arg24 := flag.Arg(1)
		mbTrans25 := thrift.NewTMemoryBufferLen(len(arg24))
		defer mbTrans25.Close()
		_, err26 := mbTrans25.WriteString(arg24)
		if err26 != nil {
			Usage()
			return
		}
		factory27 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt28 := factory27.GetProtocol(mbTrans25)
		argvalue0 := eventidgenerator.NewRequestHeader()
		err29 := argvalue0.Read(jsProt28)
		if err29 != nil {
			Usage()
			return
		}
		value0 := eventidgenerator.RequestHeader(argvalue0)
		arg30 := flag.Arg(2)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue1 := eventidgenerator.NewEventIDRequest()
		err35 := argvalue1.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetLastEventId(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
