// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package eventidgenerator

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/event"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = event.GoUnusedProtection__
var ERROR_CLIENT_UNAUTHORIZED ErrorCode
var ERROR_CLIENT_INVALID_EVENT_CODE ErrorCode
var ERROR_CLIENT_SHOULD_NOT_SET_EID ErrorCode
var ERROR_SERVER_NOT_AVAILABLE ErrorCode
var ERROR_SERVER_BUSY ErrorCode
var ERROR_SERVER_IO_FAILED ErrorCode

func init() {
	ERROR_CLIENT_UNAUTHORIZED = 400

	ERROR_CLIENT_INVALID_EVENT_CODE = 401

	ERROR_CLIENT_SHOULD_NOT_SET_EID = 402

	ERROR_SERVER_NOT_AVAILABLE = 500

	ERROR_SERVER_BUSY = 501

	ERROR_SERVER_IO_FAILED = 502

}
