// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package eventidgenerator

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/event"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = event.GoUnusedProtection__

type EventIDService interface {
	// 生成id
	//
	// Parameters:
	//  - Header
	//  - Request
	GenerateEventId(header *common.RequestHeader, request *EventIDRequest) (r *EventIDResponse, ue *ServiceException, err error)
	// 检查用，获取 last id，而不生成新的 id
	//
	// Parameters:
	//  - Header
	//  - Request
	GetLastEventId(header *common.RequestHeader, request *EventIDRequest) (r *EventIDResponse, ue *ServiceException, err error)
}

type EventIDServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewEventIDServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *EventIDServiceClient {
	return &EventIDServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewEventIDServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *EventIDServiceClient {
	return &EventIDServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 生成id
//
// Parameters:
//  - Header
//  - Request
func (p *EventIDServiceClient) GenerateEventId(header *common.RequestHeader, request *EventIDRequest) (r *EventIDResponse, ue *ServiceException, err error) {
	if err = p.sendGenerateEventId(header, request); err != nil {
		return
	}
	return p.recvGenerateEventId()
}

func (p *EventIDServiceClient) sendGenerateEventId(header *common.RequestHeader, request *EventIDRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("generateEventId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args2 := NewGenerateEventIdArgs()
	args2.Header = header
	args2.Request = request
	if err = args2.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *EventIDServiceClient) recvGenerateEventId() (value *EventIDResponse, ue *ServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error4 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error5 error
		error5, err = error4.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error5
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result3 := NewGenerateEventIdResult()
	if err = result3.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result3.Success
	if result3.Ue != nil {
		ue = result3.Ue
	}
	return
}

// 检查用，获取 last id，而不生成新的 id
//
// Parameters:
//  - Header
//  - Request
func (p *EventIDServiceClient) GetLastEventId(header *common.RequestHeader, request *EventIDRequest) (r *EventIDResponse, ue *ServiceException, err error) {
	if err = p.sendGetLastEventId(header, request); err != nil {
		return
	}
	return p.recvGetLastEventId()
}

func (p *EventIDServiceClient) sendGetLastEventId(header *common.RequestHeader, request *EventIDRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getLastEventId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args6 := NewGetLastEventIdArgs()
	args6.Header = header
	args6.Request = request
	if err = args6.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *EventIDServiceClient) recvGetLastEventId() (value *EventIDResponse, ue *ServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error8 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error9 error
		error9, err = error8.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error9
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result7 := NewGetLastEventIdResult()
	if err = result7.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result7.Success
	if result7.Ue != nil {
		ue = result7.Ue
	}
	return
}

type EventIDServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      EventIDService
}

func (p *EventIDServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *EventIDServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *EventIDServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewEventIDServiceProcessor(handler EventIDService) *EventIDServiceProcessor {

	self10 := &EventIDServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self10.processorMap["generateEventId"] = &eventIDServiceProcessorGenerateEventId{handler: handler}
	self10.processorMap["getLastEventId"] = &eventIDServiceProcessorGetLastEventId{handler: handler}
	return self10
}

func (p *EventIDServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x11 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x11.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x11

}

type eventIDServiceProcessorGenerateEventId struct {
	handler EventIDService
}

func (p *eventIDServiceProcessorGenerateEventId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGenerateEventIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("generateEventId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGenerateEventIdResult()
	if result.Success, result.Ue, err = p.handler.GenerateEventId(args.Header, args.Request); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing generateEventId: "+err.Error())
		oprot.WriteMessageBegin("generateEventId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("generateEventId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type eventIDServiceProcessorGetLastEventId struct {
	handler EventIDService
}

func (p *eventIDServiceProcessorGetLastEventId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetLastEventIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getLastEventId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetLastEventIdResult()
	if result.Success, result.Ue, err = p.handler.GetLastEventId(args.Header, args.Request); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getLastEventId: "+err.Error())
		oprot.WriteMessageBegin("getLastEventId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getLastEventId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GenerateEventIdArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	Request *EventIDRequest       `thrift:"request,2" json:"request"`
}

func NewGenerateEventIdArgs() *GenerateEventIdArgs {
	return &GenerateEventIdArgs{}
}

func (p *GenerateEventIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GenerateEventIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GenerateEventIdArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = NewEventIDRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *GenerateEventIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("generateEventId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GenerateEventIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GenerateEventIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *GenerateEventIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GenerateEventIdArgs(%+v)", *p)
}

type GenerateEventIdResult struct {
	Success *EventIDResponse  `thrift:"success,0" json:"success"`
	Ue      *ServiceException `thrift:"ue,1" json:"ue"`
}

func NewGenerateEventIdResult() *GenerateEventIdResult {
	return &GenerateEventIdResult{}
}

func (p *GenerateEventIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GenerateEventIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewEventIDResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GenerateEventIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewServiceException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GenerateEventIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("generateEventId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GenerateEventIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GenerateEventIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GenerateEventIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GenerateEventIdResult(%+v)", *p)
}

type GetLastEventIdArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	Request *EventIDRequest       `thrift:"request,2" json:"request"`
}

func NewGetLastEventIdArgs() *GetLastEventIdArgs {
	return &GetLastEventIdArgs{}
}

func (p *GetLastEventIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLastEventIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetLastEventIdArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = NewEventIDRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *GetLastEventIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLastEventId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLastEventIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetLastEventIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *GetLastEventIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLastEventIdArgs(%+v)", *p)
}

type GetLastEventIdResult struct {
	Success *EventIDResponse  `thrift:"success,0" json:"success"`
	Ue      *ServiceException `thrift:"ue,1" json:"ue"`
}

func NewGetLastEventIdResult() *GetLastEventIdResult {
	return &GetLastEventIdResult{}
}

func (p *GetLastEventIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLastEventIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewEventIDResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetLastEventIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ue = NewServiceException()
	if err := p.Ue.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ue)
	}
	return nil
}

func (p *GetLastEventIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLastEventId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ue != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLastEventIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetLastEventIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ue != nil {
		if err := oprot.WriteFieldBegin("ue", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ue: %s", p, err)
		}
		if err := p.Ue.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ue)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ue: %s", p, err)
		}
	}
	return err
}

func (p *GetLastEventIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLastEventIdResult(%+v)", *p)
}
