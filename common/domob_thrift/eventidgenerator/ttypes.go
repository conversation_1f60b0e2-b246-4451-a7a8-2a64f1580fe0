// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package eventidgenerator

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/event"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = event.GoUnusedProtection__
var GoUnusedProtection__ int

type ErrorCode common.ErrorCode

type RequestHeader *common.RequestHeader

type TimeInt common.TimeInt

type EventHeader *event.EventHeader

type EventID event.EventID

type EventIDRequest struct {
	EventHeader     *event.EventHeader `thrift:"eventHeader,1" json:"eventHeader"`
	EventProperties map[string]string  `thrift:"eventProperties,2" json:"eventProperties"`
}

func NewEventIDRequest() *EventIDRequest {
	return &EventIDRequest{}
}

func (p *EventIDRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EventIDRequest) readField1(iprot thrift.TProtocol) error {
	p.EventHeader = event.NewEventHeader()
	if err := p.EventHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.EventHeader)
	}
	return nil
}

func (p *EventIDRequest) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.EventProperties = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.EventProperties[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *EventIDRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("EventIDRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EventIDRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.EventHeader != nil {
		if err := oprot.WriteFieldBegin("eventHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:eventHeader: %s", p, err)
		}
		if err := p.EventHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.EventHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:eventHeader: %s", p, err)
		}
	}
	return err
}

func (p *EventIDRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.EventProperties != nil {
		if err := oprot.WriteFieldBegin("eventProperties", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:eventProperties: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.EventProperties)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.EventProperties {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:eventProperties: %s", p, err)
		}
	}
	return err
}

func (p *EventIDRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventIDRequest(%+v)", *p)
}

type EventIDResponse struct {
	EventHeader *event.EventHeader `thrift:"eventHeader,1" json:"eventHeader"`
	Timestamp   TimeInt            `thrift:"timestamp,2" json:"timestamp"`
}

func NewEventIDResponse() *EventIDResponse {
	return &EventIDResponse{}
}

func (p *EventIDResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EventIDResponse) readField1(iprot thrift.TProtocol) error {
	p.EventHeader = event.NewEventHeader()
	if err := p.EventHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.EventHeader)
	}
	return nil
}

func (p *EventIDResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Timestamp = TimeInt(v)
	}
	return nil
}

func (p *EventIDResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("EventIDResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EventIDResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.EventHeader != nil {
		if err := oprot.WriteFieldBegin("eventHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:eventHeader: %s", p, err)
		}
		if err := p.EventHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.EventHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:eventHeader: %s", p, err)
		}
	}
	return err
}

func (p *EventIDResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:timestamp: %s", p, err)
	}
	return err
}

func (p *EventIDResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventIDResponse(%+v)", *p)
}

type ServiceException struct {
	Code    ErrorCode `thrift:"code,1" json:"code"`
	Message string    `thrift:"message,2" json:"message"`
}

func NewServiceException() *ServiceException {
	return &ServiceException{}
}

func (p *ServiceException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ServiceException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ErrorCode(v)
	}
	return nil
}

func (p *ServiceException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *ServiceException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ServiceException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ServiceException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return fmt.Errorf("%T.code (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:code: %s", p, err)
	}
	return err
}

func (p *ServiceException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *ServiceException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ServiceException(%+v)", *p)
}
