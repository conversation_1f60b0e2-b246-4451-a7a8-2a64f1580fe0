// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"airport_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  ResultString getFileInfoNoticedAirportServer(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool noticeFileInfoCipher(RequestHeader requestHeader, string cipherText)")
	fmt.Fprintln(os.St<PERSON>r, "  ResultSignal terminalServerBeatHeart(RequestHeader requestHeader, i32 port)")
	fmt.Fprintln(os.Stderr, "  ResultI32List whichTerminalServer(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool terminalServerSayGoodbye(RequestHeader requestHeader, i32 port)")
	fmt.Fprintln(os.Stderr, "  ResultAirportInfoList getOtherAirportServer(RequestHeader requestHeader)")
	fmt.Fprintln(os.Stderr, "  ResultAirportInfoList getDownloadableAirportServer(RequestHeader requestHeader)")
	fmt.Fprintln(os.Stderr, "  ResultBool noticeFinishFile(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool noticeCancelFile(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultTopicInfoList getFilePendingTopics(RequestHeader requestHeader)")
	fmt.Fprintln(os.Stderr, "  ResultString getFinishedFileInfoAirportServer(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool addFile(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := airport_server.NewAirportServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getFileInfoNoticedAirportServer":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFileInfoNoticedAirportServer requires 2 args")
			flag.Usage()
		}
		arg50 := flag.Arg(1)
		mbTrans51 := thrift.NewTMemoryBufferLen(len(arg50))
		defer mbTrans51.Close()
		_, err52 := mbTrans51.WriteString(arg50)
		if err52 != nil {
			Usage()
			return
		}
		factory53 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt54 := factory53.GetProtocol(mbTrans51)
		argvalue0 := airport_server.NewRequestHeader()
		err55 := argvalue0.Read(jsProt54)
		if err55 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg56 := flag.Arg(2)
		mbTrans57 := thrift.NewTMemoryBufferLen(len(arg56))
		defer mbTrans57.Close()
		_, err58 := mbTrans57.WriteString(arg56)
		if err58 != nil {
			Usage()
			return
		}
		factory59 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt60 := factory59.GetProtocol(mbTrans57)
		argvalue1 := airport_server.NewTopicInfo()
		err61 := argvalue1.Read(jsProt60)
		if err61 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFileInfoNoticedAirportServer(value0, value1))
		fmt.Print("\n")
		break
	case "noticeFileInfoCipher":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "NoticeFileInfoCipher requires 2 args")
			flag.Usage()
		}
		arg62 := flag.Arg(1)
		mbTrans63 := thrift.NewTMemoryBufferLen(len(arg62))
		defer mbTrans63.Close()
		_, err64 := mbTrans63.WriteString(arg62)
		if err64 != nil {
			Usage()
			return
		}
		factory65 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt66 := factory65.GetProtocol(mbTrans63)
		argvalue0 := airport_server.NewRequestHeader()
		err67 := argvalue0.Read(jsProt66)
		if err67 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.NoticeFileInfoCipher(value0, value1))
		fmt.Print("\n")
		break
	case "terminalServerBeatHeart":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "TerminalServerBeatHeart requires 2 args")
			flag.Usage()
		}
		arg69 := flag.Arg(1)
		mbTrans70 := thrift.NewTMemoryBufferLen(len(arg69))
		defer mbTrans70.Close()
		_, err71 := mbTrans70.WriteString(arg69)
		if err71 != nil {
			Usage()
			return
		}
		factory72 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt73 := factory72.GetProtocol(mbTrans70)
		argvalue0 := airport_server.NewRequestHeader()
		err74 := argvalue0.Read(jsProt73)
		if err74 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err75 := (strconv.Atoi(flag.Arg(2)))
		if err75 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.TerminalServerBeatHeart(value0, value1))
		fmt.Print("\n")
		break
	case "whichTerminalServer":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "WhichTerminalServer requires 2 args")
			flag.Usage()
		}
		arg76 := flag.Arg(1)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue0 := airport_server.NewRequestHeader()
		err81 := argvalue0.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg82 := flag.Arg(2)
		mbTrans83 := thrift.NewTMemoryBufferLen(len(arg82))
		defer mbTrans83.Close()
		_, err84 := mbTrans83.WriteString(arg82)
		if err84 != nil {
			Usage()
			return
		}
		factory85 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt86 := factory85.GetProtocol(mbTrans83)
		argvalue1 := airport_server.NewTopicInfo()
		err87 := argvalue1.Read(jsProt86)
		if err87 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.WhichTerminalServer(value0, value1))
		fmt.Print("\n")
		break
	case "terminalServerSayGoodbye":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "TerminalServerSayGoodbye requires 2 args")
			flag.Usage()
		}
		arg88 := flag.Arg(1)
		mbTrans89 := thrift.NewTMemoryBufferLen(len(arg88))
		defer mbTrans89.Close()
		_, err90 := mbTrans89.WriteString(arg88)
		if err90 != nil {
			Usage()
			return
		}
		factory91 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt92 := factory91.GetProtocol(mbTrans89)
		argvalue0 := airport_server.NewRequestHeader()
		err93 := argvalue0.Read(jsProt92)
		if err93 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err94 := (strconv.Atoi(flag.Arg(2)))
		if err94 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.TerminalServerSayGoodbye(value0, value1))
		fmt.Print("\n")
		break
	case "getOtherAirportServer":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOtherAirportServer requires 1 args")
			flag.Usage()
		}
		arg95 := flag.Arg(1)
		mbTrans96 := thrift.NewTMemoryBufferLen(len(arg95))
		defer mbTrans96.Close()
		_, err97 := mbTrans96.WriteString(arg95)
		if err97 != nil {
			Usage()
			return
		}
		factory98 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt99 := factory98.GetProtocol(mbTrans96)
		argvalue0 := airport_server.NewRequestHeader()
		err100 := argvalue0.Read(jsProt99)
		if err100 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetOtherAirportServer(value0))
		fmt.Print("\n")
		break
	case "getDownloadableAirportServer":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetDownloadableAirportServer requires 1 args")
			flag.Usage()
		}
		arg101 := flag.Arg(1)
		mbTrans102 := thrift.NewTMemoryBufferLen(len(arg101))
		defer mbTrans102.Close()
		_, err103 := mbTrans102.WriteString(arg101)
		if err103 != nil {
			Usage()
			return
		}
		factory104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt105 := factory104.GetProtocol(mbTrans102)
		argvalue0 := airport_server.NewRequestHeader()
		err106 := argvalue0.Read(jsProt105)
		if err106 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetDownloadableAirportServer(value0))
		fmt.Print("\n")
		break
	case "noticeFinishFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "NoticeFinishFile requires 2 args")
			flag.Usage()
		}
		arg107 := flag.Arg(1)
		mbTrans108 := thrift.NewTMemoryBufferLen(len(arg107))
		defer mbTrans108.Close()
		_, err109 := mbTrans108.WriteString(arg107)
		if err109 != nil {
			Usage()
			return
		}
		factory110 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt111 := factory110.GetProtocol(mbTrans108)
		argvalue0 := airport_server.NewRequestHeader()
		err112 := argvalue0.Read(jsProt111)
		if err112 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg113 := flag.Arg(2)
		mbTrans114 := thrift.NewTMemoryBufferLen(len(arg113))
		defer mbTrans114.Close()
		_, err115 := mbTrans114.WriteString(arg113)
		if err115 != nil {
			Usage()
			return
		}
		factory116 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt117 := factory116.GetProtocol(mbTrans114)
		argvalue1 := airport_server.NewTopicInfo()
		err118 := argvalue1.Read(jsProt117)
		if err118 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.NoticeFinishFile(value0, value1))
		fmt.Print("\n")
		break
	case "noticeCancelFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "NoticeCancelFile requires 2 args")
			flag.Usage()
		}
		arg119 := flag.Arg(1)
		mbTrans120 := thrift.NewTMemoryBufferLen(len(arg119))
		defer mbTrans120.Close()
		_, err121 := mbTrans120.WriteString(arg119)
		if err121 != nil {
			Usage()
			return
		}
		factory122 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt123 := factory122.GetProtocol(mbTrans120)
		argvalue0 := airport_server.NewRequestHeader()
		err124 := argvalue0.Read(jsProt123)
		if err124 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg125 := flag.Arg(2)
		mbTrans126 := thrift.NewTMemoryBufferLen(len(arg125))
		defer mbTrans126.Close()
		_, err127 := mbTrans126.WriteString(arg125)
		if err127 != nil {
			Usage()
			return
		}
		factory128 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt129 := factory128.GetProtocol(mbTrans126)
		argvalue1 := airport_server.NewTopicInfo()
		err130 := argvalue1.Read(jsProt129)
		if err130 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.NoticeCancelFile(value0, value1))
		fmt.Print("\n")
		break
	case "getFilePendingTopics":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetFilePendingTopics requires 1 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := airport_server.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetFilePendingTopics(value0))
		fmt.Print("\n")
		break
	case "getFinishedFileInfoAirportServer":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinishedFileInfoAirportServer requires 2 args")
			flag.Usage()
		}
		arg137 := flag.Arg(1)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		argvalue0 := airport_server.NewRequestHeader()
		err142 := argvalue0.Read(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg143 := flag.Arg(2)
		mbTrans144 := thrift.NewTMemoryBufferLen(len(arg143))
		defer mbTrans144.Close()
		_, err145 := mbTrans144.WriteString(arg143)
		if err145 != nil {
			Usage()
			return
		}
		factory146 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt147 := factory146.GetProtocol(mbTrans144)
		argvalue1 := airport_server.NewTopicInfo()
		err148 := argvalue1.Read(jsProt147)
		if err148 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetFinishedFileInfoAirportServer(value0, value1))
		fmt.Print("\n")
		break
	case "addFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddFile requires 2 args")
			flag.Usage()
		}
		arg149 := flag.Arg(1)
		mbTrans150 := thrift.NewTMemoryBufferLen(len(arg149))
		defer mbTrans150.Close()
		_, err151 := mbTrans150.WriteString(arg149)
		if err151 != nil {
			Usage()
			return
		}
		factory152 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt153 := factory152.GetProtocol(mbTrans150)
		argvalue0 := airport_server.NewRequestHeader()
		err154 := argvalue0.Read(jsProt153)
		if err154 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg155 := flag.Arg(2)
		mbTrans156 := thrift.NewTMemoryBufferLen(len(arg155))
		defer mbTrans156.Close()
		_, err157 := mbTrans156.WriteString(arg155)
		if err157 != nil {
			Usage()
			return
		}
		factory158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt159 := factory158.GetProtocol(mbTrans156)
		argvalue1 := airport_server.NewTopicInfo()
		err160 := argvalue1.Read(jsProt159)
		if err160 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddFile(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
