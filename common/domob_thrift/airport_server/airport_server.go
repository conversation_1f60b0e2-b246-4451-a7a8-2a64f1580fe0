// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package airport_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/airport_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = airport_types.GoUnusedProtection__

type AirportServer interface {
	// 获取已经被通知到的airport server列表，返回的结果加密，因为都是host:port，防止被截取后攻击
	// 返回为StringList加密后的结果，用在server间传输，防止被截取攻击或者篡改
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	GetFileInfoNoticedAirportServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultString, err error)
	// 发送文件信息(加密后)
	// 用于server之间通信，防止被篡改
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - CipherText: TopicFileInfo加密后的结果，拥有防篡改的校验，避免传输过程中出错
	NoticeFileInfoCipher(requestHeader *common.RequestHeader, cipherText string) (r *airport_types.ResultBool, err error)
	// terminal server心跳，并获取信号
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Port: terminal server的监听端口
	TerminalServerBeatHeart(requestHeader *common.RequestHeader, port int32) (r *airport_types.ResultSignal, err error)
	// 询问topicInfo的文件应该向谁拉
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	WhichTerminalServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultI32List, err error)
	// terminal server退出
	// 仅供terminal server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Port: terminal server的监听端口
	TerminalServerSayGoodbye(requestHeader *common.RequestHeader, port int32) (r *airport_types.ResultBool, err error)
	// 获取其它airport server的host和port
	// 仅供terminal server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	GetOtherAirportServer(requestHeader *common.RequestHeader) (r *airport_types.ResultAirportInfoList, err error)
	// 获取可提供下载数据的airport server的host和port
	// 仅供terminal server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	GetDownloadableAirportServer(requestHeader *common.RequestHeader) (r *airport_types.ResultAirportInfoList, err error)
	// 通知airport server已经下载完文件
	// 仅供terminal server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	NoticeFinishFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 通知airport server取消下载文件
	// 仅供terminal server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	NoticeCancelFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 获取还未结束的文件的topic，file_status == 0
	// 仅供terminal server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	GetFilePendingTopics(requestHeader *common.RequestHeader) (r *airport_types.ResultTopicInfoList, err error)
	// 获取已经完成(该机房都处理完)的airport server列表，返回的结果加密，因为都是host:port，防止被截取后攻击
	// 返回为StringList加密后的结果，用在server间传输，防止被截取攻击或者篡改
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	GetFinishedFileInfoAirportServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultString, err error)
	// 添加文件，data server已收完
	// 仅供data server调用
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	AddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
}

type AirportServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewAirportServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AirportServerClient {
	return &AirportServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewAirportServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AirportServerClient {
	return &AirportServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 获取已经被通知到的airport server列表，返回的结果加密，因为都是host:port，防止被截取后攻击
// 返回为StringList加密后的结果，用在server间传输，防止被截取攻击或者篡改
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *AirportServerClient) GetFileInfoNoticedAirportServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultString, err error) {
	if err = p.sendGetFileInfoNoticedAirportServer(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvGetFileInfoNoticedAirportServer()
}

func (p *AirportServerClient) sendGetFileInfoNoticedAirportServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFileInfoNoticedAirportServer", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetFileInfoNoticedAirportServerArgs()
	args0.RequestHeader = requestHeader
	args0.TopicInfo = topicInfo
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvGetFileInfoNoticedAirportServer() (value *airport_types.ResultString, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetFileInfoNoticedAirportServerResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 发送文件信息(加密后)
// 用于server之间通信，防止被篡改
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - CipherText: TopicFileInfo加密后的结果，拥有防篡改的校验，避免传输过程中出错
func (p *AirportServerClient) NoticeFileInfoCipher(requestHeader *common.RequestHeader, cipherText string) (r *airport_types.ResultBool, err error) {
	if err = p.sendNoticeFileInfoCipher(requestHeader, cipherText); err != nil {
		return
	}
	return p.recvNoticeFileInfoCipher()
}

func (p *AirportServerClient) sendNoticeFileInfoCipher(requestHeader *common.RequestHeader, cipherText string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("noticeFileInfoCipher", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewNoticeFileInfoCipherArgs()
	args4.RequestHeader = requestHeader
	args4.CipherText = cipherText
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvNoticeFileInfoCipher() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewNoticeFileInfoCipherResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// terminal server心跳，并获取信号
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Port: terminal server的监听端口
func (p *AirportServerClient) TerminalServerBeatHeart(requestHeader *common.RequestHeader, port int32) (r *airport_types.ResultSignal, err error) {
	if err = p.sendTerminalServerBeatHeart(requestHeader, port); err != nil {
		return
	}
	return p.recvTerminalServerBeatHeart()
}

func (p *AirportServerClient) sendTerminalServerBeatHeart(requestHeader *common.RequestHeader, port int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("terminalServerBeatHeart", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewTerminalServerBeatHeartArgs()
	args8.RequestHeader = requestHeader
	args8.Port = port
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvTerminalServerBeatHeart() (value *airport_types.ResultSignal, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewTerminalServerBeatHeartResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 询问topicInfo的文件应该向谁拉
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *AirportServerClient) WhichTerminalServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultI32List, err error) {
	if err = p.sendWhichTerminalServer(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvWhichTerminalServer()
}

func (p *AirportServerClient) sendWhichTerminalServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("whichTerminalServer", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewWhichTerminalServerArgs()
	args12.RequestHeader = requestHeader
	args12.TopicInfo = topicInfo
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvWhichTerminalServer() (value *airport_types.ResultI32List, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewWhichTerminalServerResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// terminal server退出
// 仅供terminal server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Port: terminal server的监听端口
func (p *AirportServerClient) TerminalServerSayGoodbye(requestHeader *common.RequestHeader, port int32) (r *airport_types.ResultBool, err error) {
	if err = p.sendTerminalServerSayGoodbye(requestHeader, port); err != nil {
		return
	}
	return p.recvTerminalServerSayGoodbye()
}

func (p *AirportServerClient) sendTerminalServerSayGoodbye(requestHeader *common.RequestHeader, port int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("terminalServerSayGoodbye", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewTerminalServerSayGoodbyeArgs()
	args16.RequestHeader = requestHeader
	args16.Port = port
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvTerminalServerSayGoodbye() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewTerminalServerSayGoodbyeResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

// 获取其它airport server的host和port
// 仅供terminal server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
func (p *AirportServerClient) GetOtherAirportServer(requestHeader *common.RequestHeader) (r *airport_types.ResultAirportInfoList, err error) {
	if err = p.sendGetOtherAirportServer(requestHeader); err != nil {
		return
	}
	return p.recvGetOtherAirportServer()
}

func (p *AirportServerClient) sendGetOtherAirportServer(requestHeader *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getOtherAirportServer", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetOtherAirportServerArgs()
	args20.RequestHeader = requestHeader
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvGetOtherAirportServer() (value *airport_types.ResultAirportInfoList, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetOtherAirportServerResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 获取可提供下载数据的airport server的host和port
// 仅供terminal server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
func (p *AirportServerClient) GetDownloadableAirportServer(requestHeader *common.RequestHeader) (r *airport_types.ResultAirportInfoList, err error) {
	if err = p.sendGetDownloadableAirportServer(requestHeader); err != nil {
		return
	}
	return p.recvGetDownloadableAirportServer()
}

func (p *AirportServerClient) sendGetDownloadableAirportServer(requestHeader *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getDownloadableAirportServer", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetDownloadableAirportServerArgs()
	args24.RequestHeader = requestHeader
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvGetDownloadableAirportServer() (value *airport_types.ResultAirportInfoList, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetDownloadableAirportServerResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	return
}

// 通知airport server已经下载完文件
// 仅供terminal server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *AirportServerClient) NoticeFinishFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendNoticeFinishFile(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvNoticeFinishFile()
}

func (p *AirportServerClient) sendNoticeFinishFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("noticeFinishFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewNoticeFinishFileArgs()
	args28.RequestHeader = requestHeader
	args28.TopicInfo = topicInfo
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvNoticeFinishFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewNoticeFinishFileResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	return
}

// 通知airport server取消下载文件
// 仅供terminal server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *AirportServerClient) NoticeCancelFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendNoticeCancelFile(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvNoticeCancelFile()
}

func (p *AirportServerClient) sendNoticeCancelFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("noticeCancelFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewNoticeCancelFileArgs()
	args32.RequestHeader = requestHeader
	args32.TopicInfo = topicInfo
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvNoticeCancelFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewNoticeCancelFileResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	return
}

// 获取还未结束的文件的topic，file_status == 0
// 仅供terminal server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
func (p *AirportServerClient) GetFilePendingTopics(requestHeader *common.RequestHeader) (r *airport_types.ResultTopicInfoList, err error) {
	if err = p.sendGetFilePendingTopics(requestHeader); err != nil {
		return
	}
	return p.recvGetFilePendingTopics()
}

func (p *AirportServerClient) sendGetFilePendingTopics(requestHeader *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFilePendingTopics", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetFilePendingTopicsArgs()
	args36.RequestHeader = requestHeader
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvGetFilePendingTopics() (value *airport_types.ResultTopicInfoList, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetFilePendingTopicsResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	return
}

// 获取已经完成(该机房都处理完)的airport server列表，返回的结果加密，因为都是host:port，防止被截取后攻击
// 返回为StringList加密后的结果，用在server间传输，防止被截取攻击或者篡改
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *AirportServerClient) GetFinishedFileInfoAirportServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultString, err error) {
	if err = p.sendGetFinishedFileInfoAirportServer(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvGetFinishedFileInfoAirportServer()
}

func (p *AirportServerClient) sendGetFinishedFileInfoAirportServer(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFinishedFileInfoAirportServer", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewGetFinishedFileInfoAirportServerArgs()
	args40.RequestHeader = requestHeader
	args40.TopicInfo = topicInfo
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvGetFinishedFileInfoAirportServer() (value *airport_types.ResultString, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewGetFinishedFileInfoAirportServerResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	return
}

// 添加文件，data server已收完
// 仅供data server调用
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *AirportServerClient) AddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendAddFile(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvAddFile()
}

func (p *AirportServerClient) sendAddFile(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewAddFileArgs()
	args44.RequestHeader = requestHeader
	args44.TopicInfo = topicInfo
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AirportServerClient) recvAddFile() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewAddFileResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	return
}

type AirportServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AirportServer
}

func (p *AirportServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AirportServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AirportServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAirportServerProcessor(handler AirportServer) *AirportServerProcessor {

	self48 := &AirportServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self48.processorMap["getFileInfoNoticedAirportServer"] = &airportServerProcessorGetFileInfoNoticedAirportServer{handler: handler}
	self48.processorMap["noticeFileInfoCipher"] = &airportServerProcessorNoticeFileInfoCipher{handler: handler}
	self48.processorMap["terminalServerBeatHeart"] = &airportServerProcessorTerminalServerBeatHeart{handler: handler}
	self48.processorMap["whichTerminalServer"] = &airportServerProcessorWhichTerminalServer{handler: handler}
	self48.processorMap["terminalServerSayGoodbye"] = &airportServerProcessorTerminalServerSayGoodbye{handler: handler}
	self48.processorMap["getOtherAirportServer"] = &airportServerProcessorGetOtherAirportServer{handler: handler}
	self48.processorMap["getDownloadableAirportServer"] = &airportServerProcessorGetDownloadableAirportServer{handler: handler}
	self48.processorMap["noticeFinishFile"] = &airportServerProcessorNoticeFinishFile{handler: handler}
	self48.processorMap["noticeCancelFile"] = &airportServerProcessorNoticeCancelFile{handler: handler}
	self48.processorMap["getFilePendingTopics"] = &airportServerProcessorGetFilePendingTopics{handler: handler}
	self48.processorMap["getFinishedFileInfoAirportServer"] = &airportServerProcessorGetFinishedFileInfoAirportServer{handler: handler}
	self48.processorMap["addFile"] = &airportServerProcessorAddFile{handler: handler}
	return self48
}

func (p *AirportServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x49 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x49.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x49

}

type airportServerProcessorGetFileInfoNoticedAirportServer struct {
	handler AirportServer
}

func (p *airportServerProcessorGetFileInfoNoticedAirportServer) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFileInfoNoticedAirportServerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFileInfoNoticedAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFileInfoNoticedAirportServerResult()
	if result.Success, err = p.handler.GetFileInfoNoticedAirportServer(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFileInfoNoticedAirportServer: "+err.Error())
		oprot.WriteMessageBegin("getFileInfoNoticedAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFileInfoNoticedAirportServer", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorNoticeFileInfoCipher struct {
	handler AirportServer
}

func (p *airportServerProcessorNoticeFileInfoCipher) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewNoticeFileInfoCipherArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("noticeFileInfoCipher", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewNoticeFileInfoCipherResult()
	if result.Success, err = p.handler.NoticeFileInfoCipher(args.RequestHeader, args.CipherText); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing noticeFileInfoCipher: "+err.Error())
		oprot.WriteMessageBegin("noticeFileInfoCipher", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("noticeFileInfoCipher", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorTerminalServerBeatHeart struct {
	handler AirportServer
}

func (p *airportServerProcessorTerminalServerBeatHeart) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewTerminalServerBeatHeartArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("terminalServerBeatHeart", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewTerminalServerBeatHeartResult()
	if result.Success, err = p.handler.TerminalServerBeatHeart(args.RequestHeader, args.Port); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing terminalServerBeatHeart: "+err.Error())
		oprot.WriteMessageBegin("terminalServerBeatHeart", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("terminalServerBeatHeart", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorWhichTerminalServer struct {
	handler AirportServer
}

func (p *airportServerProcessorWhichTerminalServer) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewWhichTerminalServerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("whichTerminalServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewWhichTerminalServerResult()
	if result.Success, err = p.handler.WhichTerminalServer(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing whichTerminalServer: "+err.Error())
		oprot.WriteMessageBegin("whichTerminalServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("whichTerminalServer", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorTerminalServerSayGoodbye struct {
	handler AirportServer
}

func (p *airportServerProcessorTerminalServerSayGoodbye) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewTerminalServerSayGoodbyeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("terminalServerSayGoodbye", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewTerminalServerSayGoodbyeResult()
	if result.Success, err = p.handler.TerminalServerSayGoodbye(args.RequestHeader, args.Port); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing terminalServerSayGoodbye: "+err.Error())
		oprot.WriteMessageBegin("terminalServerSayGoodbye", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("terminalServerSayGoodbye", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorGetOtherAirportServer struct {
	handler AirportServer
}

func (p *airportServerProcessorGetOtherAirportServer) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetOtherAirportServerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getOtherAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetOtherAirportServerResult()
	if result.Success, err = p.handler.GetOtherAirportServer(args.RequestHeader); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getOtherAirportServer: "+err.Error())
		oprot.WriteMessageBegin("getOtherAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getOtherAirportServer", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorGetDownloadableAirportServer struct {
	handler AirportServer
}

func (p *airportServerProcessorGetDownloadableAirportServer) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDownloadableAirportServerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getDownloadableAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDownloadableAirportServerResult()
	if result.Success, err = p.handler.GetDownloadableAirportServer(args.RequestHeader); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getDownloadableAirportServer: "+err.Error())
		oprot.WriteMessageBegin("getDownloadableAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getDownloadableAirportServer", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorNoticeFinishFile struct {
	handler AirportServer
}

func (p *airportServerProcessorNoticeFinishFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewNoticeFinishFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("noticeFinishFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewNoticeFinishFileResult()
	if result.Success, err = p.handler.NoticeFinishFile(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing noticeFinishFile: "+err.Error())
		oprot.WriteMessageBegin("noticeFinishFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("noticeFinishFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorNoticeCancelFile struct {
	handler AirportServer
}

func (p *airportServerProcessorNoticeCancelFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewNoticeCancelFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("noticeCancelFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewNoticeCancelFileResult()
	if result.Success, err = p.handler.NoticeCancelFile(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing noticeCancelFile: "+err.Error())
		oprot.WriteMessageBegin("noticeCancelFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("noticeCancelFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorGetFilePendingTopics struct {
	handler AirportServer
}

func (p *airportServerProcessorGetFilePendingTopics) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFilePendingTopicsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFilePendingTopics", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFilePendingTopicsResult()
	if result.Success, err = p.handler.GetFilePendingTopics(args.RequestHeader); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFilePendingTopics: "+err.Error())
		oprot.WriteMessageBegin("getFilePendingTopics", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFilePendingTopics", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorGetFinishedFileInfoAirportServer struct {
	handler AirportServer
}

func (p *airportServerProcessorGetFinishedFileInfoAirportServer) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFinishedFileInfoAirportServerArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFinishedFileInfoAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFinishedFileInfoAirportServerResult()
	if result.Success, err = p.handler.GetFinishedFileInfoAirportServer(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFinishedFileInfoAirportServer: "+err.Error())
		oprot.WriteMessageBegin("getFinishedFileInfoAirportServer", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFinishedFileInfoAirportServer", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type airportServerProcessorAddFile struct {
	handler AirportServer
}

func (p *airportServerProcessorAddFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddFileResult()
	if result.Success, err = p.handler.AddFile(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addFile: "+err.Error())
		oprot.WriteMessageBegin("addFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetFileInfoNoticedAirportServerArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewGetFileInfoNoticedAirportServerArgs() *GetFileInfoNoticedAirportServerArgs {
	return &GetFileInfoNoticedAirportServerArgs{}
}

func (p *GetFileInfoNoticedAirportServerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileInfoNoticedAirportServerArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFileInfoNoticedAirportServerArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *GetFileInfoNoticedAirportServerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileInfoNoticedAirportServer_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileInfoNoticedAirportServerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFileInfoNoticedAirportServerArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetFileInfoNoticedAirportServerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileInfoNoticedAirportServerArgs(%+v)", *p)
}

type GetFileInfoNoticedAirportServerResult struct {
	Success *airport_types.ResultString `thrift:"success,0" json:"success"`
}

func NewGetFileInfoNoticedAirportServerResult() *GetFileInfoNoticedAirportServerResult {
	return &GetFileInfoNoticedAirportServerResult{}
}

func (p *GetFileInfoNoticedAirportServerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileInfoNoticedAirportServerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultString()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFileInfoNoticedAirportServerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileInfoNoticedAirportServer_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileInfoNoticedAirportServerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFileInfoNoticedAirportServerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileInfoNoticedAirportServerResult(%+v)", *p)
}

type NoticeFileInfoCipherArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	CipherText    string                `thrift:"cipherText,2" json:"cipherText"`
}

func NewNoticeFileInfoCipherArgs() *NoticeFileInfoCipherArgs {
	return &NoticeFileInfoCipherArgs{}
}

func (p *NoticeFileInfoCipherArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeFileInfoCipherArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *NoticeFileInfoCipherArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CipherText = v
	}
	return nil
}

func (p *NoticeFileInfoCipherArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeFileInfoCipher_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeFileInfoCipherArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFileInfoCipherArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cipherText", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cipherText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CipherText)); err != nil {
		return fmt.Errorf("%T.cipherText (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cipherText: %s", p, err)
	}
	return err
}

func (p *NoticeFileInfoCipherArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeFileInfoCipherArgs(%+v)", *p)
}

type NoticeFileInfoCipherResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewNoticeFileInfoCipherResult() *NoticeFileInfoCipherResult {
	return &NoticeFileInfoCipherResult{}
}

func (p *NoticeFileInfoCipherResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeFileInfoCipherResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *NoticeFileInfoCipherResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeFileInfoCipher_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeFileInfoCipherResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFileInfoCipherResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeFileInfoCipherResult(%+v)", *p)
}

type TerminalServerBeatHeartArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Port          int32                 `thrift:"port,2" json:"port"`
}

func NewTerminalServerBeatHeartArgs() *TerminalServerBeatHeartArgs {
	return &TerminalServerBeatHeartArgs{}
}

func (p *TerminalServerBeatHeartArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerBeatHeartArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *TerminalServerBeatHeartArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Port = v
	}
	return nil
}

func (p *TerminalServerBeatHeartArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("terminalServerBeatHeart_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerBeatHeartArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *TerminalServerBeatHeartArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("port", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:port: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Port)); err != nil {
		return fmt.Errorf("%T.port (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:port: %s", p, err)
	}
	return err
}

func (p *TerminalServerBeatHeartArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TerminalServerBeatHeartArgs(%+v)", *p)
}

type TerminalServerBeatHeartResult struct {
	Success *airport_types.ResultSignal `thrift:"success,0" json:"success"`
}

func NewTerminalServerBeatHeartResult() *TerminalServerBeatHeartResult {
	return &TerminalServerBeatHeartResult{}
}

func (p *TerminalServerBeatHeartResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerBeatHeartResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultSignal()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *TerminalServerBeatHeartResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("terminalServerBeatHeart_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerBeatHeartResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *TerminalServerBeatHeartResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TerminalServerBeatHeartResult(%+v)", *p)
}

type WhichTerminalServerArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewWhichTerminalServerArgs() *WhichTerminalServerArgs {
	return &WhichTerminalServerArgs{}
}

func (p *WhichTerminalServerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WhichTerminalServerArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *WhichTerminalServerArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *WhichTerminalServerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("whichTerminalServer_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WhichTerminalServerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *WhichTerminalServerArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *WhichTerminalServerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WhichTerminalServerArgs(%+v)", *p)
}

type WhichTerminalServerResult struct {
	Success *airport_types.ResultI32List `thrift:"success,0" json:"success"`
}

func NewWhichTerminalServerResult() *WhichTerminalServerResult {
	return &WhichTerminalServerResult{}
}

func (p *WhichTerminalServerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WhichTerminalServerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultI32List()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *WhichTerminalServerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("whichTerminalServer_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WhichTerminalServerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *WhichTerminalServerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WhichTerminalServerResult(%+v)", *p)
}

type TerminalServerSayGoodbyeArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Port          int32                 `thrift:"port,2" json:"port"`
}

func NewTerminalServerSayGoodbyeArgs() *TerminalServerSayGoodbyeArgs {
	return &TerminalServerSayGoodbyeArgs{}
}

func (p *TerminalServerSayGoodbyeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerSayGoodbyeArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *TerminalServerSayGoodbyeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Port = v
	}
	return nil
}

func (p *TerminalServerSayGoodbyeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("terminalServerSayGoodbye_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerSayGoodbyeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *TerminalServerSayGoodbyeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("port", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:port: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Port)); err != nil {
		return fmt.Errorf("%T.port (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:port: %s", p, err)
	}
	return err
}

func (p *TerminalServerSayGoodbyeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TerminalServerSayGoodbyeArgs(%+v)", *p)
}

type TerminalServerSayGoodbyeResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewTerminalServerSayGoodbyeResult() *TerminalServerSayGoodbyeResult {
	return &TerminalServerSayGoodbyeResult{}
}

func (p *TerminalServerSayGoodbyeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerSayGoodbyeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *TerminalServerSayGoodbyeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("terminalServerSayGoodbye_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TerminalServerSayGoodbyeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *TerminalServerSayGoodbyeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TerminalServerSayGoodbyeResult(%+v)", *p)
}

type GetOtherAirportServerArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
}

func NewGetOtherAirportServerArgs() *GetOtherAirportServerArgs {
	return &GetOtherAirportServerArgs{}
}

func (p *GetOtherAirportServerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOtherAirportServerArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetOtherAirportServerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOtherAirportServer_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOtherAirportServerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetOtherAirportServerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOtherAirportServerArgs(%+v)", *p)
}

type GetOtherAirportServerResult struct {
	Success *airport_types.ResultAirportInfoList `thrift:"success,0" json:"success"`
}

func NewGetOtherAirportServerResult() *GetOtherAirportServerResult {
	return &GetOtherAirportServerResult{}
}

func (p *GetOtherAirportServerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetOtherAirportServerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultAirportInfoList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetOtherAirportServerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getOtherAirportServer_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetOtherAirportServerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetOtherAirportServerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetOtherAirportServerResult(%+v)", *p)
}

type GetDownloadableAirportServerArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
}

func NewGetDownloadableAirportServerArgs() *GetDownloadableAirportServerArgs {
	return &GetDownloadableAirportServerArgs{}
}

func (p *GetDownloadableAirportServerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDownloadableAirportServerArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetDownloadableAirportServerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDownloadableAirportServer_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDownloadableAirportServerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetDownloadableAirportServerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDownloadableAirportServerArgs(%+v)", *p)
}

type GetDownloadableAirportServerResult struct {
	Success *airport_types.ResultAirportInfoList `thrift:"success,0" json:"success"`
}

func NewGetDownloadableAirportServerResult() *GetDownloadableAirportServerResult {
	return &GetDownloadableAirportServerResult{}
}

func (p *GetDownloadableAirportServerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDownloadableAirportServerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultAirportInfoList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetDownloadableAirportServerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDownloadableAirportServer_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDownloadableAirportServerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDownloadableAirportServerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDownloadableAirportServerResult(%+v)", *p)
}

type NoticeFinishFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewNoticeFinishFileArgs() *NoticeFinishFileArgs {
	return &NoticeFinishFileArgs{}
}

func (p *NoticeFinishFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeFinishFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *NoticeFinishFileArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *NoticeFinishFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeFinishFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeFinishFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFinishFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFinishFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeFinishFileArgs(%+v)", *p)
}

type NoticeFinishFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewNoticeFinishFileResult() *NoticeFinishFileResult {
	return &NoticeFinishFileResult{}
}

func (p *NoticeFinishFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeFinishFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *NoticeFinishFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeFinishFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeFinishFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *NoticeFinishFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeFinishFileResult(%+v)", *p)
}

type NoticeCancelFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewNoticeCancelFileArgs() *NoticeCancelFileArgs {
	return &NoticeCancelFileArgs{}
}

func (p *NoticeCancelFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeCancelFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *NoticeCancelFileArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *NoticeCancelFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeCancelFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeCancelFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *NoticeCancelFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *NoticeCancelFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeCancelFileArgs(%+v)", *p)
}

type NoticeCancelFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewNoticeCancelFileResult() *NoticeCancelFileResult {
	return &NoticeCancelFileResult{}
}

func (p *NoticeCancelFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NoticeCancelFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *NoticeCancelFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("noticeCancelFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NoticeCancelFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *NoticeCancelFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NoticeCancelFileResult(%+v)", *p)
}

type GetFilePendingTopicsArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
}

func NewGetFilePendingTopicsArgs() *GetFilePendingTopicsArgs {
	return &GetFilePendingTopicsArgs{}
}

func (p *GetFilePendingTopicsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFilePendingTopicsArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFilePendingTopicsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFilePendingTopics_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFilePendingTopicsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFilePendingTopicsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFilePendingTopicsArgs(%+v)", *p)
}

type GetFilePendingTopicsResult struct {
	Success *airport_types.ResultTopicInfoList `thrift:"success,0" json:"success"`
}

func NewGetFilePendingTopicsResult() *GetFilePendingTopicsResult {
	return &GetFilePendingTopicsResult{}
}

func (p *GetFilePendingTopicsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFilePendingTopicsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultTopicInfoList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFilePendingTopicsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFilePendingTopics_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFilePendingTopicsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFilePendingTopicsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFilePendingTopicsResult(%+v)", *p)
}

type GetFinishedFileInfoAirportServerArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewGetFinishedFileInfoAirportServerArgs() *GetFinishedFileInfoAirportServerArgs {
	return &GetFinishedFileInfoAirportServerArgs{}
}

func (p *GetFinishedFileInfoAirportServerArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedFileInfoAirportServerArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFinishedFileInfoAirportServerArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *GetFinishedFileInfoAirportServerArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinishedFileInfoAirportServer_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedFileInfoAirportServerArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFinishedFileInfoAirportServerArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetFinishedFileInfoAirportServerArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinishedFileInfoAirportServerArgs(%+v)", *p)
}

type GetFinishedFileInfoAirportServerResult struct {
	Success *airport_types.ResultString `thrift:"success,0" json:"success"`
}

func NewGetFinishedFileInfoAirportServerResult() *GetFinishedFileInfoAirportServerResult {
	return &GetFinishedFileInfoAirportServerResult{}
}

func (p *GetFinishedFileInfoAirportServerResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedFileInfoAirportServerResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultString()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFinishedFileInfoAirportServerResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFinishedFileInfoAirportServer_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFinishedFileInfoAirportServerResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFinishedFileInfoAirportServerResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFinishedFileInfoAirportServerResult(%+v)", *p)
}

type AddFileArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewAddFileArgs() *AddFileArgs {
	return &AddFileArgs{}
}

func (p *AddFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *AddFileArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *AddFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *AddFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *AddFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFileArgs(%+v)", *p)
}

type AddFileResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewAddFileResult() *AddFileResult {
	return &AddFileResult{}
}

func (p *AddFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddFileResult(%+v)", *p)
}
