// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package clksender

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/clksender_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = clksender_types.GoUnusedProtection__
var GoUnusedProtection__ int

type StatsConditionParam struct {
	StartTime int64 `thrift:"startTime,1" json:"startTime"`
	EndTime   int64 `thrift:"endTime,2" json:"endTime"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Channels    []int32 `thrift:"channels,10" json:"channels"`
	AppIds      []int32 `thrift:"appIds,11" json:"appIds"`
	ChnIds      []int32 `thrift:"chnIds,12" json:"chnIds"`
	CfgIds      []int32 `thrift:"cfgIds,13" json:"cfgIds"`
	ExchangeIds []int32 `thrift:"exchangeIds,14" json:"exchangeIds"`
	SponsorIds  []int32 `thrift:"sponsorIds,15" json:"sponsorIds"`
	Pids        []int32 `thrift:"pids,16" json:"pids"`
	Sids        []int32 `thrift:"sids,17" json:"sids"`
	Cids        []int32 `thrift:"cids,18" json:"cids"`
}

func NewStatsConditionParam() *StatsConditionParam {
	return &StatsConditionParam{}
}

func (p *StatsConditionParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *StatsConditionParam) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Channels = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Channels = append(p.Channels, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AppIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.AppIds = append(p.AppIds, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChnIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.ChnIds = append(p.ChnIds, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CfgIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.CfgIds = append(p.CfgIds, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExchangeIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.ExchangeIds = append(p.ExchangeIds, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SponsorIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.SponsorIds = append(p.SponsorIds, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Pids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.Pids = append(p.Pids, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Sids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.Sids = append(p.Sids, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Cids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.Cids = append(p.Cids, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsConditionParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsConditionParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsConditionParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *StatsConditionParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Channels != nil {
		if err := oprot.WriteFieldBegin("channels", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:channels: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Channels)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Channels {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:channels: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.AppIds != nil {
		if err := oprot.WriteFieldBegin("appIds", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:appIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AppIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AppIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:appIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField12(oprot thrift.TProtocol) (err error) {
	if p.ChnIds != nil {
		if err := oprot.WriteFieldBegin("chnIds", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:chnIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ChnIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChnIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:chnIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField13(oprot thrift.TProtocol) (err error) {
	if p.CfgIds != nil {
		if err := oprot.WriteFieldBegin("cfgIds", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:cfgIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CfgIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CfgIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:cfgIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField14(oprot thrift.TProtocol) (err error) {
	if p.ExchangeIds != nil {
		if err := oprot.WriteFieldBegin("exchangeIds", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:exchangeIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ExchangeIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExchangeIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:exchangeIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField15(oprot thrift.TProtocol) (err error) {
	if p.SponsorIds != nil {
		if err := oprot.WriteFieldBegin("sponsorIds", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:sponsorIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SponsorIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SponsorIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:sponsorIds: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField16(oprot thrift.TProtocol) (err error) {
	if p.Pids != nil {
		if err := oprot.WriteFieldBegin("pids", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:pids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Pids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Pids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:pids: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField17(oprot thrift.TProtocol) (err error) {
	if p.Sids != nil {
		if err := oprot.WriteFieldBegin("sids", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:sids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Sids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Sids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:sids: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) writeField18(oprot thrift.TProtocol) (err error) {
	if p.Cids != nil {
		if err := oprot.WriteFieldBegin("cids", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:cids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Cids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Cids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:cids: %s", p, err)
		}
	}
	return err
}

func (p *StatsConditionParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsConditionParam(%+v)", *p)
}

type StatsGroupByParam struct {
	Dt         bool `thrift:"dt,1" json:"dt"`
	Hr         bool `thrift:"hr,2" json:"hr"`
	Channel    bool `thrift:"channel,3" json:"channel"`
	AppId      bool `thrift:"appId,4" json:"appId"`
	ChnId      bool `thrift:"chnId,5" json:"chnId"`
	CfgId      bool `thrift:"cfgId,6" json:"cfgId"`
	ExchangeId bool `thrift:"exchangeId,7" json:"exchangeId"`
	SponsorId  bool `thrift:"sponsorId,8" json:"sponsorId"`
	Pid        bool `thrift:"pid,9" json:"pid"`
	Sid        bool `thrift:"sid,10" json:"sid"`
	Cid        bool `thrift:"cid,11" json:"cid"`
}

func NewStatsGroupByParam() *StatsGroupByParam {
	return &StatsGroupByParam{}
}

func (p *StatsGroupByParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *StatsGroupByParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *StatsGroupByParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *StatsGroupByParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CfgId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *StatsGroupByParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *StatsGroupByParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *StatsGroupByParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *StatsGroupByParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsGroupByParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsGroupByParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.BOOL, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channel: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channel: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:appId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:appId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:chnId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:chnId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cfgId", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:cfgId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.CfgId)); err != nil {
		return fmt.Errorf("%T.cfgId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:cfgId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:exchangeId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:exchangeId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.BOOL, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sponsorId: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sponsorId: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.BOOL, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:pid: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:pid: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:sid: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:sid: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.BOOL, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:cid: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:cid: %s", p, err)
	}
	return err
}

func (p *StatsGroupByParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsGroupByParam(%+v)", *p)
}

type ClickStatsData struct {
	Dt int32 `thrift:"dt,1" json:"dt"`
	Hr int32 `thrift:"hr,2" json:"hr"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Channel    clksender_types.AdChannel `thrift:"channel,11" json:"channel"`
	AppId      int32                     `thrift:"appId,12" json:"appId"`
	ChnId      int32                     `thrift:"chnId,13" json:"chnId"`
	CfgId      int32                     `thrift:"cfgId,14" json:"cfgId"`
	ExchangeId int32                     `thrift:"exchangeId,15" json:"exchangeId"`
	SponsorId  int32                     `thrift:"sponsorId,16" json:"sponsorId"`
	Pid        int32                     `thrift:"pid,17" json:"pid"`
	Sid        int32                     `thrift:"sid,18" json:"sid"`
	Cid        int32                     `thrift:"cid,19" json:"cid"`
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	SuccessCount int64 `thrift:"successCount,30" json:"successCount"`
	FailedCount  int64 `thrift:"failedCount,31" json:"failedCount"`
}

func NewClickStatsData() *ClickStatsData {
	return &ClickStatsData{
		Channel: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ClickStatsData) IsSetChannel() bool {
	return int64(p.Channel) != math.MinInt32-1
}

func (p *ClickStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ClickStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *ClickStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *ClickStatsData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Channel = clksender_types.AdChannel(v)
	}
	return nil
}

func (p *ClickStatsData) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *ClickStatsData) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *ClickStatsData) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.CfgId = v
	}
	return nil
}

func (p *ClickStatsData) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *ClickStatsData) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *ClickStatsData) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *ClickStatsData) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *ClickStatsData) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *ClickStatsData) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.SuccessCount = v
	}
	return nil
}

func (p *ClickStatsData) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.FailedCount = v
	}
	return nil
}

func (p *ClickStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ClickStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ClickStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hr: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannel() {
		if err := oprot.WriteFieldBegin("channel", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:channel: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Channel)); err != nil {
			return fmt.Errorf("%T.channel (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:channel: %s", p, err)
		}
	}
	return err
}

func (p *ClickStatsData) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:appId: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:chnId: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cfgId", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:cfgId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CfgId)); err != nil {
		return fmt.Errorf("%T.cfgId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:cfgId: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:exchangeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:exchangeId: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sponsorId: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:pid: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:sid: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:cid: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("successCount", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:successCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SuccessCount)); err != nil {
		return fmt.Errorf("%T.successCount (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:successCount: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("failedCount", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:failedCount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FailedCount)); err != nil {
		return fmt.Errorf("%T.failedCount (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:failedCount: %s", p, err)
	}
	return err
}

func (p *ClickStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ClickStatsData(%+v)", *p)
}

type StatsQueryResult struct {
	TotalCount int32             `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int32             `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32             `thrift:"offset,3" json:"offset"`
	Limit      int32             `thrift:"limit,4" json:"limit"`
	Result     []*ClickStatsData `thrift:"result,5" json:"result"`
}

func NewStatsQueryResult() *StatsQueryResult {
	return &StatsQueryResult{}
}

func (p *StatsQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *StatsQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *StatsQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *StatsQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *StatsQueryResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*ClickStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewClickStatsData()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.Result = append(p.Result, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *StatsQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *StatsQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsQueryResult(%+v)", *p)
}

type ClickSenderException struct {
	Code    int32  `thrift:"code,1" json:"code"`
	Message string `thrift:"message,2" json:"message"`
}

func NewClickSenderException() *ClickSenderException {
	return &ClickSenderException{}
}

func (p *ClickSenderException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ClickSenderException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *ClickSenderException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *ClickSenderException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ClickSenderException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ClickSenderException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return fmt.Errorf("%T.code (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:code: %s", p, err)
	}
	return err
}

func (p *ClickSenderException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *ClickSenderException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ClickSenderException(%+v)", *p)
}
