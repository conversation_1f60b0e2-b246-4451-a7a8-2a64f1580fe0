// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"awc_accserver"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  WechatUser getUserInfo(RequestHeader header, string openid)")
	fmt.Fprintln(os.Stderr, "   getTaskRecord(RequestHeader header, string openid)")
	fmt.Fprintln(os.<PERSON>, "   getInviteRecord(RequestHeader header, string openid)")
	fmt.Fprintln(os.Stderr, "   getInvited(RequestHeader header, string openid)")
	fmt.Fprintln(os.Stderr, "  ExchResponse checkRequest(RequestHeader header, ExchRequest request)")
	fmt.Fprintln(os.Stderr, "  i32 Draw(RequestHeader header, string openid)")
	fmt.Fprintln(os.Stderr, "  bool domobCallBack(RequestHeader header, DomobCallBack callback)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := awc_accserver.NewAccServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getUserInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUserInfo requires 2 args")
			flag.Usage()
		}
		arg33 := flag.Arg(1)
		mbTrans34 := thrift.NewTMemoryBufferLen(len(arg33))
		defer mbTrans34.Close()
		_, err35 := mbTrans34.WriteString(arg33)
		if err35 != nil {
			Usage()
			return
		}
		factory36 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt37 := factory36.GetProtocol(mbTrans34)
		argvalue0 := awc_accserver.NewRequestHeader()
		err38 := argvalue0.Read(jsProt37)
		if err38 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetUserInfo(value0, value1))
		fmt.Print("\n")
		break
	case "getTaskRecord":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTaskRecord requires 2 args")
			flag.Usage()
		}
		arg40 := flag.Arg(1)
		mbTrans41 := thrift.NewTMemoryBufferLen(len(arg40))
		defer mbTrans41.Close()
		_, err42 := mbTrans41.WriteString(arg40)
		if err42 != nil {
			Usage()
			return
		}
		factory43 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt44 := factory43.GetProtocol(mbTrans41)
		argvalue0 := awc_accserver.NewRequestHeader()
		err45 := argvalue0.Read(jsProt44)
		if err45 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetTaskRecord(value0, value1))
		fmt.Print("\n")
		break
	case "getInviteRecord":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetInviteRecord requires 2 args")
			flag.Usage()
		}
		arg47 := flag.Arg(1)
		mbTrans48 := thrift.NewTMemoryBufferLen(len(arg47))
		defer mbTrans48.Close()
		_, err49 := mbTrans48.WriteString(arg47)
		if err49 != nil {
			Usage()
			return
		}
		factory50 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt51 := factory50.GetProtocol(mbTrans48)
		argvalue0 := awc_accserver.NewRequestHeader()
		err52 := argvalue0.Read(jsProt51)
		if err52 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetInviteRecord(value0, value1))
		fmt.Print("\n")
		break
	case "getInvited":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetInvited requires 2 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := awc_accserver.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetInvited(value0, value1))
		fmt.Print("\n")
		break
	case "checkRequest":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CheckRequest requires 2 args")
			flag.Usage()
		}
		arg61 := flag.Arg(1)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue0 := awc_accserver.NewRequestHeader()
		err66 := argvalue0.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg67 := flag.Arg(2)
		mbTrans68 := thrift.NewTMemoryBufferLen(len(arg67))
		defer mbTrans68.Close()
		_, err69 := mbTrans68.WriteString(arg67)
		if err69 != nil {
			Usage()
			return
		}
		factory70 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt71 := factory70.GetProtocol(mbTrans68)
		argvalue1 := awc_accserver.NewExchRequest()
		err72 := argvalue1.Read(jsProt71)
		if err72 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.CheckRequest(value0, value1))
		fmt.Print("\n")
		break
	case "Draw":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "Draw requires 2 args")
			flag.Usage()
		}
		arg73 := flag.Arg(1)
		mbTrans74 := thrift.NewTMemoryBufferLen(len(arg73))
		defer mbTrans74.Close()
		_, err75 := mbTrans74.WriteString(arg73)
		if err75 != nil {
			Usage()
			return
		}
		factory76 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt77 := factory76.GetProtocol(mbTrans74)
		argvalue0 := awc_accserver.NewRequestHeader()
		err78 := argvalue0.Read(jsProt77)
		if err78 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.Draw(value0, value1))
		fmt.Print("\n")
		break
	case "domobCallBack":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DomobCallBack requires 2 args")
			flag.Usage()
		}
		arg80 := flag.Arg(1)
		mbTrans81 := thrift.NewTMemoryBufferLen(len(arg80))
		defer mbTrans81.Close()
		_, err82 := mbTrans81.WriteString(arg80)
		if err82 != nil {
			Usage()
			return
		}
		factory83 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt84 := factory83.GetProtocol(mbTrans81)
		argvalue0 := awc_accserver.NewRequestHeader()
		err85 := argvalue0.Read(jsProt84)
		if err85 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg86 := flag.Arg(2)
		mbTrans87 := thrift.NewTMemoryBufferLen(len(arg86))
		defer mbTrans87.Close()
		_, err88 := mbTrans87.WriteString(arg86)
		if err88 != nil {
			Usage()
			return
		}
		factory89 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt90 := factory89.GetProtocol(mbTrans87)
		argvalue1 := awc_accserver.NewDomobCallBack()
		err91 := argvalue1.Read(jsProt90)
		if err91 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.DomobCallBack(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
