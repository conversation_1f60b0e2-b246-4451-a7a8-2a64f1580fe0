// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package compass_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//删除状态
type DeleteStatus int64

const (
	DeleteStatus_DS_RUNNABLE DeleteStatus = 0
	DeleteStatus_DS_DELETED  DeleteStatus = 1
)

func (p DeleteStatus) String() string {
	switch p {
	case DeleteStatus_DS_RUNNABLE:
		return "DeleteStatus_DS_RUNNABLE"
	case DeleteStatus_DS_DELETED:
		return "DeleteStatus_DS_DELETED"
	}
	return "<UNSET>"
}

func DeleteStatusFromString(s string) (DeleteStatus, error) {
	switch s {
	case "DeleteStatus_DS_RUNNABLE":
		return DeleteStatus_DS_RUNNABLE, nil
	case "DeleteStatus_DS_DELETED":
		return DeleteStatus_DS_DELETED, nil
	}
	return DeleteStatus(math.MinInt32 - 1), fmt.Errorf("not a valid DeleteStatus string")
}

type ScheduleStatus int64

const (
	ScheduleStatus_SS_UNKNOWN  ScheduleStatus = 0
	ScheduleStatus_SS_NEW      ScheduleStatus = 1
	ScheduleStatus_SS_COMMITED ScheduleStatus = 2
	ScheduleStatus_SS_RUNNING  ScheduleStatus = 3
	ScheduleStatus_SS_SUCESS   ScheduleStatus = 4
	ScheduleStatus_SS_FAILURE  ScheduleStatus = 5
	ScheduleStatus_SS_STOPED   ScheduleStatus = 101
)

func (p ScheduleStatus) String() string {
	switch p {
	case ScheduleStatus_SS_UNKNOWN:
		return "ScheduleStatus_SS_UNKNOWN"
	case ScheduleStatus_SS_NEW:
		return "ScheduleStatus_SS_NEW"
	case ScheduleStatus_SS_COMMITED:
		return "ScheduleStatus_SS_COMMITED"
	case ScheduleStatus_SS_RUNNING:
		return "ScheduleStatus_SS_RUNNING"
	case ScheduleStatus_SS_SUCESS:
		return "ScheduleStatus_SS_SUCESS"
	case ScheduleStatus_SS_FAILURE:
		return "ScheduleStatus_SS_FAILURE"
	case ScheduleStatus_SS_STOPED:
		return "ScheduleStatus_SS_STOPED"
	}
	return "<UNSET>"
}

func ScheduleStatusFromString(s string) (ScheduleStatus, error) {
	switch s {
	case "ScheduleStatus_SS_UNKNOWN":
		return ScheduleStatus_SS_UNKNOWN, nil
	case "ScheduleStatus_SS_NEW":
		return ScheduleStatus_SS_NEW, nil
	case "ScheduleStatus_SS_COMMITED":
		return ScheduleStatus_SS_COMMITED, nil
	case "ScheduleStatus_SS_RUNNING":
		return ScheduleStatus_SS_RUNNING, nil
	case "ScheduleStatus_SS_SUCESS":
		return ScheduleStatus_SS_SUCESS, nil
	case "ScheduleStatus_SS_FAILURE":
		return ScheduleStatus_SS_FAILURE, nil
	case "ScheduleStatus_SS_STOPED":
		return ScheduleStatus_SS_STOPED, nil
	}
	return ScheduleStatus(math.MinInt32 - 1), fmt.Errorf("not a valid ScheduleStatus string")
}

type CompassCategory int64

const (
	CompassCategory_CC_UNKNOWN    CompassCategory = 0
	CompassCategory_CC_CROWD      CompassCategory = 1
	CompassCategory_CC_LIFE_CYCLE CompassCategory = 2
	CompassCategory_CC_APP_LAUNCH CompassCategory = 3
)

func (p CompassCategory) String() string {
	switch p {
	case CompassCategory_CC_UNKNOWN:
		return "CompassCategory_CC_UNKNOWN"
	case CompassCategory_CC_CROWD:
		return "CompassCategory_CC_CROWD"
	case CompassCategory_CC_LIFE_CYCLE:
		return "CompassCategory_CC_LIFE_CYCLE"
	case CompassCategory_CC_APP_LAUNCH:
		return "CompassCategory_CC_APP_LAUNCH"
	}
	return "<UNSET>"
}

func CompassCategoryFromString(s string) (CompassCategory, error) {
	switch s {
	case "CompassCategory_CC_UNKNOWN":
		return CompassCategory_CC_UNKNOWN, nil
	case "CompassCategory_CC_CROWD":
		return CompassCategory_CC_CROWD, nil
	case "CompassCategory_CC_LIFE_CYCLE":
		return CompassCategory_CC_LIFE_CYCLE, nil
	case "CompassCategory_CC_APP_LAUNCH":
		return CompassCategory_CC_APP_LAUNCH, nil
	}
	return CompassCategory(math.MinInt32 - 1), fmt.Errorf("not a valid CompassCategory string")
}

type PlatformType int64

const (
	PlatformType_PT_UNKNOWN PlatformType = 0
	PlatformType_PT_DOMOB   PlatformType = 1
	PlatformType_PT_TSA     PlatformType = 2
)

func (p PlatformType) String() string {
	switch p {
	case PlatformType_PT_UNKNOWN:
		return "PlatformType_PT_UNKNOWN"
	case PlatformType_PT_DOMOB:
		return "PlatformType_PT_DOMOB"
	case PlatformType_PT_TSA:
		return "PlatformType_PT_TSA"
	}
	return "<UNSET>"
}

func PlatformTypeFromString(s string) (PlatformType, error) {
	switch s {
	case "PlatformType_PT_UNKNOWN":
		return PlatformType_PT_UNKNOWN, nil
	case "PlatformType_PT_DOMOB":
		return PlatformType_PT_DOMOB, nil
	case "PlatformType_PT_TSA":
		return PlatformType_PT_TSA, nil
	}
	return PlatformType(math.MinInt32 - 1), fmt.Errorf("not a valid PlatformType string")
}

type SeedScopeType int64

const (
	SeedScopeType_SST_UNKNOWN SeedScopeType = 0
	SeedScopeType_SST_INCLUDE SeedScopeType = 1
	SeedScopeType_SST_EXCLUDE SeedScopeType = 2
)

func (p SeedScopeType) String() string {
	switch p {
	case SeedScopeType_SST_UNKNOWN:
		return "SeedScopeType_SST_UNKNOWN"
	case SeedScopeType_SST_INCLUDE:
		return "SeedScopeType_SST_INCLUDE"
	case SeedScopeType_SST_EXCLUDE:
		return "SeedScopeType_SST_EXCLUDE"
	}
	return "<UNSET>"
}

func SeedScopeTypeFromString(s string) (SeedScopeType, error) {
	switch s {
	case "SeedScopeType_SST_UNKNOWN":
		return SeedScopeType_SST_UNKNOWN, nil
	case "SeedScopeType_SST_INCLUDE":
		return SeedScopeType_SST_INCLUDE, nil
	case "SeedScopeType_SST_EXCLUDE":
		return SeedScopeType_SST_EXCLUDE, nil
	}
	return SeedScopeType(math.MinInt32 - 1), fmt.Errorf("not a valid SeedScopeType string")
}

type DeviceType int64

const (
	DeviceType_DT_UNKNOWN   DeviceType = 0
	DeviceType_DT_QQ        DeviceType = 1
	DeviceType_DT_PHONE     DeviceType = 2
	DeviceType_DT_PHONE_MD5 DeviceType = 3
	DeviceType_DT_IDFA      DeviceType = 4
	DeviceType_DT_IDFA_MD5  DeviceType = 5
	DeviceType_DT_IMEI      DeviceType = 6
	DeviceType_DT_IMEI_MD5  DeviceType = 7
	DeviceType_DT_WX_OPENID DeviceType = 8
)

func (p DeviceType) String() string {
	switch p {
	case DeviceType_DT_UNKNOWN:
		return "DeviceType_DT_UNKNOWN"
	case DeviceType_DT_QQ:
		return "DeviceType_DT_QQ"
	case DeviceType_DT_PHONE:
		return "DeviceType_DT_PHONE"
	case DeviceType_DT_PHONE_MD5:
		return "DeviceType_DT_PHONE_MD5"
	case DeviceType_DT_IDFA:
		return "DeviceType_DT_IDFA"
	case DeviceType_DT_IDFA_MD5:
		return "DeviceType_DT_IDFA_MD5"
	case DeviceType_DT_IMEI:
		return "DeviceType_DT_IMEI"
	case DeviceType_DT_IMEI_MD5:
		return "DeviceType_DT_IMEI_MD5"
	case DeviceType_DT_WX_OPENID:
		return "DeviceType_DT_WX_OPENID"
	}
	return "<UNSET>"
}

func DeviceTypeFromString(s string) (DeviceType, error) {
	switch s {
	case "DeviceType_DT_UNKNOWN":
		return DeviceType_DT_UNKNOWN, nil
	case "DeviceType_DT_QQ":
		return DeviceType_DT_QQ, nil
	case "DeviceType_DT_PHONE":
		return DeviceType_DT_PHONE, nil
	case "DeviceType_DT_PHONE_MD5":
		return DeviceType_DT_PHONE_MD5, nil
	case "DeviceType_DT_IDFA":
		return DeviceType_DT_IDFA, nil
	case "DeviceType_DT_IDFA_MD5":
		return DeviceType_DT_IDFA_MD5, nil
	case "DeviceType_DT_IMEI":
		return DeviceType_DT_IMEI, nil
	case "DeviceType_DT_IMEI_MD5":
		return DeviceType_DT_IMEI_MD5, nil
	case "DeviceType_DT_WX_OPENID":
		return DeviceType_DT_WX_OPENID, nil
	}
	return DeviceType(math.MinInt32 - 1), fmt.Errorf("not a valid DeviceType string")
}

type AdAction int64

const (
	AdAction_AA_UNKNOWN    AdAction = 0
	AdAction_AA_LURK       AdAction = 1
	AdAction_AA_EXPOSE     AdAction = 2
	AdAction_AA_CLK        AdAction = 3
	AdAction_AA_CLK_INNER  AdAction = 4
	AdAction_AA_VIDEO      AdAction = 5
	AdAction_AA_VIDEO_DONE AdAction = 6
	AdAction_AA_ACT        AdAction = 7
	AdAction_AA_BUY        AdAction = 8
	AdAction_AA_DOWNLOAD   AdAction = 9
	AdAction_AA_REGISTER   AdAction = 10
	AdAction_AA_LIKE       AdAction = 11
	AdAction_AA_COMMENT    AdAction = 12
	AdAction_AA_FORWARD    AdAction = 13
	AdAction_AA_FOCUS      AdAction = 14
)

func (p AdAction) String() string {
	switch p {
	case AdAction_AA_UNKNOWN:
		return "AdAction_AA_UNKNOWN"
	case AdAction_AA_LURK:
		return "AdAction_AA_LURK"
	case AdAction_AA_EXPOSE:
		return "AdAction_AA_EXPOSE"
	case AdAction_AA_CLK:
		return "AdAction_AA_CLK"
	case AdAction_AA_CLK_INNER:
		return "AdAction_AA_CLK_INNER"
	case AdAction_AA_VIDEO:
		return "AdAction_AA_VIDEO"
	case AdAction_AA_VIDEO_DONE:
		return "AdAction_AA_VIDEO_DONE"
	case AdAction_AA_ACT:
		return "AdAction_AA_ACT"
	case AdAction_AA_BUY:
		return "AdAction_AA_BUY"
	case AdAction_AA_DOWNLOAD:
		return "AdAction_AA_DOWNLOAD"
	case AdAction_AA_REGISTER:
		return "AdAction_AA_REGISTER"
	case AdAction_AA_LIKE:
		return "AdAction_AA_LIKE"
	case AdAction_AA_COMMENT:
		return "AdAction_AA_COMMENT"
	case AdAction_AA_FORWARD:
		return "AdAction_AA_FORWARD"
	case AdAction_AA_FOCUS:
		return "AdAction_AA_FOCUS"
	}
	return "<UNSET>"
}

func AdActionFromString(s string) (AdAction, error) {
	switch s {
	case "AdAction_AA_UNKNOWN":
		return AdAction_AA_UNKNOWN, nil
	case "AdAction_AA_LURK":
		return AdAction_AA_LURK, nil
	case "AdAction_AA_EXPOSE":
		return AdAction_AA_EXPOSE, nil
	case "AdAction_AA_CLK":
		return AdAction_AA_CLK, nil
	case "AdAction_AA_CLK_INNER":
		return AdAction_AA_CLK_INNER, nil
	case "AdAction_AA_VIDEO":
		return AdAction_AA_VIDEO, nil
	case "AdAction_AA_VIDEO_DONE":
		return AdAction_AA_VIDEO_DONE, nil
	case "AdAction_AA_ACT":
		return AdAction_AA_ACT, nil
	case "AdAction_AA_BUY":
		return AdAction_AA_BUY, nil
	case "AdAction_AA_DOWNLOAD":
		return AdAction_AA_DOWNLOAD, nil
	case "AdAction_AA_REGISTER":
		return AdAction_AA_REGISTER, nil
	case "AdAction_AA_LIKE":
		return AdAction_AA_LIKE, nil
	case "AdAction_AA_COMMENT":
		return AdAction_AA_COMMENT, nil
	case "AdAction_AA_FORWARD":
		return AdAction_AA_FORWARD, nil
	case "AdAction_AA_FOCUS":
		return AdAction_AA_FOCUS, nil
	}
	return AdAction(math.MinInt32 - 1), fmt.Errorf("not a valid AdAction string")
}

//渠道类型
type ChannelType int64

const (
	ChannelType_CT_UNKNOWN ChannelType = 0
	ChannelType_CT_IMG     ChannelType = 1
	ChannelType_CT_VIDEO   ChannelType = 2
)

func (p ChannelType) String() string {
	switch p {
	case ChannelType_CT_UNKNOWN:
		return "ChannelType_CT_UNKNOWN"
	case ChannelType_CT_IMG:
		return "ChannelType_CT_IMG"
	case ChannelType_CT_VIDEO:
		return "ChannelType_CT_VIDEO"
	}
	return "<UNSET>"
}

func ChannelTypeFromString(s string) (ChannelType, error) {
	switch s {
	case "ChannelType_CT_UNKNOWN":
		return ChannelType_CT_UNKNOWN, nil
	case "ChannelType_CT_IMG":
		return ChannelType_CT_IMG, nil
	case "ChannelType_CT_VIDEO":
		return ChannelType_CT_VIDEO, nil
	}
	return ChannelType(math.MinInt32 - 1), fmt.Errorf("not a valid ChannelType string")
}

type FileStatus int64

const (
	FileStatus_FS_UNKNOWN   FileStatus = 0
	FileStatus_FS_NEW       FileStatus = 1
	FileStatus_FS_SCANNED   FileStatus = 2
	FileStatus_FS_COMMITTED FileStatus = 3
)

func (p FileStatus) String() string {
	switch p {
	case FileStatus_FS_UNKNOWN:
		return "FileStatus_FS_UNKNOWN"
	case FileStatus_FS_NEW:
		return "FileStatus_FS_NEW"
	case FileStatus_FS_SCANNED:
		return "FileStatus_FS_SCANNED"
	case FileStatus_FS_COMMITTED:
		return "FileStatus_FS_COMMITTED"
	}
	return "<UNSET>"
}

func FileStatusFromString(s string) (FileStatus, error) {
	switch s {
	case "FileStatus_FS_UNKNOWN":
		return FileStatus_FS_UNKNOWN, nil
	case "FileStatus_FS_NEW":
		return FileStatus_FS_NEW, nil
	case "FileStatus_FS_SCANNED":
		return FileStatus_FS_SCANNED, nil
	case "FileStatus_FS_COMMITTED":
		return FileStatus_FS_COMMITTED, nil
	}
	return FileStatus(math.MinInt32 - 1), fmt.Errorf("not a valid FileStatus string")
}

type LifeCycleSettingDefaultType int64

const (
	LifeCycleSettingDefaultType_LSDT_OTHER     LifeCycleSettingDefaultType = 0
	LifeCycleSettingDefaultType_LSDT_AWARENESS LifeCycleSettingDefaultType = 1
	LifeCycleSettingDefaultType_LSDT_FAMILIAR  LifeCycleSettingDefaultType = 2
	LifeCycleSettingDefaultType_LSDT_INTEREST  LifeCycleSettingDefaultType = 3
	LifeCycleSettingDefaultType_LSDT_FANS      LifeCycleSettingDefaultType = 4
)

func (p LifeCycleSettingDefaultType) String() string {
	switch p {
	case LifeCycleSettingDefaultType_LSDT_OTHER:
		return "LifeCycleSettingDefaultType_LSDT_OTHER"
	case LifeCycleSettingDefaultType_LSDT_AWARENESS:
		return "LifeCycleSettingDefaultType_LSDT_AWARENESS"
	case LifeCycleSettingDefaultType_LSDT_FAMILIAR:
		return "LifeCycleSettingDefaultType_LSDT_FAMILIAR"
	case LifeCycleSettingDefaultType_LSDT_INTEREST:
		return "LifeCycleSettingDefaultType_LSDT_INTEREST"
	case LifeCycleSettingDefaultType_LSDT_FANS:
		return "LifeCycleSettingDefaultType_LSDT_FANS"
	}
	return "<UNSET>"
}

func LifeCycleSettingDefaultTypeFromString(s string) (LifeCycleSettingDefaultType, error) {
	switch s {
	case "LifeCycleSettingDefaultType_LSDT_OTHER":
		return LifeCycleSettingDefaultType_LSDT_OTHER, nil
	case "LifeCycleSettingDefaultType_LSDT_AWARENESS":
		return LifeCycleSettingDefaultType_LSDT_AWARENESS, nil
	case "LifeCycleSettingDefaultType_LSDT_FAMILIAR":
		return LifeCycleSettingDefaultType_LSDT_FAMILIAR, nil
	case "LifeCycleSettingDefaultType_LSDT_INTEREST":
		return LifeCycleSettingDefaultType_LSDT_INTEREST, nil
	case "LifeCycleSettingDefaultType_LSDT_FANS":
		return LifeCycleSettingDefaultType_LSDT_FANS, nil
	}
	return LifeCycleSettingDefaultType(math.MinInt32 - 1), fmt.Errorf("not a valid LifeCycleSettingDefaultType string")
}

type RuleSettingOption int64

const (
	RuleSettingOption_LCSO_UNKNOWN RuleSettingOption = 0
	RuleSettingOption_LCSO_INCLUDE RuleSettingOption = 1
	RuleSettingOption_LCSO_EXCLUDE RuleSettingOption = 2
)

func (p RuleSettingOption) String() string {
	switch p {
	case RuleSettingOption_LCSO_UNKNOWN:
		return "RuleSettingOption_LCSO_UNKNOWN"
	case RuleSettingOption_LCSO_INCLUDE:
		return "RuleSettingOption_LCSO_INCLUDE"
	case RuleSettingOption_LCSO_EXCLUDE:
		return "RuleSettingOption_LCSO_EXCLUDE"
	}
	return "<UNSET>"
}

func RuleSettingOptionFromString(s string) (RuleSettingOption, error) {
	switch s {
	case "RuleSettingOption_LCSO_UNKNOWN":
		return RuleSettingOption_LCSO_UNKNOWN, nil
	case "RuleSettingOption_LCSO_INCLUDE":
		return RuleSettingOption_LCSO_INCLUDE, nil
	case "RuleSettingOption_LCSO_EXCLUDE":
		return RuleSettingOption_LCSO_EXCLUDE, nil
	}
	return RuleSettingOption(math.MinInt32 - 1), fmt.Errorf("not a valid RuleSettingOption string")
}

type LifeCycleType int64

const (
	LifeCycleType_LCT_UNKNOWN   LifeCycleType = 0
	LifeCycleType_LCT_CUSTOM    LifeCycleType = 1
	LifeCycleType_LCT_POTENTIAL LifeCycleType = 2
	LifeCycleType_LCT_AWAKENING LifeCycleType = 3
)

func (p LifeCycleType) String() string {
	switch p {
	case LifeCycleType_LCT_UNKNOWN:
		return "LifeCycleType_LCT_UNKNOWN"
	case LifeCycleType_LCT_CUSTOM:
		return "LifeCycleType_LCT_CUSTOM"
	case LifeCycleType_LCT_POTENTIAL:
		return "LifeCycleType_LCT_POTENTIAL"
	case LifeCycleType_LCT_AWAKENING:
		return "LifeCycleType_LCT_AWAKENING"
	}
	return "<UNSET>"
}

func LifeCycleTypeFromString(s string) (LifeCycleType, error) {
	switch s {
	case "LifeCycleType_LCT_UNKNOWN":
		return LifeCycleType_LCT_UNKNOWN, nil
	case "LifeCycleType_LCT_CUSTOM":
		return LifeCycleType_LCT_CUSTOM, nil
	case "LifeCycleType_LCT_POTENTIAL":
		return LifeCycleType_LCT_POTENTIAL, nil
	case "LifeCycleType_LCT_AWAKENING":
		return LifeCycleType_LCT_AWAKENING, nil
	}
	return LifeCycleType(math.MinInt32 - 1), fmt.Errorf("not a valid LifeCycleType string")
}

type AppLaunchType int64

const (
	AppLaunchType_ALT_UNKNOWN AppLaunchType = 0
	AppLaunchType_ALT_COMMON  AppLaunchType = 1
	AppLaunchType_ALT_EXPAND  AppLaunchType = 2
)

func (p AppLaunchType) String() string {
	switch p {
	case AppLaunchType_ALT_UNKNOWN:
		return "AppLaunchType_ALT_UNKNOWN"
	case AppLaunchType_ALT_COMMON:
		return "AppLaunchType_ALT_COMMON"
	case AppLaunchType_ALT_EXPAND:
		return "AppLaunchType_ALT_EXPAND"
	}
	return "<UNSET>"
}

func AppLaunchTypeFromString(s string) (AppLaunchType, error) {
	switch s {
	case "AppLaunchType_ALT_UNKNOWN":
		return AppLaunchType_ALT_UNKNOWN, nil
	case "AppLaunchType_ALT_COMMON":
		return AppLaunchType_ALT_COMMON, nil
	case "AppLaunchType_ALT_EXPAND":
		return AppLaunchType_ALT_EXPAND, nil
	}
	return AppLaunchType(math.MinInt32 - 1), fmt.Errorf("not a valid AppLaunchType string")
}

type PromotionParams struct {
	Uids []int32 `thrift:"uids,1" json:"uids"`
	Name string  `thrift:"name,2" json:"name"`
	Desc string  `thrift:"desc,3" json:"desc"`
	Ids  []int32 `thrift:"ids,4" json:"ids"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Offset    int32 `thrift:"offset,11" json:"offset"`
	Limit     int32 `thrift:"limit,12" json:"limit"`
	Ascending bool  `thrift:"ascending,13" json:"ascending"`
}

func NewPromotionParams() *PromotionParams {
	return &PromotionParams{}
}

func (p *PromotionParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PromotionParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Uids = append(p.Uids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PromotionParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PromotionParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *PromotionParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.Ids = append(p.Ids, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PromotionParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *PromotionParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *PromotionParams) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *PromotionParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PromotionParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PromotionParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *PromotionParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *PromotionParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:desc: %s", p, err)
	}
	return err
}

func (p *PromotionParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ids: %s", p, err)
		}
	}
	return err
}

func (p *PromotionParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:offset: %s", p, err)
	}
	return err
}

func (p *PromotionParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:limit: %s", p, err)
	}
	return err
}

func (p *PromotionParams) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ascending: %s", p, err)
	}
	return err
}

func (p *PromotionParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromotionParams(%+v)", *p)
}

type ChannelParams struct {
	Uids  []int32 `thrift:"uids,1" json:"uids"`
	Name  string  `thrift:"name,2" json:"name"`
	Media string  `thrift:"media,3" json:"media"`
	Ids   []int32 `thrift:"ids,4" json:"ids"`
	Types []int32 `thrift:"types,5" json:"types"`
	Size  int32   `thrift:"size,6" json:"size"`
	Desc  string  `thrift:"desc,7" json:"desc"`
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Offset    int32 `thrift:"offset,11" json:"offset"`
	Limit     int32 `thrift:"limit,12" json:"limit"`
	Ascending bool  `thrift:"ascending,13" json:"ascending"`
}

func NewChannelParams() *ChannelParams {
	return &ChannelParams{}
}

func (p *ChannelParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChannelParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.Uids = append(p.Uids, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ChannelParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ChannelParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *ChannelParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.Ids = append(p.Ids, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ChannelParams) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Types = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = v
		}
		p.Types = append(p.Types, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ChannelParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *ChannelParams) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *ChannelParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ChannelParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ChannelParams) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ChannelParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ChannelParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChannelParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *ChannelParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *ChannelParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:media: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Media)); err != nil {
		return fmt.Errorf("%T.media (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:media: %s", p, err)
	}
	return err
}

func (p *ChannelParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ids: %s", p, err)
		}
	}
	return err
}

func (p *ChannelParams) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Types != nil {
		if err := oprot.WriteFieldBegin("types", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:types: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Types)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Types {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:types: %s", p, err)
		}
	}
	return err
}

func (p *ChannelParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:size: %s", p, err)
	}
	return err
}

func (p *ChannelParams) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:desc: %s", p, err)
	}
	return err
}

func (p *ChannelParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:offset: %s", p, err)
	}
	return err
}

func (p *ChannelParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:limit: %s", p, err)
	}
	return err
}

func (p *ChannelParams) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ascending: %s", p, err)
	}
	return err
}

func (p *ChannelParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChannelParams(%+v)", *p)
}

type CrowdScheduleParams struct {
	Platforms []PlatformType   `thrift:"platforms,1" json:"platforms"`
	Statuss   []ScheduleStatus `thrift:"statuss,2" json:"statuss"`
}

func NewCrowdScheduleParams() *CrowdScheduleParams {
	return &CrowdScheduleParams{}
}

func (p *CrowdScheduleParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdScheduleParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Platforms = make([]PlatformType, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 PlatformType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = PlatformType(v)
		}
		p.Platforms = append(p.Platforms, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdScheduleParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Statuss = make([]ScheduleStatus, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 ScheduleStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = ScheduleStatus(v)
		}
		p.Statuss = append(p.Statuss, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdScheduleParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdScheduleParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdScheduleParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Platforms != nil {
		if err := oprot.WriteFieldBegin("platforms", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:platforms: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Platforms)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Platforms {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:platforms: %s", p, err)
		}
	}
	return err
}

func (p *CrowdScheduleParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Statuss != nil {
		if err := oprot.WriteFieldBegin("statuss", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:statuss: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Statuss)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Statuss {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:statuss: %s", p, err)
		}
	}
	return err
}

func (p *CrowdScheduleParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdScheduleParams(%+v)", *p)
}

type CrowdParams struct {
	Uids         []int32                `thrift:"uids,1" json:"uids"`
	Name         string                 `thrift:"name,2" json:"name"`
	Desc         string                 `thrift:"desc,3" json:"desc"`
	Source       string                 `thrift:"source,4" json:"source"`
	Ids          []int32                `thrift:"ids,5" json:"ids"`
	Schedules    []*CrowdScheduleParams `thrift:"schedules,6" json:"schedules"`
	PromotionIds []int32                `thrift:"promotionIds,7" json:"promotionIds"`
	ChannelIds   []int32                `thrift:"channelIds,8" json:"channelIds"`
	MixText      string                 `thrift:"mixText,9" json:"mixText"`
	DeviceTypes  []int32                `thrift:"deviceTypes,10" json:"deviceTypes"`
	AdActions    []int32                `thrift:"adActions,11" json:"adActions"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	Offset    int32 `thrift:"offset,21" json:"offset"`
	Limit     int32 `thrift:"limit,22" json:"limit"`
	Ascending bool  `thrift:"ascending,23" json:"ascending"`
}

func NewCrowdParams() *CrowdParams {
	return &CrowdParams{}
}

func (p *CrowdParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.Uids = append(p.Uids, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CrowdParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *CrowdParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *CrowdParams) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.Ids = append(p.Ids, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdParams) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Schedules = make([]*CrowdScheduleParams, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewCrowdScheduleParams()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.Schedules = append(p.Schedules, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdParams) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PromotionIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.PromotionIds = append(p.PromotionIds, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdParams) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChannelIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.ChannelIds = append(p.ChannelIds, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *CrowdParams) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DeviceTypes = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.DeviceTypes = append(p.DeviceTypes, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdParams) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdActions = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.AdActions = append(p.AdActions, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdParams) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *CrowdParams) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *CrowdParams) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *CrowdParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *CrowdParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *CrowdParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:desc: %s", p, err)
	}
	return err
}

func (p *CrowdParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:source: %s", p, err)
	}
	return err
}

func (p *CrowdParams) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:ids: %s", p, err)
		}
	}
	return err
}

func (p *CrowdParams) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Schedules != nil {
		if err := oprot.WriteFieldBegin("schedules", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:schedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Schedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Schedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:schedules: %s", p, err)
		}
	}
	return err
}

func (p *CrowdParams) writeField7(oprot thrift.TProtocol) (err error) {
	if p.PromotionIds != nil {
		if err := oprot.WriteFieldBegin("promotionIds", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:promotionIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PromotionIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PromotionIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:promotionIds: %s", p, err)
		}
	}
	return err
}

func (p *CrowdParams) writeField8(oprot thrift.TProtocol) (err error) {
	if p.ChannelIds != nil {
		if err := oprot.WriteFieldBegin("channelIds", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:channelIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ChannelIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChannelIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:channelIds: %s", p, err)
		}
	}
	return err
}

func (p *CrowdParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:mixText: %s", p, err)
	}
	return err
}

func (p *CrowdParams) writeField10(oprot thrift.TProtocol) (err error) {
	if p.DeviceTypes != nil {
		if err := oprot.WriteFieldBegin("deviceTypes", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:deviceTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DeviceTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DeviceTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:deviceTypes: %s", p, err)
		}
	}
	return err
}

func (p *CrowdParams) writeField11(oprot thrift.TProtocol) (err error) {
	if p.AdActions != nil {
		if err := oprot.WriteFieldBegin("adActions", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:adActions: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdActions)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdActions {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:adActions: %s", p, err)
		}
	}
	return err
}

func (p *CrowdParams) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:offset: %s", p, err)
	}
	return err
}

func (p *CrowdParams) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:limit: %s", p, err)
	}
	return err
}

func (p *CrowdParams) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:ascending: %s", p, err)
	}
	return err
}

func (p *CrowdParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdParams(%+v)", *p)
}

type LifeCycleParams struct {
	Uids       []int32 `thrift:"uids,1" json:"uids"`
	Name       string  `thrift:"name,2" json:"name"`
	Remark     string  `thrift:"remark,3" json:"remark"`
	Ids        []int32 `thrift:"ids,4" json:"ids"`
	BuildTypes []int32 `thrift:"buildTypes,5" json:"buildTypes"`
	GroupIds   []int32 `thrift:"groupIds,6" json:"groupIds"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	PromotionIds []int32 `thrift:"promotionIds,11" json:"promotionIds"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	MixText string `thrift:"mixText,21" json:"mixText"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Offset    int32 `thrift:"offset,41" json:"offset"`
	Limit     int32 `thrift:"limit,42" json:"limit"`
	Ascending bool  `thrift:"ascending,43" json:"ascending"`
}

func NewLifeCycleParams() *LifeCycleParams {
	return &LifeCycleParams{
		Ascending: true,
	}
}

func (p *LifeCycleParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.Uids = append(p.Uids, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *LifeCycleParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *LifeCycleParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = v
		}
		p.Ids = append(p.Ids, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleParams) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.BuildTypes = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem16 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem16 = v
		}
		p.BuildTypes = append(p.BuildTypes, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleParams) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GroupIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem17 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem17 = v
		}
		p.GroupIds = append(p.GroupIds, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleParams) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PromotionIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem18 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem18 = v
		}
		p.PromotionIds = append(p.PromotionIds, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleParams) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *LifeCycleParams) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *LifeCycleParams) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *LifeCycleParams) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *LifeCycleParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LifeCycleParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *LifeCycleParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:remark: %s", p, err)
	}
	return err
}

func (p *LifeCycleParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ids: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleParams) writeField5(oprot thrift.TProtocol) (err error) {
	if p.BuildTypes != nil {
		if err := oprot.WriteFieldBegin("buildTypes", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:buildTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.BuildTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.BuildTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:buildTypes: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleParams) writeField6(oprot thrift.TProtocol) (err error) {
	if p.GroupIds != nil {
		if err := oprot.WriteFieldBegin("groupIds", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:groupIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.GroupIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GroupIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:groupIds: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleParams) writeField11(oprot thrift.TProtocol) (err error) {
	if p.PromotionIds != nil {
		if err := oprot.WriteFieldBegin("promotionIds", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:promotionIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PromotionIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PromotionIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:promotionIds: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleParams) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:mixText: %s", p, err)
	}
	return err
}

func (p *LifeCycleParams) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:offset: %s", p, err)
	}
	return err
}

func (p *LifeCycleParams) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:limit: %s", p, err)
	}
	return err
}

func (p *LifeCycleParams) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:ascending: %s", p, err)
	}
	return err
}

func (p *LifeCycleParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCycleParams(%+v)", *p)
}

type AppLaunchParams struct {
	Uids      []int32 `thrift:"uids,1" json:"uids"`
	Name      string  `thrift:"name,2" json:"name"`
	Desc      string  `thrift:"desc,3" json:"desc"`
	Ids       []int32 `thrift:"ids,4" json:"ids"`
	Platforms []int32 `thrift:"platforms,5" json:"platforms"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Statuss     []ScheduleStatus `thrift:"statuss,11" json:"statuss"`
	MixText     string           `thrift:"mixText,12" json:"mixText"`
	DeviceTypes []int32          `thrift:"deviceTypes,13" json:"deviceTypes"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	Offset    int32 `thrift:"offset,21" json:"offset"`
	Limit     int32 `thrift:"limit,22" json:"limit"`
	Ascending bool  `thrift:"ascending,23" json:"ascending"`
}

func NewAppLaunchParams() *AppLaunchParams {
	return &AppLaunchParams{}
}

func (p *AppLaunchParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppLaunchParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem19 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem19 = v
		}
		p.Uids = append(p.Uids, _elem19)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunchParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AppLaunchParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *AppLaunchParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem20 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem20 = v
		}
		p.Ids = append(p.Ids, _elem20)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunchParams) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Platforms = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem21 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem21 = v
		}
		p.Platforms = append(p.Platforms, _elem21)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunchParams) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Statuss = make([]ScheduleStatus, 0, size)
	for i := 0; i < size; i++ {
		var _elem22 ScheduleStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem22 = ScheduleStatus(v)
		}
		p.Statuss = append(p.Statuss, _elem22)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunchParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *AppLaunchParams) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DeviceTypes = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem23 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem23 = v
		}
		p.DeviceTypes = append(p.DeviceTypes, _elem23)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunchParams) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AppLaunchParams) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AppLaunchParams) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *AppLaunchParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppLaunchParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppLaunchParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunchParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *AppLaunchParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:desc: %s", p, err)
	}
	return err
}

func (p *AppLaunchParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ids: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunchParams) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Platforms != nil {
		if err := oprot.WriteFieldBegin("platforms", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:platforms: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Platforms)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Platforms {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:platforms: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunchParams) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Statuss != nil {
		if err := oprot.WriteFieldBegin("statuss", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:statuss: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Statuss)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Statuss {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:statuss: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunchParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:mixText: %s", p, err)
	}
	return err
}

func (p *AppLaunchParams) writeField13(oprot thrift.TProtocol) (err error) {
	if p.DeviceTypes != nil {
		if err := oprot.WriteFieldBegin("deviceTypes", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:deviceTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DeviceTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DeviceTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:deviceTypes: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunchParams) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:offset: %s", p, err)
	}
	return err
}

func (p *AppLaunchParams) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:limit: %s", p, err)
	}
	return err
}

func (p *AppLaunchParams) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:ascending: %s", p, err)
	}
	return err
}

func (p *AppLaunchParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppLaunchParams(%+v)", *p)
}

type Promotion struct {
	Id          int32  `thrift:"id,1" json:"id"`
	Uid         int32  `thrift:"uid,2" json:"uid"`
	Name        string `thrift:"name,3" json:"name"`
	Description string `thrift:"description,4" json:"description"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Deleted    DeleteStatus `thrift:"deleted,31" json:"deleted"`
	CreateTime int64        `thrift:"createTime,32" json:"createTime"`
	LastUpdate int64        `thrift:"lastUpdate,33" json:"lastUpdate"`
}

func NewPromotion() *Promotion {
	return &Promotion{
		Deleted: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Promotion) IsSetDeleted() bool {
	return int64(p.Deleted) != math.MinInt32-1
}

func (p *Promotion) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Promotion) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Promotion) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Promotion) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Promotion) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Promotion) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Deleted = DeleteStatus(v)
	}
	return nil
}

func (p *Promotion) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Promotion) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Promotion) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Promotion"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Promotion) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:description: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeleted() {
		if err := oprot.WriteFieldBegin("deleted", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:deleted: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
			return fmt.Errorf("%T.deleted (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:deleted: %s", p, err)
		}
	}
	return err
}

func (p *Promotion) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Promotion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Promotion(%+v)", *p)
}

type Channel struct {
	Id          int32       `thrift:"id,1" json:"id"`
	Uid         int32       `thrift:"uid,2" json:"uid"`
	Name        string      `thrift:"name,3" json:"name"`
	Media       string      `thrift:"media,4" json:"media"`
	ChannelType ChannelType `thrift:"channelType,5" json:"channelType"`
	Size        int32       `thrift:"size,6" json:"size"`
	Description string      `thrift:"description,7" json:"description"`
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Deleted    DeleteStatus `thrift:"deleted,31" json:"deleted"`
	CreateTime int64        `thrift:"createTime,32" json:"createTime"`
	LastUpdate int64        `thrift:"lastUpdate,33" json:"lastUpdate"`
}

func NewChannel() *Channel {
	return &Channel{
		ChannelType: math.MinInt32 - 1, // unset sentinal value

		Deleted: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Channel) IsSetChannelType() bool {
	return int64(p.ChannelType) != math.MinInt32-1
}

func (p *Channel) IsSetDeleted() bool {
	return int64(p.Deleted) != math.MinInt32-1
}

func (p *Channel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I64 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Channel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Channel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Channel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Channel) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *Channel) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChannelType = ChannelType(v)
	}
	return nil
}

func (p *Channel) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *Channel) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Channel) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Deleted = DeleteStatus(v)
	}
	return nil
}

func (p *Channel) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Channel) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Channel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Channel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Channel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Channel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Channel) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Channel) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:media: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Media)); err != nil {
		return fmt.Errorf("%T.media (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:media: %s", p, err)
	}
	return err
}

func (p *Channel) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannelType() {
		if err := oprot.WriteFieldBegin("channelType", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:channelType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
			return fmt.Errorf("%T.channelType (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:channelType: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:size: %s", p, err)
	}
	return err
}

func (p *Channel) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:description: %s", p, err)
	}
	return err
}

func (p *Channel) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeleted() {
		if err := oprot.WriteFieldBegin("deleted", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:deleted: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
			return fmt.Errorf("%T.deleted (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:deleted: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *Channel) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Channel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Channel(%+v)", *p)
}

type InsightSchedule struct {
	Id              int32           `thrift:"id,1" json:"id"`
	Category        CompassCategory `thrift:"category,2" json:"category"`
	CategoryId      int32           `thrift:"categoryId,3" json:"categoryId"`
	SearchInsightId int32           `thrift:"searchInsightId,4" json:"searchInsightId"`
	Platform        PlatformType    `thrift:"platform,5" json:"platform"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Status ScheduleStatus `thrift:"status,11" json:"status"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	CommitDt int64 `thrift:"commitDt,21" json:"commitDt"`
	FinishDt int64 `thrift:"finishDt,22" json:"finishDt"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Deleted int32 `thrift:"deleted,31" json:"deleted"`
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	CreateTime int64 `thrift:"createTime,41" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,42" json:"lastUpdate"`
}

func NewInsightSchedule() *InsightSchedule {
	return &InsightSchedule{
		Category: math.MinInt32 - 1, // unset sentinal value

		Platform: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *InsightSchedule) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *InsightSchedule) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *InsightSchedule) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *InsightSchedule) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I64 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *InsightSchedule) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *InsightSchedule) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = CompassCategory(v)
	}
	return nil
}

func (p *InsightSchedule) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CategoryId = v
	}
	return nil
}

func (p *InsightSchedule) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SearchInsightId = v
	}
	return nil
}

func (p *InsightSchedule) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Platform = PlatformType(v)
	}
	return nil
}

func (p *InsightSchedule) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Status = ScheduleStatus(v)
	}
	return nil
}

func (p *InsightSchedule) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CommitDt = v
	}
	return nil
}

func (p *InsightSchedule) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.FinishDt = v
	}
	return nil
}

func (p *InsightSchedule) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *InsightSchedule) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *InsightSchedule) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *InsightSchedule) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("InsightSchedule"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *InsightSchedule) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *InsightSchedule) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("categoryId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:categoryId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CategoryId)); err != nil {
		return fmt.Errorf("%T.categoryId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:categoryId: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchInsightId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:searchInsightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SearchInsightId)); err != nil {
		return fmt.Errorf("%T.searchInsightId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:searchInsightId: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:platform: %s", p, err)
		}
	}
	return err
}

func (p *InsightSchedule) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:status: %s", p, err)
		}
	}
	return err
}

func (p *InsightSchedule) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("commitDt", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:commitDt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CommitDt)); err != nil {
		return fmt.Errorf("%T.commitDt (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:commitDt: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("finishDt", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:finishDt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FinishDt)); err != nil {
		return fmt.Errorf("%T.finishDt (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:finishDt: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:deleted: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:createTime: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:lastUpdate: %s", p, err)
	}
	return err
}

func (p *InsightSchedule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("InsightSchedule(%+v)", *p)
}

type PushSchedule struct {
	Id           int32        `thrift:"id,1" json:"id"`
	AppId        int32        `thrift:"appId,2" json:"appId"`
	SearchPushId int32        `thrift:"searchPushId,3" json:"searchPushId"`
	Platform     PlatformType `thrift:"platform,4" json:"platform"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	TsaUser int32 `thrift:"tsaUser,11" json:"tsaUser"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	Status ScheduleStatus `thrift:"status,21" json:"status"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	CommitDt int64 `thrift:"commitDt,31" json:"commitDt"`
	FinishDt int64 `thrift:"finishDt,32" json:"finishDt"`
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Deleted int32 `thrift:"deleted,41" json:"deleted"`
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	CreateTime int64 `thrift:"createTime,51" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,52" json:"lastUpdate"`
}

func NewPushSchedule() *PushSchedule {
	return &PushSchedule{
		Platform: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PushSchedule) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *PushSchedule) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *PushSchedule) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I64 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I64 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PushSchedule) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PushSchedule) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *PushSchedule) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SearchPushId = v
	}
	return nil
}

func (p *PushSchedule) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Platform = PlatformType(v)
	}
	return nil
}

func (p *PushSchedule) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.TsaUser = v
	}
	return nil
}

func (p *PushSchedule) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Status = ScheduleStatus(v)
	}
	return nil
}

func (p *PushSchedule) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.CommitDt = v
	}
	return nil
}

func (p *PushSchedule) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.FinishDt = v
	}
	return nil
}

func (p *PushSchedule) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *PushSchedule) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *PushSchedule) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *PushSchedule) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PushSchedule"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PushSchedule) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appId: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchPushId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:searchPushId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SearchPushId)); err != nil {
		return fmt.Errorf("%T.searchPushId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:searchPushId: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:platform: %s", p, err)
		}
	}
	return err
}

func (p *PushSchedule) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tsaUser", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:tsaUser: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TsaUser)); err != nil {
		return fmt.Errorf("%T.tsaUser (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:tsaUser: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (21) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:status: %s", p, err)
		}
	}
	return err
}

func (p *PushSchedule) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("commitDt", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:commitDt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CommitDt)); err != nil {
		return fmt.Errorf("%T.commitDt (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:commitDt: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("finishDt", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:finishDt: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FinishDt)); err != nil {
		return fmt.Errorf("%T.finishDt (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:finishDt: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:deleted: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:createTime: %s", p, err)
	}
	return err
}

func (p *PushSchedule) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:lastUpdate: %s", p, err)
	}
	return err
}

func (p *PushSchedule) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PushSchedule(%+v)", *p)
}

type DeviceFile struct {
	Id       int32  `thrift:"id,1" json:"id"`
	CrowdId  int32  `thrift:"crowdId,2" json:"crowdId"`
	FileName string `thrift:"fileName,3" json:"fileName"`
	FilePath string `thrift:"filePath,4" json:"filePath"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	FileSize   int64  `thrift:"fileSize,11" json:"fileSize"`
	FileMd5    string `thrift:"fileMd5,12" json:"fileMd5"`
	FileRecord int64  `thrift:"fileRecord,13" json:"fileRecord"`
	FileUnique int64  `thrift:"fileUnique,14" json:"fileUnique"`
	FileIgnore int64  `thrift:"fileIgnore,15" json:"fileIgnore"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Status  FileStatus   `thrift:"status,31" json:"status"`
	Deleted DeleteStatus `thrift:"deleted,32" json:"deleted"`
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	CreateTime int64 `thrift:"createTime,41" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,42" json:"lastUpdate"`
}

func NewDeviceFile() *DeviceFile {
	return &DeviceFile{
		Status: math.MinInt32 - 1, // unset sentinal value

		Deleted: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DeviceFile) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DeviceFile) IsSetDeleted() bool {
	return int64(p.Deleted) != math.MinInt32-1
}

func (p *DeviceFile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I64 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I64 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeviceFile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DeviceFile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CrowdId = v
	}
	return nil
}

func (p *DeviceFile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FileName = v
	}
	return nil
}

func (p *DeviceFile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FilePath = v
	}
	return nil
}

func (p *DeviceFile) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.FileSize = v
	}
	return nil
}

func (p *DeviceFile) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.FileMd5 = v
	}
	return nil
}

func (p *DeviceFile) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.FileRecord = v
	}
	return nil
}

func (p *DeviceFile) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.FileUnique = v
	}
	return nil
}

func (p *DeviceFile) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.FileIgnore = v
	}
	return nil
}

func (p *DeviceFile) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Status = FileStatus(v)
	}
	return nil
}

func (p *DeviceFile) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Deleted = DeleteStatus(v)
	}
	return nil
}

func (p *DeviceFile) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DeviceFile) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *DeviceFile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeviceFile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeviceFile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("crowdId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:crowdId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CrowdId)); err != nil {
		return fmt.Errorf("%T.crowdId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:crowdId: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fileName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileName)); err != nil {
		return fmt.Errorf("%T.fileName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fileName: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filePath", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:filePath: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FilePath)); err != nil {
		return fmt.Errorf("%T.filePath (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:filePath: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileSize", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:fileSize: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FileSize)); err != nil {
		return fmt.Errorf("%T.fileSize (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:fileSize: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileMd5", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:fileMd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileMd5)); err != nil {
		return fmt.Errorf("%T.fileMd5 (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:fileMd5: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileRecord", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:fileRecord: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FileRecord)); err != nil {
		return fmt.Errorf("%T.fileRecord (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:fileRecord: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileUnique", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:fileUnique: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FileUnique)); err != nil {
		return fmt.Errorf("%T.fileUnique (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:fileUnique: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileIgnore", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:fileIgnore: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FileIgnore)); err != nil {
		return fmt.Errorf("%T.fileIgnore (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:fileIgnore: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:status: %s", p, err)
		}
	}
	return err
}

func (p *DeviceFile) writeField32(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeleted() {
		if err := oprot.WriteFieldBegin("deleted", thrift.I32, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:deleted: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
			return fmt.Errorf("%T.deleted (32) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:deleted: %s", p, err)
		}
	}
	return err
}

func (p *DeviceFile) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:createTime: %s", p, err)
	}
	return err
}

func (p *DeviceFile) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:lastUpdate: %s", p, err)
	}
	return err
}

func (p *DeviceFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeviceFile(%+v)", *p)
}

type Crowd struct {
	Id            int32      `thrift:"id,1" json:"id"`
	Uid           int32      `thrift:"uid,2" json:"uid"`
	SearchCrowdId int64      `thrift:"searchCrowdId,3" json:"searchCrowdId"`
	Name          string     `thrift:"name,4" json:"name"`
	Description   string     `thrift:"description,5" json:"description"`
	Source        string     `thrift:"source,6" json:"source"`
	DeviceType    DeviceType `thrift:"deviceType,7" json:"deviceType"`
	AdAct         AdAction   `thrift:"adAct,8" json:"adAct"`
	PromotionId   int64      `thrift:"promotionId,9" json:"promotionId"`
	ChannelId     int64      `thrift:"channelId,10" json:"channelId"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	FileSession string        `thrift:"fileSession,31" json:"fileSession"`
	FileInfo    []*DeviceFile `thrift:"fileInfo,32" json:"fileInfo"`
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Schedules []*InsightSchedule `thrift:"schedules,41" json:"schedules"`
	Status    ScheduleStatus     `thrift:"status,42" json:"status"`
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	UvTotal int32 `thrift:"uvTotal,51" json:"uvTotal"`
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	Deleted DeleteStatus `thrift:"deleted,61" json:"deleted"`
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	UvCommitDt int32 `thrift:"uvCommitDt,71" json:"uvCommitDt"`
	UvFinishDt int32 `thrift:"uvFinishDt,72" json:"uvFinishDt"`
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	CreateTime int64 `thrift:"createTime,81" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,82" json:"lastUpdate"`
}

func NewCrowd() *Crowd {
	return &Crowd{
		DeviceType: math.MinInt32 - 1, // unset sentinal value

		AdAct: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Deleted: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Crowd) IsSetDeviceType() bool {
	return int64(p.DeviceType) != math.MinInt32-1
}

func (p *Crowd) IsSetAdAct() bool {
	return int64(p.AdAct) != math.MinInt32-1
}

func (p *Crowd) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Crowd) IsSetDeleted() bool {
	return int64(p.Deleted) != math.MinInt32-1
}

func (p *Crowd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.LIST {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.LIST {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I32 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.I32 {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.I32 {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 81:
			if fieldTypeId == thrift.I64 {
				if err := p.readField81(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 82:
			if fieldTypeId == thrift.I64 {
				if err := p.readField82(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Crowd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Crowd) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Crowd) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SearchCrowdId = v
	}
	return nil
}

func (p *Crowd) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Crowd) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Crowd) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *Crowd) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.DeviceType = DeviceType(v)
	}
	return nil
}

func (p *Crowd) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AdAct = AdAction(v)
	}
	return nil
}

func (p *Crowd) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *Crowd) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *Crowd) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.FileSession = v
	}
	return nil
}

func (p *Crowd) readField32(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.FileInfo = make([]*DeviceFile, 0, size)
	for i := 0; i < size; i++ {
		_elem24 := NewDeviceFile()
		if err := _elem24.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem24)
		}
		p.FileInfo = append(p.FileInfo, _elem24)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Crowd) readField41(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Schedules = make([]*InsightSchedule, 0, size)
	for i := 0; i < size; i++ {
		_elem25 := NewInsightSchedule()
		if err := _elem25.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem25)
		}
		p.Schedules = append(p.Schedules, _elem25)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Crowd) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Status = ScheduleStatus(v)
	}
	return nil
}

func (p *Crowd) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.UvTotal = v
	}
	return nil
}

func (p *Crowd) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Deleted = DeleteStatus(v)
	}
	return nil
}

func (p *Crowd) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.UvCommitDt = v
	}
	return nil
}

func (p *Crowd) readField72(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 72: %s", err)
	} else {
		p.UvFinishDt = v
	}
	return nil
}

func (p *Crowd) readField81(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 81: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Crowd) readField82(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 82: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Crowd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Crowd"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField81(oprot); err != nil {
		return err
	}
	if err := p.writeField82(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Crowd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchCrowdId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:searchCrowdId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchCrowdId)); err != nil {
		return fmt.Errorf("%T.searchCrowdId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:searchCrowdId: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:description: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:source: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeviceType() {
		if err := oprot.WriteFieldBegin("deviceType", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:deviceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DeviceType)); err != nil {
			return fmt.Errorf("%T.deviceType (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:deviceType: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdAct() {
		if err := oprot.WriteFieldBegin("adAct", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:adAct: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdAct)); err != nil {
			return fmt.Errorf("%T.adAct (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:adAct: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:promotionId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:promotionId: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelId", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:channelId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channelId (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:channelId: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileSession", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:fileSession: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FileSession)); err != nil {
		return fmt.Errorf("%T.fileSession (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:fileSession: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField32(oprot thrift.TProtocol) (err error) {
	if p.FileInfo != nil {
		if err := oprot.WriteFieldBegin("fileInfo", thrift.LIST, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:fileInfo: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.FileInfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.FileInfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:fileInfo: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField41(oprot thrift.TProtocol) (err error) {
	if p.Schedules != nil {
		if err := oprot.WriteFieldBegin("schedules", thrift.LIST, 41); err != nil {
			return fmt.Errorf("%T write field begin error 41:schedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Schedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Schedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 41:schedules: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField42(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 42); err != nil {
			return fmt.Errorf("%T write field begin error 42:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (42) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 42:status: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvTotal", thrift.I32, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:uvTotal: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvTotal)); err != nil {
		return fmt.Errorf("%T.uvTotal (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:uvTotal: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField61(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeleted() {
		if err := oprot.WriteFieldBegin("deleted", thrift.I32, 61); err != nil {
			return fmt.Errorf("%T write field begin error 61:deleted: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
			return fmt.Errorf("%T.deleted (61) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 61:deleted: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvCommitDt", thrift.I32, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:uvCommitDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvCommitDt)); err != nil {
		return fmt.Errorf("%T.uvCommitDt (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:uvCommitDt: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField72(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvFinishDt", thrift.I32, 72); err != nil {
		return fmt.Errorf("%T write field begin error 72:uvFinishDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvFinishDt)); err != nil {
		return fmt.Errorf("%T.uvFinishDt (72) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 72:uvFinishDt: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField81(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 81); err != nil {
		return fmt.Errorf("%T write field begin error 81:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (81) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 81:createTime: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField82(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 82); err != nil {
		return fmt.Errorf("%T write field begin error 82:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (82) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 82:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Crowd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Crowd(%+v)", *p)
}

type LifeCycleSettingAttr struct {
	Id        int32 `thrift:"id,1" json:"id"`
	SettingId int32 `thrift:"settingId,2" json:"settingId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	DtB int32 `thrift:"dtB,11" json:"dtB"`
	DtE int32 `thrift:"dtE,12" json:"dtE"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	ChannelId   int32 `thrift:"channelId,21" json:"channelId"`
	PromotionId int32 `thrift:"promotionId,22" json:"promotionId"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	AdAct  AdAction `thrift:"adAct,31" json:"adAct"`
	Op     int32    `thrift:"op,32" json:"op"`
	Number int32    `thrift:"number,33" json:"number"`
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	Deleted    int32 `thrift:"deleted,61" json:"deleted"`
	CreateTime int32 `thrift:"createTime,62" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,63" json:"lastUpdate"`
}

func NewLifeCycleSettingAttr() *LifeCycleSettingAttr {
	return &LifeCycleSettingAttr{
		AdAct: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *LifeCycleSettingAttr) IsSetAdAct() bool {
	return int64(p.AdAct) != math.MinInt32-1
}

func (p *LifeCycleSettingAttr) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I32 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.I32 {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SettingId = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DtB = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.DtE = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.AdAct = AdAction(v)
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Op = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Number = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *LifeCycleSettingAttr) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LifeCycleSettingAttr"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleSettingAttr) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settingId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:settingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SettingId)); err != nil {
		return fmt.Errorf("%T.settingId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:settingId: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dtB", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:dtB: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DtB)); err != nil {
		return fmt.Errorf("%T.dtB (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:dtB: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dtE", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:dtE: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DtE)); err != nil {
		return fmt.Errorf("%T.dtE (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:dtE: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelId", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:channelId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channelId (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:channelId: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:promotionId: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdAct() {
		if err := oprot.WriteFieldBegin("adAct", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:adAct: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdAct)); err != nil {
			return fmt.Errorf("%T.adAct (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:adAct: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("op", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:op: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Op)); err != nil {
		return fmt.Errorf("%T.op (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:op: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("number", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:number: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Number)); err != nil {
		return fmt.Errorf("%T.number (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:number: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:deleted: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:createTime: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) writeField63(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 63); err != nil {
		return fmt.Errorf("%T write field begin error 63:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (63) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 63:lastUpdate: %s", p, err)
	}
	return err
}

func (p *LifeCycleSettingAttr) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCycleSettingAttr(%+v)", *p)
}

type LifeCycleSetting struct {
	Id          int32             `thrift:"id,1" json:"id"`
	LifeCycleId int32             `thrift:"LifeCycleId,2" json:"LifeCycleId"`
	InEx        RuleSettingOption `thrift:"inEx,3" json:"inEx"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Attrs []*LifeCycleSettingAttr `thrift:"attrs,11" json:"attrs"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	DefaultType LifeCycleSettingDefaultType `thrift:"defaultType,21" json:"defaultType"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Deleted    int32 `thrift:"deleted,31" json:"deleted"`
	CreateTime int32 `thrift:"createTime,32" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,33" json:"lastUpdate"`
}

func NewLifeCycleSetting() *LifeCycleSetting {
	return &LifeCycleSetting{
		InEx: math.MinInt32 - 1, // unset sentinal value

		DefaultType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *LifeCycleSetting) IsSetInEx() bool {
	return int64(p.InEx) != math.MinInt32-1
}

func (p *LifeCycleSetting) IsSetDefaultType() bool {
	return int64(p.DefaultType) != math.MinInt32-1
}

func (p *LifeCycleSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *LifeCycleSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.LifeCycleId = v
	}
	return nil
}

func (p *LifeCycleSetting) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.InEx = RuleSettingOption(v)
	}
	return nil
}

func (p *LifeCycleSetting) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Attrs = make([]*LifeCycleSettingAttr, 0, size)
	for i := 0; i < size; i++ {
		_elem26 := NewLifeCycleSettingAttr()
		if err := _elem26.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem26)
		}
		p.Attrs = append(p.Attrs, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleSetting) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.DefaultType = LifeCycleSettingDefaultType(v)
	}
	return nil
}

func (p *LifeCycleSetting) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *LifeCycleSetting) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *LifeCycleSetting) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *LifeCycleSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LifeCycleSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *LifeCycleSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("LifeCycleId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:LifeCycleId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LifeCycleId)); err != nil {
		return fmt.Errorf("%T.LifeCycleId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:LifeCycleId: %s", p, err)
	}
	return err
}

func (p *LifeCycleSetting) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInEx() {
		if err := oprot.WriteFieldBegin("inEx", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:inEx: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.InEx)); err != nil {
			return fmt.Errorf("%T.inEx (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:inEx: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:attrs: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Attrs {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:attrs: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleSetting) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetDefaultType() {
		if err := oprot.WriteFieldBegin("defaultType", thrift.I32, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:defaultType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DefaultType)); err != nil {
			return fmt.Errorf("%T.defaultType (21) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:defaultType: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleSetting) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:deleted: %s", p, err)
	}
	return err
}

func (p *LifeCycleSetting) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *LifeCycleSetting) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastUpdate: %s", p, err)
	}
	return err
}

func (p *LifeCycleSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCycleSetting(%+v)", *p)
}

type LifeCycle struct {
	Id                int32         `thrift:"id,1" json:"id"`
	SearchLifeCycleId int32         `thrift:"searchLifeCycleId,2" json:"searchLifeCycleId"`
	Uid               int32         `thrift:"uid,3" json:"uid"`
	Name              string        `thrift:"name,4" json:"name"`
	PromotionId       int32         `thrift:"promotionId,5" json:"promotionId"`
	Remark            string        `thrift:"remark,6" json:"remark"`
	BuildType         LifeCycleType `thrift:"buildType,7" json:"buildType"`
	GroupId           int32         `thrift:"groupId,8" json:"groupId"`
	// unused field # 9
	// unused field # 10
	Settings []*LifeCycleSetting `thrift:"settings,11" json:"settings"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	Schedules []*InsightSchedule `thrift:"schedules,21" json:"schedules"`
	Status    ScheduleStatus     `thrift:"status,22" json:"status"`
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	UvCurrent      int32 `thrift:"uvCurrent,31" json:"uvCurrent"`
	UvLast         int32 `thrift:"uvLast,32" json:"uvLast"`
	RepetitionRate int32 `thrift:"repetitionRate,33" json:"repetitionRate"`
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Deleted int32 `thrift:"deleted,41" json:"deleted"`
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	UvCommitDt int32 `thrift:"uvCommitDt,51" json:"uvCommitDt"`
	UvFinishDt int32 `thrift:"uvFinishDt,52" json:"uvFinishDt"`
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	CreateTime int32 `thrift:"createTime,61" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,62" json:"lastUpdate"`
}

func NewLifeCycle() *LifeCycle {
	return &LifeCycle{
		BuildType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *LifeCycle) IsSetBuildType() bool {
	return int64(p.BuildType) != math.MinInt32-1
}

func (p *LifeCycle) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *LifeCycle) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.LIST {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I32 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I32 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I32 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LifeCycle) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *LifeCycle) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchLifeCycleId = v
	}
	return nil
}

func (p *LifeCycle) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *LifeCycle) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *LifeCycle) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *LifeCycle) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *LifeCycle) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.BuildType = LifeCycleType(v)
	}
	return nil
}

func (p *LifeCycle) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.GroupId = v
	}
	return nil
}

func (p *LifeCycle) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Settings = make([]*LifeCycleSetting, 0, size)
	for i := 0; i < size; i++ {
		_elem27 := NewLifeCycleSetting()
		if err := _elem27.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem27)
		}
		p.Settings = append(p.Settings, _elem27)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycle) readField21(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Schedules = make([]*InsightSchedule, 0, size)
	for i := 0; i < size; i++ {
		_elem28 := NewInsightSchedule()
		if err := _elem28.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem28)
		}
		p.Schedules = append(p.Schedules, _elem28)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycle) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = ScheduleStatus(v)
	}
	return nil
}

func (p *LifeCycle) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.UvCurrent = v
	}
	return nil
}

func (p *LifeCycle) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.UvLast = v
	}
	return nil
}

func (p *LifeCycle) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.RepetitionRate = v
	}
	return nil
}

func (p *LifeCycle) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *LifeCycle) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.UvCommitDt = v
	}
	return nil
}

func (p *LifeCycle) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.UvFinishDt = v
	}
	return nil
}

func (p *LifeCycle) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *LifeCycle) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *LifeCycle) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LifeCycle"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LifeCycle) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchLifeCycleId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchLifeCycleId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SearchLifeCycleId)); err != nil {
		return fmt.Errorf("%T.searchLifeCycleId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchLifeCycleId: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:promotionId: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:remark: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetBuildType() {
		if err := oprot.WriteFieldBegin("buildType", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:buildType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BuildType)); err != nil {
			return fmt.Errorf("%T.buildType (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:buildType: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycle) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("groupId", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:groupId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GroupId)); err != nil {
		return fmt.Errorf("%T.groupId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:groupId: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Settings != nil {
		if err := oprot.WriteFieldBegin("settings", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:settings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Settings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Settings {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:settings: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycle) writeField21(oprot thrift.TProtocol) (err error) {
	if p.Schedules != nil {
		if err := oprot.WriteFieldBegin("schedules", thrift.LIST, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:schedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Schedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Schedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:schedules: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycle) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:status: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycle) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvCurrent", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:uvCurrent: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvCurrent)); err != nil {
		return fmt.Errorf("%T.uvCurrent (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:uvCurrent: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvLast", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:uvLast: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvLast)); err != nil {
		return fmt.Errorf("%T.uvLast (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:uvLast: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("repetitionRate", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:repetitionRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RepetitionRate)); err != nil {
		return fmt.Errorf("%T.repetitionRate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:repetitionRate: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:deleted: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvCommitDt", thrift.I32, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:uvCommitDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvCommitDt)); err != nil {
		return fmt.Errorf("%T.uvCommitDt (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:uvCommitDt: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvFinishDt", thrift.I32, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:uvFinishDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvFinishDt)); err != nil {
		return fmt.Errorf("%T.uvFinishDt (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:uvFinishDt: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:createTime: %s", p, err)
	}
	return err
}

func (p *LifeCycle) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:lastUpdate: %s", p, err)
	}
	return err
}

func (p *LifeCycle) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCycle(%+v)", *p)
}

type AppLaunchSettingAttr struct {
	Id        int32 `thrift:"id,1" json:"id"`
	SettingId int32 `thrift:"settingId,2" json:"settingId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	SourceType int32 `thrift:"sourceType,11" json:"sourceType"`
	SourceId   int32 `thrift:"sourceId,12" json:"sourceId"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Deleted    int32 `thrift:"deleted,31" json:"deleted"`
	CreateTime int32 `thrift:"createTime,32" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,33" json:"lastUpdate"`
}

func NewAppLaunchSettingAttr() *AppLaunchSettingAttr {
	return &AppLaunchSettingAttr{}
}

func (p *AppLaunchSettingAttr) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppLaunchSettingAttr) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AppLaunchSettingAttr) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SettingId = v
	}
	return nil
}

func (p *AppLaunchSettingAttr) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SourceType = v
	}
	return nil
}

func (p *AppLaunchSettingAttr) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SourceId = v
	}
	return nil
}

func (p *AppLaunchSettingAttr) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *AppLaunchSettingAttr) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AppLaunchSettingAttr) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *AppLaunchSettingAttr) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppLaunchSettingAttr"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppLaunchSettingAttr) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AppLaunchSettingAttr) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settingId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:settingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SettingId)); err != nil {
		return fmt.Errorf("%T.settingId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:settingId: %s", p, err)
	}
	return err
}

func (p *AppLaunchSettingAttr) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sourceType", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:sourceType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SourceType)); err != nil {
		return fmt.Errorf("%T.sourceType (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:sourceType: %s", p, err)
	}
	return err
}

func (p *AppLaunchSettingAttr) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sourceId", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sourceId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SourceId)); err != nil {
		return fmt.Errorf("%T.sourceId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sourceId: %s", p, err)
	}
	return err
}

func (p *AppLaunchSettingAttr) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:deleted: %s", p, err)
	}
	return err
}

func (p *AppLaunchSettingAttr) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *AppLaunchSettingAttr) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AppLaunchSettingAttr) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppLaunchSettingAttr(%+v)", *p)
}

type AppLaunchSetting struct {
	Id          int32             `thrift:"id,1" json:"id"`
	AppLaunchId int32             `thrift:"appLaunchId,2" json:"appLaunchId"`
	InEx        RuleSettingOption `thrift:"inEx,3" json:"inEx"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Attrs []*AppLaunchSettingAttr `thrift:"attrs,11" json:"attrs"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Deleted    int32 `thrift:"deleted,31" json:"deleted"`
	CreateTime int32 `thrift:"createTime,32" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,33" json:"lastUpdate"`
}

func NewAppLaunchSetting() *AppLaunchSetting {
	return &AppLaunchSetting{
		InEx: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppLaunchSetting) IsSetInEx() bool {
	return int64(p.InEx) != math.MinInt32-1
}

func (p *AppLaunchSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppLaunchSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AppLaunchSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppLaunchId = v
	}
	return nil
}

func (p *AppLaunchSetting) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.InEx = RuleSettingOption(v)
	}
	return nil
}

func (p *AppLaunchSetting) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Attrs = make([]*AppLaunchSettingAttr, 0, size)
	for i := 0; i < size; i++ {
		_elem29 := NewAppLaunchSettingAttr()
		if err := _elem29.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem29)
		}
		p.Attrs = append(p.Attrs, _elem29)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunchSetting) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *AppLaunchSetting) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AppLaunchSetting) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *AppLaunchSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppLaunchSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppLaunchSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AppLaunchSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appLaunchId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appLaunchId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppLaunchId)); err != nil {
		return fmt.Errorf("%T.appLaunchId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appLaunchId: %s", p, err)
	}
	return err
}

func (p *AppLaunchSetting) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInEx() {
		if err := oprot.WriteFieldBegin("inEx", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:inEx: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.InEx)); err != nil {
			return fmt.Errorf("%T.inEx (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:inEx: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunchSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:attrs: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Attrs {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:attrs: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunchSetting) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:deleted: %s", p, err)
	}
	return err
}

func (p *AppLaunchSetting) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:createTime: %s", p, err)
	}
	return err
}

func (p *AppLaunchSetting) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AppLaunchSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppLaunchSetting(%+v)", *p)
}

type AppLaunch struct {
	Id                int32  `thrift:"id,1" json:"id"`
	SearchAppLaunchId int32  `thrift:"searchAppLaunchId,2" json:"searchAppLaunchId"`
	SearchEffctId     int32  `thrift:"searchEffctId,3" json:"searchEffctId"`
	Uid               int32  `thrift:"uid,4" json:"uid"`
	Name              string `thrift:"name,5" json:"name"`
	Description       string `thrift:"description,6" json:"description"`
	ExpandId          int32  `thrift:"expandId,7" json:"expandId"`
	SearchPushId      int32  `thrift:"searchPushId,8" json:"searchPushId"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	DeviceType    DeviceType    `thrift:"deviceType,21" json:"deviceType"`
	BuildType     AppLaunchType `thrift:"buildType,22" json:"buildType"`
	Platform      PlatformType  `thrift:"platform,23" json:"platform"`
	UvExpand      int64         `thrift:"uvExpand,24" json:"uvExpand"`
	ScopeType     SeedScopeType `thrift:"scopeType,25" json:"scopeType"`
	ExpandUvTotal int64         `thrift:"expandUvTotal,26" json:"expandUvTotal"`
	GdtAccount    int32         `thrift:"gdtAccount,27" json:"gdtAccount"`
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Settings []*AppLaunchSetting `thrift:"settings,31" json:"settings"`
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	InsightSchedules []*InsightSchedule `thrift:"insightSchedules,41" json:"insightSchedules"`
	PushSchedules    []*PushSchedule    `thrift:"pushSchedules,42" json:"pushSchedules"`
	EffectStatus     ScheduleStatus     `thrift:"effectStatus,43" json:"effectStatus"`
	Status           ScheduleStatus     `thrift:"status,44" json:"status"`
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	UvTotal int32 `thrift:"uvTotal,51" json:"uvTotal"`
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	Deleted int32 `thrift:"deleted,61" json:"deleted"`
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	UvCommitDt     int32 `thrift:"uvCommitDt,71" json:"uvCommitDt"`
	UvFinishDt     int32 `thrift:"uvFinishDt,72" json:"uvFinishDt"`
	EffectCommitDt int32 `thrift:"effectCommitDt,73" json:"effectCommitDt"`
	EffectFinishDt int32 `thrift:"effectFinishDt,74" json:"effectFinishDt"`
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	CreateTime int32 `thrift:"createTime,81" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,82" json:"lastUpdate"`
}

func NewAppLaunch() *AppLaunch {
	return &AppLaunch{
		DeviceType: math.MinInt32 - 1, // unset sentinal value

		BuildType: math.MinInt32 - 1, // unset sentinal value

		Platform: math.MinInt32 - 1, // unset sentinal value

		ScopeType: math.MinInt32 - 1, // unset sentinal value

		EffectStatus: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppLaunch) IsSetDeviceType() bool {
	return int64(p.DeviceType) != math.MinInt32-1
}

func (p *AppLaunch) IsSetBuildType() bool {
	return int64(p.BuildType) != math.MinInt32-1
}

func (p *AppLaunch) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *AppLaunch) IsSetScopeType() bool {
	return int64(p.ScopeType) != math.MinInt32-1
}

func (p *AppLaunch) IsSetEffectStatus() bool {
	return int64(p.EffectStatus) != math.MinInt32-1
}

func (p *AppLaunch) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AppLaunch) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I64 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.LIST {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.LIST {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.LIST {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I32 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I32 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I32 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.I32 {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.I32 {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 73:
			if fieldTypeId == thrift.I32 {
				if err := p.readField73(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 74:
			if fieldTypeId == thrift.I32 {
				if err := p.readField74(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 81:
			if fieldTypeId == thrift.I32 {
				if err := p.readField81(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 82:
			if fieldTypeId == thrift.I32 {
				if err := p.readField82(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppLaunch) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AppLaunch) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchAppLaunchId = v
	}
	return nil
}

func (p *AppLaunch) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SearchEffctId = v
	}
	return nil
}

func (p *AppLaunch) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AppLaunch) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AppLaunch) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *AppLaunch) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ExpandId = v
	}
	return nil
}

func (p *AppLaunch) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SearchPushId = v
	}
	return nil
}

func (p *AppLaunch) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.DeviceType = DeviceType(v)
	}
	return nil
}

func (p *AppLaunch) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.BuildType = AppLaunchType(v)
	}
	return nil
}

func (p *AppLaunch) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Platform = PlatformType(v)
	}
	return nil
}

func (p *AppLaunch) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.UvExpand = v
	}
	return nil
}

func (p *AppLaunch) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.ScopeType = SeedScopeType(v)
	}
	return nil
}

func (p *AppLaunch) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.ExpandUvTotal = v
	}
	return nil
}

func (p *AppLaunch) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.GdtAccount = v
	}
	return nil
}

func (p *AppLaunch) readField31(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Settings = make([]*AppLaunchSetting, 0, size)
	for i := 0; i < size; i++ {
		_elem30 := NewAppLaunchSetting()
		if err := _elem30.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem30)
		}
		p.Settings = append(p.Settings, _elem30)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunch) readField41(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.InsightSchedules = make([]*InsightSchedule, 0, size)
	for i := 0; i < size; i++ {
		_elem31 := NewInsightSchedule()
		if err := _elem31.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem31)
		}
		p.InsightSchedules = append(p.InsightSchedules, _elem31)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunch) readField42(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PushSchedules = make([]*PushSchedule, 0, size)
	for i := 0; i < size; i++ {
		_elem32 := NewPushSchedule()
		if err := _elem32.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem32)
		}
		p.PushSchedules = append(p.PushSchedules, _elem32)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppLaunch) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.EffectStatus = ScheduleStatus(v)
	}
	return nil
}

func (p *AppLaunch) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.Status = ScheduleStatus(v)
	}
	return nil
}

func (p *AppLaunch) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.UvTotal = v
	}
	return nil
}

func (p *AppLaunch) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *AppLaunch) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.UvCommitDt = v
	}
	return nil
}

func (p *AppLaunch) readField72(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 72: %s", err)
	} else {
		p.UvFinishDt = v
	}
	return nil
}

func (p *AppLaunch) readField73(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 73: %s", err)
	} else {
		p.EffectCommitDt = v
	}
	return nil
}

func (p *AppLaunch) readField74(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 74: %s", err)
	} else {
		p.EffectFinishDt = v
	}
	return nil
}

func (p *AppLaunch) readField81(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 81: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AppLaunch) readField82(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 82: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *AppLaunch) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppLaunch"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField73(oprot); err != nil {
		return err
	}
	if err := p.writeField74(oprot); err != nil {
		return err
	}
	if err := p.writeField81(oprot); err != nil {
		return err
	}
	if err := p.writeField82(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppLaunch) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchAppLaunchId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchAppLaunchId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SearchAppLaunchId)); err != nil {
		return fmt.Errorf("%T.searchAppLaunchId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchAppLaunchId: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchEffctId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:searchEffctId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SearchEffctId)); err != nil {
		return fmt.Errorf("%T.searchEffctId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:searchEffctId: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:uid: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:description: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expandId", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:expandId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExpandId)); err != nil {
		return fmt.Errorf("%T.expandId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:expandId: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchPushId", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:searchPushId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SearchPushId)); err != nil {
		return fmt.Errorf("%T.searchPushId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:searchPushId: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetDeviceType() {
		if err := oprot.WriteFieldBegin("deviceType", thrift.I32, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:deviceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DeviceType)); err != nil {
			return fmt.Errorf("%T.deviceType (21) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:deviceType: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField22(oprot thrift.TProtocol) (err error) {
	if p.IsSetBuildType() {
		if err := oprot.WriteFieldBegin("buildType", thrift.I32, 22); err != nil {
			return fmt.Errorf("%T write field begin error 22:buildType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BuildType)); err != nil {
			return fmt.Errorf("%T.buildType (22) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 22:buildType: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField23(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (23) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:platform: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvExpand", thrift.I64, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:uvExpand: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UvExpand)); err != nil {
		return fmt.Errorf("%T.uvExpand (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:uvExpand: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField25(oprot thrift.TProtocol) (err error) {
	if p.IsSetScopeType() {
		if err := oprot.WriteFieldBegin("scopeType", thrift.I32, 25); err != nil {
			return fmt.Errorf("%T write field begin error 25:scopeType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ScopeType)); err != nil {
			return fmt.Errorf("%T.scopeType (25) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 25:scopeType: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expandUvTotal", thrift.I64, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:expandUvTotal: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExpandUvTotal)); err != nil {
		return fmt.Errorf("%T.expandUvTotal (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:expandUvTotal: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gdtAccount", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:gdtAccount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GdtAccount)); err != nil {
		return fmt.Errorf("%T.gdtAccount (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:gdtAccount: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField31(oprot thrift.TProtocol) (err error) {
	if p.Settings != nil {
		if err := oprot.WriteFieldBegin("settings", thrift.LIST, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:settings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Settings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Settings {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:settings: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField41(oprot thrift.TProtocol) (err error) {
	if p.InsightSchedules != nil {
		if err := oprot.WriteFieldBegin("insightSchedules", thrift.LIST, 41); err != nil {
			return fmt.Errorf("%T write field begin error 41:insightSchedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.InsightSchedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.InsightSchedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 41:insightSchedules: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField42(oprot thrift.TProtocol) (err error) {
	if p.PushSchedules != nil {
		if err := oprot.WriteFieldBegin("pushSchedules", thrift.LIST, 42); err != nil {
			return fmt.Errorf("%T write field begin error 42:pushSchedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PushSchedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PushSchedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 42:pushSchedules: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField43(oprot thrift.TProtocol) (err error) {
	if p.IsSetEffectStatus() {
		if err := oprot.WriteFieldBegin("effectStatus", thrift.I32, 43); err != nil {
			return fmt.Errorf("%T write field begin error 43:effectStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.EffectStatus)); err != nil {
			return fmt.Errorf("%T.effectStatus (43) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 43:effectStatus: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField44(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 44); err != nil {
			return fmt.Errorf("%T write field begin error 44:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (44) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 44:status: %s", p, err)
		}
	}
	return err
}

func (p *AppLaunch) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvTotal", thrift.I32, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:uvTotal: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvTotal)); err != nil {
		return fmt.Errorf("%T.uvTotal (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:uvTotal: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:deleted: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvCommitDt", thrift.I32, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:uvCommitDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvCommitDt)); err != nil {
		return fmt.Errorf("%T.uvCommitDt (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:uvCommitDt: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField72(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uvFinishDt", thrift.I32, 72); err != nil {
		return fmt.Errorf("%T write field begin error 72:uvFinishDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvFinishDt)); err != nil {
		return fmt.Errorf("%T.uvFinishDt (72) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 72:uvFinishDt: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField73(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectCommitDt", thrift.I32, 73); err != nil {
		return fmt.Errorf("%T write field begin error 73:effectCommitDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EffectCommitDt)); err != nil {
		return fmt.Errorf("%T.effectCommitDt (73) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 73:effectCommitDt: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField74(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectFinishDt", thrift.I32, 74); err != nil {
		return fmt.Errorf("%T write field begin error 74:effectFinishDt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EffectFinishDt)); err != nil {
		return fmt.Errorf("%T.effectFinishDt (74) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 74:effectFinishDt: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField81(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 81); err != nil {
		return fmt.Errorf("%T write field begin error 81:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (81) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 81:createTime: %s", p, err)
	}
	return err
}

func (p *AppLaunch) writeField82(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 82); err != nil {
		return fmt.Errorf("%T write field begin error 82:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (82) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 82:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AppLaunch) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppLaunch(%+v)", *p)
}

type Project struct {
	Id     int32  `thrift:"id,1" json:"id"`
	Uid    int32  `thrift:"uid,2" json:"uid"`
	Name   string `thrift:"name,3" json:"name"`
	Owner  string `thrift:"owner,4" json:"owner"`
	Remark string `thrift:"remark,5" json:"remark"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Deleted int32 `thrift:"deleted,11" json:"deleted"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	CreateTime int32 `thrift:"createTime,21" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,22" json:"lastUpdate"`
}

func NewProject() *Project {
	return &Project{}
}

func (p *Project) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Project) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Project) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Project) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Project) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Owner = v
	}
	return nil
}

func (p *Project) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *Project) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *Project) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Project) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Project) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Project"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Project) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Project) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Project) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Project) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("owner", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:owner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Owner)); err != nil {
		return fmt.Errorf("%T.owner (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:owner: %s", p, err)
	}
	return err
}

func (p *Project) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:remark: %s", p, err)
	}
	return err
}

func (p *Project) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:deleted: %s", p, err)
	}
	return err
}

func (p *Project) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:createTime: %s", p, err)
	}
	return err
}

func (p *Project) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Project) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Project(%+v)", *p)
}

type ProjectParams struct {
	Uids []int32 `thrift:"uids,1" json:"uids"`
	Ids  []int32 `thrift:"ids,2" json:"ids"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Name   string `thrift:"name,11" json:"name"`
	Owner  string `thrift:"owner,12" json:"owner"`
	Remark string `thrift:"remark,13" json:"remark"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	MixText string `thrift:"mixText,21" json:"mixText"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Offset    int32 `thrift:"offset,41" json:"offset"`
	Limit     int32 `thrift:"limit,42" json:"limit"`
	Ascending bool  `thrift:"ascending,43" json:"ascending"`
}

func NewProjectParams() *ProjectParams {
	return &ProjectParams{
		Ascending: true,
	}
}

func (p *ProjectParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProjectParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem33 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem33 = v
		}
		p.Uids = append(p.Uids, _elem33)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProjectParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem34 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem34 = v
		}
		p.Ids = append(p.Ids, _elem34)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProjectParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ProjectParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Owner = v
	}
	return nil
}

func (p *ProjectParams) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *ProjectParams) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *ProjectParams) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ProjectParams) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ProjectParams) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *ProjectParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProjectParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProjectParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *ProjectParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *ProjectParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:name: %s", p, err)
	}
	return err
}

func (p *ProjectParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("owner", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:owner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Owner)); err != nil {
		return fmt.Errorf("%T.owner (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:owner: %s", p, err)
	}
	return err
}

func (p *ProjectParams) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:remark: %s", p, err)
	}
	return err
}

func (p *ProjectParams) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:mixText: %s", p, err)
	}
	return err
}

func (p *ProjectParams) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:offset: %s", p, err)
	}
	return err
}

func (p *ProjectParams) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:limit: %s", p, err)
	}
	return err
}

func (p *ProjectParams) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:ascending: %s", p, err)
	}
	return err
}

func (p *ProjectParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectParams(%+v)", *p)
}

type Campaign struct {
	Id        int32  `thrift:"id,1" json:"id"`
	Uid       int32  `thrift:"uid,2" json:"uid"`
	ProjectId int32  `thrift:"projectId,3" json:"projectId"`
	Name      string `thrift:"name,4" json:"name"`
	Owner     string `thrift:"owner,5" json:"owner"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	TotalBudget int64  `thrift:"totalBudget,11" json:"totalBudget"`
	Budget      int64  `thrift:"budget,12" json:"budget"`
	DataCost    int64  `thrift:"dataCost,13" json:"dataCost"`
	DataSource  string `thrift:"dataSource,14" json:"dataSource"`
	Fee         int64  `thrift:"fee,15" json:"fee"`
	BeginTime   int32  `thrift:"beginTime,16" json:"beginTime"`
	EndTime     int32  `thrift:"endTime,17" json:"endTime"`
	Remark      string `thrift:"remark,18" json:"remark"`
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Status  int32 `thrift:"status,31" json:"status"`
	Deleted int32 `thrift:"deleted,32" json:"deleted"`
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	CreateTime int32 `thrift:"createTime,41" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,42" json:"lastUpdate"`
}

func NewCampaign() *Campaign {
	return &Campaign{}
}

func (p *Campaign) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Campaign) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Campaign) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Campaign) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProjectId = v
	}
	return nil
}

func (p *Campaign) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Campaign) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Owner = v
	}
	return nil
}

func (p *Campaign) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.TotalBudget = v
	}
	return nil
}

func (p *Campaign) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *Campaign) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.DataCost = v
	}
	return nil
}

func (p *Campaign) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DataSource = v
	}
	return nil
}

func (p *Campaign) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Fee = v
	}
	return nil
}

func (p *Campaign) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.BeginTime = v
	}
	return nil
}

func (p *Campaign) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *Campaign) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *Campaign) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Campaign) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *Campaign) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Campaign) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Campaign) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Campaign"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Campaign) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("projectId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:projectId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProjectId)); err != nil {
		return fmt.Errorf("%T.projectId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:projectId: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("owner", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:owner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Owner)); err != nil {
		return fmt.Errorf("%T.owner (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:owner: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:totalBudget: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:budget: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dataCost", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:dataCost: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DataCost)); err != nil {
		return fmt.Errorf("%T.dataCost (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:dataCost: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dataSource", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:dataSource: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataSource)); err != nil {
		return fmt.Errorf("%T.dataSource (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:dataSource: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fee", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:fee: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Fee)); err != nil {
		return fmt.Errorf("%T.fee (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:fee: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beginTime", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:beginTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BeginTime)); err != nil {
		return fmt.Errorf("%T.beginTime (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:beginTime: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:endTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:endTime: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:remark: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:status: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:deleted: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:createTime: %s", p, err)
	}
	return err
}

func (p *Campaign) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Campaign) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Campaign(%+v)", *p)
}

type CampaignParams struct {
	Uids       []int32 `thrift:"uids,1" json:"uids"`
	Ids        []int32 `thrift:"ids,2" json:"ids"`
	ProjectIds []int32 `thrift:"projectIds,3" json:"projectIds"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Name   string `thrift:"name,11" json:"name"`
	Owner  string `thrift:"owner,12" json:"owner"`
	Remark string `thrift:"remark,13" json:"remark"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	MixText string `thrift:"mixText,21" json:"mixText"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Offset    int32 `thrift:"offset,41" json:"offset"`
	Limit     int32 `thrift:"limit,42" json:"limit"`
	Ascending bool  `thrift:"ascending,43" json:"ascending"`
}

func NewCampaignParams() *CampaignParams {
	return &CampaignParams{
		Ascending: true,
	}
}

func (p *CampaignParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CampaignParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem35 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem35 = v
		}
		p.Uids = append(p.Uids, _elem35)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CampaignParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem36 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem36 = v
		}
		p.Ids = append(p.Ids, _elem36)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CampaignParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProjectIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem37 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem37 = v
		}
		p.ProjectIds = append(p.ProjectIds, _elem37)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CampaignParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CampaignParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Owner = v
	}
	return nil
}

func (p *CampaignParams) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *CampaignParams) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *CampaignParams) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *CampaignParams) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *CampaignParams) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *CampaignParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CampaignParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CampaignParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *CampaignParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *CampaignParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ProjectIds != nil {
		if err := oprot.WriteFieldBegin("projectIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:projectIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ProjectIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProjectIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:projectIds: %s", p, err)
		}
	}
	return err
}

func (p *CampaignParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:name: %s", p, err)
	}
	return err
}

func (p *CampaignParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("owner", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:owner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Owner)); err != nil {
		return fmt.Errorf("%T.owner (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:owner: %s", p, err)
	}
	return err
}

func (p *CampaignParams) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:remark: %s", p, err)
	}
	return err
}

func (p *CampaignParams) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:mixText: %s", p, err)
	}
	return err
}

func (p *CampaignParams) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:offset: %s", p, err)
	}
	return err
}

func (p *CampaignParams) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:limit: %s", p, err)
	}
	return err
}

func (p *CampaignParams) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:ascending: %s", p, err)
	}
	return err
}

func (p *CampaignParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CampaignParams(%+v)", *p)
}

type Strategy struct {
	Id         int32  `thrift:"id,1" json:"id"`
	Uid        int32  `thrift:"uid,2" json:"uid"`
	ProjectId  int32  `thrift:"projectId,3" json:"projectId"`
	CampaignId int32  `thrift:"campaignId,4" json:"campaignId"`
	Name       string `thrift:"name,5" json:"name"`
	Media      string `thrift:"media,6" json:"media"`
	Budget     int64  `thrift:"budget,7" json:"budget"`
	BeginTime  int32  `thrift:"beginTime,8" json:"beginTime"`
	EndTime    int32  `thrift:"endTime,9" json:"endTime"`
	Remark     string `thrift:"remark,10" json:"remark"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	Deleted int32 `thrift:"deleted,21" json:"deleted"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	CreateTime int32 `thrift:"createTime,31" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,32" json:"lastUpdate"`
}

func NewStrategy() *Strategy {
	return &Strategy{}
}

func (p *Strategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Strategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Strategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Strategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProjectId = v
	}
	return nil
}

func (p *Strategy) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *Strategy) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Strategy) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *Strategy) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *Strategy) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.BeginTime = v
	}
	return nil
}

func (p *Strategy) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *Strategy) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *Strategy) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *Strategy) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Strategy) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Strategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Strategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Strategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("projectId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:projectId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProjectId)); err != nil {
		return fmt.Errorf("%T.projectId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:projectId: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaignId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:campaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaignId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:campaignId: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:media: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Media)); err != nil {
		return fmt.Errorf("%T.media (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:media: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:budget: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beginTime", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:beginTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BeginTime)); err != nil {
		return fmt.Errorf("%T.beginTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:beginTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:endTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:endTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:remark: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:deleted: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:createTime: %s", p, err)
	}
	return err
}

func (p *Strategy) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Strategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Strategy(%+v)", *p)
}

type StrategyParams struct {
	Uids        []int32 `thrift:"uids,1" json:"uids"`
	Ids         []int32 `thrift:"ids,2" json:"ids"`
	ProjectIds  []int32 `thrift:"projectIds,3" json:"projectIds"`
	CampaignIds []int32 `thrift:"campaignIds,4" json:"campaignIds"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Name   string `thrift:"name,11" json:"name"`
	Media  string `thrift:"media,12" json:"media"`
	Remark string `thrift:"remark,13" json:"remark"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	MixText string `thrift:"mixText,21" json:"mixText"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Offset    int32 `thrift:"offset,41" json:"offset"`
	Limit     int32 `thrift:"limit,42" json:"limit"`
	Ascending bool  `thrift:"ascending,43" json:"ascending"`
}

func NewStrategyParams() *StrategyParams {
	return &StrategyParams{
		Ascending: true,
	}
}

func (p *StrategyParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StrategyParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem38 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem38 = v
		}
		p.Uids = append(p.Uids, _elem38)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategyParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem39 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem39 = v
		}
		p.Ids = append(p.Ids, _elem39)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategyParams) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProjectIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem40 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem40 = v
		}
		p.ProjectIds = append(p.ProjectIds, _elem40)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategyParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CampaignIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem41 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem41 = v
		}
		p.CampaignIds = append(p.CampaignIds, _elem41)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StrategyParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *StrategyParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *StrategyParams) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *StrategyParams) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *StrategyParams) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *StrategyParams) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *StrategyParams) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *StrategyParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StrategyParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StrategyParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *StrategyParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *StrategyParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ProjectIds != nil {
		if err := oprot.WriteFieldBegin("projectIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:projectIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ProjectIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProjectIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:projectIds: %s", p, err)
		}
	}
	return err
}

func (p *StrategyParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.CampaignIds != nil {
		if err := oprot.WriteFieldBegin("campaignIds", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:campaignIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CampaignIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CampaignIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:campaignIds: %s", p, err)
		}
	}
	return err
}

func (p *StrategyParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:name: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:media: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Media)); err != nil {
		return fmt.Errorf("%T.media (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:media: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:remark: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:mixText: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:offset: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:limit: %s", p, err)
	}
	return err
}

func (p *StrategyParams) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:ascending: %s", p, err)
	}
	return err
}

func (p *StrategyParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StrategyParams(%+v)", *p)
}

type Creative struct {
	Id     int32  `thrift:"id,1" json:"id"`
	Uid    int32  `thrift:"uid,2" json:"uid"`
	Name   string `thrift:"name,3" json:"name"`
	Url    string `thrift:"url,4" json:"url"`
	Remark string `thrift:"remark,5" json:"remark"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Deleted int32 `thrift:"deleted,11" json:"deleted"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	CreateTime int32 `thrift:"createTime,21" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,22" json:"lastUpdate"`
}

func NewCreative() *Creative {
	return &Creative{}
}

func (p *Creative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Creative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Creative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *Creative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Creative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Creative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *Creative) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *Creative) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Creative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Creative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Creative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Creative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Creative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *Creative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Creative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:url: %s", p, err)
	}
	return err
}

func (p *Creative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:remark: %s", p, err)
	}
	return err
}

func (p *Creative) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:deleted: %s", p, err)
	}
	return err
}

func (p *Creative) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:createTime: %s", p, err)
	}
	return err
}

func (p *Creative) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Creative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Creative(%+v)", *p)
}

type CreativeParams struct {
	Uids []int32 `thrift:"uids,1" json:"uids"`
	Ids  []int32 `thrift:"ids,2" json:"ids"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Name   string `thrift:"name,11" json:"name"`
	Remark string `thrift:"remark,12" json:"remark"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	MixText string `thrift:"mixText,21" json:"mixText"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	Offset    int32 `thrift:"offset,41" json:"offset"`
	Limit     int32 `thrift:"limit,42" json:"limit"`
	Ascending bool  `thrift:"ascending,43" json:"ascending"`
}

func NewCreativeParams() *CreativeParams {
	return &CreativeParams{
		Ascending: true,
	}
}

func (p *CreativeParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem42 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem42 = v
		}
		p.Uids = append(p.Uids, _elem42)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem43 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem43 = v
		}
		p.Ids = append(p.Ids, _elem43)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CreativeParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *CreativeParams) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *CreativeParams) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *CreativeParams) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *CreativeParams) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *CreativeParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *CreativeParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *CreativeParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:name: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:remark: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:mixText: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:offset: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:limit: %s", p, err)
	}
	return err
}

func (p *CreativeParams) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:ascending: %s", p, err)
	}
	return err
}

func (p *CreativeParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeParams(%+v)", *p)
}

type LifeCycleGroup struct {
	Id   int32  `thrift:"id,1" json:"id"`
	Uid  int32  `thrift:"uid,2" json:"uid"`
	Name string `thrift:"name,3" json:"name"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Deleted int32 `thrift:"deleted,11" json:"deleted"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	CreateTime int32 `thrift:"createTime,31" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,32" json:"lastUpdate"`
}

func NewLifeCycleGroup() *LifeCycleGroup {
	return &LifeCycleGroup{}
}

func (p *LifeCycleGroup) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleGroup) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *LifeCycleGroup) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *LifeCycleGroup) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *LifeCycleGroup) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *LifeCycleGroup) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *LifeCycleGroup) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *LifeCycleGroup) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LifeCycleGroup"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroup) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroup) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:deleted: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:deleted: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroup) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:createTime: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroup) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:lastUpdate: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCycleGroup(%+v)", *p)
}

type LifeCycleGroupParams struct {
	Uids    []int32 `thrift:"uids,1" json:"uids"`
	Ids     []int32 `thrift:"ids,2" json:"ids"`
	Name    string  `thrift:"name,3" json:"name"`
	MixText string  `thrift:"mixText,4" json:"mixText"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	Offset    int32 `thrift:"offset,11" json:"offset"`
	Limit     int32 `thrift:"limit,12" json:"limit"`
	Ascending bool  `thrift:"ascending,13" json:"ascending"`
}

func NewLifeCycleGroupParams() *LifeCycleGroupParams {
	return &LifeCycleGroupParams{
		Ascending: true,
	}
}

func (p *LifeCycleGroupParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleGroupParams) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Uids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem44 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem44 = v
		}
		p.Uids = append(p.Uids, _elem44)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleGroupParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem45 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem45 = v
		}
		p.Ids = append(p.Ids, _elem45)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *LifeCycleGroupParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *LifeCycleGroupParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MixText = v
	}
	return nil
}

func (p *LifeCycleGroupParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *LifeCycleGroupParams) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *LifeCycleGroupParams) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *LifeCycleGroupParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("LifeCycleGroupParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *LifeCycleGroupParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Uids != nil {
		if err := oprot.WriteFieldBegin("uids", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:uids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Uids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Uids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:uids: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleGroupParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *LifeCycleGroupParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroupParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mixText", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:mixText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MixText)); err != nil {
		return fmt.Errorf("%T.mixText (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:mixText: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroupParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:offset: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroupParams) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:limit: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroupParams) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ascending: %s", p, err)
	}
	return err
}

func (p *LifeCycleGroupParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("LifeCycleGroupParams(%+v)", *p)
}
