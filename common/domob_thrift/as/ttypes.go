// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package as

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/adserver_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/searchui_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = adserver_types.GoUnusedProtection__
var _ = searchui_types.GoUnusedProtection__
var GoUnusedProtection__ int

type LargeIdInt common.LargeIdInt

type SDKVersion common.SDKVersion

type SDKPlatform common.SDKPlatform

type JailBreakCode common.JailBreakCode

type AdPlacementType common.AdPlacementType

type TimeInt common.TimeInt

type IdInt common.IdInt

type ResourceGroup *common.ResourceGroup

type CreativeIdInt common.IdInt

type Channel *common.Channel

type UiRequest struct {
	ClientIp       string          `thrift:"client_ip,1" json:"client_ip"`
	SdkserverIp    string          `thrift:"sdkserver_ip,2" json:"sdkserver_ip"`
	HttpUa         string          `thrift:"http_ua,3" json:"http_ua"`
	SdkUa          string          `thrift:"sdk_ua,4" json:"sdk_ua"`
	Sp             string          `thrift:"sp,5" json:"sp"`
	Uu             string          `thrift:"uu,6" json:"uu"`
	Sdk            string          `thrift:"sdk,7" json:"sdk"`
	Rt             string          `thrift:"rt,8" json:"rt"`
	Ts             string          `thrift:"ts,9" json:"ts"`
	Ipb            string          `thrift:"ipb,10" json:"ipb"`
	Idv            string          `thrift:"idv,11" json:"idv"`
	V              string          `thrift:"v,12" json:"v"`
	L              string          `thrift:"l,13" json:"l"`
	F              string          `thrift:"f,14" json:"f"`
	E              string          `thrift:"e,15" json:"e"`
	Cid            string          `thrift:"cid,16" json:"cid"`
	So             string          `thrift:"so,17" json:"so"`
	Dim            string          `thrift:"dim,18" json:"dim"`
	Sw             string          `thrift:"sw,19" json:"sw"`
	Sh             string          `thrift:"sh,20" json:"sh"`
	Sd             string          `thrift:"sd,21" json:"sd"`
	C              string          `thrift:"c,22" json:"c"`
	K              string          `thrift:"k,23" json:"k"`
	M              string          `thrift:"m,24" json:"m"`
	TestAction     string          `thrift:"test_action,25" json:"test_action"`
	StatReqs       string          `thrift:"stat_reqs,26" json:"stat_reqs"`
	StatTime       string          `thrift:"stat_time,27" json:"stat_time"`
	PbIdentifier   string          `thrift:"pb_identifier,28" json:"pb_identifier"`
	PbVersion      string          `thrift:"pb_version,29" json:"pb_version"`
	CoordTimestamp string          `thrift:"coord_timestamp,30" json:"coord_timestamp"`
	Coord          string          `thrift:"coord,31" json:"coord"`
	Pc             string          `thrift:"pc,32" json:"pc"`
	Dob            string          `thrift:"dob,33" json:"dob"`
	Gender         string          `thrift:"gender,34" json:"gender"`
	Spot           string          `thrift:"spot,35" json:"spot"`
	Network        string          `thrift:"network,36" json:"network"`
	LmConfig       string          `thrift:"lm_config,37" json:"lm_config"`
	LmRes          string          `thrift:"lm_res,38" json:"lm_res"`
	Avg            string          `thrift:"avg,39" json:"avg"`
	Num            string          `thrift:"num,40" json:"num"`
	Hh             string          `thrift:"hh,41" json:"hh"`
	Search         string          `thrift:"search,42" json:"search"`
	Url            string          `thrift:"url,43" json:"url"`
	Referer        string          `thrift:"referer,44" json:"referer"`
	SearchId       LargeIdInt      `thrift:"search_id,45" json:"search_id"`
	PbName         string          `thrift:"pb_name,46" json:"pb_name"`
	Lpkg           string          `thrift:"lpkg,47" json:"lpkg"`
	Sv             SDKVersion      `thrift:"sv,48" json:"sv"`
	SdkPlatform    SDKPlatform     `thrift:"sdk_platform,49" json:"sdk_platform"`
	Isv            int32           `thrift:"isv,50" json:"isv"`
	AltDims        []string        `thrift:"alt_dims,51" json:"alt_dims"`
	Jb             JailBreakCode   `thrift:"jb,52" json:"jb"`
	Ma             string          `thrift:"ma,53" json:"ma"`
	Pt             AdPlacementType `thrift:"pt,54" json:"pt"`
	Ducookie       string          `thrift:"ducookie,55" json:"ducookie"`
	Duid           int64           `thrift:"duid,56" json:"duid"`
	Oid            string          `thrift:"oid,57" json:"oid"`
	Intermediary   string          `thrift:"intermediary,58" json:"intermediary"`
	Dmac           string          `thrift:"dmac,59" json:"dmac"`
	Amac           string          `thrift:"amac,60" json:"amac"`
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	CreativeId int32 `thrift:"creative_id,71" json:"creative_id"`
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	Pmid int32  `thrift:"pmid,80" json:"pmid"`
	Ifa  string `thrift:"ifa,81" json:"ifa"`
	Lat  string `thrift:"lat,82" json:"lat"`
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	Rawpt               AdPlacementType `thrift:"rawpt,90" json:"rawpt"`
	Lac                 string          `thrift:"lac,91" json:"lac"`
	Cell                string          `thrift:"cell,92" json:"cell"`
	Mcc                 string          `thrift:"mcc,93" json:"mcc"`
	Mnc                 string          `thrift:"mnc,94" json:"mnc"`
	Scan                string          `thrift:"scan,95" json:"scan"`
	CoordAcc            int32           `thrift:"coord_acc,96" json:"coord_acc"`
	CoordStatus         int32           `thrift:"coord_status,97" json:"coord_status"`
	CoordEc             int32           `thrift:"coord_ec,98" json:"coord_ec"`
	ImpressionFulfilled []int32         `thrift:"impressionFulfilled,99" json:"impressionFulfilled"`
	ClickFulfilled      []int32         `thrift:"clickFulfilled,100" json:"clickFulfilled"`
	CachedResource      []int32         `thrift:"cachedResource,101" json:"cachedResource"`
	FirstVisitTime      TimeInt         `thrift:"firstVisitTime,102" json:"firstVisitTime"`
	// unused field # 103
	// unused field # 104
	// unused field # 105
	// unused field # 106
	// unused field # 107
	// unused field # 108
	// unused field # 109
	Anid  string          `thrift:"anid,110" json:"anid"`
	Anid2 string          `thrift:"anid2,111" json:"anid2"`
	Ids   []CreativeIdInt `thrift:"ids,112" json:"ids"`
	// unused field # 113
	// unused field # 114
	// unused field # 115
	// unused field # 116
	// unused field # 117
	// unused field # 118
	// unused field # 119
	CapabilityModifier []int32                 `thrift:"capabilityModifier,120" json:"capabilityModifier"`
	An                 string                  `thrift:"an,121" json:"an"`
	ServerHostName     string                  `thrift:"serverHostName,122" json:"serverHostName"`
	LastVisitTime      TimeInt                 `thrift:"lastVisitTime,123" json:"lastVisitTime"`
	ReqContainers      []int32                 `thrift:"reqContainers,124" json:"reqContainers"`
	InstalledPackages  []string                `thrift:"installedPackages,125" json:"installedPackages"`
	Channel            string                  `thrift:"channel,126" json:"channel"`
	SubChannel         string                  `thrift:"subChannel,127" json:"subChannel"`
	Title              string                  `thrift:"title,128" json:"title"`
	PartnerId          int32                   `thrift:"partnerId,129" json:"partnerId"`
	RequestSourceType  enums.RequestSourceType `thrift:"requestSourceType,130" json:"requestSourceType"`
}

func NewUiRequest() *UiRequest {
	return &UiRequest{
		Sv: math.MinInt32 - 1, // unset sentinal value

		SdkPlatform: math.MinInt32 - 1, // unset sentinal value

		Jb: math.MinInt32 - 1, // unset sentinal value

		Pt: math.MinInt32 - 1, // unset sentinal value

		Rawpt: math.MinInt32 - 1, // unset sentinal value

		RequestSourceType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UiRequest) IsSetSv() bool {
	return int64(p.Sv) != math.MinInt32-1
}

func (p *UiRequest) IsSetSdkPlatform() bool {
	return int64(p.SdkPlatform) != math.MinInt32-1
}

func (p *UiRequest) IsSetJb() bool {
	return int64(p.Jb) != math.MinInt32-1
}

func (p *UiRequest) IsSetPt() bool {
	return int64(p.Pt) != math.MinInt32-1
}

func (p *UiRequest) IsSetRawpt() bool {
	return int64(p.Rawpt) != math.MinInt32-1
}

func (p *UiRequest) IsSetRequestSourceType() bool {
	return int64(p.RequestSourceType) != math.MinInt32-1
}

func (p *UiRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.STRING {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.STRING {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.I64 {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.STRING {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.STRING {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.I32 {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.I32 {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I32 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.LIST {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I32 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.STRING {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I32 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.STRING {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.I64 {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.STRING {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.STRING {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.STRING {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.STRING {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.I32 {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 80:
			if fieldTypeId == thrift.I32 {
				if err := p.readField80(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 81:
			if fieldTypeId == thrift.STRING {
				if err := p.readField81(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 82:
			if fieldTypeId == thrift.STRING {
				if err := p.readField82(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 90:
			if fieldTypeId == thrift.I32 {
				if err := p.readField90(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 91:
			if fieldTypeId == thrift.STRING {
				if err := p.readField91(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 92:
			if fieldTypeId == thrift.STRING {
				if err := p.readField92(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 93:
			if fieldTypeId == thrift.STRING {
				if err := p.readField93(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 94:
			if fieldTypeId == thrift.STRING {
				if err := p.readField94(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 95:
			if fieldTypeId == thrift.STRING {
				if err := p.readField95(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 96:
			if fieldTypeId == thrift.I32 {
				if err := p.readField96(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I32 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.LIST {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 100:
			if fieldTypeId == thrift.LIST {
				if err := p.readField100(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 101:
			if fieldTypeId == thrift.LIST {
				if err := p.readField101(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 102:
			if fieldTypeId == thrift.I64 {
				if err := p.readField102(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 110:
			if fieldTypeId == thrift.STRING {
				if err := p.readField110(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 111:
			if fieldTypeId == thrift.STRING {
				if err := p.readField111(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 112:
			if fieldTypeId == thrift.LIST {
				if err := p.readField112(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 120:
			if fieldTypeId == thrift.LIST {
				if err := p.readField120(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 121:
			if fieldTypeId == thrift.STRING {
				if err := p.readField121(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 122:
			if fieldTypeId == thrift.STRING {
				if err := p.readField122(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 123:
			if fieldTypeId == thrift.I64 {
				if err := p.readField123(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 124:
			if fieldTypeId == thrift.LIST {
				if err := p.readField124(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 125:
			if fieldTypeId == thrift.LIST {
				if err := p.readField125(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 126:
			if fieldTypeId == thrift.STRING {
				if err := p.readField126(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 127:
			if fieldTypeId == thrift.STRING {
				if err := p.readField127(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 128:
			if fieldTypeId == thrift.STRING {
				if err := p.readField128(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 129:
			if fieldTypeId == thrift.I32 {
				if err := p.readField129(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 130:
			if fieldTypeId == thrift.I32 {
				if err := p.readField130(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UiRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *UiRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SdkserverIp = v
	}
	return nil
}

func (p *UiRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.HttpUa = v
	}
	return nil
}

func (p *UiRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SdkUa = v
	}
	return nil
}

func (p *UiRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sp = v
	}
	return nil
}

func (p *UiRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Uu = v
	}
	return nil
}

func (p *UiRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Sdk = v
	}
	return nil
}

func (p *UiRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Rt = v
	}
	return nil
}

func (p *UiRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Ts = v
	}
	return nil
}

func (p *UiRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *UiRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Idv = v
	}
	return nil
}

func (p *UiRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.V = v
	}
	return nil
}

func (p *UiRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.L = v
	}
	return nil
}

func (p *UiRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.F = v
	}
	return nil
}

func (p *UiRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.E = v
	}
	return nil
}

func (p *UiRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *UiRequest) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.So = v
	}
	return nil
}

func (p *UiRequest) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Dim = v
	}
	return nil
}

func (p *UiRequest) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Sw = v
	}
	return nil
}

func (p *UiRequest) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Sh = v
	}
	return nil
}

func (p *UiRequest) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Sd = v
	}
	return nil
}

func (p *UiRequest) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.C = v
	}
	return nil
}

func (p *UiRequest) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.K = v
	}
	return nil
}

func (p *UiRequest) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.M = v
	}
	return nil
}

func (p *UiRequest) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.TestAction = v
	}
	return nil
}

func (p *UiRequest) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.StatReqs = v
	}
	return nil
}

func (p *UiRequest) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.StatTime = v
	}
	return nil
}

func (p *UiRequest) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.PbIdentifier = v
	}
	return nil
}

func (p *UiRequest) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.PbVersion = v
	}
	return nil
}

func (p *UiRequest) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CoordTimestamp = v
	}
	return nil
}

func (p *UiRequest) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Coord = v
	}
	return nil
}

func (p *UiRequest) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Pc = v
	}
	return nil
}

func (p *UiRequest) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Dob = v
	}
	return nil
}

func (p *UiRequest) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Gender = v
	}
	return nil
}

func (p *UiRequest) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Spot = v
	}
	return nil
}

func (p *UiRequest) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *UiRequest) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.LmConfig = v
	}
	return nil
}

func (p *UiRequest) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.LmRes = v
	}
	return nil
}

func (p *UiRequest) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Avg = v
	}
	return nil
}

func (p *UiRequest) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Num = v
	}
	return nil
}

func (p *UiRequest) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Hh = v
	}
	return nil
}

func (p *UiRequest) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.Search = v
	}
	return nil
}

func (p *UiRequest) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *UiRequest) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.Referer = v
	}
	return nil
}

func (p *UiRequest) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *UiRequest) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.PbName = v
	}
	return nil
}

func (p *UiRequest) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.Lpkg = v
	}
	return nil
}

func (p *UiRequest) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.Sv = SDKVersion(v)
	}
	return nil
}

func (p *UiRequest) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.SdkPlatform = SDKPlatform(v)
	}
	return nil
}

func (p *UiRequest) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.Isv = v
	}
	return nil
}

func (p *UiRequest) readField51(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AltDims = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.AltDims = append(p.AltDims, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.Jb = JailBreakCode(v)
	}
	return nil
}

func (p *UiRequest) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.Ma = v
	}
	return nil
}

func (p *UiRequest) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Pt = AdPlacementType(v)
	}
	return nil
}

func (p *UiRequest) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.Ducookie = v
	}
	return nil
}

func (p *UiRequest) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *UiRequest) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *UiRequest) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.Intermediary = v
	}
	return nil
}

func (p *UiRequest) readField59(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 59: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *UiRequest) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *UiRequest) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *UiRequest) readField80(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 80: %s", err)
	} else {
		p.Pmid = v
	}
	return nil
}

func (p *UiRequest) readField81(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 81: %s", err)
	} else {
		p.Ifa = v
	}
	return nil
}

func (p *UiRequest) readField82(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 82: %s", err)
	} else {
		p.Lat = v
	}
	return nil
}

func (p *UiRequest) readField90(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 90: %s", err)
	} else {
		p.Rawpt = AdPlacementType(v)
	}
	return nil
}

func (p *UiRequest) readField91(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 91: %s", err)
	} else {
		p.Lac = v
	}
	return nil
}

func (p *UiRequest) readField92(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 92: %s", err)
	} else {
		p.Cell = v
	}
	return nil
}

func (p *UiRequest) readField93(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 93: %s", err)
	} else {
		p.Mcc = v
	}
	return nil
}

func (p *UiRequest) readField94(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 94: %s", err)
	} else {
		p.Mnc = v
	}
	return nil
}

func (p *UiRequest) readField95(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 95: %s", err)
	} else {
		p.Scan = v
	}
	return nil
}

func (p *UiRequest) readField96(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 96: %s", err)
	} else {
		p.CoordAcc = v
	}
	return nil
}

func (p *UiRequest) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.CoordStatus = v
	}
	return nil
}

func (p *UiRequest) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CoordEc = v
	}
	return nil
}

func (p *UiRequest) readField99(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpressionFulfilled = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.ImpressionFulfilled = append(p.ImpressionFulfilled, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField100(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClickFulfilled = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.ClickFulfilled = append(p.ClickFulfilled, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField101(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CachedResource = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.CachedResource = append(p.CachedResource, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField102(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 102: %s", err)
	} else {
		p.FirstVisitTime = TimeInt(v)
	}
	return nil
}

func (p *UiRequest) readField110(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 110: %s", err)
	} else {
		p.Anid = v
	}
	return nil
}

func (p *UiRequest) readField111(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 111: %s", err)
	} else {
		p.Anid2 = v
	}
	return nil
}

func (p *UiRequest) readField112(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]CreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 CreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = CreativeIdInt(v)
		}
		p.Ids = append(p.Ids, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField120(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CapabilityModifier = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.CapabilityModifier = append(p.CapabilityModifier, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField121(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 121: %s", err)
	} else {
		p.An = v
	}
	return nil
}

func (p *UiRequest) readField122(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 122: %s", err)
	} else {
		p.ServerHostName = v
	}
	return nil
}

func (p *UiRequest) readField123(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 123: %s", err)
	} else {
		p.LastVisitTime = TimeInt(v)
	}
	return nil
}

func (p *UiRequest) readField124(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ReqContainers = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.ReqContainers = append(p.ReqContainers, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField125(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.InstalledPackages = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.InstalledPackages = append(p.InstalledPackages, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiRequest) readField126(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 126: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *UiRequest) readField127(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 127: %s", err)
	} else {
		p.SubChannel = v
	}
	return nil
}

func (p *UiRequest) readField128(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 128: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *UiRequest) readField129(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 129: %s", err)
	} else {
		p.PartnerId = v
	}
	return nil
}

func (p *UiRequest) readField130(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 130: %s", err)
	} else {
		p.RequestSourceType = enums.RequestSourceType(v)
	}
	return nil
}

func (p *UiRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UiRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField80(oprot); err != nil {
		return err
	}
	if err := p.writeField81(oprot); err != nil {
		return err
	}
	if err := p.writeField82(oprot); err != nil {
		return err
	}
	if err := p.writeField90(oprot); err != nil {
		return err
	}
	if err := p.writeField91(oprot); err != nil {
		return err
	}
	if err := p.writeField92(oprot); err != nil {
		return err
	}
	if err := p.writeField93(oprot); err != nil {
		return err
	}
	if err := p.writeField94(oprot); err != nil {
		return err
	}
	if err := p.writeField95(oprot); err != nil {
		return err
	}
	if err := p.writeField96(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := p.writeField100(oprot); err != nil {
		return err
	}
	if err := p.writeField101(oprot); err != nil {
		return err
	}
	if err := p.writeField102(oprot); err != nil {
		return err
	}
	if err := p.writeField110(oprot); err != nil {
		return err
	}
	if err := p.writeField111(oprot); err != nil {
		return err
	}
	if err := p.writeField112(oprot); err != nil {
		return err
	}
	if err := p.writeField120(oprot); err != nil {
		return err
	}
	if err := p.writeField121(oprot); err != nil {
		return err
	}
	if err := p.writeField122(oprot); err != nil {
		return err
	}
	if err := p.writeField123(oprot); err != nil {
		return err
	}
	if err := p.writeField124(oprot); err != nil {
		return err
	}
	if err := p.writeField125(oprot); err != nil {
		return err
	}
	if err := p.writeField126(oprot); err != nil {
		return err
	}
	if err := p.writeField127(oprot); err != nil {
		return err
	}
	if err := p.writeField128(oprot); err != nil {
		return err
	}
	if err := p.writeField129(oprot); err != nil {
		return err
	}
	if err := p.writeField130(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UiRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client_ip", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:client_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.client_ip (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:client_ip: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdkserver_ip", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sdkserver_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdkserverIp)); err != nil {
		return fmt.Errorf("%T.sdkserver_ip (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sdkserver_ip: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("http_ua", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:http_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HttpUa)); err != nil {
		return fmt.Errorf("%T.http_ua (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:http_ua: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_ua", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sdk_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdkUa)); err != nil {
		return fmt.Errorf("%T.sdk_ua (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sdk_ua: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sp: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sp)); err != nil {
		return fmt.Errorf("%T.sp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sp: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uu", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:uu: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uu)); err != nil {
		return fmt.Errorf("%T.uu (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:uu: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:sdk: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sdk)); err != nil {
		return fmt.Errorf("%T.sdk (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:sdk: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rt", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:rt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Rt)); err != nil {
		return fmt.Errorf("%T.rt (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:rt: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ts", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ts: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ts)); err != nil {
		return fmt.Errorf("%T.ts (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ts: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:ipb: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idv", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:idv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idv)); err != nil {
		return fmt.Errorf("%T.idv (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:idv: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("v", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:v: %s", p, err)
	}
	if err := oprot.WriteString(string(p.V)); err != nil {
		return fmt.Errorf("%T.v (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:v: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("l", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:l: %s", p, err)
	}
	if err := oprot.WriteString(string(p.L)); err != nil {
		return fmt.Errorf("%T.l (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:l: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("f", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:f: %s", p, err)
	}
	if err := oprot.WriteString(string(p.F)); err != nil {
		return fmt.Errorf("%T.f (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:f: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("e", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:e: %s", p, err)
	}
	if err := oprot.WriteString(string(p.E)); err != nil {
		return fmt.Errorf("%T.e (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:e: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:cid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:cid: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("so", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:so: %s", p, err)
	}
	if err := oprot.WriteString(string(p.So)); err != nil {
		return fmt.Errorf("%T.so (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:so: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dim", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:dim: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dim)); err != nil {
		return fmt.Errorf("%T.dim (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:dim: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sw", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:sw: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sw)); err != nil {
		return fmt.Errorf("%T.sw (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:sw: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sh", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:sh: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sh)); err != nil {
		return fmt.Errorf("%T.sh (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:sh: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:sd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sd)); err != nil {
		return fmt.Errorf("%T.sd (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:sd: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("c", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:c: %s", p, err)
	}
	if err := oprot.WriteString(string(p.C)); err != nil {
		return fmt.Errorf("%T.c (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:c: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("k", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:k: %s", p, err)
	}
	if err := oprot.WriteString(string(p.K)); err != nil {
		return fmt.Errorf("%T.k (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:k: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("m", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:m: %s", p, err)
	}
	if err := oprot.WriteString(string(p.M)); err != nil {
		return fmt.Errorf("%T.m (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:m: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("test_action", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:test_action: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TestAction)); err != nil {
		return fmt.Errorf("%T.test_action (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:test_action: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stat_reqs", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:stat_reqs: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StatReqs)); err != nil {
		return fmt.Errorf("%T.stat_reqs (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:stat_reqs: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stat_time", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:stat_time: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StatTime)); err != nil {
		return fmt.Errorf("%T.stat_time (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:stat_time: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_identifier", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:pb_identifier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbIdentifier)); err != nil {
		return fmt.Errorf("%T.pb_identifier (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:pb_identifier: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_version", thrift.STRING, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:pb_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbVersion)); err != nil {
		return fmt.Errorf("%T.pb_version (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:pb_version: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_timestamp", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:coord_timestamp: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordTimestamp)); err != nil {
		return fmt.Errorf("%T.coord_timestamp (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:coord_timestamp: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:coord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Coord)); err != nil {
		return fmt.Errorf("%T.coord (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:coord: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pc", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:pc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pc)); err != nil {
		return fmt.Errorf("%T.pc (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:pc: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dob", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:dob: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dob)); err != nil {
		return fmt.Errorf("%T.dob (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:dob: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gender", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:gender: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Gender)); err != nil {
		return fmt.Errorf("%T.gender (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:gender: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spot", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:spot: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Spot)); err != nil {
		return fmt.Errorf("%T.spot (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:spot: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:network: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lm_config", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:lm_config: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LmConfig)); err != nil {
		return fmt.Errorf("%T.lm_config (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:lm_config: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lm_res", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:lm_res: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LmRes)); err != nil {
		return fmt.Errorf("%T.lm_res (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:lm_res: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("avg", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:avg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Avg)); err != nil {
		return fmt.Errorf("%T.avg (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:avg: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("num", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Num)); err != nil {
		return fmt.Errorf("%T.num (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:num: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hh", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:hh: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hh)); err != nil {
		return fmt.Errorf("%T.hh (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:hh: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:search: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Search)); err != nil {
		return fmt.Errorf("%T.search (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:search: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:url: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("referer", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:referer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Referer)); err != nil {
		return fmt.Errorf("%T.referer (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:referer: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:search_id: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pb_name", thrift.STRING, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:pb_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PbName)); err != nil {
		return fmt.Errorf("%T.pb_name (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:pb_name: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lpkg", thrift.STRING, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:lpkg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Lpkg)); err != nil {
		return fmt.Errorf("%T.lpkg (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:lpkg: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:sv: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_platform", thrift.I32, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:sdk_platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkPlatform)); err != nil {
		return fmt.Errorf("%T.sdk_platform (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:sdk_platform: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isv", thrift.I32, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:isv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Isv)); err != nil {
		return fmt.Errorf("%T.isv (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:isv: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField51(oprot thrift.TProtocol) (err error) {
	if p.AltDims != nil {
		if err := oprot.WriteFieldBegin("alt_dims", thrift.LIST, 51); err != nil {
			return fmt.Errorf("%T write field begin error 51:alt_dims: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AltDims)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AltDims {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 51:alt_dims: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jb", thrift.I32, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:jb: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Jb)); err != nil {
		return fmt.Errorf("%T.jb (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:jb: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ma", thrift.STRING, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:ma: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ma)); err != nil {
		return fmt.Errorf("%T.ma (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:ma: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pt", thrift.I32, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:pt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pt)); err != nil {
		return fmt.Errorf("%T.pt (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:pt: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ducookie", thrift.STRING, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:ducookie: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ducookie)); err != nil {
		return fmt.Errorf("%T.ducookie (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:ducookie: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.I64, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:duid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:duid: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:oid: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("intermediary", thrift.STRING, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:intermediary: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Intermediary)); err != nil {
		return fmt.Errorf("%T.intermediary (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:intermediary: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField59(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 59); err != nil {
		return fmt.Errorf("%T write field begin error 59:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (59) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 59:dmac: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:amac: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:creative_id: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField80(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pmid", thrift.I32, 80); err != nil {
		return fmt.Errorf("%T write field begin error 80:pmid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pmid)); err != nil {
		return fmt.Errorf("%T.pmid (80) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 80:pmid: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField81(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ifa", thrift.STRING, 81); err != nil {
		return fmt.Errorf("%T write field begin error 81:ifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ifa)); err != nil {
		return fmt.Errorf("%T.ifa (81) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 81:ifa: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField82(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lat", thrift.STRING, 82); err != nil {
		return fmt.Errorf("%T write field begin error 82:lat: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Lat)); err != nil {
		return fmt.Errorf("%T.lat (82) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 82:lat: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField90(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rawpt", thrift.I32, 90); err != nil {
		return fmt.Errorf("%T write field begin error 90:rawpt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rawpt)); err != nil {
		return fmt.Errorf("%T.rawpt (90) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 90:rawpt: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField91(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lac", thrift.STRING, 91); err != nil {
		return fmt.Errorf("%T write field begin error 91:lac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Lac)); err != nil {
		return fmt.Errorf("%T.lac (91) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 91:lac: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField92(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cell", thrift.STRING, 92); err != nil {
		return fmt.Errorf("%T write field begin error 92:cell: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cell)); err != nil {
		return fmt.Errorf("%T.cell (92) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 92:cell: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField93(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mcc", thrift.STRING, 93); err != nil {
		return fmt.Errorf("%T write field begin error 93:mcc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mcc)); err != nil {
		return fmt.Errorf("%T.mcc (93) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 93:mcc: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField94(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mnc", thrift.STRING, 94); err != nil {
		return fmt.Errorf("%T write field begin error 94:mnc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Mnc)); err != nil {
		return fmt.Errorf("%T.mnc (94) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 94:mnc: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField95(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("scan", thrift.STRING, 95); err != nil {
		return fmt.Errorf("%T write field begin error 95:scan: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Scan)); err != nil {
		return fmt.Errorf("%T.scan (95) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 95:scan: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField96(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_acc", thrift.I32, 96); err != nil {
		return fmt.Errorf("%T write field begin error 96:coord_acc: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CoordAcc)); err != nil {
		return fmt.Errorf("%T.coord_acc (96) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 96:coord_acc: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:coord_status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CoordStatus)); err != nil {
		return fmt.Errorf("%T.coord_status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:coord_status: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_ec", thrift.I32, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:coord_ec: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CoordEc)); err != nil {
		return fmt.Errorf("%T.coord_ec (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:coord_ec: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField99(oprot thrift.TProtocol) (err error) {
	if p.ImpressionFulfilled != nil {
		if err := oprot.WriteFieldBegin("impressionFulfilled", thrift.LIST, 99); err != nil {
			return fmt.Errorf("%T write field begin error 99:impressionFulfilled: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ImpressionFulfilled)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpressionFulfilled {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 99:impressionFulfilled: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField100(oprot thrift.TProtocol) (err error) {
	if p.ClickFulfilled != nil {
		if err := oprot.WriteFieldBegin("clickFulfilled", thrift.LIST, 100); err != nil {
			return fmt.Errorf("%T write field begin error 100:clickFulfilled: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ClickFulfilled)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClickFulfilled {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 100:clickFulfilled: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField101(oprot thrift.TProtocol) (err error) {
	if p.CachedResource != nil {
		if err := oprot.WriteFieldBegin("cachedResource", thrift.LIST, 101); err != nil {
			return fmt.Errorf("%T write field begin error 101:cachedResource: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CachedResource)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CachedResource {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 101:cachedResource: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField102(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("firstVisitTime", thrift.I64, 102); err != nil {
		return fmt.Errorf("%T write field begin error 102:firstVisitTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FirstVisitTime)); err != nil {
		return fmt.Errorf("%T.firstVisitTime (102) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 102:firstVisitTime: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField110(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anid", thrift.STRING, 110); err != nil {
		return fmt.Errorf("%T write field begin error 110:anid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Anid)); err != nil {
		return fmt.Errorf("%T.anid (110) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 110:anid: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField111(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anid2", thrift.STRING, 111); err != nil {
		return fmt.Errorf("%T write field begin error 111:anid2: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Anid2)); err != nil {
		return fmt.Errorf("%T.anid2 (111) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 111:anid2: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField112(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 112); err != nil {
			return fmt.Errorf("%T write field begin error 112:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 112:ids: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField120(oprot thrift.TProtocol) (err error) {
	if p.CapabilityModifier != nil {
		if err := oprot.WriteFieldBegin("capabilityModifier", thrift.LIST, 120); err != nil {
			return fmt.Errorf("%T write field begin error 120:capabilityModifier: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CapabilityModifier)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CapabilityModifier {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 120:capabilityModifier: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField121(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("an", thrift.STRING, 121); err != nil {
		return fmt.Errorf("%T write field begin error 121:an: %s", p, err)
	}
	if err := oprot.WriteString(string(p.An)); err != nil {
		return fmt.Errorf("%T.an (121) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 121:an: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField122(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("serverHostName", thrift.STRING, 122); err != nil {
		return fmt.Errorf("%T write field begin error 122:serverHostName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ServerHostName)); err != nil {
		return fmt.Errorf("%T.serverHostName (122) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 122:serverHostName: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField123(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastVisitTime", thrift.I64, 123); err != nil {
		return fmt.Errorf("%T write field begin error 123:lastVisitTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastVisitTime)); err != nil {
		return fmt.Errorf("%T.lastVisitTime (123) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 123:lastVisitTime: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField124(oprot thrift.TProtocol) (err error) {
	if p.ReqContainers != nil {
		if err := oprot.WriteFieldBegin("reqContainers", thrift.LIST, 124); err != nil {
			return fmt.Errorf("%T write field begin error 124:reqContainers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ReqContainers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ReqContainers {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 124:reqContainers: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField125(oprot thrift.TProtocol) (err error) {
	if p.InstalledPackages != nil {
		if err := oprot.WriteFieldBegin("installedPackages", thrift.LIST, 125); err != nil {
			return fmt.Errorf("%T write field begin error 125:installedPackages: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.InstalledPackages)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.InstalledPackages {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 125:installedPackages: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) writeField126(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 126); err != nil {
		return fmt.Errorf("%T write field begin error 126:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (126) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 126:channel: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField127(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subChannel", thrift.STRING, 127); err != nil {
		return fmt.Errorf("%T write field begin error 127:subChannel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubChannel)); err != nil {
		return fmt.Errorf("%T.subChannel (127) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 127:subChannel: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField128(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 128); err != nil {
		return fmt.Errorf("%T write field begin error 128:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (128) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 128:title: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField129(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("partnerId", thrift.I32, 129); err != nil {
		return fmt.Errorf("%T write field begin error 129:partnerId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PartnerId)); err != nil {
		return fmt.Errorf("%T.partnerId (129) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 129:partnerId: %s", p, err)
	}
	return err
}

func (p *UiRequest) writeField130(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequestSourceType() {
		if err := oprot.WriteFieldBegin("requestSourceType", thrift.I32, 130); err != nil {
			return fmt.Errorf("%T write field begin error 130:requestSourceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RequestSourceType)); err != nil {
			return fmt.Errorf("%T.requestSourceType (130) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 130:requestSourceType: %s", p, err)
		}
	}
	return err
}

func (p *UiRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UiRequest(%+v)", *p)
}

type AsResponse struct {
	Status    int32  `thrift:"status,1" json:"status"`
	Content   string `thrift:"content,2" json:"content"`
	SetCookie string `thrift:"set_cookie,3" json:"set_cookie"`
}

func NewAsResponse() *AsResponse {
	return &AsResponse{}
}

func (p *AsResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AsResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AsResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AsResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SetCookie = v
	}
	return nil
}

func (p *AsResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AsResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AsResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *AsResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:content: %s", p, err)
	}
	return err
}

func (p *AsResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("set_cookie", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:set_cookie: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SetCookie)); err != nil {
		return fmt.Errorf("%T.set_cookie (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:set_cookie: %s", p, err)
	}
	return err
}

func (p *AsResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AsResponse(%+v)", *p)
}

type UiAdWrapper struct {
	Ad         *adserver_types.ResAd `thrift:"ad,1" json:"ad"`
	LandingUrl string                `thrift:"landingUrl,2" json:"landingUrl"`
	RpUrl      string                `thrift:"rpUrl,3" json:"rpUrl"`
	RpTracker  string                `thrift:"rpTracker,4" json:"rpTracker"`
	TrackerId  string                `thrift:"trackerId,5" json:"trackerId"`
	DomobTrack string                `thrift:"domobTrack,6" json:"domobTrack"`
}

func NewUiAdWrapper() *UiAdWrapper {
	return &UiAdWrapper{}
}

func (p *UiAdWrapper) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UiAdWrapper) readField1(iprot thrift.TProtocol) error {
	p.Ad = adserver_types.NewResAd()
	if err := p.Ad.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ad)
	}
	return nil
}

func (p *UiAdWrapper) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.LandingUrl = v
	}
	return nil
}

func (p *UiAdWrapper) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RpUrl = v
	}
	return nil
}

func (p *UiAdWrapper) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RpTracker = v
	}
	return nil
}

func (p *UiAdWrapper) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TrackerId = v
	}
	return nil
}

func (p *UiAdWrapper) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DomobTrack = v
	}
	return nil
}

func (p *UiAdWrapper) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UiAdWrapper"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UiAdWrapper) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ad != nil {
		if err := oprot.WriteFieldBegin("ad", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ad: %s", p, err)
		}
		if err := p.Ad.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ad)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ad: %s", p, err)
		}
	}
	return err
}

func (p *UiAdWrapper) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landingUrl", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:landingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LandingUrl)); err != nil {
		return fmt.Errorf("%T.landingUrl (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:landingUrl: %s", p, err)
	}
	return err
}

func (p *UiAdWrapper) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rpUrl", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rpUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RpUrl)); err != nil {
		return fmt.Errorf("%T.rpUrl (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rpUrl: %s", p, err)
	}
	return err
}

func (p *UiAdWrapper) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rpTracker", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:rpTracker: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RpTracker)); err != nil {
		return fmt.Errorf("%T.rpTracker (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:rpTracker: %s", p, err)
	}
	return err
}

func (p *UiAdWrapper) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackerId", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:trackerId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackerId)); err != nil {
		return fmt.Errorf("%T.trackerId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:trackerId: %s", p, err)
	}
	return err
}

func (p *UiAdWrapper) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("domobTrack", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:domobTrack: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DomobTrack)); err != nil {
		return fmt.Errorf("%T.domobTrack (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:domobTrack: %s", p, err)
	}
	return err
}

func (p *UiAdWrapper) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UiAdWrapper(%+v)", *p)
}

type UiDetWrapper struct {
	Det               *adserver_types.AdserverDetRes `thrift:"det,1" json:"det"`
	ConfigTimestamp   int32                          `thrift:"config_timestamp,2" json:"config_timestamp"`
	ResourceTimestamp int32                          `thrift:"resource_timestamp,3" json:"resource_timestamp"`
}

func NewUiDetWrapper() *UiDetWrapper {
	return &UiDetWrapper{}
}

func (p *UiDetWrapper) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UiDetWrapper) readField1(iprot thrift.TProtocol) error {
	p.Det = adserver_types.NewAdserverDetRes()
	if err := p.Det.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Det)
	}
	return nil
}

func (p *UiDetWrapper) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ConfigTimestamp = v
	}
	return nil
}

func (p *UiDetWrapper) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ResourceTimestamp = v
	}
	return nil
}

func (p *UiDetWrapper) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UiDetWrapper"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UiDetWrapper) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Det != nil {
		if err := oprot.WriteFieldBegin("det", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:det: %s", p, err)
		}
		if err := p.Det.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Det)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:det: %s", p, err)
		}
	}
	return err
}

func (p *UiDetWrapper) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("config_timestamp", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:config_timestamp: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ConfigTimestamp)); err != nil {
		return fmt.Errorf("%T.config_timestamp (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:config_timestamp: %s", p, err)
	}
	return err
}

func (p *UiDetWrapper) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("resource_timestamp", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:resource_timestamp: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ResourceTimestamp)); err != nil {
		return fmt.Errorf("%T.resource_timestamp (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:resource_timestamp: %s", p, err)
	}
	return err
}

func (p *UiDetWrapper) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UiDetWrapper(%+v)", *p)
}

type AsRawResponse struct {
	Status                     int32                         `thrift:"status,1" json:"status"`
	Ads                        []*UiAdWrapper                `thrift:"ads,2" json:"ads"`
	Det                        *UiDetWrapper                 `thrift:"det,3" json:"det"`
	CookieId                   string                        `thrift:"cookieId,4" json:"cookieId"`
	SearchId                   LargeIdInt                    `thrift:"searchId,5" json:"searchId"`
	ExpId                      int32                         `thrift:"expId,6" json:"expId"`
	OsCode                     int32                         `thrift:"osCode,7" json:"osCode"`
	DeviceCode                 int32                         `thrift:"deviceCode,8" json:"deviceCode"`
	PlacementSettingExtInfo    string                        `thrift:"placementSettingExtInfo,9" json:"placementSettingExtInfo"`
	ExtInfo                    string                        `thrift:"extInfo,10" json:"extInfo"`
	MuteVideoSetting           int32                         `thrift:"muteVideoSetting,11" json:"muteVideoSetting"`
	RegionCode                 IdInt                         `thrift:"regionCode,12" json:"regionCode"`
	PreIssuedResource          []*common.ResourceGroup       `thrift:"preIssuedResource,13" json:"preIssuedResource"`
	PlacementSettingExtInfoMap map[string]string             `thrift:"placementSettingExtInfoMap,14" json:"placementSettingExtInfoMap"`
	Latitude                   float64                       `thrift:"latitude,15" json:"latitude"`
	Longitude                  float64                       `thrift:"longitude,16" json:"longitude"`
	AdContainers               []*adserver_types.AdContainer `thrift:"adContainers,17" json:"adContainers"`
	AdChannels                 []*common.Channel             `thrift:"adChannels,18" json:"adChannels"`
	PartnerId                  int32                         `thrift:"partnerId,19" json:"partnerId"`
	ResponseProcessType        enums.ResponseProcessType     `thrift:"responseProcessType,20" json:"responseProcessType"`
	PartnerAppId               int64                         `thrift:"partnerAppId,21" json:"partnerAppId"`
	PartnerPlacementId         int64                         `thrift:"partnerPlacementId,22" json:"partnerPlacementId"`
}

func NewAsRawResponse() *AsRawResponse {
	return &AsRawResponse{
		ResponseProcessType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AsRawResponse) IsSetResponseProcessType() bool {
	return int64(p.ResponseProcessType) != math.MinInt32-1
}

func (p *AsRawResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.MAP {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AsRawResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AsRawResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ads = make([]*UiAdWrapper, 0, size)
	for i := 0; i < size; i++ {
		_elem8 := NewUiAdWrapper()
		if err := _elem8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem8)
		}
		p.Ads = append(p.Ads, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AsRawResponse) readField3(iprot thrift.TProtocol) error {
	p.Det = NewUiDetWrapper()
	if err := p.Det.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Det)
	}
	return nil
}

func (p *AsRawResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CookieId = v
	}
	return nil
}

func (p *AsRawResponse) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *AsRawResponse) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ExpId = v
	}
	return nil
}

func (p *AsRawResponse) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *AsRawResponse) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *AsRawResponse) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PlacementSettingExtInfo = v
	}
	return nil
}

func (p *AsRawResponse) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *AsRawResponse) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.MuteVideoSetting = v
	}
	return nil
}

func (p *AsRawResponse) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.RegionCode = IdInt(v)
	}
	return nil
}

func (p *AsRawResponse) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PreIssuedResource = make([]*common.ResourceGroup, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := common.NewResourceGroup()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.PreIssuedResource = append(p.PreIssuedResource, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AsRawResponse) readField14(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.PlacementSettingExtInfoMap = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key10 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key10 = v
		}
		var _val11 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val11 = v
		}
		p.PlacementSettingExtInfoMap[_key10] = _val11
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AsRawResponse) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *AsRawResponse) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *AsRawResponse) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdContainers = make([]*adserver_types.AdContainer, 0, size)
	for i := 0; i < size; i++ {
		_elem12 := adserver_types.NewAdContainer()
		if err := _elem12.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem12)
		}
		p.AdContainers = append(p.AdContainers, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AsRawResponse) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdChannels = make([]*common.Channel, 0, size)
	for i := 0; i < size; i++ {
		_elem13 := common.NewChannel()
		if err := _elem13.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem13)
		}
		p.AdChannels = append(p.AdChannels, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AsRawResponse) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.PartnerId = v
	}
	return nil
}

func (p *AsRawResponse) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ResponseProcessType = enums.ResponseProcessType(v)
	}
	return nil
}

func (p *AsRawResponse) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.PartnerAppId = v
	}
	return nil
}

func (p *AsRawResponse) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.PartnerPlacementId = v
	}
	return nil
}

func (p *AsRawResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AsRawResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AsRawResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ads != nil {
		if err := oprot.WriteFieldBegin("ads", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ads: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Ads)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ads {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ads: %s", p, err)
		}
	}
	return err
}

func (p *AsRawResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Det != nil {
		if err := oprot.WriteFieldBegin("det", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:det: %s", p, err)
		}
		if err := p.Det.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Det)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:det: %s", p, err)
		}
	}
	return err
}

func (p *AsRawResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookieId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cookieId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CookieId)); err != nil {
		return fmt.Errorf("%T.cookieId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cookieId: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:searchId: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:expId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExpId)); err != nil {
		return fmt.Errorf("%T.expId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:expId: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("osCode", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:osCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.osCode (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:osCode: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceCode", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:deviceCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.deviceCode (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:deviceCode: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementSettingExtInfo", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:placementSettingExtInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PlacementSettingExtInfo)); err != nil {
		return fmt.Errorf("%T.placementSettingExtInfo (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:placementSettingExtInfo: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:extInfo: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("muteVideoSetting", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:muteVideoSetting: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MuteVideoSetting)); err != nil {
		return fmt.Errorf("%T.muteVideoSetting (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:muteVideoSetting: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regionCode", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:regionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.regionCode (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:regionCode: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField13(oprot thrift.TProtocol) (err error) {
	if p.PreIssuedResource != nil {
		if err := oprot.WriteFieldBegin("preIssuedResource", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:preIssuedResource: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PreIssuedResource)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PreIssuedResource {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:preIssuedResource: %s", p, err)
		}
	}
	return err
}

func (p *AsRawResponse) writeField14(oprot thrift.TProtocol) (err error) {
	if p.PlacementSettingExtInfoMap != nil {
		if err := oprot.WriteFieldBegin("placementSettingExtInfoMap", thrift.MAP, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:placementSettingExtInfoMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.PlacementSettingExtInfoMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.PlacementSettingExtInfoMap {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:placementSettingExtInfoMap: %s", p, err)
		}
	}
	return err
}

func (p *AsRawResponse) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:latitude: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:longitude: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField17(oprot thrift.TProtocol) (err error) {
	if p.AdContainers != nil {
		if err := oprot.WriteFieldBegin("adContainers", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:adContainers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdContainers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdContainers {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:adContainers: %s", p, err)
		}
	}
	return err
}

func (p *AsRawResponse) writeField18(oprot thrift.TProtocol) (err error) {
	if p.AdChannels != nil {
		if err := oprot.WriteFieldBegin("adChannels", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:adChannels: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdChannels)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdChannels {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:adChannels: %s", p, err)
		}
	}
	return err
}

func (p *AsRawResponse) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("partnerId", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:partnerId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PartnerId)); err != nil {
		return fmt.Errorf("%T.partnerId (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:partnerId: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetResponseProcessType() {
		if err := oprot.WriteFieldBegin("responseProcessType", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:responseProcessType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResponseProcessType)); err != nil {
			return fmt.Errorf("%T.responseProcessType (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:responseProcessType: %s", p, err)
		}
	}
	return err
}

func (p *AsRawResponse) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("partnerAppId", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:partnerAppId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PartnerAppId)); err != nil {
		return fmt.Errorf("%T.partnerAppId (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:partnerAppId: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("partnerPlacementId", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:partnerPlacementId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PartnerPlacementId)); err != nil {
		return fmt.Errorf("%T.partnerPlacementId (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:partnerPlacementId: %s", p, err)
	}
	return err
}

func (p *AsRawResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AsRawResponse(%+v)", *p)
}
