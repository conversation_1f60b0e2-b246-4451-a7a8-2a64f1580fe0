// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"hdy_server"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  QueryResult searchLandingsByParams(RequestHeader header, LandingParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getLandingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getLandingByTagIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addLanding(RequestHeader header, HdyLanding landing)")
	fmt.Fprintln(os.Stderr, "  void editLanding(RequestHeader header, HdyLanding landing)")
	fmt.Fprintln(os.Stderr, "  void deleteLandingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchLandingTagsByParams(RequestHeader header, LandingTagParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getLandingTagByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addLandingTag(RequestHeader header, LandingTag tag,  landingIds)")
	fmt.Fprintln(os.Stderr, "  void editLandingTag(RequestHeader header, LandingTag tag)")
	fmt.Fprintln(os.Stderr, "  void deleteLandingTagByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdPlacementsByParams(RequestHeader header, AdPlacementParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getAdPlacementByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdPlacement(RequestHeader header, AdPlacement adPlacement)")
	fmt.Fprintln(os.Stderr, "  void editAdPlacement(RequestHeader header, AdPlacement adPlacement)")
	fmt.Fprintln(os.Stderr, "  void deleteAdPlacementsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchGuidePlacementsByParams(RequestHeader header, GuidePlacementParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getGuidePlacementByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addGuidePlacement(RequestHeader header, GuidePlacement guidePlacement)")
	fmt.Fprintln(os.Stderr, "  void editGuidePlacement(RequestHeader header, GuidePlacement guidePlacement)")
	fmt.Fprintln(os.Stderr, "  void deleteGuidePlacementsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchMediasByParams(RequestHeader header, MediaSearchParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getMediaByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addMedia(RequestHeader header, Media media)")
	fmt.Fprintln(os.Stderr, "  void editMedia(RequestHeader header, Media media)")
	fmt.Fprintln(os.Stderr, "  void deleteMediasByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchMediaRedirectsByParams(RequestHeader header, MediaRedirectSearchParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getMediaRedirectByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addMediaRedirect(RequestHeader header, MediaRedirect redirect)")
	fmt.Fprintln(os.Stderr, "  void editMediaRedirect(RequestHeader header, MediaRedirect redirect)")
	fmt.Fprintln(os.Stderr, "  void deleteMediaRedirectsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchOrdersByParams(RequestHeader header, OrderSearchParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getOrderByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addOrder(RequestHeader header, Order order)")
	fmt.Fprintln(os.Stderr, "  void editOrder(RequestHeader header, Order order)")
	fmt.Fprintln(os.Stderr, "  void deleteOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void pauseOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchStrategiesByParams(RequestHeader header, StrategySearchParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getStrategyByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addStrategy(RequestHeader header, Strategy strategy)")
	fmt.Fprintln(os.Stderr, "  void editStrategy(RequestHeader header, Strategy strategy)")
	fmt.Fprintln(os.Stderr, "  void deleteStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void pauseStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchCreativesByParams(RequestHeader header, CreativeSearchParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getCreativeByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "  void editCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "  void deleteCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void pauseCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := hdy_server.NewHdyServerServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "searchLandingsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchLandingsByParams requires 4 args")
			flag.Usage()
		}
		arg257 := flag.Arg(1)
		mbTrans258 := thrift.NewTMemoryBufferLen(len(arg257))
		defer mbTrans258.Close()
		_, err259 := mbTrans258.WriteString(arg257)
		if err259 != nil {
			Usage()
			return
		}
		factory260 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt261 := factory260.GetProtocol(mbTrans258)
		argvalue0 := hdy_server.NewRequestHeader()
		err262 := argvalue0.Read(jsProt261)
		if err262 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg263 := flag.Arg(2)
		mbTrans264 := thrift.NewTMemoryBufferLen(len(arg263))
		defer mbTrans264.Close()
		_, err265 := mbTrans264.WriteString(arg263)
		if err265 != nil {
			Usage()
			return
		}
		factory266 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt267 := factory266.GetProtocol(mbTrans264)
		argvalue1 := hdy_server.NewLandingParams()
		err268 := argvalue1.Read(jsProt267)
		if err268 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err269 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err269 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err270 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err270 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchLandingsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getLandingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLandingByIds requires 2 args")
			flag.Usage()
		}
		arg271 := flag.Arg(1)
		mbTrans272 := thrift.NewTMemoryBufferLen(len(arg271))
		defer mbTrans272.Close()
		_, err273 := mbTrans272.WriteString(arg271)
		if err273 != nil {
			Usage()
			return
		}
		factory274 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt275 := factory274.GetProtocol(mbTrans272)
		argvalue0 := hdy_server.NewRequestHeader()
		err276 := argvalue0.Read(jsProt275)
		if err276 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg277 := flag.Arg(2)
		mbTrans278 := thrift.NewTMemoryBufferLen(len(arg277))
		defer mbTrans278.Close()
		_, err279 := mbTrans278.WriteString(arg277)
		if err279 != nil {
			Usage()
			return
		}
		factory280 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt281 := factory280.GetProtocol(mbTrans278)
		containerStruct1 := hdy_server.NewGetLandingByIdsArgs()
		err282 := containerStruct1.ReadField2(jsProt281)
		if err282 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetLandingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getLandingByTagIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLandingByTagIds requires 2 args")
			flag.Usage()
		}
		arg283 := flag.Arg(1)
		mbTrans284 := thrift.NewTMemoryBufferLen(len(arg283))
		defer mbTrans284.Close()
		_, err285 := mbTrans284.WriteString(arg283)
		if err285 != nil {
			Usage()
			return
		}
		factory286 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt287 := factory286.GetProtocol(mbTrans284)
		argvalue0 := hdy_server.NewRequestHeader()
		err288 := argvalue0.Read(jsProt287)
		if err288 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg289 := flag.Arg(2)
		mbTrans290 := thrift.NewTMemoryBufferLen(len(arg289))
		defer mbTrans290.Close()
		_, err291 := mbTrans290.WriteString(arg289)
		if err291 != nil {
			Usage()
			return
		}
		factory292 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt293 := factory292.GetProtocol(mbTrans290)
		containerStruct1 := hdy_server.NewGetLandingByTagIdsArgs()
		err294 := containerStruct1.ReadField2(jsProt293)
		if err294 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetLandingByTagIds(value0, value1))
		fmt.Print("\n")
		break
	case "addLanding":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddLanding requires 2 args")
			flag.Usage()
		}
		arg295 := flag.Arg(1)
		mbTrans296 := thrift.NewTMemoryBufferLen(len(arg295))
		defer mbTrans296.Close()
		_, err297 := mbTrans296.WriteString(arg295)
		if err297 != nil {
			Usage()
			return
		}
		factory298 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt299 := factory298.GetProtocol(mbTrans296)
		argvalue0 := hdy_server.NewRequestHeader()
		err300 := argvalue0.Read(jsProt299)
		if err300 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg301 := flag.Arg(2)
		mbTrans302 := thrift.NewTMemoryBufferLen(len(arg301))
		defer mbTrans302.Close()
		_, err303 := mbTrans302.WriteString(arg301)
		if err303 != nil {
			Usage()
			return
		}
		factory304 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt305 := factory304.GetProtocol(mbTrans302)
		argvalue1 := hdy_server.NewHdyLanding()
		err306 := argvalue1.Read(jsProt305)
		if err306 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddLanding(value0, value1))
		fmt.Print("\n")
		break
	case "editLanding":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditLanding requires 2 args")
			flag.Usage()
		}
		arg307 := flag.Arg(1)
		mbTrans308 := thrift.NewTMemoryBufferLen(len(arg307))
		defer mbTrans308.Close()
		_, err309 := mbTrans308.WriteString(arg307)
		if err309 != nil {
			Usage()
			return
		}
		factory310 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt311 := factory310.GetProtocol(mbTrans308)
		argvalue0 := hdy_server.NewRequestHeader()
		err312 := argvalue0.Read(jsProt311)
		if err312 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg313 := flag.Arg(2)
		mbTrans314 := thrift.NewTMemoryBufferLen(len(arg313))
		defer mbTrans314.Close()
		_, err315 := mbTrans314.WriteString(arg313)
		if err315 != nil {
			Usage()
			return
		}
		factory316 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt317 := factory316.GetProtocol(mbTrans314)
		argvalue1 := hdy_server.NewHdyLanding()
		err318 := argvalue1.Read(jsProt317)
		if err318 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditLanding(value0, value1))
		fmt.Print("\n")
		break
	case "deleteLandingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteLandingByIds requires 2 args")
			flag.Usage()
		}
		arg319 := flag.Arg(1)
		mbTrans320 := thrift.NewTMemoryBufferLen(len(arg319))
		defer mbTrans320.Close()
		_, err321 := mbTrans320.WriteString(arg319)
		if err321 != nil {
			Usage()
			return
		}
		factory322 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt323 := factory322.GetProtocol(mbTrans320)
		argvalue0 := hdy_server.NewRequestHeader()
		err324 := argvalue0.Read(jsProt323)
		if err324 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg325 := flag.Arg(2)
		mbTrans326 := thrift.NewTMemoryBufferLen(len(arg325))
		defer mbTrans326.Close()
		_, err327 := mbTrans326.WriteString(arg325)
		if err327 != nil {
			Usage()
			return
		}
		factory328 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt329 := factory328.GetProtocol(mbTrans326)
		containerStruct1 := hdy_server.NewDeleteLandingByIdsArgs()
		err330 := containerStruct1.ReadField2(jsProt329)
		if err330 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteLandingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchLandingTagsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchLandingTagsByParams requires 4 args")
			flag.Usage()
		}
		arg331 := flag.Arg(1)
		mbTrans332 := thrift.NewTMemoryBufferLen(len(arg331))
		defer mbTrans332.Close()
		_, err333 := mbTrans332.WriteString(arg331)
		if err333 != nil {
			Usage()
			return
		}
		factory334 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt335 := factory334.GetProtocol(mbTrans332)
		argvalue0 := hdy_server.NewRequestHeader()
		err336 := argvalue0.Read(jsProt335)
		if err336 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg337 := flag.Arg(2)
		mbTrans338 := thrift.NewTMemoryBufferLen(len(arg337))
		defer mbTrans338.Close()
		_, err339 := mbTrans338.WriteString(arg337)
		if err339 != nil {
			Usage()
			return
		}
		factory340 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt341 := factory340.GetProtocol(mbTrans338)
		argvalue1 := hdy_server.NewLandingTagParams()
		err342 := argvalue1.Read(jsProt341)
		if err342 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err343 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err343 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err344 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err344 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchLandingTagsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getLandingTagByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLandingTagByIds requires 2 args")
			flag.Usage()
		}
		arg345 := flag.Arg(1)
		mbTrans346 := thrift.NewTMemoryBufferLen(len(arg345))
		defer mbTrans346.Close()
		_, err347 := mbTrans346.WriteString(arg345)
		if err347 != nil {
			Usage()
			return
		}
		factory348 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt349 := factory348.GetProtocol(mbTrans346)
		argvalue0 := hdy_server.NewRequestHeader()
		err350 := argvalue0.Read(jsProt349)
		if err350 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg351 := flag.Arg(2)
		mbTrans352 := thrift.NewTMemoryBufferLen(len(arg351))
		defer mbTrans352.Close()
		_, err353 := mbTrans352.WriteString(arg351)
		if err353 != nil {
			Usage()
			return
		}
		factory354 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt355 := factory354.GetProtocol(mbTrans352)
		containerStruct1 := hdy_server.NewGetLandingTagByIdsArgs()
		err356 := containerStruct1.ReadField2(jsProt355)
		if err356 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetLandingTagByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addLandingTag":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddLandingTag requires 3 args")
			flag.Usage()
		}
		arg357 := flag.Arg(1)
		mbTrans358 := thrift.NewTMemoryBufferLen(len(arg357))
		defer mbTrans358.Close()
		_, err359 := mbTrans358.WriteString(arg357)
		if err359 != nil {
			Usage()
			return
		}
		factory360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt361 := factory360.GetProtocol(mbTrans358)
		argvalue0 := hdy_server.NewRequestHeader()
		err362 := argvalue0.Read(jsProt361)
		if err362 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg363 := flag.Arg(2)
		mbTrans364 := thrift.NewTMemoryBufferLen(len(arg363))
		defer mbTrans364.Close()
		_, err365 := mbTrans364.WriteString(arg363)
		if err365 != nil {
			Usage()
			return
		}
		factory366 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt367 := factory366.GetProtocol(mbTrans364)
		argvalue1 := hdy_server.NewLandingTag()
		err368 := argvalue1.Read(jsProt367)
		if err368 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg369 := flag.Arg(3)
		mbTrans370 := thrift.NewTMemoryBufferLen(len(arg369))
		defer mbTrans370.Close()
		_, err371 := mbTrans370.WriteString(arg369)
		if err371 != nil {
			Usage()
			return
		}
		factory372 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt373 := factory372.GetProtocol(mbTrans370)
		containerStruct2 := hdy_server.NewAddLandingTagArgs()
		err374 := containerStruct2.ReadField3(jsProt373)
		if err374 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.LandingIds
		value2 := argvalue2
		fmt.Print(client.AddLandingTag(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editLandingTag":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditLandingTag requires 2 args")
			flag.Usage()
		}
		arg375 := flag.Arg(1)
		mbTrans376 := thrift.NewTMemoryBufferLen(len(arg375))
		defer mbTrans376.Close()
		_, err377 := mbTrans376.WriteString(arg375)
		if err377 != nil {
			Usage()
			return
		}
		factory378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt379 := factory378.GetProtocol(mbTrans376)
		argvalue0 := hdy_server.NewRequestHeader()
		err380 := argvalue0.Read(jsProt379)
		if err380 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg381 := flag.Arg(2)
		mbTrans382 := thrift.NewTMemoryBufferLen(len(arg381))
		defer mbTrans382.Close()
		_, err383 := mbTrans382.WriteString(arg381)
		if err383 != nil {
			Usage()
			return
		}
		factory384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt385 := factory384.GetProtocol(mbTrans382)
		argvalue1 := hdy_server.NewLandingTag()
		err386 := argvalue1.Read(jsProt385)
		if err386 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditLandingTag(value0, value1))
		fmt.Print("\n")
		break
	case "deleteLandingTagByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteLandingTagByIds requires 2 args")
			flag.Usage()
		}
		arg387 := flag.Arg(1)
		mbTrans388 := thrift.NewTMemoryBufferLen(len(arg387))
		defer mbTrans388.Close()
		_, err389 := mbTrans388.WriteString(arg387)
		if err389 != nil {
			Usage()
			return
		}
		factory390 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt391 := factory390.GetProtocol(mbTrans388)
		argvalue0 := hdy_server.NewRequestHeader()
		err392 := argvalue0.Read(jsProt391)
		if err392 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg393 := flag.Arg(2)
		mbTrans394 := thrift.NewTMemoryBufferLen(len(arg393))
		defer mbTrans394.Close()
		_, err395 := mbTrans394.WriteString(arg393)
		if err395 != nil {
			Usage()
			return
		}
		factory396 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt397 := factory396.GetProtocol(mbTrans394)
		containerStruct1 := hdy_server.NewDeleteLandingTagByIdsArgs()
		err398 := containerStruct1.ReadField2(jsProt397)
		if err398 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteLandingTagByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdPlacementsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchAdPlacementsByParams requires 4 args")
			flag.Usage()
		}
		arg399 := flag.Arg(1)
		mbTrans400 := thrift.NewTMemoryBufferLen(len(arg399))
		defer mbTrans400.Close()
		_, err401 := mbTrans400.WriteString(arg399)
		if err401 != nil {
			Usage()
			return
		}
		factory402 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt403 := factory402.GetProtocol(mbTrans400)
		argvalue0 := hdy_server.NewRequestHeader()
		err404 := argvalue0.Read(jsProt403)
		if err404 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg405 := flag.Arg(2)
		mbTrans406 := thrift.NewTMemoryBufferLen(len(arg405))
		defer mbTrans406.Close()
		_, err407 := mbTrans406.WriteString(arg405)
		if err407 != nil {
			Usage()
			return
		}
		factory408 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt409 := factory408.GetProtocol(mbTrans406)
		argvalue1 := hdy_server.NewAdPlacementParams()
		err410 := argvalue1.Read(jsProt409)
		if err410 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err411 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err411 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err412 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err412 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchAdPlacementsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAdPlacementByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdPlacementByIds requires 2 args")
			flag.Usage()
		}
		arg413 := flag.Arg(1)
		mbTrans414 := thrift.NewTMemoryBufferLen(len(arg413))
		defer mbTrans414.Close()
		_, err415 := mbTrans414.WriteString(arg413)
		if err415 != nil {
			Usage()
			return
		}
		factory416 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt417 := factory416.GetProtocol(mbTrans414)
		argvalue0 := hdy_server.NewRequestHeader()
		err418 := argvalue0.Read(jsProt417)
		if err418 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg419 := flag.Arg(2)
		mbTrans420 := thrift.NewTMemoryBufferLen(len(arg419))
		defer mbTrans420.Close()
		_, err421 := mbTrans420.WriteString(arg419)
		if err421 != nil {
			Usage()
			return
		}
		factory422 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt423 := factory422.GetProtocol(mbTrans420)
		containerStruct1 := hdy_server.NewGetAdPlacementByIdsArgs()
		err424 := containerStruct1.ReadField2(jsProt423)
		if err424 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdPlacementByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addAdPlacement":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdPlacement requires 2 args")
			flag.Usage()
		}
		arg425 := flag.Arg(1)
		mbTrans426 := thrift.NewTMemoryBufferLen(len(arg425))
		defer mbTrans426.Close()
		_, err427 := mbTrans426.WriteString(arg425)
		if err427 != nil {
			Usage()
			return
		}
		factory428 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt429 := factory428.GetProtocol(mbTrans426)
		argvalue0 := hdy_server.NewRequestHeader()
		err430 := argvalue0.Read(jsProt429)
		if err430 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg431 := flag.Arg(2)
		mbTrans432 := thrift.NewTMemoryBufferLen(len(arg431))
		defer mbTrans432.Close()
		_, err433 := mbTrans432.WriteString(arg431)
		if err433 != nil {
			Usage()
			return
		}
		factory434 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt435 := factory434.GetProtocol(mbTrans432)
		argvalue1 := hdy_server.NewAdPlacement()
		err436 := argvalue1.Read(jsProt435)
		if err436 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAdPlacement(value0, value1))
		fmt.Print("\n")
		break
	case "editAdPlacement":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdPlacement requires 2 args")
			flag.Usage()
		}
		arg437 := flag.Arg(1)
		mbTrans438 := thrift.NewTMemoryBufferLen(len(arg437))
		defer mbTrans438.Close()
		_, err439 := mbTrans438.WriteString(arg437)
		if err439 != nil {
			Usage()
			return
		}
		factory440 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt441 := factory440.GetProtocol(mbTrans438)
		argvalue0 := hdy_server.NewRequestHeader()
		err442 := argvalue0.Read(jsProt441)
		if err442 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg443 := flag.Arg(2)
		mbTrans444 := thrift.NewTMemoryBufferLen(len(arg443))
		defer mbTrans444.Close()
		_, err445 := mbTrans444.WriteString(arg443)
		if err445 != nil {
			Usage()
			return
		}
		factory446 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt447 := factory446.GetProtocol(mbTrans444)
		argvalue1 := hdy_server.NewAdPlacement()
		err448 := argvalue1.Read(jsProt447)
		if err448 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditAdPlacement(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdPlacementsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdPlacementsByIds requires 2 args")
			flag.Usage()
		}
		arg449 := flag.Arg(1)
		mbTrans450 := thrift.NewTMemoryBufferLen(len(arg449))
		defer mbTrans450.Close()
		_, err451 := mbTrans450.WriteString(arg449)
		if err451 != nil {
			Usage()
			return
		}
		factory452 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt453 := factory452.GetProtocol(mbTrans450)
		argvalue0 := hdy_server.NewRequestHeader()
		err454 := argvalue0.Read(jsProt453)
		if err454 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg455 := flag.Arg(2)
		mbTrans456 := thrift.NewTMemoryBufferLen(len(arg455))
		defer mbTrans456.Close()
		_, err457 := mbTrans456.WriteString(arg455)
		if err457 != nil {
			Usage()
			return
		}
		factory458 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt459 := factory458.GetProtocol(mbTrans456)
		containerStruct1 := hdy_server.NewDeleteAdPlacementsByIdsArgs()
		err460 := containerStruct1.ReadField2(jsProt459)
		if err460 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteAdPlacementsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchGuidePlacementsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchGuidePlacementsByParams requires 4 args")
			flag.Usage()
		}
		arg461 := flag.Arg(1)
		mbTrans462 := thrift.NewTMemoryBufferLen(len(arg461))
		defer mbTrans462.Close()
		_, err463 := mbTrans462.WriteString(arg461)
		if err463 != nil {
			Usage()
			return
		}
		factory464 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt465 := factory464.GetProtocol(mbTrans462)
		argvalue0 := hdy_server.NewRequestHeader()
		err466 := argvalue0.Read(jsProt465)
		if err466 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg467 := flag.Arg(2)
		mbTrans468 := thrift.NewTMemoryBufferLen(len(arg467))
		defer mbTrans468.Close()
		_, err469 := mbTrans468.WriteString(arg467)
		if err469 != nil {
			Usage()
			return
		}
		factory470 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt471 := factory470.GetProtocol(mbTrans468)
		argvalue1 := hdy_server.NewGuidePlacementParams()
		err472 := argvalue1.Read(jsProt471)
		if err472 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err473 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err473 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err474 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err474 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchGuidePlacementsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getGuidePlacementByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetGuidePlacementByIds requires 2 args")
			flag.Usage()
		}
		arg475 := flag.Arg(1)
		mbTrans476 := thrift.NewTMemoryBufferLen(len(arg475))
		defer mbTrans476.Close()
		_, err477 := mbTrans476.WriteString(arg475)
		if err477 != nil {
			Usage()
			return
		}
		factory478 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt479 := factory478.GetProtocol(mbTrans476)
		argvalue0 := hdy_server.NewRequestHeader()
		err480 := argvalue0.Read(jsProt479)
		if err480 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg481 := flag.Arg(2)
		mbTrans482 := thrift.NewTMemoryBufferLen(len(arg481))
		defer mbTrans482.Close()
		_, err483 := mbTrans482.WriteString(arg481)
		if err483 != nil {
			Usage()
			return
		}
		factory484 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt485 := factory484.GetProtocol(mbTrans482)
		containerStruct1 := hdy_server.NewGetGuidePlacementByIdsArgs()
		err486 := containerStruct1.ReadField2(jsProt485)
		if err486 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetGuidePlacementByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addGuidePlacement":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddGuidePlacement requires 2 args")
			flag.Usage()
		}
		arg487 := flag.Arg(1)
		mbTrans488 := thrift.NewTMemoryBufferLen(len(arg487))
		defer mbTrans488.Close()
		_, err489 := mbTrans488.WriteString(arg487)
		if err489 != nil {
			Usage()
			return
		}
		factory490 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt491 := factory490.GetProtocol(mbTrans488)
		argvalue0 := hdy_server.NewRequestHeader()
		err492 := argvalue0.Read(jsProt491)
		if err492 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg493 := flag.Arg(2)
		mbTrans494 := thrift.NewTMemoryBufferLen(len(arg493))
		defer mbTrans494.Close()
		_, err495 := mbTrans494.WriteString(arg493)
		if err495 != nil {
			Usage()
			return
		}
		factory496 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt497 := factory496.GetProtocol(mbTrans494)
		argvalue1 := hdy_server.NewGuidePlacement()
		err498 := argvalue1.Read(jsProt497)
		if err498 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddGuidePlacement(value0, value1))
		fmt.Print("\n")
		break
	case "editGuidePlacement":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditGuidePlacement requires 2 args")
			flag.Usage()
		}
		arg499 := flag.Arg(1)
		mbTrans500 := thrift.NewTMemoryBufferLen(len(arg499))
		defer mbTrans500.Close()
		_, err501 := mbTrans500.WriteString(arg499)
		if err501 != nil {
			Usage()
			return
		}
		factory502 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt503 := factory502.GetProtocol(mbTrans500)
		argvalue0 := hdy_server.NewRequestHeader()
		err504 := argvalue0.Read(jsProt503)
		if err504 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg505 := flag.Arg(2)
		mbTrans506 := thrift.NewTMemoryBufferLen(len(arg505))
		defer mbTrans506.Close()
		_, err507 := mbTrans506.WriteString(arg505)
		if err507 != nil {
			Usage()
			return
		}
		factory508 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt509 := factory508.GetProtocol(mbTrans506)
		argvalue1 := hdy_server.NewGuidePlacement()
		err510 := argvalue1.Read(jsProt509)
		if err510 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditGuidePlacement(value0, value1))
		fmt.Print("\n")
		break
	case "deleteGuidePlacementsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteGuidePlacementsByIds requires 2 args")
			flag.Usage()
		}
		arg511 := flag.Arg(1)
		mbTrans512 := thrift.NewTMemoryBufferLen(len(arg511))
		defer mbTrans512.Close()
		_, err513 := mbTrans512.WriteString(arg511)
		if err513 != nil {
			Usage()
			return
		}
		factory514 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt515 := factory514.GetProtocol(mbTrans512)
		argvalue0 := hdy_server.NewRequestHeader()
		err516 := argvalue0.Read(jsProt515)
		if err516 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg517 := flag.Arg(2)
		mbTrans518 := thrift.NewTMemoryBufferLen(len(arg517))
		defer mbTrans518.Close()
		_, err519 := mbTrans518.WriteString(arg517)
		if err519 != nil {
			Usage()
			return
		}
		factory520 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt521 := factory520.GetProtocol(mbTrans518)
		containerStruct1 := hdy_server.NewDeleteGuidePlacementsByIdsArgs()
		err522 := containerStruct1.ReadField2(jsProt521)
		if err522 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteGuidePlacementsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchMediasByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchMediasByParams requires 4 args")
			flag.Usage()
		}
		arg523 := flag.Arg(1)
		mbTrans524 := thrift.NewTMemoryBufferLen(len(arg523))
		defer mbTrans524.Close()
		_, err525 := mbTrans524.WriteString(arg523)
		if err525 != nil {
			Usage()
			return
		}
		factory526 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt527 := factory526.GetProtocol(mbTrans524)
		argvalue0 := hdy_server.NewRequestHeader()
		err528 := argvalue0.Read(jsProt527)
		if err528 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg529 := flag.Arg(2)
		mbTrans530 := thrift.NewTMemoryBufferLen(len(arg529))
		defer mbTrans530.Close()
		_, err531 := mbTrans530.WriteString(arg529)
		if err531 != nil {
			Usage()
			return
		}
		factory532 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt533 := factory532.GetProtocol(mbTrans530)
		argvalue1 := hdy_server.NewMediaSearchParams()
		err534 := argvalue1.Read(jsProt533)
		if err534 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err535 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err535 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err536 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err536 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchMediasByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getMediaByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaByIds requires 2 args")
			flag.Usage()
		}
		arg537 := flag.Arg(1)
		mbTrans538 := thrift.NewTMemoryBufferLen(len(arg537))
		defer mbTrans538.Close()
		_, err539 := mbTrans538.WriteString(arg537)
		if err539 != nil {
			Usage()
			return
		}
		factory540 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt541 := factory540.GetProtocol(mbTrans538)
		argvalue0 := hdy_server.NewRequestHeader()
		err542 := argvalue0.Read(jsProt541)
		if err542 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg543 := flag.Arg(2)
		mbTrans544 := thrift.NewTMemoryBufferLen(len(arg543))
		defer mbTrans544.Close()
		_, err545 := mbTrans544.WriteString(arg543)
		if err545 != nil {
			Usage()
			return
		}
		factory546 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt547 := factory546.GetProtocol(mbTrans544)
		containerStruct1 := hdy_server.NewGetMediaByIdsArgs()
		err548 := containerStruct1.ReadField2(jsProt547)
		if err548 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetMediaByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddMedia requires 2 args")
			flag.Usage()
		}
		arg549 := flag.Arg(1)
		mbTrans550 := thrift.NewTMemoryBufferLen(len(arg549))
		defer mbTrans550.Close()
		_, err551 := mbTrans550.WriteString(arg549)
		if err551 != nil {
			Usage()
			return
		}
		factory552 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt553 := factory552.GetProtocol(mbTrans550)
		argvalue0 := hdy_server.NewRequestHeader()
		err554 := argvalue0.Read(jsProt553)
		if err554 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg555 := flag.Arg(2)
		mbTrans556 := thrift.NewTMemoryBufferLen(len(arg555))
		defer mbTrans556.Close()
		_, err557 := mbTrans556.WriteString(arg555)
		if err557 != nil {
			Usage()
			return
		}
		factory558 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt559 := factory558.GetProtocol(mbTrans556)
		argvalue1 := hdy_server.NewMedia()
		err560 := argvalue1.Read(jsProt559)
		if err560 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddMedia(value0, value1))
		fmt.Print("\n")
		break
	case "editMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditMedia requires 2 args")
			flag.Usage()
		}
		arg561 := flag.Arg(1)
		mbTrans562 := thrift.NewTMemoryBufferLen(len(arg561))
		defer mbTrans562.Close()
		_, err563 := mbTrans562.WriteString(arg561)
		if err563 != nil {
			Usage()
			return
		}
		factory564 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt565 := factory564.GetProtocol(mbTrans562)
		argvalue0 := hdy_server.NewRequestHeader()
		err566 := argvalue0.Read(jsProt565)
		if err566 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg567 := flag.Arg(2)
		mbTrans568 := thrift.NewTMemoryBufferLen(len(arg567))
		defer mbTrans568.Close()
		_, err569 := mbTrans568.WriteString(arg567)
		if err569 != nil {
			Usage()
			return
		}
		factory570 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt571 := factory570.GetProtocol(mbTrans568)
		argvalue1 := hdy_server.NewMedia()
		err572 := argvalue1.Read(jsProt571)
		if err572 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditMedia(value0, value1))
		fmt.Print("\n")
		break
	case "deleteMediasByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteMediasByIds requires 2 args")
			flag.Usage()
		}
		arg573 := flag.Arg(1)
		mbTrans574 := thrift.NewTMemoryBufferLen(len(arg573))
		defer mbTrans574.Close()
		_, err575 := mbTrans574.WriteString(arg573)
		if err575 != nil {
			Usage()
			return
		}
		factory576 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt577 := factory576.GetProtocol(mbTrans574)
		argvalue0 := hdy_server.NewRequestHeader()
		err578 := argvalue0.Read(jsProt577)
		if err578 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg579 := flag.Arg(2)
		mbTrans580 := thrift.NewTMemoryBufferLen(len(arg579))
		defer mbTrans580.Close()
		_, err581 := mbTrans580.WriteString(arg579)
		if err581 != nil {
			Usage()
			return
		}
		factory582 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt583 := factory582.GetProtocol(mbTrans580)
		containerStruct1 := hdy_server.NewDeleteMediasByIdsArgs()
		err584 := containerStruct1.ReadField2(jsProt583)
		if err584 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteMediasByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchMediaRedirectsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchMediaRedirectsByParams requires 4 args")
			flag.Usage()
		}
		arg585 := flag.Arg(1)
		mbTrans586 := thrift.NewTMemoryBufferLen(len(arg585))
		defer mbTrans586.Close()
		_, err587 := mbTrans586.WriteString(arg585)
		if err587 != nil {
			Usage()
			return
		}
		factory588 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt589 := factory588.GetProtocol(mbTrans586)
		argvalue0 := hdy_server.NewRequestHeader()
		err590 := argvalue0.Read(jsProt589)
		if err590 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg591 := flag.Arg(2)
		mbTrans592 := thrift.NewTMemoryBufferLen(len(arg591))
		defer mbTrans592.Close()
		_, err593 := mbTrans592.WriteString(arg591)
		if err593 != nil {
			Usage()
			return
		}
		factory594 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt595 := factory594.GetProtocol(mbTrans592)
		argvalue1 := hdy_server.NewMediaRedirectSearchParams()
		err596 := argvalue1.Read(jsProt595)
		if err596 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err597 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err597 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err598 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err598 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchMediaRedirectsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getMediaRedirectByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaRedirectByIds requires 2 args")
			flag.Usage()
		}
		arg599 := flag.Arg(1)
		mbTrans600 := thrift.NewTMemoryBufferLen(len(arg599))
		defer mbTrans600.Close()
		_, err601 := mbTrans600.WriteString(arg599)
		if err601 != nil {
			Usage()
			return
		}
		factory602 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt603 := factory602.GetProtocol(mbTrans600)
		argvalue0 := hdy_server.NewRequestHeader()
		err604 := argvalue0.Read(jsProt603)
		if err604 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg605 := flag.Arg(2)
		mbTrans606 := thrift.NewTMemoryBufferLen(len(arg605))
		defer mbTrans606.Close()
		_, err607 := mbTrans606.WriteString(arg605)
		if err607 != nil {
			Usage()
			return
		}
		factory608 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt609 := factory608.GetProtocol(mbTrans606)
		containerStruct1 := hdy_server.NewGetMediaRedirectByIdsArgs()
		err610 := containerStruct1.ReadField2(jsProt609)
		if err610 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetMediaRedirectByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addMediaRedirect":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddMediaRedirect requires 2 args")
			flag.Usage()
		}
		arg611 := flag.Arg(1)
		mbTrans612 := thrift.NewTMemoryBufferLen(len(arg611))
		defer mbTrans612.Close()
		_, err613 := mbTrans612.WriteString(arg611)
		if err613 != nil {
			Usage()
			return
		}
		factory614 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt615 := factory614.GetProtocol(mbTrans612)
		argvalue0 := hdy_server.NewRequestHeader()
		err616 := argvalue0.Read(jsProt615)
		if err616 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg617 := flag.Arg(2)
		mbTrans618 := thrift.NewTMemoryBufferLen(len(arg617))
		defer mbTrans618.Close()
		_, err619 := mbTrans618.WriteString(arg617)
		if err619 != nil {
			Usage()
			return
		}
		factory620 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt621 := factory620.GetProtocol(mbTrans618)
		argvalue1 := hdy_server.NewMediaRedirect()
		err622 := argvalue1.Read(jsProt621)
		if err622 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddMediaRedirect(value0, value1))
		fmt.Print("\n")
		break
	case "editMediaRedirect":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditMediaRedirect requires 2 args")
			flag.Usage()
		}
		arg623 := flag.Arg(1)
		mbTrans624 := thrift.NewTMemoryBufferLen(len(arg623))
		defer mbTrans624.Close()
		_, err625 := mbTrans624.WriteString(arg623)
		if err625 != nil {
			Usage()
			return
		}
		factory626 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt627 := factory626.GetProtocol(mbTrans624)
		argvalue0 := hdy_server.NewRequestHeader()
		err628 := argvalue0.Read(jsProt627)
		if err628 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg629 := flag.Arg(2)
		mbTrans630 := thrift.NewTMemoryBufferLen(len(arg629))
		defer mbTrans630.Close()
		_, err631 := mbTrans630.WriteString(arg629)
		if err631 != nil {
			Usage()
			return
		}
		factory632 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt633 := factory632.GetProtocol(mbTrans630)
		argvalue1 := hdy_server.NewMediaRedirect()
		err634 := argvalue1.Read(jsProt633)
		if err634 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditMediaRedirect(value0, value1))
		fmt.Print("\n")
		break
	case "deleteMediaRedirectsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteMediaRedirectsByIds requires 2 args")
			flag.Usage()
		}
		arg635 := flag.Arg(1)
		mbTrans636 := thrift.NewTMemoryBufferLen(len(arg635))
		defer mbTrans636.Close()
		_, err637 := mbTrans636.WriteString(arg635)
		if err637 != nil {
			Usage()
			return
		}
		factory638 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt639 := factory638.GetProtocol(mbTrans636)
		argvalue0 := hdy_server.NewRequestHeader()
		err640 := argvalue0.Read(jsProt639)
		if err640 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg641 := flag.Arg(2)
		mbTrans642 := thrift.NewTMemoryBufferLen(len(arg641))
		defer mbTrans642.Close()
		_, err643 := mbTrans642.WriteString(arg641)
		if err643 != nil {
			Usage()
			return
		}
		factory644 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt645 := factory644.GetProtocol(mbTrans642)
		containerStruct1 := hdy_server.NewDeleteMediaRedirectsByIdsArgs()
		err646 := containerStruct1.ReadField2(jsProt645)
		if err646 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteMediaRedirectsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchOrdersByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchOrdersByParams requires 4 args")
			flag.Usage()
		}
		arg647 := flag.Arg(1)
		mbTrans648 := thrift.NewTMemoryBufferLen(len(arg647))
		defer mbTrans648.Close()
		_, err649 := mbTrans648.WriteString(arg647)
		if err649 != nil {
			Usage()
			return
		}
		factory650 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt651 := factory650.GetProtocol(mbTrans648)
		argvalue0 := hdy_server.NewRequestHeader()
		err652 := argvalue0.Read(jsProt651)
		if err652 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg653 := flag.Arg(2)
		mbTrans654 := thrift.NewTMemoryBufferLen(len(arg653))
		defer mbTrans654.Close()
		_, err655 := mbTrans654.WriteString(arg653)
		if err655 != nil {
			Usage()
			return
		}
		factory656 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt657 := factory656.GetProtocol(mbTrans654)
		argvalue1 := hdy_server.NewOrderSearchParams()
		err658 := argvalue1.Read(jsProt657)
		if err658 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err659 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err659 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err660 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err660 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchOrdersByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getOrderByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOrderByIds requires 2 args")
			flag.Usage()
		}
		arg661 := flag.Arg(1)
		mbTrans662 := thrift.NewTMemoryBufferLen(len(arg661))
		defer mbTrans662.Close()
		_, err663 := mbTrans662.WriteString(arg661)
		if err663 != nil {
			Usage()
			return
		}
		factory664 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt665 := factory664.GetProtocol(mbTrans662)
		argvalue0 := hdy_server.NewRequestHeader()
		err666 := argvalue0.Read(jsProt665)
		if err666 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg667 := flag.Arg(2)
		mbTrans668 := thrift.NewTMemoryBufferLen(len(arg667))
		defer mbTrans668.Close()
		_, err669 := mbTrans668.WriteString(arg667)
		if err669 != nil {
			Usage()
			return
		}
		factory670 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt671 := factory670.GetProtocol(mbTrans668)
		containerStruct1 := hdy_server.NewGetOrderByIdsArgs()
		err672 := containerStruct1.ReadField2(jsProt671)
		if err672 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetOrderByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addOrder":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddOrder requires 2 args")
			flag.Usage()
		}
		arg673 := flag.Arg(1)
		mbTrans674 := thrift.NewTMemoryBufferLen(len(arg673))
		defer mbTrans674.Close()
		_, err675 := mbTrans674.WriteString(arg673)
		if err675 != nil {
			Usage()
			return
		}
		factory676 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt677 := factory676.GetProtocol(mbTrans674)
		argvalue0 := hdy_server.NewRequestHeader()
		err678 := argvalue0.Read(jsProt677)
		if err678 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg679 := flag.Arg(2)
		mbTrans680 := thrift.NewTMemoryBufferLen(len(arg679))
		defer mbTrans680.Close()
		_, err681 := mbTrans680.WriteString(arg679)
		if err681 != nil {
			Usage()
			return
		}
		factory682 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt683 := factory682.GetProtocol(mbTrans680)
		argvalue1 := hdy_server.NewOrder()
		err684 := argvalue1.Read(jsProt683)
		if err684 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddOrder(value0, value1))
		fmt.Print("\n")
		break
	case "editOrder":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditOrder requires 2 args")
			flag.Usage()
		}
		arg685 := flag.Arg(1)
		mbTrans686 := thrift.NewTMemoryBufferLen(len(arg685))
		defer mbTrans686.Close()
		_, err687 := mbTrans686.WriteString(arg685)
		if err687 != nil {
			Usage()
			return
		}
		factory688 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt689 := factory688.GetProtocol(mbTrans686)
		argvalue0 := hdy_server.NewRequestHeader()
		err690 := argvalue0.Read(jsProt689)
		if err690 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg691 := flag.Arg(2)
		mbTrans692 := thrift.NewTMemoryBufferLen(len(arg691))
		defer mbTrans692.Close()
		_, err693 := mbTrans692.WriteString(arg691)
		if err693 != nil {
			Usage()
			return
		}
		factory694 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt695 := factory694.GetProtocol(mbTrans692)
		argvalue1 := hdy_server.NewOrder()
		err696 := argvalue1.Read(jsProt695)
		if err696 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditOrder(value0, value1))
		fmt.Print("\n")
		break
	case "deleteOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg697 := flag.Arg(1)
		mbTrans698 := thrift.NewTMemoryBufferLen(len(arg697))
		defer mbTrans698.Close()
		_, err699 := mbTrans698.WriteString(arg697)
		if err699 != nil {
			Usage()
			return
		}
		factory700 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt701 := factory700.GetProtocol(mbTrans698)
		argvalue0 := hdy_server.NewRequestHeader()
		err702 := argvalue0.Read(jsProt701)
		if err702 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg703 := flag.Arg(2)
		mbTrans704 := thrift.NewTMemoryBufferLen(len(arg703))
		defer mbTrans704.Close()
		_, err705 := mbTrans704.WriteString(arg703)
		if err705 != nil {
			Usage()
			return
		}
		factory706 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt707 := factory706.GetProtocol(mbTrans704)
		containerStruct1 := hdy_server.NewDeleteOrdersByIdsArgs()
		err708 := containerStruct1.ReadField2(jsProt707)
		if err708 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "pauseOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg709 := flag.Arg(1)
		mbTrans710 := thrift.NewTMemoryBufferLen(len(arg709))
		defer mbTrans710.Close()
		_, err711 := mbTrans710.WriteString(arg709)
		if err711 != nil {
			Usage()
			return
		}
		factory712 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt713 := factory712.GetProtocol(mbTrans710)
		argvalue0 := hdy_server.NewRequestHeader()
		err714 := argvalue0.Read(jsProt713)
		if err714 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg715 := flag.Arg(2)
		mbTrans716 := thrift.NewTMemoryBufferLen(len(arg715))
		defer mbTrans716.Close()
		_, err717 := mbTrans716.WriteString(arg715)
		if err717 != nil {
			Usage()
			return
		}
		factory718 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt719 := factory718.GetProtocol(mbTrans716)
		containerStruct1 := hdy_server.NewPauseOrdersByIdsArgs()
		err720 := containerStruct1.ReadField2(jsProt719)
		if err720 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg721 := flag.Arg(1)
		mbTrans722 := thrift.NewTMemoryBufferLen(len(arg721))
		defer mbTrans722.Close()
		_, err723 := mbTrans722.WriteString(arg721)
		if err723 != nil {
			Usage()
			return
		}
		factory724 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt725 := factory724.GetProtocol(mbTrans722)
		argvalue0 := hdy_server.NewRequestHeader()
		err726 := argvalue0.Read(jsProt725)
		if err726 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg727 := flag.Arg(2)
		mbTrans728 := thrift.NewTMemoryBufferLen(len(arg727))
		defer mbTrans728.Close()
		_, err729 := mbTrans728.WriteString(arg727)
		if err729 != nil {
			Usage()
			return
		}
		factory730 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt731 := factory730.GetProtocol(mbTrans728)
		containerStruct1 := hdy_server.NewResumeOrdersByIdsArgs()
		err732 := containerStruct1.ReadField2(jsProt731)
		if err732 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchStrategiesByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchStrategiesByParams requires 4 args")
			flag.Usage()
		}
		arg733 := flag.Arg(1)
		mbTrans734 := thrift.NewTMemoryBufferLen(len(arg733))
		defer mbTrans734.Close()
		_, err735 := mbTrans734.WriteString(arg733)
		if err735 != nil {
			Usage()
			return
		}
		factory736 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt737 := factory736.GetProtocol(mbTrans734)
		argvalue0 := hdy_server.NewRequestHeader()
		err738 := argvalue0.Read(jsProt737)
		if err738 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg739 := flag.Arg(2)
		mbTrans740 := thrift.NewTMemoryBufferLen(len(arg739))
		defer mbTrans740.Close()
		_, err741 := mbTrans740.WriteString(arg739)
		if err741 != nil {
			Usage()
			return
		}
		factory742 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt743 := factory742.GetProtocol(mbTrans740)
		argvalue1 := hdy_server.NewStrategySearchParams()
		err744 := argvalue1.Read(jsProt743)
		if err744 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err745 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err745 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err746 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err746 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchStrategiesByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getStrategyByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetStrategyByIds requires 2 args")
			flag.Usage()
		}
		arg747 := flag.Arg(1)
		mbTrans748 := thrift.NewTMemoryBufferLen(len(arg747))
		defer mbTrans748.Close()
		_, err749 := mbTrans748.WriteString(arg747)
		if err749 != nil {
			Usage()
			return
		}
		factory750 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt751 := factory750.GetProtocol(mbTrans748)
		argvalue0 := hdy_server.NewRequestHeader()
		err752 := argvalue0.Read(jsProt751)
		if err752 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg753 := flag.Arg(2)
		mbTrans754 := thrift.NewTMemoryBufferLen(len(arg753))
		defer mbTrans754.Close()
		_, err755 := mbTrans754.WriteString(arg753)
		if err755 != nil {
			Usage()
			return
		}
		factory756 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt757 := factory756.GetProtocol(mbTrans754)
		containerStruct1 := hdy_server.NewGetStrategyByIdsArgs()
		err758 := containerStruct1.ReadField2(jsProt757)
		if err758 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetStrategyByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddStrategy requires 2 args")
			flag.Usage()
		}
		arg759 := flag.Arg(1)
		mbTrans760 := thrift.NewTMemoryBufferLen(len(arg759))
		defer mbTrans760.Close()
		_, err761 := mbTrans760.WriteString(arg759)
		if err761 != nil {
			Usage()
			return
		}
		factory762 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt763 := factory762.GetProtocol(mbTrans760)
		argvalue0 := hdy_server.NewRequestHeader()
		err764 := argvalue0.Read(jsProt763)
		if err764 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg765 := flag.Arg(2)
		mbTrans766 := thrift.NewTMemoryBufferLen(len(arg765))
		defer mbTrans766.Close()
		_, err767 := mbTrans766.WriteString(arg765)
		if err767 != nil {
			Usage()
			return
		}
		factory768 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt769 := factory768.GetProtocol(mbTrans766)
		argvalue1 := hdy_server.NewStrategy()
		err770 := argvalue1.Read(jsProt769)
		if err770 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "editStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditStrategy requires 2 args")
			flag.Usage()
		}
		arg771 := flag.Arg(1)
		mbTrans772 := thrift.NewTMemoryBufferLen(len(arg771))
		defer mbTrans772.Close()
		_, err773 := mbTrans772.WriteString(arg771)
		if err773 != nil {
			Usage()
			return
		}
		factory774 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt775 := factory774.GetProtocol(mbTrans772)
		argvalue0 := hdy_server.NewRequestHeader()
		err776 := argvalue0.Read(jsProt775)
		if err776 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg777 := flag.Arg(2)
		mbTrans778 := thrift.NewTMemoryBufferLen(len(arg777))
		defer mbTrans778.Close()
		_, err779 := mbTrans778.WriteString(arg777)
		if err779 != nil {
			Usage()
			return
		}
		factory780 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt781 := factory780.GetProtocol(mbTrans778)
		argvalue1 := hdy_server.NewStrategy()
		err782 := argvalue1.Read(jsProt781)
		if err782 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "deleteStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg783 := flag.Arg(1)
		mbTrans784 := thrift.NewTMemoryBufferLen(len(arg783))
		defer mbTrans784.Close()
		_, err785 := mbTrans784.WriteString(arg783)
		if err785 != nil {
			Usage()
			return
		}
		factory786 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt787 := factory786.GetProtocol(mbTrans784)
		argvalue0 := hdy_server.NewRequestHeader()
		err788 := argvalue0.Read(jsProt787)
		if err788 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg789 := flag.Arg(2)
		mbTrans790 := thrift.NewTMemoryBufferLen(len(arg789))
		defer mbTrans790.Close()
		_, err791 := mbTrans790.WriteString(arg789)
		if err791 != nil {
			Usage()
			return
		}
		factory792 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt793 := factory792.GetProtocol(mbTrans790)
		containerStruct1 := hdy_server.NewDeleteStrategiesByIdsArgs()
		err794 := containerStruct1.ReadField2(jsProt793)
		if err794 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "pauseStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg795 := flag.Arg(1)
		mbTrans796 := thrift.NewTMemoryBufferLen(len(arg795))
		defer mbTrans796.Close()
		_, err797 := mbTrans796.WriteString(arg795)
		if err797 != nil {
			Usage()
			return
		}
		factory798 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt799 := factory798.GetProtocol(mbTrans796)
		argvalue0 := hdy_server.NewRequestHeader()
		err800 := argvalue0.Read(jsProt799)
		if err800 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg801 := flag.Arg(2)
		mbTrans802 := thrift.NewTMemoryBufferLen(len(arg801))
		defer mbTrans802.Close()
		_, err803 := mbTrans802.WriteString(arg801)
		if err803 != nil {
			Usage()
			return
		}
		factory804 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt805 := factory804.GetProtocol(mbTrans802)
		containerStruct1 := hdy_server.NewPauseStrategiesByIdsArgs()
		err806 := containerStruct1.ReadField2(jsProt805)
		if err806 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg807 := flag.Arg(1)
		mbTrans808 := thrift.NewTMemoryBufferLen(len(arg807))
		defer mbTrans808.Close()
		_, err809 := mbTrans808.WriteString(arg807)
		if err809 != nil {
			Usage()
			return
		}
		factory810 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt811 := factory810.GetProtocol(mbTrans808)
		argvalue0 := hdy_server.NewRequestHeader()
		err812 := argvalue0.Read(jsProt811)
		if err812 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg813 := flag.Arg(2)
		mbTrans814 := thrift.NewTMemoryBufferLen(len(arg813))
		defer mbTrans814.Close()
		_, err815 := mbTrans814.WriteString(arg813)
		if err815 != nil {
			Usage()
			return
		}
		factory816 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt817 := factory816.GetProtocol(mbTrans814)
		containerStruct1 := hdy_server.NewResumeStrategiesByIdsArgs()
		err818 := containerStruct1.ReadField2(jsProt817)
		if err818 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchCreativesByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchCreativesByParams requires 4 args")
			flag.Usage()
		}
		arg819 := flag.Arg(1)
		mbTrans820 := thrift.NewTMemoryBufferLen(len(arg819))
		defer mbTrans820.Close()
		_, err821 := mbTrans820.WriteString(arg819)
		if err821 != nil {
			Usage()
			return
		}
		factory822 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt823 := factory822.GetProtocol(mbTrans820)
		argvalue0 := hdy_server.NewRequestHeader()
		err824 := argvalue0.Read(jsProt823)
		if err824 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg825 := flag.Arg(2)
		mbTrans826 := thrift.NewTMemoryBufferLen(len(arg825))
		defer mbTrans826.Close()
		_, err827 := mbTrans826.WriteString(arg825)
		if err827 != nil {
			Usage()
			return
		}
		factory828 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt829 := factory828.GetProtocol(mbTrans826)
		argvalue1 := hdy_server.NewCreativeSearchParams()
		err830 := argvalue1.Read(jsProt829)
		if err830 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err831 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err831 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err832 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err832 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchCreativesByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getCreativeByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCreativeByIds requires 2 args")
			flag.Usage()
		}
		arg833 := flag.Arg(1)
		mbTrans834 := thrift.NewTMemoryBufferLen(len(arg833))
		defer mbTrans834.Close()
		_, err835 := mbTrans834.WriteString(arg833)
		if err835 != nil {
			Usage()
			return
		}
		factory836 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt837 := factory836.GetProtocol(mbTrans834)
		argvalue0 := hdy_server.NewRequestHeader()
		err838 := argvalue0.Read(jsProt837)
		if err838 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg839 := flag.Arg(2)
		mbTrans840 := thrift.NewTMemoryBufferLen(len(arg839))
		defer mbTrans840.Close()
		_, err841 := mbTrans840.WriteString(arg839)
		if err841 != nil {
			Usage()
			return
		}
		factory842 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt843 := factory842.GetProtocol(mbTrans840)
		containerStruct1 := hdy_server.NewGetCreativeByIdsArgs()
		err844 := containerStruct1.ReadField2(jsProt843)
		if err844 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCreativeByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreative requires 2 args")
			flag.Usage()
		}
		arg845 := flag.Arg(1)
		mbTrans846 := thrift.NewTMemoryBufferLen(len(arg845))
		defer mbTrans846.Close()
		_, err847 := mbTrans846.WriteString(arg845)
		if err847 != nil {
			Usage()
			return
		}
		factory848 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt849 := factory848.GetProtocol(mbTrans846)
		argvalue0 := hdy_server.NewRequestHeader()
		err850 := argvalue0.Read(jsProt849)
		if err850 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg851 := flag.Arg(2)
		mbTrans852 := thrift.NewTMemoryBufferLen(len(arg851))
		defer mbTrans852.Close()
		_, err853 := mbTrans852.WriteString(arg851)
		if err853 != nil {
			Usage()
			return
		}
		factory854 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt855 := factory854.GetProtocol(mbTrans852)
		argvalue1 := hdy_server.NewCreative()
		err856 := argvalue1.Read(jsProt855)
		if err856 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCreative(value0, value1))
		fmt.Print("\n")
		break
	case "editCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCreative requires 2 args")
			flag.Usage()
		}
		arg857 := flag.Arg(1)
		mbTrans858 := thrift.NewTMemoryBufferLen(len(arg857))
		defer mbTrans858.Close()
		_, err859 := mbTrans858.WriteString(arg857)
		if err859 != nil {
			Usage()
			return
		}
		factory860 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt861 := factory860.GetProtocol(mbTrans858)
		argvalue0 := hdy_server.NewRequestHeader()
		err862 := argvalue0.Read(jsProt861)
		if err862 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg863 := flag.Arg(2)
		mbTrans864 := thrift.NewTMemoryBufferLen(len(arg863))
		defer mbTrans864.Close()
		_, err865 := mbTrans864.WriteString(arg863)
		if err865 != nil {
			Usage()
			return
		}
		factory866 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt867 := factory866.GetProtocol(mbTrans864)
		argvalue1 := hdy_server.NewCreative()
		err868 := argvalue1.Read(jsProt867)
		if err868 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCreative(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg869 := flag.Arg(1)
		mbTrans870 := thrift.NewTMemoryBufferLen(len(arg869))
		defer mbTrans870.Close()
		_, err871 := mbTrans870.WriteString(arg869)
		if err871 != nil {
			Usage()
			return
		}
		factory872 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt873 := factory872.GetProtocol(mbTrans870)
		argvalue0 := hdy_server.NewRequestHeader()
		err874 := argvalue0.Read(jsProt873)
		if err874 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg875 := flag.Arg(2)
		mbTrans876 := thrift.NewTMemoryBufferLen(len(arg875))
		defer mbTrans876.Close()
		_, err877 := mbTrans876.WriteString(arg875)
		if err877 != nil {
			Usage()
			return
		}
		factory878 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt879 := factory878.GetProtocol(mbTrans876)
		containerStruct1 := hdy_server.NewDeleteCreativesByIdsArgs()
		err880 := containerStruct1.ReadField2(jsProt879)
		if err880 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "pauseCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg881 := flag.Arg(1)
		mbTrans882 := thrift.NewTMemoryBufferLen(len(arg881))
		defer mbTrans882.Close()
		_, err883 := mbTrans882.WriteString(arg881)
		if err883 != nil {
			Usage()
			return
		}
		factory884 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt885 := factory884.GetProtocol(mbTrans882)
		argvalue0 := hdy_server.NewRequestHeader()
		err886 := argvalue0.Read(jsProt885)
		if err886 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg887 := flag.Arg(2)
		mbTrans888 := thrift.NewTMemoryBufferLen(len(arg887))
		defer mbTrans888.Close()
		_, err889 := mbTrans888.WriteString(arg887)
		if err889 != nil {
			Usage()
			return
		}
		factory890 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt891 := factory890.GetProtocol(mbTrans888)
		containerStruct1 := hdy_server.NewPauseCreativesByIdsArgs()
		err892 := containerStruct1.ReadField2(jsProt891)
		if err892 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg893 := flag.Arg(1)
		mbTrans894 := thrift.NewTMemoryBufferLen(len(arg893))
		defer mbTrans894.Close()
		_, err895 := mbTrans894.WriteString(arg893)
		if err895 != nil {
			Usage()
			return
		}
		factory896 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt897 := factory896.GetProtocol(mbTrans894)
		argvalue0 := hdy_server.NewRequestHeader()
		err898 := argvalue0.Read(jsProt897)
		if err898 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg899 := flag.Arg(2)
		mbTrans900 := thrift.NewTMemoryBufferLen(len(arg899))
		defer mbTrans900.Close()
		_, err901 := mbTrans900.WriteString(arg899)
		if err901 != nil {
			Usage()
			return
		}
		factory902 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt903 := factory902.GetProtocol(mbTrans900)
		containerStruct1 := hdy_server.NewResumeCreativesByIdsArgs()
		err904 := containerStruct1.ReadField2(jsProt903)
		if err904 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
