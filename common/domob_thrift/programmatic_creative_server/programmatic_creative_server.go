// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package programmatic_creative_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = programmatic_creative_types.GoUnusedProtection__

type ProgrammaticCreativeServer interface {
	dm303.DomobService

	// Parameters:
	//  - Header
	//  - Request
	//  - Filter
	GetAppMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, filter *programmatic_creative_types.MaterialFilter) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - AppInfo
	//  - Images
	AddImageMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, images []*programmatic_creative_types.PCImage) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - AppInfo
	//  - Image
	DelImageMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, image *programmatic_creative_types.PCImage) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - AppInfo
	//  - Texts
	AddTextMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, texts []*programmatic_creative_types.PCText) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - AppInfo
	//  - CreativeInfo
	AddCreativeInfo(app_info *programmatic_creative_types.AppIdInfo, creative_info *programmatic_creative_types.CreativeInfo) (r *programmatic_creative_types.CreativeInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - TemplateInfo
	AddTemplate(header *common.RequestHeader, template_info *programmatic_creative_types.TemplateInfo) (r *programmatic_creative_types.TemplateInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - Filter
	GetTemplate(header *common.RequestHeader, filter *programmatic_creative_types.TemplateFilter) (r *programmatic_creative_types.TemplateResultList, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - TemplateInfo
	DelTemplate(header *common.RequestHeader, template_info *programmatic_creative_types.TemplateInfo) (r *programmatic_creative_types.TemplateInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - AppInfo
	//  - Videos
	AddVideoMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, videos []*programmatic_creative_types.PCVideo) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - AppInfo
	//  - Video
	DelVideoMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - TextModel
	AddTextModel(header *common.RequestHeader, text_model *programmatic_creative_types.PCTextModel) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - Text
	GetTextFingerprint(header *common.RequestHeader, text string) (r string, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - AppName
	RefineAppName(header *common.RequestHeader, app_name string) (r string, rae *programmatic_creative_types.PCSException, err error)
}

type ProgrammaticCreativeServerClient struct {
	*dm303.DomobServiceClient
}

func NewProgrammaticCreativeServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ProgrammaticCreativeServerClient {
	return &ProgrammaticCreativeServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewProgrammaticCreativeServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ProgrammaticCreativeServerClient {
	return &ProgrammaticCreativeServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Header
//  - Request
//  - Filter
func (p *ProgrammaticCreativeServerClient) GetAppMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, filter *programmatic_creative_types.MaterialFilter) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetAppMaterial(header, request, filter); err != nil {
		return
	}
	return p.recvGetAppMaterial()
}

func (p *ProgrammaticCreativeServerClient) sendGetAppMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, filter *programmatic_creative_types.MaterialFilter) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetAppMaterialArgs()
	args0.Header = header
	args0.Request = request
	args0.Filter = filter
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvGetAppMaterial() (value *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetAppMaterialResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Rae != nil {
		rae = result1.Rae
	}
	return
}

// Parameters:
//  - Header
//  - AppInfo
//  - Images
func (p *ProgrammaticCreativeServerClient) AddImageMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, images []*programmatic_creative_types.PCImage) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddImageMaterial(header, app_info, images); err != nil {
		return
	}
	return p.recvAddImageMaterial()
}

func (p *ProgrammaticCreativeServerClient) sendAddImageMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, images []*programmatic_creative_types.PCImage) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addImageMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewAddImageMaterialArgs()
	args4.Header = header
	args4.AppInfo = app_info
	args4.Images = images
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvAddImageMaterial() (value *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewAddImageMaterialResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Rae != nil {
		rae = result5.Rae
	}
	return
}

// Parameters:
//  - Header
//  - AppInfo
//  - Image
func (p *ProgrammaticCreativeServerClient) DelImageMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, image *programmatic_creative_types.PCImage) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendDelImageMaterial(header, app_info, image); err != nil {
		return
	}
	return p.recvDelImageMaterial()
}

func (p *ProgrammaticCreativeServerClient) sendDelImageMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, image *programmatic_creative_types.PCImage) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("delImageMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewDelImageMaterialArgs()
	args8.Header = header
	args8.AppInfo = app_info
	args8.Image = image
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvDelImageMaterial() (value *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewDelImageMaterialResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Rae != nil {
		rae = result9.Rae
	}
	return
}

// Parameters:
//  - Header
//  - AppInfo
//  - Texts
func (p *ProgrammaticCreativeServerClient) AddTextMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, texts []*programmatic_creative_types.PCText) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddTextMaterial(header, app_info, texts); err != nil {
		return
	}
	return p.recvAddTextMaterial()
}

func (p *ProgrammaticCreativeServerClient) sendAddTextMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, texts []*programmatic_creative_types.PCText) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addTextMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewAddTextMaterialArgs()
	args12.Header = header
	args12.AppInfo = app_info
	args12.Texts = texts
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvAddTextMaterial() (value *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewAddTextMaterialResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Rae != nil {
		rae = result13.Rae
	}
	return
}

// Parameters:
//  - AppInfo
//  - CreativeInfo
func (p *ProgrammaticCreativeServerClient) AddCreativeInfo(app_info *programmatic_creative_types.AppIdInfo, creative_info *programmatic_creative_types.CreativeInfo) (r *programmatic_creative_types.CreativeInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddCreativeInfo(app_info, creative_info); err != nil {
		return
	}
	return p.recvAddCreativeInfo()
}

func (p *ProgrammaticCreativeServerClient) sendAddCreativeInfo(app_info *programmatic_creative_types.AppIdInfo, creative_info *programmatic_creative_types.CreativeInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addCreativeInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewAddCreativeInfoArgs()
	args16.AppInfo = app_info
	args16.CreativeInfo = creative_info
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvAddCreativeInfo() (value *programmatic_creative_types.CreativeInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewAddCreativeInfoResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Rae != nil {
		rae = result17.Rae
	}
	return
}

// Parameters:
//  - Header
//  - TemplateInfo
func (p *ProgrammaticCreativeServerClient) AddTemplate(header *common.RequestHeader, template_info *programmatic_creative_types.TemplateInfo) (r *programmatic_creative_types.TemplateInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddTemplate(header, template_info); err != nil {
		return
	}
	return p.recvAddTemplate()
}

func (p *ProgrammaticCreativeServerClient) sendAddTemplate(header *common.RequestHeader, template_info *programmatic_creative_types.TemplateInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addTemplate", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewAddTemplateArgs()
	args20.Header = header
	args20.TemplateInfo = template_info
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvAddTemplate() (value *programmatic_creative_types.TemplateInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewAddTemplateResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Rae != nil {
		rae = result21.Rae
	}
	return
}

// Parameters:
//  - Header
//  - Filter
func (p *ProgrammaticCreativeServerClient) GetTemplate(header *common.RequestHeader, filter *programmatic_creative_types.TemplateFilter) (r *programmatic_creative_types.TemplateResultList, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetTemplate(header, filter); err != nil {
		return
	}
	return p.recvGetTemplate()
}

func (p *ProgrammaticCreativeServerClient) sendGetTemplate(header *common.RequestHeader, filter *programmatic_creative_types.TemplateFilter) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTemplate", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetTemplateArgs()
	args24.Header = header
	args24.Filter = filter
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvGetTemplate() (value *programmatic_creative_types.TemplateResultList, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetTemplateResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.Rae != nil {
		rae = result25.Rae
	}
	return
}

// Parameters:
//  - Header
//  - TemplateInfo
func (p *ProgrammaticCreativeServerClient) DelTemplate(header *common.RequestHeader, template_info *programmatic_creative_types.TemplateInfo) (r *programmatic_creative_types.TemplateInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendDelTemplate(header, template_info); err != nil {
		return
	}
	return p.recvDelTemplate()
}

func (p *ProgrammaticCreativeServerClient) sendDelTemplate(header *common.RequestHeader, template_info *programmatic_creative_types.TemplateInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("delTemplate", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewDelTemplateArgs()
	args28.Header = header
	args28.TemplateInfo = template_info
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvDelTemplate() (value *programmatic_creative_types.TemplateInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewDelTemplateResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Rae != nil {
		rae = result29.Rae
	}
	return
}

// Parameters:
//  - Header
//  - AppInfo
//  - Videos
func (p *ProgrammaticCreativeServerClient) AddVideoMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, videos []*programmatic_creative_types.PCVideo) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddVideoMaterial(header, app_info, videos); err != nil {
		return
	}
	return p.recvAddVideoMaterial()
}

func (p *ProgrammaticCreativeServerClient) sendAddVideoMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, videos []*programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addVideoMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewAddVideoMaterialArgs()
	args32.Header = header
	args32.AppInfo = app_info
	args32.Videos = videos
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvAddVideoMaterial() (value *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewAddVideoMaterialResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Rae != nil {
		rae = result33.Rae
	}
	return
}

// Parameters:
//  - Header
//  - AppInfo
//  - Video
func (p *ProgrammaticCreativeServerClient) DelVideoMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendDelVideoMaterial(header, app_info, video); err != nil {
		return
	}
	return p.recvDelVideoMaterial()
}

func (p *ProgrammaticCreativeServerClient) sendDelVideoMaterial(header *common.RequestHeader, app_info *programmatic_creative_types.AppIdInfo, video *programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("delVideoMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewDelVideoMaterialArgs()
	args36.Header = header
	args36.AppInfo = app_info
	args36.Video = video
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvDelVideoMaterial() (value *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewDelVideoMaterialResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Rae != nil {
		rae = result37.Rae
	}
	return
}

// Parameters:
//  - Header
//  - TextModel
func (p *ProgrammaticCreativeServerClient) AddTextModel(header *common.RequestHeader, text_model *programmatic_creative_types.PCTextModel) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddTextModel(header, text_model); err != nil {
		return
	}
	return p.recvAddTextModel()
}

func (p *ProgrammaticCreativeServerClient) sendAddTextModel(header *common.RequestHeader, text_model *programmatic_creative_types.PCTextModel) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addTextModel", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewAddTextModelArgs()
	args40.Header = header
	args40.TextModel = text_model
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvAddTextModel() (value programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewAddTextModelResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Rae != nil {
		rae = result41.Rae
	}
	return
}

// Parameters:
//  - Header
//  - Text
func (p *ProgrammaticCreativeServerClient) GetTextFingerprint(header *common.RequestHeader, text string) (r string, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetTextFingerprint(header, text); err != nil {
		return
	}
	return p.recvGetTextFingerprint()
}

func (p *ProgrammaticCreativeServerClient) sendGetTextFingerprint(header *common.RequestHeader, text string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTextFingerprint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetTextFingerprintArgs()
	args44.Header = header
	args44.Text = text
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvGetTextFingerprint() (value string, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetTextFingerprintResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.Rae != nil {
		rae = result45.Rae
	}
	return
}

// Parameters:
//  - Header
//  - AppName
func (p *ProgrammaticCreativeServerClient) RefineAppName(header *common.RequestHeader, app_name string) (r string, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendRefineAppName(header, app_name); err != nil {
		return
	}
	return p.recvRefineAppName()
}

func (p *ProgrammaticCreativeServerClient) sendRefineAppName(header *common.RequestHeader, app_name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("refineAppName", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewRefineAppNameArgs()
	args48.Header = header
	args48.AppName = app_name
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ProgrammaticCreativeServerClient) recvRefineAppName() (value string, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewRefineAppNameResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Rae != nil {
		rae = result49.Rae
	}
	return
}

type ProgrammaticCreativeServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewProgrammaticCreativeServerProcessor(handler ProgrammaticCreativeServer) *ProgrammaticCreativeServerProcessor {
	self52 := &ProgrammaticCreativeServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self52.AddToProcessorMap("getAppMaterial", &programmaticCreativeServerProcessorGetAppMaterial{handler: handler})
	self52.AddToProcessorMap("addImageMaterial", &programmaticCreativeServerProcessorAddImageMaterial{handler: handler})
	self52.AddToProcessorMap("delImageMaterial", &programmaticCreativeServerProcessorDelImageMaterial{handler: handler})
	self52.AddToProcessorMap("addTextMaterial", &programmaticCreativeServerProcessorAddTextMaterial{handler: handler})
	self52.AddToProcessorMap("addCreativeInfo", &programmaticCreativeServerProcessorAddCreativeInfo{handler: handler})
	self52.AddToProcessorMap("addTemplate", &programmaticCreativeServerProcessorAddTemplate{handler: handler})
	self52.AddToProcessorMap("getTemplate", &programmaticCreativeServerProcessorGetTemplate{handler: handler})
	self52.AddToProcessorMap("delTemplate", &programmaticCreativeServerProcessorDelTemplate{handler: handler})
	self52.AddToProcessorMap("addVideoMaterial", &programmaticCreativeServerProcessorAddVideoMaterial{handler: handler})
	self52.AddToProcessorMap("delVideoMaterial", &programmaticCreativeServerProcessorDelVideoMaterial{handler: handler})
	self52.AddToProcessorMap("addTextModel", &programmaticCreativeServerProcessorAddTextModel{handler: handler})
	self52.AddToProcessorMap("getTextFingerprint", &programmaticCreativeServerProcessorGetTextFingerprint{handler: handler})
	self52.AddToProcessorMap("refineAppName", &programmaticCreativeServerProcessorRefineAppName{handler: handler})
	return self52
}

type programmaticCreativeServerProcessorGetAppMaterial struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorGetAppMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppMaterialResult()
	if result.Success, result.Rae, err = p.handler.GetAppMaterial(args.Header, args.Request, args.Filter); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppMaterial: "+err.Error())
		oprot.WriteMessageBegin("getAppMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorAddImageMaterial struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorAddImageMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddImageMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addImageMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddImageMaterialResult()
	if result.Success, result.Rae, err = p.handler.AddImageMaterial(args.Header, args.AppInfo, args.Images); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addImageMaterial: "+err.Error())
		oprot.WriteMessageBegin("addImageMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addImageMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorDelImageMaterial struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorDelImageMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDelImageMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("delImageMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDelImageMaterialResult()
	if result.Success, result.Rae, err = p.handler.DelImageMaterial(args.Header, args.AppInfo, args.Image); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing delImageMaterial: "+err.Error())
		oprot.WriteMessageBegin("delImageMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("delImageMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorAddTextMaterial struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorAddTextMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddTextMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addTextMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddTextMaterialResult()
	if result.Success, result.Rae, err = p.handler.AddTextMaterial(args.Header, args.AppInfo, args.Texts); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addTextMaterial: "+err.Error())
		oprot.WriteMessageBegin("addTextMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addTextMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorAddCreativeInfo struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorAddCreativeInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCreativeInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addCreativeInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCreativeInfoResult()
	if result.Success, result.Rae, err = p.handler.AddCreativeInfo(args.AppInfo, args.CreativeInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addCreativeInfo: "+err.Error())
		oprot.WriteMessageBegin("addCreativeInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addCreativeInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorAddTemplate struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorAddTemplate) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddTemplateArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddTemplateResult()
	if result.Success, result.Rae, err = p.handler.AddTemplate(args.Header, args.TemplateInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addTemplate: "+err.Error())
		oprot.WriteMessageBegin("addTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addTemplate", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorGetTemplate struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorGetTemplate) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTemplateArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTemplateResult()
	if result.Success, result.Rae, err = p.handler.GetTemplate(args.Header, args.Filter); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTemplate: "+err.Error())
		oprot.WriteMessageBegin("getTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTemplate", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorDelTemplate struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorDelTemplate) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDelTemplateArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("delTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDelTemplateResult()
	if result.Success, result.Rae, err = p.handler.DelTemplate(args.Header, args.TemplateInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing delTemplate: "+err.Error())
		oprot.WriteMessageBegin("delTemplate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("delTemplate", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorAddVideoMaterial struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorAddVideoMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddVideoMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddVideoMaterialResult()
	if result.Success, result.Rae, err = p.handler.AddVideoMaterial(args.Header, args.AppInfo, args.Videos); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addVideoMaterial: "+err.Error())
		oprot.WriteMessageBegin("addVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addVideoMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorDelVideoMaterial struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorDelVideoMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDelVideoMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("delVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDelVideoMaterialResult()
	if result.Success, result.Rae, err = p.handler.DelVideoMaterial(args.Header, args.AppInfo, args.Video); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing delVideoMaterial: "+err.Error())
		oprot.WriteMessageBegin("delVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("delVideoMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorAddTextModel struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorAddTextModel) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddTextModelArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addTextModel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddTextModelResult()
	if result.Success, result.Rae, err = p.handler.AddTextModel(args.Header, args.TextModel); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addTextModel: "+err.Error())
		oprot.WriteMessageBegin("addTextModel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addTextModel", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorGetTextFingerprint struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorGetTextFingerprint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTextFingerprintArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTextFingerprint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTextFingerprintResult()
	if result.Success, result.Rae, err = p.handler.GetTextFingerprint(args.Header, args.Text); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTextFingerprint: "+err.Error())
		oprot.WriteMessageBegin("getTextFingerprint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTextFingerprint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type programmaticCreativeServerProcessorRefineAppName struct {
	handler ProgrammaticCreativeServer
}

func (p *programmaticCreativeServerProcessorRefineAppName) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRefineAppNameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("refineAppName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRefineAppNameResult()
	if result.Success, result.Rae, err = p.handler.RefineAppName(args.Header, args.AppName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing refineAppName: "+err.Error())
		oprot.WriteMessageBegin("refineAppName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("refineAppName", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetAppMaterialArgs struct {
	Header  *common.RequestHeader                           `thrift:"header,1" json:"header"`
	Request *programmatic_creative_types.AppMaterialRequest `thrift:"request,2" json:"request"`
	Filter  *programmatic_creative_types.MaterialFilter     `thrift:"filter,3" json:"filter"`
}

func NewGetAppMaterialArgs() *GetAppMaterialArgs {
	return &GetAppMaterialArgs{}
}

func (p *GetAppMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *GetAppMaterialArgs) readField3(iprot thrift.TProtocol) error {
	p.Filter = programmatic_creative_types.NewMaterialFilter()
	if err := p.Filter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Filter)
	}
	return nil
}

func (p *GetAppMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Filter != nil {
		if err := oprot.WriteFieldBegin("filter", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:filter: %s", p, err)
		}
		if err := p.Filter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Filter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:filter: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppMaterialArgs(%+v)", *p)
}

type GetAppMaterialResult struct {
	Success *programmatic_creative_types.MaterialsInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException  `thrift:"rae,1" json:"rae"`
}

func NewGetAppMaterialResult() *GetAppMaterialResult {
	return &GetAppMaterialResult{}
}

func (p *GetAppMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppMaterialResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewMaterialsInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetAppMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppMaterialResult(%+v)", *p)
}

type AddImageMaterialArgs struct {
	Header  *common.RequestHeader                  `thrift:"header,1" json:"header"`
	AppInfo *programmatic_creative_types.AppIdInfo `thrift:"app_info,2" json:"app_info"`
	Images  []*programmatic_creative_types.PCImage `thrift:"images,3" json:"images"`
}

func NewAddImageMaterialArgs() *AddImageMaterialArgs {
	return &AddImageMaterialArgs{}
}

func (p *AddImageMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddImageMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddImageMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.AppInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.AppInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppInfo)
	}
	return nil
}

func (p *AddImageMaterialArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Images = make([]*programmatic_creative_types.PCImage, 0, size)
	for i := 0; i < size; i++ {
		_elem53 := programmatic_creative_types.NewPCImage()
		if err := _elem53.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem53)
		}
		p.Images = append(p.Images, _elem53)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddImageMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addImageMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddImageMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddImageMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppInfo != nil {
		if err := oprot.WriteFieldBegin("app_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_info: %s", p, err)
		}
		if err := p.AppInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_info: %s", p, err)
		}
	}
	return err
}

func (p *AddImageMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Images != nil {
		if err := oprot.WriteFieldBegin("images", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:images: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Images)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Images {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:images: %s", p, err)
		}
	}
	return err
}

func (p *AddImageMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddImageMaterialArgs(%+v)", *p)
}

type AddImageMaterialResult struct {
	Success *programmatic_creative_types.MaterialsInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException  `thrift:"rae,1" json:"rae"`
}

func NewAddImageMaterialResult() *AddImageMaterialResult {
	return &AddImageMaterialResult{}
}

func (p *AddImageMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddImageMaterialResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewMaterialsInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddImageMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddImageMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addImageMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddImageMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddImageMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddImageMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddImageMaterialResult(%+v)", *p)
}

type DelImageMaterialArgs struct {
	Header  *common.RequestHeader                  `thrift:"header,1" json:"header"`
	AppInfo *programmatic_creative_types.AppIdInfo `thrift:"app_info,2" json:"app_info"`
	Image   *programmatic_creative_types.PCImage   `thrift:"image,3" json:"image"`
}

func NewDelImageMaterialArgs() *DelImageMaterialArgs {
	return &DelImageMaterialArgs{}
}

func (p *DelImageMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelImageMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DelImageMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.AppInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.AppInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppInfo)
	}
	return nil
}

func (p *DelImageMaterialArgs) readField3(iprot thrift.TProtocol) error {
	p.Image = programmatic_creative_types.NewPCImage()
	if err := p.Image.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Image)
	}
	return nil
}

func (p *DelImageMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("delImageMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelImageMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DelImageMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppInfo != nil {
		if err := oprot.WriteFieldBegin("app_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_info: %s", p, err)
		}
		if err := p.AppInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_info: %s", p, err)
		}
	}
	return err
}

func (p *DelImageMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Image != nil {
		if err := oprot.WriteFieldBegin("image", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:image: %s", p, err)
		}
		if err := p.Image.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Image)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:image: %s", p, err)
		}
	}
	return err
}

func (p *DelImageMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelImageMaterialArgs(%+v)", *p)
}

type DelImageMaterialResult struct {
	Success *programmatic_creative_types.MaterialsInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException  `thrift:"rae,1" json:"rae"`
}

func NewDelImageMaterialResult() *DelImageMaterialResult {
	return &DelImageMaterialResult{}
}

func (p *DelImageMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelImageMaterialResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewMaterialsInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DelImageMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *DelImageMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("delImageMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelImageMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DelImageMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *DelImageMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelImageMaterialResult(%+v)", *p)
}

type AddTextMaterialArgs struct {
	Header  *common.RequestHeader                  `thrift:"header,1" json:"header"`
	AppInfo *programmatic_creative_types.AppIdInfo `thrift:"app_info,2" json:"app_info"`
	Texts   []*programmatic_creative_types.PCText  `thrift:"texts,3" json:"texts"`
}

func NewAddTextMaterialArgs() *AddTextMaterialArgs {
	return &AddTextMaterialArgs{}
}

func (p *AddTextMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTextMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTextMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.AppInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.AppInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppInfo)
	}
	return nil
}

func (p *AddTextMaterialArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Texts = make([]*programmatic_creative_types.PCText, 0, size)
	for i := 0; i < size; i++ {
		_elem54 := programmatic_creative_types.NewPCText()
		if err := _elem54.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem54)
		}
		p.Texts = append(p.Texts, _elem54)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddTextMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTextMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTextMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTextMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppInfo != nil {
		if err := oprot.WriteFieldBegin("app_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_info: %s", p, err)
		}
		if err := p.AppInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_info: %s", p, err)
		}
	}
	return err
}

func (p *AddTextMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Texts != nil {
		if err := oprot.WriteFieldBegin("texts", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:texts: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Texts)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Texts {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:texts: %s", p, err)
		}
	}
	return err
}

func (p *AddTextMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTextMaterialArgs(%+v)", *p)
}

type AddTextMaterialResult struct {
	Success *programmatic_creative_types.MaterialsInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException  `thrift:"rae,1" json:"rae"`
}

func NewAddTextMaterialResult() *AddTextMaterialResult {
	return &AddTextMaterialResult{}
}

func (p *AddTextMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTextMaterialResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewMaterialsInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddTextMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddTextMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTextMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTextMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddTextMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddTextMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTextMaterialResult(%+v)", *p)
}

type AddCreativeInfoArgs struct {
	AppInfo      *programmatic_creative_types.AppIdInfo    `thrift:"app_info,1" json:"app_info"`
	CreativeInfo *programmatic_creative_types.CreativeInfo `thrift:"creative_info,2" json:"creative_info"`
}

func NewAddCreativeInfoArgs() *AddCreativeInfoArgs {
	return &AddCreativeInfoArgs{}
}

func (p *AddCreativeInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.AppInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.AppInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppInfo)
	}
	return nil
}

func (p *AddCreativeInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.CreativeInfo = programmatic_creative_types.NewCreativeInfo()
	if err := p.CreativeInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CreativeInfo)
	}
	return nil
}

func (p *AddCreativeInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCreativeInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.AppInfo != nil {
		if err := oprot.WriteFieldBegin("app_info", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:app_info: %s", p, err)
		}
		if err := p.AppInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:app_info: %s", p, err)
		}
	}
	return err
}

func (p *AddCreativeInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.CreativeInfo != nil {
		if err := oprot.WriteFieldBegin("creative_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:creative_info: %s", p, err)
		}
		if err := p.CreativeInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CreativeInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:creative_info: %s", p, err)
		}
	}
	return err
}

func (p *AddCreativeInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCreativeInfoArgs(%+v)", *p)
}

type AddCreativeInfoResult struct {
	Success *programmatic_creative_types.CreativeInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddCreativeInfoResult() *AddCreativeInfoResult {
	return &AddCreativeInfoResult{}
}

func (p *AddCreativeInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewCreativeInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddCreativeInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddCreativeInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCreativeInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddCreativeInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddCreativeInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCreativeInfoResult(%+v)", *p)
}

type AddTemplateArgs struct {
	Header       *common.RequestHeader                     `thrift:"header,1" json:"header"`
	TemplateInfo *programmatic_creative_types.TemplateInfo `thrift:"template_info,2" json:"template_info"`
}

func NewAddTemplateArgs() *AddTemplateArgs {
	return &AddTemplateArgs{}
}

func (p *AddTemplateArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTemplateArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTemplateArgs) readField2(iprot thrift.TProtocol) error {
	p.TemplateInfo = programmatic_creative_types.NewTemplateInfo()
	if err := p.TemplateInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TemplateInfo)
	}
	return nil
}

func (p *AddTemplateArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTemplate_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTemplateArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplateArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TemplateInfo != nil {
		if err := oprot.WriteFieldBegin("template_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:template_info: %s", p, err)
		}
		if err := p.TemplateInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TemplateInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:template_info: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplateArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTemplateArgs(%+v)", *p)
}

type AddTemplateResult struct {
	Success *programmatic_creative_types.TemplateInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddTemplateResult() *AddTemplateResult {
	return &AddTemplateResult{}
}

func (p *AddTemplateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTemplateResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewTemplateInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddTemplateResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddTemplateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTemplate_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTemplateResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplateResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTemplateResult(%+v)", *p)
}

type GetTemplateArgs struct {
	Header *common.RequestHeader                       `thrift:"header,1" json:"header"`
	Filter *programmatic_creative_types.TemplateFilter `thrift:"filter,2" json:"filter"`
}

func NewGetTemplateArgs() *GetTemplateArgs {
	return &GetTemplateArgs{}
}

func (p *GetTemplateArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTemplateArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTemplateArgs) readField2(iprot thrift.TProtocol) error {
	p.Filter = programmatic_creative_types.NewTemplateFilter()
	if err := p.Filter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Filter)
	}
	return nil
}

func (p *GetTemplateArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTemplate_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTemplateArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTemplateArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Filter != nil {
		if err := oprot.WriteFieldBegin("filter", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:filter: %s", p, err)
		}
		if err := p.Filter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Filter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:filter: %s", p, err)
		}
	}
	return err
}

func (p *GetTemplateArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateArgs(%+v)", *p)
}

type GetTemplateResult struct {
	Success *programmatic_creative_types.TemplateResultList `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException       `thrift:"rae,1" json:"rae"`
}

func NewGetTemplateResult() *GetTemplateResult {
	return &GetTemplateResult{}
}

func (p *GetTemplateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTemplateResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewTemplateResultList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTemplateResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetTemplateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTemplate_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTemplateResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTemplateResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetTemplateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTemplateResult(%+v)", *p)
}

type DelTemplateArgs struct {
	Header       *common.RequestHeader                     `thrift:"header,1" json:"header"`
	TemplateInfo *programmatic_creative_types.TemplateInfo `thrift:"template_info,2" json:"template_info"`
}

func NewDelTemplateArgs() *DelTemplateArgs {
	return &DelTemplateArgs{}
}

func (p *DelTemplateArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelTemplateArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DelTemplateArgs) readField2(iprot thrift.TProtocol) error {
	p.TemplateInfo = programmatic_creative_types.NewTemplateInfo()
	if err := p.TemplateInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TemplateInfo)
	}
	return nil
}

func (p *DelTemplateArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("delTemplate_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelTemplateArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DelTemplateArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TemplateInfo != nil {
		if err := oprot.WriteFieldBegin("template_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:template_info: %s", p, err)
		}
		if err := p.TemplateInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TemplateInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:template_info: %s", p, err)
		}
	}
	return err
}

func (p *DelTemplateArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelTemplateArgs(%+v)", *p)
}

type DelTemplateResult struct {
	Success *programmatic_creative_types.TemplateInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewDelTemplateResult() *DelTemplateResult {
	return &DelTemplateResult{}
}

func (p *DelTemplateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelTemplateResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewTemplateInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DelTemplateResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *DelTemplateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("delTemplate_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelTemplateResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DelTemplateResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *DelTemplateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelTemplateResult(%+v)", *p)
}

type AddVideoMaterialArgs struct {
	Header  *common.RequestHeader                  `thrift:"header,1" json:"header"`
	AppInfo *programmatic_creative_types.AppIdInfo `thrift:"app_info,2" json:"app_info"`
	Videos  []*programmatic_creative_types.PCVideo `thrift:"videos,3" json:"videos"`
}

func NewAddVideoMaterialArgs() *AddVideoMaterialArgs {
	return &AddVideoMaterialArgs{}
}

func (p *AddVideoMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddVideoMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddVideoMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.AppInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.AppInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppInfo)
	}
	return nil
}

func (p *AddVideoMaterialArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Videos = make([]*programmatic_creative_types.PCVideo, 0, size)
	for i := 0; i < size; i++ {
		_elem55 := programmatic_creative_types.NewPCVideo()
		if err := _elem55.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem55)
		}
		p.Videos = append(p.Videos, _elem55)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddVideoMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addVideoMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddVideoMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppInfo != nil {
		if err := oprot.WriteFieldBegin("app_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_info: %s", p, err)
		}
		if err := p.AppInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_info: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Videos != nil {
		if err := oprot.WriteFieldBegin("videos", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:videos: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Videos)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Videos {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:videos: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddVideoMaterialArgs(%+v)", *p)
}

type AddVideoMaterialResult struct {
	Success *programmatic_creative_types.MaterialsInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException  `thrift:"rae,1" json:"rae"`
}

func NewAddVideoMaterialResult() *AddVideoMaterialResult {
	return &AddVideoMaterialResult{}
}

func (p *AddVideoMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddVideoMaterialResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewMaterialsInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddVideoMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddVideoMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addVideoMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddVideoMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddVideoMaterialResult(%+v)", *p)
}

type DelVideoMaterialArgs struct {
	Header  *common.RequestHeader                  `thrift:"header,1" json:"header"`
	AppInfo *programmatic_creative_types.AppIdInfo `thrift:"app_info,2" json:"app_info"`
	Video   *programmatic_creative_types.PCVideo   `thrift:"video,3" json:"video"`
}

func NewDelVideoMaterialArgs() *DelVideoMaterialArgs {
	return &DelVideoMaterialArgs{}
}

func (p *DelVideoMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelVideoMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DelVideoMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.AppInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.AppInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppInfo)
	}
	return nil
}

func (p *DelVideoMaterialArgs) readField3(iprot thrift.TProtocol) error {
	p.Video = programmatic_creative_types.NewPCVideo()
	if err := p.Video.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Video)
	}
	return nil
}

func (p *DelVideoMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("delVideoMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelVideoMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DelVideoMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppInfo != nil {
		if err := oprot.WriteFieldBegin("app_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_info: %s", p, err)
		}
		if err := p.AppInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_info: %s", p, err)
		}
	}
	return err
}

func (p *DelVideoMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Video != nil {
		if err := oprot.WriteFieldBegin("video", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:video: %s", p, err)
		}
		if err := p.Video.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Video)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:video: %s", p, err)
		}
	}
	return err
}

func (p *DelVideoMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelVideoMaterialArgs(%+v)", *p)
}

type DelVideoMaterialResult struct {
	Success *programmatic_creative_types.MaterialsInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException  `thrift:"rae,1" json:"rae"`
}

func NewDelVideoMaterialResult() *DelVideoMaterialResult {
	return &DelVideoMaterialResult{}
}

func (p *DelVideoMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelVideoMaterialResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewMaterialsInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DelVideoMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *DelVideoMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("delVideoMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelVideoMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DelVideoMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *DelVideoMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelVideoMaterialResult(%+v)", *p)
}

type AddTextModelArgs struct {
	Header    *common.RequestHeader                    `thrift:"header,1" json:"header"`
	TextModel *programmatic_creative_types.PCTextModel `thrift:"text_model,2" json:"text_model"`
}

func NewAddTextModelArgs() *AddTextModelArgs {
	return &AddTextModelArgs{}
}

func (p *AddTextModelArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTextModelArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTextModelArgs) readField2(iprot thrift.TProtocol) error {
	p.TextModel = programmatic_creative_types.NewPCTextModel()
	if err := p.TextModel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TextModel)
	}
	return nil
}

func (p *AddTextModelArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTextModel_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTextModelArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTextModelArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TextModel != nil {
		if err := oprot.WriteFieldBegin("text_model", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:text_model: %s", p, err)
		}
		if err := p.TextModel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TextModel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:text_model: %s", p, err)
		}
	}
	return err
}

func (p *AddTextModelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTextModelArgs(%+v)", *p)
}

type AddTextModelResult struct {
	Success programmatic_creative_types.PCSStatus     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddTextModelResult() *AddTextModelResult {
	return &AddTextModelResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddTextModelResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddTextModelResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTextModelResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = programmatic_creative_types.PCSStatus(v)
	}
	return nil
}

func (p *AddTextModelResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddTextModelResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTextModel_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTextModelResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddTextModelResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddTextModelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTextModelResult(%+v)", *p)
}

type GetTextFingerprintArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Text   string                `thrift:"text,2" json:"text"`
}

func NewGetTextFingerprintArgs() *GetTextFingerprintArgs {
	return &GetTextFingerprintArgs{}
}

func (p *GetTextFingerprintArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTextFingerprintArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTextFingerprintArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *GetTextFingerprintArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTextFingerprint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTextFingerprintArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTextFingerprintArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:text: %s", p, err)
	}
	return err
}

func (p *GetTextFingerprintArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTextFingerprintArgs(%+v)", *p)
}

type GetTextFingerprintResult struct {
	Success string                                    `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewGetTextFingerprintResult() *GetTextFingerprintResult {
	return &GetTextFingerprintResult{}
}

func (p *GetTextFingerprintResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTextFingerprintResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetTextFingerprintResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetTextFingerprintResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTextFingerprint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTextFingerprintResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetTextFingerprintResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetTextFingerprintResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTextFingerprintResult(%+v)", *p)
}

type RefineAppNameArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	AppName string                `thrift:"app_name,2" json:"app_name"`
}

func NewRefineAppNameArgs() *RefineAppNameArgs {
	return &RefineAppNameArgs{}
}

func (p *RefineAppNameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RefineAppNameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RefineAppNameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *RefineAppNameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("refineAppName_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RefineAppNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RefineAppNameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.app_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:app_name: %s", p, err)
	}
	return err
}

func (p *RefineAppNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefineAppNameArgs(%+v)", *p)
}

type RefineAppNameResult struct {
	Success string                                    `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewRefineAppNameResult() *RefineAppNameResult {
	return &RefineAppNameResult{}
}

func (p *RefineAppNameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RefineAppNameResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *RefineAppNameResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *RefineAppNameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("refineAppName_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RefineAppNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *RefineAppNameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *RefineAppNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefineAppNameResult(%+v)", *p)
}
