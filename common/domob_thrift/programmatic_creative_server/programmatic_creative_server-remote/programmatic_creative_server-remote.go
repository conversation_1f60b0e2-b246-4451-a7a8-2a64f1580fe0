// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"programmatic_creative_server"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  MaterialsInfo getAppMaterial(RequestHeader header, AppMaterialRequest request, MaterialFilter filter)")
	fmt.Fprintln(os.Stderr, "  MaterialsInfo addImageMaterial(RequestHeader header, AppIdInfo app_info,  images)")
	fmt.Fprintln(os.Stderr, "  MaterialsInfo delImageMaterial(RequestHeader header, AppIdInfo app_info, PCImage image)")
	fmt.Fprintln(os.Stderr, "  MaterialsInfo addTextMaterial(RequestHeader header, AppIdInfo app_info,  texts)")
	fmt.Fprintln(os.Stderr, "  CreativeInfo addCreativeInfo(AppIdInfo app_info, CreativeInfo creative_info)")
	fmt.Fprintln(os.Stderr, "  TemplateInfo addTemplate(RequestHeader header, TemplateInfo template_info)")
	fmt.Fprintln(os.Stderr, "  TemplateResultList getTemplate(RequestHeader header, TemplateFilter filter)")
	fmt.Fprintln(os.Stderr, "  TemplateInfo delTemplate(RequestHeader header, TemplateInfo template_info)")
	fmt.Fprintln(os.Stderr, "  MaterialsInfo addVideoMaterial(RequestHeader header, AppIdInfo app_info,  videos)")
	fmt.Fprintln(os.Stderr, "  MaterialsInfo delVideoMaterial(RequestHeader header, AppIdInfo app_info, PCVideo video)")
	fmt.Fprintln(os.Stderr, "  PCSStatus addTextModel(RequestHeader header, PCTextModel text_model)")
	fmt.Fprintln(os.Stderr, "  string getTextFingerprint(RequestHeader header, string text)")
	fmt.Fprintln(os.Stderr, "  string refineAppName(RequestHeader header, string app_name)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := programmatic_creative_server.NewProgrammaticCreativeServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getAppMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAppMaterial requires 3 args")
			flag.Usage()
		}
		arg56 := flag.Arg(1)
		mbTrans57 := thrift.NewTMemoryBufferLen(len(arg56))
		defer mbTrans57.Close()
		_, err58 := mbTrans57.WriteString(arg56)
		if err58 != nil {
			Usage()
			return
		}
		factory59 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt60 := factory59.GetProtocol(mbTrans57)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err61 := argvalue0.Read(jsProt60)
		if err61 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg62 := flag.Arg(2)
		mbTrans63 := thrift.NewTMemoryBufferLen(len(arg62))
		defer mbTrans63.Close()
		_, err64 := mbTrans63.WriteString(arg62)
		if err64 != nil {
			Usage()
			return
		}
		factory65 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt66 := factory65.GetProtocol(mbTrans63)
		argvalue1 := programmatic_creative_server.NewAppMaterialRequest()
		err67 := argvalue1.Read(jsProt66)
		if err67 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg68 := flag.Arg(3)
		mbTrans69 := thrift.NewTMemoryBufferLen(len(arg68))
		defer mbTrans69.Close()
		_, err70 := mbTrans69.WriteString(arg68)
		if err70 != nil {
			Usage()
			return
		}
		factory71 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt72 := factory71.GetProtocol(mbTrans69)
		argvalue2 := programmatic_creative_server.NewMaterialFilter()
		err73 := argvalue2.Read(jsProt72)
		if err73 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetAppMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addImageMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddImageMaterial requires 3 args")
			flag.Usage()
		}
		arg74 := flag.Arg(1)
		mbTrans75 := thrift.NewTMemoryBufferLen(len(arg74))
		defer mbTrans75.Close()
		_, err76 := mbTrans75.WriteString(arg74)
		if err76 != nil {
			Usage()
			return
		}
		factory77 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt78 := factory77.GetProtocol(mbTrans75)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err79 := argvalue0.Read(jsProt78)
		if err79 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg80 := flag.Arg(2)
		mbTrans81 := thrift.NewTMemoryBufferLen(len(arg80))
		defer mbTrans81.Close()
		_, err82 := mbTrans81.WriteString(arg80)
		if err82 != nil {
			Usage()
			return
		}
		factory83 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt84 := factory83.GetProtocol(mbTrans81)
		argvalue1 := programmatic_creative_server.NewAppIdInfo()
		err85 := argvalue1.Read(jsProt84)
		if err85 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg86 := flag.Arg(3)
		mbTrans87 := thrift.NewTMemoryBufferLen(len(arg86))
		defer mbTrans87.Close()
		_, err88 := mbTrans87.WriteString(arg86)
		if err88 != nil {
			Usage()
			return
		}
		factory89 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt90 := factory89.GetProtocol(mbTrans87)
		containerStruct2 := programmatic_creative_server.NewAddImageMaterialArgs()
		err91 := containerStruct2.ReadField3(jsProt90)
		if err91 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Images
		value2 := argvalue2
		fmt.Print(client.AddImageMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "delImageMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DelImageMaterial requires 3 args")
			flag.Usage()
		}
		arg92 := flag.Arg(1)
		mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
		defer mbTrans93.Close()
		_, err94 := mbTrans93.WriteString(arg92)
		if err94 != nil {
			Usage()
			return
		}
		factory95 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt96 := factory95.GetProtocol(mbTrans93)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err97 := argvalue0.Read(jsProt96)
		if err97 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg98 := flag.Arg(2)
		mbTrans99 := thrift.NewTMemoryBufferLen(len(arg98))
		defer mbTrans99.Close()
		_, err100 := mbTrans99.WriteString(arg98)
		if err100 != nil {
			Usage()
			return
		}
		factory101 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt102 := factory101.GetProtocol(mbTrans99)
		argvalue1 := programmatic_creative_server.NewAppIdInfo()
		err103 := argvalue1.Read(jsProt102)
		if err103 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg104 := flag.Arg(3)
		mbTrans105 := thrift.NewTMemoryBufferLen(len(arg104))
		defer mbTrans105.Close()
		_, err106 := mbTrans105.WriteString(arg104)
		if err106 != nil {
			Usage()
			return
		}
		factory107 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt108 := factory107.GetProtocol(mbTrans105)
		argvalue2 := programmatic_creative_server.NewPCImage()
		err109 := argvalue2.Read(jsProt108)
		if err109 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.DelImageMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addTextMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddTextMaterial requires 3 args")
			flag.Usage()
		}
		arg110 := flag.Arg(1)
		mbTrans111 := thrift.NewTMemoryBufferLen(len(arg110))
		defer mbTrans111.Close()
		_, err112 := mbTrans111.WriteString(arg110)
		if err112 != nil {
			Usage()
			return
		}
		factory113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt114 := factory113.GetProtocol(mbTrans111)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err115 := argvalue0.Read(jsProt114)
		if err115 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg116 := flag.Arg(2)
		mbTrans117 := thrift.NewTMemoryBufferLen(len(arg116))
		defer mbTrans117.Close()
		_, err118 := mbTrans117.WriteString(arg116)
		if err118 != nil {
			Usage()
			return
		}
		factory119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt120 := factory119.GetProtocol(mbTrans117)
		argvalue1 := programmatic_creative_server.NewAppIdInfo()
		err121 := argvalue1.Read(jsProt120)
		if err121 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg122 := flag.Arg(3)
		mbTrans123 := thrift.NewTMemoryBufferLen(len(arg122))
		defer mbTrans123.Close()
		_, err124 := mbTrans123.WriteString(arg122)
		if err124 != nil {
			Usage()
			return
		}
		factory125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt126 := factory125.GetProtocol(mbTrans123)
		containerStruct2 := programmatic_creative_server.NewAddTextMaterialArgs()
		err127 := containerStruct2.ReadField3(jsProt126)
		if err127 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Texts
		value2 := argvalue2
		fmt.Print(client.AddTextMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addCreativeInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreativeInfo requires 2 args")
			flag.Usage()
		}
		arg128 := flag.Arg(1)
		mbTrans129 := thrift.NewTMemoryBufferLen(len(arg128))
		defer mbTrans129.Close()
		_, err130 := mbTrans129.WriteString(arg128)
		if err130 != nil {
			Usage()
			return
		}
		factory131 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt132 := factory131.GetProtocol(mbTrans129)
		argvalue0 := programmatic_creative_server.NewAppIdInfo()
		err133 := argvalue0.Read(jsProt132)
		if err133 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg134 := flag.Arg(2)
		mbTrans135 := thrift.NewTMemoryBufferLen(len(arg134))
		defer mbTrans135.Close()
		_, err136 := mbTrans135.WriteString(arg134)
		if err136 != nil {
			Usage()
			return
		}
		factory137 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt138 := factory137.GetProtocol(mbTrans135)
		argvalue1 := programmatic_creative_server.NewCreativeInfo()
		err139 := argvalue1.Read(jsProt138)
		if err139 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCreativeInfo(value0, value1))
		fmt.Print("\n")
		break
	case "addTemplate":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTemplate requires 2 args")
			flag.Usage()
		}
		arg140 := flag.Arg(1)
		mbTrans141 := thrift.NewTMemoryBufferLen(len(arg140))
		defer mbTrans141.Close()
		_, err142 := mbTrans141.WriteString(arg140)
		if err142 != nil {
			Usage()
			return
		}
		factory143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt144 := factory143.GetProtocol(mbTrans141)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err145 := argvalue0.Read(jsProt144)
		if err145 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg146 := flag.Arg(2)
		mbTrans147 := thrift.NewTMemoryBufferLen(len(arg146))
		defer mbTrans147.Close()
		_, err148 := mbTrans147.WriteString(arg146)
		if err148 != nil {
			Usage()
			return
		}
		factory149 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt150 := factory149.GetProtocol(mbTrans147)
		argvalue1 := programmatic_creative_server.NewTemplateInfo()
		err151 := argvalue1.Read(jsProt150)
		if err151 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTemplate(value0, value1))
		fmt.Print("\n")
		break
	case "getTemplate":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTemplate requires 2 args")
			flag.Usage()
		}
		arg152 := flag.Arg(1)
		mbTrans153 := thrift.NewTMemoryBufferLen(len(arg152))
		defer mbTrans153.Close()
		_, err154 := mbTrans153.WriteString(arg152)
		if err154 != nil {
			Usage()
			return
		}
		factory155 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt156 := factory155.GetProtocol(mbTrans153)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err157 := argvalue0.Read(jsProt156)
		if err157 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg158 := flag.Arg(2)
		mbTrans159 := thrift.NewTMemoryBufferLen(len(arg158))
		defer mbTrans159.Close()
		_, err160 := mbTrans159.WriteString(arg158)
		if err160 != nil {
			Usage()
			return
		}
		factory161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt162 := factory161.GetProtocol(mbTrans159)
		argvalue1 := programmatic_creative_server.NewTemplateFilter()
		err163 := argvalue1.Read(jsProt162)
		if err163 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetTemplate(value0, value1))
		fmt.Print("\n")
		break
	case "delTemplate":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DelTemplate requires 2 args")
			flag.Usage()
		}
		arg164 := flag.Arg(1)
		mbTrans165 := thrift.NewTMemoryBufferLen(len(arg164))
		defer mbTrans165.Close()
		_, err166 := mbTrans165.WriteString(arg164)
		if err166 != nil {
			Usage()
			return
		}
		factory167 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt168 := factory167.GetProtocol(mbTrans165)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err169 := argvalue0.Read(jsProt168)
		if err169 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg170 := flag.Arg(2)
		mbTrans171 := thrift.NewTMemoryBufferLen(len(arg170))
		defer mbTrans171.Close()
		_, err172 := mbTrans171.WriteString(arg170)
		if err172 != nil {
			Usage()
			return
		}
		factory173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt174 := factory173.GetProtocol(mbTrans171)
		argvalue1 := programmatic_creative_server.NewTemplateInfo()
		err175 := argvalue1.Read(jsProt174)
		if err175 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.DelTemplate(value0, value1))
		fmt.Print("\n")
		break
	case "addVideoMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddVideoMaterial requires 3 args")
			flag.Usage()
		}
		arg176 := flag.Arg(1)
		mbTrans177 := thrift.NewTMemoryBufferLen(len(arg176))
		defer mbTrans177.Close()
		_, err178 := mbTrans177.WriteString(arg176)
		if err178 != nil {
			Usage()
			return
		}
		factory179 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt180 := factory179.GetProtocol(mbTrans177)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err181 := argvalue0.Read(jsProt180)
		if err181 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg182 := flag.Arg(2)
		mbTrans183 := thrift.NewTMemoryBufferLen(len(arg182))
		defer mbTrans183.Close()
		_, err184 := mbTrans183.WriteString(arg182)
		if err184 != nil {
			Usage()
			return
		}
		factory185 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt186 := factory185.GetProtocol(mbTrans183)
		argvalue1 := programmatic_creative_server.NewAppIdInfo()
		err187 := argvalue1.Read(jsProt186)
		if err187 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg188 := flag.Arg(3)
		mbTrans189 := thrift.NewTMemoryBufferLen(len(arg188))
		defer mbTrans189.Close()
		_, err190 := mbTrans189.WriteString(arg188)
		if err190 != nil {
			Usage()
			return
		}
		factory191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt192 := factory191.GetProtocol(mbTrans189)
		containerStruct2 := programmatic_creative_server.NewAddVideoMaterialArgs()
		err193 := containerStruct2.ReadField3(jsProt192)
		if err193 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Videos
		value2 := argvalue2
		fmt.Print(client.AddVideoMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "delVideoMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DelVideoMaterial requires 3 args")
			flag.Usage()
		}
		arg194 := flag.Arg(1)
		mbTrans195 := thrift.NewTMemoryBufferLen(len(arg194))
		defer mbTrans195.Close()
		_, err196 := mbTrans195.WriteString(arg194)
		if err196 != nil {
			Usage()
			return
		}
		factory197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt198 := factory197.GetProtocol(mbTrans195)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err199 := argvalue0.Read(jsProt198)
		if err199 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg200 := flag.Arg(2)
		mbTrans201 := thrift.NewTMemoryBufferLen(len(arg200))
		defer mbTrans201.Close()
		_, err202 := mbTrans201.WriteString(arg200)
		if err202 != nil {
			Usage()
			return
		}
		factory203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt204 := factory203.GetProtocol(mbTrans201)
		argvalue1 := programmatic_creative_server.NewAppIdInfo()
		err205 := argvalue1.Read(jsProt204)
		if err205 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg206 := flag.Arg(3)
		mbTrans207 := thrift.NewTMemoryBufferLen(len(arg206))
		defer mbTrans207.Close()
		_, err208 := mbTrans207.WriteString(arg206)
		if err208 != nil {
			Usage()
			return
		}
		factory209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt210 := factory209.GetProtocol(mbTrans207)
		argvalue2 := programmatic_creative_server.NewPCVideo()
		err211 := argvalue2.Read(jsProt210)
		if err211 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.DelVideoMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addTextModel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTextModel requires 2 args")
			flag.Usage()
		}
		arg212 := flag.Arg(1)
		mbTrans213 := thrift.NewTMemoryBufferLen(len(arg212))
		defer mbTrans213.Close()
		_, err214 := mbTrans213.WriteString(arg212)
		if err214 != nil {
			Usage()
			return
		}
		factory215 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt216 := factory215.GetProtocol(mbTrans213)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err217 := argvalue0.Read(jsProt216)
		if err217 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg218 := flag.Arg(2)
		mbTrans219 := thrift.NewTMemoryBufferLen(len(arg218))
		defer mbTrans219.Close()
		_, err220 := mbTrans219.WriteString(arg218)
		if err220 != nil {
			Usage()
			return
		}
		factory221 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt222 := factory221.GetProtocol(mbTrans219)
		argvalue1 := programmatic_creative_server.NewPCTextModel()
		err223 := argvalue1.Read(jsProt222)
		if err223 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTextModel(value0, value1))
		fmt.Print("\n")
		break
	case "getTextFingerprint":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTextFingerprint requires 2 args")
			flag.Usage()
		}
		arg224 := flag.Arg(1)
		mbTrans225 := thrift.NewTMemoryBufferLen(len(arg224))
		defer mbTrans225.Close()
		_, err226 := mbTrans225.WriteString(arg224)
		if err226 != nil {
			Usage()
			return
		}
		factory227 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt228 := factory227.GetProtocol(mbTrans225)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err229 := argvalue0.Read(jsProt228)
		if err229 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetTextFingerprint(value0, value1))
		fmt.Print("\n")
		break
	case "refineAppName":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RefineAppName requires 2 args")
			flag.Usage()
		}
		arg231 := flag.Arg(1)
		mbTrans232 := thrift.NewTMemoryBufferLen(len(arg231))
		defer mbTrans232.Close()
		_, err233 := mbTrans232.WriteString(arg231)
		if err233 != nil {
			Usage()
			return
		}
		factory234 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt235 := factory234.GetProtocol(mbTrans232)
		argvalue0 := programmatic_creative_server.NewRequestHeader()
		err236 := argvalue0.Read(jsProt235)
		if err236 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.RefineAppName(value0, value1))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err242 := (strconv.Atoi(flag.Arg(1)))
		if err242 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
