// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appinfo_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appinfo_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = appinfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

type IdInt appinfo_types.IdInt

type App *appinfo_types.App

type AppChannel *appinfo_types.AppChannel

type AppInfoUpdateEvent struct {
	AppId IdInt              `thrift:"appId,1" json:"appId"`
	App   *appinfo_types.App `thrift:"app,2" json:"app"`
}

func NewAppInfoUpdateEvent() *AppInfoUpdateEvent {
	return &AppInfoUpdateEvent{}
}

func (p *AppInfoUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppId = IdInt(v)
	}
	return nil
}

func (p *AppInfoUpdateEvent) readField2(iprot thrift.TProtocol) error {
	p.App = appinfo_types.NewApp()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *AppInfoUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appId: %s", p, err)
	}
	return err
}

func (p *AppInfoUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app: %s", p, err)
		}
	}
	return err
}

func (p *AppInfoUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoUpdateEvent(%+v)", *p)
}

type AppChannelUpdateEvent struct {
	AppChnId   IdInt                     `thrift:"appChnId,1" json:"appChnId"`
	AppId      IdInt                     `thrift:"appId,2" json:"appId"`
	AppChannel *appinfo_types.AppChannel `thrift:"appChannel,3" json:"appChannel"`
}

func NewAppChannelUpdateEvent() *AppChannelUpdateEvent {
	return &AppChannelUpdateEvent{}
}

func (p *AppChannelUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppChannelUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AppChnId = IdInt(v)
	}
	return nil
}

func (p *AppChannelUpdateEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = IdInt(v)
	}
	return nil
}

func (p *AppChannelUpdateEvent) readField3(iprot thrift.TProtocol) error {
	p.AppChannel = appinfo_types.NewAppChannel()
	if err := p.AppChannel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppChannel)
	}
	return nil
}

func (p *AppChannelUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppChannelUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppChannelUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appChnId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appChnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppChnId)); err != nil {
		return fmt.Errorf("%T.appChnId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appChnId: %s", p, err)
	}
	return err
}

func (p *AppChannelUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appId: %s", p, err)
	}
	return err
}

func (p *AppChannelUpdateEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AppChannel != nil {
		if err := oprot.WriteFieldBegin("appChannel", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appChannel: %s", p, err)
		}
		if err := p.AppChannel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppChannel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appChannel: %s", p, err)
		}
	}
	return err
}

func (p *AppChannelUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppChannelUpdateEvent(%+v)", *p)
}
