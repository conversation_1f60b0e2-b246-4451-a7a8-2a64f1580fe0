// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbox_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dbox_stats_types"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dbox_stats_types.GoUnusedProtection__

type DboxStatsQueryService interface { //统计数据查询接口

	// 查询广告媒体差乘报表统计数据
	// @param header requestHeader
	// @param aderGroupingParam 广告分组方式参数
	// @param deverGroupingParam 媒体分组方式参数
	// @param dateParam dateParam, unix time
	// @param groupByMediaType 是否按照媒体类型分组, bool
	// @param costType 广告消费类型
	// @param offset 偏移量
	// @param limit 返回数据的数量限制
	// @param ascending 按创建时间进行排序，升序或降序
	// @return 广告媒体差乘数据，倒序
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - AderGroupingParam: 广告分组方式参数，如果为Null，则获取所有广告的数据，如果idList为空，则不返回数据
	//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则不返回数据
	//  - DateParam: 日期参数，如果为Null，则认为是全部时间
	//  - GroupByMediaType: 是否按照媒体类型分组，默认false
	//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	QueryAderDeverStatsData(requestHeader *common.RequestHeader, aderGroupingParam *dbox_stats_types.AderGroupingParam, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, groupByMediaType bool, costType CostType, offset QueryInt, limit QueryInt, ascending bool) (r []*dbox_stats_types.AderDeverStatsData, dsqe *DboxStatsQueryException, err error)
	// 查询媒体统计数据
	// @param header requestHeader
	// @param deverGroupingParam 媒体分组方式参数
	// @param dateParam dateParam, unix time
	// @param costType 广告消费类型
	// @param offset 偏移量
	// @param limit 返回数据的数量限制
	// @param ascending 按创建时间进行排序，升序或降序
	// @return 媒体数据
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则认为没有要查询的媒体
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	QueryDeverStatsData(requestHeader *common.RequestHeader, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType, offset QueryInt, limit QueryInt, ascending bool) (r []*dbox_stats_types.DeverStatsData, dsqe *DboxStatsQueryException, err error)
	// 汇总媒体统计数据
	// @param header requestHeader
	// @param deverGroupingParam 媒体分组方式参数
	// @param dateParam dateParam, unix time
	// @param costType 广告消费类型
	// @return 汇总媒体数据
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则认为没有要查询的媒体
	//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
	//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
	SummaryDeverStatsData(requestHeader *common.RequestHeader, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType) (r *dbox_stats_types.DeverSummaryData, dsqe *DboxStatsQueryException, err error)
	// 汇总广告媒体差乘报表统计数据
	// @param header requestHeader
	// @param aderGroupingParam 广告分组方式参数
	// @param deverGroupingParam 媒体分组方式参数
	// @param dateParam dateParam, unix time
	// @param groupByMediaType 是否按照媒体类型分组, bool
	// @param costType 广告消费类型
	// @return 广告媒体差乘汇总数据
	//
	// Parameters:
	//  - RequestHeader: 请求头, 必填
	//  - AderGroupingParam: 广告分组方式参数，如果为Null，则获取所有广告的数据，如果idList为空，则不返回数据
	//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则不返回数据
	//  - DateParam: 日期参数，如果为Null，则认为是全部时间
	//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
	SummaryAderDeverStatsData(requestHeader *common.RequestHeader, aderGroupingParam *dbox_stats_types.AderGroupingParam, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType) (r *dbox_stats_types.AderDeverSummaryData, dsqe *DboxStatsQueryException, err error)
}

//统计数据查询接口
type DboxStatsQueryServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDboxStatsQueryServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DboxStatsQueryServiceClient {
	return &DboxStatsQueryServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDboxStatsQueryServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DboxStatsQueryServiceClient {
	return &DboxStatsQueryServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 查询广告媒体差乘报表统计数据
// @param header requestHeader
// @param aderGroupingParam 广告分组方式参数
// @param deverGroupingParam 媒体分组方式参数
// @param dateParam dateParam, unix time
// @param groupByMediaType 是否按照媒体类型分组, bool
// @param costType 广告消费类型
// @param offset 偏移量
// @param limit 返回数据的数量限制
// @param ascending 按创建时间进行排序，升序或降序
// @return 广告媒体差乘数据，倒序
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - AderGroupingParam: 广告分组方式参数，如果为Null，则获取所有广告的数据，如果idList为空，则不返回数据
//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则不返回数据
//  - DateParam: 日期参数，如果为Null，则认为是全部时间
//  - GroupByMediaType: 是否按照媒体类型分组，默认false
//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *DboxStatsQueryServiceClient) QueryAderDeverStatsData(requestHeader *common.RequestHeader, aderGroupingParam *dbox_stats_types.AderGroupingParam, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, groupByMediaType bool, costType CostType, offset QueryInt, limit QueryInt, ascending bool) (r []*dbox_stats_types.AderDeverStatsData, dsqe *DboxStatsQueryException, err error) {
	if err = p.sendQueryAderDeverStatsData(requestHeader, aderGroupingParam, deverGroupingParam, dateParam, groupByMediaType, costType, offset, limit, ascending); err != nil {
		return
	}
	return p.recvQueryAderDeverStatsData()
}

func (p *DboxStatsQueryServiceClient) sendQueryAderDeverStatsData(requestHeader *common.RequestHeader, aderGroupingParam *dbox_stats_types.AderGroupingParam, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, groupByMediaType bool, costType CostType, offset QueryInt, limit QueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryAderDeverStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewQueryAderDeverStatsDataArgs()
	args0.RequestHeader = requestHeader
	args0.AderGroupingParam = aderGroupingParam
	args0.DeverGroupingParam = deverGroupingParam
	args0.DateParam = dateParam
	args0.GroupByMediaType = groupByMediaType
	args0.CostType = costType
	args0.Offset = offset
	args0.Limit = limit
	args0.Ascending = ascending
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DboxStatsQueryServiceClient) recvQueryAderDeverStatsData() (value []*dbox_stats_types.AderDeverStatsData, dsqe *DboxStatsQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewQueryAderDeverStatsDataResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Dsqe != nil {
		dsqe = result1.Dsqe
	}
	return
}

// 查询媒体统计数据
// @param header requestHeader
// @param deverGroupingParam 媒体分组方式参数
// @param dateParam dateParam, unix time
// @param costType 广告消费类型
// @param offset 偏移量
// @param limit 返回数据的数量限制
// @param ascending 按创建时间进行排序，升序或降序
// @return 媒体数据
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则认为没有要查询的媒体
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *DboxStatsQueryServiceClient) QueryDeverStatsData(requestHeader *common.RequestHeader, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType, offset QueryInt, limit QueryInt, ascending bool) (r []*dbox_stats_types.DeverStatsData, dsqe *DboxStatsQueryException, err error) {
	if err = p.sendQueryDeverStatsData(requestHeader, deverGroupingParam, dateParam, costType, offset, limit, ascending); err != nil {
		return
	}
	return p.recvQueryDeverStatsData()
}

func (p *DboxStatsQueryServiceClient) sendQueryDeverStatsData(requestHeader *common.RequestHeader, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType, offset QueryInt, limit QueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryDeverStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewQueryDeverStatsDataArgs()
	args4.RequestHeader = requestHeader
	args4.DeverGroupingParam = deverGroupingParam
	args4.DateParam = dateParam
	args4.CostType = costType
	args4.Offset = offset
	args4.Limit = limit
	args4.Ascending = ascending
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DboxStatsQueryServiceClient) recvQueryDeverStatsData() (value []*dbox_stats_types.DeverStatsData, dsqe *DboxStatsQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewQueryDeverStatsDataResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Dsqe != nil {
		dsqe = result5.Dsqe
	}
	return
}

// 汇总媒体统计数据
// @param header requestHeader
// @param deverGroupingParam 媒体分组方式参数
// @param dateParam dateParam, unix time
// @param costType 广告消费类型
// @return 汇总媒体数据
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则认为没有要查询的媒体
//  - DateParam: 日期参数, 如果为Null, 则认为是全部时间
//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
func (p *DboxStatsQueryServiceClient) SummaryDeverStatsData(requestHeader *common.RequestHeader, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType) (r *dbox_stats_types.DeverSummaryData, dsqe *DboxStatsQueryException, err error) {
	if err = p.sendSummaryDeverStatsData(requestHeader, deverGroupingParam, dateParam, costType); err != nil {
		return
	}
	return p.recvSummaryDeverStatsData()
}

func (p *DboxStatsQueryServiceClient) sendSummaryDeverStatsData(requestHeader *common.RequestHeader, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("summaryDeverStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewSummaryDeverStatsDataArgs()
	args8.RequestHeader = requestHeader
	args8.DeverGroupingParam = deverGroupingParam
	args8.DateParam = dateParam
	args8.CostType = costType
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DboxStatsQueryServiceClient) recvSummaryDeverStatsData() (value *dbox_stats_types.DeverSummaryData, dsqe *DboxStatsQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewSummaryDeverStatsDataResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Dsqe != nil {
		dsqe = result9.Dsqe
	}
	return
}

// 汇总广告媒体差乘报表统计数据
// @param header requestHeader
// @param aderGroupingParam 广告分组方式参数
// @param deverGroupingParam 媒体分组方式参数
// @param dateParam dateParam, unix time
// @param groupByMediaType 是否按照媒体类型分组, bool
// @param costType 广告消费类型
// @return 广告媒体差乘汇总数据
//
// Parameters:
//  - RequestHeader: 请求头, 必填
//  - AderGroupingParam: 广告分组方式参数，如果为Null，则获取所有广告的数据，如果idList为空，则不返回数据
//  - DeverGroupingParam: 媒体分组方式参数，如果为Null，则获取所有媒体的数据，如果idList为空，则不返回数据
//  - DateParam: 日期参数，如果为Null，则认为是全部时间
//  - CostType: 广告消费类型，如果为Null，则认为是按照全部广告消费类型进行累计, 效果与CT_ALL相同；如果为CT_UNKNOWN，则服务端不予处理，抛出异常
func (p *DboxStatsQueryServiceClient) SummaryAderDeverStatsData(requestHeader *common.RequestHeader, aderGroupingParam *dbox_stats_types.AderGroupingParam, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType) (r *dbox_stats_types.AderDeverSummaryData, dsqe *DboxStatsQueryException, err error) {
	if err = p.sendSummaryAderDeverStatsData(requestHeader, aderGroupingParam, deverGroupingParam, dateParam, costType); err != nil {
		return
	}
	return p.recvSummaryAderDeverStatsData()
}

func (p *DboxStatsQueryServiceClient) sendSummaryAderDeverStatsData(requestHeader *common.RequestHeader, aderGroupingParam *dbox_stats_types.AderGroupingParam, deverGroupingParam *dbox_stats_types.DeverGroupingParam, dateParam *dbox_stats_types.DateParam, costType CostType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("summaryAderDeverStatsData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewSummaryAderDeverStatsDataArgs()
	args12.RequestHeader = requestHeader
	args12.AderGroupingParam = aderGroupingParam
	args12.DeverGroupingParam = deverGroupingParam
	args12.DateParam = dateParam
	args12.CostType = costType
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DboxStatsQueryServiceClient) recvSummaryAderDeverStatsData() (value *dbox_stats_types.AderDeverSummaryData, dsqe *DboxStatsQueryException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewSummaryAderDeverStatsDataResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Dsqe != nil {
		dsqe = result13.Dsqe
	}
	return
}

type DboxStatsQueryServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DboxStatsQueryService
}

func (p *DboxStatsQueryServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DboxStatsQueryServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DboxStatsQueryServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDboxStatsQueryServiceProcessor(handler DboxStatsQueryService) *DboxStatsQueryServiceProcessor {

	self16 := &DboxStatsQueryServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self16.processorMap["queryAderDeverStatsData"] = &dboxStatsQueryServiceProcessorQueryAderDeverStatsData{handler: handler}
	self16.processorMap["queryDeverStatsData"] = &dboxStatsQueryServiceProcessorQueryDeverStatsData{handler: handler}
	self16.processorMap["summaryDeverStatsData"] = &dboxStatsQueryServiceProcessorSummaryDeverStatsData{handler: handler}
	self16.processorMap["summaryAderDeverStatsData"] = &dboxStatsQueryServiceProcessorSummaryAderDeverStatsData{handler: handler}
	return self16
}

func (p *DboxStatsQueryServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x17 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x17.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x17

}

type dboxStatsQueryServiceProcessorQueryAderDeverStatsData struct {
	handler DboxStatsQueryService
}

func (p *dboxStatsQueryServiceProcessorQueryAderDeverStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryAderDeverStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryAderDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryAderDeverStatsDataResult()
	if result.Success, result.Dsqe, err = p.handler.QueryAderDeverStatsData(args.RequestHeader, args.AderGroupingParam, args.DeverGroupingParam, args.DateParam, args.GroupByMediaType, args.CostType, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryAderDeverStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryAderDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryAderDeverStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dboxStatsQueryServiceProcessorQueryDeverStatsData struct {
	handler DboxStatsQueryService
}

func (p *dboxStatsQueryServiceProcessorQueryDeverStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryDeverStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryDeverStatsDataResult()
	if result.Success, result.Dsqe, err = p.handler.QueryDeverStatsData(args.RequestHeader, args.DeverGroupingParam, args.DateParam, args.CostType, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryDeverStatsData: "+err.Error())
		oprot.WriteMessageBegin("queryDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryDeverStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dboxStatsQueryServiceProcessorSummaryDeverStatsData struct {
	handler DboxStatsQueryService
}

func (p *dboxStatsQueryServiceProcessorSummaryDeverStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSummaryDeverStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("summaryDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSummaryDeverStatsDataResult()
	if result.Success, result.Dsqe, err = p.handler.SummaryDeverStatsData(args.RequestHeader, args.DeverGroupingParam, args.DateParam, args.CostType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing summaryDeverStatsData: "+err.Error())
		oprot.WriteMessageBegin("summaryDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("summaryDeverStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dboxStatsQueryServiceProcessorSummaryAderDeverStatsData struct {
	handler DboxStatsQueryService
}

func (p *dboxStatsQueryServiceProcessorSummaryAderDeverStatsData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSummaryAderDeverStatsDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("summaryAderDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSummaryAderDeverStatsDataResult()
	if result.Success, result.Dsqe, err = p.handler.SummaryAderDeverStatsData(args.RequestHeader, args.AderGroupingParam, args.DeverGroupingParam, args.DateParam, args.CostType); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing summaryAderDeverStatsData: "+err.Error())
		oprot.WriteMessageBegin("summaryAderDeverStatsData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("summaryAderDeverStatsData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type QueryAderDeverStatsDataArgs struct {
	RequestHeader      *common.RequestHeader                `thrift:"requestHeader,1" json:"requestHeader"`
	AderGroupingParam  *dbox_stats_types.AderGroupingParam  `thrift:"aderGroupingParam,2" json:"aderGroupingParam"`
	DeverGroupingParam *dbox_stats_types.DeverGroupingParam `thrift:"deverGroupingParam,3" json:"deverGroupingParam"`
	DateParam          *dbox_stats_types.DateParam          `thrift:"dateParam,4" json:"dateParam"`
	GroupByMediaType   bool                                 `thrift:"groupByMediaType,5" json:"groupByMediaType"`
	CostType           CostType                             `thrift:"costType,6" json:"costType"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    QueryInt `thrift:"offset,10" json:"offset"`
	Limit     QueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool     `thrift:"ascending,12" json:"ascending"`
}

func NewQueryAderDeverStatsDataArgs() *QueryAderDeverStatsDataArgs {
	return &QueryAderDeverStatsDataArgs{
		CostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryAderDeverStatsDataArgs) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *QueryAderDeverStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.AderGroupingParam = dbox_stats_types.NewAderGroupingParam()
	if err := p.AderGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AderGroupingParam)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.DeverGroupingParam = dbox_stats_types.NewDeverGroupingParam()
	if err := p.DeverGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeverGroupingParam)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	p.DateParam = dbox_stats_types.NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.GroupByMediaType = v
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = QueryInt(v)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = QueryInt(v)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAderDeverStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAderDeverStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AderGroupingParam != nil {
		if err := oprot.WriteFieldBegin("aderGroupingParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:aderGroupingParam: %s", p, err)
		}
		if err := p.AderGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AderGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:aderGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeverGroupingParam != nil {
		if err := oprot.WriteFieldBegin("deverGroupingParam", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deverGroupingParam: %s", p, err)
		}
		if err := p.DeverGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeverGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deverGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("groupByMediaType", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:groupByMediaType: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.GroupByMediaType)); err != nil {
		return fmt.Errorf("%T.groupByMediaType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:groupByMediaType: %s", p, err)
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:costType: %s", p, err)
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *QueryAderDeverStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAderDeverStatsDataArgs(%+v)", *p)
}

type QueryAderDeverStatsDataResult struct {
	Success []*dbox_stats_types.AderDeverStatsData `thrift:"success,0" json:"success"`
	Dsqe    *DboxStatsQueryException               `thrift:"dsqe,1" json:"dsqe"`
}

func NewQueryAderDeverStatsDataResult() *QueryAderDeverStatsDataResult {
	return &QueryAderDeverStatsDataResult{}
}

func (p *QueryAderDeverStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAderDeverStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*dbox_stats_types.AderDeverStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem18 := dbox_stats_types.NewAderDeverStatsData()
		if err := _elem18.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem18)
		}
		p.Success = append(p.Success, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryAderDeverStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Dsqe = NewDboxStatsQueryException()
	if err := p.Dsqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Dsqe)
	}
	return nil
}

func (p *QueryAderDeverStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAderDeverStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Dsqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAderDeverStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderDeverStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Dsqe != nil {
		if err := oprot.WriteFieldBegin("dsqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dsqe: %s", p, err)
		}
		if err := p.Dsqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Dsqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dsqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryAderDeverStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAderDeverStatsDataResult(%+v)", *p)
}

type QueryDeverStatsDataArgs struct {
	RequestHeader      *common.RequestHeader                `thrift:"requestHeader,1" json:"requestHeader"`
	DeverGroupingParam *dbox_stats_types.DeverGroupingParam `thrift:"deverGroupingParam,2" json:"deverGroupingParam"`
	DateParam          *dbox_stats_types.DateParam          `thrift:"dateParam,3" json:"dateParam"`
	CostType           CostType                             `thrift:"costType,4" json:"costType"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    QueryInt `thrift:"offset,10" json:"offset"`
	Limit     QueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool     `thrift:"ascending,12" json:"ascending"`
}

func NewQueryDeverStatsDataArgs() *QueryDeverStatsDataArgs {
	return &QueryDeverStatsDataArgs{
		CostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryDeverStatsDataArgs) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *QueryDeverStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.DeverGroupingParam = dbox_stats_types.NewDeverGroupingParam()
	if err := p.DeverGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeverGroupingParam)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.DateParam = dbox_stats_types.NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = QueryInt(v)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = QueryInt(v)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DeverGroupingParam != nil {
		if err := oprot.WriteFieldBegin("deverGroupingParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:deverGroupingParam: %s", p, err)
		}
		if err := p.DeverGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeverGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:deverGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:costType: %s", p, err)
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *QueryDeverStatsDataArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *QueryDeverStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverStatsDataArgs(%+v)", *p)
}

type QueryDeverStatsDataResult struct {
	Success []*dbox_stats_types.DeverStatsData `thrift:"success,0" json:"success"`
	Dsqe    *DboxStatsQueryException           `thrift:"dsqe,1" json:"dsqe"`
}

func NewQueryDeverStatsDataResult() *QueryDeverStatsDataResult {
	return &QueryDeverStatsDataResult{}
}

func (p *QueryDeverStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*dbox_stats_types.DeverStatsData, 0, size)
	for i := 0; i < size; i++ {
		_elem19 := dbox_stats_types.NewDeverStatsData()
		if err := _elem19.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem19)
		}
		p.Success = append(p.Success, _elem19)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Dsqe = NewDboxStatsQueryException()
	if err := p.Dsqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Dsqe)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryDeverStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Dsqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryDeverStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Dsqe != nil {
		if err := oprot.WriteFieldBegin("dsqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dsqe: %s", p, err)
		}
		if err := p.Dsqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Dsqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dsqe: %s", p, err)
		}
	}
	return err
}

func (p *QueryDeverStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryDeverStatsDataResult(%+v)", *p)
}

type SummaryDeverStatsDataArgs struct {
	RequestHeader      *common.RequestHeader                `thrift:"requestHeader,1" json:"requestHeader"`
	DeverGroupingParam *dbox_stats_types.DeverGroupingParam `thrift:"deverGroupingParam,2" json:"deverGroupingParam"`
	DateParam          *dbox_stats_types.DateParam          `thrift:"dateParam,3" json:"dateParam"`
	CostType           CostType                             `thrift:"costType,4" json:"costType"`
}

func NewSummaryDeverStatsDataArgs() *SummaryDeverStatsDataArgs {
	return &SummaryDeverStatsDataArgs{
		CostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SummaryDeverStatsDataArgs) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *SummaryDeverStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SummaryDeverStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *SummaryDeverStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.DeverGroupingParam = dbox_stats_types.NewDeverGroupingParam()
	if err := p.DeverGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeverGroupingParam)
	}
	return nil
}

func (p *SummaryDeverStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.DateParam = dbox_stats_types.NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *SummaryDeverStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *SummaryDeverStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("summaryDeverStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SummaryDeverStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *SummaryDeverStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.DeverGroupingParam != nil {
		if err := oprot.WriteFieldBegin("deverGroupingParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:deverGroupingParam: %s", p, err)
		}
		if err := p.DeverGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeverGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:deverGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *SummaryDeverStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *SummaryDeverStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:costType: %s", p, err)
	}
	return err
}

func (p *SummaryDeverStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SummaryDeverStatsDataArgs(%+v)", *p)
}

type SummaryDeverStatsDataResult struct {
	Success *dbox_stats_types.DeverSummaryData `thrift:"success,0" json:"success"`
	Dsqe    *DboxStatsQueryException           `thrift:"dsqe,1" json:"dsqe"`
}

func NewSummaryDeverStatsDataResult() *SummaryDeverStatsDataResult {
	return &SummaryDeverStatsDataResult{}
}

func (p *SummaryDeverStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SummaryDeverStatsDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = dbox_stats_types.NewDeverSummaryData()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SummaryDeverStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Dsqe = NewDboxStatsQueryException()
	if err := p.Dsqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Dsqe)
	}
	return nil
}

func (p *SummaryDeverStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("summaryDeverStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Dsqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SummaryDeverStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SummaryDeverStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Dsqe != nil {
		if err := oprot.WriteFieldBegin("dsqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dsqe: %s", p, err)
		}
		if err := p.Dsqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Dsqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dsqe: %s", p, err)
		}
	}
	return err
}

func (p *SummaryDeverStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SummaryDeverStatsDataResult(%+v)", *p)
}

type SummaryAderDeverStatsDataArgs struct {
	RequestHeader      *common.RequestHeader                `thrift:"requestHeader,1" json:"requestHeader"`
	AderGroupingParam  *dbox_stats_types.AderGroupingParam  `thrift:"aderGroupingParam,2" json:"aderGroupingParam"`
	DeverGroupingParam *dbox_stats_types.DeverGroupingParam `thrift:"deverGroupingParam,3" json:"deverGroupingParam"`
	DateParam          *dbox_stats_types.DateParam          `thrift:"dateParam,4" json:"dateParam"`
	CostType           CostType                             `thrift:"costType,5" json:"costType"`
}

func NewSummaryAderDeverStatsDataArgs() *SummaryAderDeverStatsDataArgs {
	return &SummaryAderDeverStatsDataArgs{
		CostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SummaryAderDeverStatsDataArgs) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *SummaryAderDeverStatsDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataArgs) readField2(iprot thrift.TProtocol) error {
	p.AderGroupingParam = dbox_stats_types.NewAderGroupingParam()
	if err := p.AderGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AderGroupingParam)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataArgs) readField3(iprot thrift.TProtocol) error {
	p.DeverGroupingParam = dbox_stats_types.NewDeverGroupingParam()
	if err := p.DeverGroupingParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DeverGroupingParam)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataArgs) readField4(iprot thrift.TProtocol) error {
	p.DateParam = dbox_stats_types.NewDateParam()
	if err := p.DateParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DateParam)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("summaryAderDeverStatsData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *SummaryAderDeverStatsDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AderGroupingParam != nil {
		if err := oprot.WriteFieldBegin("aderGroupingParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:aderGroupingParam: %s", p, err)
		}
		if err := p.AderGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AderGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:aderGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *SummaryAderDeverStatsDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DeverGroupingParam != nil {
		if err := oprot.WriteFieldBegin("deverGroupingParam", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:deverGroupingParam: %s", p, err)
		}
		if err := p.DeverGroupingParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DeverGroupingParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:deverGroupingParam: %s", p, err)
		}
	}
	return err
}

func (p *SummaryAderDeverStatsDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.DateParam != nil {
		if err := oprot.WriteFieldBegin("dateParam", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:dateParam: %s", p, err)
		}
		if err := p.DateParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DateParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:dateParam: %s", p, err)
		}
	}
	return err
}

func (p *SummaryAderDeverStatsDataArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:costType: %s", p, err)
	}
	return err
}

func (p *SummaryAderDeverStatsDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SummaryAderDeverStatsDataArgs(%+v)", *p)
}

type SummaryAderDeverStatsDataResult struct {
	Success *dbox_stats_types.AderDeverSummaryData `thrift:"success,0" json:"success"`
	Dsqe    *DboxStatsQueryException               `thrift:"dsqe,1" json:"dsqe"`
}

func NewSummaryAderDeverStatsDataResult() *SummaryAderDeverStatsDataResult {
	return &SummaryAderDeverStatsDataResult{}
}

func (p *SummaryAderDeverStatsDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = dbox_stats_types.NewAderDeverSummaryData()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataResult) readField1(iprot thrift.TProtocol) error {
	p.Dsqe = NewDboxStatsQueryException()
	if err := p.Dsqe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Dsqe)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("summaryAderDeverStatsData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Dsqe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SummaryAderDeverStatsDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SummaryAderDeverStatsDataResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Dsqe != nil {
		if err := oprot.WriteFieldBegin("dsqe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:dsqe: %s", p, err)
		}
		if err := p.Dsqe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Dsqe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:dsqe: %s", p, err)
		}
	}
	return err
}

func (p *SummaryAderDeverStatsDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SummaryAderDeverStatsDataResult(%+v)", *p)
}
