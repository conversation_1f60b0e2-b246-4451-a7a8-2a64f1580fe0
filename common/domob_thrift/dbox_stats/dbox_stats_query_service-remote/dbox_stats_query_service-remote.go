// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dbox_stats"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "   queryAderDeverStatsData(RequestHeader requestHeader, AderGroupingParam aderGroupingParam, DeverGroupingParam deverGroupingParam, DateParam dateParam, bool groupByMediaType, CostType costType, QueryInt offset, QueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   queryDeverStatsData(RequestHeader requestHeader, DeverGroupingParam deverGroupingParam, DateParam dateParam, CostType costType, QueryInt offset, QueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  DeverSummaryData summaryDeverStatsData(RequestHeader requestHeader, DeverGroupingParam deverGroupingParam, DateParam dateParam, CostType costType)")
	fmt.Fprintln(os.Stderr, "  AderDeverSummaryData summaryAderDeverStatsData(RequestHeader requestHeader, AderGroupingParam aderGroupingParam, DeverGroupingParam deverGroupingParam, DateParam dateParam, CostType costType)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dbox_stats.NewDboxStatsQueryServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "queryAderDeverStatsData":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "QueryAderDeverStatsData requires 9 args")
			flag.Usage()
		}
		arg20 := flag.Arg(1)
		mbTrans21 := thrift.NewTMemoryBufferLen(len(arg20))
		defer mbTrans21.Close()
		_, err22 := mbTrans21.WriteString(arg20)
		if err22 != nil {
			Usage()
			return
		}
		factory23 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt24 := factory23.GetProtocol(mbTrans21)
		argvalue0 := dbox_stats.NewRequestHeader()
		err25 := argvalue0.Read(jsProt24)
		if err25 != nil {
			Usage()
			return
		}
		value0 := dbox_stats.RequestHeader(argvalue0)
		arg26 := flag.Arg(2)
		mbTrans27 := thrift.NewTMemoryBufferLen(len(arg26))
		defer mbTrans27.Close()
		_, err28 := mbTrans27.WriteString(arg26)
		if err28 != nil {
			Usage()
			return
		}
		factory29 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt30 := factory29.GetProtocol(mbTrans27)
		argvalue1 := dbox_stats.NewAderGroupingParam()
		err31 := argvalue1.Read(jsProt30)
		if err31 != nil {
			Usage()
			return
		}
		value1 := dbox_stats.AderGroupingParam(argvalue1)
		arg32 := flag.Arg(3)
		mbTrans33 := thrift.NewTMemoryBufferLen(len(arg32))
		defer mbTrans33.Close()
		_, err34 := mbTrans33.WriteString(arg32)
		if err34 != nil {
			Usage()
			return
		}
		factory35 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt36 := factory35.GetProtocol(mbTrans33)
		argvalue2 := dbox_stats.NewDeverGroupingParam()
		err37 := argvalue2.Read(jsProt36)
		if err37 != nil {
			Usage()
			return
		}
		value2 := dbox_stats.DeverGroupingParam(argvalue2)
		arg38 := flag.Arg(4)
		mbTrans39 := thrift.NewTMemoryBufferLen(len(arg38))
		defer mbTrans39.Close()
		_, err40 := mbTrans39.WriteString(arg38)
		if err40 != nil {
			Usage()
			return
		}
		factory41 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt42 := factory41.GetProtocol(mbTrans39)
		argvalue3 := dbox_stats.NewDateParam()
		err43 := argvalue3.Read(jsProt42)
		if err43 != nil {
			Usage()
			return
		}
		value3 := dbox_stats.DateParam(argvalue3)
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := dbox_stats.CostType(tmp5)
		value5 := dbox_stats.CostType(argvalue5)
		tmp6, err45 := (strconv.Atoi(flag.Arg(7)))
		if err45 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := dbox_stats.QueryInt(argvalue6)
		tmp7, err46 := (strconv.Atoi(flag.Arg(8)))
		if err46 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := dbox_stats.QueryInt(argvalue7)
		argvalue8 := flag.Arg(9) == "true"
		value8 := argvalue8
		fmt.Print(client.QueryAderDeverStatsData(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "queryDeverStatsData":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "QueryDeverStatsData requires 7 args")
			flag.Usage()
		}
		arg48 := flag.Arg(1)
		mbTrans49 := thrift.NewTMemoryBufferLen(len(arg48))
		defer mbTrans49.Close()
		_, err50 := mbTrans49.WriteString(arg48)
		if err50 != nil {
			Usage()
			return
		}
		factory51 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt52 := factory51.GetProtocol(mbTrans49)
		argvalue0 := dbox_stats.NewRequestHeader()
		err53 := argvalue0.Read(jsProt52)
		if err53 != nil {
			Usage()
			return
		}
		value0 := dbox_stats.RequestHeader(argvalue0)
		arg54 := flag.Arg(2)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue1 := dbox_stats.NewDeverGroupingParam()
		err59 := argvalue1.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value1 := dbox_stats.DeverGroupingParam(argvalue1)
		arg60 := flag.Arg(3)
		mbTrans61 := thrift.NewTMemoryBufferLen(len(arg60))
		defer mbTrans61.Close()
		_, err62 := mbTrans61.WriteString(arg60)
		if err62 != nil {
			Usage()
			return
		}
		factory63 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt64 := factory63.GetProtocol(mbTrans61)
		argvalue2 := dbox_stats.NewDateParam()
		err65 := argvalue2.Read(jsProt64)
		if err65 != nil {
			Usage()
			return
		}
		value2 := dbox_stats.DateParam(argvalue2)
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := dbox_stats.CostType(tmp3)
		value3 := dbox_stats.CostType(argvalue3)
		tmp4, err66 := (strconv.Atoi(flag.Arg(5)))
		if err66 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := dbox_stats.QueryInt(argvalue4)
		tmp5, err67 := (strconv.Atoi(flag.Arg(6)))
		if err67 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := dbox_stats.QueryInt(argvalue5)
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.QueryDeverStatsData(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "summaryDeverStatsData":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SummaryDeverStatsData requires 4 args")
			flag.Usage()
		}
		arg69 := flag.Arg(1)
		mbTrans70 := thrift.NewTMemoryBufferLen(len(arg69))
		defer mbTrans70.Close()
		_, err71 := mbTrans70.WriteString(arg69)
		if err71 != nil {
			Usage()
			return
		}
		factory72 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt73 := factory72.GetProtocol(mbTrans70)
		argvalue0 := dbox_stats.NewRequestHeader()
		err74 := argvalue0.Read(jsProt73)
		if err74 != nil {
			Usage()
			return
		}
		value0 := dbox_stats.RequestHeader(argvalue0)
		arg75 := flag.Arg(2)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue1 := dbox_stats.NewDeverGroupingParam()
		err80 := argvalue1.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value1 := dbox_stats.DeverGroupingParam(argvalue1)
		arg81 := flag.Arg(3)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue2 := dbox_stats.NewDateParam()
		err86 := argvalue2.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value2 := dbox_stats.DateParam(argvalue2)
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := dbox_stats.CostType(tmp3)
		value3 := dbox_stats.CostType(argvalue3)
		fmt.Print(client.SummaryDeverStatsData(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "summaryAderDeverStatsData":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "SummaryAderDeverStatsData requires 5 args")
			flag.Usage()
		}
		arg87 := flag.Arg(1)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue0 := dbox_stats.NewRequestHeader()
		err92 := argvalue0.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value0 := dbox_stats.RequestHeader(argvalue0)
		arg93 := flag.Arg(2)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		argvalue1 := dbox_stats.NewAderGroupingParam()
		err98 := argvalue1.Read(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		value1 := dbox_stats.AderGroupingParam(argvalue1)
		arg99 := flag.Arg(3)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		argvalue2 := dbox_stats.NewDeverGroupingParam()
		err104 := argvalue2.Read(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		value2 := dbox_stats.DeverGroupingParam(argvalue2)
		arg105 := flag.Arg(4)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		argvalue3 := dbox_stats.NewDateParam()
		err110 := argvalue3.Read(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		value3 := dbox_stats.DateParam(argvalue3)
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := dbox_stats.CostType(tmp4)
		value4 := dbox_stats.CostType(argvalue4)
		fmt.Print(client.SummaryAderDeverStatsData(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
