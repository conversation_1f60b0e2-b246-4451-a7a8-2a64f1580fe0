// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbox_stats

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dbox_stats_types"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dbox_stats_types.GoUnusedProtection__
var GoUnusedProtection__ int

type DboxStatsServiceCode int64

const (
	DboxStatsServiceCode_ERROR_INVALID_PARAM   DboxStatsServiceCode = 20001
	DboxStatsServiceCode_ERROR_SERVER_BUSY     DboxStatsServiceCode = 21002
	DboxStatsServiceCode_ERROR_SERVER_DB_ERROR DboxStatsServiceCode = 21003
	DboxStatsServiceCode_ERROR_SYSTEM_ERROR    DboxStatsServiceCode = 22001
)

func (p DboxStatsServiceCode) String() string {
	switch p {
	case DboxStatsServiceCode_ERROR_INVALID_PARAM:
		return "DboxStatsServiceCode_ERROR_INVALID_PARAM"
	case DboxStatsServiceCode_ERROR_SERVER_BUSY:
		return "DboxStatsServiceCode_ERROR_SERVER_BUSY"
	case DboxStatsServiceCode_ERROR_SERVER_DB_ERROR:
		return "DboxStatsServiceCode_ERROR_SERVER_DB_ERROR"
	case DboxStatsServiceCode_ERROR_SYSTEM_ERROR:
		return "DboxStatsServiceCode_ERROR_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func DboxStatsServiceCodeFromString(s string) (DboxStatsServiceCode, error) {
	switch s {
	case "DboxStatsServiceCode_ERROR_INVALID_PARAM":
		return DboxStatsServiceCode_ERROR_INVALID_PARAM, nil
	case "DboxStatsServiceCode_ERROR_SERVER_BUSY":
		return DboxStatsServiceCode_ERROR_SERVER_BUSY, nil
	case "DboxStatsServiceCode_ERROR_SERVER_DB_ERROR":
		return DboxStatsServiceCode_ERROR_SERVER_DB_ERROR, nil
	case "DboxStatsServiceCode_ERROR_SYSTEM_ERROR":
		return DboxStatsServiceCode_ERROR_SYSTEM_ERROR, nil
	}
	return DboxStatsServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid DboxStatsServiceCode string")
}

type NumberInt int64

type IdInt common.IdInt

type TimeInt common.TimeInt

type Cost common.Amount

type Revenue common.Amount

type CostType common.CostType

type RequestHeader *common.RequestHeader

type QueryInt common.QueryInt

type AderDeverStatsData *dbox_stats_types.AderDeverStatsData

type AderDeverSummaryData *dbox_stats_types.AderDeverSummaryData

type DeverStatsData *dbox_stats_types.DeverStatsData

type DeverSummaryData *dbox_stats_types.DeverSummaryData

type DateParam *dbox_stats_types.DateParam

type AderGroupingParam *dbox_stats_types.AderGroupingParam

type DeverGroupingParam *dbox_stats_types.DeverGroupingParam

type DboxStatsQueryException struct {
	Code    DboxStatsServiceCode `thrift:"code,1" json:"code"`
	Message string               `thrift:"message,2" json:"message"`
}

func NewDboxStatsQueryException() *DboxStatsQueryException {
	return &DboxStatsQueryException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DboxStatsQueryException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DboxStatsQueryException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DboxStatsQueryException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = DboxStatsServiceCode(v)
	}
	return nil
}

func (p *DboxStatsQueryException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DboxStatsQueryException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DboxStatsQueryException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DboxStatsQueryException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DboxStatsQueryException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DboxStatsQueryException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DboxStatsQueryException(%+v)", *p)
}
