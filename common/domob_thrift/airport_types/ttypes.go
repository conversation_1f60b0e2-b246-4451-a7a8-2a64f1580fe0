// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package airport_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type AirlinesStatus int64

const (
	AirlinesStatus_AIRLINES_STATUS_STOPPED AirlinesStatus = 0
	AirlinesStatus_AIRLINES_STATUS_RUNNING AirlinesStatus = 1
)

func (p AirlinesStatus) String() string {
	switch p {
	case AirlinesStatus_AIRLINES_STATUS_STOPPED:
		return "AirlinesStatus_AIRLINES_STATUS_STOPPED"
	case AirlinesStatus_AIRLINES_STATUS_RUNNING:
		return "AirlinesStatus_AIRLINES_STATUS_RUNNING"
	}
	return "<UNSET>"
}

func AirlinesStatusFromString(s string) (AirlinesStatus, error) {
	switch s {
	case "AirlinesStatus_AIRLINES_STATUS_STOPPED":
		return AirlinesStatus_AIRLINES_STATUS_STOPPED, nil
	case "AirlinesStatus_AIRLINES_STATUS_RUNNING":
		return AirlinesStatus_AIRLINES_STATUS_RUNNING, nil
	}
	return AirlinesStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AirlinesStatus string")
}

type RunningStatus int64

const (
	RunningStatus_RUNNING_STATUS_NORMAL  RunningStatus = 0
	RunningStatus_RUNNING_STATUS_TIMEOUT RunningStatus = 1
	RunningStatus_RUNNING_STATUS_ERROR   RunningStatus = 2
)

func (p RunningStatus) String() string {
	switch p {
	case RunningStatus_RUNNING_STATUS_NORMAL:
		return "RunningStatus_RUNNING_STATUS_NORMAL"
	case RunningStatus_RUNNING_STATUS_TIMEOUT:
		return "RunningStatus_RUNNING_STATUS_TIMEOUT"
	case RunningStatus_RUNNING_STATUS_ERROR:
		return "RunningStatus_RUNNING_STATUS_ERROR"
	}
	return "<UNSET>"
}

func RunningStatusFromString(s string) (RunningStatus, error) {
	switch s {
	case "RunningStatus_RUNNING_STATUS_NORMAL":
		return RunningStatus_RUNNING_STATUS_NORMAL, nil
	case "RunningStatus_RUNNING_STATUS_TIMEOUT":
		return RunningStatus_RUNNING_STATUS_TIMEOUT, nil
	case "RunningStatus_RUNNING_STATUS_ERROR":
		return RunningStatus_RUNNING_STATUS_ERROR, nil
	}
	return RunningStatus(math.MinInt32 - 1), fmt.Errorf("not a valid RunningStatus string")
}

type FlightFileStatus int64

const (
	FlightFileStatus_FLIGHT_FILE_STATUS_PENDING             FlightFileStatus = 0
	FlightFileStatus_FLIGHT_FILE_STATUS_NOTICED             FlightFileStatus = 1
	FlightFileStatus_FLIGHT_FILE_STATUS_NOTICE_FAILED       FlightFileStatus = 2
	FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_BEGIN  FlightFileStatus = 10
	FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_END    FlightFileStatus = 11
	FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_ERROR  FlightFileStatus = 12
	FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_BEGIN FlightFileStatus = 20
	FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_END   FlightFileStatus = 21
	FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_SUCCEED    FlightFileStatus = 22
	FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_ERROR      FlightFileStatus = 23
	FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_DISCARD    FlightFileStatus = 24
)

func (p FlightFileStatus) String() string {
	switch p {
	case FlightFileStatus_FLIGHT_FILE_STATUS_PENDING:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_PENDING"
	case FlightFileStatus_FLIGHT_FILE_STATUS_NOTICED:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_NOTICED"
	case FlightFileStatus_FLIGHT_FILE_STATUS_NOTICE_FAILED:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_NOTICE_FAILED"
	case FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_BEGIN:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_BEGIN"
	case FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_END:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_END"
	case FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_ERROR:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_ERROR"
	case FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_BEGIN:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_BEGIN"
	case FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_END:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_END"
	case FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_SUCCEED:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_SUCCEED"
	case FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_ERROR:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_ERROR"
	case FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_DISCARD:
		return "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_DISCARD"
	}
	return "<UNSET>"
}

func FlightFileStatusFromString(s string) (FlightFileStatus, error) {
	switch s {
	case "FlightFileStatus_FLIGHT_FILE_STATUS_PENDING":
		return FlightFileStatus_FLIGHT_FILE_STATUS_PENDING, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_NOTICED":
		return FlightFileStatus_FLIGHT_FILE_STATUS_NOTICED, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_NOTICE_FAILED":
		return FlightFileStatus_FLIGHT_FILE_STATUS_NOTICE_FAILED, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_BEGIN":
		return FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_BEGIN, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_END":
		return FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_END, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_ERROR":
		return FlightFileStatus_FLIGHT_FILE_STATUS_AIRPORT_PULL_ERROR, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_BEGIN":
		return FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_BEGIN, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_END":
		return FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_PULL_END, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_SUCCEED":
		return FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_SUCCEED, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_ERROR":
		return FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_ERROR, nil
	case "FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_DISCARD":
		return FlightFileStatus_FLIGHT_FILE_STATUS_TERMINAL_DISCARD, nil
	}
	return FlightFileStatus(math.MinInt32 - 1), fmt.Errorf("not a valid FlightFileStatus string")
}

type ProcessType int64

const (
	ProcessType_PROCESS_TYPE_ALL_ARRIVAL    ProcessType = 0
	ProcessType_PROCESS_TYPE_SORTED_ARRIVAL ProcessType = 1
	ProcessType_PROCESS_TYPE_LATEST_ARRIVAL ProcessType = 2
)

func (p ProcessType) String() string {
	switch p {
	case ProcessType_PROCESS_TYPE_ALL_ARRIVAL:
		return "ProcessType_PROCESS_TYPE_ALL_ARRIVAL"
	case ProcessType_PROCESS_TYPE_SORTED_ARRIVAL:
		return "ProcessType_PROCESS_TYPE_SORTED_ARRIVAL"
	case ProcessType_PROCESS_TYPE_LATEST_ARRIVAL:
		return "ProcessType_PROCESS_TYPE_LATEST_ARRIVAL"
	}
	return "<UNSET>"
}

func ProcessTypeFromString(s string) (ProcessType, error) {
	switch s {
	case "ProcessType_PROCESS_TYPE_ALL_ARRIVAL":
		return ProcessType_PROCESS_TYPE_ALL_ARRIVAL, nil
	case "ProcessType_PROCESS_TYPE_SORTED_ARRIVAL":
		return ProcessType_PROCESS_TYPE_SORTED_ARRIVAL, nil
	case "ProcessType_PROCESS_TYPE_LATEST_ARRIVAL":
		return ProcessType_PROCESS_TYPE_LATEST_ARRIVAL, nil
	}
	return ProcessType(math.MinInt32 - 1), fmt.Errorf("not a valid ProcessType string")
}

type SignalType int64

const (
	SignalType_SIGNAL_TYPE_ALIVE   SignalType = 0
	SignalType_SIGNAL_TYPE_RESTART SignalType = 1
	SignalType_SIGNAL_TYPE_STOP    SignalType = 2
)

func (p SignalType) String() string {
	switch p {
	case SignalType_SIGNAL_TYPE_ALIVE:
		return "SignalType_SIGNAL_TYPE_ALIVE"
	case SignalType_SIGNAL_TYPE_RESTART:
		return "SignalType_SIGNAL_TYPE_RESTART"
	case SignalType_SIGNAL_TYPE_STOP:
		return "SignalType_SIGNAL_TYPE_STOP"
	}
	return "<UNSET>"
}

func SignalTypeFromString(s string) (SignalType, error) {
	switch s {
	case "SignalType_SIGNAL_TYPE_ALIVE":
		return SignalType_SIGNAL_TYPE_ALIVE, nil
	case "SignalType_SIGNAL_TYPE_RESTART":
		return SignalType_SIGNAL_TYPE_RESTART, nil
	case "SignalType_SIGNAL_TYPE_STOP":
		return SignalType_SIGNAL_TYPE_STOP, nil
	}
	return SignalType(math.MinInt32 - 1), fmt.Errorf("not a valid SignalType string")
}

type AirportExceptionType int64

const (
	AirportExceptionType_AET_RATE_LIMITING AirportExceptionType = 1
)

func (p AirportExceptionType) String() string {
	switch p {
	case AirportExceptionType_AET_RATE_LIMITING:
		return "AirportExceptionType_AET_RATE_LIMITING"
	}
	return "<UNSET>"
}

func AirportExceptionTypeFromString(s string) (AirportExceptionType, error) {
	switch s {
	case "AirportExceptionType_AET_RATE_LIMITING":
		return AirportExceptionType_AET_RATE_LIMITING, nil
	}
	return AirportExceptionType(math.MinInt32 - 1), fmt.Errorf("not a valid AirportExceptionType string")
}

type AirportException struct {
	Code AirportExceptionType `thrift:"code,1" json:"code"`
}

func NewAirportException() *AirportException {
	return &AirportException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AirportException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *AirportException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AirportException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = AirportExceptionType(v)
	}
	return nil
}

func (p *AirportException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AirportException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AirportException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *AirportException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AirportException(%+v)", *p)
}

type TopicInfo struct {
	Topic       string      `thrift:"topic,1" json:"topic"`
	Timestamp   int32       `thrift:"timestamp,2" json:"timestamp"`
	Random      int32       `thrift:"random,3" json:"random"`
	ProcessType ProcessType `thrift:"processType,4" json:"processType"`
}

func NewTopicInfo() *TopicInfo {
	return &TopicInfo{
		ProcessType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TopicInfo) IsSetProcessType() bool {
	return int64(p.ProcessType) != math.MinInt32-1
}

func (p *TopicInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TopicInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Topic = v
	}
	return nil
}

func (p *TopicInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *TopicInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Random = v
	}
	return nil
}

func (p *TopicInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ProcessType = ProcessType(v)
	}
	return nil
}

func (p *TopicInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TopicInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TopicInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topic", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topic: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Topic)); err != nil {
		return fmt.Errorf("%T.topic (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topic: %s", p, err)
	}
	return err
}

func (p *TopicInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:timestamp: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:timestamp: %s", p, err)
	}
	return err
}

func (p *TopicInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("random", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:random: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Random)); err != nil {
		return fmt.Errorf("%T.random (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:random: %s", p, err)
	}
	return err
}

func (p *TopicInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetProcessType() {
		if err := oprot.WriteFieldBegin("processType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:processType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProcessType)); err != nil {
			return fmt.Errorf("%T.processType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:processType: %s", p, err)
		}
	}
	return err
}

func (p *TopicInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TopicInfo(%+v)", *p)
}

type FileTargetInfo struct {
	Host    string `thrift:"host,1" json:"host"`
	Account string `thrift:"account,2" json:"account"`
	Path    string `thrift:"path,3" json:"path"`
}

func NewFileTargetInfo() *FileTargetInfo {
	return &FileTargetInfo{}
}

func (p *FileTargetInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FileTargetInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *FileTargetInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *FileTargetInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Path = v
	}
	return nil
}

func (p *FileTargetInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FileTargetInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FileTargetInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:host: %s", p, err)
	}
	return err
}

func (p *FileTargetInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:account: %s", p, err)
	}
	return err
}

func (p *FileTargetInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("path", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Path)); err != nil {
		return fmt.Errorf("%T.path (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:path: %s", p, err)
	}
	return err
}

func (p *FileTargetInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileTargetInfo(%+v)", *p)
}

type FileInfo struct {
	TotalLength  int64             `thrift:"totalLength,1" json:"totalLength"`
	PerBlockSize int32             `thrift:"perBlockSize,2" json:"perBlockSize"`
	HashList     []string          `thrift:"hashList,3" json:"hashList"`
	TargetList   []*FileTargetInfo `thrift:"targetList,4" json:"targetList"`
	SrcHost      string            `thrift:"srcHost,5" json:"srcHost"`
}

func NewFileInfo() *FileInfo {
	return &FileInfo{}
}

func (p *FileInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FileInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalLength = v
	}
	return nil
}

func (p *FileInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PerBlockSize = v
	}
	return nil
}

func (p *FileInfo) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.HashList = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.HashList = append(p.HashList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *FileInfo) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TargetList = make([]*FileTargetInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewFileTargetInfo()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.TargetList = append(p.TargetList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *FileInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SrcHost = v
	}
	return nil
}

func (p *FileInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FileInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FileInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalLength", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalLength: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalLength)); err != nil {
		return fmt.Errorf("%T.totalLength (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalLength: %s", p, err)
	}
	return err
}

func (p *FileInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("perBlockSize", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:perBlockSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PerBlockSize)); err != nil {
		return fmt.Errorf("%T.perBlockSize (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:perBlockSize: %s", p, err)
	}
	return err
}

func (p *FileInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.HashList != nil {
		if err := oprot.WriteFieldBegin("hashList", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:hashList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.HashList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.HashList {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:hashList: %s", p, err)
		}
	}
	return err
}

func (p *FileInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.TargetList != nil {
		if err := oprot.WriteFieldBegin("targetList", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:targetList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TargetList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TargetList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:targetList: %s", p, err)
		}
	}
	return err
}

func (p *FileInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("srcHost", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:srcHost: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SrcHost)); err != nil {
		return fmt.Errorf("%T.srcHost (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:srcHost: %s", p, err)
	}
	return err
}

func (p *FileInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FileInfo(%+v)", *p)
}

type TopicFileInfo struct {
	TopicInfo *TopicInfo `thrift:"topicInfo,1" json:"topicInfo"`
	FileInfo  *FileInfo  `thrift:"fileInfo,2" json:"fileInfo"`
}

func NewTopicFileInfo() *TopicFileInfo {
	return &TopicFileInfo{}
}

func (p *TopicFileInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TopicFileInfo) readField1(iprot thrift.TProtocol) error {
	p.TopicInfo = NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *TopicFileInfo) readField2(iprot thrift.TProtocol) error {
	p.FileInfo = NewFileInfo()
	if err := p.FileInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.FileInfo)
	}
	return nil
}

func (p *TopicFileInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TopicFileInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TopicFileInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *TopicFileInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.FileInfo != nil {
		if err := oprot.WriteFieldBegin("fileInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:fileInfo: %s", p, err)
		}
		if err := p.FileInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.FileInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:fileInfo: %s", p, err)
		}
	}
	return err
}

func (p *TopicFileInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TopicFileInfo(%+v)", *p)
}

type DataInfo struct {
	Data       []byte `thrift:"data,1" json:"data"`
	BlockIndex int32  `thrift:"blockIndex,2" json:"blockIndex"`
}

func NewDataInfo() *DataInfo {
	return &DataInfo{}
}

func (p *DataInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DataInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Data = v
	}
	return nil
}

func (p *DataInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.BlockIndex = v
	}
	return nil
}

func (p *DataInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DataInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DataInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.STRING, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:data: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Data); err != nil {
			return fmt.Errorf("%T.data (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:data: %s", p, err)
		}
	}
	return err
}

func (p *DataInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("blockIndex", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:blockIndex: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BlockIndex)); err != nil {
		return fmt.Errorf("%T.blockIndex (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:blockIndex: %s", p, err)
	}
	return err
}

func (p *DataInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataInfo(%+v)", *p)
}

type StringList struct {
	StringList []string `thrift:"stringList,1" json:"stringList"`
}

func NewStringList() *StringList {
	return &StringList{}
}

func (p *StringList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StringList) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StringList = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.StringList = append(p.StringList, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StringList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StringList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StringList) writeField1(oprot thrift.TProtocol) (err error) {
	if p.StringList != nil {
		if err := oprot.WriteFieldBegin("stringList", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:stringList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.StringList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StringList {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:stringList: %s", p, err)
		}
	}
	return err
}

func (p *StringList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StringList(%+v)", *p)
}

type AirportInfo struct {
	Host string `thrift:"host,1" json:"host"`
	Port int32  `thrift:"port,2" json:"port"`
}

func NewAirportInfo() *AirportInfo {
	return &AirportInfo{}
}

func (p *AirportInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AirportInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *AirportInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Port = v
	}
	return nil
}

func (p *AirportInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AirportInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AirportInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:host: %s", p, err)
	}
	return err
}

func (p *AirportInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("port", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:port: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Port)); err != nil {
		return fmt.Errorf("%T.port (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:port: %s", p, err)
	}
	return err
}

func (p *AirportInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AirportInfo(%+v)", *p)
}

type FlightFile struct {
	TopicFileInfo *TopicFileInfo `thrift:"topicFileInfo,1" json:"topicFileInfo"`
	FlightId      int32          `thrift:"flightId,2" json:"flightId"`
	Account       string         `thrift:"account,3" json:"account"`
	Path          string         `thrift:"path,4" json:"path"`
}

func NewFlightFile() *FlightFile {
	return &FlightFile{}
}

func (p *FlightFile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FlightFile) readField1(iprot thrift.TProtocol) error {
	p.TopicFileInfo = NewTopicFileInfo()
	if err := p.TopicFileInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicFileInfo)
	}
	return nil
}

func (p *FlightFile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *FlightFile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *FlightFile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Path = v
	}
	return nil
}

func (p *FlightFile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FlightFile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FlightFile) writeField1(oprot thrift.TProtocol) (err error) {
	if p.TopicFileInfo != nil {
		if err := oprot.WriteFieldBegin("topicFileInfo", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:topicFileInfo: %s", p, err)
		}
		if err := p.TopicFileInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicFileInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:topicFileInfo: %s", p, err)
		}
	}
	return err
}

func (p *FlightFile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:flightId: %s", p, err)
	}
	return err
}

func (p *FlightFile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account: %s", p, err)
	}
	return err
}

func (p *FlightFile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("path", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Path)); err != nil {
		return fmt.Errorf("%T.path (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:path: %s", p, err)
	}
	return err
}

func (p *FlightFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FlightFile(%+v)", *p)
}

type AirportLocalFileInfo struct {
	SrcDir   string `thrift:"srcDir,1" json:"srcDir"`
	Filename string `thrift:"filename,2" json:"filename"`
	DestDir  string `thrift:"destDir,3" json:"destDir"`
}

func NewAirportLocalFileInfo() *AirportLocalFileInfo {
	return &AirportLocalFileInfo{}
}

func (p *AirportLocalFileInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AirportLocalFileInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SrcDir = v
	}
	return nil
}

func (p *AirportLocalFileInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Filename = v
	}
	return nil
}

func (p *AirportLocalFileInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DestDir = v
	}
	return nil
}

func (p *AirportLocalFileInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AirportLocalFileInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AirportLocalFileInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("srcDir", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:srcDir: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SrcDir)); err != nil {
		return fmt.Errorf("%T.srcDir (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:srcDir: %s", p, err)
	}
	return err
}

func (p *AirportLocalFileInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filename", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:filename: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Filename)); err != nil {
		return fmt.Errorf("%T.filename (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:filename: %s", p, err)
	}
	return err
}

func (p *AirportLocalFileInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("destDir", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:destDir: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DestDir)); err != nil {
		return fmt.Errorf("%T.destDir (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:destDir: %s", p, err)
	}
	return err
}

func (p *AirportLocalFileInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AirportLocalFileInfo(%+v)", *p)
}

type AirportFlightFile struct {
	LocalFileInfo *AirportLocalFileInfo `thrift:"localFileInfo,1" json:"localFileInfo"`
	TopicInfo     *TopicInfo            `thrift:"topicInfo,2" json:"topicInfo"`
	FlightId      int32                 `thrift:"flightId,3" json:"flightId"`
}

func NewAirportFlightFile() *AirportFlightFile {
	return &AirportFlightFile{}
}

func (p *AirportFlightFile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AirportFlightFile) readField1(iprot thrift.TProtocol) error {
	p.LocalFileInfo = NewAirportLocalFileInfo()
	if err := p.LocalFileInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.LocalFileInfo)
	}
	return nil
}

func (p *AirportFlightFile) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *AirportFlightFile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *AirportFlightFile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AirportFlightFile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AirportFlightFile) writeField1(oprot thrift.TProtocol) (err error) {
	if p.LocalFileInfo != nil {
		if err := oprot.WriteFieldBegin("localFileInfo", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:localFileInfo: %s", p, err)
		}
		if err := p.LocalFileInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.LocalFileInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:localFileInfo: %s", p, err)
		}
	}
	return err
}

func (p *AirportFlightFile) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *AirportFlightFile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:flightId: %s", p, err)
	}
	return err
}

func (p *AirportFlightFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AirportFlightFile(%+v)", *p)
}

type FlightInfo struct {
	FlightId  int32  `thrift:"flightId,1" json:"flightId"`
	Host      string `thrift:"host,2" json:"host"`
	Account   string `thrift:"account,3" json:"account"`
	Path      string `thrift:"path,4" json:"path"`
	IsDeleted bool   `thrift:"isDeleted,5" json:"isDeleted"`
	IsPaused  bool   `thrift:"isPaused,6" json:"isPaused"`
}

func NewFlightInfo() *FlightInfo {
	return &FlightInfo{}
}

func (p *FlightInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FlightInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *FlightInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *FlightInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *FlightInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Path = v
	}
	return nil
}

func (p *FlightInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IsDeleted = v
	}
	return nil
}

func (p *FlightInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IsPaused = v
	}
	return nil
}

func (p *FlightInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FlightInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FlightInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:flightId: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("path", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Path)); err != nil {
		return fmt.Errorf("%T.path (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:path: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isDeleted", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:isDeleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsDeleted)); err != nil {
		return fmt.Errorf("%T.isDeleted (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:isDeleted: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isPaused", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:isPaused: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsPaused)); err != nil {
		return fmt.Errorf("%T.isPaused (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:isPaused: %s", p, err)
	}
	return err
}

func (p *FlightInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FlightInfo(%+v)", *p)
}

type ResultBool struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        bool                  `thrift:"result,2" json:"result"`
}

func NewResultBool() *ResultBool {
	return &ResultBool{}
}

func (p *ResultBool) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultBool) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultBool) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Result = v
	}
	return nil
}

func (p *ResultBool) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultBool"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultBool) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultBool) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Result)); err != nil {
		return fmt.Errorf("%T.result (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:result: %s", p, err)
	}
	return err
}

func (p *ResultBool) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultBool(%+v)", *p)
}

type ResultData struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Data          []byte                `thrift:"data,2" json:"data"`
}

func NewResultData() *ResultData {
	return &ResultData{}
}

func (p *ResultData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultData) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Data = v
	}
	return nil
}

func (p *ResultData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultData) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultData) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.STRING, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:data: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Data); err != nil {
			return fmt.Errorf("%T.data (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:data: %s", p, err)
		}
	}
	return err
}

func (p *ResultData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultData(%+v)", *p)
}

type ResultString struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        string                `thrift:"result,2" json:"result"`
}

func NewResultString() *ResultString {
	return &ResultString{}
}

func (p *ResultString) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultString) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultString) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Result = v
	}
	return nil
}

func (p *ResultString) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultString"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultString) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultString) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Result)); err != nil {
		return fmt.Errorf("%T.result (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:result: %s", p, err)
	}
	return err
}

func (p *ResultString) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultString(%+v)", *p)
}

type ResultI32List struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        []int32               `thrift:"result,2" json:"result"`
}

func NewResultI32List() *ResultI32List {
	return &ResultI32List{}
}

func (p *ResultI32List) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultI32List) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultI32List) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.Result = append(p.Result, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResultI32List) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultI32List"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultI32List) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultI32List) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultI32List) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultI32List(%+v)", *p)
}

type ResultAirportInfoList struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        []*AirportInfo        `thrift:"result,2" json:"result"`
}

func NewResultAirportInfoList() *ResultAirportInfoList {
	return &ResultAirportInfoList{}
}

func (p *ResultAirportInfoList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultAirportInfoList) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultAirportInfoList) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*AirportInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem4 := NewAirportInfo()
		if err := _elem4.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem4)
		}
		p.Result = append(p.Result, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResultAirportInfoList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultAirportInfoList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultAirportInfoList) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultAirportInfoList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultAirportInfoList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultAirportInfoList(%+v)", *p)
}

type ResultTopicInfoList struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        []*TopicInfo          `thrift:"result,2" json:"result"`
}

func NewResultTopicInfoList() *ResultTopicInfoList {
	return &ResultTopicInfoList{}
}

func (p *ResultTopicInfoList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultTopicInfoList) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultTopicInfoList) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*TopicInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewTopicInfo()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Result = append(p.Result, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResultTopicInfoList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultTopicInfoList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultTopicInfoList) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultTopicInfoList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultTopicInfoList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultTopicInfoList(%+v)", *p)
}

type ResultTopicFileInfoList struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        []*TopicFileInfo      `thrift:"result,2" json:"result"`
	Success       bool                  `thrift:"success,3" json:"success"`
}

func NewResultTopicFileInfoList() *ResultTopicFileInfoList {
	return &ResultTopicFileInfoList{}
}

func (p *ResultTopicFileInfoList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultTopicFileInfoList) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultTopicFileInfoList) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*TopicFileInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem6 := NewTopicFileInfo()
		if err := _elem6.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem6)
		}
		p.Result = append(p.Result, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResultTopicFileInfoList) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ResultTopicFileInfoList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultTopicFileInfoList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultTopicFileInfoList) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultTopicFileInfoList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultTopicFileInfoList) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:success: %s", p, err)
	}
	return err
}

func (p *ResultTopicFileInfoList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultTopicFileInfoList(%+v)", *p)
}

type ResultSignal struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        SignalType            `thrift:"result,2" json:"result"`
}

func NewResultSignal() *ResultSignal {
	return &ResultSignal{
		Result: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ResultSignal) IsSetResult() bool {
	return int64(p.Result) != math.MinInt32-1
}

func (p *ResultSignal) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultSignal) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultSignal) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Result = SignalType(v)
	}
	return nil
}

func (p *ResultSignal) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultSignal"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultSignal) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultSignal) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetResult() {
		if err := oprot.WriteFieldBegin("result", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Result)); err != nil {
			return fmt.Errorf("%T.result (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultSignal) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultSignal(%+v)", *p)
}

type ResultFlightFileStatus struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        FlightFileStatus      `thrift:"result,2" json:"result"`
	Success       bool                  `thrift:"success,3" json:"success"`
}

func NewResultFlightFileStatus() *ResultFlightFileStatus {
	return &ResultFlightFileStatus{
		Result: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ResultFlightFileStatus) IsSetResult() bool {
	return int64(p.Result) != math.MinInt32-1
}

func (p *ResultFlightFileStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultFlightFileStatus) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultFlightFileStatus) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Result = FlightFileStatus(v)
	}
	return nil
}

func (p *ResultFlightFileStatus) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ResultFlightFileStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultFlightFileStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultFlightFileStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultFlightFileStatus) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetResult() {
		if err := oprot.WriteFieldBegin("result", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Result)); err != nil {
			return fmt.Errorf("%T.result (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultFlightFileStatus) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:success: %s", p, err)
	}
	return err
}

func (p *ResultFlightFileStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultFlightFileStatus(%+v)", *p)
}

type ResultAirportFlightFileList struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        []*AirportFlightFile  `thrift:"result,2" json:"result"`
}

func NewResultAirportFlightFileList() *ResultAirportFlightFileList {
	return &ResultAirportFlightFileList{}
}

func (p *ResultAirportFlightFileList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultAirportFlightFileList) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultAirportFlightFileList) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*AirportFlightFile, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := NewAirportFlightFile()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.Result = append(p.Result, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResultAirportFlightFileList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultAirportFlightFileList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultAirportFlightFileList) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultAirportFlightFileList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultAirportFlightFileList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultAirportFlightFileList(%+v)", *p)
}

type ResultFlightInfo struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        *FlightInfo           `thrift:"result,2" json:"result"`
	Success       bool                  `thrift:"success,3" json:"success"`
}

func NewResultFlightInfo() *ResultFlightInfo {
	return &ResultFlightInfo{}
}

func (p *ResultFlightInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultFlightInfo) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultFlightInfo) readField2(iprot thrift.TProtocol) error {
	p.Result = NewFlightInfo()
	if err := p.Result.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Result)
	}
	return nil
}

func (p *ResultFlightInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ResultFlightInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultFlightInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultFlightInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultFlightInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := p.Result.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Result)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultFlightInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:success: %s", p, err)
	}
	return err
}

func (p *ResultFlightInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultFlightInfo(%+v)", *p)
}

type ResultFlightFileList struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Result        []*FlightFile         `thrift:"result,2" json:"result"`
	Success       bool                  `thrift:"success,3" json:"success"`
}

func NewResultFlightFileList() *ResultFlightFileList {
	return &ResultFlightFileList{}
}

func (p *ResultFlightFileList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResultFlightFileList) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *ResultFlightFileList) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*FlightFile, 0, size)
	for i := 0; i < size; i++ {
		_elem8 := NewFlightFile()
		if err := _elem8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem8)
		}
		p.Result = append(p.Result, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResultFlightFileList) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ResultFlightFileList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResultFlightFileList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResultFlightFileList) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *ResultFlightFileList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:result: %s", p, err)
		}
	}
	return err
}

func (p *ResultFlightFileList) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:success: %s", p, err)
	}
	return err
}

func (p *ResultFlightFileList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResultFlightFileList(%+v)", *p)
}
