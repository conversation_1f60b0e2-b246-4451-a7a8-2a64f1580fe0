// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package airport_rsync

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/airport_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = airport_types.GoUnusedProtection__
var GoUnusedProtection__ int

type FlightInfo struct {
	Id             int32                        `thrift:"id,1" json:"id"`
	AirlinesId     int32                        `thrift:"airlinesId,2" json:"airlinesId"`
	Host           string                       `thrift:"host,3" json:"host"`
	Account        string                       `thrift:"account,4" json:"account"`
	Path           string                       `thrift:"path,5" json:"path"`
	IsDeleted      bool                         `thrift:"isDeleted,6" json:"isDeleted"`
	IsPaused       bool                         `thrift:"isPaused,7" json:"isPaused"`
	LastUpdateTime int32                        `thrift:"lastUpdateTime,8" json:"lastUpdateTime"`
	Topic          string                       `thrift:"topic,9" json:"topic"`
	AirlinesStatus airport_types.AirlinesStatus `thrift:"airlinesStatus,10" json:"airlinesStatus"`
}

func NewFlightInfo() *FlightInfo {
	return &FlightInfo{
		AirlinesStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FlightInfo) IsSetAirlinesStatus() bool {
	return int64(p.AirlinesStatus) != math.MinInt32-1
}

func (p *FlightInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FlightInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FlightInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AirlinesId = v
	}
	return nil
}

func (p *FlightInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *FlightInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *FlightInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Path = v
	}
	return nil
}

func (p *FlightInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IsDeleted = v
	}
	return nil
}

func (p *FlightInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.IsPaused = v
	}
	return nil
}

func (p *FlightInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.LastUpdateTime = v
	}
	return nil
}

func (p *FlightInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Topic = v
	}
	return nil
}

func (p *FlightInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AirlinesStatus = airport_types.AirlinesStatus(v)
	}
	return nil
}

func (p *FlightInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FlightInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FlightInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("airlinesId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:airlinesId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AirlinesId)); err != nil {
		return fmt.Errorf("%T.airlinesId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:airlinesId: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:host: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:account: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("path", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Path)); err != nil {
		return fmt.Errorf("%T.path (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:path: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isDeleted", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:isDeleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsDeleted)); err != nil {
		return fmt.Errorf("%T.isDeleted (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:isDeleted: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isPaused", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:isPaused: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsPaused)); err != nil {
		return fmt.Errorf("%T.isPaused (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:isPaused: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdateTime", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:lastUpdateTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdateTime)); err != nil {
		return fmt.Errorf("%T.lastUpdateTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:lastUpdateTime: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topic", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:topic: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Topic)); err != nil {
		return fmt.Errorf("%T.topic (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:topic: %s", p, err)
	}
	return err
}

func (p *FlightInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetAirlinesStatus() {
		if err := oprot.WriteFieldBegin("airlinesStatus", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:airlinesStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AirlinesStatus)); err != nil {
			return fmt.Errorf("%T.airlinesStatus (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:airlinesStatus: %s", p, err)
		}
	}
	return err
}

func (p *FlightInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FlightInfo(%+v)", *p)
}

type FlightFile struct {
	Id             int32                          `thrift:"id,1" json:"id"`
	FlightId       int32                          `thrift:"flightId,2" json:"flightId"`
	Filename       string                         `thrift:"filename,3" json:"filename"`
	FileSendTime   int32                          `thrift:"fileSendTime,4" json:"fileSendTime"`
	FileSize       int64                          `thrift:"fileSize,5" json:"fileSize"`
	FileStatus     airport_types.FlightFileStatus `thrift:"fileStatus,6" json:"fileStatus"`
	LastUpdateTime int32                          `thrift:"lastUpdateTime,7" json:"lastUpdateTime"`
}

func NewFlightFile() *FlightFile {
	return &FlightFile{
		FileStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FlightFile) IsSetFileStatus() bool {
	return int64(p.FileStatus) != math.MinInt32-1
}

func (p *FlightFile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FlightFile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FlightFile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *FlightFile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Filename = v
	}
	return nil
}

func (p *FlightFile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FileSendTime = v
	}
	return nil
}

func (p *FlightFile) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.FileSize = v
	}
	return nil
}

func (p *FlightFile) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.FileStatus = airport_types.FlightFileStatus(v)
	}
	return nil
}

func (p *FlightFile) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.LastUpdateTime = v
	}
	return nil
}

func (p *FlightFile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FlightFile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FlightFile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FlightFile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:flightId: %s", p, err)
	}
	return err
}

func (p *FlightFile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filename", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:filename: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Filename)); err != nil {
		return fmt.Errorf("%T.filename (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:filename: %s", p, err)
	}
	return err
}

func (p *FlightFile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileSendTime", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:fileSendTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileSendTime)); err != nil {
		return fmt.Errorf("%T.fileSendTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:fileSendTime: %s", p, err)
	}
	return err
}

func (p *FlightFile) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fileSize", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:fileSize: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FileSize)); err != nil {
		return fmt.Errorf("%T.fileSize (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:fileSize: %s", p, err)
	}
	return err
}

func (p *FlightFile) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileStatus() {
		if err := oprot.WriteFieldBegin("fileStatus", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:fileStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileStatus)); err != nil {
			return fmt.Errorf("%T.fileStatus (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:fileStatus: %s", p, err)
		}
	}
	return err
}

func (p *FlightFile) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdateTime", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:lastUpdateTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdateTime)); err != nil {
		return fmt.Errorf("%T.lastUpdateTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:lastUpdateTime: %s", p, err)
	}
	return err
}

func (p *FlightFile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FlightFile(%+v)", *p)
}

type FlightStatus struct {
	Id             int32                       `thrift:"id,1" json:"id"`
	FlightId       int32                       `thrift:"flightId,2" json:"flightId"`
	ExtInfo        string                      `thrift:"extInfo,3" json:"extInfo"`
	LastUpdateTime int32                       `thrift:"lastUpdateTime,4" json:"lastUpdateTime"`
	Status         airport_types.RunningStatus `thrift:"status,5" json:"status"`
	SyncDbTime     int32                       `thrift:"syncDbTime,6" json:"syncDbTime"`
}

func NewFlightStatus() *FlightStatus {
	return &FlightStatus{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FlightStatus) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FlightStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FlightStatus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FlightStatus) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FlightId = v
	}
	return nil
}

func (p *FlightStatus) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *FlightStatus) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.LastUpdateTime = v
	}
	return nil
}

func (p *FlightStatus) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Status = airport_types.RunningStatus(v)
	}
	return nil
}

func (p *FlightStatus) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SyncDbTime = v
	}
	return nil
}

func (p *FlightStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FlightStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FlightStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FlightStatus) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("flightId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:flightId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FlightId)); err != nil {
		return fmt.Errorf("%T.flightId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:flightId: %s", p, err)
	}
	return err
}

func (p *FlightStatus) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:extInfo: %s", p, err)
	}
	return err
}

func (p *FlightStatus) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdateTime", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:lastUpdateTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdateTime)); err != nil {
		return fmt.Errorf("%T.lastUpdateTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:lastUpdateTime: %s", p, err)
	}
	return err
}

func (p *FlightStatus) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:status: %s", p, err)
		}
	}
	return err
}

func (p *FlightStatus) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("syncDbTime", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:syncDbTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SyncDbTime)); err != nil {
		return fmt.Errorf("%T.syncDbTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:syncDbTime: %s", p, err)
	}
	return err
}

func (p *FlightStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FlightStatus(%+v)", *p)
}

type AirportStatus struct {
	Id             int32                       `thrift:"id,1" json:"id"`
	Host           string                      `thrift:"host,2" json:"host"`
	Status         airport_types.RunningStatus `thrift:"status,3" json:"status"`
	Signal         airport_types.SignalType    `thrift:"signal,4" json:"signal"`
	BeatHeartTime  int32                       `thrift:"beatHeartTime,5" json:"beatHeartTime"`
	RunningVersion string                      `thrift:"runningVersion,6" json:"runningVersion"`
	CurrentVersion string                      `thrift:"currentVersion,7" json:"currentVersion"`
	LastUpdateTime int32                       `thrift:"lastUpdateTime,8" json:"lastUpdateTime"`
	SyncDbTime     int32                       `thrift:"syncDbTime,9" json:"syncDbTime"`
	StartupTime    int32                       `thrift:"startupTime,10" json:"startupTime"`
	RunDirSize     int64                       `thrift:"runDirSize,11" json:"runDirSize"`
}

func NewAirportStatus() *AirportStatus {
	return &AirportStatus{
		Status: math.MinInt32 - 1, // unset sentinal value

		Signal: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AirportStatus) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AirportStatus) IsSetSignal() bool {
	return int64(p.Signal) != math.MinInt32-1
}

func (p *AirportStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AirportStatus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AirportStatus) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *AirportStatus) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = airport_types.RunningStatus(v)
	}
	return nil
}

func (p *AirportStatus) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Signal = airport_types.SignalType(v)
	}
	return nil
}

func (p *AirportStatus) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.BeatHeartTime = v
	}
	return nil
}

func (p *AirportStatus) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.RunningVersion = v
	}
	return nil
}

func (p *AirportStatus) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CurrentVersion = v
	}
	return nil
}

func (p *AirportStatus) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.LastUpdateTime = v
	}
	return nil
}

func (p *AirportStatus) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SyncDbTime = v
	}
	return nil
}

func (p *AirportStatus) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.StartupTime = v
	}
	return nil
}

func (p *AirportStatus) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.RunDirSize = v
	}
	return nil
}

func (p *AirportStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AirportStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AirportStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:status: %s", p, err)
		}
	}
	return err
}

func (p *AirportStatus) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetSignal() {
		if err := oprot.WriteFieldBegin("signal", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:signal: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Signal)); err != nil {
			return fmt.Errorf("%T.signal (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:signal: %s", p, err)
		}
	}
	return err
}

func (p *AirportStatus) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beatHeartTime", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:beatHeartTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BeatHeartTime)); err != nil {
		return fmt.Errorf("%T.beatHeartTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:beatHeartTime: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runningVersion", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:runningVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RunningVersion)); err != nil {
		return fmt.Errorf("%T.runningVersion (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:runningVersion: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currentVersion", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:currentVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrentVersion)); err != nil {
		return fmt.Errorf("%T.currentVersion (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:currentVersion: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdateTime", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:lastUpdateTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdateTime)); err != nil {
		return fmt.Errorf("%T.lastUpdateTime (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:lastUpdateTime: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("syncDbTime", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:syncDbTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SyncDbTime)); err != nil {
		return fmt.Errorf("%T.syncDbTime (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:syncDbTime: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startupTime", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:startupTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartupTime)); err != nil {
		return fmt.Errorf("%T.startupTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:startupTime: %s", p, err)
	}
	return err
}

func (p *AirportStatus) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runDirSize", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:runDirSize: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RunDirSize)); err != nil {
		return fmt.Errorf("%T.runDirSize (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:runDirSize: %s", p, err)
	}
	return err
}

func (p *AirportStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AirportStatus(%+v)", *p)
}

type TerminalStatus struct {
	Id             int32                       `thrift:"id,1" json:"id"`
	Host           string                      `thrift:"host,2" json:"host"`
	Account        string                      `thrift:"account,3" json:"account"`
	Status         airport_types.RunningStatus `thrift:"status,4" json:"status"`
	ExtInfo        string                      `thrift:"extInfo,5" json:"extInfo"`
	Signal         airport_types.SignalType    `thrift:"signal,6" json:"signal"`
	BeatHeartTime  int32                       `thrift:"beatHeartTime,7" json:"beatHeartTime"`
	RunningVersion string                      `thrift:"runningVersion,8" json:"runningVersion"`
	CurrentVersion string                      `thrift:"currentVersion,9" json:"currentVersion"`
	LastUpdateTime int32                       `thrift:"lastUpdateTime,10" json:"lastUpdateTime"`
	SyncDbTime     int32                       `thrift:"syncDbTime,11" json:"syncDbTime"`
	StartupTime    int32                       `thrift:"startupTime,12" json:"startupTime"`
}

func NewTerminalStatus() *TerminalStatus {
	return &TerminalStatus{
		Status: math.MinInt32 - 1, // unset sentinal value

		Signal: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TerminalStatus) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *TerminalStatus) IsSetSignal() bool {
	return int64(p.Signal) != math.MinInt32-1
}

func (p *TerminalStatus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TerminalStatus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *TerminalStatus) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Host = v
	}
	return nil
}

func (p *TerminalStatus) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *TerminalStatus) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = airport_types.RunningStatus(v)
	}
	return nil
}

func (p *TerminalStatus) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *TerminalStatus) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Signal = airport_types.SignalType(v)
	}
	return nil
}

func (p *TerminalStatus) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.BeatHeartTime = v
	}
	return nil
}

func (p *TerminalStatus) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.RunningVersion = v
	}
	return nil
}

func (p *TerminalStatus) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CurrentVersion = v
	}
	return nil
}

func (p *TerminalStatus) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.LastUpdateTime = v
	}
	return nil
}

func (p *TerminalStatus) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SyncDbTime = v
	}
	return nil
}

func (p *TerminalStatus) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.StartupTime = v
	}
	return nil
}

func (p *TerminalStatus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TerminalStatus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TerminalStatus) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("host", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:host: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Host)); err != nil {
		return fmt.Errorf("%T.host (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:host: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:status: %s", p, err)
		}
	}
	return err
}

func (p *TerminalStatus) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:extInfo: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetSignal() {
		if err := oprot.WriteFieldBegin("signal", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:signal: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Signal)); err != nil {
			return fmt.Errorf("%T.signal (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:signal: %s", p, err)
		}
	}
	return err
}

func (p *TerminalStatus) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("beatHeartTime", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:beatHeartTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BeatHeartTime)); err != nil {
		return fmt.Errorf("%T.beatHeartTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:beatHeartTime: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("runningVersion", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:runningVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RunningVersion)); err != nil {
		return fmt.Errorf("%T.runningVersion (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:runningVersion: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currentVersion", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:currentVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrentVersion)); err != nil {
		return fmt.Errorf("%T.currentVersion (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:currentVersion: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdateTime", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:lastUpdateTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdateTime)); err != nil {
		return fmt.Errorf("%T.lastUpdateTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:lastUpdateTime: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("syncDbTime", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:syncDbTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SyncDbTime)); err != nil {
		return fmt.Errorf("%T.syncDbTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:syncDbTime: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startupTime", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:startupTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartupTime)); err != nil {
		return fmt.Errorf("%T.startupTime (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:startupTime: %s", p, err)
	}
	return err
}

func (p *TerminalStatus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TerminalStatus(%+v)", *p)
}
