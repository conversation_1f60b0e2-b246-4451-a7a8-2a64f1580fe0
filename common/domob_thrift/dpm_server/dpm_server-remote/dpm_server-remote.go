// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dpm_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>der<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addPromotion(RequestHeader header, Promotion promotion)")
	fmt.Fprintln(os.Stderr, "  void editPromotion(RequestHeader header, i32 promotionId, Promotion promotion)")
	fmt.Fprintln(os.<PERSON>, "  QueryResult searchPromotionByParam(RequestHeader header, PromotionParam param)")
	fmt.Fprintln(os.Stderr, "   getPromotionByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addPromotionProperty(RequestHeader header, PromotionProperty promotionProperty)")
	fmt.Fprintln(os.Stderr, "  void editPromotionProperty(RequestHeader header, i32 promotionPropertyId, PromotionProperty promotionProperty)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchPromotionPropertyByParam(RequestHeader header, PromotionPropertyParam param)")
	fmt.Fprintln(os.Stderr, "   getPromotionPropertyByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addChannel(RequestHeader header, Channel channel)")
	fmt.Fprintln(os.Stderr, "  void editChannel(RequestHeader header, i32 channelId, Channel channel)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchChannelByParam(RequestHeader header, ChannelParam param)")
	fmt.Fprintln(os.Stderr, "   getChannelByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addTracking(RequestHeader header, Tracking tracking)")
	fmt.Fprintln(os.Stderr, "  void editTracking(RequestHeader header, i32 trackingId, Tracking tracking)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchTrackingByParam(RequestHeader header, TrackingParam param)")
	fmt.Fprintln(os.Stderr, "   getTrackingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCompany(RequestHeader header, Company company)")
	fmt.Fprintln(os.Stderr, "  void editCompany(RequestHeader header, i32 companyId, Company company)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchCompanyByParam(RequestHeader header, CompanyParam param)")
	fmt.Fprintln(os.Stderr, "   getCompanyByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dpm_server.NewDpmServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPromotion requires 2 args")
			flag.Usage()
		}
		arg97 := flag.Arg(1)
		mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
		defer mbTrans98.Close()
		_, err99 := mbTrans98.WriteString(arg97)
		if err99 != nil {
			Usage()
			return
		}
		factory100 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt101 := factory100.GetProtocol(mbTrans98)
		argvalue0 := dpm_server.NewRequestHeader()
		err102 := argvalue0.Read(jsProt101)
		if err102 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg103 := flag.Arg(2)
		mbTrans104 := thrift.NewTMemoryBufferLen(len(arg103))
		defer mbTrans104.Close()
		_, err105 := mbTrans104.WriteString(arg103)
		if err105 != nil {
			Usage()
			return
		}
		factory106 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt107 := factory106.GetProtocol(mbTrans104)
		argvalue1 := dpm_server.NewPromotion()
		err108 := argvalue1.Read(jsProt107)
		if err108 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "editPromotion":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditPromotion requires 3 args")
			flag.Usage()
		}
		arg109 := flag.Arg(1)
		mbTrans110 := thrift.NewTMemoryBufferLen(len(arg109))
		defer mbTrans110.Close()
		_, err111 := mbTrans110.WriteString(arg109)
		if err111 != nil {
			Usage()
			return
		}
		factory112 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt113 := factory112.GetProtocol(mbTrans110)
		argvalue0 := dpm_server.NewRequestHeader()
		err114 := argvalue0.Read(jsProt113)
		if err114 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err115 := (strconv.Atoi(flag.Arg(2)))
		if err115 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg116 := flag.Arg(3)
		mbTrans117 := thrift.NewTMemoryBufferLen(len(arg116))
		defer mbTrans117.Close()
		_, err118 := mbTrans117.WriteString(arg116)
		if err118 != nil {
			Usage()
			return
		}
		factory119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt120 := factory119.GetProtocol(mbTrans117)
		argvalue2 := dpm_server.NewPromotion()
		err121 := argvalue2.Read(jsProt120)
		if err121 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditPromotion(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchPromotionByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchPromotionByParam requires 2 args")
			flag.Usage()
		}
		arg122 := flag.Arg(1)
		mbTrans123 := thrift.NewTMemoryBufferLen(len(arg122))
		defer mbTrans123.Close()
		_, err124 := mbTrans123.WriteString(arg122)
		if err124 != nil {
			Usage()
			return
		}
		factory125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt126 := factory125.GetProtocol(mbTrans123)
		argvalue0 := dpm_server.NewRequestHeader()
		err127 := argvalue0.Read(jsProt126)
		if err127 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg128 := flag.Arg(2)
		mbTrans129 := thrift.NewTMemoryBufferLen(len(arg128))
		defer mbTrans129.Close()
		_, err130 := mbTrans129.WriteString(arg128)
		if err130 != nil {
			Usage()
			return
		}
		factory131 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt132 := factory131.GetProtocol(mbTrans129)
		argvalue1 := dpm_server.NewPromotionParam()
		err133 := argvalue1.Read(jsProt132)
		if err133 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchPromotionByParam(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPromotionByIds requires 2 args")
			flag.Usage()
		}
		arg134 := flag.Arg(1)
		mbTrans135 := thrift.NewTMemoryBufferLen(len(arg134))
		defer mbTrans135.Close()
		_, err136 := mbTrans135.WriteString(arg134)
		if err136 != nil {
			Usage()
			return
		}
		factory137 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt138 := factory137.GetProtocol(mbTrans135)
		argvalue0 := dpm_server.NewRequestHeader()
		err139 := argvalue0.Read(jsProt138)
		if err139 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg140 := flag.Arg(2)
		mbTrans141 := thrift.NewTMemoryBufferLen(len(arg140))
		defer mbTrans141.Close()
		_, err142 := mbTrans141.WriteString(arg140)
		if err142 != nil {
			Usage()
			return
		}
		factory143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt144 := factory143.GetProtocol(mbTrans141)
		containerStruct1 := dpm_server.NewGetPromotionByIdsArgs()
		err145 := containerStruct1.ReadField2(jsProt144)
		if err145 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPromotionByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addPromotionProperty":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPromotionProperty requires 2 args")
			flag.Usage()
		}
		arg146 := flag.Arg(1)
		mbTrans147 := thrift.NewTMemoryBufferLen(len(arg146))
		defer mbTrans147.Close()
		_, err148 := mbTrans147.WriteString(arg146)
		if err148 != nil {
			Usage()
			return
		}
		factory149 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt150 := factory149.GetProtocol(mbTrans147)
		argvalue0 := dpm_server.NewRequestHeader()
		err151 := argvalue0.Read(jsProt150)
		if err151 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg152 := flag.Arg(2)
		mbTrans153 := thrift.NewTMemoryBufferLen(len(arg152))
		defer mbTrans153.Close()
		_, err154 := mbTrans153.WriteString(arg152)
		if err154 != nil {
			Usage()
			return
		}
		factory155 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt156 := factory155.GetProtocol(mbTrans153)
		argvalue1 := dpm_server.NewPromotionProperty()
		err157 := argvalue1.Read(jsProt156)
		if err157 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPromotionProperty(value0, value1))
		fmt.Print("\n")
		break
	case "editPromotionProperty":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditPromotionProperty requires 3 args")
			flag.Usage()
		}
		arg158 := flag.Arg(1)
		mbTrans159 := thrift.NewTMemoryBufferLen(len(arg158))
		defer mbTrans159.Close()
		_, err160 := mbTrans159.WriteString(arg158)
		if err160 != nil {
			Usage()
			return
		}
		factory161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt162 := factory161.GetProtocol(mbTrans159)
		argvalue0 := dpm_server.NewRequestHeader()
		err163 := argvalue0.Read(jsProt162)
		if err163 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err164 := (strconv.Atoi(flag.Arg(2)))
		if err164 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg165 := flag.Arg(3)
		mbTrans166 := thrift.NewTMemoryBufferLen(len(arg165))
		defer mbTrans166.Close()
		_, err167 := mbTrans166.WriteString(arg165)
		if err167 != nil {
			Usage()
			return
		}
		factory168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt169 := factory168.GetProtocol(mbTrans166)
		argvalue2 := dpm_server.NewPromotionProperty()
		err170 := argvalue2.Read(jsProt169)
		if err170 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditPromotionProperty(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchPromotionPropertyByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchPromotionPropertyByParam requires 2 args")
			flag.Usage()
		}
		arg171 := flag.Arg(1)
		mbTrans172 := thrift.NewTMemoryBufferLen(len(arg171))
		defer mbTrans172.Close()
		_, err173 := mbTrans172.WriteString(arg171)
		if err173 != nil {
			Usage()
			return
		}
		factory174 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt175 := factory174.GetProtocol(mbTrans172)
		argvalue0 := dpm_server.NewRequestHeader()
		err176 := argvalue0.Read(jsProt175)
		if err176 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg177 := flag.Arg(2)
		mbTrans178 := thrift.NewTMemoryBufferLen(len(arg177))
		defer mbTrans178.Close()
		_, err179 := mbTrans178.WriteString(arg177)
		if err179 != nil {
			Usage()
			return
		}
		factory180 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt181 := factory180.GetProtocol(mbTrans178)
		argvalue1 := dpm_server.NewPromotionPropertyParam()
		err182 := argvalue1.Read(jsProt181)
		if err182 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchPromotionPropertyByParam(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionPropertyByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPromotionPropertyByIds requires 2 args")
			flag.Usage()
		}
		arg183 := flag.Arg(1)
		mbTrans184 := thrift.NewTMemoryBufferLen(len(arg183))
		defer mbTrans184.Close()
		_, err185 := mbTrans184.WriteString(arg183)
		if err185 != nil {
			Usage()
			return
		}
		factory186 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt187 := factory186.GetProtocol(mbTrans184)
		argvalue0 := dpm_server.NewRequestHeader()
		err188 := argvalue0.Read(jsProt187)
		if err188 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg189 := flag.Arg(2)
		mbTrans190 := thrift.NewTMemoryBufferLen(len(arg189))
		defer mbTrans190.Close()
		_, err191 := mbTrans190.WriteString(arg189)
		if err191 != nil {
			Usage()
			return
		}
		factory192 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt193 := factory192.GetProtocol(mbTrans190)
		containerStruct1 := dpm_server.NewGetPromotionPropertyByIdsArgs()
		err194 := containerStruct1.ReadField2(jsProt193)
		if err194 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPromotionPropertyByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addChannel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddChannel requires 2 args")
			flag.Usage()
		}
		arg195 := flag.Arg(1)
		mbTrans196 := thrift.NewTMemoryBufferLen(len(arg195))
		defer mbTrans196.Close()
		_, err197 := mbTrans196.WriteString(arg195)
		if err197 != nil {
			Usage()
			return
		}
		factory198 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt199 := factory198.GetProtocol(mbTrans196)
		argvalue0 := dpm_server.NewRequestHeader()
		err200 := argvalue0.Read(jsProt199)
		if err200 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg201 := flag.Arg(2)
		mbTrans202 := thrift.NewTMemoryBufferLen(len(arg201))
		defer mbTrans202.Close()
		_, err203 := mbTrans202.WriteString(arg201)
		if err203 != nil {
			Usage()
			return
		}
		factory204 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt205 := factory204.GetProtocol(mbTrans202)
		argvalue1 := dpm_server.NewChannel()
		err206 := argvalue1.Read(jsProt205)
		if err206 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddChannel(value0, value1))
		fmt.Print("\n")
		break
	case "editChannel":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditChannel requires 3 args")
			flag.Usage()
		}
		arg207 := flag.Arg(1)
		mbTrans208 := thrift.NewTMemoryBufferLen(len(arg207))
		defer mbTrans208.Close()
		_, err209 := mbTrans208.WriteString(arg207)
		if err209 != nil {
			Usage()
			return
		}
		factory210 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt211 := factory210.GetProtocol(mbTrans208)
		argvalue0 := dpm_server.NewRequestHeader()
		err212 := argvalue0.Read(jsProt211)
		if err212 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err213 := (strconv.Atoi(flag.Arg(2)))
		if err213 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg214 := flag.Arg(3)
		mbTrans215 := thrift.NewTMemoryBufferLen(len(arg214))
		defer mbTrans215.Close()
		_, err216 := mbTrans215.WriteString(arg214)
		if err216 != nil {
			Usage()
			return
		}
		factory217 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt218 := factory217.GetProtocol(mbTrans215)
		argvalue2 := dpm_server.NewChannel()
		err219 := argvalue2.Read(jsProt218)
		if err219 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditChannel(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchChannelByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchChannelByParam requires 2 args")
			flag.Usage()
		}
		arg220 := flag.Arg(1)
		mbTrans221 := thrift.NewTMemoryBufferLen(len(arg220))
		defer mbTrans221.Close()
		_, err222 := mbTrans221.WriteString(arg220)
		if err222 != nil {
			Usage()
			return
		}
		factory223 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt224 := factory223.GetProtocol(mbTrans221)
		argvalue0 := dpm_server.NewRequestHeader()
		err225 := argvalue0.Read(jsProt224)
		if err225 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg226 := flag.Arg(2)
		mbTrans227 := thrift.NewTMemoryBufferLen(len(arg226))
		defer mbTrans227.Close()
		_, err228 := mbTrans227.WriteString(arg226)
		if err228 != nil {
			Usage()
			return
		}
		factory229 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt230 := factory229.GetProtocol(mbTrans227)
		argvalue1 := dpm_server.NewChannelParam()
		err231 := argvalue1.Read(jsProt230)
		if err231 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchChannelByParam(value0, value1))
		fmt.Print("\n")
		break
	case "getChannelByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetChannelByIds requires 2 args")
			flag.Usage()
		}
		arg232 := flag.Arg(1)
		mbTrans233 := thrift.NewTMemoryBufferLen(len(arg232))
		defer mbTrans233.Close()
		_, err234 := mbTrans233.WriteString(arg232)
		if err234 != nil {
			Usage()
			return
		}
		factory235 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt236 := factory235.GetProtocol(mbTrans233)
		argvalue0 := dpm_server.NewRequestHeader()
		err237 := argvalue0.Read(jsProt236)
		if err237 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg238 := flag.Arg(2)
		mbTrans239 := thrift.NewTMemoryBufferLen(len(arg238))
		defer mbTrans239.Close()
		_, err240 := mbTrans239.WriteString(arg238)
		if err240 != nil {
			Usage()
			return
		}
		factory241 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt242 := factory241.GetProtocol(mbTrans239)
		containerStruct1 := dpm_server.NewGetChannelByIdsArgs()
		err243 := containerStruct1.ReadField2(jsProt242)
		if err243 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetChannelByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addTracking":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTracking requires 2 args")
			flag.Usage()
		}
		arg244 := flag.Arg(1)
		mbTrans245 := thrift.NewTMemoryBufferLen(len(arg244))
		defer mbTrans245.Close()
		_, err246 := mbTrans245.WriteString(arg244)
		if err246 != nil {
			Usage()
			return
		}
		factory247 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt248 := factory247.GetProtocol(mbTrans245)
		argvalue0 := dpm_server.NewRequestHeader()
		err249 := argvalue0.Read(jsProt248)
		if err249 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg250 := flag.Arg(2)
		mbTrans251 := thrift.NewTMemoryBufferLen(len(arg250))
		defer mbTrans251.Close()
		_, err252 := mbTrans251.WriteString(arg250)
		if err252 != nil {
			Usage()
			return
		}
		factory253 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt254 := factory253.GetProtocol(mbTrans251)
		argvalue1 := dpm_server.NewTracking()
		err255 := argvalue1.Read(jsProt254)
		if err255 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTracking(value0, value1))
		fmt.Print("\n")
		break
	case "editTracking":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditTracking requires 3 args")
			flag.Usage()
		}
		arg256 := flag.Arg(1)
		mbTrans257 := thrift.NewTMemoryBufferLen(len(arg256))
		defer mbTrans257.Close()
		_, err258 := mbTrans257.WriteString(arg256)
		if err258 != nil {
			Usage()
			return
		}
		factory259 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt260 := factory259.GetProtocol(mbTrans257)
		argvalue0 := dpm_server.NewRequestHeader()
		err261 := argvalue0.Read(jsProt260)
		if err261 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err262 := (strconv.Atoi(flag.Arg(2)))
		if err262 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg263 := flag.Arg(3)
		mbTrans264 := thrift.NewTMemoryBufferLen(len(arg263))
		defer mbTrans264.Close()
		_, err265 := mbTrans264.WriteString(arg263)
		if err265 != nil {
			Usage()
			return
		}
		factory266 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt267 := factory266.GetProtocol(mbTrans264)
		argvalue2 := dpm_server.NewTracking()
		err268 := argvalue2.Read(jsProt267)
		if err268 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditTracking(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchTrackingByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchTrackingByParam requires 2 args")
			flag.Usage()
		}
		arg269 := flag.Arg(1)
		mbTrans270 := thrift.NewTMemoryBufferLen(len(arg269))
		defer mbTrans270.Close()
		_, err271 := mbTrans270.WriteString(arg269)
		if err271 != nil {
			Usage()
			return
		}
		factory272 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt273 := factory272.GetProtocol(mbTrans270)
		argvalue0 := dpm_server.NewRequestHeader()
		err274 := argvalue0.Read(jsProt273)
		if err274 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg275 := flag.Arg(2)
		mbTrans276 := thrift.NewTMemoryBufferLen(len(arg275))
		defer mbTrans276.Close()
		_, err277 := mbTrans276.WriteString(arg275)
		if err277 != nil {
			Usage()
			return
		}
		factory278 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt279 := factory278.GetProtocol(mbTrans276)
		argvalue1 := dpm_server.NewTrackingParam()
		err280 := argvalue1.Read(jsProt279)
		if err280 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchTrackingByParam(value0, value1))
		fmt.Print("\n")
		break
	case "getTrackingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTrackingByIds requires 2 args")
			flag.Usage()
		}
		arg281 := flag.Arg(1)
		mbTrans282 := thrift.NewTMemoryBufferLen(len(arg281))
		defer mbTrans282.Close()
		_, err283 := mbTrans282.WriteString(arg281)
		if err283 != nil {
			Usage()
			return
		}
		factory284 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt285 := factory284.GetProtocol(mbTrans282)
		argvalue0 := dpm_server.NewRequestHeader()
		err286 := argvalue0.Read(jsProt285)
		if err286 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg287 := flag.Arg(2)
		mbTrans288 := thrift.NewTMemoryBufferLen(len(arg287))
		defer mbTrans288.Close()
		_, err289 := mbTrans288.WriteString(arg287)
		if err289 != nil {
			Usage()
			return
		}
		factory290 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt291 := factory290.GetProtocol(mbTrans288)
		containerStruct1 := dpm_server.NewGetTrackingByIdsArgs()
		err292 := containerStruct1.ReadField2(jsProt291)
		if err292 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetTrackingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCompany":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCompany requires 2 args")
			flag.Usage()
		}
		arg293 := flag.Arg(1)
		mbTrans294 := thrift.NewTMemoryBufferLen(len(arg293))
		defer mbTrans294.Close()
		_, err295 := mbTrans294.WriteString(arg293)
		if err295 != nil {
			Usage()
			return
		}
		factory296 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt297 := factory296.GetProtocol(mbTrans294)
		argvalue0 := dpm_server.NewRequestHeader()
		err298 := argvalue0.Read(jsProt297)
		if err298 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg299 := flag.Arg(2)
		mbTrans300 := thrift.NewTMemoryBufferLen(len(arg299))
		defer mbTrans300.Close()
		_, err301 := mbTrans300.WriteString(arg299)
		if err301 != nil {
			Usage()
			return
		}
		factory302 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt303 := factory302.GetProtocol(mbTrans300)
		argvalue1 := dpm_server.NewCompany()
		err304 := argvalue1.Read(jsProt303)
		if err304 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCompany(value0, value1))
		fmt.Print("\n")
		break
	case "editCompany":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCompany requires 3 args")
			flag.Usage()
		}
		arg305 := flag.Arg(1)
		mbTrans306 := thrift.NewTMemoryBufferLen(len(arg305))
		defer mbTrans306.Close()
		_, err307 := mbTrans306.WriteString(arg305)
		if err307 != nil {
			Usage()
			return
		}
		factory308 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt309 := factory308.GetProtocol(mbTrans306)
		argvalue0 := dpm_server.NewRequestHeader()
		err310 := argvalue0.Read(jsProt309)
		if err310 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err311 := (strconv.Atoi(flag.Arg(2)))
		if err311 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg312 := flag.Arg(3)
		mbTrans313 := thrift.NewTMemoryBufferLen(len(arg312))
		defer mbTrans313.Close()
		_, err314 := mbTrans313.WriteString(arg312)
		if err314 != nil {
			Usage()
			return
		}
		factory315 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt316 := factory315.GetProtocol(mbTrans313)
		argvalue2 := dpm_server.NewCompany()
		err317 := argvalue2.Read(jsProt316)
		if err317 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditCompany(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchCompanyByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchCompanyByParam requires 2 args")
			flag.Usage()
		}
		arg318 := flag.Arg(1)
		mbTrans319 := thrift.NewTMemoryBufferLen(len(arg318))
		defer mbTrans319.Close()
		_, err320 := mbTrans319.WriteString(arg318)
		if err320 != nil {
			Usage()
			return
		}
		factory321 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt322 := factory321.GetProtocol(mbTrans319)
		argvalue0 := dpm_server.NewRequestHeader()
		err323 := argvalue0.Read(jsProt322)
		if err323 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg324 := flag.Arg(2)
		mbTrans325 := thrift.NewTMemoryBufferLen(len(arg324))
		defer mbTrans325.Close()
		_, err326 := mbTrans325.WriteString(arg324)
		if err326 != nil {
			Usage()
			return
		}
		factory327 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt328 := factory327.GetProtocol(mbTrans325)
		argvalue1 := dpm_server.NewCompanyParam()
		err329 := argvalue1.Read(jsProt328)
		if err329 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchCompanyByParam(value0, value1))
		fmt.Print("\n")
		break
	case "getCompanyByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCompanyByIds requires 2 args")
			flag.Usage()
		}
		arg330 := flag.Arg(1)
		mbTrans331 := thrift.NewTMemoryBufferLen(len(arg330))
		defer mbTrans331.Close()
		_, err332 := mbTrans331.WriteString(arg330)
		if err332 != nil {
			Usage()
			return
		}
		factory333 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt334 := factory333.GetProtocol(mbTrans331)
		argvalue0 := dpm_server.NewRequestHeader()
		err335 := argvalue0.Read(jsProt334)
		if err335 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg336 := flag.Arg(2)
		mbTrans337 := thrift.NewTMemoryBufferLen(len(arg336))
		defer mbTrans337.Close()
		_, err338 := mbTrans337.WriteString(arg336)
		if err338 != nil {
			Usage()
			return
		}
		factory339 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt340 := factory339.GetProtocol(mbTrans337)
		containerStruct1 := dpm_server.NewGetCompanyByIdsArgs()
		err341 := containerStruct1.ReadField2(jsProt340)
		if err341 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCompanyByIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
