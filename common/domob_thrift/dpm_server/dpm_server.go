// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dpm_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dpm_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dpm_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type DpmServer interface {
	// Parameters:
	//  - Header
	//  - Promotion
	AddPromotion(header *common.RequestHeader, promotion *dpm_types.Promotion) (r int32, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - PromotionId
	//  - Promotion
	EditPromotion(header *common.RequestHeader, promotionId int32, promotion *dpm_types.Promotion) (e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Param
	SearchPromotionByParam(header *common.RequestHeader, param *dpm_types.PromotionParam) (r *common.QueryResult, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Ids
	GetPromotionByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Promotion, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - PromotionProperty
	AddPromotionProperty(header *common.RequestHeader, promotionProperty *dpm_types.PromotionProperty) (r int32, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - PromotionPropertyId
	//  - PromotionProperty
	EditPromotionProperty(header *common.RequestHeader, promotionPropertyId int32, promotionProperty *dpm_types.PromotionProperty) (e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Param
	SearchPromotionPropertyByParam(header *common.RequestHeader, param *dpm_types.PromotionPropertyParam) (r *common.QueryResult, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Ids
	GetPromotionPropertyByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.PromotionProperty, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Channel
	AddChannel(header *common.RequestHeader, channel *dpm_types.Channel) (r int32, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - ChannelId
	//  - Channel
	EditChannel(header *common.RequestHeader, channelId int32, channel *dpm_types.Channel) (e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Param
	SearchChannelByParam(header *common.RequestHeader, param *dpm_types.ChannelParam) (r *common.QueryResult, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Ids
	GetChannelByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Channel, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Tracking
	AddTracking(header *common.RequestHeader, tracking *dpm_types.Tracking) (r int32, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - TrackingId
	//  - Tracking
	EditTracking(header *common.RequestHeader, trackingId int32, tracking *dpm_types.Tracking) (e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Param
	SearchTrackingByParam(header *common.RequestHeader, param *dpm_types.TrackingParam) (r *common.QueryResult, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Ids
	GetTrackingByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Tracking, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Company
	AddCompany(header *common.RequestHeader, company *dpm_types.Company) (r int32, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - CompanyId
	//  - Company
	EditCompany(header *common.RequestHeader, companyId int32, company *dpm_types.Company) (e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Param
	SearchCompanyByParam(header *common.RequestHeader, param *dpm_types.CompanyParam) (r *common.QueryResult, e *DpmServerException, err error)
	// Parameters:
	//  - Header
	//  - Ids
	GetCompanyByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Company, e *DpmServerException, err error)
}

type DpmServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDpmServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DpmServerClient {
	return &DpmServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDpmServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DpmServerClient {
	return &DpmServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// Parameters:
//  - Header
//  - Promotion
func (p *DpmServerClient) AddPromotion(header *common.RequestHeader, promotion *dpm_types.Promotion) (r int32, e *DpmServerException, err error) {
	if err = p.sendAddPromotion(header, promotion); err != nil {
		return
	}
	return p.recvAddPromotion()
}

func (p *DpmServerClient) sendAddPromotion(header *common.RequestHeader, promotion *dpm_types.Promotion) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addPromotion", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddPromotionArgs()
	args0.Header = header
	args0.Promotion = promotion
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvAddPromotion() (value int32, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddPromotionResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// Parameters:
//  - Header
//  - PromotionId
//  - Promotion
func (p *DpmServerClient) EditPromotion(header *common.RequestHeader, promotionId int32, promotion *dpm_types.Promotion) (e *DpmServerException, err error) {
	if err = p.sendEditPromotion(header, promotionId, promotion); err != nil {
		return
	}
	return p.recvEditPromotion()
}

func (p *DpmServerClient) sendEditPromotion(header *common.RequestHeader, promotionId int32, promotion *dpm_types.Promotion) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editPromotion", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewEditPromotionArgs()
	args4.Header = header
	args4.PromotionId = promotionId
	args4.Promotion = promotion
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvEditPromotion() (e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewEditPromotionResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result5.E != nil {
		e = result5.E
	}
	return
}

// Parameters:
//  - Header
//  - Param
func (p *DpmServerClient) SearchPromotionByParam(header *common.RequestHeader, param *dpm_types.PromotionParam) (r *common.QueryResult, e *DpmServerException, err error) {
	if err = p.sendSearchPromotionByParam(header, param); err != nil {
		return
	}
	return p.recvSearchPromotionByParam()
}

func (p *DpmServerClient) sendSearchPromotionByParam(header *common.RequestHeader, param *dpm_types.PromotionParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchPromotionByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewSearchPromotionByParamArgs()
	args8.Header = header
	args8.Param = param
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvSearchPromotionByParam() (value *common.QueryResult, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewSearchPromotionByParamResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

// Parameters:
//  - Header
//  - Ids
func (p *DpmServerClient) GetPromotionByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Promotion, e *DpmServerException, err error) {
	if err = p.sendGetPromotionByIds(header, ids); err != nil {
		return
	}
	return p.recvGetPromotionByIds()
}

func (p *DpmServerClient) sendGetPromotionByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPromotionByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetPromotionByIdsArgs()
	args12.Header = header
	args12.Ids = ids
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvGetPromotionByIds() (value map[int32]*dpm_types.Promotion, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetPromotionByIdsResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.E != nil {
		e = result13.E
	}
	return
}

// Parameters:
//  - Header
//  - PromotionProperty
func (p *DpmServerClient) AddPromotionProperty(header *common.RequestHeader, promotionProperty *dpm_types.PromotionProperty) (r int32, e *DpmServerException, err error) {
	if err = p.sendAddPromotionProperty(header, promotionProperty); err != nil {
		return
	}
	return p.recvAddPromotionProperty()
}

func (p *DpmServerClient) sendAddPromotionProperty(header *common.RequestHeader, promotionProperty *dpm_types.PromotionProperty) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addPromotionProperty", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewAddPromotionPropertyArgs()
	args16.Header = header
	args16.PromotionProperty = promotionProperty
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvAddPromotionProperty() (value int32, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewAddPromotionPropertyResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.E != nil {
		e = result17.E
	}
	return
}

// Parameters:
//  - Header
//  - PromotionPropertyId
//  - PromotionProperty
func (p *DpmServerClient) EditPromotionProperty(header *common.RequestHeader, promotionPropertyId int32, promotionProperty *dpm_types.PromotionProperty) (e *DpmServerException, err error) {
	if err = p.sendEditPromotionProperty(header, promotionPropertyId, promotionProperty); err != nil {
		return
	}
	return p.recvEditPromotionProperty()
}

func (p *DpmServerClient) sendEditPromotionProperty(header *common.RequestHeader, promotionPropertyId int32, promotionProperty *dpm_types.PromotionProperty) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editPromotionProperty", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewEditPromotionPropertyArgs()
	args20.Header = header
	args20.PromotionPropertyId = promotionPropertyId
	args20.PromotionProperty = promotionProperty
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvEditPromotionProperty() (e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewEditPromotionPropertyResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result21.E != nil {
		e = result21.E
	}
	return
}

// Parameters:
//  - Header
//  - Param
func (p *DpmServerClient) SearchPromotionPropertyByParam(header *common.RequestHeader, param *dpm_types.PromotionPropertyParam) (r *common.QueryResult, e *DpmServerException, err error) {
	if err = p.sendSearchPromotionPropertyByParam(header, param); err != nil {
		return
	}
	return p.recvSearchPromotionPropertyByParam()
}

func (p *DpmServerClient) sendSearchPromotionPropertyByParam(header *common.RequestHeader, param *dpm_types.PromotionPropertyParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchPromotionPropertyByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewSearchPromotionPropertyByParamArgs()
	args24.Header = header
	args24.Param = param
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvSearchPromotionPropertyByParam() (value *common.QueryResult, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewSearchPromotionPropertyByParamResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.E != nil {
		e = result25.E
	}
	return
}

// Parameters:
//  - Header
//  - Ids
func (p *DpmServerClient) GetPromotionPropertyByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.PromotionProperty, e *DpmServerException, err error) {
	if err = p.sendGetPromotionPropertyByIds(header, ids); err != nil {
		return
	}
	return p.recvGetPromotionPropertyByIds()
}

func (p *DpmServerClient) sendGetPromotionPropertyByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPromotionPropertyByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewGetPromotionPropertyByIdsArgs()
	args28.Header = header
	args28.Ids = ids
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvGetPromotionPropertyByIds() (value map[int32]*dpm_types.PromotionProperty, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewGetPromotionPropertyByIdsResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.E != nil {
		e = result29.E
	}
	return
}

// Parameters:
//  - Header
//  - Channel
func (p *DpmServerClient) AddChannel(header *common.RequestHeader, channel *dpm_types.Channel) (r int32, e *DpmServerException, err error) {
	if err = p.sendAddChannel(header, channel); err != nil {
		return
	}
	return p.recvAddChannel()
}

func (p *DpmServerClient) sendAddChannel(header *common.RequestHeader, channel *dpm_types.Channel) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addChannel", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewAddChannelArgs()
	args32.Header = header
	args32.Channel = channel
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvAddChannel() (value int32, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewAddChannelResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.E != nil {
		e = result33.E
	}
	return
}

// Parameters:
//  - Header
//  - ChannelId
//  - Channel
func (p *DpmServerClient) EditChannel(header *common.RequestHeader, channelId int32, channel *dpm_types.Channel) (e *DpmServerException, err error) {
	if err = p.sendEditChannel(header, channelId, channel); err != nil {
		return
	}
	return p.recvEditChannel()
}

func (p *DpmServerClient) sendEditChannel(header *common.RequestHeader, channelId int32, channel *dpm_types.Channel) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editChannel", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewEditChannelArgs()
	args36.Header = header
	args36.ChannelId = channelId
	args36.Channel = channel
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvEditChannel() (e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewEditChannelResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result37.E != nil {
		e = result37.E
	}
	return
}

// Parameters:
//  - Header
//  - Param
func (p *DpmServerClient) SearchChannelByParam(header *common.RequestHeader, param *dpm_types.ChannelParam) (r *common.QueryResult, e *DpmServerException, err error) {
	if err = p.sendSearchChannelByParam(header, param); err != nil {
		return
	}
	return p.recvSearchChannelByParam()
}

func (p *DpmServerClient) sendSearchChannelByParam(header *common.RequestHeader, param *dpm_types.ChannelParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchChannelByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewSearchChannelByParamArgs()
	args40.Header = header
	args40.Param = param
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvSearchChannelByParam() (value *common.QueryResult, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewSearchChannelByParamResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.E != nil {
		e = result41.E
	}
	return
}

// Parameters:
//  - Header
//  - Ids
func (p *DpmServerClient) GetChannelByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Channel, e *DpmServerException, err error) {
	if err = p.sendGetChannelByIds(header, ids); err != nil {
		return
	}
	return p.recvGetChannelByIds()
}

func (p *DpmServerClient) sendGetChannelByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getChannelByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetChannelByIdsArgs()
	args44.Header = header
	args44.Ids = ids
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvGetChannelByIds() (value map[int32]*dpm_types.Channel, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetChannelByIdsResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.E != nil {
		e = result45.E
	}
	return
}

// Parameters:
//  - Header
//  - Tracking
func (p *DpmServerClient) AddTracking(header *common.RequestHeader, tracking *dpm_types.Tracking) (r int32, e *DpmServerException, err error) {
	if err = p.sendAddTracking(header, tracking); err != nil {
		return
	}
	return p.recvAddTracking()
}

func (p *DpmServerClient) sendAddTracking(header *common.RequestHeader, tracking *dpm_types.Tracking) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addTracking", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewAddTrackingArgs()
	args48.Header = header
	args48.Tracking = tracking
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvAddTracking() (value int32, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewAddTrackingResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.E != nil {
		e = result49.E
	}
	return
}

// Parameters:
//  - Header
//  - TrackingId
//  - Tracking
func (p *DpmServerClient) EditTracking(header *common.RequestHeader, trackingId int32, tracking *dpm_types.Tracking) (e *DpmServerException, err error) {
	if err = p.sendEditTracking(header, trackingId, tracking); err != nil {
		return
	}
	return p.recvEditTracking()
}

func (p *DpmServerClient) sendEditTracking(header *common.RequestHeader, trackingId int32, tracking *dpm_types.Tracking) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editTracking", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewEditTrackingArgs()
	args52.Header = header
	args52.TrackingId = trackingId
	args52.Tracking = tracking
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvEditTracking() (e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewEditTrackingResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result53.E != nil {
		e = result53.E
	}
	return
}

// Parameters:
//  - Header
//  - Param
func (p *DpmServerClient) SearchTrackingByParam(header *common.RequestHeader, param *dpm_types.TrackingParam) (r *common.QueryResult, e *DpmServerException, err error) {
	if err = p.sendSearchTrackingByParam(header, param); err != nil {
		return
	}
	return p.recvSearchTrackingByParam()
}

func (p *DpmServerClient) sendSearchTrackingByParam(header *common.RequestHeader, param *dpm_types.TrackingParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchTrackingByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewSearchTrackingByParamArgs()
	args56.Header = header
	args56.Param = param
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvSearchTrackingByParam() (value *common.QueryResult, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewSearchTrackingByParamResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.E != nil {
		e = result57.E
	}
	return
}

// Parameters:
//  - Header
//  - Ids
func (p *DpmServerClient) GetTrackingByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Tracking, e *DpmServerException, err error) {
	if err = p.sendGetTrackingByIds(header, ids); err != nil {
		return
	}
	return p.recvGetTrackingByIds()
}

func (p *DpmServerClient) sendGetTrackingByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTrackingByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewGetTrackingByIdsArgs()
	args60.Header = header
	args60.Ids = ids
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvGetTrackingByIds() (value map[int32]*dpm_types.Tracking, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewGetTrackingByIdsResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result61.Success
	if result61.E != nil {
		e = result61.E
	}
	return
}

// Parameters:
//  - Header
//  - Company
func (p *DpmServerClient) AddCompany(header *common.RequestHeader, company *dpm_types.Company) (r int32, e *DpmServerException, err error) {
	if err = p.sendAddCompany(header, company); err != nil {
		return
	}
	return p.recvAddCompany()
}

func (p *DpmServerClient) sendAddCompany(header *common.RequestHeader, company *dpm_types.Company) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addCompany", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewAddCompanyArgs()
	args64.Header = header
	args64.Company = company
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvAddCompany() (value int32, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewAddCompanyResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.E != nil {
		e = result65.E
	}
	return
}

// Parameters:
//  - Header
//  - CompanyId
//  - Company
func (p *DpmServerClient) EditCompany(header *common.RequestHeader, companyId int32, company *dpm_types.Company) (e *DpmServerException, err error) {
	if err = p.sendEditCompany(header, companyId, company); err != nil {
		return
	}
	return p.recvEditCompany()
}

func (p *DpmServerClient) sendEditCompany(header *common.RequestHeader, companyId int32, company *dpm_types.Company) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editCompany", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args68 := NewEditCompanyArgs()
	args68.Header = header
	args68.CompanyId = companyId
	args68.Company = company
	if err = args68.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvEditCompany() (e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error70 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error71 error
		error71, err = error70.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error71
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result69 := NewEditCompanyResult()
	if err = result69.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result69.E != nil {
		e = result69.E
	}
	return
}

// Parameters:
//  - Header
//  - Param
func (p *DpmServerClient) SearchCompanyByParam(header *common.RequestHeader, param *dpm_types.CompanyParam) (r *common.QueryResult, e *DpmServerException, err error) {
	if err = p.sendSearchCompanyByParam(header, param); err != nil {
		return
	}
	return p.recvSearchCompanyByParam()
}

func (p *DpmServerClient) sendSearchCompanyByParam(header *common.RequestHeader, param *dpm_types.CompanyParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchCompanyByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args72 := NewSearchCompanyByParamArgs()
	args72.Header = header
	args72.Param = param
	if err = args72.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvSearchCompanyByParam() (value *common.QueryResult, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error74 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error75 error
		error75, err = error74.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error75
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result73 := NewSearchCompanyByParamResult()
	if err = result73.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result73.Success
	if result73.E != nil {
		e = result73.E
	}
	return
}

// Parameters:
//  - Header
//  - Ids
func (p *DpmServerClient) GetCompanyByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dpm_types.Company, e *DpmServerException, err error) {
	if err = p.sendGetCompanyByIds(header, ids); err != nil {
		return
	}
	return p.recvGetCompanyByIds()
}

func (p *DpmServerClient) sendGetCompanyByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCompanyByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args76 := NewGetCompanyByIdsArgs()
	args76.Header = header
	args76.Ids = ids
	if err = args76.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DpmServerClient) recvGetCompanyByIds() (value map[int32]*dpm_types.Company, e *DpmServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error78 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error79 error
		error79, err = error78.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error79
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result77 := NewGetCompanyByIdsResult()
	if err = result77.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result77.Success
	if result77.E != nil {
		e = result77.E
	}
	return
}

type DpmServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DpmServer
}

func (p *DpmServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DpmServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DpmServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDpmServerProcessor(handler DpmServer) *DpmServerProcessor {

	self80 := &DpmServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self80.processorMap["addPromotion"] = &dpmServerProcessorAddPromotion{handler: handler}
	self80.processorMap["editPromotion"] = &dpmServerProcessorEditPromotion{handler: handler}
	self80.processorMap["searchPromotionByParam"] = &dpmServerProcessorSearchPromotionByParam{handler: handler}
	self80.processorMap["getPromotionByIds"] = &dpmServerProcessorGetPromotionByIds{handler: handler}
	self80.processorMap["addPromotionProperty"] = &dpmServerProcessorAddPromotionProperty{handler: handler}
	self80.processorMap["editPromotionProperty"] = &dpmServerProcessorEditPromotionProperty{handler: handler}
	self80.processorMap["searchPromotionPropertyByParam"] = &dpmServerProcessorSearchPromotionPropertyByParam{handler: handler}
	self80.processorMap["getPromotionPropertyByIds"] = &dpmServerProcessorGetPromotionPropertyByIds{handler: handler}
	self80.processorMap["addChannel"] = &dpmServerProcessorAddChannel{handler: handler}
	self80.processorMap["editChannel"] = &dpmServerProcessorEditChannel{handler: handler}
	self80.processorMap["searchChannelByParam"] = &dpmServerProcessorSearchChannelByParam{handler: handler}
	self80.processorMap["getChannelByIds"] = &dpmServerProcessorGetChannelByIds{handler: handler}
	self80.processorMap["addTracking"] = &dpmServerProcessorAddTracking{handler: handler}
	self80.processorMap["editTracking"] = &dpmServerProcessorEditTracking{handler: handler}
	self80.processorMap["searchTrackingByParam"] = &dpmServerProcessorSearchTrackingByParam{handler: handler}
	self80.processorMap["getTrackingByIds"] = &dpmServerProcessorGetTrackingByIds{handler: handler}
	self80.processorMap["addCompany"] = &dpmServerProcessorAddCompany{handler: handler}
	self80.processorMap["editCompany"] = &dpmServerProcessorEditCompany{handler: handler}
	self80.processorMap["searchCompanyByParam"] = &dpmServerProcessorSearchCompanyByParam{handler: handler}
	self80.processorMap["getCompanyByIds"] = &dpmServerProcessorGetCompanyByIds{handler: handler}
	return self80
}

func (p *DpmServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x81 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x81.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x81

}

type dpmServerProcessorAddPromotion struct {
	handler DpmServer
}

func (p *dpmServerProcessorAddPromotion) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddPromotionArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addPromotion", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddPromotionResult()
	if result.Success, result.E, err = p.handler.AddPromotion(args.Header, args.Promotion); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addPromotion: "+err.Error())
		oprot.WriteMessageBegin("addPromotion", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addPromotion", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorEditPromotion struct {
	handler DpmServer
}

func (p *dpmServerProcessorEditPromotion) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditPromotionArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editPromotion", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditPromotionResult()
	if result.E, err = p.handler.EditPromotion(args.Header, args.PromotionId, args.Promotion); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editPromotion: "+err.Error())
		oprot.WriteMessageBegin("editPromotion", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editPromotion", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorSearchPromotionByParam struct {
	handler DpmServer
}

func (p *dpmServerProcessorSearchPromotionByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchPromotionByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchPromotionByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchPromotionByParamResult()
	if result.Success, result.E, err = p.handler.SearchPromotionByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchPromotionByParam: "+err.Error())
		oprot.WriteMessageBegin("searchPromotionByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchPromotionByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorGetPromotionByIds struct {
	handler DpmServer
}

func (p *dpmServerProcessorGetPromotionByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPromotionByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPromotionByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPromotionByIdsResult()
	if result.Success, result.E, err = p.handler.GetPromotionByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPromotionByIds: "+err.Error())
		oprot.WriteMessageBegin("getPromotionByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPromotionByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorAddPromotionProperty struct {
	handler DpmServer
}

func (p *dpmServerProcessorAddPromotionProperty) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddPromotionPropertyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addPromotionProperty", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddPromotionPropertyResult()
	if result.Success, result.E, err = p.handler.AddPromotionProperty(args.Header, args.PromotionProperty); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addPromotionProperty: "+err.Error())
		oprot.WriteMessageBegin("addPromotionProperty", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addPromotionProperty", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorEditPromotionProperty struct {
	handler DpmServer
}

func (p *dpmServerProcessorEditPromotionProperty) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditPromotionPropertyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editPromotionProperty", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditPromotionPropertyResult()
	if result.E, err = p.handler.EditPromotionProperty(args.Header, args.PromotionPropertyId, args.PromotionProperty); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editPromotionProperty: "+err.Error())
		oprot.WriteMessageBegin("editPromotionProperty", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editPromotionProperty", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorSearchPromotionPropertyByParam struct {
	handler DpmServer
}

func (p *dpmServerProcessorSearchPromotionPropertyByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchPromotionPropertyByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchPromotionPropertyByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchPromotionPropertyByParamResult()
	if result.Success, result.E, err = p.handler.SearchPromotionPropertyByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchPromotionPropertyByParam: "+err.Error())
		oprot.WriteMessageBegin("searchPromotionPropertyByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchPromotionPropertyByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorGetPromotionPropertyByIds struct {
	handler DpmServer
}

func (p *dpmServerProcessorGetPromotionPropertyByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPromotionPropertyByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPromotionPropertyByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPromotionPropertyByIdsResult()
	if result.Success, result.E, err = p.handler.GetPromotionPropertyByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPromotionPropertyByIds: "+err.Error())
		oprot.WriteMessageBegin("getPromotionPropertyByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPromotionPropertyByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorAddChannel struct {
	handler DpmServer
}

func (p *dpmServerProcessorAddChannel) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddChannelArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddChannelResult()
	if result.Success, result.E, err = p.handler.AddChannel(args.Header, args.Channel); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addChannel: "+err.Error())
		oprot.WriteMessageBegin("addChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addChannel", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorEditChannel struct {
	handler DpmServer
}

func (p *dpmServerProcessorEditChannel) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditChannelArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditChannelResult()
	if result.E, err = p.handler.EditChannel(args.Header, args.ChannelId, args.Channel); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editChannel: "+err.Error())
		oprot.WriteMessageBegin("editChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editChannel", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorSearchChannelByParam struct {
	handler DpmServer
}

func (p *dpmServerProcessorSearchChannelByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchChannelByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchChannelByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchChannelByParamResult()
	if result.Success, result.E, err = p.handler.SearchChannelByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchChannelByParam: "+err.Error())
		oprot.WriteMessageBegin("searchChannelByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchChannelByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorGetChannelByIds struct {
	handler DpmServer
}

func (p *dpmServerProcessorGetChannelByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetChannelByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getChannelByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetChannelByIdsResult()
	if result.Success, result.E, err = p.handler.GetChannelByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getChannelByIds: "+err.Error())
		oprot.WriteMessageBegin("getChannelByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getChannelByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorAddTracking struct {
	handler DpmServer
}

func (p *dpmServerProcessorAddTracking) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddTrackingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addTracking", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddTrackingResult()
	if result.Success, result.E, err = p.handler.AddTracking(args.Header, args.Tracking); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addTracking: "+err.Error())
		oprot.WriteMessageBegin("addTracking", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addTracking", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorEditTracking struct {
	handler DpmServer
}

func (p *dpmServerProcessorEditTracking) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditTrackingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editTracking", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditTrackingResult()
	if result.E, err = p.handler.EditTracking(args.Header, args.TrackingId, args.Tracking); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editTracking: "+err.Error())
		oprot.WriteMessageBegin("editTracking", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editTracking", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorSearchTrackingByParam struct {
	handler DpmServer
}

func (p *dpmServerProcessorSearchTrackingByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchTrackingByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchTrackingByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchTrackingByParamResult()
	if result.Success, result.E, err = p.handler.SearchTrackingByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchTrackingByParam: "+err.Error())
		oprot.WriteMessageBegin("searchTrackingByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchTrackingByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorGetTrackingByIds struct {
	handler DpmServer
}

func (p *dpmServerProcessorGetTrackingByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTrackingByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTrackingByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTrackingByIdsResult()
	if result.Success, result.E, err = p.handler.GetTrackingByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTrackingByIds: "+err.Error())
		oprot.WriteMessageBegin("getTrackingByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTrackingByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorAddCompany struct {
	handler DpmServer
}

func (p *dpmServerProcessorAddCompany) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCompanyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addCompany", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCompanyResult()
	if result.Success, result.E, err = p.handler.AddCompany(args.Header, args.Company); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addCompany: "+err.Error())
		oprot.WriteMessageBegin("addCompany", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addCompany", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorEditCompany struct {
	handler DpmServer
}

func (p *dpmServerProcessorEditCompany) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditCompanyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editCompany", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditCompanyResult()
	if result.E, err = p.handler.EditCompany(args.Header, args.CompanyId, args.Company); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editCompany: "+err.Error())
		oprot.WriteMessageBegin("editCompany", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editCompany", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorSearchCompanyByParam struct {
	handler DpmServer
}

func (p *dpmServerProcessorSearchCompanyByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchCompanyByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchCompanyByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchCompanyByParamResult()
	if result.Success, result.E, err = p.handler.SearchCompanyByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchCompanyByParam: "+err.Error())
		oprot.WriteMessageBegin("searchCompanyByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchCompanyByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dpmServerProcessorGetCompanyByIds struct {
	handler DpmServer
}

func (p *dpmServerProcessorGetCompanyByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCompanyByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCompanyByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCompanyByIdsResult()
	if result.Success, result.E, err = p.handler.GetCompanyByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCompanyByIds: "+err.Error())
		oprot.WriteMessageBegin("getCompanyByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCompanyByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddPromotionArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Promotion *dpm_types.Promotion  `thrift:"promotion,2" json:"promotion"`
}

func NewAddPromotionArgs() *AddPromotionArgs {
	return &AddPromotionArgs{}
}

func (p *AddPromotionArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddPromotionArgs) readField2(iprot thrift.TProtocol) error {
	p.Promotion = dpm_types.NewPromotion()
	if err := p.Promotion.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Promotion)
	}
	return nil
}

func (p *AddPromotionArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPromotion_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddPromotionArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Promotion != nil {
		if err := oprot.WriteFieldBegin("promotion", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:promotion: %s", p, err)
		}
		if err := p.Promotion.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Promotion)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:promotion: %s", p, err)
		}
	}
	return err
}

func (p *AddPromotionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPromotionArgs(%+v)", *p)
}

type AddPromotionResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewAddPromotionResult() *AddPromotionResult {
	return &AddPromotionResult{}
}

func (p *AddPromotionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddPromotionResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddPromotionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPromotion_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddPromotionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddPromotionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPromotionResult(%+v)", *p)
}

type EditPromotionArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	PromotionId int32                 `thrift:"promotionId,2" json:"promotionId"`
	Promotion   *dpm_types.Promotion  `thrift:"promotion,3" json:"promotion"`
}

func NewEditPromotionArgs() *EditPromotionArgs {
	return &EditPromotionArgs{}
}

func (p *EditPromotionArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditPromotionArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *EditPromotionArgs) readField3(iprot thrift.TProtocol) error {
	p.Promotion = dpm_types.NewPromotion()
	if err := p.Promotion.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Promotion)
	}
	return nil
}

func (p *EditPromotionArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPromotion_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditPromotionArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:promotionId: %s", p, err)
	}
	return err
}

func (p *EditPromotionArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Promotion != nil {
		if err := oprot.WriteFieldBegin("promotion", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:promotion: %s", p, err)
		}
		if err := p.Promotion.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Promotion)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:promotion: %s", p, err)
		}
	}
	return err
}

func (p *EditPromotionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPromotionArgs(%+v)", *p)
}

type EditPromotionResult struct {
	E *DpmServerException `thrift:"e,1" json:"e"`
}

func NewEditPromotionResult() *EditPromotionResult {
	return &EditPromotionResult{}
}

func (p *EditPromotionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditPromotionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPromotion_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditPromotionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPromotionResult(%+v)", *p)
}

type SearchPromotionByParamArgs struct {
	Header *common.RequestHeader     `thrift:"header,1" json:"header"`
	Param  *dpm_types.PromotionParam `thrift:"param,2" json:"param"`
}

func NewSearchPromotionByParamArgs() *SearchPromotionByParamArgs {
	return &SearchPromotionByParamArgs{}
}

func (p *SearchPromotionByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchPromotionByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dpm_types.NewPromotionParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *SearchPromotionByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchPromotionByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchPromotionByParamArgs(%+v)", *p)
}

type SearchPromotionByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewSearchPromotionByParamResult() *SearchPromotionByParamResult {
	return &SearchPromotionByParamResult{}
}

func (p *SearchPromotionByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchPromotionByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchPromotionByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchPromotionByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchPromotionByParamResult(%+v)", *p)
}

type GetPromotionByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetPromotionByIdsArgs() *GetPromotionByIdsArgs {
	return &GetPromotionByIdsArgs{}
}

func (p *GetPromotionByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPromotionByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem82 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem82 = v
		}
		p.Ids = append(p.Ids, _elem82)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPromotionByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPromotionByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPromotionByIdsArgs(%+v)", *p)
}

type GetPromotionByIdsResult struct {
	Success map[int32]*dpm_types.Promotion `thrift:"success,0" json:"success"`
	E       *DpmServerException            `thrift:"e,1" json:"e"`
}

func NewGetPromotionByIdsResult() *GetPromotionByIdsResult {
	return &GetPromotionByIdsResult{}
}

func (p *GetPromotionByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dpm_types.Promotion, size)
	for i := 0; i < size; i++ {
		var _key83 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key83 = v
		}
		_val84 := dpm_types.NewPromotion()
		if err := _val84.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val84)
		}
		p.Success[_key83] = _val84
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetPromotionByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetPromotionByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPromotionByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPromotionByIdsResult(%+v)", *p)
}

type AddPromotionPropertyArgs struct {
	Header            *common.RequestHeader        `thrift:"header,1" json:"header"`
	PromotionProperty *dpm_types.PromotionProperty `thrift:"promotionProperty,2" json:"promotionProperty"`
}

func NewAddPromotionPropertyArgs() *AddPromotionPropertyArgs {
	return &AddPromotionPropertyArgs{}
}

func (p *AddPromotionPropertyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionPropertyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddPromotionPropertyArgs) readField2(iprot thrift.TProtocol) error {
	p.PromotionProperty = dpm_types.NewPromotionProperty()
	if err := p.PromotionProperty.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PromotionProperty)
	}
	return nil
}

func (p *AddPromotionPropertyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPromotionProperty_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionPropertyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddPromotionPropertyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.PromotionProperty != nil {
		if err := oprot.WriteFieldBegin("promotionProperty", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:promotionProperty: %s", p, err)
		}
		if err := p.PromotionProperty.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PromotionProperty)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:promotionProperty: %s", p, err)
		}
	}
	return err
}

func (p *AddPromotionPropertyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPromotionPropertyArgs(%+v)", *p)
}

type AddPromotionPropertyResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewAddPromotionPropertyResult() *AddPromotionPropertyResult {
	return &AddPromotionPropertyResult{}
}

func (p *AddPromotionPropertyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionPropertyResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddPromotionPropertyResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddPromotionPropertyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPromotionProperty_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPromotionPropertyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddPromotionPropertyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddPromotionPropertyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPromotionPropertyResult(%+v)", *p)
}

type EditPromotionPropertyArgs struct {
	Header              *common.RequestHeader        `thrift:"header,1" json:"header"`
	PromotionPropertyId int32                        `thrift:"promotionPropertyId,2" json:"promotionPropertyId"`
	PromotionProperty   *dpm_types.PromotionProperty `thrift:"promotionProperty,3" json:"promotionProperty"`
}

func NewEditPromotionPropertyArgs() *EditPromotionPropertyArgs {
	return &EditPromotionPropertyArgs{}
}

func (p *EditPromotionPropertyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionPropertyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditPromotionPropertyArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PromotionPropertyId = v
	}
	return nil
}

func (p *EditPromotionPropertyArgs) readField3(iprot thrift.TProtocol) error {
	p.PromotionProperty = dpm_types.NewPromotionProperty()
	if err := p.PromotionProperty.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PromotionProperty)
	}
	return nil
}

func (p *EditPromotionPropertyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPromotionProperty_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionPropertyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditPromotionPropertyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionPropertyId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:promotionPropertyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionPropertyId)); err != nil {
		return fmt.Errorf("%T.promotionPropertyId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:promotionPropertyId: %s", p, err)
	}
	return err
}

func (p *EditPromotionPropertyArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.PromotionProperty != nil {
		if err := oprot.WriteFieldBegin("promotionProperty", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:promotionProperty: %s", p, err)
		}
		if err := p.PromotionProperty.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PromotionProperty)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:promotionProperty: %s", p, err)
		}
	}
	return err
}

func (p *EditPromotionPropertyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPromotionPropertyArgs(%+v)", *p)
}

type EditPromotionPropertyResult struct {
	E *DpmServerException `thrift:"e,1" json:"e"`
}

func NewEditPromotionPropertyResult() *EditPromotionPropertyResult {
	return &EditPromotionPropertyResult{}
}

func (p *EditPromotionPropertyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionPropertyResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditPromotionPropertyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPromotionProperty_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPromotionPropertyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditPromotionPropertyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPromotionPropertyResult(%+v)", *p)
}

type SearchPromotionPropertyByParamArgs struct {
	Header *common.RequestHeader             `thrift:"header,1" json:"header"`
	Param  *dpm_types.PromotionPropertyParam `thrift:"param,2" json:"param"`
}

func NewSearchPromotionPropertyByParamArgs() *SearchPromotionPropertyByParamArgs {
	return &SearchPromotionPropertyByParamArgs{}
}

func (p *SearchPromotionPropertyByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dpm_types.NewPromotionPropertyParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchPromotionPropertyByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionPropertyByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionPropertyByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchPromotionPropertyByParamArgs(%+v)", *p)
}

type SearchPromotionPropertyByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewSearchPromotionPropertyByParamResult() *SearchPromotionPropertyByParamResult {
	return &SearchPromotionPropertyByParamResult{}
}

func (p *SearchPromotionPropertyByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchPromotionPropertyByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchPromotionPropertyByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionPropertyByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchPromotionPropertyByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchPromotionPropertyByParamResult(%+v)", *p)
}

type GetPromotionPropertyByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetPromotionPropertyByIdsArgs() *GetPromotionPropertyByIdsArgs {
	return &GetPromotionPropertyByIdsArgs{}
}

func (p *GetPromotionPropertyByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem85 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem85 = v
		}
		p.Ids = append(p.Ids, _elem85)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPromotionPropertyByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionPropertyByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionPropertyByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPromotionPropertyByIdsArgs(%+v)", *p)
}

type GetPromotionPropertyByIdsResult struct {
	Success map[int32]*dpm_types.PromotionProperty `thrift:"success,0" json:"success"`
	E       *DpmServerException                    `thrift:"e,1" json:"e"`
}

func NewGetPromotionPropertyByIdsResult() *GetPromotionPropertyByIdsResult {
	return &GetPromotionPropertyByIdsResult{}
}

func (p *GetPromotionPropertyByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dpm_types.PromotionProperty, size)
	for i := 0; i < size; i++ {
		var _key86 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key86 = v
		}
		_val87 := dpm_types.NewPromotionProperty()
		if err := _val87.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val87)
		}
		p.Success[_key86] = _val87
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPromotionPropertyByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPromotionPropertyByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionPropertyByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetPromotionPropertyByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPromotionPropertyByIdsResult(%+v)", *p)
}

type AddChannelArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	Channel *dpm_types.Channel    `thrift:"channel,2" json:"channel"`
}

func NewAddChannelArgs() *AddChannelArgs {
	return &AddChannelArgs{}
}

func (p *AddChannelArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddChannelArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddChannelArgs) readField2(iprot thrift.TProtocol) error {
	p.Channel = dpm_types.NewChannel()
	if err := p.Channel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Channel)
	}
	return nil
}

func (p *AddChannelArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addChannel_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddChannelArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddChannelArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Channel != nil {
		if err := oprot.WriteFieldBegin("channel", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:channel: %s", p, err)
		}
		if err := p.Channel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Channel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:channel: %s", p, err)
		}
	}
	return err
}

func (p *AddChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddChannelArgs(%+v)", *p)
}

type AddChannelResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewAddChannelResult() *AddChannelResult {
	return &AddChannelResult{}
}

func (p *AddChannelResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddChannelResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddChannelResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddChannelResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addChannel_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddChannelResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddChannelResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddChannelResult(%+v)", *p)
}

type EditChannelArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	ChannelId int32                 `thrift:"channelId,2" json:"channelId"`
	Channel   *dpm_types.Channel    `thrift:"channel,3" json:"channel"`
}

func NewEditChannelArgs() *EditChannelArgs {
	return &EditChannelArgs{}
}

func (p *EditChannelArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditChannelArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditChannelArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *EditChannelArgs) readField3(iprot thrift.TProtocol) error {
	p.Channel = dpm_types.NewChannel()
	if err := p.Channel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Channel)
	}
	return nil
}

func (p *EditChannelArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editChannel_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditChannelArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditChannelArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:channelId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channelId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:channelId: %s", p, err)
	}
	return err
}

func (p *EditChannelArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Channel != nil {
		if err := oprot.WriteFieldBegin("channel", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:channel: %s", p, err)
		}
		if err := p.Channel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Channel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:channel: %s", p, err)
		}
	}
	return err
}

func (p *EditChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditChannelArgs(%+v)", *p)
}

type EditChannelResult struct {
	E *DpmServerException `thrift:"e,1" json:"e"`
}

func NewEditChannelResult() *EditChannelResult {
	return &EditChannelResult{}
}

func (p *EditChannelResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditChannelResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditChannelResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editChannel_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditChannelResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditChannelResult(%+v)", *p)
}

type SearchChannelByParamArgs struct {
	Header *common.RequestHeader   `thrift:"header,1" json:"header"`
	Param  *dpm_types.ChannelParam `thrift:"param,2" json:"param"`
}

func NewSearchChannelByParamArgs() *SearchChannelByParamArgs {
	return &SearchChannelByParamArgs{}
}

func (p *SearchChannelByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchChannelByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchChannelByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dpm_types.NewChannelParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *SearchChannelByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchChannelByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchChannelByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchChannelByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *SearchChannelByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchChannelByParamArgs(%+v)", *p)
}

type SearchChannelByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewSearchChannelByParamResult() *SearchChannelByParamResult {
	return &SearchChannelByParamResult{}
}

func (p *SearchChannelByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchChannelByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchChannelByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchChannelByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchChannelByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchChannelByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchChannelByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchChannelByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchChannelByParamResult(%+v)", *p)
}

type GetChannelByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetChannelByIdsArgs() *GetChannelByIdsArgs {
	return &GetChannelByIdsArgs{}
}

func (p *GetChannelByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetChannelByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetChannelByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem88 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem88 = v
		}
		p.Ids = append(p.Ids, _elem88)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetChannelByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getChannelByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetChannelByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetChannelByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetChannelByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetChannelByIdsArgs(%+v)", *p)
}

type GetChannelByIdsResult struct {
	Success map[int32]*dpm_types.Channel `thrift:"success,0" json:"success"`
	E       *DpmServerException          `thrift:"e,1" json:"e"`
}

func NewGetChannelByIdsResult() *GetChannelByIdsResult {
	return &GetChannelByIdsResult{}
}

func (p *GetChannelByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetChannelByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dpm_types.Channel, size)
	for i := 0; i < size; i++ {
		var _key89 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key89 = v
		}
		_val90 := dpm_types.NewChannel()
		if err := _val90.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val90)
		}
		p.Success[_key89] = _val90
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetChannelByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetChannelByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getChannelByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetChannelByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetChannelByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetChannelByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetChannelByIdsResult(%+v)", *p)
}

type AddTrackingArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Tracking *dpm_types.Tracking   `thrift:"tracking,2" json:"tracking"`
}

func NewAddTrackingArgs() *AddTrackingArgs {
	return &AddTrackingArgs{}
}

func (p *AddTrackingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTrackingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTrackingArgs) readField2(iprot thrift.TProtocol) error {
	p.Tracking = dpm_types.NewTracking()
	if err := p.Tracking.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Tracking)
	}
	return nil
}

func (p *AddTrackingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTracking_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTrackingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTrackingArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Tracking != nil {
		if err := oprot.WriteFieldBegin("tracking", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:tracking: %s", p, err)
		}
		if err := p.Tracking.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Tracking)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:tracking: %s", p, err)
		}
	}
	return err
}

func (p *AddTrackingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTrackingArgs(%+v)", *p)
}

type AddTrackingResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewAddTrackingResult() *AddTrackingResult {
	return &AddTrackingResult{}
}

func (p *AddTrackingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTrackingResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddTrackingResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddTrackingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTracking_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTrackingResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddTrackingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddTrackingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTrackingResult(%+v)", *p)
}

type EditTrackingArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	TrackingId int32                 `thrift:"trackingId,2" json:"trackingId"`
	Tracking   *dpm_types.Tracking   `thrift:"tracking,3" json:"tracking"`
}

func NewEditTrackingArgs() *EditTrackingArgs {
	return &EditTrackingArgs{}
}

func (p *EditTrackingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditTrackingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditTrackingArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TrackingId = v
	}
	return nil
}

func (p *EditTrackingArgs) readField3(iprot thrift.TProtocol) error {
	p.Tracking = dpm_types.NewTracking()
	if err := p.Tracking.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Tracking)
	}
	return nil
}

func (p *EditTrackingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editTracking_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditTrackingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditTrackingArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:trackingId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TrackingId)); err != nil {
		return fmt.Errorf("%T.trackingId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:trackingId: %s", p, err)
	}
	return err
}

func (p *EditTrackingArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Tracking != nil {
		if err := oprot.WriteFieldBegin("tracking", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:tracking: %s", p, err)
		}
		if err := p.Tracking.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Tracking)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:tracking: %s", p, err)
		}
	}
	return err
}

func (p *EditTrackingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditTrackingArgs(%+v)", *p)
}

type EditTrackingResult struct {
	E *DpmServerException `thrift:"e,1" json:"e"`
}

func NewEditTrackingResult() *EditTrackingResult {
	return &EditTrackingResult{}
}

func (p *EditTrackingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditTrackingResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditTrackingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editTracking_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditTrackingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditTrackingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditTrackingResult(%+v)", *p)
}

type SearchTrackingByParamArgs struct {
	Header *common.RequestHeader    `thrift:"header,1" json:"header"`
	Param  *dpm_types.TrackingParam `thrift:"param,2" json:"param"`
}

func NewSearchTrackingByParamArgs() *SearchTrackingByParamArgs {
	return &SearchTrackingByParamArgs{}
}

func (p *SearchTrackingByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchTrackingByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchTrackingByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dpm_types.NewTrackingParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *SearchTrackingByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchTrackingByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchTrackingByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchTrackingByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *SearchTrackingByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchTrackingByParamArgs(%+v)", *p)
}

type SearchTrackingByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewSearchTrackingByParamResult() *SearchTrackingByParamResult {
	return &SearchTrackingByParamResult{}
}

func (p *SearchTrackingByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchTrackingByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchTrackingByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchTrackingByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchTrackingByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchTrackingByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchTrackingByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchTrackingByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchTrackingByParamResult(%+v)", *p)
}

type GetTrackingByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetTrackingByIdsArgs() *GetTrackingByIdsArgs {
	return &GetTrackingByIdsArgs{}
}

func (p *GetTrackingByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTrackingByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTrackingByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem91 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem91 = v
		}
		p.Ids = append(p.Ids, _elem91)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTrackingByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTrackingByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTrackingByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTrackingByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetTrackingByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTrackingByIdsArgs(%+v)", *p)
}

type GetTrackingByIdsResult struct {
	Success map[int32]*dpm_types.Tracking `thrift:"success,0" json:"success"`
	E       *DpmServerException           `thrift:"e,1" json:"e"`
}

func NewGetTrackingByIdsResult() *GetTrackingByIdsResult {
	return &GetTrackingByIdsResult{}
}

func (p *GetTrackingByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTrackingByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dpm_types.Tracking, size)
	for i := 0; i < size; i++ {
		var _key92 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key92 = v
		}
		_val93 := dpm_types.NewTracking()
		if err := _val93.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val93)
		}
		p.Success[_key92] = _val93
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetTrackingByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetTrackingByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTrackingByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTrackingByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTrackingByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetTrackingByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTrackingByIdsResult(%+v)", *p)
}

type AddCompanyArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	Company *dpm_types.Company    `thrift:"company,2" json:"company"`
}

func NewAddCompanyArgs() *AddCompanyArgs {
	return &AddCompanyArgs{}
}

func (p *AddCompanyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCompanyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddCompanyArgs) readField2(iprot thrift.TProtocol) error {
	p.Company = dpm_types.NewCompany()
	if err := p.Company.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Company)
	}
	return nil
}

func (p *AddCompanyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCompany_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCompanyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddCompanyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Company != nil {
		if err := oprot.WriteFieldBegin("company", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:company: %s", p, err)
		}
		if err := p.Company.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Company)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:company: %s", p, err)
		}
	}
	return err
}

func (p *AddCompanyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCompanyArgs(%+v)", *p)
}

type AddCompanyResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewAddCompanyResult() *AddCompanyResult {
	return &AddCompanyResult{}
}

func (p *AddCompanyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCompanyResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddCompanyResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddCompanyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCompany_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCompanyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddCompanyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddCompanyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCompanyResult(%+v)", *p)
}

type EditCompanyArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	CompanyId int32                 `thrift:"companyId,2" json:"companyId"`
	Company   *dpm_types.Company    `thrift:"company,3" json:"company"`
}

func NewEditCompanyArgs() *EditCompanyArgs {
	return &EditCompanyArgs{}
}

func (p *EditCompanyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCompanyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditCompanyArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CompanyId = v
	}
	return nil
}

func (p *EditCompanyArgs) readField3(iprot thrift.TProtocol) error {
	p.Company = dpm_types.NewCompany()
	if err := p.Company.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Company)
	}
	return nil
}

func (p *EditCompanyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCompany_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCompanyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditCompanyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:companyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CompanyId)); err != nil {
		return fmt.Errorf("%T.companyId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:companyId: %s", p, err)
	}
	return err
}

func (p *EditCompanyArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Company != nil {
		if err := oprot.WriteFieldBegin("company", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:company: %s", p, err)
		}
		if err := p.Company.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Company)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:company: %s", p, err)
		}
	}
	return err
}

func (p *EditCompanyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCompanyArgs(%+v)", *p)
}

type EditCompanyResult struct {
	E *DpmServerException `thrift:"e,1" json:"e"`
}

func NewEditCompanyResult() *EditCompanyResult {
	return &EditCompanyResult{}
}

func (p *EditCompanyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCompanyResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditCompanyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCompany_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCompanyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditCompanyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCompanyResult(%+v)", *p)
}

type SearchCompanyByParamArgs struct {
	Header *common.RequestHeader   `thrift:"header,1" json:"header"`
	Param  *dpm_types.CompanyParam `thrift:"param,2" json:"param"`
}

func NewSearchCompanyByParamArgs() *SearchCompanyByParamArgs {
	return &SearchCompanyByParamArgs{}
}

func (p *SearchCompanyByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchCompanyByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchCompanyByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dpm_types.NewCompanyParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *SearchCompanyByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchCompanyByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchCompanyByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchCompanyByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *SearchCompanyByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchCompanyByParamArgs(%+v)", *p)
}

type SearchCompanyByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DpmServerException `thrift:"e,1" json:"e"`
}

func NewSearchCompanyByParamResult() *SearchCompanyByParamResult {
	return &SearchCompanyByParamResult{}
}

func (p *SearchCompanyByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchCompanyByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchCompanyByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchCompanyByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchCompanyByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchCompanyByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchCompanyByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchCompanyByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchCompanyByParamResult(%+v)", *p)
}

type GetCompanyByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetCompanyByIdsArgs() *GetCompanyByIdsArgs {
	return &GetCompanyByIdsArgs{}
}

func (p *GetCompanyByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCompanyByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetCompanyByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem94 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem94 = v
		}
		p.Ids = append(p.Ids, _elem94)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetCompanyByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCompanyByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCompanyByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetCompanyByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetCompanyByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCompanyByIdsArgs(%+v)", *p)
}

type GetCompanyByIdsResult struct {
	Success map[int32]*dpm_types.Company `thrift:"success,0" json:"success"`
	E       *DpmServerException          `thrift:"e,1" json:"e"`
}

func NewGetCompanyByIdsResult() *GetCompanyByIdsResult {
	return &GetCompanyByIdsResult{}
}

func (p *GetCompanyByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCompanyByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dpm_types.Company, size)
	for i := 0; i < size; i++ {
		var _key95 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key95 = v
		}
		_val96 := dpm_types.NewCompany()
		if err := _val96.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val96)
		}
		p.Success[_key95] = _val96
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetCompanyByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDpmServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetCompanyByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCompanyByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCompanyByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCompanyByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetCompanyByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCompanyByIdsResult(%+v)", *p)
}
