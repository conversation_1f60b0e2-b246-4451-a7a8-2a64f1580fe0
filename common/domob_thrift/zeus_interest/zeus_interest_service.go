// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package zeus_interest

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/zeus_ad_types"
	"rtb_model_server/common/domob_thrift/zeus_common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = zeus_common.GoUnusedProtection__
var _ = zeus_ad_types.GoUnusedProtection__

type ZeusInterestService interface {
	dm303.DomobService

	// Parameters:
	//  - Header
	//  - Id
	Get(header *common.RequestHeader, id int32) (r string, e *zeus_common.ZeusException, err error)
	// Parameters:
	//  - Header
	//  - MainTagIds
	//  - OtherTagIds
	//  - TargetingSpec: json str,参考facebook targeting_spec
	//  - AccessToken
	//  - FbAccountId
	GetFbInterestIdsSuggestionByTags(header *common.RequestHeader, main_tag_ids []int64, other_tag_ids []int64, targeting_spec string, access_token string, fb_account_id int64) (r []int64, e *zeus_common.ZeusException, err error)
	// Parameters:
	//  - Header
	//  - MainTagIds
	//  - OtherTagIds
	//  - TargetingSpec: json str,参考facebook targeting_spec
	//  - AccessToken
	//  - FbAccountId
	GetInterestsSuggestionByTags(header *common.RequestHeader, main_tag_ids []int64, other_tag_ids []int64, targeting_spec string, access_token string, fb_account_id int64) (r []*zeus_ad_types.ZeusInterestWarehouse, e *zeus_common.ZeusException, err error)
}

type ZeusInterestServiceClient struct {
	*dm303.DomobServiceClient
}

func NewZeusInterestServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ZeusInterestServiceClient {
	return &ZeusInterestServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewZeusInterestServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ZeusInterestServiceClient {
	return &ZeusInterestServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Header
//  - Id
func (p *ZeusInterestServiceClient) Get(header *common.RequestHeader, id int32) (r string, e *zeus_common.ZeusException, err error) {
	if err = p.sendGet(header, id); err != nil {
		return
	}
	return p.recvGet()
}

func (p *ZeusInterestServiceClient) sendGet(header *common.RequestHeader, id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetArgs()
	args0.Header = header
	args0.Id = id
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ZeusInterestServiceClient) recvGet() (value string, e *zeus_common.ZeusException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// Parameters:
//  - Header
//  - MainTagIds
//  - OtherTagIds
//  - TargetingSpec: json str,参考facebook targeting_spec
//  - AccessToken
//  - FbAccountId
func (p *ZeusInterestServiceClient) GetFbInterestIdsSuggestionByTags(header *common.RequestHeader, main_tag_ids []int64, other_tag_ids []int64, targeting_spec string, access_token string, fb_account_id int64) (r []int64, e *zeus_common.ZeusException, err error) {
	if err = p.sendGetFbInterestIdsSuggestionByTags(header, main_tag_ids, other_tag_ids, targeting_spec, access_token, fb_account_id); err != nil {
		return
	}
	return p.recvGetFbInterestIdsSuggestionByTags()
}

func (p *ZeusInterestServiceClient) sendGetFbInterestIdsSuggestionByTags(header *common.RequestHeader, main_tag_ids []int64, other_tag_ids []int64, targeting_spec string, access_token string, fb_account_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_fb_interest_ids_suggestion_by_tags", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetFbInterestIdsSuggestionByTagsArgs()
	args4.Header = header
	args4.MainTagIds = main_tag_ids
	args4.OtherTagIds = other_tag_ids
	args4.TargetingSpec = targeting_spec
	args4.AccessToken = access_token
	args4.FbAccountId = fb_account_id
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ZeusInterestServiceClient) recvGetFbInterestIdsSuggestionByTags() (value []int64, e *zeus_common.ZeusException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetFbInterestIdsSuggestionByTagsResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

// Parameters:
//  - Header
//  - MainTagIds
//  - OtherTagIds
//  - TargetingSpec: json str,参考facebook targeting_spec
//  - AccessToken
//  - FbAccountId
func (p *ZeusInterestServiceClient) GetInterestsSuggestionByTags(header *common.RequestHeader, main_tag_ids []int64, other_tag_ids []int64, targeting_spec string, access_token string, fb_account_id int64) (r []*zeus_ad_types.ZeusInterestWarehouse, e *zeus_common.ZeusException, err error) {
	if err = p.sendGetInterestsSuggestionByTags(header, main_tag_ids, other_tag_ids, targeting_spec, access_token, fb_account_id); err != nil {
		return
	}
	return p.recvGetInterestsSuggestionByTags()
}

func (p *ZeusInterestServiceClient) sendGetInterestsSuggestionByTags(header *common.RequestHeader, main_tag_ids []int64, other_tag_ids []int64, targeting_spec string, access_token string, fb_account_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_interests_suggestion_by_tags", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetInterestsSuggestionByTagsArgs()
	args8.Header = header
	args8.MainTagIds = main_tag_ids
	args8.OtherTagIds = other_tag_ids
	args8.TargetingSpec = targeting_spec
	args8.AccessToken = access_token
	args8.FbAccountId = fb_account_id
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ZeusInterestServiceClient) recvGetInterestsSuggestionByTags() (value []*zeus_ad_types.ZeusInterestWarehouse, e *zeus_common.ZeusException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetInterestsSuggestionByTagsResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

type ZeusInterestServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewZeusInterestServiceProcessor(handler ZeusInterestService) *ZeusInterestServiceProcessor {
	self12 := &ZeusInterestServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self12.AddToProcessorMap("get", &zeusInterestServiceProcessorGet{handler: handler})
	self12.AddToProcessorMap("get_fb_interest_ids_suggestion_by_tags", &zeusInterestServiceProcessorGetFbInterestIdsSuggestionByTags{handler: handler})
	self12.AddToProcessorMap("get_interests_suggestion_by_tags", &zeusInterestServiceProcessorGetInterestsSuggestionByTags{handler: handler})
	return self12
}

type zeusInterestServiceProcessorGet struct {
	handler ZeusInterestService
}

func (p *zeusInterestServiceProcessorGet) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetResult()
	if result.Success, result.E, err = p.handler.Get(args.Header, args.Id); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get: "+err.Error())
		oprot.WriteMessageBegin("get", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type zeusInterestServiceProcessorGetFbInterestIdsSuggestionByTags struct {
	handler ZeusInterestService
}

func (p *zeusInterestServiceProcessorGetFbInterestIdsSuggestionByTags) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFbInterestIdsSuggestionByTagsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_fb_interest_ids_suggestion_by_tags", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFbInterestIdsSuggestionByTagsResult()
	if result.Success, result.E, err = p.handler.GetFbInterestIdsSuggestionByTags(args.Header, args.MainTagIds, args.OtherTagIds, args.TargetingSpec, args.AccessToken, args.FbAccountId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_fb_interest_ids_suggestion_by_tags: "+err.Error())
		oprot.WriteMessageBegin("get_fb_interest_ids_suggestion_by_tags", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_fb_interest_ids_suggestion_by_tags", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type zeusInterestServiceProcessorGetInterestsSuggestionByTags struct {
	handler ZeusInterestService
}

func (p *zeusInterestServiceProcessorGetInterestsSuggestionByTags) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetInterestsSuggestionByTagsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_interests_suggestion_by_tags", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetInterestsSuggestionByTagsResult()
	if result.Success, result.E, err = p.handler.GetInterestsSuggestionByTags(args.Header, args.MainTagIds, args.OtherTagIds, args.TargetingSpec, args.AccessToken, args.FbAccountId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_interests_suggestion_by_tags: "+err.Error())
		oprot.WriteMessageBegin("get_interests_suggestion_by_tags", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_interests_suggestion_by_tags", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Id     int32                 `thrift:"id,2" json:"id"`
}

func NewGetArgs() *GetArgs {
	return &GetArgs{}
}

func (p *GetArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *GetArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *GetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetArgs(%+v)", *p)
}

type GetResult struct {
	Success string                     `thrift:"success,0" json:"success"`
	E       *zeus_common.ZeusException `thrift:"e,1" json:"e"`
}

func NewGetResult() *GetResult {
	return &GetResult{}
}

func (p *GetResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetResult) readField1(iprot thrift.TProtocol) error {
	p.E = zeus_common.NewZeusException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetResult(%+v)", *p)
}

type GetFbInterestIdsSuggestionByTagsArgs struct {
	Header        *common.RequestHeader `thrift:"header,1" json:"header"`
	MainTagIds    []int64               `thrift:"main_tag_ids,2" json:"main_tag_ids"`
	OtherTagIds   []int64               `thrift:"other_tag_ids,3" json:"other_tag_ids"`
	TargetingSpec string                `thrift:"targeting_spec,4" json:"targeting_spec"`
	AccessToken   string                `thrift:"access_token,5" json:"access_token"`
	FbAccountId   int64                 `thrift:"fb_account_id,6" json:"fb_account_id"`
}

func NewGetFbInterestIdsSuggestionByTagsArgs() *GetFbInterestIdsSuggestionByTagsArgs {
	return &GetFbInterestIdsSuggestionByTagsArgs{}
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MainTagIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.MainTagIds = append(p.MainTagIds, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OtherTagIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.OtherTagIds = append(p.OtherTagIds, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TargetingSpec = v
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AccessToken = v
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.FbAccountId = v
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_fb_interest_ids_suggestion_by_tags_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.MainTagIds != nil {
		if err := oprot.WriteFieldBegin("main_tag_ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:main_tag_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.MainTagIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MainTagIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:main_tag_ids: %s", p, err)
		}
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.OtherTagIds != nil {
		if err := oprot.WriteFieldBegin("other_tag_ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:other_tag_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.OtherTagIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OtherTagIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:other_tag_ids: %s", p, err)
		}
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("targeting_spec", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:targeting_spec: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TargetingSpec)); err != nil {
		return fmt.Errorf("%T.targeting_spec (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:targeting_spec: %s", p, err)
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:access_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessToken)); err != nil {
		return fmt.Errorf("%T.access_token (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:access_token: %s", p, err)
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_account_id", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:fb_account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbAccountId)); err != nil {
		return fmt.Errorf("%T.fb_account_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:fb_account_id: %s", p, err)
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFbInterestIdsSuggestionByTagsArgs(%+v)", *p)
}

type GetFbInterestIdsSuggestionByTagsResult struct {
	Success []int64                    `thrift:"success,0" json:"success"`
	E       *zeus_common.ZeusException `thrift:"e,1" json:"e"`
}

func NewGetFbInterestIdsSuggestionByTagsResult() *GetFbInterestIdsSuggestionByTagsResult {
	return &GetFbInterestIdsSuggestionByTagsResult{}
}

func (p *GetFbInterestIdsSuggestionByTagsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = v
		}
		p.Success = append(p.Success, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsResult) readField1(iprot thrift.TProtocol) error {
	p.E = zeus_common.NewZeusException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_fb_interest_ids_suggestion_by_tags_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFbInterestIdsSuggestionByTagsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetFbInterestIdsSuggestionByTagsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFbInterestIdsSuggestionByTagsResult(%+v)", *p)
}

type GetInterestsSuggestionByTagsArgs struct {
	Header        *common.RequestHeader `thrift:"header,1" json:"header"`
	MainTagIds    []int64               `thrift:"main_tag_ids,2" json:"main_tag_ids"`
	OtherTagIds   []int64               `thrift:"other_tag_ids,3" json:"other_tag_ids"`
	TargetingSpec string                `thrift:"targeting_spec,4" json:"targeting_spec"`
	AccessToken   string                `thrift:"access_token,5" json:"access_token"`
	FbAccountId   int64                 `thrift:"fb_account_id,6" json:"fb_account_id"`
}

func NewGetInterestsSuggestionByTagsArgs() *GetInterestsSuggestionByTagsArgs {
	return &GetInterestsSuggestionByTagsArgs{}
}

func (p *GetInterestsSuggestionByTagsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MainTagIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem16 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem16 = v
		}
		p.MainTagIds = append(p.MainTagIds, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OtherTagIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem17 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem17 = v
		}
		p.OtherTagIds = append(p.OtherTagIds, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TargetingSpec = v
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AccessToken = v
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.FbAccountId = v
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_interests_suggestion_by_tags_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetInterestsSuggestionByTagsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.MainTagIds != nil {
		if err := oprot.WriteFieldBegin("main_tag_ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:main_tag_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.MainTagIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MainTagIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:main_tag_ids: %s", p, err)
		}
	}
	return err
}

func (p *GetInterestsSuggestionByTagsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.OtherTagIds != nil {
		if err := oprot.WriteFieldBegin("other_tag_ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:other_tag_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.OtherTagIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OtherTagIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:other_tag_ids: %s", p, err)
		}
	}
	return err
}

func (p *GetInterestsSuggestionByTagsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("targeting_spec", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:targeting_spec: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TargetingSpec)); err != nil {
		return fmt.Errorf("%T.targeting_spec (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:targeting_spec: %s", p, err)
	}
	return err
}

func (p *GetInterestsSuggestionByTagsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:access_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessToken)); err != nil {
		return fmt.Errorf("%T.access_token (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:access_token: %s", p, err)
	}
	return err
}

func (p *GetInterestsSuggestionByTagsArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_account_id", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:fb_account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbAccountId)); err != nil {
		return fmt.Errorf("%T.fb_account_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:fb_account_id: %s", p, err)
	}
	return err
}

func (p *GetInterestsSuggestionByTagsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetInterestsSuggestionByTagsArgs(%+v)", *p)
}

type GetInterestsSuggestionByTagsResult struct {
	Success []*zeus_ad_types.ZeusInterestWarehouse `thrift:"success,0" json:"success"`
	E       *zeus_common.ZeusException             `thrift:"e,1" json:"e"`
}

func NewGetInterestsSuggestionByTagsResult() *GetInterestsSuggestionByTagsResult {
	return &GetInterestsSuggestionByTagsResult{}
}

func (p *GetInterestsSuggestionByTagsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*zeus_ad_types.ZeusInterestWarehouse, 0, size)
	for i := 0; i < size; i++ {
		_elem18 := zeus_ad_types.NewZeusInterestWarehouse()
		if err := _elem18.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem18)
		}
		p.Success = append(p.Success, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsResult) readField1(iprot thrift.TProtocol) error {
	p.E = zeus_common.NewZeusException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_interests_suggestion_by_tags_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetInterestsSuggestionByTagsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetInterestsSuggestionByTagsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetInterestsSuggestionByTagsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetInterestsSuggestionByTagsResult(%+v)", *p)
}
