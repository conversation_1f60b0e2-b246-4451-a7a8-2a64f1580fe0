// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"ct_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>der<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addSourceMaterial(RequestHeader header, SourceMaterial sourceMaterial)")
	fmt.Fprintln(os.Stderr, "   getSourceMaterialByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.<PERSON>, "  QueryResult searchSourceMaterialByParams(RequestHeader header, SourceMaterialParams params)")
	fmt.Fprintln(os.Stderr, "  bool checkSourceMaterialNameIsExists(RequestHeader header, string name)")
	fmt.Fprintln(os.Stderr, "  i32 addDeliveryMaterial(RequestHeader header, DeliveryMaterial deliveryMaterial)")
	fmt.Fprintln(os.Stderr, "  bool updateDeliveryMaterial(RequestHeader header, DeliveryMaterial deliveryMaterial, VideoGenerationInfo generateInfo)")
	fmt.Fprintln(os.Stderr, "   getDeliveryMaterialByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchDeliveryMaterialByParams(RequestHeader header, DeliveryMaterialParams params)")
	fmt.Fprintln(os.Stderr, "  bool checkDeliveryMaterialNameIsExists(RequestHeader header, string name)")
	fmt.Fprintln(os.Stderr, "   getDeliveryChannelByMaterialId(RequestHeader header, i32 materialId)")
	fmt.Fprintln(os.Stderr, "  bool videoConvert(RequestHeader header, i32 materialId,  channels)")
	fmt.Fprintln(os.Stderr, "  void updateVideoConvertStatus(RequestHeader header,  videoInfo)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchVideoMaterialByParams(RequestHeader header, VideoMaterialParams params)")
	fmt.Fprintln(os.Stderr, "   getVideoInfoByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getVideoMaterialByChannelId(RequestHeader header, i32 channelId)")
	fmt.Fprintln(os.Stderr, "  string getTranscodingEncodeIdByIdAndExchangeId(RequestHeader header, string id, i32 exchangeId)")
	fmt.Fprintln(os.Stderr, "   batchConvertHtmlToImage(RequestHeader header,  codes)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := ct_server.NewCtServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addSourceMaterial":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSourceMaterial requires 2 args")
			flag.Usage()
		}
		arg87 := flag.Arg(1)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue0 := ct_server.NewRequestHeader()
		err92 := argvalue0.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg93 := flag.Arg(2)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		argvalue1 := ct_server.NewSourceMaterial()
		err98 := argvalue1.Read(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSourceMaterial(value0, value1))
		fmt.Print("\n")
		break
	case "getSourceMaterialByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSourceMaterialByIds requires 2 args")
			flag.Usage()
		}
		arg99 := flag.Arg(1)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		argvalue0 := ct_server.NewRequestHeader()
		err104 := argvalue0.Read(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg105 := flag.Arg(2)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		containerStruct1 := ct_server.NewGetSourceMaterialByIdsArgs()
		err110 := containerStruct1.ReadField2(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSourceMaterialByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchSourceMaterialByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchSourceMaterialByParams requires 2 args")
			flag.Usage()
		}
		arg111 := flag.Arg(1)
		mbTrans112 := thrift.NewTMemoryBufferLen(len(arg111))
		defer mbTrans112.Close()
		_, err113 := mbTrans112.WriteString(arg111)
		if err113 != nil {
			Usage()
			return
		}
		factory114 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt115 := factory114.GetProtocol(mbTrans112)
		argvalue0 := ct_server.NewRequestHeader()
		err116 := argvalue0.Read(jsProt115)
		if err116 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg117 := flag.Arg(2)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		argvalue1 := ct_server.NewSourceMaterialParams()
		err122 := argvalue1.Read(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchSourceMaterialByParams(value0, value1))
		fmt.Print("\n")
		break
	case "checkSourceMaterialNameIsExists":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CheckSourceMaterialNameIsExists requires 2 args")
			flag.Usage()
		}
		arg123 := flag.Arg(1)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue0 := ct_server.NewRequestHeader()
		err128 := argvalue0.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.CheckSourceMaterialNameIsExists(value0, value1))
		fmt.Print("\n")
		break
	case "addDeliveryMaterial":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddDeliveryMaterial requires 2 args")
			flag.Usage()
		}
		arg130 := flag.Arg(1)
		mbTrans131 := thrift.NewTMemoryBufferLen(len(arg130))
		defer mbTrans131.Close()
		_, err132 := mbTrans131.WriteString(arg130)
		if err132 != nil {
			Usage()
			return
		}
		factory133 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt134 := factory133.GetProtocol(mbTrans131)
		argvalue0 := ct_server.NewRequestHeader()
		err135 := argvalue0.Read(jsProt134)
		if err135 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg136 := flag.Arg(2)
		mbTrans137 := thrift.NewTMemoryBufferLen(len(arg136))
		defer mbTrans137.Close()
		_, err138 := mbTrans137.WriteString(arg136)
		if err138 != nil {
			Usage()
			return
		}
		factory139 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt140 := factory139.GetProtocol(mbTrans137)
		argvalue1 := ct_server.NewDeliveryMaterial()
		err141 := argvalue1.Read(jsProt140)
		if err141 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddDeliveryMaterial(value0, value1))
		fmt.Print("\n")
		break
	case "updateDeliveryMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateDeliveryMaterial requires 3 args")
			flag.Usage()
		}
		arg142 := flag.Arg(1)
		mbTrans143 := thrift.NewTMemoryBufferLen(len(arg142))
		defer mbTrans143.Close()
		_, err144 := mbTrans143.WriteString(arg142)
		if err144 != nil {
			Usage()
			return
		}
		factory145 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt146 := factory145.GetProtocol(mbTrans143)
		argvalue0 := ct_server.NewRequestHeader()
		err147 := argvalue0.Read(jsProt146)
		if err147 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg148 := flag.Arg(2)
		mbTrans149 := thrift.NewTMemoryBufferLen(len(arg148))
		defer mbTrans149.Close()
		_, err150 := mbTrans149.WriteString(arg148)
		if err150 != nil {
			Usage()
			return
		}
		factory151 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt152 := factory151.GetProtocol(mbTrans149)
		argvalue1 := ct_server.NewDeliveryMaterial()
		err153 := argvalue1.Read(jsProt152)
		if err153 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg154 := flag.Arg(3)
		mbTrans155 := thrift.NewTMemoryBufferLen(len(arg154))
		defer mbTrans155.Close()
		_, err156 := mbTrans155.WriteString(arg154)
		if err156 != nil {
			Usage()
			return
		}
		factory157 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt158 := factory157.GetProtocol(mbTrans155)
		argvalue2 := ct_server.NewVideoGenerationInfo()
		err159 := argvalue2.Read(jsProt158)
		if err159 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.UpdateDeliveryMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getDeliveryMaterialByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDeliveryMaterialByIds requires 2 args")
			flag.Usage()
		}
		arg160 := flag.Arg(1)
		mbTrans161 := thrift.NewTMemoryBufferLen(len(arg160))
		defer mbTrans161.Close()
		_, err162 := mbTrans161.WriteString(arg160)
		if err162 != nil {
			Usage()
			return
		}
		factory163 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt164 := factory163.GetProtocol(mbTrans161)
		argvalue0 := ct_server.NewRequestHeader()
		err165 := argvalue0.Read(jsProt164)
		if err165 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg166 := flag.Arg(2)
		mbTrans167 := thrift.NewTMemoryBufferLen(len(arg166))
		defer mbTrans167.Close()
		_, err168 := mbTrans167.WriteString(arg166)
		if err168 != nil {
			Usage()
			return
		}
		factory169 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt170 := factory169.GetProtocol(mbTrans167)
		containerStruct1 := ct_server.NewGetDeliveryMaterialByIdsArgs()
		err171 := containerStruct1.ReadField2(jsProt170)
		if err171 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDeliveryMaterialByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchDeliveryMaterialByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchDeliveryMaterialByParams requires 2 args")
			flag.Usage()
		}
		arg172 := flag.Arg(1)
		mbTrans173 := thrift.NewTMemoryBufferLen(len(arg172))
		defer mbTrans173.Close()
		_, err174 := mbTrans173.WriteString(arg172)
		if err174 != nil {
			Usage()
			return
		}
		factory175 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt176 := factory175.GetProtocol(mbTrans173)
		argvalue0 := ct_server.NewRequestHeader()
		err177 := argvalue0.Read(jsProt176)
		if err177 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg178 := flag.Arg(2)
		mbTrans179 := thrift.NewTMemoryBufferLen(len(arg178))
		defer mbTrans179.Close()
		_, err180 := mbTrans179.WriteString(arg178)
		if err180 != nil {
			Usage()
			return
		}
		factory181 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt182 := factory181.GetProtocol(mbTrans179)
		argvalue1 := ct_server.NewDeliveryMaterialParams()
		err183 := argvalue1.Read(jsProt182)
		if err183 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchDeliveryMaterialByParams(value0, value1))
		fmt.Print("\n")
		break
	case "checkDeliveryMaterialNameIsExists":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CheckDeliveryMaterialNameIsExists requires 2 args")
			flag.Usage()
		}
		arg184 := flag.Arg(1)
		mbTrans185 := thrift.NewTMemoryBufferLen(len(arg184))
		defer mbTrans185.Close()
		_, err186 := mbTrans185.WriteString(arg184)
		if err186 != nil {
			Usage()
			return
		}
		factory187 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt188 := factory187.GetProtocol(mbTrans185)
		argvalue0 := ct_server.NewRequestHeader()
		err189 := argvalue0.Read(jsProt188)
		if err189 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.CheckDeliveryMaterialNameIsExists(value0, value1))
		fmt.Print("\n")
		break
	case "getDeliveryChannelByMaterialId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDeliveryChannelByMaterialId requires 2 args")
			flag.Usage()
		}
		arg191 := flag.Arg(1)
		mbTrans192 := thrift.NewTMemoryBufferLen(len(arg191))
		defer mbTrans192.Close()
		_, err193 := mbTrans192.WriteString(arg191)
		if err193 != nil {
			Usage()
			return
		}
		factory194 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt195 := factory194.GetProtocol(mbTrans192)
		argvalue0 := ct_server.NewRequestHeader()
		err196 := argvalue0.Read(jsProt195)
		if err196 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err197 := (strconv.Atoi(flag.Arg(2)))
		if err197 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetDeliveryChannelByMaterialId(value0, value1))
		fmt.Print("\n")
		break
	case "videoConvert":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "VideoConvert requires 3 args")
			flag.Usage()
		}
		arg198 := flag.Arg(1)
		mbTrans199 := thrift.NewTMemoryBufferLen(len(arg198))
		defer mbTrans199.Close()
		_, err200 := mbTrans199.WriteString(arg198)
		if err200 != nil {
			Usage()
			return
		}
		factory201 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt202 := factory201.GetProtocol(mbTrans199)
		argvalue0 := ct_server.NewRequestHeader()
		err203 := argvalue0.Read(jsProt202)
		if err203 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err204 := (strconv.Atoi(flag.Arg(2)))
		if err204 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg205 := flag.Arg(3)
		mbTrans206 := thrift.NewTMemoryBufferLen(len(arg205))
		defer mbTrans206.Close()
		_, err207 := mbTrans206.WriteString(arg205)
		if err207 != nil {
			Usage()
			return
		}
		factory208 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt209 := factory208.GetProtocol(mbTrans206)
		containerStruct2 := ct_server.NewVideoConvertArgs()
		err210 := containerStruct2.ReadField3(jsProt209)
		if err210 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Channels
		value2 := argvalue2
		fmt.Print(client.VideoConvert(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateVideoConvertStatus":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateVideoConvertStatus requires 2 args")
			flag.Usage()
		}
		arg211 := flag.Arg(1)
		mbTrans212 := thrift.NewTMemoryBufferLen(len(arg211))
		defer mbTrans212.Close()
		_, err213 := mbTrans212.WriteString(arg211)
		if err213 != nil {
			Usage()
			return
		}
		factory214 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt215 := factory214.GetProtocol(mbTrans212)
		argvalue0 := ct_server.NewRequestHeader()
		err216 := argvalue0.Read(jsProt215)
		if err216 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg217 := flag.Arg(2)
		mbTrans218 := thrift.NewTMemoryBufferLen(len(arg217))
		defer mbTrans218.Close()
		_, err219 := mbTrans218.WriteString(arg217)
		if err219 != nil {
			Usage()
			return
		}
		factory220 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt221 := factory220.GetProtocol(mbTrans218)
		containerStruct1 := ct_server.NewUpdateVideoConvertStatusArgs()
		err222 := containerStruct1.ReadField2(jsProt221)
		if err222 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.VideoInfo
		value1 := argvalue1
		fmt.Print(client.UpdateVideoConvertStatus(value0, value1))
		fmt.Print("\n")
		break
	case "searchVideoMaterialByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchVideoMaterialByParams requires 2 args")
			flag.Usage()
		}
		arg223 := flag.Arg(1)
		mbTrans224 := thrift.NewTMemoryBufferLen(len(arg223))
		defer mbTrans224.Close()
		_, err225 := mbTrans224.WriteString(arg223)
		if err225 != nil {
			Usage()
			return
		}
		factory226 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt227 := factory226.GetProtocol(mbTrans224)
		argvalue0 := ct_server.NewRequestHeader()
		err228 := argvalue0.Read(jsProt227)
		if err228 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg229 := flag.Arg(2)
		mbTrans230 := thrift.NewTMemoryBufferLen(len(arg229))
		defer mbTrans230.Close()
		_, err231 := mbTrans230.WriteString(arg229)
		if err231 != nil {
			Usage()
			return
		}
		factory232 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt233 := factory232.GetProtocol(mbTrans230)
		argvalue1 := ct_server.NewVideoMaterialParams()
		err234 := argvalue1.Read(jsProt233)
		if err234 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchVideoMaterialByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getVideoInfoByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVideoInfoByIds requires 2 args")
			flag.Usage()
		}
		arg235 := flag.Arg(1)
		mbTrans236 := thrift.NewTMemoryBufferLen(len(arg235))
		defer mbTrans236.Close()
		_, err237 := mbTrans236.WriteString(arg235)
		if err237 != nil {
			Usage()
			return
		}
		factory238 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt239 := factory238.GetProtocol(mbTrans236)
		argvalue0 := ct_server.NewRequestHeader()
		err240 := argvalue0.Read(jsProt239)
		if err240 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg241 := flag.Arg(2)
		mbTrans242 := thrift.NewTMemoryBufferLen(len(arg241))
		defer mbTrans242.Close()
		_, err243 := mbTrans242.WriteString(arg241)
		if err243 != nil {
			Usage()
			return
		}
		factory244 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt245 := factory244.GetProtocol(mbTrans242)
		containerStruct1 := ct_server.NewGetVideoInfoByIdsArgs()
		err246 := containerStruct1.ReadField2(jsProt245)
		if err246 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetVideoInfoByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getVideoMaterialByChannelId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVideoMaterialByChannelId requires 2 args")
			flag.Usage()
		}
		arg247 := flag.Arg(1)
		mbTrans248 := thrift.NewTMemoryBufferLen(len(arg247))
		defer mbTrans248.Close()
		_, err249 := mbTrans248.WriteString(arg247)
		if err249 != nil {
			Usage()
			return
		}
		factory250 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt251 := factory250.GetProtocol(mbTrans248)
		argvalue0 := ct_server.NewRequestHeader()
		err252 := argvalue0.Read(jsProt251)
		if err252 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err253 := (strconv.Atoi(flag.Arg(2)))
		if err253 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetVideoMaterialByChannelId(value0, value1))
		fmt.Print("\n")
		break
	case "getTranscodingEncodeIdByIdAndExchangeId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetTranscodingEncodeIdByIdAndExchangeId requires 3 args")
			flag.Usage()
		}
		arg254 := flag.Arg(1)
		mbTrans255 := thrift.NewTMemoryBufferLen(len(arg254))
		defer mbTrans255.Close()
		_, err256 := mbTrans255.WriteString(arg254)
		if err256 != nil {
			Usage()
			return
		}
		factory257 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt258 := factory257.GetProtocol(mbTrans255)
		argvalue0 := ct_server.NewRequestHeader()
		err259 := argvalue0.Read(jsProt258)
		if err259 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err261 := (strconv.Atoi(flag.Arg(3)))
		if err261 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetTranscodingEncodeIdByIdAndExchangeId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "batchConvertHtmlToImage":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "BatchConvertHtmlToImage requires 2 args")
			flag.Usage()
		}
		arg262 := flag.Arg(1)
		mbTrans263 := thrift.NewTMemoryBufferLen(len(arg262))
		defer mbTrans263.Close()
		_, err264 := mbTrans263.WriteString(arg262)
		if err264 != nil {
			Usage()
			return
		}
		factory265 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt266 := factory265.GetProtocol(mbTrans263)
		argvalue0 := ct_server.NewRequestHeader()
		err267 := argvalue0.Read(jsProt266)
		if err267 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg268 := flag.Arg(2)
		mbTrans269 := thrift.NewTMemoryBufferLen(len(arg268))
		defer mbTrans269.Close()
		_, err270 := mbTrans269.WriteString(arg268)
		if err270 != nil {
			Usage()
			return
		}
		factory271 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt272 := factory271.GetProtocol(mbTrans269)
		containerStruct1 := ct_server.NewBatchConvertHtmlToImageArgs()
		err273 := containerStruct1.ReadField2(jsProt272)
		if err273 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Codes
		value1 := argvalue1
		fmt.Print(client.BatchConvertHtmlToImage(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
