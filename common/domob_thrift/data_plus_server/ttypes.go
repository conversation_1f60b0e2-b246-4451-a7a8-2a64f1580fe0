// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package data_plus_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/data_plus_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = data_plus_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type DataPlusServiceExceptionCode int64

const (
	DataPlusServiceExceptionCode_QUERY_PARAM_ERROR DataPlusServiceExceptionCode = 10000
	DataPlusServiceExceptionCode_NAME_EXISTS       DataPlusServiceExceptionCode = 10101
	DataPlusServiceExceptionCode_USER_NOT_EXISTS   DataPlusServiceExceptionCode = 10102
	DataPlusServiceExceptionCode_FILE_NOT_MATCHING DataPlusServiceExceptionCode = 10201
	DataPlusServiceExceptionCode_SYSTEM_ERROR      DataPlusServiceExceptionCode = 20000
)

func (p DataPlusServiceExceptionCode) String() string {
	switch p {
	case DataPlusServiceExceptionCode_QUERY_PARAM_ERROR:
		return "DataPlusServiceExceptionCode_QUERY_PARAM_ERROR"
	case DataPlusServiceExceptionCode_NAME_EXISTS:
		return "DataPlusServiceExceptionCode_NAME_EXISTS"
	case DataPlusServiceExceptionCode_USER_NOT_EXISTS:
		return "DataPlusServiceExceptionCode_USER_NOT_EXISTS"
	case DataPlusServiceExceptionCode_FILE_NOT_MATCHING:
		return "DataPlusServiceExceptionCode_FILE_NOT_MATCHING"
	case DataPlusServiceExceptionCode_SYSTEM_ERROR:
		return "DataPlusServiceExceptionCode_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func DataPlusServiceExceptionCodeFromString(s string) (DataPlusServiceExceptionCode, error) {
	switch s {
	case "DataPlusServiceExceptionCode_QUERY_PARAM_ERROR":
		return DataPlusServiceExceptionCode_QUERY_PARAM_ERROR, nil
	case "DataPlusServiceExceptionCode_NAME_EXISTS":
		return DataPlusServiceExceptionCode_NAME_EXISTS, nil
	case "DataPlusServiceExceptionCode_USER_NOT_EXISTS":
		return DataPlusServiceExceptionCode_USER_NOT_EXISTS, nil
	case "DataPlusServiceExceptionCode_FILE_NOT_MATCHING":
		return DataPlusServiceExceptionCode_FILE_NOT_MATCHING, nil
	case "DataPlusServiceExceptionCode_SYSTEM_ERROR":
		return DataPlusServiceExceptionCode_SYSTEM_ERROR, nil
	}
	return DataPlusServiceExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid DataPlusServiceExceptionCode string")
}

type DataPlusServiceException struct {
	Code    DataPlusServiceExceptionCode `thrift:"code,1" json:"code"`
	Message string                       `thrift:"message,2" json:"message"`
}

func NewDataPlusServiceException() *DataPlusServiceException {
	return &DataPlusServiceException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DataPlusServiceException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DataPlusServiceException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DataPlusServiceException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = DataPlusServiceExceptionCode(v)
	}
	return nil
}

func (p *DataPlusServiceException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DataPlusServiceException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DataPlusServiceException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DataPlusServiceException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DataPlusServiceException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DataPlusServiceException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataPlusServiceException(%+v)", *p)
}
