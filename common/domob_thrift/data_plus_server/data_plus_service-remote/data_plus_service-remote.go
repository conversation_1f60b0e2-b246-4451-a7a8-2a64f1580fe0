// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"data_plus_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addAudience(RequestHeader header, Audience audience)")
	fmt.Fprintln(os.Stderr, "  void deleteAudiencesByIds(RequestHeader header, i32 uid,  ids)")
	fmt.Fprintln(os.<PERSON>r, "  void editAudience(RequestHeader header, Audience audience)")
	fmt.Fprintln(os.Stderr, "  void commitAudience(RequestHeader header, i32 uid, i32 id)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAudiencesByParams(RequestHeader header, AudienceParams params)")
	fmt.Fprintln(os.Stderr, "   getAudiencesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := data_plus_server.NewDataPlusServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addAudience":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAudience requires 2 args")
			flag.Usage()
		}
		arg30 := flag.Arg(1)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue0 := data_plus_server.NewRequestHeader()
		err35 := argvalue0.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg36 := flag.Arg(2)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		argvalue1 := data_plus_server.NewAudience()
		err41 := argvalue1.Read(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAudience(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAudiencesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAudiencesByIds requires 3 args")
			flag.Usage()
		}
		arg42 := flag.Arg(1)
		mbTrans43 := thrift.NewTMemoryBufferLen(len(arg42))
		defer mbTrans43.Close()
		_, err44 := mbTrans43.WriteString(arg42)
		if err44 != nil {
			Usage()
			return
		}
		factory45 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt46 := factory45.GetProtocol(mbTrans43)
		argvalue0 := data_plus_server.NewRequestHeader()
		err47 := argvalue0.Read(jsProt46)
		if err47 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err48 := (strconv.Atoi(flag.Arg(2)))
		if err48 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg49 := flag.Arg(3)
		mbTrans50 := thrift.NewTMemoryBufferLen(len(arg49))
		defer mbTrans50.Close()
		_, err51 := mbTrans50.WriteString(arg49)
		if err51 != nil {
			Usage()
			return
		}
		factory52 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt53 := factory52.GetProtocol(mbTrans50)
		containerStruct2 := data_plus_server.NewDeleteAudiencesByIdsArgs()
		err54 := containerStruct2.ReadField3(jsProt53)
		if err54 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAudiencesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAudience":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAudience requires 2 args")
			flag.Usage()
		}
		arg55 := flag.Arg(1)
		mbTrans56 := thrift.NewTMemoryBufferLen(len(arg55))
		defer mbTrans56.Close()
		_, err57 := mbTrans56.WriteString(arg55)
		if err57 != nil {
			Usage()
			return
		}
		factory58 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt59 := factory58.GetProtocol(mbTrans56)
		argvalue0 := data_plus_server.NewRequestHeader()
		err60 := argvalue0.Read(jsProt59)
		if err60 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg61 := flag.Arg(2)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue1 := data_plus_server.NewAudience()
		err66 := argvalue1.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditAudience(value0, value1))
		fmt.Print("\n")
		break
	case "commitAudience":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CommitAudience requires 3 args")
			flag.Usage()
		}
		arg67 := flag.Arg(1)
		mbTrans68 := thrift.NewTMemoryBufferLen(len(arg67))
		defer mbTrans68.Close()
		_, err69 := mbTrans68.WriteString(arg67)
		if err69 != nil {
			Usage()
			return
		}
		factory70 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt71 := factory70.GetProtocol(mbTrans68)
		argvalue0 := data_plus_server.NewRequestHeader()
		err72 := argvalue0.Read(jsProt71)
		if err72 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err73 := (strconv.Atoi(flag.Arg(2)))
		if err73 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err74 := (strconv.Atoi(flag.Arg(3)))
		if err74 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.CommitAudience(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchAudiencesByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAudiencesByParams requires 2 args")
			flag.Usage()
		}
		arg75 := flag.Arg(1)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue0 := data_plus_server.NewRequestHeader()
		err80 := argvalue0.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg81 := flag.Arg(2)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue1 := data_plus_server.NewAudienceParams()
		err86 := argvalue1.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAudiencesByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getAudiencesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAudiencesByIds requires 2 args")
			flag.Usage()
		}
		arg87 := flag.Arg(1)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue0 := data_plus_server.NewRequestHeader()
		err92 := argvalue0.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg93 := flag.Arg(2)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		containerStruct1 := data_plus_server.NewGetAudiencesByIdsArgs()
		err98 := containerStruct1.ReadField2(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAudiencesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
