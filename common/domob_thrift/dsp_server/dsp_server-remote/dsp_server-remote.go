// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dsp_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addCampaign(RequestHeader header, DSPCampaign campaign)")
	fmt.Fprintln(os.Stderr, "  void editCampaign(RequestHeader header, DSPCampaign campaign)")
	fmt.Fprintln(os.<PERSON>, "  void editCampaignSchedule(RequestHeader header, i32 id,  schedules)")
	fmt.Fprintln(os.Stderr, "   getCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult listCampaignsByScheduleIds(RequestHeader header,  ids, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryCampaignsByParam(RequestHeader header, QueryParam param)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryCampaignsByParamWithOrder(RequestHeader header, QueryParam param, OrderParam order)")
	fmt.Fprintln(os.Stderr, "   listCampaignsBySchIdsAndChnType(RequestHeader header,  schIds, CampaignChannelType channelType, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   listAffiliateByAffTypeAndExternalIds(RequestHeader header, CampaignChannelType affType,  externalIds, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  i32 addLinkPay(RequestHeader header, LinkPay linkPay)")
	fmt.Fprintln(os.Stderr, "  void editLinkPay(RequestHeader header, LinkPay linkPay)")
	fmt.Fprintln(os.Stderr, "   getLinkPaysByParam(RequestHeader header, LinkPayParams param)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dsp_server.NewDspServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCampaign requires 2 args")
			flag.Usage()
		}
		arg61 := flag.Arg(1)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue0 := dsp_server.NewRequestHeader()
		err66 := argvalue0.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg67 := flag.Arg(2)
		mbTrans68 := thrift.NewTMemoryBufferLen(len(arg67))
		defer mbTrans68.Close()
		_, err69 := mbTrans68.WriteString(arg67)
		if err69 != nil {
			Usage()
			return
		}
		factory70 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt71 := factory70.GetProtocol(mbTrans68)
		argvalue1 := dsp_server.NewDSPCampaign()
		err72 := argvalue1.Read(jsProt71)
		if err72 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "editCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCampaign requires 2 args")
			flag.Usage()
		}
		arg73 := flag.Arg(1)
		mbTrans74 := thrift.NewTMemoryBufferLen(len(arg73))
		defer mbTrans74.Close()
		_, err75 := mbTrans74.WriteString(arg73)
		if err75 != nil {
			Usage()
			return
		}
		factory76 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt77 := factory76.GetProtocol(mbTrans74)
		argvalue0 := dsp_server.NewRequestHeader()
		err78 := argvalue0.Read(jsProt77)
		if err78 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg79 := flag.Arg(2)
		mbTrans80 := thrift.NewTMemoryBufferLen(len(arg79))
		defer mbTrans80.Close()
		_, err81 := mbTrans80.WriteString(arg79)
		if err81 != nil {
			Usage()
			return
		}
		factory82 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt83 := factory82.GetProtocol(mbTrans80)
		argvalue1 := dsp_server.NewDSPCampaign()
		err84 := argvalue1.Read(jsProt83)
		if err84 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "editCampaignSchedule":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCampaignSchedule requires 3 args")
			flag.Usage()
		}
		arg85 := flag.Arg(1)
		mbTrans86 := thrift.NewTMemoryBufferLen(len(arg85))
		defer mbTrans86.Close()
		_, err87 := mbTrans86.WriteString(arg85)
		if err87 != nil {
			Usage()
			return
		}
		factory88 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt89 := factory88.GetProtocol(mbTrans86)
		argvalue0 := dsp_server.NewRequestHeader()
		err90 := argvalue0.Read(jsProt89)
		if err90 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err91 := (strconv.Atoi(flag.Arg(2)))
		if err91 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg92 := flag.Arg(3)
		mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
		defer mbTrans93.Close()
		_, err94 := mbTrans93.WriteString(arg92)
		if err94 != nil {
			Usage()
			return
		}
		factory95 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt96 := factory95.GetProtocol(mbTrans93)
		containerStruct2 := dsp_server.NewEditCampaignScheduleArgs()
		err97 := containerStruct2.ReadField3(jsProt96)
		if err97 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Schedules
		value2 := argvalue2
		fmt.Print(client.EditCampaignSchedule(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg98 := flag.Arg(1)
		mbTrans99 := thrift.NewTMemoryBufferLen(len(arg98))
		defer mbTrans99.Close()
		_, err100 := mbTrans99.WriteString(arg98)
		if err100 != nil {
			Usage()
			return
		}
		factory101 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt102 := factory101.GetProtocol(mbTrans99)
		argvalue0 := dsp_server.NewRequestHeader()
		err103 := argvalue0.Read(jsProt102)
		if err103 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg104 := flag.Arg(2)
		mbTrans105 := thrift.NewTMemoryBufferLen(len(arg104))
		defer mbTrans105.Close()
		_, err106 := mbTrans105.WriteString(arg104)
		if err106 != nil {
			Usage()
			return
		}
		factory107 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt108 := factory107.GetProtocol(mbTrans105)
		containerStruct1 := dsp_server.NewGetCampaignsByIdsArgs()
		err109 := containerStruct1.ReadField2(jsProt108)
		if err109 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listCampaignsByScheduleIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListCampaignsByScheduleIds requires 4 args")
			flag.Usage()
		}
		arg110 := flag.Arg(1)
		mbTrans111 := thrift.NewTMemoryBufferLen(len(arg110))
		defer mbTrans111.Close()
		_, err112 := mbTrans111.WriteString(arg110)
		if err112 != nil {
			Usage()
			return
		}
		factory113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt114 := factory113.GetProtocol(mbTrans111)
		argvalue0 := dsp_server.NewRequestHeader()
		err115 := argvalue0.Read(jsProt114)
		if err115 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg116 := flag.Arg(2)
		mbTrans117 := thrift.NewTMemoryBufferLen(len(arg116))
		defer mbTrans117.Close()
		_, err118 := mbTrans117.WriteString(arg116)
		if err118 != nil {
			Usage()
			return
		}
		factory119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt120 := factory119.GetProtocol(mbTrans117)
		containerStruct1 := dsp_server.NewListCampaignsByScheduleIdsArgs()
		err121 := containerStruct1.ReadField2(jsProt120)
		if err121 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		tmp2, err122 := (strconv.Atoi(flag.Arg(3)))
		if err122 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err123 := (strconv.Atoi(flag.Arg(4)))
		if err123 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListCampaignsByScheduleIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "queryCampaignsByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "QueryCampaignsByParam requires 2 args")
			flag.Usage()
		}
		arg124 := flag.Arg(1)
		mbTrans125 := thrift.NewTMemoryBufferLen(len(arg124))
		defer mbTrans125.Close()
		_, err126 := mbTrans125.WriteString(arg124)
		if err126 != nil {
			Usage()
			return
		}
		factory127 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt128 := factory127.GetProtocol(mbTrans125)
		argvalue0 := dsp_server.NewRequestHeader()
		err129 := argvalue0.Read(jsProt128)
		if err129 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg130 := flag.Arg(2)
		mbTrans131 := thrift.NewTMemoryBufferLen(len(arg130))
		defer mbTrans131.Close()
		_, err132 := mbTrans131.WriteString(arg130)
		if err132 != nil {
			Usage()
			return
		}
		factory133 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt134 := factory133.GetProtocol(mbTrans131)
		argvalue1 := dsp_server.NewQueryParam()
		err135 := argvalue1.Read(jsProt134)
		if err135 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.QueryCampaignsByParam(value0, value1))
		fmt.Print("\n")
		break
	case "queryCampaignsByParamWithOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "QueryCampaignsByParamWithOrder requires 3 args")
			flag.Usage()
		}
		arg136 := flag.Arg(1)
		mbTrans137 := thrift.NewTMemoryBufferLen(len(arg136))
		defer mbTrans137.Close()
		_, err138 := mbTrans137.WriteString(arg136)
		if err138 != nil {
			Usage()
			return
		}
		factory139 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt140 := factory139.GetProtocol(mbTrans137)
		argvalue0 := dsp_server.NewRequestHeader()
		err141 := argvalue0.Read(jsProt140)
		if err141 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg142 := flag.Arg(2)
		mbTrans143 := thrift.NewTMemoryBufferLen(len(arg142))
		defer mbTrans143.Close()
		_, err144 := mbTrans143.WriteString(arg142)
		if err144 != nil {
			Usage()
			return
		}
		factory145 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt146 := factory145.GetProtocol(mbTrans143)
		argvalue1 := dsp_server.NewQueryParam()
		err147 := argvalue1.Read(jsProt146)
		if err147 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg148 := flag.Arg(3)
		mbTrans149 := thrift.NewTMemoryBufferLen(len(arg148))
		defer mbTrans149.Close()
		_, err150 := mbTrans149.WriteString(arg148)
		if err150 != nil {
			Usage()
			return
		}
		factory151 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt152 := factory151.GetProtocol(mbTrans149)
		argvalue2 := dsp_server.NewOrderParam()
		err153 := argvalue2.Read(jsProt152)
		if err153 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.QueryCampaignsByParamWithOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listCampaignsBySchIdsAndChnType":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListCampaignsBySchIdsAndChnType requires 5 args")
			flag.Usage()
		}
		arg154 := flag.Arg(1)
		mbTrans155 := thrift.NewTMemoryBufferLen(len(arg154))
		defer mbTrans155.Close()
		_, err156 := mbTrans155.WriteString(arg154)
		if err156 != nil {
			Usage()
			return
		}
		factory157 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt158 := factory157.GetProtocol(mbTrans155)
		argvalue0 := dsp_server.NewRequestHeader()
		err159 := argvalue0.Read(jsProt158)
		if err159 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg160 := flag.Arg(2)
		mbTrans161 := thrift.NewTMemoryBufferLen(len(arg160))
		defer mbTrans161.Close()
		_, err162 := mbTrans161.WriteString(arg160)
		if err162 != nil {
			Usage()
			return
		}
		factory163 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt164 := factory163.GetProtocol(mbTrans161)
		containerStruct1 := dsp_server.NewListCampaignsBySchIdsAndChnTypeArgs()
		err165 := containerStruct1.ReadField2(jsProt164)
		if err165 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.SchIds
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := dsp_server.CampaignChannelType(tmp2)
		value2 := argvalue2
		tmp3, err166 := (strconv.Atoi(flag.Arg(4)))
		if err166 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err167 := (strconv.Atoi(flag.Arg(5)))
		if err167 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListCampaignsBySchIdsAndChnType(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listAffiliateByAffTypeAndExternalIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListAffiliateByAffTypeAndExternalIds requires 5 args")
			flag.Usage()
		}
		arg168 := flag.Arg(1)
		mbTrans169 := thrift.NewTMemoryBufferLen(len(arg168))
		defer mbTrans169.Close()
		_, err170 := mbTrans169.WriteString(arg168)
		if err170 != nil {
			Usage()
			return
		}
		factory171 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt172 := factory171.GetProtocol(mbTrans169)
		argvalue0 := dsp_server.NewRequestHeader()
		err173 := argvalue0.Read(jsProt172)
		if err173 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := dsp_server.CampaignChannelType(tmp1)
		value1 := argvalue1
		arg174 := flag.Arg(3)
		mbTrans175 := thrift.NewTMemoryBufferLen(len(arg174))
		defer mbTrans175.Close()
		_, err176 := mbTrans175.WriteString(arg174)
		if err176 != nil {
			Usage()
			return
		}
		factory177 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt178 := factory177.GetProtocol(mbTrans175)
		containerStruct2 := dsp_server.NewListAffiliateByAffTypeAndExternalIdsArgs()
		err179 := containerStruct2.ReadField3(jsProt178)
		if err179 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ExternalIds
		value2 := argvalue2
		tmp3, err180 := (strconv.Atoi(flag.Arg(4)))
		if err180 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err181 := (strconv.Atoi(flag.Arg(5)))
		if err181 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListAffiliateByAffTypeAndExternalIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addLinkPay":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddLinkPay requires 2 args")
			flag.Usage()
		}
		arg182 := flag.Arg(1)
		mbTrans183 := thrift.NewTMemoryBufferLen(len(arg182))
		defer mbTrans183.Close()
		_, err184 := mbTrans183.WriteString(arg182)
		if err184 != nil {
			Usage()
			return
		}
		factory185 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt186 := factory185.GetProtocol(mbTrans183)
		argvalue0 := dsp_server.NewRequestHeader()
		err187 := argvalue0.Read(jsProt186)
		if err187 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg188 := flag.Arg(2)
		mbTrans189 := thrift.NewTMemoryBufferLen(len(arg188))
		defer mbTrans189.Close()
		_, err190 := mbTrans189.WriteString(arg188)
		if err190 != nil {
			Usage()
			return
		}
		factory191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt192 := factory191.GetProtocol(mbTrans189)
		argvalue1 := dsp_server.NewLinkPay()
		err193 := argvalue1.Read(jsProt192)
		if err193 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddLinkPay(value0, value1))
		fmt.Print("\n")
		break
	case "editLinkPay":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditLinkPay requires 2 args")
			flag.Usage()
		}
		arg194 := flag.Arg(1)
		mbTrans195 := thrift.NewTMemoryBufferLen(len(arg194))
		defer mbTrans195.Close()
		_, err196 := mbTrans195.WriteString(arg194)
		if err196 != nil {
			Usage()
			return
		}
		factory197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt198 := factory197.GetProtocol(mbTrans195)
		argvalue0 := dsp_server.NewRequestHeader()
		err199 := argvalue0.Read(jsProt198)
		if err199 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg200 := flag.Arg(2)
		mbTrans201 := thrift.NewTMemoryBufferLen(len(arg200))
		defer mbTrans201.Close()
		_, err202 := mbTrans201.WriteString(arg200)
		if err202 != nil {
			Usage()
			return
		}
		factory203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt204 := factory203.GetProtocol(mbTrans201)
		argvalue1 := dsp_server.NewLinkPay()
		err205 := argvalue1.Read(jsProt204)
		if err205 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditLinkPay(value0, value1))
		fmt.Print("\n")
		break
	case "getLinkPaysByParam":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLinkPaysByParam requires 2 args")
			flag.Usage()
		}
		arg206 := flag.Arg(1)
		mbTrans207 := thrift.NewTMemoryBufferLen(len(arg206))
		defer mbTrans207.Close()
		_, err208 := mbTrans207.WriteString(arg206)
		if err208 != nil {
			Usage()
			return
		}
		factory209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt210 := factory209.GetProtocol(mbTrans207)
		argvalue0 := dsp_server.NewRequestHeader()
		err211 := argvalue0.Read(jsProt210)
		if err211 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg212 := flag.Arg(2)
		mbTrans213 := thrift.NewTMemoryBufferLen(len(arg212))
		defer mbTrans213.Close()
		_, err214 := mbTrans213.WriteString(arg212)
		if err214 != nil {
			Usage()
			return
		}
		factory215 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt216 := factory215.GetProtocol(mbTrans213)
		argvalue1 := dsp_server.NewLinkPayParams()
		err217 := argvalue1.Read(jsProt216)
		if err217 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetLinkPaysByParam(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
