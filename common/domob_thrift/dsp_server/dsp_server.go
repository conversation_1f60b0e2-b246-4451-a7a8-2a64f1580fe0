// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dsp_types"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dsp_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__

type DspServer interface { //DSP 服务接口定义

	// 添加DSP推广活动
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Campaign: 推广活动结构体
	AddCampaign(header *common.RequestHeader, campaign *dsp_types.DSPCampaign) (r int32, e *DspServerException, err error)
	// 编辑DSP推广活动
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Campaign: 推广活动结构体
	EditCampaign(header *common.RequestHeader, campaign *dsp_types.DSPCampaign) (e *DspServerException, err error)
	// 编辑活动排期信息
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Id: 需要更新的排期的活动ID *
	//  - Schedules: 需要更新的排期列表 *
	EditCampaignSchedule(header *common.RequestHeader, id int32, schedules []*dsp_types.DSPCampaignSchedule) (e *DspServerException, err error)
	// 批量获取推广活动列表
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 推广活动的编号列表 list, 最大允许一次请求100个活动 *
	GetCampaignsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dsp_types.DSPCampaign, e *DspServerException, err error)
	// 该接口主要提供给订单系统获取活动相关信息使用
	// 根据订单系统的排期ID获取所有关联的campaign ids
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 排期ID *
	//  - Offset: 偏移量 *
	//  - Limit: 数量限制, 最大支持100个 *
	ListCampaignsByScheduleIds(header *common.RequestHeader, ids []int32, offset int32, limit int32) (r *common.QueryResult, e *DspServerException, err error)
	// 检索活动信息,根据检索的参数返回结果
	// 返回活动ID列表
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Param: 查询参数 *
	QueryCampaignsByParam(header *common.RequestHeader, param *dsp_types.QueryParam) (r *common.QueryResult, e *DspServerException, err error)
	// 检索活动信息,根据检索的参数返回结果,增加排序支持
	// 返回活动ID列表
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Param: 查询参数 *
	//  - Order: 排序条件 *
	QueryCampaignsByParamWithOrder(header *common.RequestHeader, param *dsp_types.QueryParam, order *dsp_types.OrderParam) (r *common.QueryResult, e *DspServerException, err error)
	// 根据渠道类型和排期ID获取所有的活动信息
	// 该接口主要是给各个业务线使用
	// 结果默认按活动创建时间降序排序
	//
	// Parameters:
	//  - Header: 请求消息头结构休 *
	//  - SchIds: (订单系统中)排期ID列表,为空list表示不限制排期ID *
	//  - ChannelType: 渠道类型 *
	//  - Offset: 查询的偏移量 *
	//  - Limit: 查询数量限制,最大支持100个 *
	ListCampaignsBySchIdsAndChnType(header *common.RequestHeader, schIds []int32, channelType dsp_types.CampaignChannelType, offset int32, limit int32) (r []*dsp_types.DSPCampaign, e *DspServerException, err error)
	// 根据产品线类型和外部渠道ID获取所有的外采渠道信息
	//
	// Parameters:
	//  - Header: 请求消息头结构休 *
	//  - AffType: 外采渠道类型 *
	//  - ExternalIds: 外采渠道ID *
	//  - Offset: 查询的偏移量 *
	//  - Limit: 查询数量限制,最大支持100个 *
	ListAffiliateByAffTypeAndExternalIds(header *common.RequestHeader, affType dsp_types.CampaignChannelType, externalIds []int32, offset int32, limit int32) (r []*dsp_types.DSPAffiliate, e *DspServerException, err error)
	// 添加监测链接付费数据
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - LinkPay: 付费数据结构体
	AddLinkPay(header *common.RequestHeader, linkPay *dsp_types.LinkPay) (r int32, e *DspServerException, err error)
	// 编辑监测链接付费数据
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - LinkPay: 付费数据结构体
	EditLinkPay(header *common.RequestHeader, linkPay *dsp_types.LinkPay) (e *DspServerException, err error)
	// 检索付费信息,根据检索的参数返回结果
	// 返回付费数据列表
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Param: 查询参数 *
	GetLinkPaysByParam(header *common.RequestHeader, param *dsp_types.LinkPayParams) (r map[int32]*dsp_types.LinkPay, e *DspServerException, err error)
}

//DSP 服务接口定义
type DspServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDspServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DspServerClient {
	return &DspServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDspServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DspServerClient {
	return &DspServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 添加DSP推广活动
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Campaign: 推广活动结构体
func (p *DspServerClient) AddCampaign(header *common.RequestHeader, campaign *dsp_types.DSPCampaign) (r int32, e *DspServerException, err error) {
	if err = p.sendAddCampaign(header, campaign); err != nil {
		return
	}
	return p.recvAddCampaign()
}

func (p *DspServerClient) sendAddCampaign(header *common.RequestHeader, campaign *dsp_types.DSPCampaign) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addCampaign", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddCampaignArgs()
	args0.Header = header
	args0.Campaign = campaign
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvAddCampaign() (value int32, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddCampaignResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// 编辑DSP推广活动
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Campaign: 推广活动结构体
func (p *DspServerClient) EditCampaign(header *common.RequestHeader, campaign *dsp_types.DSPCampaign) (e *DspServerException, err error) {
	if err = p.sendEditCampaign(header, campaign); err != nil {
		return
	}
	return p.recvEditCampaign()
}

func (p *DspServerClient) sendEditCampaign(header *common.RequestHeader, campaign *dsp_types.DSPCampaign) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editCampaign", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewEditCampaignArgs()
	args4.Header = header
	args4.Campaign = campaign
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvEditCampaign() (e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewEditCampaignResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result5.E != nil {
		e = result5.E
	}
	return
}

// 编辑活动排期信息
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Id: 需要更新的排期的活动ID *
//  - Schedules: 需要更新的排期列表 *
func (p *DspServerClient) EditCampaignSchedule(header *common.RequestHeader, id int32, schedules []*dsp_types.DSPCampaignSchedule) (e *DspServerException, err error) {
	if err = p.sendEditCampaignSchedule(header, id, schedules); err != nil {
		return
	}
	return p.recvEditCampaignSchedule()
}

func (p *DspServerClient) sendEditCampaignSchedule(header *common.RequestHeader, id int32, schedules []*dsp_types.DSPCampaignSchedule) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editCampaignSchedule", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewEditCampaignScheduleArgs()
	args8.Header = header
	args8.Id = id
	args8.Schedules = schedules
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvEditCampaignSchedule() (e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewEditCampaignScheduleResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result9.E != nil {
		e = result9.E
	}
	return
}

// 批量获取推广活动列表
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 推广活动的编号列表 list, 最大允许一次请求100个活动 *
func (p *DspServerClient) GetCampaignsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dsp_types.DSPCampaign, e *DspServerException, err error) {
	if err = p.sendGetCampaignsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetCampaignsByIds()
}

func (p *DspServerClient) sendGetCampaignsByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCampaignsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetCampaignsByIdsArgs()
	args12.Header = header
	args12.Ids = ids
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvGetCampaignsByIds() (value map[int32]*dsp_types.DSPCampaign, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetCampaignsByIdsResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.E != nil {
		e = result13.E
	}
	return
}

// 该接口主要提供给订单系统获取活动相关信息使用
// 根据订单系统的排期ID获取所有关联的campaign ids
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 排期ID *
//  - Offset: 偏移量 *
//  - Limit: 数量限制, 最大支持100个 *
func (p *DspServerClient) ListCampaignsByScheduleIds(header *common.RequestHeader, ids []int32, offset int32, limit int32) (r *common.QueryResult, e *DspServerException, err error) {
	if err = p.sendListCampaignsByScheduleIds(header, ids, offset, limit); err != nil {
		return
	}
	return p.recvListCampaignsByScheduleIds()
}

func (p *DspServerClient) sendListCampaignsByScheduleIds(header *common.RequestHeader, ids []int32, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listCampaignsByScheduleIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewListCampaignsByScheduleIdsArgs()
	args16.Header = header
	args16.Ids = ids
	args16.Offset = offset
	args16.Limit = limit
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvListCampaignsByScheduleIds() (value *common.QueryResult, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewListCampaignsByScheduleIdsResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.E != nil {
		e = result17.E
	}
	return
}

// 检索活动信息,根据检索的参数返回结果
// 返回活动ID列表
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Param: 查询参数 *
func (p *DspServerClient) QueryCampaignsByParam(header *common.RequestHeader, param *dsp_types.QueryParam) (r *common.QueryResult, e *DspServerException, err error) {
	if err = p.sendQueryCampaignsByParam(header, param); err != nil {
		return
	}
	return p.recvQueryCampaignsByParam()
}

func (p *DspServerClient) sendQueryCampaignsByParam(header *common.RequestHeader, param *dsp_types.QueryParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryCampaignsByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewQueryCampaignsByParamArgs()
	args20.Header = header
	args20.Param = param
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvQueryCampaignsByParam() (value *common.QueryResult, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewQueryCampaignsByParamResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.E != nil {
		e = result21.E
	}
	return
}

// 检索活动信息,根据检索的参数返回结果,增加排序支持
// 返回活动ID列表
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Param: 查询参数 *
//  - Order: 排序条件 *
func (p *DspServerClient) QueryCampaignsByParamWithOrder(header *common.RequestHeader, param *dsp_types.QueryParam, order *dsp_types.OrderParam) (r *common.QueryResult, e *DspServerException, err error) {
	if err = p.sendQueryCampaignsByParamWithOrder(header, param, order); err != nil {
		return
	}
	return p.recvQueryCampaignsByParamWithOrder()
}

func (p *DspServerClient) sendQueryCampaignsByParamWithOrder(header *common.RequestHeader, param *dsp_types.QueryParam, order *dsp_types.OrderParam) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryCampaignsByParamWithOrder", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewQueryCampaignsByParamWithOrderArgs()
	args24.Header = header
	args24.Param = param
	args24.Order = order
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvQueryCampaignsByParamWithOrder() (value *common.QueryResult, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewQueryCampaignsByParamWithOrderResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.E != nil {
		e = result25.E
	}
	return
}

// 根据渠道类型和排期ID获取所有的活动信息
// 该接口主要是给各个业务线使用
// 结果默认按活动创建时间降序排序
//
// Parameters:
//  - Header: 请求消息头结构休 *
//  - SchIds: (订单系统中)排期ID列表,为空list表示不限制排期ID *
//  - ChannelType: 渠道类型 *
//  - Offset: 查询的偏移量 *
//  - Limit: 查询数量限制,最大支持100个 *
func (p *DspServerClient) ListCampaignsBySchIdsAndChnType(header *common.RequestHeader, schIds []int32, channelType dsp_types.CampaignChannelType, offset int32, limit int32) (r []*dsp_types.DSPCampaign, e *DspServerException, err error) {
	if err = p.sendListCampaignsBySchIdsAndChnType(header, schIds, channelType, offset, limit); err != nil {
		return
	}
	return p.recvListCampaignsBySchIdsAndChnType()
}

func (p *DspServerClient) sendListCampaignsBySchIdsAndChnType(header *common.RequestHeader, schIds []int32, channelType dsp_types.CampaignChannelType, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listCampaignsBySchIdsAndChnType", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewListCampaignsBySchIdsAndChnTypeArgs()
	args28.Header = header
	args28.SchIds = schIds
	args28.ChannelType = channelType
	args28.Offset = offset
	args28.Limit = limit
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvListCampaignsBySchIdsAndChnType() (value []*dsp_types.DSPCampaign, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewListCampaignsBySchIdsAndChnTypeResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.E != nil {
		e = result29.E
	}
	return
}

// 根据产品线类型和外部渠道ID获取所有的外采渠道信息
//
// Parameters:
//  - Header: 请求消息头结构休 *
//  - AffType: 外采渠道类型 *
//  - ExternalIds: 外采渠道ID *
//  - Offset: 查询的偏移量 *
//  - Limit: 查询数量限制,最大支持100个 *
func (p *DspServerClient) ListAffiliateByAffTypeAndExternalIds(header *common.RequestHeader, affType dsp_types.CampaignChannelType, externalIds []int32, offset int32, limit int32) (r []*dsp_types.DSPAffiliate, e *DspServerException, err error) {
	if err = p.sendListAffiliateByAffTypeAndExternalIds(header, affType, externalIds, offset, limit); err != nil {
		return
	}
	return p.recvListAffiliateByAffTypeAndExternalIds()
}

func (p *DspServerClient) sendListAffiliateByAffTypeAndExternalIds(header *common.RequestHeader, affType dsp_types.CampaignChannelType, externalIds []int32, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listAffiliateByAffTypeAndExternalIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewListAffiliateByAffTypeAndExternalIdsArgs()
	args32.Header = header
	args32.AffType = affType
	args32.ExternalIds = externalIds
	args32.Offset = offset
	args32.Limit = limit
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvListAffiliateByAffTypeAndExternalIds() (value []*dsp_types.DSPAffiliate, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewListAffiliateByAffTypeAndExternalIdsResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.E != nil {
		e = result33.E
	}
	return
}

// 添加监测链接付费数据
//
// Parameters:
//  - Header: 请求消息头结构体
//  - LinkPay: 付费数据结构体
func (p *DspServerClient) AddLinkPay(header *common.RequestHeader, linkPay *dsp_types.LinkPay) (r int32, e *DspServerException, err error) {
	if err = p.sendAddLinkPay(header, linkPay); err != nil {
		return
	}
	return p.recvAddLinkPay()
}

func (p *DspServerClient) sendAddLinkPay(header *common.RequestHeader, linkPay *dsp_types.LinkPay) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addLinkPay", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewAddLinkPayArgs()
	args36.Header = header
	args36.LinkPay = linkPay
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvAddLinkPay() (value int32, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewAddLinkPayResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.E != nil {
		e = result37.E
	}
	return
}

// 编辑监测链接付费数据
//
// Parameters:
//  - Header: 请求消息头结构体
//  - LinkPay: 付费数据结构体
func (p *DspServerClient) EditLinkPay(header *common.RequestHeader, linkPay *dsp_types.LinkPay) (e *DspServerException, err error) {
	if err = p.sendEditLinkPay(header, linkPay); err != nil {
		return
	}
	return p.recvEditLinkPay()
}

func (p *DspServerClient) sendEditLinkPay(header *common.RequestHeader, linkPay *dsp_types.LinkPay) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editLinkPay", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewEditLinkPayArgs()
	args40.Header = header
	args40.LinkPay = linkPay
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvEditLinkPay() (e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewEditLinkPayResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result41.E != nil {
		e = result41.E
	}
	return
}

// 检索付费信息,根据检索的参数返回结果
// 返回付费数据列表
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Param: 查询参数 *
func (p *DspServerClient) GetLinkPaysByParam(header *common.RequestHeader, param *dsp_types.LinkPayParams) (r map[int32]*dsp_types.LinkPay, e *DspServerException, err error) {
	if err = p.sendGetLinkPaysByParam(header, param); err != nil {
		return
	}
	return p.recvGetLinkPaysByParam()
}

func (p *DspServerClient) sendGetLinkPaysByParam(header *common.RequestHeader, param *dsp_types.LinkPayParams) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getLinkPaysByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetLinkPaysByParamArgs()
	args44.Header = header
	args44.Param = param
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DspServerClient) recvGetLinkPaysByParam() (value map[int32]*dsp_types.LinkPay, e *DspServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetLinkPaysByParamResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.E != nil {
		e = result45.E
	}
	return
}

type DspServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DspServer
}

func (p *DspServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DspServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DspServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDspServerProcessor(handler DspServer) *DspServerProcessor {

	self48 := &DspServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self48.processorMap["addCampaign"] = &dspServerProcessorAddCampaign{handler: handler}
	self48.processorMap["editCampaign"] = &dspServerProcessorEditCampaign{handler: handler}
	self48.processorMap["editCampaignSchedule"] = &dspServerProcessorEditCampaignSchedule{handler: handler}
	self48.processorMap["getCampaignsByIds"] = &dspServerProcessorGetCampaignsByIds{handler: handler}
	self48.processorMap["listCampaignsByScheduleIds"] = &dspServerProcessorListCampaignsByScheduleIds{handler: handler}
	self48.processorMap["queryCampaignsByParam"] = &dspServerProcessorQueryCampaignsByParam{handler: handler}
	self48.processorMap["queryCampaignsByParamWithOrder"] = &dspServerProcessorQueryCampaignsByParamWithOrder{handler: handler}
	self48.processorMap["listCampaignsBySchIdsAndChnType"] = &dspServerProcessorListCampaignsBySchIdsAndChnType{handler: handler}
	self48.processorMap["listAffiliateByAffTypeAndExternalIds"] = &dspServerProcessorListAffiliateByAffTypeAndExternalIds{handler: handler}
	self48.processorMap["addLinkPay"] = &dspServerProcessorAddLinkPay{handler: handler}
	self48.processorMap["editLinkPay"] = &dspServerProcessorEditLinkPay{handler: handler}
	self48.processorMap["getLinkPaysByParam"] = &dspServerProcessorGetLinkPaysByParam{handler: handler}
	return self48
}

func (p *DspServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x49 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x49.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x49

}

type dspServerProcessorAddCampaign struct {
	handler DspServer
}

func (p *dspServerProcessorAddCampaign) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCampaignArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addCampaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCampaignResult()
	if result.Success, result.E, err = p.handler.AddCampaign(args.Header, args.Campaign); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addCampaign: "+err.Error())
		oprot.WriteMessageBegin("addCampaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addCampaign", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorEditCampaign struct {
	handler DspServer
}

func (p *dspServerProcessorEditCampaign) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditCampaignArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editCampaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditCampaignResult()
	if result.E, err = p.handler.EditCampaign(args.Header, args.Campaign); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editCampaign: "+err.Error())
		oprot.WriteMessageBegin("editCampaign", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editCampaign", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorEditCampaignSchedule struct {
	handler DspServer
}

func (p *dspServerProcessorEditCampaignSchedule) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditCampaignScheduleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editCampaignSchedule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditCampaignScheduleResult()
	if result.E, err = p.handler.EditCampaignSchedule(args.Header, args.Id, args.Schedules); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editCampaignSchedule: "+err.Error())
		oprot.WriteMessageBegin("editCampaignSchedule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editCampaignSchedule", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorGetCampaignsByIds struct {
	handler DspServer
}

func (p *dspServerProcessorGetCampaignsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCampaignsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCampaignsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCampaignsByIdsResult()
	if result.Success, result.E, err = p.handler.GetCampaignsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCampaignsByIds: "+err.Error())
		oprot.WriteMessageBegin("getCampaignsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCampaignsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorListCampaignsByScheduleIds struct {
	handler DspServer
}

func (p *dspServerProcessorListCampaignsByScheduleIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListCampaignsByScheduleIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listCampaignsByScheduleIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListCampaignsByScheduleIdsResult()
	if result.Success, result.E, err = p.handler.ListCampaignsByScheduleIds(args.Header, args.Ids, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listCampaignsByScheduleIds: "+err.Error())
		oprot.WriteMessageBegin("listCampaignsByScheduleIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listCampaignsByScheduleIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorQueryCampaignsByParam struct {
	handler DspServer
}

func (p *dspServerProcessorQueryCampaignsByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryCampaignsByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryCampaignsByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryCampaignsByParamResult()
	if result.Success, result.E, err = p.handler.QueryCampaignsByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryCampaignsByParam: "+err.Error())
		oprot.WriteMessageBegin("queryCampaignsByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryCampaignsByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorQueryCampaignsByParamWithOrder struct {
	handler DspServer
}

func (p *dspServerProcessorQueryCampaignsByParamWithOrder) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryCampaignsByParamWithOrderArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryCampaignsByParamWithOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryCampaignsByParamWithOrderResult()
	if result.Success, result.E, err = p.handler.QueryCampaignsByParamWithOrder(args.Header, args.Param, args.Order); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryCampaignsByParamWithOrder: "+err.Error())
		oprot.WriteMessageBegin("queryCampaignsByParamWithOrder", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryCampaignsByParamWithOrder", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorListCampaignsBySchIdsAndChnType struct {
	handler DspServer
}

func (p *dspServerProcessorListCampaignsBySchIdsAndChnType) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListCampaignsBySchIdsAndChnTypeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listCampaignsBySchIdsAndChnType", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListCampaignsBySchIdsAndChnTypeResult()
	if result.Success, result.E, err = p.handler.ListCampaignsBySchIdsAndChnType(args.Header, args.SchIds, args.ChannelType, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listCampaignsBySchIdsAndChnType: "+err.Error())
		oprot.WriteMessageBegin("listCampaignsBySchIdsAndChnType", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listCampaignsBySchIdsAndChnType", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorListAffiliateByAffTypeAndExternalIds struct {
	handler DspServer
}

func (p *dspServerProcessorListAffiliateByAffTypeAndExternalIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListAffiliateByAffTypeAndExternalIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listAffiliateByAffTypeAndExternalIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListAffiliateByAffTypeAndExternalIdsResult()
	if result.Success, result.E, err = p.handler.ListAffiliateByAffTypeAndExternalIds(args.Header, args.AffType, args.ExternalIds, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listAffiliateByAffTypeAndExternalIds: "+err.Error())
		oprot.WriteMessageBegin("listAffiliateByAffTypeAndExternalIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listAffiliateByAffTypeAndExternalIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorAddLinkPay struct {
	handler DspServer
}

func (p *dspServerProcessorAddLinkPay) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddLinkPayArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addLinkPay", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddLinkPayResult()
	if result.Success, result.E, err = p.handler.AddLinkPay(args.Header, args.LinkPay); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addLinkPay: "+err.Error())
		oprot.WriteMessageBegin("addLinkPay", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addLinkPay", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorEditLinkPay struct {
	handler DspServer
}

func (p *dspServerProcessorEditLinkPay) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditLinkPayArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editLinkPay", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditLinkPayResult()
	if result.E, err = p.handler.EditLinkPay(args.Header, args.LinkPay); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editLinkPay: "+err.Error())
		oprot.WriteMessageBegin("editLinkPay", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editLinkPay", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dspServerProcessorGetLinkPaysByParam struct {
	handler DspServer
}

func (p *dspServerProcessorGetLinkPaysByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetLinkPaysByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getLinkPaysByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetLinkPaysByParamResult()
	if result.Success, result.E, err = p.handler.GetLinkPaysByParam(args.Header, args.Param); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getLinkPaysByParam: "+err.Error())
		oprot.WriteMessageBegin("getLinkPaysByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getLinkPaysByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddCampaignArgs struct {
	Header   *common.RequestHeader  `thrift:"header,1" json:"header"`
	Campaign *dsp_types.DSPCampaign `thrift:"campaign,2" json:"campaign"`
}

func NewAddCampaignArgs() *AddCampaignArgs {
	return &AddCampaignArgs{}
}

func (p *AddCampaignArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCampaignArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddCampaignArgs) readField2(iprot thrift.TProtocol) error {
	p.Campaign = dsp_types.NewDSPCampaign()
	if err := p.Campaign.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Campaign)
	}
	return nil
}

func (p *AddCampaignArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCampaign_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCampaignArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddCampaignArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Campaign != nil {
		if err := oprot.WriteFieldBegin("campaign", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:campaign: %s", p, err)
		}
		if err := p.Campaign.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Campaign)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:campaign: %s", p, err)
		}
	}
	return err
}

func (p *AddCampaignArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCampaignArgs(%+v)", *p)
}

type AddCampaignResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DspServerException `thrift:"e,1" json:"e"`
}

func NewAddCampaignResult() *AddCampaignResult {
	return &AddCampaignResult{}
}

func (p *AddCampaignResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCampaignResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddCampaignResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddCampaignResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCampaign_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCampaignResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddCampaignResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddCampaignResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCampaignResult(%+v)", *p)
}

type EditCampaignArgs struct {
	Header   *common.RequestHeader  `thrift:"header,1" json:"header"`
	Campaign *dsp_types.DSPCampaign `thrift:"campaign,2" json:"campaign"`
}

func NewEditCampaignArgs() *EditCampaignArgs {
	return &EditCampaignArgs{}
}

func (p *EditCampaignArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditCampaignArgs) readField2(iprot thrift.TProtocol) error {
	p.Campaign = dsp_types.NewDSPCampaign()
	if err := p.Campaign.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Campaign)
	}
	return nil
}

func (p *EditCampaignArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCampaign_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditCampaignArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Campaign != nil {
		if err := oprot.WriteFieldBegin("campaign", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:campaign: %s", p, err)
		}
		if err := p.Campaign.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Campaign)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:campaign: %s", p, err)
		}
	}
	return err
}

func (p *EditCampaignArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCampaignArgs(%+v)", *p)
}

type EditCampaignResult struct {
	E *DspServerException `thrift:"e,1" json:"e"`
}

func NewEditCampaignResult() *EditCampaignResult {
	return &EditCampaignResult{}
}

func (p *EditCampaignResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditCampaignResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCampaign_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditCampaignResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCampaignResult(%+v)", *p)
}

type EditCampaignScheduleArgs struct {
	Header    *common.RequestHeader            `thrift:"header,1" json:"header"`
	Id        int32                            `thrift:"id,2" json:"id"`
	Schedules []*dsp_types.DSPCampaignSchedule `thrift:"schedules,3" json:"schedules"`
}

func NewEditCampaignScheduleArgs() *EditCampaignScheduleArgs {
	return &EditCampaignScheduleArgs{}
}

func (p *EditCampaignScheduleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignScheduleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditCampaignScheduleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *EditCampaignScheduleArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Schedules = make([]*dsp_types.DSPCampaignSchedule, 0, size)
	for i := 0; i < size; i++ {
		_elem50 := dsp_types.NewDSPCampaignSchedule()
		if err := _elem50.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem50)
		}
		p.Schedules = append(p.Schedules, _elem50)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *EditCampaignScheduleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCampaignSchedule_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignScheduleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditCampaignScheduleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *EditCampaignScheduleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Schedules != nil {
		if err := oprot.WriteFieldBegin("schedules", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:schedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Schedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Schedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:schedules: %s", p, err)
		}
	}
	return err
}

func (p *EditCampaignScheduleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCampaignScheduleArgs(%+v)", *p)
}

type EditCampaignScheduleResult struct {
	E *DspServerException `thrift:"e,1" json:"e"`
}

func NewEditCampaignScheduleResult() *EditCampaignScheduleResult {
	return &EditCampaignScheduleResult{}
}

func (p *EditCampaignScheduleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignScheduleResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditCampaignScheduleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCampaignSchedule_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCampaignScheduleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditCampaignScheduleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCampaignScheduleResult(%+v)", *p)
}

type GetCampaignsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetCampaignsByIdsArgs() *GetCampaignsByIdsArgs {
	return &GetCampaignsByIdsArgs{}
}

func (p *GetCampaignsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCampaignsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetCampaignsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem51 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem51 = v
		}
		p.Ids = append(p.Ids, _elem51)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetCampaignsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCampaignsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCampaignsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetCampaignsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetCampaignsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCampaignsByIdsArgs(%+v)", *p)
}

type GetCampaignsByIdsResult struct {
	Success map[int32]*dsp_types.DSPCampaign `thrift:"success,0" json:"success"`
	E       *DspServerException              `thrift:"e,1" json:"e"`
}

func NewGetCampaignsByIdsResult() *GetCampaignsByIdsResult {
	return &GetCampaignsByIdsResult{}
}

func (p *GetCampaignsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCampaignsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dsp_types.DSPCampaign, size)
	for i := 0; i < size; i++ {
		var _key52 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key52 = v
		}
		_val53 := dsp_types.NewDSPCampaign()
		if err := _val53.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val53)
		}
		p.Success[_key52] = _val53
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetCampaignsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetCampaignsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCampaignsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCampaignsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCampaignsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetCampaignsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCampaignsByIdsResult(%+v)", *p)
}

type ListCampaignsByScheduleIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
	Offset int32                 `thrift:"offset,3" json:"offset"`
	Limit  int32                 `thrift:"limit,4" json:"limit"`
}

func NewListCampaignsByScheduleIdsArgs() *ListCampaignsByScheduleIdsArgs {
	return &ListCampaignsByScheduleIdsArgs{}
}

func (p *ListCampaignsByScheduleIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem54 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem54 = v
		}
		p.Ids = append(p.Ids, _elem54)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listCampaignsByScheduleIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsByScheduleIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsByScheduleIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListCampaignsByScheduleIdsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListCampaignsByScheduleIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCampaignsByScheduleIdsArgs(%+v)", *p)
}

type ListCampaignsByScheduleIdsResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DspServerException `thrift:"e,1" json:"e"`
}

func NewListCampaignsByScheduleIdsResult() *ListCampaignsByScheduleIdsResult {
	return &ListCampaignsByScheduleIdsResult{}
}

func (p *ListCampaignsByScheduleIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listCampaignsByScheduleIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsByScheduleIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsByScheduleIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsByScheduleIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCampaignsByScheduleIdsResult(%+v)", *p)
}

type QueryCampaignsByParamArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Param  *dsp_types.QueryParam `thrift:"param,2" json:"param"`
}

func NewQueryCampaignsByParamArgs() *QueryCampaignsByParamArgs {
	return &QueryCampaignsByParamArgs{}
}

func (p *QueryCampaignsByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryCampaignsByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dsp_types.NewQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *QueryCampaignsByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryCampaignsByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryCampaignsByParamArgs(%+v)", *p)
}

type QueryCampaignsByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DspServerException `thrift:"e,1" json:"e"`
}

func NewQueryCampaignsByParamResult() *QueryCampaignsByParamResult {
	return &QueryCampaignsByParamResult{}
}

func (p *QueryCampaignsByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryCampaignsByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryCampaignsByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryCampaignsByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryCampaignsByParamResult(%+v)", *p)
}

type QueryCampaignsByParamWithOrderArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Param  *dsp_types.QueryParam `thrift:"param,2" json:"param"`
	Order  *dsp_types.OrderParam `thrift:"order,3" json:"order"`
}

func NewQueryCampaignsByParamWithOrderArgs() *QueryCampaignsByParamWithOrderArgs {
	return &QueryCampaignsByParamWithOrderArgs{}
}

func (p *QueryCampaignsByParamWithOrderArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dsp_types.NewQueryParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderArgs) readField3(iprot thrift.TProtocol) error {
	p.Order = dsp_types.NewOrderParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryCampaignsByParamWithOrder_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamWithOrderArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamWithOrderArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamWithOrderArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryCampaignsByParamWithOrderArgs(%+v)", *p)
}

type QueryCampaignsByParamWithOrderResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DspServerException `thrift:"e,1" json:"e"`
}

func NewQueryCampaignsByParamWithOrderResult() *QueryCampaignsByParamWithOrderResult {
	return &QueryCampaignsByParamWithOrderResult{}
}

func (p *QueryCampaignsByParamWithOrderResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryCampaignsByParamWithOrder_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryCampaignsByParamWithOrderResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamWithOrderResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryCampaignsByParamWithOrderResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryCampaignsByParamWithOrderResult(%+v)", *p)
}

type ListCampaignsBySchIdsAndChnTypeArgs struct {
	Header      *common.RequestHeader         `thrift:"header,1" json:"header"`
	SchIds      []int32                       `thrift:"schIds,2" json:"schIds"`
	ChannelType dsp_types.CampaignChannelType `thrift:"channelType,3" json:"channelType"`
	Offset      int32                         `thrift:"offset,4" json:"offset"`
	Limit       int32                         `thrift:"limit,5" json:"limit"`
}

func NewListCampaignsBySchIdsAndChnTypeArgs() *ListCampaignsBySchIdsAndChnTypeArgs {
	return &ListCampaignsBySchIdsAndChnTypeArgs{
		ChannelType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) IsSetChannelType() bool {
	return int64(p.ChannelType) != math.MinInt32-1
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SchIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem55 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem55 = v
		}
		p.SchIds = append(p.SchIds, _elem55)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ChannelType = dsp_types.CampaignChannelType(v)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listCampaignsBySchIdsAndChnType_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SchIds != nil {
		if err := oprot.WriteFieldBegin("schIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:schIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SchIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SchIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:schIds: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannelType() {
		if err := oprot.WriteFieldBegin("channelType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:channelType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ChannelType)); err != nil {
			return fmt.Errorf("%T.channelType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:channelType: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *ListCampaignsBySchIdsAndChnTypeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCampaignsBySchIdsAndChnTypeArgs(%+v)", *p)
}

type ListCampaignsBySchIdsAndChnTypeResult struct {
	Success []*dsp_types.DSPCampaign `thrift:"success,0" json:"success"`
	E       *DspServerException      `thrift:"e,1" json:"e"`
}

func NewListCampaignsBySchIdsAndChnTypeResult() *ListCampaignsBySchIdsAndChnTypeResult {
	return &ListCampaignsBySchIdsAndChnTypeResult{}
}

func (p *ListCampaignsBySchIdsAndChnTypeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*dsp_types.DSPCampaign, 0, size)
	for i := 0; i < size; i++ {
		_elem56 := dsp_types.NewDSPCampaign()
		if err := _elem56.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem56)
		}
		p.Success = append(p.Success, _elem56)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listCampaignsBySchIdsAndChnType_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListCampaignsBySchIdsAndChnTypeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsBySchIdsAndChnTypeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ListCampaignsBySchIdsAndChnTypeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCampaignsBySchIdsAndChnTypeResult(%+v)", *p)
}

type ListAffiliateByAffTypeAndExternalIdsArgs struct {
	Header      *common.RequestHeader         `thrift:"header,1" json:"header"`
	AffType     dsp_types.CampaignChannelType `thrift:"affType,2" json:"affType"`
	ExternalIds []int32                       `thrift:"externalIds,3" json:"externalIds"`
	Offset      int32                         `thrift:"offset,4" json:"offset"`
	Limit       int32                         `thrift:"limit,5" json:"limit"`
}

func NewListAffiliateByAffTypeAndExternalIdsArgs() *ListAffiliateByAffTypeAndExternalIdsArgs {
	return &ListAffiliateByAffTypeAndExternalIdsArgs{
		AffType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) IsSetAffType() bool {
	return int64(p.AffType) != math.MinInt32-1
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AffType = dsp_types.CampaignChannelType(v)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExternalIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem57 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem57 = v
		}
		p.ExternalIds = append(p.ExternalIds, _elem57)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAffiliateByAffTypeAndExternalIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetAffType() {
		if err := oprot.WriteFieldBegin("affType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:affType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AffType)); err != nil {
			return fmt.Errorf("%T.affType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:affType: %s", p, err)
		}
	}
	return err
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ExternalIds != nil {
		if err := oprot.WriteFieldBegin("externalIds", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:externalIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ExternalIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExternalIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:externalIds: %s", p, err)
		}
	}
	return err
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *ListAffiliateByAffTypeAndExternalIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAffiliateByAffTypeAndExternalIdsArgs(%+v)", *p)
}

type ListAffiliateByAffTypeAndExternalIdsResult struct {
	Success []*dsp_types.DSPAffiliate `thrift:"success,0" json:"success"`
	E       *DspServerException       `thrift:"e,1" json:"e"`
}

func NewListAffiliateByAffTypeAndExternalIdsResult() *ListAffiliateByAffTypeAndExternalIdsResult {
	return &ListAffiliateByAffTypeAndExternalIdsResult{}
}

func (p *ListAffiliateByAffTypeAndExternalIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*dsp_types.DSPAffiliate, 0, size)
	for i := 0; i < size; i++ {
		_elem58 := dsp_types.NewDSPAffiliate()
		if err := _elem58.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem58)
		}
		p.Success = append(p.Success, _elem58)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAffiliateByAffTypeAndExternalIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAffiliateByAffTypeAndExternalIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListAffiliateByAffTypeAndExternalIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ListAffiliateByAffTypeAndExternalIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAffiliateByAffTypeAndExternalIdsResult(%+v)", *p)
}

type AddLinkPayArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	LinkPay *dsp_types.LinkPay    `thrift:"linkPay,2" json:"linkPay"`
}

func NewAddLinkPayArgs() *AddLinkPayArgs {
	return &AddLinkPayArgs{}
}

func (p *AddLinkPayArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddLinkPayArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddLinkPayArgs) readField2(iprot thrift.TProtocol) error {
	p.LinkPay = dsp_types.NewLinkPay()
	if err := p.LinkPay.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.LinkPay)
	}
	return nil
}

func (p *AddLinkPayArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addLinkPay_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddLinkPayArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddLinkPayArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.LinkPay != nil {
		if err := oprot.WriteFieldBegin("linkPay", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:linkPay: %s", p, err)
		}
		if err := p.LinkPay.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.LinkPay)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:linkPay: %s", p, err)
		}
	}
	return err
}

func (p *AddLinkPayArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddLinkPayArgs(%+v)", *p)
}

type AddLinkPayResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DspServerException `thrift:"e,1" json:"e"`
}

func NewAddLinkPayResult() *AddLinkPayResult {
	return &AddLinkPayResult{}
}

func (p *AddLinkPayResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddLinkPayResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddLinkPayResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddLinkPayResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addLinkPay_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddLinkPayResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddLinkPayResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddLinkPayResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddLinkPayResult(%+v)", *p)
}

type EditLinkPayArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	LinkPay *dsp_types.LinkPay    `thrift:"linkPay,2" json:"linkPay"`
}

func NewEditLinkPayArgs() *EditLinkPayArgs {
	return &EditLinkPayArgs{}
}

func (p *EditLinkPayArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditLinkPayArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditLinkPayArgs) readField2(iprot thrift.TProtocol) error {
	p.LinkPay = dsp_types.NewLinkPay()
	if err := p.LinkPay.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.LinkPay)
	}
	return nil
}

func (p *EditLinkPayArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editLinkPay_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditLinkPayArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditLinkPayArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.LinkPay != nil {
		if err := oprot.WriteFieldBegin("linkPay", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:linkPay: %s", p, err)
		}
		if err := p.LinkPay.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.LinkPay)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:linkPay: %s", p, err)
		}
	}
	return err
}

func (p *EditLinkPayArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditLinkPayArgs(%+v)", *p)
}

type EditLinkPayResult struct {
	E *DspServerException `thrift:"e,1" json:"e"`
}

func NewEditLinkPayResult() *EditLinkPayResult {
	return &EditLinkPayResult{}
}

func (p *EditLinkPayResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditLinkPayResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditLinkPayResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editLinkPay_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditLinkPayResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditLinkPayResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditLinkPayResult(%+v)", *p)
}

type GetLinkPaysByParamArgs struct {
	Header *common.RequestHeader    `thrift:"header,1" json:"header"`
	Param  *dsp_types.LinkPayParams `thrift:"param,2" json:"param"`
}

func NewGetLinkPaysByParamArgs() *GetLinkPaysByParamArgs {
	return &GetLinkPaysByParamArgs{}
}

func (p *GetLinkPaysByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLinkPaysByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetLinkPaysByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = dsp_types.NewLinkPayParams()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *GetLinkPaysByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLinkPaysByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLinkPaysByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetLinkPaysByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *GetLinkPaysByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLinkPaysByParamArgs(%+v)", *p)
}

type GetLinkPaysByParamResult struct {
	Success map[int32]*dsp_types.LinkPay `thrift:"success,0" json:"success"`
	E       *DspServerException          `thrift:"e,1" json:"e"`
}

func NewGetLinkPaysByParamResult() *GetLinkPaysByParamResult {
	return &GetLinkPaysByParamResult{}
}

func (p *GetLinkPaysByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLinkPaysByParamResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dsp_types.LinkPay, size)
	for i := 0; i < size; i++ {
		var _key59 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key59 = v
		}
		_val60 := dsp_types.NewLinkPay()
		if err := _val60.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val60)
		}
		p.Success[_key59] = _val60
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetLinkPaysByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDspServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetLinkPaysByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLinkPaysByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLinkPaysByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetLinkPaysByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetLinkPaysByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLinkPaysByParamResult(%+v)", *p)
}
