// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dsp_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dsp_types"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dsp_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

type ExceptionCode int64

const (
	ExceptionCode_CAMPAIGN_ERROR            ExceptionCode = 10000
	ExceptionCode_CAMPAIGN_NOT_EXISTS       ExceptionCode = 10001
	ExceptionCode_CAMPAIGN_SCHEDULEID_EXIST ExceptionCode = 10002
	ExceptionCode_CAMPAIGN_VALIDATE_ERROR   ExceptionCode = 10005
	ExceptionCode_ADD_CAMPAIGN_ERROR        ExceptionCode = 10006
	ExceptionCode_EDIT_CAMPAIGN_ERROR       ExceptionCode = 10007
	ExceptionCode_QUERY_CAMPAIGN_ERROR      ExceptionCode = 10008
	ExceptionCode_ADD_LINKPAY_ERROR         ExceptionCode = 10010
	ExceptionCode_EDIT_LINKPAY_ERROR        ExceptionCode = 10011
	ExceptionCode_LINKPAY_NOT_EXISTS        ExceptionCode = 10012
	ExceptionCode_QUERY_LINKPAY_ERROR       ExceptionCode = 10013
	ExceptionCode_SYSTEM_ERROR              ExceptionCode = 20000
)

func (p ExceptionCode) String() string {
	switch p {
	case ExceptionCode_CAMPAIGN_ERROR:
		return "ExceptionCode_CAMPAIGN_ERROR"
	case ExceptionCode_CAMPAIGN_NOT_EXISTS:
		return "ExceptionCode_CAMPAIGN_NOT_EXISTS"
	case ExceptionCode_CAMPAIGN_SCHEDULEID_EXIST:
		return "ExceptionCode_CAMPAIGN_SCHEDULEID_EXIST"
	case ExceptionCode_CAMPAIGN_VALIDATE_ERROR:
		return "ExceptionCode_CAMPAIGN_VALIDATE_ERROR"
	case ExceptionCode_ADD_CAMPAIGN_ERROR:
		return "ExceptionCode_ADD_CAMPAIGN_ERROR"
	case ExceptionCode_EDIT_CAMPAIGN_ERROR:
		return "ExceptionCode_EDIT_CAMPAIGN_ERROR"
	case ExceptionCode_QUERY_CAMPAIGN_ERROR:
		return "ExceptionCode_QUERY_CAMPAIGN_ERROR"
	case ExceptionCode_ADD_LINKPAY_ERROR:
		return "ExceptionCode_ADD_LINKPAY_ERROR"
	case ExceptionCode_EDIT_LINKPAY_ERROR:
		return "ExceptionCode_EDIT_LINKPAY_ERROR"
	case ExceptionCode_LINKPAY_NOT_EXISTS:
		return "ExceptionCode_LINKPAY_NOT_EXISTS"
	case ExceptionCode_QUERY_LINKPAY_ERROR:
		return "ExceptionCode_QUERY_LINKPAY_ERROR"
	case ExceptionCode_SYSTEM_ERROR:
		return "ExceptionCode_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func ExceptionCodeFromString(s string) (ExceptionCode, error) {
	switch s {
	case "ExceptionCode_CAMPAIGN_ERROR":
		return ExceptionCode_CAMPAIGN_ERROR, nil
	case "ExceptionCode_CAMPAIGN_NOT_EXISTS":
		return ExceptionCode_CAMPAIGN_NOT_EXISTS, nil
	case "ExceptionCode_CAMPAIGN_SCHEDULEID_EXIST":
		return ExceptionCode_CAMPAIGN_SCHEDULEID_EXIST, nil
	case "ExceptionCode_CAMPAIGN_VALIDATE_ERROR":
		return ExceptionCode_CAMPAIGN_VALIDATE_ERROR, nil
	case "ExceptionCode_ADD_CAMPAIGN_ERROR":
		return ExceptionCode_ADD_CAMPAIGN_ERROR, nil
	case "ExceptionCode_EDIT_CAMPAIGN_ERROR":
		return ExceptionCode_EDIT_CAMPAIGN_ERROR, nil
	case "ExceptionCode_QUERY_CAMPAIGN_ERROR":
		return ExceptionCode_QUERY_CAMPAIGN_ERROR, nil
	case "ExceptionCode_ADD_LINKPAY_ERROR":
		return ExceptionCode_ADD_LINKPAY_ERROR, nil
	case "ExceptionCode_EDIT_LINKPAY_ERROR":
		return ExceptionCode_EDIT_LINKPAY_ERROR, nil
	case "ExceptionCode_LINKPAY_NOT_EXISTS":
		return ExceptionCode_LINKPAY_NOT_EXISTS, nil
	case "ExceptionCode_QUERY_LINKPAY_ERROR":
		return ExceptionCode_QUERY_LINKPAY_ERROR, nil
	case "ExceptionCode_SYSTEM_ERROR":
		return ExceptionCode_SYSTEM_ERROR, nil
	}
	return ExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid ExceptionCode string")
}

type DspServerException struct {
	Code    ExceptionCode `thrift:"code,1" json:"code"`
	Message string        `thrift:"message,2" json:"message"`
}

func NewDspServerException() *DspServerException {
	return &DspServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DspServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DspServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DspServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ExceptionCode(v)
	}
	return nil
}

func (p *DspServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DspServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DspServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DspServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DspServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *DspServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DspServerException(%+v)", *p)
}
