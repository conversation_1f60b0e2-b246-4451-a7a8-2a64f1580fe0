// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_debug_info

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//RTB的广告过滤类型
type RTBAdFilterType int64

const (
	RTBAdFilterType_RTB_FILTER_UNKNOWN                    RTBAdFilterType = 0
	RTBAdFilterType_RTB_FILTER_CAMPAIGN_TIME              RTBAdFilterType = 200
	RTBAdFilterType_RTB_FILTER_CAMPAIGN_INFO_NULL         RTBAdFilterType = 201
	RTBAdFilterType_RTB_FILTER_STRATEGY_EXCHANGE          RTBAdFilterType = 300
	RTBAdFilterType_RTB_FILTER_STRATEGY_DEVICE            RTBAdFilterType = 301
	RTBAdFilterType_RTB_FILTER_STRATEGY_ACCESS            RTBAdFilterType = 302
	RTBAdFilterType_RTB_FILTER_STRATEGY_CARRIER           RTBAdFilterType = 303
	RTBAdFilterType_RTB_FILTER_STRATEGY_REGION            RTBAdFilterType = 304
	RTBAdFilterType_RTB_FILTER_STRATEGY_TIME              RTBAdFilterType = 305
	RTBAdFilterType_RTB_FILTER_STRATEGY_PLATFORM          RTBAdFilterType = 306
	RTBAdFilterType_RTB_FILTER_STRATEGY_CHANNEL           RTBAdFilterType = 307
	RTBAdFilterType_RTB_FILTER_STRATEGY_CATEGORY          RTBAdFilterType = 308
	RTBAdFilterType_RTB_FILTER_STRATEGY_POI               RTBAdFilterType = 309
	RTBAdFilterType_RTB_FILTER_STRATEGY_WIFI              RTBAdFilterType = 310
	RTBAdFilterType_RTB_FILTER_STRATEGY_TAG               RTBAdFilterType = 311
	RTBAdFilterType_RTB_FILTER_STRATEGY_BUDGET            RTBAdFilterType = 312
	RTBAdFilterType_RTB_FILTER_STRATEGY_MEDIA             RTBAdFilterType = 313
	RTBAdFilterType_RTB_FILTER_STRATEGY_USER_DEVICE       RTBAdFilterType = 314
	RTBAdFilterType_RTB_FILTER_STRATEGY_BID_TIMES         RTBAdFilterType = 315
	RTBAdFilterType_RTB_FILTER_STRATEGY_REST_BUDGET       RTBAdFilterType = 316
	RTBAdFilterType_RTB_FILTER_STRATEGY_SPEED_RATE        RTBAdFilterType = 317
	RTBAdFilterType_RTB_FILTER_STRATEGY_RAND              RTBAdFilterType = 318
	RTBAdFilterType_RTB_FILTER_STRATEGY_INFO_NULL         RTBAdFilterType = 319
	RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_NULL      RTBAdFilterType = 320
	RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_BLACKLIST RTBAdFilterType = 321
	RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_UNIQ      RTBAdFilterType = 322
	RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_FREQ      RTBAdFilterType = 323
	RTBAdFilterType_RTB_FILTER_CREATIVE_TIME              RTBAdFilterType = 400
	RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER         RTBAdFilterType = 401
	RTBAdFilterType_RTB_FILTER_CREATIVE_DURATION          RTBAdFilterType = 402
	RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_BIG       RTBAdFilterType = 403
	RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_REQ       RTBAdFilterType = 404
	RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_ABANDON   RTBAdFilterType = 405
	RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_NUM           RTBAdFilterType = 406
	RTBAdFilterType_RTB_FILTER_CREATIVE_CTR               RTBAdFilterType = 407
	RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE             RTBAdFilterType = 408
	RTBAdFilterType_RTB_FILTER_CREATIVE_BID               RTBAdFilterType = 409
	RTBAdFilterType_RTB_FILTER_CREATIVE_EXP               RTBAdFilterType = 410
	RTBAdFilterType_RTB_FILTER_CREATIVE_DVOS              RTBAdFilterType = 411
	RTBAdFilterType_RTB_FILTER_CREATIVE_IMP_FREQ          RTBAdFilterType = 412
	RTBAdFilterType_RTB_FILTER_CREATIVE_DEVICE_ID         RTBAdFilterType = 413
	RTBAdFilterType_RTB_FILTER_CREATIVE_PROMOTION_NUM     RTBAdFilterType = 414
	RTBAdFilterType_RTB_FILTER_CREATIVE_CTR_TARGET        RTBAdFilterType = 415
	RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_LIMIT       RTBAdFilterType = 416
	RTBAdFilterType_RTB_FILTER_CREATIVE_DETECT_SPEED      RTBAdFilterType = 417
	RTBAdFilterType_RTB_FILTER_CREATIVE_BID_FLOOR         RTBAdFilterType = 418
	RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_ATR         RTBAdFilterType = 419
	RTBAdFilterType_RTB_FILTER_CREATIVE_APP_IOS           RTBAdFilterType = 420
	RTBAdFilterType_RTB_FILTER_CREATIVE_BLACK             RTBAdFilterType = 421
	RTBAdFilterType_RTB_FILTER_CREATIVE_HIGH_QUALITY      RTBAdFilterType = 422
	RTBAdFilterType_RTB_FILTER_CREATIVE_DATA_STATE        RTBAdFilterType = 423
	RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_QUALITY       RTBAdFilterType = 424
	RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_DAY           RTBAdFilterType = 425
	RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_LIMIT         RTBAdFilterType = 426
	RTBAdFilterType_RTB_FILTER_CREATIVE_DT_SOURCE         RTBAdFilterType = 427
	RTBAdFilterType_RTB_FILTER_CREATIVE_RAND              RTBAdFilterType = 428
	RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE_ADJUST      RTBAdFilterType = 429
	RTBAdFilterType_RTB_FILTER_CREATIVE_OPT_PRICE         RTBAdFilterType = 430
	RTBAdFilterType_RTB_FILTER_CREATIVE_INFO_NULL         RTBAdFilterType = 431
	RTBAdFilterType_RTB_FILTER_CREATIVE_GET_CONTAINER     RTBAdFilterType = 432
	RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER_NUM     RTBAdFilterType = 433
	RTBAdFilterType_RTB_FILTER_CREATIVE_ADD_INFO          RTBAdFilterType = 434
)

func (p RTBAdFilterType) String() string {
	switch p {
	case RTBAdFilterType_RTB_FILTER_UNKNOWN:
		return "RTBAdFilterType_RTB_FILTER_UNKNOWN"
	case RTBAdFilterType_RTB_FILTER_CAMPAIGN_TIME:
		return "RTBAdFilterType_RTB_FILTER_CAMPAIGN_TIME"
	case RTBAdFilterType_RTB_FILTER_CAMPAIGN_INFO_NULL:
		return "RTBAdFilterType_RTB_FILTER_CAMPAIGN_INFO_NULL"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_EXCHANGE:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_EXCHANGE"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_DEVICE:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_DEVICE"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_ACCESS:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_ACCESS"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_CARRIER:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_CARRIER"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_REGION:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_REGION"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_TIME:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_TIME"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_PLATFORM:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_PLATFORM"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_CHANNEL:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_CHANNEL"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_CATEGORY:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_CATEGORY"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_POI:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_POI"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_WIFI:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_WIFI"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_TAG:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_TAG"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_BUDGET:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_BUDGET"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_MEDIA:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_MEDIA"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_USER_DEVICE:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_USER_DEVICE"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_BID_TIMES:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_BID_TIMES"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_REST_BUDGET:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_REST_BUDGET"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_SPEED_RATE:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_SPEED_RATE"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_RAND:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_RAND"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_INFO_NULL:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_INFO_NULL"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_NULL:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_NULL"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_BLACKLIST:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_BLACKLIST"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_UNIQ:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_UNIQ"
	case RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_FREQ:
		return "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_FREQ"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_TIME:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_TIME"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_DURATION:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_DURATION"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_BIG:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_BIG"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_REQ:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_REQ"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_ABANDON:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_ABANDON"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_NUM:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_NUM"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_CTR:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_CTR"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_BID:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_BID"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_EXP:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_EXP"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_DVOS:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_DVOS"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_IMP_FREQ:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_IMP_FREQ"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_DEVICE_ID:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_DEVICE_ID"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_PROMOTION_NUM:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_PROMOTION_NUM"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_CTR_TARGET:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_CTR_TARGET"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_LIMIT:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_LIMIT"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_DETECT_SPEED:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_DETECT_SPEED"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_BID_FLOOR:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_BID_FLOOR"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_ATR:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_ATR"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_APP_IOS:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_APP_IOS"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_BLACK:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_BLACK"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_HIGH_QUALITY:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_HIGH_QUALITY"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_DATA_STATE:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_DATA_STATE"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_QUALITY:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_QUALITY"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_DAY:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_DAY"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_LIMIT:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_LIMIT"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_DT_SOURCE:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_DT_SOURCE"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_RAND:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_RAND"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE_ADJUST:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE_ADJUST"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_OPT_PRICE:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_OPT_PRICE"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_INFO_NULL:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_INFO_NULL"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_GET_CONTAINER:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_GET_CONTAINER"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER_NUM:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER_NUM"
	case RTBAdFilterType_RTB_FILTER_CREATIVE_ADD_INFO:
		return "RTBAdFilterType_RTB_FILTER_CREATIVE_ADD_INFO"
	}
	return "<UNSET>"
}

func RTBAdFilterTypeFromString(s string) (RTBAdFilterType, error) {
	switch s {
	case "RTBAdFilterType_RTB_FILTER_UNKNOWN":
		return RTBAdFilterType_RTB_FILTER_UNKNOWN, nil
	case "RTBAdFilterType_RTB_FILTER_CAMPAIGN_TIME":
		return RTBAdFilterType_RTB_FILTER_CAMPAIGN_TIME, nil
	case "RTBAdFilterType_RTB_FILTER_CAMPAIGN_INFO_NULL":
		return RTBAdFilterType_RTB_FILTER_CAMPAIGN_INFO_NULL, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_EXCHANGE":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_EXCHANGE, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_DEVICE":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_DEVICE, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_ACCESS":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_ACCESS, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_CARRIER":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_CARRIER, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_REGION":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_REGION, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_TIME":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_TIME, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_PLATFORM":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_PLATFORM, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_CHANNEL":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_CHANNEL, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_CATEGORY":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_CATEGORY, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_POI":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_POI, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_WIFI":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_WIFI, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_TAG":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_TAG, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_BUDGET":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_BUDGET, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_MEDIA":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_MEDIA, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_USER_DEVICE":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_USER_DEVICE, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_BID_TIMES":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_BID_TIMES, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_REST_BUDGET":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_REST_BUDGET, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_SPEED_RATE":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_SPEED_RATE, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_RAND":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_RAND, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_INFO_NULL":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_INFO_NULL, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_NULL":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_NULL, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_BLACKLIST":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_BLACKLIST, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_UNIQ":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_UNIQ, nil
	case "RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_FREQ":
		return RTBAdFilterType_RTB_FILTER_STRATEGY_SPONSOR_FREQ, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_TIME":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_TIME, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_DURATION":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_DURATION, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_BIG":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_BIG, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_REQ":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_REQ, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_ABANDON":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_TOUTIAO_ABANDON, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_NUM":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_NUM, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_CTR":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_CTR, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_BID":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_BID, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_EXP":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_EXP, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_DVOS":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_DVOS, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_IMP_FREQ":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_IMP_FREQ, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_DEVICE_ID":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_DEVICE_ID, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_PROMOTION_NUM":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_PROMOTION_NUM, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_CTR_TARGET":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_CTR_TARGET, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_LIMIT":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_LIMIT, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_DETECT_SPEED":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_DETECT_SPEED, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_BID_FLOOR":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_BID_FLOOR, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_ATR":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_ACOST_ATR, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_APP_IOS":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_APP_IOS, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_BLACK":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_BLACK, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_HIGH_QUALITY":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_HIGH_QUALITY, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_DATA_STATE":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_DATA_STATE, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_QUALITY":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_QUALITY, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_DAY":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_NEW_DAY, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_LIMIT":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_MAX_LIMIT, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_DT_SOURCE":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_DT_SOURCE, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_RAND":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_RAND, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE_ADJUST":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_PRICE_ADJUST, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_OPT_PRICE":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_OPT_PRICE, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_INFO_NULL":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_INFO_NULL, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_GET_CONTAINER":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_GET_CONTAINER, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER_NUM":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_CONTAINER_NUM, nil
	case "RTBAdFilterType_RTB_FILTER_CREATIVE_ADD_INFO":
		return RTBAdFilterType_RTB_FILTER_CREATIVE_ADD_INFO, nil
	}
	return RTBAdFilterType(math.MinInt32 - 1), fmt.Errorf("not a valid RTBAdFilterType string")
}
