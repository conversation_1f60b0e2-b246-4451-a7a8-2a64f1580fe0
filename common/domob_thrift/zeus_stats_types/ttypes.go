// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package zeus_stats_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/zeus_common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = zeus_common.GoUnusedProtection__
var GoUnusedProtection__ int

type ZeusMetricOrderType int64

const (
	ZeusMetricOrderType_TOP_ABSOLUTE    ZeusMetricOrderType = 1
	ZeusMetricOrderType_TOP_RELATIVE    ZeusMetricOrderType = 2
	ZeusMetricOrderType_BOTTOM_ABSOLUTE ZeusMetricOrderType = 3
	ZeusMetricOrderType_BOTTOM_RELATIVE ZeusMetricOrderType = 4
	ZeusMetricOrderType_VALUE_ABSOLUTE  ZeusMetricOrderType = 5
)

func (p ZeusMetricOrderType) String() string {
	switch p {
	case ZeusMetricOrderType_TOP_ABSOLUTE:
		return "ZeusMetricOrderType_TOP_ABSOLUTE"
	case ZeusMetricOrderType_TOP_RELATIVE:
		return "ZeusMetricOrderType_TOP_RELATIVE"
	case ZeusMetricOrderType_BOTTOM_ABSOLUTE:
		return "ZeusMetricOrderType_BOTTOM_ABSOLUTE"
	case ZeusMetricOrderType_BOTTOM_RELATIVE:
		return "ZeusMetricOrderType_BOTTOM_RELATIVE"
	case ZeusMetricOrderType_VALUE_ABSOLUTE:
		return "ZeusMetricOrderType_VALUE_ABSOLUTE"
	}
	return "<UNSET>"
}

func ZeusMetricOrderTypeFromString(s string) (ZeusMetricOrderType, error) {
	switch s {
	case "ZeusMetricOrderType_TOP_ABSOLUTE":
		return ZeusMetricOrderType_TOP_ABSOLUTE, nil
	case "ZeusMetricOrderType_TOP_RELATIVE":
		return ZeusMetricOrderType_TOP_RELATIVE, nil
	case "ZeusMetricOrderType_BOTTOM_ABSOLUTE":
		return ZeusMetricOrderType_BOTTOM_ABSOLUTE, nil
	case "ZeusMetricOrderType_BOTTOM_RELATIVE":
		return ZeusMetricOrderType_BOTTOM_RELATIVE, nil
	case "ZeusMetricOrderType_VALUE_ABSOLUTE":
		return ZeusMetricOrderType_VALUE_ABSOLUTE, nil
	}
	return ZeusMetricOrderType(math.MinInt32 - 1), fmt.Errorf("not a valid ZeusMetricOrderType string")
}

//返回数据的粒度
type ZeusDateType int64

const (
	ZeusDateType_HR ZeusDateType = 0
	ZeusDateType_DT ZeusDateType = 1
	ZeusDateType_MH ZeusDateType = 2
)

func (p ZeusDateType) String() string {
	switch p {
	case ZeusDateType_HR:
		return "ZeusDateType_HR"
	case ZeusDateType_DT:
		return "ZeusDateType_DT"
	case ZeusDateType_MH:
		return "ZeusDateType_MH"
	}
	return "<UNSET>"
}

func ZeusDateTypeFromString(s string) (ZeusDateType, error) {
	switch s {
	case "ZeusDateType_HR":
		return ZeusDateType_HR, nil
	case "ZeusDateType_DT":
		return ZeusDateType_DT, nil
	case "ZeusDateType_MH":
		return ZeusDateType_MH, nil
	}
	return ZeusDateType(math.MinInt32 - 1), fmt.Errorf("not a valid ZeusDateType string")
}

type ZeusCampaignData struct {
	MobileAppInstall int64 `thrift:"mobile_app_install,1" json:"mobile_app_install"`
	Impressions      int64 `thrift:"impressions,2" json:"impressions"`
	Clicks           int64 `thrift:"clicks,3" json:"clicks"`
	Spent            int64 `thrift:"spent,4" json:"spent"`
	Income           int64 `thrift:"income,5" json:"income"`
	Roi              int32 `thrift:"roi,6" json:"roi"`
	Ecpa             int32 `thrift:"ecpa,7" json:"ecpa"`
}

func NewZeusCampaignData() *ZeusCampaignData {
	return &ZeusCampaignData{}
}

func (p *ZeusCampaignData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusCampaignData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MobileAppInstall = v
	}
	return nil
}

func (p *ZeusCampaignData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Impressions = v
	}
	return nil
}

func (p *ZeusCampaignData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Clicks = v
	}
	return nil
}

func (p *ZeusCampaignData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Spent = v
	}
	return nil
}

func (p *ZeusCampaignData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Income = v
	}
	return nil
}

func (p *ZeusCampaignData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Roi = v
	}
	return nil
}

func (p *ZeusCampaignData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ecpa = v
	}
	return nil
}

func (p *ZeusCampaignData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusCampaignData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusCampaignData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile_app_install", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mobile_app_install: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MobileAppInstall)); err != nil {
		return fmt.Errorf("%T.mobile_app_install (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mobile_app_install: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressions", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:impressions: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Impressions)); err != nil {
		return fmt.Errorf("%T.impressions (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:impressions: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clicks", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:clicks: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clicks)); err != nil {
		return fmt.Errorf("%T.clicks (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:clicks: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spent", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:spent: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Spent)); err != nil {
		return fmt.Errorf("%T.spent (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:spent: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:income: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Income)); err != nil {
		return fmt.Errorf("%T.income (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:income: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("roi", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:roi: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Roi)); err != nil {
		return fmt.Errorf("%T.roi (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:roi: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ecpa", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ecpa: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ecpa)); err != nil {
		return fmt.Errorf("%T.ecpa (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ecpa: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusCampaignData(%+v)", *p)
}

type ZeusAdData struct {
	MobileAppInstall int64 `thrift:"mobile_app_install,1" json:"mobile_app_install"`
	Impressions      int64 `thrift:"impressions,2" json:"impressions"`
	Clicks           int64 `thrift:"clicks,3" json:"clicks"`
	Spent            int64 `thrift:"spent,4" json:"spent"`
	Income           int64 `thrift:"income,5" json:"income"`
	Ctr              int32 `thrift:"ctr,6" json:"ctr"`
	Cvr              int32 `thrift:"cvr,7" json:"cvr"`
	Ecpa             int32 `thrift:"ecpa,8" json:"ecpa"`
	PostLike         int64 `thrift:"post_like,9" json:"post_like"`
}

func NewZeusAdData() *ZeusAdData {
	return &ZeusAdData{}
}

func (p *ZeusAdData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MobileAppInstall = v
	}
	return nil
}

func (p *ZeusAdData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Impressions = v
	}
	return nil
}

func (p *ZeusAdData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Clicks = v
	}
	return nil
}

func (p *ZeusAdData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Spent = v
	}
	return nil
}

func (p *ZeusAdData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Income = v
	}
	return nil
}

func (p *ZeusAdData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Ctr = v
	}
	return nil
}

func (p *ZeusAdData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Cvr = v
	}
	return nil
}

func (p *ZeusAdData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Ecpa = v
	}
	return nil
}

func (p *ZeusAdData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PostLike = v
	}
	return nil
}

func (p *ZeusAdData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAdData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile_app_install", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mobile_app_install: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MobileAppInstall)); err != nil {
		return fmt.Errorf("%T.mobile_app_install (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mobile_app_install: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressions", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:impressions: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Impressions)); err != nil {
		return fmt.Errorf("%T.impressions (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:impressions: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clicks", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:clicks: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clicks)); err != nil {
		return fmt.Errorf("%T.clicks (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:clicks: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spent", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:spent: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Spent)); err != nil {
		return fmt.Errorf("%T.spent (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:spent: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:income: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Income)); err != nil {
		return fmt.Errorf("%T.income (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:income: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctr", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ctr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ctr)); err != nil {
		return fmt.Errorf("%T.ctr (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ctr: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cvr", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:cvr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cvr)); err != nil {
		return fmt.Errorf("%T.cvr (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:cvr: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ecpa", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ecpa: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ecpa)); err != nil {
		return fmt.Errorf("%T.ecpa (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ecpa: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("post_like", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:post_like: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PostLike)); err != nil {
		return fmt.Errorf("%T.post_like (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:post_like: %s", p, err)
	}
	return err
}

func (p *ZeusAdData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAdData(%+v)", *p)
}

type ZeusCampaignStatsData struct {
	Id           int64             `thrift:"id,1" json:"id"`
	Targeting    string            `thrift:"targeting,2" json:"targeting"`
	CampaignData *ZeusCampaignData `thrift:"campaign_data,3" json:"campaign_data"`
}

func NewZeusCampaignStatsData() *ZeusCampaignStatsData {
	return &ZeusCampaignStatsData{}
}

func (p *ZeusCampaignStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusCampaignStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ZeusCampaignStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Targeting = v
	}
	return nil
}

func (p *ZeusCampaignStatsData) readField3(iprot thrift.TProtocol) error {
	p.CampaignData = NewZeusCampaignData()
	if err := p.CampaignData.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CampaignData)
	}
	return nil
}

func (p *ZeusCampaignStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusCampaignStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusCampaignStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("targeting", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:targeting: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Targeting)); err != nil {
		return fmt.Errorf("%T.targeting (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:targeting: %s", p, err)
	}
	return err
}

func (p *ZeusCampaignStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if p.CampaignData != nil {
		if err := oprot.WriteFieldBegin("campaign_data", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:campaign_data: %s", p, err)
		}
		if err := p.CampaignData.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CampaignData)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:campaign_data: %s", p, err)
		}
	}
	return err
}

func (p *ZeusCampaignStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusCampaignStatsData(%+v)", *p)
}

type ZeusAdStatsData struct {
	Id        int64       `thrift:"id,1" json:"id"`
	Targeting string      `thrift:"targeting,2" json:"targeting"`
	AdData    *ZeusAdData `thrift:"ad_data,3" json:"ad_data"`
	Ids       []int64     `thrift:"ids,4" json:"ids"`
	Aid       int64       `thrift:"aid,5" json:"aid"`
	Uid       int64       `thrift:"uid,6" json:"uid"`
}

func NewZeusAdStatsData() *ZeusAdStatsData {
	return &ZeusAdStatsData{}
}

func (p *ZeusAdStatsData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdStatsData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ZeusAdStatsData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Targeting = v
	}
	return nil
}

func (p *ZeusAdStatsData) readField3(iprot thrift.TProtocol) error {
	p.AdData = NewZeusAdData()
	if err := p.AdData.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdData)
	}
	return nil
}

func (p *ZeusAdStatsData) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ZeusAdStatsData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Aid = v
	}
	return nil
}

func (p *ZeusAdStatsData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *ZeusAdStatsData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAdStatsData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdStatsData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("targeting", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:targeting: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Targeting)); err != nil {
		return fmt.Errorf("%T.targeting (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:targeting: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsData) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AdData != nil {
		if err := oprot.WriteFieldBegin("ad_data", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ad_data: %s", p, err)
		}
		if err := p.AdData.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdData)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ad_data: %s", p, err)
		}
	}
	return err
}

func (p *ZeusAdStatsData) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ids: %s", p, err)
		}
	}
	return err
}

func (p *ZeusAdStatsData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aid", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:aid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Aid)); err != nil {
		return fmt.Errorf("%T.aid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:aid: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:uid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:uid: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAdStatsData(%+v)", *p)
}

type ZeusMetricComparer struct {
	MetricName  string              `thrift:"metric_name,1" json:"metric_name"`
	MetricValue int32               `thrift:"metric_value,2" json:"metric_value"`
	MetricType  ZeusMetricOrderType `thrift:"metric_type,3" json:"metric_type"`
}

func NewZeusMetricComparer() *ZeusMetricComparer {
	return &ZeusMetricComparer{
		MetricType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ZeusMetricComparer) IsSetMetricType() bool {
	return int64(p.MetricType) != math.MinInt32-1
}

func (p *ZeusMetricComparer) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusMetricComparer) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MetricName = v
	}
	return nil
}

func (p *ZeusMetricComparer) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MetricValue = v
	}
	return nil
}

func (p *ZeusMetricComparer) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MetricType = ZeusMetricOrderType(v)
	}
	return nil
}

func (p *ZeusMetricComparer) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusMetricComparer"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusMetricComparer) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("metric_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:metric_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MetricName)); err != nil {
		return fmt.Errorf("%T.metric_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:metric_name: %s", p, err)
	}
	return err
}

func (p *ZeusMetricComparer) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("metric_value", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:metric_value: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MetricValue)); err != nil {
		return fmt.Errorf("%T.metric_value (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:metric_value: %s", p, err)
	}
	return err
}

func (p *ZeusMetricComparer) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMetricType() {
		if err := oprot.WriteFieldBegin("metric_type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:metric_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MetricType)); err != nil {
			return fmt.Errorf("%T.metric_type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:metric_type: %s", p, err)
		}
	}
	return err
}

func (p *ZeusMetricComparer) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusMetricComparer(%+v)", *p)
}

type ZeusAdsetForAutoStats struct {
	ZeusAdsetId      int64 `thrift:"zeus_adset_id,1" json:"zeus_adset_id"`
	FbAdsetId        int64 `thrift:"fb_adset_id,2" json:"fb_adset_id"`
	Impressions      int32 `thrift:"impressions,3" json:"impressions"`
	Clicks           int32 `thrift:"clicks,4" json:"clicks"`
	Spent            int32 `thrift:"spent,5" json:"spent"`
	MobileAppInstall int32 `thrift:"mobile_app_install,6" json:"mobile_app_install"`
	AdsetStatsTime   int64 `thrift:"adset_stats_time,7" json:"adset_stats_time"`
}

func NewZeusAdsetForAutoStats() *ZeusAdsetForAutoStats {
	return &ZeusAdsetForAutoStats{}
}

func (p *ZeusAdsetForAutoStats) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ZeusAdsetId = v
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FbAdsetId = v
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Impressions = v
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Clicks = v
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Spent = v
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MobileAppInstall = v
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AdsetStatsTime = v
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAdsetForAutoStats"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdsetForAutoStats) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_adset_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:zeus_adset_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusAdsetId)); err != nil {
		return fmt.Errorf("%T.zeus_adset_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:zeus_adset_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdsetForAutoStats) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_adset_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fb_adset_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbAdsetId)); err != nil {
		return fmt.Errorf("%T.fb_adset_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fb_adset_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdsetForAutoStats) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressions", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:impressions: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Impressions)); err != nil {
		return fmt.Errorf("%T.impressions (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:impressions: %s", p, err)
	}
	return err
}

func (p *ZeusAdsetForAutoStats) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clicks", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:clicks: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Clicks)); err != nil {
		return fmt.Errorf("%T.clicks (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:clicks: %s", p, err)
	}
	return err
}

func (p *ZeusAdsetForAutoStats) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spent", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:spent: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Spent)); err != nil {
		return fmt.Errorf("%T.spent (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:spent: %s", p, err)
	}
	return err
}

func (p *ZeusAdsetForAutoStats) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile_app_install", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:mobile_app_install: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MobileAppInstall)); err != nil {
		return fmt.Errorf("%T.mobile_app_install (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:mobile_app_install: %s", p, err)
	}
	return err
}

func (p *ZeusAdsetForAutoStats) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adset_stats_time", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:adset_stats_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdsetStatsTime)); err != nil {
		return fmt.Errorf("%T.adset_stats_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:adset_stats_time: %s", p, err)
	}
	return err
}

func (p *ZeusAdsetForAutoStats) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAdsetForAutoStats(%+v)", *p)
}

type ZeusAdCalculatedStats struct {
	Ctr       int32 `thrift:"ctr,1" json:"ctr"`
	Cvr       int32 `thrift:"cvr,2" json:"cvr"`
	Income    int32 `thrift:"income,3" json:"income"`
	SocialCtr int32 `thrift:"social_ctr,4" json:"social_ctr"`
	ROI       int32 `thrift:"ROI,5" json:"ROI"`
	Profit    int32 `thrift:"profit,6" json:"profit"`
	ECPA      int32 `thrift:"eCPA,7" json:"eCPA"`
}

func NewZeusAdCalculatedStats() *ZeusAdCalculatedStats {
	return &ZeusAdCalculatedStats{}
}

func (p *ZeusAdCalculatedStats) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdCalculatedStats) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Ctr = v
	}
	return nil
}

func (p *ZeusAdCalculatedStats) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cvr = v
	}
	return nil
}

func (p *ZeusAdCalculatedStats) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Income = v
	}
	return nil
}

func (p *ZeusAdCalculatedStats) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SocialCtr = v
	}
	return nil
}

func (p *ZeusAdCalculatedStats) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ROI = v
	}
	return nil
}

func (p *ZeusAdCalculatedStats) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Profit = v
	}
	return nil
}

func (p *ZeusAdCalculatedStats) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ECPA = v
	}
	return nil
}

func (p *ZeusAdCalculatedStats) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAdCalculatedStats"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdCalculatedStats) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctr", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ctr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ctr)); err != nil {
		return fmt.Errorf("%T.ctr (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ctr: %s", p, err)
	}
	return err
}

func (p *ZeusAdCalculatedStats) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cvr", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cvr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cvr)); err != nil {
		return fmt.Errorf("%T.cvr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cvr: %s", p, err)
	}
	return err
}

func (p *ZeusAdCalculatedStats) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:income: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Income)); err != nil {
		return fmt.Errorf("%T.income (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:income: %s", p, err)
	}
	return err
}

func (p *ZeusAdCalculatedStats) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("social_ctr", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:social_ctr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SocialCtr)); err != nil {
		return fmt.Errorf("%T.social_ctr (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:social_ctr: %s", p, err)
	}
	return err
}

func (p *ZeusAdCalculatedStats) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ROI", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ROI: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ROI)); err != nil {
		return fmt.Errorf("%T.ROI (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ROI: %s", p, err)
	}
	return err
}

func (p *ZeusAdCalculatedStats) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("profit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:profit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Profit)); err != nil {
		return fmt.Errorf("%T.profit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:profit: %s", p, err)
	}
	return err
}

func (p *ZeusAdCalculatedStats) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("eCPA", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:eCPA: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ECPA)); err != nil {
		return fmt.Errorf("%T.eCPA (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:eCPA: %s", p, err)
	}
	return err
}

func (p *ZeusAdCalculatedStats) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAdCalculatedStats(%+v)", *p)
}

type ZeusAdAdvertisingStats struct {
	// unused field # 1
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	Impressions             int32  `thrift:"impressions,9" json:"impressions"`
	Clicks                  int32  `thrift:"clicks,10" json:"clicks"`
	Spent                   int32  `thrift:"spent,11" json:"spent"`
	SocialImpressions       int32  `thrift:"social_impressions,12" json:"social_impressions"`
	SocialClicks            int32  `thrift:"social_clicks,13" json:"social_clicks"`
	SocialSpent             int32  `thrift:"social_spent,14" json:"social_spent"`
	UniqueImpressions       int32  `thrift:"unique_impressions,15" json:"unique_impressions"`
	UniqueClicks            int32  `thrift:"unique_clicks,16" json:"unique_clicks"`
	UniqueSocialImpressions int32  `thrift:"unique_social_impressions,17" json:"unique_social_impressions"`
	UniqueSocialClicks      int32  `thrift:"unique_social_clicks,18" json:"unique_social_clicks"`
	Reach                   int32  `thrift:"reach,19" json:"reach"`
	Frequency               int32  `thrift:"frequency,20" json:"frequency"`
	RelevanceScore          string `thrift:"relevance_score,21" json:"relevance_score"`
}

func NewZeusAdAdvertisingStats() *ZeusAdAdvertisingStats {
	return &ZeusAdAdvertisingStats{
		Impressions: 0,

		Clicks: 0,

		Spent: 0,
	}
}

func (p *ZeusAdAdvertisingStats) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Impressions = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Clicks = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Spent = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SocialImpressions = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.SocialClicks = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.SocialSpent = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.UniqueImpressions = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.UniqueClicks = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.UniqueSocialImpressions = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.UniqueSocialClicks = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Reach = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Frequency = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.RelevanceScore = v
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAdAdvertisingStats"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdAdvertisingStats) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressions", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:impressions: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Impressions)); err != nil {
		return fmt.Errorf("%T.impressions (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:impressions: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clicks", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:clicks: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Clicks)); err != nil {
		return fmt.Errorf("%T.clicks (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:clicks: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spent", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:spent: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Spent)); err != nil {
		return fmt.Errorf("%T.spent (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:spent: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("social_impressions", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:social_impressions: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SocialImpressions)); err != nil {
		return fmt.Errorf("%T.social_impressions (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:social_impressions: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("social_clicks", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:social_clicks: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SocialClicks)); err != nil {
		return fmt.Errorf("%T.social_clicks (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:social_clicks: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("social_spent", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:social_spent: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SocialSpent)); err != nil {
		return fmt.Errorf("%T.social_spent (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:social_spent: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unique_impressions", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:unique_impressions: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UniqueImpressions)); err != nil {
		return fmt.Errorf("%T.unique_impressions (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:unique_impressions: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unique_clicks", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:unique_clicks: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UniqueClicks)); err != nil {
		return fmt.Errorf("%T.unique_clicks (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:unique_clicks: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unique_social_impressions", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:unique_social_impressions: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UniqueSocialImpressions)); err != nil {
		return fmt.Errorf("%T.unique_social_impressions (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:unique_social_impressions: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unique_social_clicks", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:unique_social_clicks: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UniqueSocialClicks)); err != nil {
		return fmt.Errorf("%T.unique_social_clicks (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:unique_social_clicks: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reach", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:reach: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Reach)); err != nil {
		return fmt.Errorf("%T.reach (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:reach: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("frequency", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:frequency: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Frequency)); err != nil {
		return fmt.Errorf("%T.frequency (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:frequency: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("relevance_score", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:relevance_score: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RelevanceScore)); err != nil {
		return fmt.Errorf("%T.relevance_score (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:relevance_score: %s", p, err)
	}
	return err
}

func (p *ZeusAdAdvertisingStats) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAdAdvertisingStats(%+v)", *p)
}

type ZeusAdActionStats struct {
	// unused field # 1
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	MobileAppInstallDaily int32 `thrift:"mobile_app_install_daily,29" json:"mobile_app_install_daily"`
	MobileAppInstall      int32 `thrift:"mobile_app_install,30" json:"mobile_app_install"`
	FbMobileActiveApp     int32 `thrift:"fb_mobile_active_app,31" json:"fb_mobile_active_app"`
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	LinkClick int32 `thrift:"link_click,44" json:"link_click"`
	PostLike  int32 `thrift:"post_like,45" json:"post_like"`
}

func NewZeusAdActionStats() *ZeusAdActionStats {
	return &ZeusAdActionStats{
		MobileAppInstall: 0,
	}
}

func (p *ZeusAdActionStats) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I32 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.I32 {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdActionStats) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.MobileAppInstallDaily = v
	}
	return nil
}

func (p *ZeusAdActionStats) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.MobileAppInstall = v
	}
	return nil
}

func (p *ZeusAdActionStats) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.FbMobileActiveApp = v
	}
	return nil
}

func (p *ZeusAdActionStats) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.LinkClick = v
	}
	return nil
}

func (p *ZeusAdActionStats) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.PostLike = v
	}
	return nil
}

func (p *ZeusAdActionStats) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAdActionStats"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdActionStats) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile_app_install_daily", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:mobile_app_install_daily: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MobileAppInstallDaily)); err != nil {
		return fmt.Errorf("%T.mobile_app_install_daily (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:mobile_app_install_daily: %s", p, err)
	}
	return err
}

func (p *ZeusAdActionStats) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mobile_app_install", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:mobile_app_install: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MobileAppInstall)); err != nil {
		return fmt.Errorf("%T.mobile_app_install (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:mobile_app_install: %s", p, err)
	}
	return err
}

func (p *ZeusAdActionStats) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_mobile_active_app", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:fb_mobile_active_app: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FbMobileActiveApp)); err != nil {
		return fmt.Errorf("%T.fb_mobile_active_app (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:fb_mobile_active_app: %s", p, err)
	}
	return err
}

func (p *ZeusAdActionStats) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("link_click", thrift.I32, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:link_click: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LinkClick)); err != nil {
		return fmt.Errorf("%T.link_click (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:link_click: %s", p, err)
	}
	return err
}

func (p *ZeusAdActionStats) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("post_like", thrift.I32, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:post_like: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PostLike)); err != nil {
		return fmt.Errorf("%T.post_like (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:post_like: %s", p, err)
	}
	return err
}

func (p *ZeusAdActionStats) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAdActionStats(%+v)", *p)
}

type ZeusAdStatsInfo struct {
	ZeusAdsetId      int64                   `thrift:"zeus_adset_id,1" json:"zeus_adset_id"`
	ZeusCampaignId   int64                   `thrift:"zeus_campaign_id,2" json:"zeus_campaign_id"`
	AccountId        int64                   `thrift:"account_id,3" json:"account_id"`
	IncomePrice      int32                   `thrift:"income_price,4" json:"income_price"`
	IncomeType       int32                   `thrift:"income_type,5" json:"income_type"`
	Budget           int32                   `thrift:"budget,6" json:"budget"`
	Dt               int32                   `thrift:"dt,7" json:"dt"`
	Hr               int32                   `thrift:"hr,8" json:"hr"`
	AdvertisingStats *ZeusAdAdvertisingStats `thrift:"advertising_stats,9" json:"advertising_stats"`
	// unused field # 10
	ActionStats   *ZeusAdActionStats `thrift:"action_stats,11" json:"action_stats"`
	ZeusAdId      int64              `thrift:"zeus_ad_id,12" json:"zeus_ad_id"`
	TargetingDict map[string]string  `thrift:"targeting_dict,13" json:"targeting_dict"`
	ActionOnly    bool               `thrift:"action_only,14" json:"action_only"`
	FbCampaignId  int64              `thrift:"fb_campaign_id,15" json:"fb_campaign_id"`
	FbAdsetId     int64              `thrift:"fb_adset_id,16" json:"fb_adset_id"`
	FbAdId        int64              `thrift:"fb_ad_id,17" json:"fb_ad_id"`
	FbCreativeId  int64              `thrift:"fb_creative_id,18" json:"fb_creative_id"`
	CostPrice     int64              `thrift:"cost_price,19" json:"cost_price"`
	// unused field # 20
	IncomeActionPrice int64 `thrift:"income_action_price,21" json:"income_action_price"`
	IncomeActionType  int32 `thrift:"income_action_type,22" json:"income_action_type"`
	CostActionPrice   int64 `thrift:"cost_action_price,23" json:"cost_action_price"`
	CostActionType    int32 `thrift:"cost_action_type,24" json:"cost_action_type"`
	PromotionId       int64 `thrift:"promotion_id,25" json:"promotion_id"`
	ZeusCreativeId    int64 `thrift:"zeus_creative_id,26" json:"zeus_creative_id"`
	ZeusUserId        int64 `thrift:"zeus_user_id,27" json:"zeus_user_id"`
	FbUserId          int64 `thrift:"fb_user_id,28" json:"fb_user_id"`
	CostType          int32 `thrift:"cost_type,29" json:"cost_type"`
}

func NewZeusAdStatsInfo() *ZeusAdStatsInfo {
	return &ZeusAdStatsInfo{
		FbCampaignId: 0,

		FbAdsetId: 0,

		FbAdId: 0,
	}
}

func (p *ZeusAdStatsInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.MAP {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I64 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I64 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I64 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ZeusAdsetId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ZeusCampaignId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IncomePrice = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IncomeType = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField9(iprot thrift.TProtocol) error {
	p.AdvertisingStats = NewZeusAdAdvertisingStats()
	if err := p.AdvertisingStats.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdvertisingStats)
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField11(iprot thrift.TProtocol) error {
	p.ActionStats = NewZeusAdActionStats()
	if err := p.ActionStats.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ActionStats)
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ZeusAdId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField13(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.TargetingDict = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = v
		}
		var _val2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = v
		}
		p.TargetingDict[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ActionOnly = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.FbCampaignId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.FbAdsetId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.FbAdId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.FbCreativeId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.CostPrice = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.IncomeActionPrice = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.IncomeActionType = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.CostActionPrice = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.CostActionType = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.ZeusCreativeId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.ZeusUserId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.FbUserId = v
	}
	return nil
}

func (p *ZeusAdStatsInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAdStatsInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAdStatsInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_adset_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:zeus_adset_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusAdsetId)); err != nil {
		return fmt.Errorf("%T.zeus_adset_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:zeus_adset_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_campaign_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:zeus_campaign_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusCampaignId)); err != nil {
		return fmt.Errorf("%T.zeus_campaign_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:zeus_campaign_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.account_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income_price", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:income_price: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IncomePrice)); err != nil {
		return fmt.Errorf("%T.income_price (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:income_price: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income_type", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:income_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IncomeType)); err != nil {
		return fmt.Errorf("%T.income_type (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:income_type: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:budget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:budget: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:dt: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:hr: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.AdvertisingStats != nil {
		if err := oprot.WriteFieldBegin("advertising_stats", thrift.STRUCT, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:advertising_stats: %s", p, err)
		}
		if err := p.AdvertisingStats.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdvertisingStats)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:advertising_stats: %s", p, err)
		}
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.ActionStats != nil {
		if err := oprot.WriteFieldBegin("action_stats", thrift.STRUCT, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:action_stats: %s", p, err)
		}
		if err := p.ActionStats.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ActionStats)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:action_stats: %s", p, err)
		}
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_ad_id", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:zeus_ad_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusAdId)); err != nil {
		return fmt.Errorf("%T.zeus_ad_id (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:zeus_ad_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if p.TargetingDict != nil {
		if err := oprot.WriteFieldBegin("targeting_dict", thrift.MAP, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:targeting_dict: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.TargetingDict)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.TargetingDict {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:targeting_dict: %s", p, err)
		}
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_only", thrift.BOOL, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:action_only: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ActionOnly)); err != nil {
		return fmt.Errorf("%T.action_only (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:action_only: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_campaign_id", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:fb_campaign_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbCampaignId)); err != nil {
		return fmt.Errorf("%T.fb_campaign_id (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:fb_campaign_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_adset_id", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:fb_adset_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbAdsetId)); err != nil {
		return fmt.Errorf("%T.fb_adset_id (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:fb_adset_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_ad_id", thrift.I64, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:fb_ad_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbAdId)); err != nil {
		return fmt.Errorf("%T.fb_ad_id (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:fb_ad_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_creative_id", thrift.I64, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:fb_creative_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbCreativeId)); err != nil {
		return fmt.Errorf("%T.fb_creative_id (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:fb_creative_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_price", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:cost_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CostPrice)); err != nil {
		return fmt.Errorf("%T.cost_price (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:cost_price: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income_action_price", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:income_action_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.IncomeActionPrice)); err != nil {
		return fmt.Errorf("%T.income_action_price (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:income_action_price: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income_action_type", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:income_action_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IncomeActionType)); err != nil {
		return fmt.Errorf("%T.income_action_type (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:income_action_type: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_action_price", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:cost_action_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CostActionPrice)); err != nil {
		return fmt.Errorf("%T.cost_action_price (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:cost_action_price: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_action_type", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:cost_action_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostActionType)); err != nil {
		return fmt.Errorf("%T.cost_action_type (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:cost_action_type: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotion_id", thrift.I64, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:promotion_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotion_id (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:promotion_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_creative_id", thrift.I64, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:zeus_creative_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusCreativeId)); err != nil {
		return fmt.Errorf("%T.zeus_creative_id (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:zeus_creative_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_user_id", thrift.I64, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:zeus_user_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusUserId)); err != nil {
		return fmt.Errorf("%T.zeus_user_id (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:zeus_user_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_user_id", thrift.I64, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:fb_user_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbUserId)); err != nil {
		return fmt.Errorf("%T.fb_user_id (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:fb_user_id: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:cost_type: %s", p, err)
	}
	return err
}

func (p *ZeusAdStatsInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAdStatsInfo(%+v)", *p)
}
