// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dbm_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dbm_types"
	"rtb_model_server/common/domob_thrift/passport_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dbm_types.GoUnusedProtection__
var _ = passport_types.GoUnusedProtection__
var GoUnusedProtection__ int

type DbmEventType int64

const (
	DbmEventType_DET_UNKNOWN              DbmEventType = 0
	DbmEventType_DET_ADD                  DbmEventType = 1
	DbmEventType_DET_UPDATE               DbmEventType = 2
	DbmEventType_DET_DELETE               DbmEventType = 3
	DbmEventType_DET_PAUSE                DbmEventType = 4
	DbmEventType_DET_RESUME               DbmEventType = 5
	DbmEventType_DET_INNER_APPROVE_PASS   DbmEventType = 10
	DbmEventType_DET_INNER_APPROVE_REJECT DbmEventType = 11
	DbmEventType_DET_BALANCE_OVER         DbmEventType = 20
	DbmEventType_DET_BALANCE_OK           DbmEventType = 21
	DbmEventType_DET_CHANGE_PASSWORD      DbmEventType = 22
	DbmEventType_DET_AGENT_COMMAND        DbmEventType = 23
)

func (p DbmEventType) String() string {
	switch p {
	case DbmEventType_DET_UNKNOWN:
		return "DbmEventType_DET_UNKNOWN"
	case DbmEventType_DET_ADD:
		return "DbmEventType_DET_ADD"
	case DbmEventType_DET_UPDATE:
		return "DbmEventType_DET_UPDATE"
	case DbmEventType_DET_DELETE:
		return "DbmEventType_DET_DELETE"
	case DbmEventType_DET_PAUSE:
		return "DbmEventType_DET_PAUSE"
	case DbmEventType_DET_RESUME:
		return "DbmEventType_DET_RESUME"
	case DbmEventType_DET_INNER_APPROVE_PASS:
		return "DbmEventType_DET_INNER_APPROVE_PASS"
	case DbmEventType_DET_INNER_APPROVE_REJECT:
		return "DbmEventType_DET_INNER_APPROVE_REJECT"
	case DbmEventType_DET_BALANCE_OVER:
		return "DbmEventType_DET_BALANCE_OVER"
	case DbmEventType_DET_BALANCE_OK:
		return "DbmEventType_DET_BALANCE_OK"
	case DbmEventType_DET_CHANGE_PASSWORD:
		return "DbmEventType_DET_CHANGE_PASSWORD"
	case DbmEventType_DET_AGENT_COMMAND:
		return "DbmEventType_DET_AGENT_COMMAND"
	}
	return "<UNSET>"
}

func DbmEventTypeFromString(s string) (DbmEventType, error) {
	switch s {
	case "DbmEventType_DET_UNKNOWN":
		return DbmEventType_DET_UNKNOWN, nil
	case "DbmEventType_DET_ADD":
		return DbmEventType_DET_ADD, nil
	case "DbmEventType_DET_UPDATE":
		return DbmEventType_DET_UPDATE, nil
	case "DbmEventType_DET_DELETE":
		return DbmEventType_DET_DELETE, nil
	case "DbmEventType_DET_PAUSE":
		return DbmEventType_DET_PAUSE, nil
	case "DbmEventType_DET_RESUME":
		return DbmEventType_DET_RESUME, nil
	case "DbmEventType_DET_INNER_APPROVE_PASS":
		return DbmEventType_DET_INNER_APPROVE_PASS, nil
	case "DbmEventType_DET_INNER_APPROVE_REJECT":
		return DbmEventType_DET_INNER_APPROVE_REJECT, nil
	case "DbmEventType_DET_BALANCE_OVER":
		return DbmEventType_DET_BALANCE_OVER, nil
	case "DbmEventType_DET_BALANCE_OK":
		return DbmEventType_DET_BALANCE_OK, nil
	case "DbmEventType_DET_CHANGE_PASSWORD":
		return DbmEventType_DET_CHANGE_PASSWORD, nil
	case "DbmEventType_DET_AGENT_COMMAND":
		return DbmEventType_DET_AGENT_COMMAND, nil
	}
	return DbmEventType(math.MinInt32 - 1), fmt.Errorf("not a valid DbmEventType string")
}

type DbmEventCategory int64

const (
	DbmEventCategory_DEC_UNKNOWN         DbmEventCategory = 0
	DbmEventCategory_DEC_SPONSOR         DbmEventCategory = 1
	DbmEventCategory_DEC_AD_ORDER        DbmEventCategory = 2
	DbmEventCategory_DEC_AD_CAMPAIGN     DbmEventCategory = 3
	DbmEventCategory_DEC_AD_STRATEGY     DbmEventCategory = 4
	DbmEventCategory_DEC_AD_CREATIVE     DbmEventCategory = 5
	DbmEventCategory_DEC_AGENT           DbmEventCategory = 11
	DbmEventCategory_DEC_AGENT_USER      DbmEventCategory = 12
	DbmEventCategory_DEC_SPONSOR_USER    DbmEventCategory = 13
	DbmEventCategory_DEC_AGENT_ACCOUNT   DbmEventCategory = 14
	DbmEventCategory_DEC_SPONSOR_ACCOUNT DbmEventCategory = 15
)

func (p DbmEventCategory) String() string {
	switch p {
	case DbmEventCategory_DEC_UNKNOWN:
		return "DbmEventCategory_DEC_UNKNOWN"
	case DbmEventCategory_DEC_SPONSOR:
		return "DbmEventCategory_DEC_SPONSOR"
	case DbmEventCategory_DEC_AD_ORDER:
		return "DbmEventCategory_DEC_AD_ORDER"
	case DbmEventCategory_DEC_AD_CAMPAIGN:
		return "DbmEventCategory_DEC_AD_CAMPAIGN"
	case DbmEventCategory_DEC_AD_STRATEGY:
		return "DbmEventCategory_DEC_AD_STRATEGY"
	case DbmEventCategory_DEC_AD_CREATIVE:
		return "DbmEventCategory_DEC_AD_CREATIVE"
	case DbmEventCategory_DEC_AGENT:
		return "DbmEventCategory_DEC_AGENT"
	case DbmEventCategory_DEC_AGENT_USER:
		return "DbmEventCategory_DEC_AGENT_USER"
	case DbmEventCategory_DEC_SPONSOR_USER:
		return "DbmEventCategory_DEC_SPONSOR_USER"
	case DbmEventCategory_DEC_AGENT_ACCOUNT:
		return "DbmEventCategory_DEC_AGENT_ACCOUNT"
	case DbmEventCategory_DEC_SPONSOR_ACCOUNT:
		return "DbmEventCategory_DEC_SPONSOR_ACCOUNT"
	}
	return "<UNSET>"
}

func DbmEventCategoryFromString(s string) (DbmEventCategory, error) {
	switch s {
	case "DbmEventCategory_DEC_UNKNOWN":
		return DbmEventCategory_DEC_UNKNOWN, nil
	case "DbmEventCategory_DEC_SPONSOR":
		return DbmEventCategory_DEC_SPONSOR, nil
	case "DbmEventCategory_DEC_AD_ORDER":
		return DbmEventCategory_DEC_AD_ORDER, nil
	case "DbmEventCategory_DEC_AD_CAMPAIGN":
		return DbmEventCategory_DEC_AD_CAMPAIGN, nil
	case "DbmEventCategory_DEC_AD_STRATEGY":
		return DbmEventCategory_DEC_AD_STRATEGY, nil
	case "DbmEventCategory_DEC_AD_CREATIVE":
		return DbmEventCategory_DEC_AD_CREATIVE, nil
	case "DbmEventCategory_DEC_AGENT":
		return DbmEventCategory_DEC_AGENT, nil
	case "DbmEventCategory_DEC_AGENT_USER":
		return DbmEventCategory_DEC_AGENT_USER, nil
	case "DbmEventCategory_DEC_SPONSOR_USER":
		return DbmEventCategory_DEC_SPONSOR_USER, nil
	case "DbmEventCategory_DEC_AGENT_ACCOUNT":
		return DbmEventCategory_DEC_AGENT_ACCOUNT, nil
	case "DbmEventCategory_DEC_SPONSOR_ACCOUNT":
		return DbmEventCategory_DEC_SPONSOR_ACCOUNT, nil
	}
	return DbmEventCategory(math.MinInt32 - 1), fmt.Errorf("not a valid DbmEventCategory string")
}

type DbmCommonEvent struct {
	TypeA1   DbmEventType     `thrift:"type,1" json:"type"`
	Category DbmEventCategory `thrift:"category,2" json:"category"`
	Ids      []int32          `thrift:"ids,3" json:"ids"`
}

func NewDbmCommonEvent() *DbmCommonEvent {
	return &DbmCommonEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DbmCommonEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DbmCommonEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *DbmCommonEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DbmCommonEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DbmEventType(v)
	}
	return nil
}

func (p *DbmCommonEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = DbmEventCategory(v)
	}
	return nil
}

func (p *DbmCommonEvent) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Ids = append(p.Ids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DbmCommonEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DbmCommonEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DbmCommonEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DbmCommonEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *DbmCommonEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *DbmCommonEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DbmCommonEvent(%+v)", *p)
}

type DbmOperationEvent struct {
	TypeA1         DbmEventType              `thrift:"type,1" json:"type"`
	Category       DbmEventCategory          `thrift:"category,2" json:"category"`
	OperateTime    int64                     `thrift:"operateTime,3" json:"operateTime"`
	ServiceName    string                    `thrift:"serviceName,4" json:"serviceName"`
	Sponsor        *dbm_types.SponsorProfile `thrift:"sponsor,5" json:"sponsor"`
	Order          *dbm_types.AdOrder        `thrift:"order,6" json:"order"`
	Campaign       *dbm_types.AdCampaign     `thrift:"campaign,7" json:"campaign"`
	Strategy       *dbm_types.AdStrategy     `thrift:"strategy,8" json:"strategy"`
	Creative       *dbm_types.AdCreative     `thrift:"creative,9" json:"creative"`
	AgentAccount   *dbm_types.AgentAccount   `thrift:"agentAccount,10" json:"agentAccount"`
	SponsorAccount *dbm_types.SponsorAccount `thrift:"sponsorAccount,11" json:"sponsorAccount"`
	User           *passport_types.User      `thrift:"user,12" json:"user"`
}

func NewDbmOperationEvent() *DbmOperationEvent {
	return &DbmOperationEvent{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DbmOperationEvent) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *DbmOperationEvent) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *DbmOperationEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DbmOperationEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = DbmEventType(v)
	}
	return nil
}

func (p *DbmOperationEvent) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = DbmEventCategory(v)
	}
	return nil
}

func (p *DbmOperationEvent) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OperateTime = v
	}
	return nil
}

func (p *DbmOperationEvent) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ServiceName = v
	}
	return nil
}

func (p *DbmOperationEvent) readField5(iprot thrift.TProtocol) error {
	p.Sponsor = dbm_types.NewSponsorProfile()
	if err := p.Sponsor.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sponsor)
	}
	return nil
}

func (p *DbmOperationEvent) readField6(iprot thrift.TProtocol) error {
	p.Order = dbm_types.NewAdOrder()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *DbmOperationEvent) readField7(iprot thrift.TProtocol) error {
	p.Campaign = dbm_types.NewAdCampaign()
	if err := p.Campaign.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Campaign)
	}
	return nil
}

func (p *DbmOperationEvent) readField8(iprot thrift.TProtocol) error {
	p.Strategy = dbm_types.NewAdStrategy()
	if err := p.Strategy.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Strategy)
	}
	return nil
}

func (p *DbmOperationEvent) readField9(iprot thrift.TProtocol) error {
	p.Creative = dbm_types.NewAdCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *DbmOperationEvent) readField10(iprot thrift.TProtocol) error {
	p.AgentAccount = dbm_types.NewAgentAccount()
	if err := p.AgentAccount.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AgentAccount)
	}
	return nil
}

func (p *DbmOperationEvent) readField11(iprot thrift.TProtocol) error {
	p.SponsorAccount = dbm_types.NewSponsorAccount()
	if err := p.SponsorAccount.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SponsorAccount)
	}
	return nil
}

func (p *DbmOperationEvent) readField12(iprot thrift.TProtocol) error {
	p.User = passport_types.NewUser()
	if err := p.User.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.User)
	}
	return nil
}

func (p *DbmOperationEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DbmOperationEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DbmOperationEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:category: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("operateTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:operateTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OperateTime)); err != nil {
		return fmt.Errorf("%T.operateTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:operateTime: %s", p, err)
	}
	return err
}

func (p *DbmOperationEvent) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("serviceName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:serviceName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ServiceName)); err != nil {
		return fmt.Errorf("%T.serviceName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:serviceName: %s", p, err)
	}
	return err
}

func (p *DbmOperationEvent) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Sponsor != nil {
		if err := oprot.WriteFieldBegin("sponsor", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:sponsor: %s", p, err)
		}
		if err := p.Sponsor.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sponsor)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:sponsor: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:order: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Campaign != nil {
		if err := oprot.WriteFieldBegin("campaign", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:campaign: %s", p, err)
		}
		if err := p.Campaign.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Campaign)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:campaign: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField8(oprot thrift.TProtocol) (err error) {
	if p.Strategy != nil {
		if err := oprot.WriteFieldBegin("strategy", thrift.STRUCT, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:strategy: %s", p, err)
		}
		if err := p.Strategy.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Strategy)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:strategy: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField9(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:creative: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField10(oprot thrift.TProtocol) (err error) {
	if p.AgentAccount != nil {
		if err := oprot.WriteFieldBegin("agentAccount", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:agentAccount: %s", p, err)
		}
		if err := p.AgentAccount.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AgentAccount)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:agentAccount: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField11(oprot thrift.TProtocol) (err error) {
	if p.SponsorAccount != nil {
		if err := oprot.WriteFieldBegin("sponsorAccount", thrift.STRUCT, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:sponsorAccount: %s", p, err)
		}
		if err := p.SponsorAccount.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SponsorAccount)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:sponsorAccount: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) writeField12(oprot thrift.TProtocol) (err error) {
	if p.User != nil {
		if err := oprot.WriteFieldBegin("user", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:user: %s", p, err)
		}
		if err := p.User.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.User)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:user: %s", p, err)
		}
	}
	return err
}

func (p *DbmOperationEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DbmOperationEvent(%+v)", *p)
}
