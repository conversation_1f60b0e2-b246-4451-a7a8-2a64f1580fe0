// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package video_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = programmatic_creative_types.GoUnusedProtection__

type VideoServer interface {
	dm303.DomobService

	// Parameters:
	//  - Header
	//  - Request
	AddAppVideoMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - ClipList
	//  - SrcVideo
	ClipVideoMaterial(header *common.RequestHeader, clip_list []*programmatic_creative_types.PCVideoClipInfo, src_video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - SrcVideo
	//  - WatermarkList
	RemoveVideoWatermark(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - TargetCodec
	//  - SrcVideo
	ConvertVideoCodec(header *common.RequestHeader, target_codec *programmatic_creative_types.PCVideoCodecInfo, src_video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - SrcVideo
	//  - OverlayList
	AddVideoOverlay(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, overlay_list []*programmatic_creative_types.PCVideoOverlayInfo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - VideoList
	ConcatVideos(header *common.RequestHeader, video_list []*programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - SrcVideo
	//  - EXList
	ConvertVideoMatchADX(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, EX_list []int32) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - SrcVideo
	//  - AdxMatchIdDict
	ProcessVideoMatchADX(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, adx_match_id_dict map[int32]int32) (err error)
	// Parameters:
	//  - Header
	//  - UserInfo
	GetLandingpageImages(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error)
	// 给出视频的信息，宽高、视频编码等
	//
	// Parameters:
	//  - Header
	//  - SrcVideo
	GetVideoInfo(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - UserInfo
	//  - SrcVideo
	//  - LandingPage
	//  - BottomBar
	//  - WatermarkList
	//  - VideoText
	//  - TargetWidth
	//  - TargetHeight
	//  - MaxDuration
	//  - VolumeInfo
	//  - AdditionalRequest
	RefineVideoCreative(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo, src_video *programmatic_creative_types.PCVideo, landing_page *programmatic_creative_types.PCVideoOverlayInfo, bottom_bar *programmatic_creative_types.PCVideoOverlayInfo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo, video_text *programmatic_creative_types.PCText, target_width int32, target_height int32, max_duration int32, volume_info *programmatic_creative_types.PCVideoVolumeInfo, additional_request map[string]string) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error)
	// 视频创意中心使用的处理视频创意，请求参数和refineVideoCreative方法相同，但是不存在返回值。
	// 处理结果方法会调用视频创意中心服务传递结果。
	//
	// Parameters:
	//  - Header
	//  - UserInfo
	//  - SrcVideo
	//  - LandingPage
	//  - BottomBar
	//  - WatermarkList
	//  - VideoText
	//  - TargetWidth
	//  - TargetHeight
	//  - MaxDuration
	//  - VolumeInfo
	//  - AdditionalRequest
	ProcessVideoCreative(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo, src_video *programmatic_creative_types.PCVideo, landing_page *programmatic_creative_types.PCVideoOverlayInfo, bottom_bar *programmatic_creative_types.PCVideoOverlayInfo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo, video_text *programmatic_creative_types.PCText, target_width int32, target_height int32, max_duration int32, volume_info *programmatic_creative_types.PCVideoVolumeInfo, additional_request map[string]string) (err error)
	// 获取视频缩略图
	//
	// Parameters:
	//  - Header
	//  - SourceVideo
	//  - ThumbnailRequest
	GetVideoThumbnails(header *common.RequestHeader, source_video *programmatic_creative_types.PCVideo, thumbnail_request *programmatic_creative_types.PCVideoThumbnailRequest) (r []*programmatic_creative_types.PCImage, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - SourceVideo
	GetVideoFingerPrint(header *common.RequestHeader, source_video *programmatic_creative_types.PCVideo) (r string, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - ComposInfo
	ComposeMediaSeqVideo(header *common.RequestHeader, compos_info *programmatic_creative_types.PCMediaSeqVideoComposInfo) (err error)
}

type VideoServerClient struct {
	*dm303.DomobServiceClient
}

func NewVideoServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *VideoServerClient {
	return &VideoServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewVideoServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *VideoServerClient {
	return &VideoServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Header
//  - Request
func (p *VideoServerClient) AddAppVideoMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddAppVideoMaterial(header, request); err != nil {
		return
	}
	return p.recvAddAppVideoMaterial()
}

func (p *VideoServerClient) sendAddAppVideoMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addAppVideoMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddAppVideoMaterialArgs()
	args0.Header = header
	args0.Request = request
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvAddAppVideoMaterial() (value programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddAppVideoMaterialResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Rae != nil {
		rae = result1.Rae
	}
	return
}

// Parameters:
//  - Header
//  - ClipList
//  - SrcVideo
func (p *VideoServerClient) ClipVideoMaterial(header *common.RequestHeader, clip_list []*programmatic_creative_types.PCVideoClipInfo, src_video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendClipVideoMaterial(header, clip_list, src_video); err != nil {
		return
	}
	return p.recvClipVideoMaterial()
}

func (p *VideoServerClient) sendClipVideoMaterial(header *common.RequestHeader, clip_list []*programmatic_creative_types.PCVideoClipInfo, src_video *programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("clipVideoMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewClipVideoMaterialArgs()
	args4.Header = header
	args4.ClipList = clip_list
	args4.SrcVideo = src_video
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvClipVideoMaterial() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewClipVideoMaterialResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Rae != nil {
		rae = result5.Rae
	}
	return
}

// Parameters:
//  - Header
//  - SrcVideo
//  - WatermarkList
func (p *VideoServerClient) RemoveVideoWatermark(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendRemoveVideoWatermark(header, src_video, watermark_list); err != nil {
		return
	}
	return p.recvRemoveVideoWatermark()
}

func (p *VideoServerClient) sendRemoveVideoWatermark(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("removeVideoWatermark", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewRemoveVideoWatermarkArgs()
	args8.Header = header
	args8.SrcVideo = src_video
	args8.WatermarkList = watermark_list
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvRemoveVideoWatermark() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewRemoveVideoWatermarkResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Rae != nil {
		rae = result9.Rae
	}
	return
}

// Parameters:
//  - Header
//  - TargetCodec
//  - SrcVideo
func (p *VideoServerClient) ConvertVideoCodec(header *common.RequestHeader, target_codec *programmatic_creative_types.PCVideoCodecInfo, src_video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendConvertVideoCodec(header, target_codec, src_video); err != nil {
		return
	}
	return p.recvConvertVideoCodec()
}

func (p *VideoServerClient) sendConvertVideoCodec(header *common.RequestHeader, target_codec *programmatic_creative_types.PCVideoCodecInfo, src_video *programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("convertVideoCodec", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewConvertVideoCodecArgs()
	args12.Header = header
	args12.TargetCodec = target_codec
	args12.SrcVideo = src_video
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvConvertVideoCodec() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewConvertVideoCodecResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Rae != nil {
		rae = result13.Rae
	}
	return
}

// Parameters:
//  - Header
//  - SrcVideo
//  - OverlayList
func (p *VideoServerClient) AddVideoOverlay(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, overlay_list []*programmatic_creative_types.PCVideoOverlayInfo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddVideoOverlay(header, src_video, overlay_list); err != nil {
		return
	}
	return p.recvAddVideoOverlay()
}

func (p *VideoServerClient) sendAddVideoOverlay(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, overlay_list []*programmatic_creative_types.PCVideoOverlayInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addVideoOverlay", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewAddVideoOverlayArgs()
	args16.Header = header
	args16.SrcVideo = src_video
	args16.OverlayList = overlay_list
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvAddVideoOverlay() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewAddVideoOverlayResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Rae != nil {
		rae = result17.Rae
	}
	return
}

// Parameters:
//  - Header
//  - VideoList
func (p *VideoServerClient) ConcatVideos(header *common.RequestHeader, video_list []*programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendConcatVideos(header, video_list); err != nil {
		return
	}
	return p.recvConcatVideos()
}

func (p *VideoServerClient) sendConcatVideos(header *common.RequestHeader, video_list []*programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("concatVideos", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewConcatVideosArgs()
	args20.Header = header
	args20.VideoList = video_list
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvConcatVideos() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewConcatVideosResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Rae != nil {
		rae = result21.Rae
	}
	return
}

// Parameters:
//  - Header
//  - SrcVideo
//  - EXList
func (p *VideoServerClient) ConvertVideoMatchADX(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, EX_list []int32) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendConvertVideoMatchADX(header, src_video, EX_list); err != nil {
		return
	}
	return p.recvConvertVideoMatchADX()
}

func (p *VideoServerClient) sendConvertVideoMatchADX(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, EX_list []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("convertVideoMatchADX", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewConvertVideoMatchADXArgs()
	args24.Header = header
	args24.SrcVideo = src_video
	args24.EXList = EX_list
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvConvertVideoMatchADX() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewConvertVideoMatchADXResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.Rae != nil {
		rae = result25.Rae
	}
	return
}

// Parameters:
//  - Header
//  - SrcVideo
//  - AdxMatchIdDict
func (p *VideoServerClient) ProcessVideoMatchADX(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, adx_match_id_dict map[int32]int32) (err error) {
	if err = p.sendProcessVideoMatchADX(header, src_video, adx_match_id_dict); err != nil {
		return
	}
	return
}

func (p *VideoServerClient) sendProcessVideoMatchADX(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo, adx_match_id_dict map[int32]int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("processVideoMatchADX", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewProcessVideoMatchADXArgs()
	args28.Header = header
	args28.SrcVideo = src_video
	args28.AdxMatchIdDict = adx_match_id_dict
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvProcessVideoMatchADX() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewProcessVideoMatchADXResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

// Parameters:
//  - Header
//  - UserInfo
func (p *VideoServerClient) GetLandingpageImages(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo) (r *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetLandingpageImages(header, user_info); err != nil {
		return
	}
	return p.recvGetLandingpageImages()
}

func (p *VideoServerClient) sendGetLandingpageImages(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getLandingpageImages", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetLandingpageImagesArgs()
	args32.Header = header
	args32.UserInfo = user_info
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvGetLandingpageImages() (value *programmatic_creative_types.MaterialsInfo, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetLandingpageImagesResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Rae != nil {
		rae = result33.Rae
	}
	return
}

// 给出视频的信息，宽高、视频编码等
//
// Parameters:
//  - Header
//  - SrcVideo
func (p *VideoServerClient) GetVideoInfo(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetVideoInfo(header, src_video); err != nil {
		return
	}
	return p.recvGetVideoInfo()
}

func (p *VideoServerClient) sendGetVideoInfo(header *common.RequestHeader, src_video *programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getVideoInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetVideoInfoArgs()
	args36.Header = header
	args36.SrcVideo = src_video
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvGetVideoInfo() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetVideoInfoResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Rae != nil {
		rae = result37.Rae
	}
	return
}

// Parameters:
//  - Header
//  - UserInfo
//  - SrcVideo
//  - LandingPage
//  - BottomBar
//  - WatermarkList
//  - VideoText
//  - TargetWidth
//  - TargetHeight
//  - MaxDuration
//  - VolumeInfo
//  - AdditionalRequest
func (p *VideoServerClient) RefineVideoCreative(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo, src_video *programmatic_creative_types.PCVideo, landing_page *programmatic_creative_types.PCVideoOverlayInfo, bottom_bar *programmatic_creative_types.PCVideoOverlayInfo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo, video_text *programmatic_creative_types.PCText, target_width int32, target_height int32, max_duration int32, volume_info *programmatic_creative_types.PCVideoVolumeInfo, additional_request map[string]string) (r *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendRefineVideoCreative(header, user_info, src_video, landing_page, bottom_bar, watermark_list, video_text, target_width, target_height, max_duration, volume_info, additional_request); err != nil {
		return
	}
	return p.recvRefineVideoCreative()
}

func (p *VideoServerClient) sendRefineVideoCreative(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo, src_video *programmatic_creative_types.PCVideo, landing_page *programmatic_creative_types.PCVideoOverlayInfo, bottom_bar *programmatic_creative_types.PCVideoOverlayInfo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo, video_text *programmatic_creative_types.PCText, target_width int32, target_height int32, max_duration int32, volume_info *programmatic_creative_types.PCVideoVolumeInfo, additional_request map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("refineVideoCreative", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewRefineVideoCreativeArgs()
	args40.Header = header
	args40.UserInfo = user_info
	args40.SrcVideo = src_video
	args40.LandingPage = landing_page
	args40.BottomBar = bottom_bar
	args40.WatermarkList = watermark_list
	args40.VideoText = video_text
	args40.TargetWidth = target_width
	args40.TargetHeight = target_height
	args40.MaxDuration = max_duration
	args40.VolumeInfo = volume_info
	args40.AdditionalRequest = additional_request
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvRefineVideoCreative() (value *programmatic_creative_types.PCVideoProcResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewRefineVideoCreativeResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Rae != nil {
		rae = result41.Rae
	}
	return
}

// 视频创意中心使用的处理视频创意，请求参数和refineVideoCreative方法相同，但是不存在返回值。
// 处理结果方法会调用视频创意中心服务传递结果。
//
// Parameters:
//  - Header
//  - UserInfo
//  - SrcVideo
//  - LandingPage
//  - BottomBar
//  - WatermarkList
//  - VideoText
//  - TargetWidth
//  - TargetHeight
//  - MaxDuration
//  - VolumeInfo
//  - AdditionalRequest
func (p *VideoServerClient) ProcessVideoCreative(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo, src_video *programmatic_creative_types.PCVideo, landing_page *programmatic_creative_types.PCVideoOverlayInfo, bottom_bar *programmatic_creative_types.PCVideoOverlayInfo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo, video_text *programmatic_creative_types.PCText, target_width int32, target_height int32, max_duration int32, volume_info *programmatic_creative_types.PCVideoVolumeInfo, additional_request map[string]string) (err error) {
	if err = p.sendProcessVideoCreative(header, user_info, src_video, landing_page, bottom_bar, watermark_list, video_text, target_width, target_height, max_duration, volume_info, additional_request); err != nil {
		return
	}
	return
}

func (p *VideoServerClient) sendProcessVideoCreative(header *common.RequestHeader, user_info *programmatic_creative_types.AppIdInfo, src_video *programmatic_creative_types.PCVideo, landing_page *programmatic_creative_types.PCVideoOverlayInfo, bottom_bar *programmatic_creative_types.PCVideoOverlayInfo, watermark_list []*programmatic_creative_types.PCVideoWatermarkInfo, video_text *programmatic_creative_types.PCText, target_width int32, target_height int32, max_duration int32, volume_info *programmatic_creative_types.PCVideoVolumeInfo, additional_request map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("processVideoCreative", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewProcessVideoCreativeArgs()
	args44.Header = header
	args44.UserInfo = user_info
	args44.SrcVideo = src_video
	args44.LandingPage = landing_page
	args44.BottomBar = bottom_bar
	args44.WatermarkList = watermark_list
	args44.VideoText = video_text
	args44.TargetWidth = target_width
	args44.TargetHeight = target_height
	args44.MaxDuration = max_duration
	args44.VolumeInfo = volume_info
	args44.AdditionalRequest = additional_request
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvProcessVideoCreative() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewProcessVideoCreativeResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

// 获取视频缩略图
//
// Parameters:
//  - Header
//  - SourceVideo
//  - ThumbnailRequest
func (p *VideoServerClient) GetVideoThumbnails(header *common.RequestHeader, source_video *programmatic_creative_types.PCVideo, thumbnail_request *programmatic_creative_types.PCVideoThumbnailRequest) (r []*programmatic_creative_types.PCImage, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetVideoThumbnails(header, source_video, thumbnail_request); err != nil {
		return
	}
	return p.recvGetVideoThumbnails()
}

func (p *VideoServerClient) sendGetVideoThumbnails(header *common.RequestHeader, source_video *programmatic_creative_types.PCVideo, thumbnail_request *programmatic_creative_types.PCVideoThumbnailRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getVideoThumbnails", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewGetVideoThumbnailsArgs()
	args48.Header = header
	args48.SourceVideo = source_video
	args48.ThumbnailRequest = thumbnail_request
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvGetVideoThumbnails() (value []*programmatic_creative_types.PCImage, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewGetVideoThumbnailsResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Rae != nil {
		rae = result49.Rae
	}
	return
}

// Parameters:
//  - Header
//  - SourceVideo
func (p *VideoServerClient) GetVideoFingerPrint(header *common.RequestHeader, source_video *programmatic_creative_types.PCVideo) (r string, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetVideoFingerPrint(header, source_video); err != nil {
		return
	}
	return p.recvGetVideoFingerPrint()
}

func (p *VideoServerClient) sendGetVideoFingerPrint(header *common.RequestHeader, source_video *programmatic_creative_types.PCVideo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getVideoFingerPrint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewGetVideoFingerPrintArgs()
	args52.Header = header
	args52.SourceVideo = source_video
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvGetVideoFingerPrint() (value string, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewGetVideoFingerPrintResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.Rae != nil {
		rae = result53.Rae
	}
	return
}

// Parameters:
//  - Header
//  - ComposInfo
func (p *VideoServerClient) ComposeMediaSeqVideo(header *common.RequestHeader, compos_info *programmatic_creative_types.PCMediaSeqVideoComposInfo) (err error) {
	if err = p.sendComposeMediaSeqVideo(header, compos_info); err != nil {
		return
	}
	return
}

func (p *VideoServerClient) sendComposeMediaSeqVideo(header *common.RequestHeader, compos_info *programmatic_creative_types.PCMediaSeqVideoComposInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("composeMediaSeqVideo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewComposeMediaSeqVideoArgs()
	args56.Header = header
	args56.ComposInfo = compos_info
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *VideoServerClient) recvComposeMediaSeqVideo() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewComposeMediaSeqVideoResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

type VideoServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewVideoServerProcessor(handler VideoServer) *VideoServerProcessor {
	self60 := &VideoServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self60.AddToProcessorMap("addAppVideoMaterial", &videoServerProcessorAddAppVideoMaterial{handler: handler})
	self60.AddToProcessorMap("clipVideoMaterial", &videoServerProcessorClipVideoMaterial{handler: handler})
	self60.AddToProcessorMap("removeVideoWatermark", &videoServerProcessorRemoveVideoWatermark{handler: handler})
	self60.AddToProcessorMap("convertVideoCodec", &videoServerProcessorConvertVideoCodec{handler: handler})
	self60.AddToProcessorMap("addVideoOverlay", &videoServerProcessorAddVideoOverlay{handler: handler})
	self60.AddToProcessorMap("concatVideos", &videoServerProcessorConcatVideos{handler: handler})
	self60.AddToProcessorMap("convertVideoMatchADX", &videoServerProcessorConvertVideoMatchADX{handler: handler})
	self60.AddToProcessorMap("processVideoMatchADX", &videoServerProcessorProcessVideoMatchADX{handler: handler})
	self60.AddToProcessorMap("getLandingpageImages", &videoServerProcessorGetLandingpageImages{handler: handler})
	self60.AddToProcessorMap("getVideoInfo", &videoServerProcessorGetVideoInfo{handler: handler})
	self60.AddToProcessorMap("refineVideoCreative", &videoServerProcessorRefineVideoCreative{handler: handler})
	self60.AddToProcessorMap("processVideoCreative", &videoServerProcessorProcessVideoCreative{handler: handler})
	self60.AddToProcessorMap("getVideoThumbnails", &videoServerProcessorGetVideoThumbnails{handler: handler})
	self60.AddToProcessorMap("getVideoFingerPrint", &videoServerProcessorGetVideoFingerPrint{handler: handler})
	self60.AddToProcessorMap("composeMediaSeqVideo", &videoServerProcessorComposeMediaSeqVideo{handler: handler})
	return self60
}

type videoServerProcessorAddAppVideoMaterial struct {
	handler VideoServer
}

func (p *videoServerProcessorAddAppVideoMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAppVideoMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addAppVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAppVideoMaterialResult()
	if result.Success, result.Rae, err = p.handler.AddAppVideoMaterial(args.Header, args.Request); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addAppVideoMaterial: "+err.Error())
		oprot.WriteMessageBegin("addAppVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addAppVideoMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorClipVideoMaterial struct {
	handler VideoServer
}

func (p *videoServerProcessorClipVideoMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewClipVideoMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("clipVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewClipVideoMaterialResult()
	if result.Success, result.Rae, err = p.handler.ClipVideoMaterial(args.Header, args.ClipList, args.SrcVideo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing clipVideoMaterial: "+err.Error())
		oprot.WriteMessageBegin("clipVideoMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("clipVideoMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorRemoveVideoWatermark struct {
	handler VideoServer
}

func (p *videoServerProcessorRemoveVideoWatermark) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRemoveVideoWatermarkArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("removeVideoWatermark", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRemoveVideoWatermarkResult()
	if result.Success, result.Rae, err = p.handler.RemoveVideoWatermark(args.Header, args.SrcVideo, args.WatermarkList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing removeVideoWatermark: "+err.Error())
		oprot.WriteMessageBegin("removeVideoWatermark", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("removeVideoWatermark", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorConvertVideoCodec struct {
	handler VideoServer
}

func (p *videoServerProcessorConvertVideoCodec) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConvertVideoCodecArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("convertVideoCodec", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConvertVideoCodecResult()
	if result.Success, result.Rae, err = p.handler.ConvertVideoCodec(args.Header, args.TargetCodec, args.SrcVideo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing convertVideoCodec: "+err.Error())
		oprot.WriteMessageBegin("convertVideoCodec", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("convertVideoCodec", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorAddVideoOverlay struct {
	handler VideoServer
}

func (p *videoServerProcessorAddVideoOverlay) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddVideoOverlayArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addVideoOverlay", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddVideoOverlayResult()
	if result.Success, result.Rae, err = p.handler.AddVideoOverlay(args.Header, args.SrcVideo, args.OverlayList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addVideoOverlay: "+err.Error())
		oprot.WriteMessageBegin("addVideoOverlay", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addVideoOverlay", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorConcatVideos struct {
	handler VideoServer
}

func (p *videoServerProcessorConcatVideos) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConcatVideosArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("concatVideos", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConcatVideosResult()
	if result.Success, result.Rae, err = p.handler.ConcatVideos(args.Header, args.VideoList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing concatVideos: "+err.Error())
		oprot.WriteMessageBegin("concatVideos", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("concatVideos", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorConvertVideoMatchADX struct {
	handler VideoServer
}

func (p *videoServerProcessorConvertVideoMatchADX) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConvertVideoMatchADXArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("convertVideoMatchADX", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConvertVideoMatchADXResult()
	if result.Success, result.Rae, err = p.handler.ConvertVideoMatchADX(args.Header, args.SrcVideo, args.EXList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing convertVideoMatchADX: "+err.Error())
		oprot.WriteMessageBegin("convertVideoMatchADX", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("convertVideoMatchADX", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorProcessVideoMatchADX struct {
	handler VideoServer
}

func (p *videoServerProcessorProcessVideoMatchADX) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewProcessVideoMatchADXArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("processVideoMatchADX", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewProcessVideoMatchADXResult()
	if err = p.handler.ProcessVideoMatchADX(args.Header, args.SrcVideo, args.AdxMatchIdDict); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing processVideoMatchADX: "+err.Error())
		oprot.WriteMessageBegin("processVideoMatchADX", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("processVideoMatchADX", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorGetLandingpageImages struct {
	handler VideoServer
}

func (p *videoServerProcessorGetLandingpageImages) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetLandingpageImagesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getLandingpageImages", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetLandingpageImagesResult()
	if result.Success, result.Rae, err = p.handler.GetLandingpageImages(args.Header, args.UserInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getLandingpageImages: "+err.Error())
		oprot.WriteMessageBegin("getLandingpageImages", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getLandingpageImages", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorGetVideoInfo struct {
	handler VideoServer
}

func (p *videoServerProcessorGetVideoInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetVideoInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getVideoInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetVideoInfoResult()
	if result.Success, result.Rae, err = p.handler.GetVideoInfo(args.Header, args.SrcVideo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getVideoInfo: "+err.Error())
		oprot.WriteMessageBegin("getVideoInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getVideoInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorRefineVideoCreative struct {
	handler VideoServer
}

func (p *videoServerProcessorRefineVideoCreative) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRefineVideoCreativeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("refineVideoCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRefineVideoCreativeResult()
	if result.Success, result.Rae, err = p.handler.RefineVideoCreative(args.Header, args.UserInfo, args.SrcVideo, args.LandingPage, args.BottomBar, args.WatermarkList, args.VideoText, args.TargetWidth, args.TargetHeight, args.MaxDuration, args.VolumeInfo, args.AdditionalRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing refineVideoCreative: "+err.Error())
		oprot.WriteMessageBegin("refineVideoCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("refineVideoCreative", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorProcessVideoCreative struct {
	handler VideoServer
}

func (p *videoServerProcessorProcessVideoCreative) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewProcessVideoCreativeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("processVideoCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewProcessVideoCreativeResult()
	if err = p.handler.ProcessVideoCreative(args.Header, args.UserInfo, args.SrcVideo, args.LandingPage, args.BottomBar, args.WatermarkList, args.VideoText, args.TargetWidth, args.TargetHeight, args.MaxDuration, args.VolumeInfo, args.AdditionalRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing processVideoCreative: "+err.Error())
		oprot.WriteMessageBegin("processVideoCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("processVideoCreative", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorGetVideoThumbnails struct {
	handler VideoServer
}

func (p *videoServerProcessorGetVideoThumbnails) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetVideoThumbnailsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getVideoThumbnails", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetVideoThumbnailsResult()
	if result.Success, result.Rae, err = p.handler.GetVideoThumbnails(args.Header, args.SourceVideo, args.ThumbnailRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getVideoThumbnails: "+err.Error())
		oprot.WriteMessageBegin("getVideoThumbnails", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getVideoThumbnails", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorGetVideoFingerPrint struct {
	handler VideoServer
}

func (p *videoServerProcessorGetVideoFingerPrint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetVideoFingerPrintArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getVideoFingerPrint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetVideoFingerPrintResult()
	if result.Success, result.Rae, err = p.handler.GetVideoFingerPrint(args.Header, args.SourceVideo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getVideoFingerPrint: "+err.Error())
		oprot.WriteMessageBegin("getVideoFingerPrint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getVideoFingerPrint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type videoServerProcessorComposeMediaSeqVideo struct {
	handler VideoServer
}

func (p *videoServerProcessorComposeMediaSeqVideo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewComposeMediaSeqVideoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("composeMediaSeqVideo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewComposeMediaSeqVideoResult()
	if err = p.handler.ComposeMediaSeqVideo(args.Header, args.ComposInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing composeMediaSeqVideo: "+err.Error())
		oprot.WriteMessageBegin("composeMediaSeqVideo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("composeMediaSeqVideo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddAppVideoMaterialArgs struct {
	Header  *common.RequestHeader                           `thrift:"header,1" json:"header"`
	Request *programmatic_creative_types.AppMaterialRequest `thrift:"request,2" json:"request"`
}

func NewAddAppVideoMaterialArgs() *AddAppVideoMaterialArgs {
	return &AddAppVideoMaterialArgs{}
}

func (p *AddAppVideoMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppVideoMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAppVideoMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *AddAppVideoMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppVideoMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppVideoMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAppVideoMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *AddAppVideoMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppVideoMaterialArgs(%+v)", *p)
}

type AddAppVideoMaterialResult struct {
	Success programmatic_creative_types.PCSStatus     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddAppVideoMaterialResult() *AddAppVideoMaterialResult {
	return &AddAppVideoMaterialResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddAppVideoMaterialResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddAppVideoMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppVideoMaterialResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = programmatic_creative_types.PCSStatus(v)
	}
	return nil
}

func (p *AddAppVideoMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddAppVideoMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppVideoMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppVideoMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddAppVideoMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddAppVideoMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppVideoMaterialResult(%+v)", *p)
}

type ClipVideoMaterialArgs struct {
	Header   *common.RequestHeader                          `thrift:"header,1" json:"header"`
	ClipList []*programmatic_creative_types.PCVideoClipInfo `thrift:"clip_list,2" json:"clip_list"`
	SrcVideo *programmatic_creative_types.PCVideo           `thrift:"src_video,3" json:"src_video"`
}

func NewClipVideoMaterialArgs() *ClipVideoMaterialArgs {
	return &ClipVideoMaterialArgs{}
}

func (p *ClipVideoMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ClipVideoMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ClipVideoMaterialArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClipList = make([]*programmatic_creative_types.PCVideoClipInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem61 := programmatic_creative_types.NewPCVideoClipInfo()
		if err := _elem61.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem61)
		}
		p.ClipList = append(p.ClipList, _elem61)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ClipVideoMaterialArgs) readField3(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *ClipVideoMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("clipVideoMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ClipVideoMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ClipVideoMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ClipList != nil {
		if err := oprot.WriteFieldBegin("clip_list", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:clip_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ClipList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClipList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:clip_list: %s", p, err)
		}
	}
	return err
}

func (p *ClipVideoMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:src_video: %s", p, err)
		}
	}
	return err
}

func (p *ClipVideoMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ClipVideoMaterialArgs(%+v)", *p)
}

type ClipVideoMaterialResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewClipVideoMaterialResult() *ClipVideoMaterialResult {
	return &ClipVideoMaterialResult{}
}

func (p *ClipVideoMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ClipVideoMaterialResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ClipVideoMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *ClipVideoMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("clipVideoMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ClipVideoMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ClipVideoMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *ClipVideoMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ClipVideoMaterialResult(%+v)", *p)
}

type RemoveVideoWatermarkArgs struct {
	Header        *common.RequestHeader                               `thrift:"header,1" json:"header"`
	SrcVideo      *programmatic_creative_types.PCVideo                `thrift:"src_video,2" json:"src_video"`
	WatermarkList []*programmatic_creative_types.PCVideoWatermarkInfo `thrift:"watermark_list,3" json:"watermark_list"`
}

func NewRemoveVideoWatermarkArgs() *RemoveVideoWatermarkArgs {
	return &RemoveVideoWatermarkArgs{}
}

func (p *RemoveVideoWatermarkArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveVideoWatermarkArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RemoveVideoWatermarkArgs) readField2(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *RemoveVideoWatermarkArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.WatermarkList = make([]*programmatic_creative_types.PCVideoWatermarkInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem62 := programmatic_creative_types.NewPCVideoWatermarkInfo()
		if err := _elem62.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem62)
		}
		p.WatermarkList = append(p.WatermarkList, _elem62)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RemoveVideoWatermarkArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeVideoWatermark_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveVideoWatermarkArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RemoveVideoWatermarkArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:src_video: %s", p, err)
		}
	}
	return err
}

func (p *RemoveVideoWatermarkArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.WatermarkList != nil {
		if err := oprot.WriteFieldBegin("watermark_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:watermark_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.WatermarkList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.WatermarkList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:watermark_list: %s", p, err)
		}
	}
	return err
}

func (p *RemoveVideoWatermarkArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveVideoWatermarkArgs(%+v)", *p)
}

type RemoveVideoWatermarkResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewRemoveVideoWatermarkResult() *RemoveVideoWatermarkResult {
	return &RemoveVideoWatermarkResult{}
}

func (p *RemoveVideoWatermarkResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveVideoWatermarkResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RemoveVideoWatermarkResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *RemoveVideoWatermarkResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeVideoWatermark_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveVideoWatermarkResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RemoveVideoWatermarkResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *RemoveVideoWatermarkResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveVideoWatermarkResult(%+v)", *p)
}

type ConvertVideoCodecArgs struct {
	Header      *common.RequestHeader                         `thrift:"header,1" json:"header"`
	TargetCodec *programmatic_creative_types.PCVideoCodecInfo `thrift:"target_codec,2" json:"target_codec"`
	SrcVideo    *programmatic_creative_types.PCVideo          `thrift:"src_video,3" json:"src_video"`
}

func NewConvertVideoCodecArgs() *ConvertVideoCodecArgs {
	return &ConvertVideoCodecArgs{}
}

func (p *ConvertVideoCodecArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoCodecArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ConvertVideoCodecArgs) readField2(iprot thrift.TProtocol) error {
	p.TargetCodec = programmatic_creative_types.NewPCVideoCodecInfo()
	if err := p.TargetCodec.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TargetCodec)
	}
	return nil
}

func (p *ConvertVideoCodecArgs) readField3(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *ConvertVideoCodecArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("convertVideoCodec_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoCodecArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoCodecArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TargetCodec != nil {
		if err := oprot.WriteFieldBegin("target_codec", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:target_codec: %s", p, err)
		}
		if err := p.TargetCodec.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TargetCodec)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:target_codec: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoCodecArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:src_video: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoCodecArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertVideoCodecArgs(%+v)", *p)
}

type ConvertVideoCodecResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewConvertVideoCodecResult() *ConvertVideoCodecResult {
	return &ConvertVideoCodecResult{}
}

func (p *ConvertVideoCodecResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoCodecResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConvertVideoCodecResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *ConvertVideoCodecResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("convertVideoCodec_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoCodecResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoCodecResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoCodecResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertVideoCodecResult(%+v)", *p)
}

type AddVideoOverlayArgs struct {
	Header      *common.RequestHeader                             `thrift:"header,1" json:"header"`
	SrcVideo    *programmatic_creative_types.PCVideo              `thrift:"src_video,2" json:"src_video"`
	OverlayList []*programmatic_creative_types.PCVideoOverlayInfo `thrift:"overlay_list,3" json:"overlay_list"`
}

func NewAddVideoOverlayArgs() *AddVideoOverlayArgs {
	return &AddVideoOverlayArgs{}
}

func (p *AddVideoOverlayArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddVideoOverlayArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddVideoOverlayArgs) readField2(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *AddVideoOverlayArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OverlayList = make([]*programmatic_creative_types.PCVideoOverlayInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem63 := programmatic_creative_types.NewPCVideoOverlayInfo()
		if err := _elem63.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem63)
		}
		p.OverlayList = append(p.OverlayList, _elem63)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddVideoOverlayArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addVideoOverlay_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddVideoOverlayArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoOverlayArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:src_video: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoOverlayArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.OverlayList != nil {
		if err := oprot.WriteFieldBegin("overlay_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:overlay_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.OverlayList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OverlayList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:overlay_list: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoOverlayArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddVideoOverlayArgs(%+v)", *p)
}

type AddVideoOverlayResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewAddVideoOverlayResult() *AddVideoOverlayResult {
	return &AddVideoOverlayResult{}
}

func (p *AddVideoOverlayResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddVideoOverlayResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddVideoOverlayResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddVideoOverlayResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addVideoOverlay_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddVideoOverlayResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoOverlayResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddVideoOverlayResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddVideoOverlayResult(%+v)", *p)
}

type ConcatVideosArgs struct {
	Header    *common.RequestHeader                  `thrift:"header,1" json:"header"`
	VideoList []*programmatic_creative_types.PCVideo `thrift:"video_list,2" json:"video_list"`
}

func NewConcatVideosArgs() *ConcatVideosArgs {
	return &ConcatVideosArgs{}
}

func (p *ConcatVideosArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConcatVideosArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ConcatVideosArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoList = make([]*programmatic_creative_types.PCVideo, 0, size)
	for i := 0; i < size; i++ {
		_elem64 := programmatic_creative_types.NewPCVideo()
		if err := _elem64.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem64)
		}
		p.VideoList = append(p.VideoList, _elem64)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ConcatVideosArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("concatVideos_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConcatVideosArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ConcatVideosArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.VideoList != nil {
		if err := oprot.WriteFieldBegin("video_list", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:video_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VideoList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:video_list: %s", p, err)
		}
	}
	return err
}

func (p *ConcatVideosArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConcatVideosArgs(%+v)", *p)
}

type ConcatVideosResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewConcatVideosResult() *ConcatVideosResult {
	return &ConcatVideosResult{}
}

func (p *ConcatVideosResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConcatVideosResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConcatVideosResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *ConcatVideosResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("concatVideos_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConcatVideosResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConcatVideosResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *ConcatVideosResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConcatVideosResult(%+v)", *p)
}

type ConvertVideoMatchADXArgs struct {
	Header   *common.RequestHeader                `thrift:"header,1" json:"header"`
	SrcVideo *programmatic_creative_types.PCVideo `thrift:"src_video,2" json:"src_video"`
	EXList   []int32                              `thrift:"EX_list,3" json:"EX_list"`
}

func NewConvertVideoMatchADXArgs() *ConvertVideoMatchADXArgs {
	return &ConvertVideoMatchADXArgs{}
}

func (p *ConvertVideoMatchADXArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoMatchADXArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ConvertVideoMatchADXArgs) readField2(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *ConvertVideoMatchADXArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.EXList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem65 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem65 = v
		}
		p.EXList = append(p.EXList, _elem65)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ConvertVideoMatchADXArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("convertVideoMatchADX_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoMatchADXArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoMatchADXArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:src_video: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoMatchADXArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.EXList != nil {
		if err := oprot.WriteFieldBegin("EX_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:EX_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.EXList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.EXList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:EX_list: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoMatchADXArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertVideoMatchADXArgs(%+v)", *p)
}

type ConvertVideoMatchADXResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewConvertVideoMatchADXResult() *ConvertVideoMatchADXResult {
	return &ConvertVideoMatchADXResult{}
}

func (p *ConvertVideoMatchADXResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoMatchADXResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConvertVideoMatchADXResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *ConvertVideoMatchADXResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("convertVideoMatchADX_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConvertVideoMatchADXResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoMatchADXResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *ConvertVideoMatchADXResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertVideoMatchADXResult(%+v)", *p)
}

type ProcessVideoMatchADXArgs struct {
	Header         *common.RequestHeader                `thrift:"header,1" json:"header"`
	SrcVideo       *programmatic_creative_types.PCVideo `thrift:"src_video,2" json:"src_video"`
	AdxMatchIdDict map[int32]int32                      `thrift:"adx_match_id_dict,3" json:"adx_match_id_dict"`
}

func NewProcessVideoMatchADXArgs() *ProcessVideoMatchADXArgs {
	return &ProcessVideoMatchADXArgs{}
}

func (p *ProcessVideoMatchADXArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoMatchADXArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ProcessVideoMatchADXArgs) readField2(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *ProcessVideoMatchADXArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AdxMatchIdDict = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key66 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key66 = v
		}
		var _val67 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val67 = v
		}
		p.AdxMatchIdDict[_key66] = _val67
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ProcessVideoMatchADXArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processVideoMatchADX_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoMatchADXArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoMatchADXArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:src_video: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoMatchADXArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AdxMatchIdDict != nil {
		if err := oprot.WriteFieldBegin("adx_match_id_dict", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:adx_match_id_dict: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.AdxMatchIdDict)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AdxMatchIdDict {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:adx_match_id_dict: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoMatchADXArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessVideoMatchADXArgs(%+v)", *p)
}

type ProcessVideoMatchADXResult struct {
}

func NewProcessVideoMatchADXResult() *ProcessVideoMatchADXResult {
	return &ProcessVideoMatchADXResult{}
}

func (p *ProcessVideoMatchADXResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoMatchADXResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processVideoMatchADX_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoMatchADXResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessVideoMatchADXResult(%+v)", *p)
}

type GetLandingpageImagesArgs struct {
	Header   *common.RequestHeader                  `thrift:"header,1" json:"header"`
	UserInfo *programmatic_creative_types.AppIdInfo `thrift:"user_info,2" json:"user_info"`
}

func NewGetLandingpageImagesArgs() *GetLandingpageImagesArgs {
	return &GetLandingpageImagesArgs{}
}

func (p *GetLandingpageImagesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLandingpageImagesArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetLandingpageImagesArgs) readField2(iprot thrift.TProtocol) error {
	p.UserInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.UserInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserInfo)
	}
	return nil
}

func (p *GetLandingpageImagesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLandingpageImages_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLandingpageImagesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetLandingpageImagesArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UserInfo != nil {
		if err := oprot.WriteFieldBegin("user_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:user_info: %s", p, err)
		}
		if err := p.UserInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:user_info: %s", p, err)
		}
	}
	return err
}

func (p *GetLandingpageImagesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLandingpageImagesArgs(%+v)", *p)
}

type GetLandingpageImagesResult struct {
	Success *programmatic_creative_types.MaterialsInfo `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException  `thrift:"rae,1" json:"rae"`
}

func NewGetLandingpageImagesResult() *GetLandingpageImagesResult {
	return &GetLandingpageImagesResult{}
}

func (p *GetLandingpageImagesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetLandingpageImagesResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewMaterialsInfo()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetLandingpageImagesResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetLandingpageImagesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getLandingpageImages_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetLandingpageImagesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetLandingpageImagesResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetLandingpageImagesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetLandingpageImagesResult(%+v)", *p)
}

type GetVideoInfoArgs struct {
	Header   *common.RequestHeader                `thrift:"header,1" json:"header"`
	SrcVideo *programmatic_creative_types.PCVideo `thrift:"src_video,2" json:"src_video"`
}

func NewGetVideoInfoArgs() *GetVideoInfoArgs {
	return &GetVideoInfoArgs{}
}

func (p *GetVideoInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVideoInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetVideoInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *GetVideoInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVideoInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVideoInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:src_video: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVideoInfoArgs(%+v)", *p)
}

type GetVideoInfoResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewGetVideoInfoResult() *GetVideoInfoResult {
	return &GetVideoInfoResult{}
}

func (p *GetVideoInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVideoInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetVideoInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetVideoInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVideoInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVideoInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVideoInfoResult(%+v)", *p)
}

type RefineVideoCreativeArgs struct {
	Header      *common.RequestHeader                           `thrift:"header,1" json:"header"`
	UserInfo    *programmatic_creative_types.AppIdInfo          `thrift:"user_info,2" json:"user_info"`
	SrcVideo    *programmatic_creative_types.PCVideo            `thrift:"src_video,3" json:"src_video"`
	LandingPage *programmatic_creative_types.PCVideoOverlayInfo `thrift:"landing_page,4" json:"landing_page"`
	// unused field # 5
	BottomBar         *programmatic_creative_types.PCVideoOverlayInfo     `thrift:"bottom_bar,6" json:"bottom_bar"`
	WatermarkList     []*programmatic_creative_types.PCVideoWatermarkInfo `thrift:"watermark_list,7" json:"watermark_list"`
	VideoText         *programmatic_creative_types.PCText                 `thrift:"video_text,8" json:"video_text"`
	TargetWidth       int32                                               `thrift:"target_width,9" json:"target_width"`
	TargetHeight      int32                                               `thrift:"target_height,10" json:"target_height"`
	MaxDuration       int32                                               `thrift:"max_duration,11" json:"max_duration"`
	VolumeInfo        *programmatic_creative_types.PCVideoVolumeInfo      `thrift:"volume_info,12" json:"volume_info"`
	AdditionalRequest map[string]string                                   `thrift:"additional_request,13" json:"additional_request"`
}

func NewRefineVideoCreativeArgs() *RefineVideoCreativeArgs {
	return &RefineVideoCreativeArgs{}
}

func (p *RefineVideoCreativeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.MAP {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField2(iprot thrift.TProtocol) error {
	p.UserInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.UserInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserInfo)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField3(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField4(iprot thrift.TProtocol) error {
	p.LandingPage = programmatic_creative_types.NewPCVideoOverlayInfo()
	if err := p.LandingPage.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.LandingPage)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField6(iprot thrift.TProtocol) error {
	p.BottomBar = programmatic_creative_types.NewPCVideoOverlayInfo()
	if err := p.BottomBar.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.BottomBar)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.WatermarkList = make([]*programmatic_creative_types.PCVideoWatermarkInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem68 := programmatic_creative_types.NewPCVideoWatermarkInfo()
		if err := _elem68.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem68)
		}
		p.WatermarkList = append(p.WatermarkList, _elem68)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField8(iprot thrift.TProtocol) error {
	p.VideoText = programmatic_creative_types.NewPCText()
	if err := p.VideoText.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VideoText)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.TargetWidth = v
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TargetHeight = v
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.MaxDuration = v
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField12(iprot thrift.TProtocol) error {
	p.VolumeInfo = programmatic_creative_types.NewPCVideoVolumeInfo()
	if err := p.VolumeInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VolumeInfo)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) readField13(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AdditionalRequest = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key69 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key69 = v
		}
		var _val70 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val70 = v
		}
		p.AdditionalRequest[_key69] = _val70
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("refineVideoCreative_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RefineVideoCreativeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UserInfo != nil {
		if err := oprot.WriteFieldBegin("user_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:user_info: %s", p, err)
		}
		if err := p.UserInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:user_info: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:src_video: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.LandingPage != nil {
		if err := oprot.WriteFieldBegin("landing_page", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:landing_page: %s", p, err)
		}
		if err := p.LandingPage.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.LandingPage)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:landing_page: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.BottomBar != nil {
		if err := oprot.WriteFieldBegin("bottom_bar", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:bottom_bar: %s", p, err)
		}
		if err := p.BottomBar.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.BottomBar)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:bottom_bar: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.WatermarkList != nil {
		if err := oprot.WriteFieldBegin("watermark_list", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:watermark_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.WatermarkList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.WatermarkList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:watermark_list: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if p.VideoText != nil {
		if err := oprot.WriteFieldBegin("video_text", thrift.STRUCT, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:video_text: %s", p, err)
		}
		if err := p.VideoText.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VideoText)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:video_text: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("target_width", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:target_width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetWidth)); err != nil {
		return fmt.Errorf("%T.target_width (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:target_width: %s", p, err)
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("target_height", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:target_height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetHeight)); err != nil {
		return fmt.Errorf("%T.target_height (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:target_height: %s", p, err)
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_duration", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:max_duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxDuration)); err != nil {
		return fmt.Errorf("%T.max_duration (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:max_duration: %s", p, err)
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if p.VolumeInfo != nil {
		if err := oprot.WriteFieldBegin("volume_info", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:volume_info: %s", p, err)
		}
		if err := p.VolumeInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VolumeInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:volume_info: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if p.AdditionalRequest != nil {
		if err := oprot.WriteFieldBegin("additional_request", thrift.MAP, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:additional_request: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AdditionalRequest)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AdditionalRequest {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:additional_request: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefineVideoCreativeArgs(%+v)", *p)
}

type RefineVideoCreativeResult struct {
	Success *programmatic_creative_types.PCVideoProcResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException      `thrift:"rae,1" json:"rae"`
}

func NewRefineVideoCreativeResult() *RefineVideoCreativeResult {
	return &RefineVideoCreativeResult{}
}

func (p *RefineVideoCreativeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RefineVideoCreativeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCVideoProcResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RefineVideoCreativeResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *RefineVideoCreativeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("refineVideoCreative_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RefineVideoCreativeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *RefineVideoCreativeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RefineVideoCreativeResult(%+v)", *p)
}

type ProcessVideoCreativeArgs struct {
	Header      *common.RequestHeader                           `thrift:"header,1" json:"header"`
	UserInfo    *programmatic_creative_types.AppIdInfo          `thrift:"user_info,2" json:"user_info"`
	SrcVideo    *programmatic_creative_types.PCVideo            `thrift:"src_video,3" json:"src_video"`
	LandingPage *programmatic_creative_types.PCVideoOverlayInfo `thrift:"landing_page,4" json:"landing_page"`
	// unused field # 5
	BottomBar         *programmatic_creative_types.PCVideoOverlayInfo     `thrift:"bottom_bar,6" json:"bottom_bar"`
	WatermarkList     []*programmatic_creative_types.PCVideoWatermarkInfo `thrift:"watermark_list,7" json:"watermark_list"`
	VideoText         *programmatic_creative_types.PCText                 `thrift:"video_text,8" json:"video_text"`
	TargetWidth       int32                                               `thrift:"target_width,9" json:"target_width"`
	TargetHeight      int32                                               `thrift:"target_height,10" json:"target_height"`
	MaxDuration       int32                                               `thrift:"max_duration,11" json:"max_duration"`
	VolumeInfo        *programmatic_creative_types.PCVideoVolumeInfo      `thrift:"volume_info,12" json:"volume_info"`
	AdditionalRequest map[string]string                                   `thrift:"additional_request,13" json:"additional_request"`
}

func NewProcessVideoCreativeArgs() *ProcessVideoCreativeArgs {
	return &ProcessVideoCreativeArgs{}
}

func (p *ProcessVideoCreativeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.MAP {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField2(iprot thrift.TProtocol) error {
	p.UserInfo = programmatic_creative_types.NewAppIdInfo()
	if err := p.UserInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserInfo)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField3(iprot thrift.TProtocol) error {
	p.SrcVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SrcVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcVideo)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField4(iprot thrift.TProtocol) error {
	p.LandingPage = programmatic_creative_types.NewPCVideoOverlayInfo()
	if err := p.LandingPage.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.LandingPage)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField6(iprot thrift.TProtocol) error {
	p.BottomBar = programmatic_creative_types.NewPCVideoOverlayInfo()
	if err := p.BottomBar.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.BottomBar)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.WatermarkList = make([]*programmatic_creative_types.PCVideoWatermarkInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem71 := programmatic_creative_types.NewPCVideoWatermarkInfo()
		if err := _elem71.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem71)
		}
		p.WatermarkList = append(p.WatermarkList, _elem71)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField8(iprot thrift.TProtocol) error {
	p.VideoText = programmatic_creative_types.NewPCText()
	if err := p.VideoText.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VideoText)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.TargetWidth = v
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TargetHeight = v
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.MaxDuration = v
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField12(iprot thrift.TProtocol) error {
	p.VolumeInfo = programmatic_creative_types.NewPCVideoVolumeInfo()
	if err := p.VolumeInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VolumeInfo)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) readField13(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AdditionalRequest = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key72 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key72 = v
		}
		var _val73 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val73 = v
		}
		p.AdditionalRequest[_key72] = _val73
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processVideoCreative_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoCreativeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.UserInfo != nil {
		if err := oprot.WriteFieldBegin("user_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:user_info: %s", p, err)
		}
		if err := p.UserInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:user_info: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SrcVideo != nil {
		if err := oprot.WriteFieldBegin("src_video", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:src_video: %s", p, err)
		}
		if err := p.SrcVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:src_video: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.LandingPage != nil {
		if err := oprot.WriteFieldBegin("landing_page", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:landing_page: %s", p, err)
		}
		if err := p.LandingPage.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.LandingPage)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:landing_page: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.BottomBar != nil {
		if err := oprot.WriteFieldBegin("bottom_bar", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:bottom_bar: %s", p, err)
		}
		if err := p.BottomBar.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.BottomBar)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:bottom_bar: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.WatermarkList != nil {
		if err := oprot.WriteFieldBegin("watermark_list", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:watermark_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.WatermarkList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.WatermarkList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:watermark_list: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if p.VideoText != nil {
		if err := oprot.WriteFieldBegin("video_text", thrift.STRUCT, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:video_text: %s", p, err)
		}
		if err := p.VideoText.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VideoText)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:video_text: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("target_width", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:target_width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetWidth)); err != nil {
		return fmt.Errorf("%T.target_width (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:target_width: %s", p, err)
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("target_height", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:target_height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetHeight)); err != nil {
		return fmt.Errorf("%T.target_height (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:target_height: %s", p, err)
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_duration", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:max_duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxDuration)); err != nil {
		return fmt.Errorf("%T.max_duration (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:max_duration: %s", p, err)
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if p.VolumeInfo != nil {
		if err := oprot.WriteFieldBegin("volume_info", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:volume_info: %s", p, err)
		}
		if err := p.VolumeInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VolumeInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:volume_info: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) writeField13(oprot thrift.TProtocol) (err error) {
	if p.AdditionalRequest != nil {
		if err := oprot.WriteFieldBegin("additional_request", thrift.MAP, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:additional_request: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AdditionalRequest)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AdditionalRequest {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:additional_request: %s", p, err)
		}
	}
	return err
}

func (p *ProcessVideoCreativeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessVideoCreativeArgs(%+v)", *p)
}

type ProcessVideoCreativeResult struct {
}

func NewProcessVideoCreativeResult() *ProcessVideoCreativeResult {
	return &ProcessVideoCreativeResult{}
}

func (p *ProcessVideoCreativeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoCreativeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processVideoCreative_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessVideoCreativeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessVideoCreativeResult(%+v)", *p)
}

type GetVideoThumbnailsArgs struct {
	Header           *common.RequestHeader                                `thrift:"header,1" json:"header"`
	SourceVideo      *programmatic_creative_types.PCVideo                 `thrift:"source_video,2" json:"source_video"`
	ThumbnailRequest *programmatic_creative_types.PCVideoThumbnailRequest `thrift:"thumbnail_request,3" json:"thumbnail_request"`
}

func NewGetVideoThumbnailsArgs() *GetVideoThumbnailsArgs {
	return &GetVideoThumbnailsArgs{}
}

func (p *GetVideoThumbnailsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVideoThumbnailsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetVideoThumbnailsArgs) readField2(iprot thrift.TProtocol) error {
	p.SourceVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SourceVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SourceVideo)
	}
	return nil
}

func (p *GetVideoThumbnailsArgs) readField3(iprot thrift.TProtocol) error {
	p.ThumbnailRequest = programmatic_creative_types.NewPCVideoThumbnailRequest()
	if err := p.ThumbnailRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ThumbnailRequest)
	}
	return nil
}

func (p *GetVideoThumbnailsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVideoThumbnails_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVideoThumbnailsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoThumbnailsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SourceVideo != nil {
		if err := oprot.WriteFieldBegin("source_video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:source_video: %s", p, err)
		}
		if err := p.SourceVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SourceVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:source_video: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoThumbnailsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ThumbnailRequest != nil {
		if err := oprot.WriteFieldBegin("thumbnail_request", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:thumbnail_request: %s", p, err)
		}
		if err := p.ThumbnailRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ThumbnailRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:thumbnail_request: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoThumbnailsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVideoThumbnailsArgs(%+v)", *p)
}

type GetVideoThumbnailsResult struct {
	Success []*programmatic_creative_types.PCImage    `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewGetVideoThumbnailsResult() *GetVideoThumbnailsResult {
	return &GetVideoThumbnailsResult{}
}

func (p *GetVideoThumbnailsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVideoThumbnailsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*programmatic_creative_types.PCImage, 0, size)
	for i := 0; i < size; i++ {
		_elem74 := programmatic_creative_types.NewPCImage()
		if err := _elem74.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem74)
		}
		p.Success = append(p.Success, _elem74)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetVideoThumbnailsResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetVideoThumbnailsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVideoThumbnails_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVideoThumbnailsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoThumbnailsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoThumbnailsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVideoThumbnailsResult(%+v)", *p)
}

type GetVideoFingerPrintArgs struct {
	Header      *common.RequestHeader                `thrift:"header,1" json:"header"`
	SourceVideo *programmatic_creative_types.PCVideo `thrift:"source_video,2" json:"source_video"`
}

func NewGetVideoFingerPrintArgs() *GetVideoFingerPrintArgs {
	return &GetVideoFingerPrintArgs{}
}

func (p *GetVideoFingerPrintArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVideoFingerPrintArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetVideoFingerPrintArgs) readField2(iprot thrift.TProtocol) error {
	p.SourceVideo = programmatic_creative_types.NewPCVideo()
	if err := p.SourceVideo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SourceVideo)
	}
	return nil
}

func (p *GetVideoFingerPrintArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVideoFingerPrint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVideoFingerPrintArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoFingerPrintArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SourceVideo != nil {
		if err := oprot.WriteFieldBegin("source_video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:source_video: %s", p, err)
		}
		if err := p.SourceVideo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SourceVideo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:source_video: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoFingerPrintArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVideoFingerPrintArgs(%+v)", *p)
}

type GetVideoFingerPrintResult struct {
	Success string                                    `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewGetVideoFingerPrintResult() *GetVideoFingerPrintResult {
	return &GetVideoFingerPrintResult{}
}

func (p *GetVideoFingerPrintResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetVideoFingerPrintResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetVideoFingerPrintResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetVideoFingerPrintResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getVideoFingerPrint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetVideoFingerPrintResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetVideoFingerPrintResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetVideoFingerPrintResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetVideoFingerPrintResult(%+v)", *p)
}

type ComposeMediaSeqVideoArgs struct {
	Header     *common.RequestHeader                                  `thrift:"header,1" json:"header"`
	ComposInfo *programmatic_creative_types.PCMediaSeqVideoComposInfo `thrift:"compos_info,2" json:"compos_info"`
}

func NewComposeMediaSeqVideoArgs() *ComposeMediaSeqVideoArgs {
	return &ComposeMediaSeqVideoArgs{}
}

func (p *ComposeMediaSeqVideoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ComposeMediaSeqVideoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ComposeMediaSeqVideoArgs) readField2(iprot thrift.TProtocol) error {
	p.ComposInfo = programmatic_creative_types.NewPCMediaSeqVideoComposInfo()
	if err := p.ComposInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ComposInfo)
	}
	return nil
}

func (p *ComposeMediaSeqVideoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("composeMediaSeqVideo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ComposeMediaSeqVideoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ComposeMediaSeqVideoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ComposInfo != nil {
		if err := oprot.WriteFieldBegin("compos_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:compos_info: %s", p, err)
		}
		if err := p.ComposInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ComposInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:compos_info: %s", p, err)
		}
	}
	return err
}

func (p *ComposeMediaSeqVideoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ComposeMediaSeqVideoArgs(%+v)", *p)
}

type ComposeMediaSeqVideoResult struct {
}

func NewComposeMediaSeqVideoResult() *ComposeMediaSeqVideoResult {
	return &ComposeMediaSeqVideoResult{}
}

func (p *ComposeMediaSeqVideoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ComposeMediaSeqVideoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("composeMediaSeqVideo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ComposeMediaSeqVideoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ComposeMediaSeqVideoResult(%+v)", *p)
}
