// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"video_server"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  PCSStatus addAppVideoMaterial(RequestHeader header, AppMaterialRequest request)")
	fmt.Fprintln(os.Stderr, "  PCVideoProcResult clipVideoMaterial(RequestHeader header,  clip_list, PCVideo src_video)")
	fmt.Fprintln(os.St<PERSON>r, "  PCVideoProcResult removeVideoWatermark(RequestHeader header, PCVideo src_video,  watermark_list)")
	fmt.Fprintln(os.Stderr, "  PCVideoProcResult convertVideoCodec(RequestHeader header, PCVideoCodecInfo target_codec, PCVideo src_video)")
	fmt.Fprintln(os.Stderr, "  PCVideoProcResult addVideoOverlay(RequestHeader header, PCVideo src_video,  overlay_list)")
	fmt.Fprintln(os.Stderr, "  PCVideoProcResult concatVideos(RequestHeader header,  video_list)")
	fmt.Fprintln(os.Stderr, "  PCVideoProcResult convertVideoMatchADX(RequestHeader header, PCVideo src_video,  EX_list)")
	fmt.Fprintln(os.Stderr, "  void processVideoMatchADX(RequestHeader header, PCVideo src_video,  adx_match_id_dict)")
	fmt.Fprintln(os.Stderr, "  MaterialsInfo getLandingpageImages(RequestHeader header, AppIdInfo user_info)")
	fmt.Fprintln(os.Stderr, "  PCVideoProcResult getVideoInfo(RequestHeader header, PCVideo src_video)")
	fmt.Fprintln(os.Stderr, "  PCVideoProcResult refineVideoCreative(RequestHeader header, AppIdInfo user_info, PCVideo src_video, PCVideoOverlayInfo landing_page, PCVideoOverlayInfo bottom_bar,  watermark_list, PCText video_text, i32 target_width, i32 target_height, i32 max_duration, PCVideoVolumeInfo volume_info,  additional_request)")
	fmt.Fprintln(os.Stderr, "  void processVideoCreative(RequestHeader header, AppIdInfo user_info, PCVideo src_video, PCVideoOverlayInfo landing_page, PCVideoOverlayInfo bottom_bar,  watermark_list, PCText video_text, i32 target_width, i32 target_height, i32 max_duration, PCVideoVolumeInfo volume_info,  additional_request)")
	fmt.Fprintln(os.Stderr, "   getVideoThumbnails(RequestHeader header, PCVideo source_video, PCVideoThumbnailRequest thumbnail_request)")
	fmt.Fprintln(os.Stderr, "  string getVideoFingerPrint(RequestHeader header, PCVideo source_video)")
	fmt.Fprintln(os.Stderr, "  void composeMediaSeqVideo(RequestHeader header, PCMediaSeqVideoComposInfo compos_info)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := video_server.NewVideoServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addAppVideoMaterial":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAppVideoMaterial requires 2 args")
			flag.Usage()
		}
		arg75 := flag.Arg(1)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue0 := video_server.NewRequestHeader()
		err80 := argvalue0.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg81 := flag.Arg(2)
		mbTrans82 := thrift.NewTMemoryBufferLen(len(arg81))
		defer mbTrans82.Close()
		_, err83 := mbTrans82.WriteString(arg81)
		if err83 != nil {
			Usage()
			return
		}
		factory84 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt85 := factory84.GetProtocol(mbTrans82)
		argvalue1 := video_server.NewAppMaterialRequest()
		err86 := argvalue1.Read(jsProt85)
		if err86 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAppVideoMaterial(value0, value1))
		fmt.Print("\n")
		break
	case "clipVideoMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ClipVideoMaterial requires 3 args")
			flag.Usage()
		}
		arg87 := flag.Arg(1)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue0 := video_server.NewRequestHeader()
		err92 := argvalue0.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg93 := flag.Arg(2)
		mbTrans94 := thrift.NewTMemoryBufferLen(len(arg93))
		defer mbTrans94.Close()
		_, err95 := mbTrans94.WriteString(arg93)
		if err95 != nil {
			Usage()
			return
		}
		factory96 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt97 := factory96.GetProtocol(mbTrans94)
		containerStruct1 := video_server.NewClipVideoMaterialArgs()
		err98 := containerStruct1.ReadField2(jsProt97)
		if err98 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ClipList
		value1 := argvalue1
		arg99 := flag.Arg(3)
		mbTrans100 := thrift.NewTMemoryBufferLen(len(arg99))
		defer mbTrans100.Close()
		_, err101 := mbTrans100.WriteString(arg99)
		if err101 != nil {
			Usage()
			return
		}
		factory102 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt103 := factory102.GetProtocol(mbTrans100)
		argvalue2 := video_server.NewPCVideo()
		err104 := argvalue2.Read(jsProt103)
		if err104 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ClipVideoMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "removeVideoWatermark":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "RemoveVideoWatermark requires 3 args")
			flag.Usage()
		}
		arg105 := flag.Arg(1)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		argvalue0 := video_server.NewRequestHeader()
		err110 := argvalue0.Read(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg111 := flag.Arg(2)
		mbTrans112 := thrift.NewTMemoryBufferLen(len(arg111))
		defer mbTrans112.Close()
		_, err113 := mbTrans112.WriteString(arg111)
		if err113 != nil {
			Usage()
			return
		}
		factory114 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt115 := factory114.GetProtocol(mbTrans112)
		argvalue1 := video_server.NewPCVideo()
		err116 := argvalue1.Read(jsProt115)
		if err116 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg117 := flag.Arg(3)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		containerStruct2 := video_server.NewRemoveVideoWatermarkArgs()
		err122 := containerStruct2.ReadField3(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.WatermarkList
		value2 := argvalue2
		fmt.Print(client.RemoveVideoWatermark(value0, value1, value2))
		fmt.Print("\n")
		break
	case "convertVideoCodec":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ConvertVideoCodec requires 3 args")
			flag.Usage()
		}
		arg123 := flag.Arg(1)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue0 := video_server.NewRequestHeader()
		err128 := argvalue0.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg129 := flag.Arg(2)
		mbTrans130 := thrift.NewTMemoryBufferLen(len(arg129))
		defer mbTrans130.Close()
		_, err131 := mbTrans130.WriteString(arg129)
		if err131 != nil {
			Usage()
			return
		}
		factory132 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt133 := factory132.GetProtocol(mbTrans130)
		argvalue1 := video_server.NewPCVideoCodecInfo()
		err134 := argvalue1.Read(jsProt133)
		if err134 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg135 := flag.Arg(3)
		mbTrans136 := thrift.NewTMemoryBufferLen(len(arg135))
		defer mbTrans136.Close()
		_, err137 := mbTrans136.WriteString(arg135)
		if err137 != nil {
			Usage()
			return
		}
		factory138 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt139 := factory138.GetProtocol(mbTrans136)
		argvalue2 := video_server.NewPCVideo()
		err140 := argvalue2.Read(jsProt139)
		if err140 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ConvertVideoCodec(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addVideoOverlay":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddVideoOverlay requires 3 args")
			flag.Usage()
		}
		arg141 := flag.Arg(1)
		mbTrans142 := thrift.NewTMemoryBufferLen(len(arg141))
		defer mbTrans142.Close()
		_, err143 := mbTrans142.WriteString(arg141)
		if err143 != nil {
			Usage()
			return
		}
		factory144 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt145 := factory144.GetProtocol(mbTrans142)
		argvalue0 := video_server.NewRequestHeader()
		err146 := argvalue0.Read(jsProt145)
		if err146 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg147 := flag.Arg(2)
		mbTrans148 := thrift.NewTMemoryBufferLen(len(arg147))
		defer mbTrans148.Close()
		_, err149 := mbTrans148.WriteString(arg147)
		if err149 != nil {
			Usage()
			return
		}
		factory150 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt151 := factory150.GetProtocol(mbTrans148)
		argvalue1 := video_server.NewPCVideo()
		err152 := argvalue1.Read(jsProt151)
		if err152 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg153 := flag.Arg(3)
		mbTrans154 := thrift.NewTMemoryBufferLen(len(arg153))
		defer mbTrans154.Close()
		_, err155 := mbTrans154.WriteString(arg153)
		if err155 != nil {
			Usage()
			return
		}
		factory156 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt157 := factory156.GetProtocol(mbTrans154)
		containerStruct2 := video_server.NewAddVideoOverlayArgs()
		err158 := containerStruct2.ReadField3(jsProt157)
		if err158 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.OverlayList
		value2 := argvalue2
		fmt.Print(client.AddVideoOverlay(value0, value1, value2))
		fmt.Print("\n")
		break
	case "concatVideos":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ConcatVideos requires 2 args")
			flag.Usage()
		}
		arg159 := flag.Arg(1)
		mbTrans160 := thrift.NewTMemoryBufferLen(len(arg159))
		defer mbTrans160.Close()
		_, err161 := mbTrans160.WriteString(arg159)
		if err161 != nil {
			Usage()
			return
		}
		factory162 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt163 := factory162.GetProtocol(mbTrans160)
		argvalue0 := video_server.NewRequestHeader()
		err164 := argvalue0.Read(jsProt163)
		if err164 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg165 := flag.Arg(2)
		mbTrans166 := thrift.NewTMemoryBufferLen(len(arg165))
		defer mbTrans166.Close()
		_, err167 := mbTrans166.WriteString(arg165)
		if err167 != nil {
			Usage()
			return
		}
		factory168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt169 := factory168.GetProtocol(mbTrans166)
		containerStruct1 := video_server.NewConcatVideosArgs()
		err170 := containerStruct1.ReadField2(jsProt169)
		if err170 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.VideoList
		value1 := argvalue1
		fmt.Print(client.ConcatVideos(value0, value1))
		fmt.Print("\n")
		break
	case "convertVideoMatchADX":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ConvertVideoMatchADX requires 3 args")
			flag.Usage()
		}
		arg171 := flag.Arg(1)
		mbTrans172 := thrift.NewTMemoryBufferLen(len(arg171))
		defer mbTrans172.Close()
		_, err173 := mbTrans172.WriteString(arg171)
		if err173 != nil {
			Usage()
			return
		}
		factory174 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt175 := factory174.GetProtocol(mbTrans172)
		argvalue0 := video_server.NewRequestHeader()
		err176 := argvalue0.Read(jsProt175)
		if err176 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg177 := flag.Arg(2)
		mbTrans178 := thrift.NewTMemoryBufferLen(len(arg177))
		defer mbTrans178.Close()
		_, err179 := mbTrans178.WriteString(arg177)
		if err179 != nil {
			Usage()
			return
		}
		factory180 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt181 := factory180.GetProtocol(mbTrans178)
		argvalue1 := video_server.NewPCVideo()
		err182 := argvalue1.Read(jsProt181)
		if err182 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg183 := flag.Arg(3)
		mbTrans184 := thrift.NewTMemoryBufferLen(len(arg183))
		defer mbTrans184.Close()
		_, err185 := mbTrans184.WriteString(arg183)
		if err185 != nil {
			Usage()
			return
		}
		factory186 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt187 := factory186.GetProtocol(mbTrans184)
		containerStruct2 := video_server.NewConvertVideoMatchADXArgs()
		err188 := containerStruct2.ReadField3(jsProt187)
		if err188 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.EXList
		value2 := argvalue2
		fmt.Print(client.ConvertVideoMatchADX(value0, value1, value2))
		fmt.Print("\n")
		break
	case "processVideoMatchADX":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ProcessVideoMatchADX requires 3 args")
			flag.Usage()
		}
		arg189 := flag.Arg(1)
		mbTrans190 := thrift.NewTMemoryBufferLen(len(arg189))
		defer mbTrans190.Close()
		_, err191 := mbTrans190.WriteString(arg189)
		if err191 != nil {
			Usage()
			return
		}
		factory192 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt193 := factory192.GetProtocol(mbTrans190)
		argvalue0 := video_server.NewRequestHeader()
		err194 := argvalue0.Read(jsProt193)
		if err194 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg195 := flag.Arg(2)
		mbTrans196 := thrift.NewTMemoryBufferLen(len(arg195))
		defer mbTrans196.Close()
		_, err197 := mbTrans196.WriteString(arg195)
		if err197 != nil {
			Usage()
			return
		}
		factory198 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt199 := factory198.GetProtocol(mbTrans196)
		argvalue1 := video_server.NewPCVideo()
		err200 := argvalue1.Read(jsProt199)
		if err200 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg201 := flag.Arg(3)
		mbTrans202 := thrift.NewTMemoryBufferLen(len(arg201))
		defer mbTrans202.Close()
		_, err203 := mbTrans202.WriteString(arg201)
		if err203 != nil {
			Usage()
			return
		}
		factory204 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt205 := factory204.GetProtocol(mbTrans202)
		containerStruct2 := video_server.NewProcessVideoMatchADXArgs()
		err206 := containerStruct2.ReadField3(jsProt205)
		if err206 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AdxMatchIdDict
		value2 := argvalue2
		fmt.Print(client.ProcessVideoMatchADX(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getLandingpageImages":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetLandingpageImages requires 2 args")
			flag.Usage()
		}
		arg207 := flag.Arg(1)
		mbTrans208 := thrift.NewTMemoryBufferLen(len(arg207))
		defer mbTrans208.Close()
		_, err209 := mbTrans208.WriteString(arg207)
		if err209 != nil {
			Usage()
			return
		}
		factory210 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt211 := factory210.GetProtocol(mbTrans208)
		argvalue0 := video_server.NewRequestHeader()
		err212 := argvalue0.Read(jsProt211)
		if err212 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg213 := flag.Arg(2)
		mbTrans214 := thrift.NewTMemoryBufferLen(len(arg213))
		defer mbTrans214.Close()
		_, err215 := mbTrans214.WriteString(arg213)
		if err215 != nil {
			Usage()
			return
		}
		factory216 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt217 := factory216.GetProtocol(mbTrans214)
		argvalue1 := video_server.NewAppIdInfo()
		err218 := argvalue1.Read(jsProt217)
		if err218 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetLandingpageImages(value0, value1))
		fmt.Print("\n")
		break
	case "getVideoInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVideoInfo requires 2 args")
			flag.Usage()
		}
		arg219 := flag.Arg(1)
		mbTrans220 := thrift.NewTMemoryBufferLen(len(arg219))
		defer mbTrans220.Close()
		_, err221 := mbTrans220.WriteString(arg219)
		if err221 != nil {
			Usage()
			return
		}
		factory222 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt223 := factory222.GetProtocol(mbTrans220)
		argvalue0 := video_server.NewRequestHeader()
		err224 := argvalue0.Read(jsProt223)
		if err224 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg225 := flag.Arg(2)
		mbTrans226 := thrift.NewTMemoryBufferLen(len(arg225))
		defer mbTrans226.Close()
		_, err227 := mbTrans226.WriteString(arg225)
		if err227 != nil {
			Usage()
			return
		}
		factory228 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt229 := factory228.GetProtocol(mbTrans226)
		argvalue1 := video_server.NewPCVideo()
		err230 := argvalue1.Read(jsProt229)
		if err230 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetVideoInfo(value0, value1))
		fmt.Print("\n")
		break
	case "refineVideoCreative":
		if flag.NArg()-1 != 12 {
			fmt.Fprintln(os.Stderr, "RefineVideoCreative requires 12 args")
			flag.Usage()
		}
		arg231 := flag.Arg(1)
		mbTrans232 := thrift.NewTMemoryBufferLen(len(arg231))
		defer mbTrans232.Close()
		_, err233 := mbTrans232.WriteString(arg231)
		if err233 != nil {
			Usage()
			return
		}
		factory234 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt235 := factory234.GetProtocol(mbTrans232)
		argvalue0 := video_server.NewRequestHeader()
		err236 := argvalue0.Read(jsProt235)
		if err236 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg237 := flag.Arg(2)
		mbTrans238 := thrift.NewTMemoryBufferLen(len(arg237))
		defer mbTrans238.Close()
		_, err239 := mbTrans238.WriteString(arg237)
		if err239 != nil {
			Usage()
			return
		}
		factory240 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt241 := factory240.GetProtocol(mbTrans238)
		argvalue1 := video_server.NewAppIdInfo()
		err242 := argvalue1.Read(jsProt241)
		if err242 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg243 := flag.Arg(3)
		mbTrans244 := thrift.NewTMemoryBufferLen(len(arg243))
		defer mbTrans244.Close()
		_, err245 := mbTrans244.WriteString(arg243)
		if err245 != nil {
			Usage()
			return
		}
		factory246 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt247 := factory246.GetProtocol(mbTrans244)
		argvalue2 := video_server.NewPCVideo()
		err248 := argvalue2.Read(jsProt247)
		if err248 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg249 := flag.Arg(4)
		mbTrans250 := thrift.NewTMemoryBufferLen(len(arg249))
		defer mbTrans250.Close()
		_, err251 := mbTrans250.WriteString(arg249)
		if err251 != nil {
			Usage()
			return
		}
		factory252 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt253 := factory252.GetProtocol(mbTrans250)
		argvalue3 := video_server.NewPCVideoOverlayInfo()
		err254 := argvalue3.Read(jsProt253)
		if err254 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg255 := flag.Arg(5)
		mbTrans256 := thrift.NewTMemoryBufferLen(len(arg255))
		defer mbTrans256.Close()
		_, err257 := mbTrans256.WriteString(arg255)
		if err257 != nil {
			Usage()
			return
		}
		factory258 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt259 := factory258.GetProtocol(mbTrans256)
		argvalue4 := video_server.NewPCVideoOverlayInfo()
		err260 := argvalue4.Read(jsProt259)
		if err260 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg261 := flag.Arg(6)
		mbTrans262 := thrift.NewTMemoryBufferLen(len(arg261))
		defer mbTrans262.Close()
		_, err263 := mbTrans262.WriteString(arg261)
		if err263 != nil {
			Usage()
			return
		}
		factory264 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt265 := factory264.GetProtocol(mbTrans262)
		containerStruct5 := video_server.NewRefineVideoCreativeArgs()
		err266 := containerStruct5.ReadField6(jsProt265)
		if err266 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.WatermarkList
		value5 := argvalue5
		arg267 := flag.Arg(7)
		mbTrans268 := thrift.NewTMemoryBufferLen(len(arg267))
		defer mbTrans268.Close()
		_, err269 := mbTrans268.WriteString(arg267)
		if err269 != nil {
			Usage()
			return
		}
		factory270 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt271 := factory270.GetProtocol(mbTrans268)
		argvalue6 := video_server.NewPCText()
		err272 := argvalue6.Read(jsProt271)
		if err272 != nil {
			Usage()
			return
		}
		value6 := argvalue6
		tmp7, err273 := (strconv.Atoi(flag.Arg(8)))
		if err273 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := argvalue7
		tmp8, err274 := (strconv.Atoi(flag.Arg(9)))
		if err274 != nil {
			Usage()
			return
		}
		argvalue8 := int32(tmp8)
		value8 := argvalue8
		tmp9, err275 := (strconv.Atoi(flag.Arg(10)))
		if err275 != nil {
			Usage()
			return
		}
		argvalue9 := int32(tmp9)
		value9 := argvalue9
		arg276 := flag.Arg(11)
		mbTrans277 := thrift.NewTMemoryBufferLen(len(arg276))
		defer mbTrans277.Close()
		_, err278 := mbTrans277.WriteString(arg276)
		if err278 != nil {
			Usage()
			return
		}
		factory279 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt280 := factory279.GetProtocol(mbTrans277)
		argvalue10 := video_server.NewPCVideoVolumeInfo()
		err281 := argvalue10.Read(jsProt280)
		if err281 != nil {
			Usage()
			return
		}
		value10 := argvalue10
		arg282 := flag.Arg(12)
		mbTrans283 := thrift.NewTMemoryBufferLen(len(arg282))
		defer mbTrans283.Close()
		_, err284 := mbTrans283.WriteString(arg282)
		if err284 != nil {
			Usage()
			return
		}
		factory285 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt286 := factory285.GetProtocol(mbTrans283)
		containerStruct11 := video_server.NewRefineVideoCreativeArgs()
		err287 := containerStruct11.ReadField12(jsProt286)
		if err287 != nil {
			Usage()
			return
		}
		argvalue11 := containerStruct11.AdditionalRequest
		value11 := argvalue11
		fmt.Print(client.RefineVideoCreative(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9, value10, value11))
		fmt.Print("\n")
		break
	case "processVideoCreative":
		if flag.NArg()-1 != 12 {
			fmt.Fprintln(os.Stderr, "ProcessVideoCreative requires 12 args")
			flag.Usage()
		}
		arg288 := flag.Arg(1)
		mbTrans289 := thrift.NewTMemoryBufferLen(len(arg288))
		defer mbTrans289.Close()
		_, err290 := mbTrans289.WriteString(arg288)
		if err290 != nil {
			Usage()
			return
		}
		factory291 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt292 := factory291.GetProtocol(mbTrans289)
		argvalue0 := video_server.NewRequestHeader()
		err293 := argvalue0.Read(jsProt292)
		if err293 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg294 := flag.Arg(2)
		mbTrans295 := thrift.NewTMemoryBufferLen(len(arg294))
		defer mbTrans295.Close()
		_, err296 := mbTrans295.WriteString(arg294)
		if err296 != nil {
			Usage()
			return
		}
		factory297 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt298 := factory297.GetProtocol(mbTrans295)
		argvalue1 := video_server.NewAppIdInfo()
		err299 := argvalue1.Read(jsProt298)
		if err299 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg300 := flag.Arg(3)
		mbTrans301 := thrift.NewTMemoryBufferLen(len(arg300))
		defer mbTrans301.Close()
		_, err302 := mbTrans301.WriteString(arg300)
		if err302 != nil {
			Usage()
			return
		}
		factory303 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt304 := factory303.GetProtocol(mbTrans301)
		argvalue2 := video_server.NewPCVideo()
		err305 := argvalue2.Read(jsProt304)
		if err305 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		arg306 := flag.Arg(4)
		mbTrans307 := thrift.NewTMemoryBufferLen(len(arg306))
		defer mbTrans307.Close()
		_, err308 := mbTrans307.WriteString(arg306)
		if err308 != nil {
			Usage()
			return
		}
		factory309 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt310 := factory309.GetProtocol(mbTrans307)
		argvalue3 := video_server.NewPCVideoOverlayInfo()
		err311 := argvalue3.Read(jsProt310)
		if err311 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg312 := flag.Arg(5)
		mbTrans313 := thrift.NewTMemoryBufferLen(len(arg312))
		defer mbTrans313.Close()
		_, err314 := mbTrans313.WriteString(arg312)
		if err314 != nil {
			Usage()
			return
		}
		factory315 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt316 := factory315.GetProtocol(mbTrans313)
		argvalue4 := video_server.NewPCVideoOverlayInfo()
		err317 := argvalue4.Read(jsProt316)
		if err317 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg318 := flag.Arg(6)
		mbTrans319 := thrift.NewTMemoryBufferLen(len(arg318))
		defer mbTrans319.Close()
		_, err320 := mbTrans319.WriteString(arg318)
		if err320 != nil {
			Usage()
			return
		}
		factory321 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt322 := factory321.GetProtocol(mbTrans319)
		containerStruct5 := video_server.NewProcessVideoCreativeArgs()
		err323 := containerStruct5.ReadField6(jsProt322)
		if err323 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.WatermarkList
		value5 := argvalue5
		arg324 := flag.Arg(7)
		mbTrans325 := thrift.NewTMemoryBufferLen(len(arg324))
		defer mbTrans325.Close()
		_, err326 := mbTrans325.WriteString(arg324)
		if err326 != nil {
			Usage()
			return
		}
		factory327 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt328 := factory327.GetProtocol(mbTrans325)
		argvalue6 := video_server.NewPCText()
		err329 := argvalue6.Read(jsProt328)
		if err329 != nil {
			Usage()
			return
		}
		value6 := argvalue6
		tmp7, err330 := (strconv.Atoi(flag.Arg(8)))
		if err330 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := argvalue7
		tmp8, err331 := (strconv.Atoi(flag.Arg(9)))
		if err331 != nil {
			Usage()
			return
		}
		argvalue8 := int32(tmp8)
		value8 := argvalue8
		tmp9, err332 := (strconv.Atoi(flag.Arg(10)))
		if err332 != nil {
			Usage()
			return
		}
		argvalue9 := int32(tmp9)
		value9 := argvalue9
		arg333 := flag.Arg(11)
		mbTrans334 := thrift.NewTMemoryBufferLen(len(arg333))
		defer mbTrans334.Close()
		_, err335 := mbTrans334.WriteString(arg333)
		if err335 != nil {
			Usage()
			return
		}
		factory336 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt337 := factory336.GetProtocol(mbTrans334)
		argvalue10 := video_server.NewPCVideoVolumeInfo()
		err338 := argvalue10.Read(jsProt337)
		if err338 != nil {
			Usage()
			return
		}
		value10 := argvalue10
		arg339 := flag.Arg(12)
		mbTrans340 := thrift.NewTMemoryBufferLen(len(arg339))
		defer mbTrans340.Close()
		_, err341 := mbTrans340.WriteString(arg339)
		if err341 != nil {
			Usage()
			return
		}
		factory342 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt343 := factory342.GetProtocol(mbTrans340)
		containerStruct11 := video_server.NewProcessVideoCreativeArgs()
		err344 := containerStruct11.ReadField12(jsProt343)
		if err344 != nil {
			Usage()
			return
		}
		argvalue11 := containerStruct11.AdditionalRequest
		value11 := argvalue11
		fmt.Print(client.ProcessVideoCreative(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9, value10, value11))
		fmt.Print("\n")
		break
	case "getVideoThumbnails":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetVideoThumbnails requires 3 args")
			flag.Usage()
		}
		arg345 := flag.Arg(1)
		mbTrans346 := thrift.NewTMemoryBufferLen(len(arg345))
		defer mbTrans346.Close()
		_, err347 := mbTrans346.WriteString(arg345)
		if err347 != nil {
			Usage()
			return
		}
		factory348 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt349 := factory348.GetProtocol(mbTrans346)
		argvalue0 := video_server.NewRequestHeader()
		err350 := argvalue0.Read(jsProt349)
		if err350 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg351 := flag.Arg(2)
		mbTrans352 := thrift.NewTMemoryBufferLen(len(arg351))
		defer mbTrans352.Close()
		_, err353 := mbTrans352.WriteString(arg351)
		if err353 != nil {
			Usage()
			return
		}
		factory354 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt355 := factory354.GetProtocol(mbTrans352)
		argvalue1 := video_server.NewPCVideo()
		err356 := argvalue1.Read(jsProt355)
		if err356 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg357 := flag.Arg(3)
		mbTrans358 := thrift.NewTMemoryBufferLen(len(arg357))
		defer mbTrans358.Close()
		_, err359 := mbTrans358.WriteString(arg357)
		if err359 != nil {
			Usage()
			return
		}
		factory360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt361 := factory360.GetProtocol(mbTrans358)
		argvalue2 := video_server.NewPCVideoThumbnailRequest()
		err362 := argvalue2.Read(jsProt361)
		if err362 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetVideoThumbnails(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getVideoFingerPrint":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetVideoFingerPrint requires 2 args")
			flag.Usage()
		}
		arg363 := flag.Arg(1)
		mbTrans364 := thrift.NewTMemoryBufferLen(len(arg363))
		defer mbTrans364.Close()
		_, err365 := mbTrans364.WriteString(arg363)
		if err365 != nil {
			Usage()
			return
		}
		factory366 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt367 := factory366.GetProtocol(mbTrans364)
		argvalue0 := video_server.NewRequestHeader()
		err368 := argvalue0.Read(jsProt367)
		if err368 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg369 := flag.Arg(2)
		mbTrans370 := thrift.NewTMemoryBufferLen(len(arg369))
		defer mbTrans370.Close()
		_, err371 := mbTrans370.WriteString(arg369)
		if err371 != nil {
			Usage()
			return
		}
		factory372 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt373 := factory372.GetProtocol(mbTrans370)
		argvalue1 := video_server.NewPCVideo()
		err374 := argvalue1.Read(jsProt373)
		if err374 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetVideoFingerPrint(value0, value1))
		fmt.Print("\n")
		break
	case "composeMediaSeqVideo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ComposeMediaSeqVideo requires 2 args")
			flag.Usage()
		}
		arg375 := flag.Arg(1)
		mbTrans376 := thrift.NewTMemoryBufferLen(len(arg375))
		defer mbTrans376.Close()
		_, err377 := mbTrans376.WriteString(arg375)
		if err377 != nil {
			Usage()
			return
		}
		factory378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt379 := factory378.GetProtocol(mbTrans376)
		argvalue0 := video_server.NewRequestHeader()
		err380 := argvalue0.Read(jsProt379)
		if err380 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg381 := flag.Arg(2)
		mbTrans382 := thrift.NewTMemoryBufferLen(len(arg381))
		defer mbTrans382.Close()
		_, err383 := mbTrans382.WriteString(arg381)
		if err383 != nil {
			Usage()
			return
		}
		factory384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt385 := factory384.GetProtocol(mbTrans382)
		argvalue1 := video_server.NewPCMediaSeqVideoComposInfo()
		err386 := argvalue1.Read(jsProt385)
		if err386 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ComposeMediaSeqVideo(value0, value1))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err391 := (strconv.Atoi(flag.Arg(1)))
		if err391 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
