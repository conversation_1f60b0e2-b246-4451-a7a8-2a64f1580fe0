// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appinfo

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appinfo_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = appinfo_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//AppInfoException中可能出现的异常代码
type AppInfoServiceCode int64

const (
	AppInfoServiceCode_ERROR_APP_USER_NOT_EXIST    AppInfoServiceCode = 30001
	AppInfoServiceCode_ERROR_APP_USER_NOT_MATCH    AppInfoServiceCode = 30002
	AppInfoServiceCode_ERROR_APP_NOT_EXIST         AppInfoServiceCode = 30011
	AppInfoServiceCode_ERROR_APP_NOT_MATCH         AppInfoServiceCode = 30012
	AppInfoServiceCode_ERROR_APP_ALREADY_EXIST     AppInfoServiceCode = 30013
	AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_EXIST AppInfoServiceCode = 30014
	AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_MATCH AppInfoServiceCode = 30015
	AppInfoServiceCode_ERROR_APP_PARAM_INVALID     AppInfoServiceCode = 30021
	AppInfoServiceCode_ERROR_APP_SYSTEM_ERROR      AppInfoServiceCode = 30031
)

func (p AppInfoServiceCode) String() string {
	switch p {
	case AppInfoServiceCode_ERROR_APP_USER_NOT_EXIST:
		return "AppInfoServiceCode_ERROR_APP_USER_NOT_EXIST"
	case AppInfoServiceCode_ERROR_APP_USER_NOT_MATCH:
		return "AppInfoServiceCode_ERROR_APP_USER_NOT_MATCH"
	case AppInfoServiceCode_ERROR_APP_NOT_EXIST:
		return "AppInfoServiceCode_ERROR_APP_NOT_EXIST"
	case AppInfoServiceCode_ERROR_APP_NOT_MATCH:
		return "AppInfoServiceCode_ERROR_APP_NOT_MATCH"
	case AppInfoServiceCode_ERROR_APP_ALREADY_EXIST:
		return "AppInfoServiceCode_ERROR_APP_ALREADY_EXIST"
	case AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_EXIST:
		return "AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_EXIST"
	case AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_MATCH:
		return "AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_MATCH"
	case AppInfoServiceCode_ERROR_APP_PARAM_INVALID:
		return "AppInfoServiceCode_ERROR_APP_PARAM_INVALID"
	case AppInfoServiceCode_ERROR_APP_SYSTEM_ERROR:
		return "AppInfoServiceCode_ERROR_APP_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func AppInfoServiceCodeFromString(s string) (AppInfoServiceCode, error) {
	switch s {
	case "AppInfoServiceCode_ERROR_APP_USER_NOT_EXIST":
		return AppInfoServiceCode_ERROR_APP_USER_NOT_EXIST, nil
	case "AppInfoServiceCode_ERROR_APP_USER_NOT_MATCH":
		return AppInfoServiceCode_ERROR_APP_USER_NOT_MATCH, nil
	case "AppInfoServiceCode_ERROR_APP_NOT_EXIST":
		return AppInfoServiceCode_ERROR_APP_NOT_EXIST, nil
	case "AppInfoServiceCode_ERROR_APP_NOT_MATCH":
		return AppInfoServiceCode_ERROR_APP_NOT_MATCH, nil
	case "AppInfoServiceCode_ERROR_APP_ALREADY_EXIST":
		return AppInfoServiceCode_ERROR_APP_ALREADY_EXIST, nil
	case "AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_EXIST":
		return AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_EXIST, nil
	case "AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_MATCH":
		return AppInfoServiceCode_ERROR_APP_CHANNEL_NOT_MATCH, nil
	case "AppInfoServiceCode_ERROR_APP_PARAM_INVALID":
		return AppInfoServiceCode_ERROR_APP_PARAM_INVALID, nil
	case "AppInfoServiceCode_ERROR_APP_SYSTEM_ERROR":
		return AppInfoServiceCode_ERROR_APP_SYSTEM_ERROR, nil
	}
	return AppInfoServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid AppInfoServiceCode string")
}

type IdInt appinfo_types.IdInt

type AppInfoApp *appinfo_types.App

type AppInfoAppChannel *appinfo_types.AppChannel

type RequestHeader *common.RequestHeader

type AppPlatform appinfo_types.AppPlatform

type AppInfoAppAndChannelResult *appinfo_types.AppInfoAppAndChannelResult

type AppId appinfo_types.IdInt

type ChannelId appinfo_types.IdInt

type AppQueryInt appinfo_types.AppQueryInt

type AppQueryResult *common.QueryResult

type QueryParam *appinfo_types.QueryParam

type AppInfoException struct {
	Code    AppInfoServiceCode `thrift:"code,1" json:"code"`
	Message string             `thrift:"message,2" json:"message"`
}

func NewAppInfoException() *AppInfoException {
	return &AppInfoException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppInfoException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *AppInfoException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = AppInfoServiceCode(v)
	}
	return nil
}

func (p *AppInfoException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *AppInfoException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *AppInfoException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *AppInfoException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoException(%+v)", *p)
}
