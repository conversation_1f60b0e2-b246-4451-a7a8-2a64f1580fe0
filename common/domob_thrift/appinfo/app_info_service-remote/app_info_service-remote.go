// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"appinfo"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  AppId addApp(RequestHeader header, AppInfoApp app)")
	fmt.Fprintln(os.Stderr, "  void editApp(RequestHeader header, AppInfoApp app)")
	fmt.Fprintln(os.<PERSON>, "  AppInfoApp getAppById(RequestHeader header, AppId id)")
	fmt.Fprintln(os.Stderr, "   getAppsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  AppQueryResult getAppsByPlatform(RequestHeader header, AppPlatform platform, AppQueryInt offset, AppQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  ChannelId addAppChannel(RequestHeader header, AppInfoAppChannel appchannel)")
	fmt.Fprintln(os.Stderr, "  void editAppChannel(RequestHeader header, AppInfoAppChannel appchannel)")
	fmt.Fprintln(os.Stderr, "   getAppChannelsByAppId(RequestHeader header, AppId appid)")
	fmt.Fprintln(os.Stderr, "  AppInfoAppChannel getAppChannelById(RequestHeader header, ChannelId id)")
	fmt.Fprintln(os.Stderr, "   getAppChannelsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  AppInfoAppAndChannelResult getAppAndChannelByChnId(RequestHeader header, ChannelId chnid)")
	fmt.Fprintln(os.Stderr, "   getAppsAndChannelsByChnIds(RequestHeader header,  channelids)")
	fmt.Fprintln(os.Stderr, "  AppQueryResult getAllApps(RequestHeader header, AppQueryInt offset, AppQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "  AppQueryResult getAppsByQueryParamAndPlatform(RequestHeader header, QueryParam queryParam, AppPlatform platform, AppQueryInt offset, AppQueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getAppsByPkgNameAndPlatform(RequestHeader header, string packageName, AppPlatform platform)")
	fmt.Fprintln(os.Stderr, "  AppQueryResult searchAppChannelsByParams(RequestHeader header, AppChannelParams params)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := appinfo.NewAppInfoServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addApp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddApp requires 2 args")
			flag.Usage()
		}
		arg79 := flag.Arg(1)
		mbTrans80 := thrift.NewTMemoryBufferLen(len(arg79))
		defer mbTrans80.Close()
		_, err81 := mbTrans80.WriteString(arg79)
		if err81 != nil {
			Usage()
			return
		}
		factory82 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt83 := factory82.GetProtocol(mbTrans80)
		argvalue0 := appinfo.NewRequestHeader()
		err84 := argvalue0.Read(jsProt83)
		if err84 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg85 := flag.Arg(2)
		mbTrans86 := thrift.NewTMemoryBufferLen(len(arg85))
		defer mbTrans86.Close()
		_, err87 := mbTrans86.WriteString(arg85)
		if err87 != nil {
			Usage()
			return
		}
		factory88 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt89 := factory88.GetProtocol(mbTrans86)
		argvalue1 := appinfo.NewAppInfoApp()
		err90 := argvalue1.Read(jsProt89)
		if err90 != nil {
			Usage()
			return
		}
		value1 := appinfo.AppInfoApp(argvalue1)
		fmt.Print(client.AddApp(value0, value1))
		fmt.Print("\n")
		break
	case "editApp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditApp requires 2 args")
			flag.Usage()
		}
		arg91 := flag.Arg(1)
		mbTrans92 := thrift.NewTMemoryBufferLen(len(arg91))
		defer mbTrans92.Close()
		_, err93 := mbTrans92.WriteString(arg91)
		if err93 != nil {
			Usage()
			return
		}
		factory94 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt95 := factory94.GetProtocol(mbTrans92)
		argvalue0 := appinfo.NewRequestHeader()
		err96 := argvalue0.Read(jsProt95)
		if err96 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg97 := flag.Arg(2)
		mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
		defer mbTrans98.Close()
		_, err99 := mbTrans98.WriteString(arg97)
		if err99 != nil {
			Usage()
			return
		}
		factory100 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt101 := factory100.GetProtocol(mbTrans98)
		argvalue1 := appinfo.NewAppInfoApp()
		err102 := argvalue1.Read(jsProt101)
		if err102 != nil {
			Usage()
			return
		}
		value1 := appinfo.AppInfoApp(argvalue1)
		fmt.Print(client.EditApp(value0, value1))
		fmt.Print("\n")
		break
	case "getAppById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppById requires 2 args")
			flag.Usage()
		}
		arg103 := flag.Arg(1)
		mbTrans104 := thrift.NewTMemoryBufferLen(len(arg103))
		defer mbTrans104.Close()
		_, err105 := mbTrans104.WriteString(arg103)
		if err105 != nil {
			Usage()
			return
		}
		factory106 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt107 := factory106.GetProtocol(mbTrans104)
		argvalue0 := appinfo.NewRequestHeader()
		err108 := argvalue0.Read(jsProt107)
		if err108 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		tmp1, err109 := (strconv.Atoi(flag.Arg(2)))
		if err109 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appinfo.AppId(argvalue1)
		fmt.Print(client.GetAppById(value0, value1))
		fmt.Print("\n")
		break
	case "getAppsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppsByIds requires 2 args")
			flag.Usage()
		}
		arg110 := flag.Arg(1)
		mbTrans111 := thrift.NewTMemoryBufferLen(len(arg110))
		defer mbTrans111.Close()
		_, err112 := mbTrans111.WriteString(arg110)
		if err112 != nil {
			Usage()
			return
		}
		factory113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt114 := factory113.GetProtocol(mbTrans111)
		argvalue0 := appinfo.NewRequestHeader()
		err115 := argvalue0.Read(jsProt114)
		if err115 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg116 := flag.Arg(2)
		mbTrans117 := thrift.NewTMemoryBufferLen(len(arg116))
		defer mbTrans117.Close()
		_, err118 := mbTrans117.WriteString(arg116)
		if err118 != nil {
			Usage()
			return
		}
		factory119 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt120 := factory119.GetProtocol(mbTrans117)
		containerStruct1 := appinfo.NewGetAppsByIdsArgs()
		err121 := containerStruct1.ReadField2(jsProt120)
		if err121 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAppsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAppsByPlatform":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetAppsByPlatform requires 5 args")
			flag.Usage()
		}
		arg122 := flag.Arg(1)
		mbTrans123 := thrift.NewTMemoryBufferLen(len(arg122))
		defer mbTrans123.Close()
		_, err124 := mbTrans123.WriteString(arg122)
		if err124 != nil {
			Usage()
			return
		}
		factory125 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt126 := factory125.GetProtocol(mbTrans123)
		argvalue0 := appinfo.NewRequestHeader()
		err127 := argvalue0.Read(jsProt126)
		if err127 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appinfo.AppPlatform(tmp1)
		value1 := appinfo.AppPlatform(argvalue1)
		tmp2, err128 := (strconv.Atoi(flag.Arg(3)))
		if err128 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := appinfo.AppQueryInt(argvalue2)
		tmp3, err129 := (strconv.Atoi(flag.Arg(4)))
		if err129 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := appinfo.AppQueryInt(argvalue3)
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.GetAppsByPlatform(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addAppChannel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAppChannel requires 2 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := appinfo.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg137 := flag.Arg(2)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		argvalue1 := appinfo.NewAppInfoAppChannel()
		err142 := argvalue1.Read(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		value1 := appinfo.AppInfoAppChannel(argvalue1)
		fmt.Print(client.AddAppChannel(value0, value1))
		fmt.Print("\n")
		break
	case "editAppChannel":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAppChannel requires 2 args")
			flag.Usage()
		}
		arg143 := flag.Arg(1)
		mbTrans144 := thrift.NewTMemoryBufferLen(len(arg143))
		defer mbTrans144.Close()
		_, err145 := mbTrans144.WriteString(arg143)
		if err145 != nil {
			Usage()
			return
		}
		factory146 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt147 := factory146.GetProtocol(mbTrans144)
		argvalue0 := appinfo.NewRequestHeader()
		err148 := argvalue0.Read(jsProt147)
		if err148 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg149 := flag.Arg(2)
		mbTrans150 := thrift.NewTMemoryBufferLen(len(arg149))
		defer mbTrans150.Close()
		_, err151 := mbTrans150.WriteString(arg149)
		if err151 != nil {
			Usage()
			return
		}
		factory152 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt153 := factory152.GetProtocol(mbTrans150)
		argvalue1 := appinfo.NewAppInfoAppChannel()
		err154 := argvalue1.Read(jsProt153)
		if err154 != nil {
			Usage()
			return
		}
		value1 := appinfo.AppInfoAppChannel(argvalue1)
		fmt.Print(client.EditAppChannel(value0, value1))
		fmt.Print("\n")
		break
	case "getAppChannelsByAppId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppChannelsByAppId requires 2 args")
			flag.Usage()
		}
		arg155 := flag.Arg(1)
		mbTrans156 := thrift.NewTMemoryBufferLen(len(arg155))
		defer mbTrans156.Close()
		_, err157 := mbTrans156.WriteString(arg155)
		if err157 != nil {
			Usage()
			return
		}
		factory158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt159 := factory158.GetProtocol(mbTrans156)
		argvalue0 := appinfo.NewRequestHeader()
		err160 := argvalue0.Read(jsProt159)
		if err160 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		tmp1, err161 := (strconv.Atoi(flag.Arg(2)))
		if err161 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appinfo.AppId(argvalue1)
		fmt.Print(client.GetAppChannelsByAppId(value0, value1))
		fmt.Print("\n")
		break
	case "getAppChannelById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppChannelById requires 2 args")
			flag.Usage()
		}
		arg162 := flag.Arg(1)
		mbTrans163 := thrift.NewTMemoryBufferLen(len(arg162))
		defer mbTrans163.Close()
		_, err164 := mbTrans163.WriteString(arg162)
		if err164 != nil {
			Usage()
			return
		}
		factory165 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt166 := factory165.GetProtocol(mbTrans163)
		argvalue0 := appinfo.NewRequestHeader()
		err167 := argvalue0.Read(jsProt166)
		if err167 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		tmp1, err168 := (strconv.Atoi(flag.Arg(2)))
		if err168 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appinfo.ChannelId(argvalue1)
		fmt.Print(client.GetAppChannelById(value0, value1))
		fmt.Print("\n")
		break
	case "getAppChannelsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppChannelsByIds requires 2 args")
			flag.Usage()
		}
		arg169 := flag.Arg(1)
		mbTrans170 := thrift.NewTMemoryBufferLen(len(arg169))
		defer mbTrans170.Close()
		_, err171 := mbTrans170.WriteString(arg169)
		if err171 != nil {
			Usage()
			return
		}
		factory172 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt173 := factory172.GetProtocol(mbTrans170)
		argvalue0 := appinfo.NewRequestHeader()
		err174 := argvalue0.Read(jsProt173)
		if err174 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg175 := flag.Arg(2)
		mbTrans176 := thrift.NewTMemoryBufferLen(len(arg175))
		defer mbTrans176.Close()
		_, err177 := mbTrans176.WriteString(arg175)
		if err177 != nil {
			Usage()
			return
		}
		factory178 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt179 := factory178.GetProtocol(mbTrans176)
		containerStruct1 := appinfo.NewGetAppChannelsByIdsArgs()
		err180 := containerStruct1.ReadField2(jsProt179)
		if err180 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAppChannelsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAppAndChannelByChnId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppAndChannelByChnId requires 2 args")
			flag.Usage()
		}
		arg181 := flag.Arg(1)
		mbTrans182 := thrift.NewTMemoryBufferLen(len(arg181))
		defer mbTrans182.Close()
		_, err183 := mbTrans182.WriteString(arg181)
		if err183 != nil {
			Usage()
			return
		}
		factory184 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt185 := factory184.GetProtocol(mbTrans182)
		argvalue0 := appinfo.NewRequestHeader()
		err186 := argvalue0.Read(jsProt185)
		if err186 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		tmp1, err187 := (strconv.Atoi(flag.Arg(2)))
		if err187 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appinfo.ChannelId(argvalue1)
		fmt.Print(client.GetAppAndChannelByChnId(value0, value1))
		fmt.Print("\n")
		break
	case "getAppsAndChannelsByChnIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppsAndChannelsByChnIds requires 2 args")
			flag.Usage()
		}
		arg188 := flag.Arg(1)
		mbTrans189 := thrift.NewTMemoryBufferLen(len(arg188))
		defer mbTrans189.Close()
		_, err190 := mbTrans189.WriteString(arg188)
		if err190 != nil {
			Usage()
			return
		}
		factory191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt192 := factory191.GetProtocol(mbTrans189)
		argvalue0 := appinfo.NewRequestHeader()
		err193 := argvalue0.Read(jsProt192)
		if err193 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg194 := flag.Arg(2)
		mbTrans195 := thrift.NewTMemoryBufferLen(len(arg194))
		defer mbTrans195.Close()
		_, err196 := mbTrans195.WriteString(arg194)
		if err196 != nil {
			Usage()
			return
		}
		factory197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt198 := factory197.GetProtocol(mbTrans195)
		containerStruct1 := appinfo.NewGetAppsAndChannelsByChnIdsArgs()
		err199 := containerStruct1.ReadField2(jsProt198)
		if err199 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Channelids
		value1 := argvalue1
		fmt.Print(client.GetAppsAndChannelsByChnIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAllApps":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetAllApps requires 4 args")
			flag.Usage()
		}
		arg200 := flag.Arg(1)
		mbTrans201 := thrift.NewTMemoryBufferLen(len(arg200))
		defer mbTrans201.Close()
		_, err202 := mbTrans201.WriteString(arg200)
		if err202 != nil {
			Usage()
			return
		}
		factory203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt204 := factory203.GetProtocol(mbTrans201)
		argvalue0 := appinfo.NewRequestHeader()
		err205 := argvalue0.Read(jsProt204)
		if err205 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		tmp1, err206 := (strconv.Atoi(flag.Arg(2)))
		if err206 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := appinfo.AppQueryInt(argvalue1)
		tmp2, err207 := (strconv.Atoi(flag.Arg(3)))
		if err207 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := appinfo.AppQueryInt(argvalue2)
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.GetAllApps(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAppsByQueryParamAndPlatform":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetAppsByQueryParamAndPlatform requires 6 args")
			flag.Usage()
		}
		arg209 := flag.Arg(1)
		mbTrans210 := thrift.NewTMemoryBufferLen(len(arg209))
		defer mbTrans210.Close()
		_, err211 := mbTrans210.WriteString(arg209)
		if err211 != nil {
			Usage()
			return
		}
		factory212 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt213 := factory212.GetProtocol(mbTrans210)
		argvalue0 := appinfo.NewRequestHeader()
		err214 := argvalue0.Read(jsProt213)
		if err214 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg215 := flag.Arg(2)
		mbTrans216 := thrift.NewTMemoryBufferLen(len(arg215))
		defer mbTrans216.Close()
		_, err217 := mbTrans216.WriteString(arg215)
		if err217 != nil {
			Usage()
			return
		}
		factory218 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt219 := factory218.GetProtocol(mbTrans216)
		argvalue1 := appinfo.NewQueryParam()
		err220 := argvalue1.Read(jsProt219)
		if err220 != nil {
			Usage()
			return
		}
		value1 := appinfo.QueryParam(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appinfo.AppPlatform(tmp2)
		value2 := appinfo.AppPlatform(argvalue2)
		tmp3, err221 := (strconv.Atoi(flag.Arg(4)))
		if err221 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := appinfo.AppQueryInt(argvalue3)
		tmp4, err222 := (strconv.Atoi(flag.Arg(5)))
		if err222 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := appinfo.AppQueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.GetAppsByQueryParamAndPlatform(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getAppsByPkgNameAndPlatform":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAppsByPkgNameAndPlatform requires 3 args")
			flag.Usage()
		}
		arg224 := flag.Arg(1)
		mbTrans225 := thrift.NewTMemoryBufferLen(len(arg224))
		defer mbTrans225.Close()
		_, err226 := mbTrans225.WriteString(arg224)
		if err226 != nil {
			Usage()
			return
		}
		factory227 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt228 := factory227.GetProtocol(mbTrans225)
		argvalue0 := appinfo.NewRequestHeader()
		err229 := argvalue0.Read(jsProt228)
		if err229 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appinfo.AppPlatform(tmp2)
		value2 := appinfo.AppPlatform(argvalue2)
		fmt.Print(client.GetAppsByPkgNameAndPlatform(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchAppChannelsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAppChannelsByParams requires 2 args")
			flag.Usage()
		}
		arg231 := flag.Arg(1)
		mbTrans232 := thrift.NewTMemoryBufferLen(len(arg231))
		defer mbTrans232.Close()
		_, err233 := mbTrans232.WriteString(arg231)
		if err233 != nil {
			Usage()
			return
		}
		factory234 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt235 := factory234.GetProtocol(mbTrans232)
		argvalue0 := appinfo.NewRequestHeader()
		err236 := argvalue0.Read(jsProt235)
		if err236 != nil {
			Usage()
			return
		}
		value0 := appinfo.RequestHeader(argvalue0)
		arg237 := flag.Arg(2)
		mbTrans238 := thrift.NewTMemoryBufferLen(len(arg237))
		defer mbTrans238.Close()
		_, err239 := mbTrans238.WriteString(arg237)
		if err239 != nil {
			Usage()
			return
		}
		factory240 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt241 := factory240.GetProtocol(mbTrans238)
		argvalue1 := appinfo.NewAppChannelParams()
		err242 := argvalue1.Read(jsProt241)
		if err242 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAppChannelsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
