// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appinfo

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appinfo_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = appinfo_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type AppInfoService interface { //app信息服务接口定义

	// 添加app信息
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - App: app信息结构体
	AddApp(header *common.RequestHeader, app *appinfo_types.App) (r AppId, ae *AppInfoException, err error)
	// 编辑app信息
	// packageName不允许修改
	// platform不允许修改
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - App: app信息结构体
	EditApp(header *common.RequestHeader, app *appinfo_types.App) (ae *AppInfoException, err error)
	// 根据appId获取app信息
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - Id: 查询的appId
	GetAppById(header *common.RequestHeader, id AppId) (r *appinfo_types.App, ae *AppInfoException, err error)
	// 根据appIds获取app信息
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - Ids: 查询的appId
	GetAppsByIds(header *common.RequestHeader, ids []AppId) (r map[AppId]*appinfo_types.App, ae *AppInfoException, err error)
	// 根据app类型获取app信息
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - Platform: app类型
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	GetAppsByPlatform(header *common.RequestHeader, platform AppPlatform, offset AppQueryInt, limit AppQueryInt, ascending bool) (r *common.QueryResult, ae *AppInfoException, err error)
	// 添加app渠道信息
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - Appchannel: 渠道信息结构体
	AddAppChannel(header *common.RequestHeader, appchannel *appinfo_types.AppChannel) (r ChannelId, ae *AppInfoException, err error)
	// 编辑app渠道信息
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Appchannel: 渠道信息结构体
	EditAppChannel(header *common.RequestHeader, appchannel *appinfo_types.AppChannel) (ae *AppInfoException, err error)
	// 根据appId获取渠道信息
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Appid: appId
	GetAppChannelsByAppId(header *common.RequestHeader, appid AppId) (r map[ChannelId]*appinfo_types.AppChannel, ae *AppInfoException, err error)
	// 根据渠道id获取渠道信息
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Id: 渠道Id
	GetAppChannelById(header *common.RequestHeader, id ChannelId) (r *appinfo_types.AppChannel, ae *AppInfoException, err error)
	// 根据渠道ids获取渠道信息
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Ids: 渠道Id
	GetAppChannelsByIds(header *common.RequestHeader, ids []ChannelId) (r map[ChannelId]*appinfo_types.AppChannel, ae *AppInfoException, err error)
	// 根据渠道id获取app和渠道信息
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Chnid: 渠道Id
	GetAppAndChannelByChnId(header *common.RequestHeader, chnid ChannelId) (r *appinfo_types.AppInfoAppAndChannelResult, ae *AppInfoException, err error)
	// 根据渠道id获取app和渠道信息
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Channelids: 渠道Id
	GetAppsAndChannelsByChnIds(header *common.RequestHeader, channelids []ChannelId) (r map[ChannelId]*appinfo_types.AppInfoAppAndChannelResult, ae *AppInfoException, err error)
	// 获取所有的app，支持分页
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	GetAllApps(header *common.RequestHeader, offset AppQueryInt, limit AppQueryInt, ascending bool) (r *common.QueryResult, ae *AppInfoException, err error)
	// 根据传入的字段和字段对应的值和app类型进行查询，支持分页
	// queryParam可以为空，表示queryParam不作为查询条件
	// platform可以为空，表示platform不作为查询条件
	// 升序或降序由ascending指定
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - QueryParam: 查询参数
	//  - Platform: app类型
	//  - Offset: 偏移量
	//  - Limit: 数量限制
	//  - Ascending: 是否升序
	GetAppsByQueryParamAndPlatform(header *common.RequestHeader, queryParam *appinfo_types.QueryParam, platform AppPlatform, offset AppQueryInt, limit AppQueryInt, ascending bool) (r *common.QueryResult, ae *AppInfoException, err error)
	// 根据应用包名及平台类型获取app信息，加入平台是因为有可能不同平台包名会重复，同步历史数据的时候需要用到
	// 对于iOS来讲，包名是唯一的，对于Android来讲，相同包名可能存在不同版本的app（pkgName+version唯一）
	// 不需要分页，每个包对应的app数量有限
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - PackageName: 包名
	//  - Platform: 平台类型
	GetAppsByPkgNameAndPlatform(header *common.RequestHeader, packageName string, platform AppPlatform) (r map[AppId]*appinfo_types.App, ae *AppInfoException, err error)
	// 根据条件查询渠道信息
	//
	// Parameters:
	//  - Header: 请求信息结构体
	//  - Params: 搜索参数结构体 *
	SearchAppChannelsByParams(header *common.RequestHeader, params *appinfo_types.AppChannelParams) (r *common.QueryResult, err error)
}

//app信息服务接口定义
type AppInfoServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewAppInfoServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AppInfoServiceClient {
	return &AppInfoServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewAppInfoServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AppInfoServiceClient {
	return &AppInfoServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 添加app信息
//
// Parameters:
//  - Header: 请求消息结构体
//  - App: app信息结构体
func (p *AppInfoServiceClient) AddApp(header *common.RequestHeader, app *appinfo_types.App) (r AppId, ae *AppInfoException, err error) {
	if err = p.sendAddApp(header, app); err != nil {
		return
	}
	return p.recvAddApp()
}

func (p *AppInfoServiceClient) sendAddApp(header *common.RequestHeader, app *appinfo_types.App) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addApp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddAppArgs()
	args0.Header = header
	args0.App = app
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvAddApp() (value AppId, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddAppResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Ae != nil {
		ae = result1.Ae
	}
	return
}

// 编辑app信息
// packageName不允许修改
// platform不允许修改
//
// Parameters:
//  - Header: 请求消息结构体
//  - App: app信息结构体
func (p *AppInfoServiceClient) EditApp(header *common.RequestHeader, app *appinfo_types.App) (ae *AppInfoException, err error) {
	if err = p.sendEditApp(header, app); err != nil {
		return
	}
	return p.recvEditApp()
}

func (p *AppInfoServiceClient) sendEditApp(header *common.RequestHeader, app *appinfo_types.App) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editApp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewEditAppArgs()
	args4.Header = header
	args4.App = app
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvEditApp() (ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewEditAppResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result5.Ae != nil {
		ae = result5.Ae
	}
	return
}

// 根据appId获取app信息
//
// Parameters:
//  - Header: 请求消息结构体
//  - Id: 查询的appId
func (p *AppInfoServiceClient) GetAppById(header *common.RequestHeader, id AppId) (r *appinfo_types.App, ae *AppInfoException, err error) {
	if err = p.sendGetAppById(header, id); err != nil {
		return
	}
	return p.recvGetAppById()
}

func (p *AppInfoServiceClient) sendGetAppById(header *common.RequestHeader, id AppId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetAppByIdArgs()
	args8.Header = header
	args8.Id = id
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppById() (value *appinfo_types.App, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetAppByIdResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Ae != nil {
		ae = result9.Ae
	}
	return
}

// 根据appIds获取app信息
//
// Parameters:
//  - Header: 请求消息结构体
//  - Ids: 查询的appId
func (p *AppInfoServiceClient) GetAppsByIds(header *common.RequestHeader, ids []AppId) (r map[AppId]*appinfo_types.App, ae *AppInfoException, err error) {
	if err = p.sendGetAppsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetAppsByIds()
}

func (p *AppInfoServiceClient) sendGetAppsByIds(header *common.RequestHeader, ids []AppId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetAppsByIdsArgs()
	args12.Header = header
	args12.Ids = ids
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppsByIds() (value map[AppId]*appinfo_types.App, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetAppsByIdsResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Ae != nil {
		ae = result13.Ae
	}
	return
}

// 根据app类型获取app信息
//
// Parameters:
//  - Header: 请求消息结构体
//  - Platform: app类型
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *AppInfoServiceClient) GetAppsByPlatform(header *common.RequestHeader, platform AppPlatform, offset AppQueryInt, limit AppQueryInt, ascending bool) (r *common.QueryResult, ae *AppInfoException, err error) {
	if err = p.sendGetAppsByPlatform(header, platform, offset, limit, ascending); err != nil {
		return
	}
	return p.recvGetAppsByPlatform()
}

func (p *AppInfoServiceClient) sendGetAppsByPlatform(header *common.RequestHeader, platform AppPlatform, offset AppQueryInt, limit AppQueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppsByPlatform", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetAppsByPlatformArgs()
	args16.Header = header
	args16.Platform = platform
	args16.Offset = offset
	args16.Limit = limit
	args16.Ascending = ascending
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppsByPlatform() (value *common.QueryResult, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetAppsByPlatformResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Ae != nil {
		ae = result17.Ae
	}
	return
}

// 添加app渠道信息
//
// Parameters:
//  - Header: 请求消息结构体
//  - Appchannel: 渠道信息结构体
func (p *AppInfoServiceClient) AddAppChannel(header *common.RequestHeader, appchannel *appinfo_types.AppChannel) (r ChannelId, ae *AppInfoException, err error) {
	if err = p.sendAddAppChannel(header, appchannel); err != nil {
		return
	}
	return p.recvAddAppChannel()
}

func (p *AppInfoServiceClient) sendAddAppChannel(header *common.RequestHeader, appchannel *appinfo_types.AppChannel) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addAppChannel", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewAddAppChannelArgs()
	args20.Header = header
	args20.Appchannel = appchannel
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvAddAppChannel() (value ChannelId, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewAddAppChannelResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Ae != nil {
		ae = result21.Ae
	}
	return
}

// 编辑app渠道信息
//
// Parameters:
//  - Header: 请求信息结构体
//  - Appchannel: 渠道信息结构体
func (p *AppInfoServiceClient) EditAppChannel(header *common.RequestHeader, appchannel *appinfo_types.AppChannel) (ae *AppInfoException, err error) {
	if err = p.sendEditAppChannel(header, appchannel); err != nil {
		return
	}
	return p.recvEditAppChannel()
}

func (p *AppInfoServiceClient) sendEditAppChannel(header *common.RequestHeader, appchannel *appinfo_types.AppChannel) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editAppChannel", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewEditAppChannelArgs()
	args24.Header = header
	args24.Appchannel = appchannel
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvEditAppChannel() (ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewEditAppChannelResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result25.Ae != nil {
		ae = result25.Ae
	}
	return
}

// 根据appId获取渠道信息
//
// Parameters:
//  - Header: 请求信息结构体
//  - Appid: appId
func (p *AppInfoServiceClient) GetAppChannelsByAppId(header *common.RequestHeader, appid AppId) (r map[ChannelId]*appinfo_types.AppChannel, ae *AppInfoException, err error) {
	if err = p.sendGetAppChannelsByAppId(header, appid); err != nil {
		return
	}
	return p.recvGetAppChannelsByAppId()
}

func (p *AppInfoServiceClient) sendGetAppChannelsByAppId(header *common.RequestHeader, appid AppId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppChannelsByAppId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewGetAppChannelsByAppIdArgs()
	args28.Header = header
	args28.Appid = appid
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppChannelsByAppId() (value map[ChannelId]*appinfo_types.AppChannel, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewGetAppChannelsByAppIdResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Ae != nil {
		ae = result29.Ae
	}
	return
}

// 根据渠道id获取渠道信息
//
// Parameters:
//  - Header: 请求信息结构体
//  - Id: 渠道Id
func (p *AppInfoServiceClient) GetAppChannelById(header *common.RequestHeader, id ChannelId) (r *appinfo_types.AppChannel, ae *AppInfoException, err error) {
	if err = p.sendGetAppChannelById(header, id); err != nil {
		return
	}
	return p.recvGetAppChannelById()
}

func (p *AppInfoServiceClient) sendGetAppChannelById(header *common.RequestHeader, id ChannelId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppChannelById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetAppChannelByIdArgs()
	args32.Header = header
	args32.Id = id
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppChannelById() (value *appinfo_types.AppChannel, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetAppChannelByIdResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Ae != nil {
		ae = result33.Ae
	}
	return
}

// 根据渠道ids获取渠道信息
//
// Parameters:
//  - Header: 请求信息结构体
//  - Ids: 渠道Id
func (p *AppInfoServiceClient) GetAppChannelsByIds(header *common.RequestHeader, ids []ChannelId) (r map[ChannelId]*appinfo_types.AppChannel, ae *AppInfoException, err error) {
	if err = p.sendGetAppChannelsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetAppChannelsByIds()
}

func (p *AppInfoServiceClient) sendGetAppChannelsByIds(header *common.RequestHeader, ids []ChannelId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppChannelsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetAppChannelsByIdsArgs()
	args36.Header = header
	args36.Ids = ids
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppChannelsByIds() (value map[ChannelId]*appinfo_types.AppChannel, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetAppChannelsByIdsResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Ae != nil {
		ae = result37.Ae
	}
	return
}

// 根据渠道id获取app和渠道信息
//
// Parameters:
//  - Header: 请求信息结构体
//  - Chnid: 渠道Id
func (p *AppInfoServiceClient) GetAppAndChannelByChnId(header *common.RequestHeader, chnid ChannelId) (r *appinfo_types.AppInfoAppAndChannelResult, ae *AppInfoException, err error) {
	if err = p.sendGetAppAndChannelByChnId(header, chnid); err != nil {
		return
	}
	return p.recvGetAppAndChannelByChnId()
}

func (p *AppInfoServiceClient) sendGetAppAndChannelByChnId(header *common.RequestHeader, chnid ChannelId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppAndChannelByChnId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewGetAppAndChannelByChnIdArgs()
	args40.Header = header
	args40.Chnid = chnid
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppAndChannelByChnId() (value *appinfo_types.AppInfoAppAndChannelResult, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewGetAppAndChannelByChnIdResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Ae != nil {
		ae = result41.Ae
	}
	return
}

// 根据渠道id获取app和渠道信息
//
// Parameters:
//  - Header: 请求信息结构体
//  - Channelids: 渠道Id
func (p *AppInfoServiceClient) GetAppsAndChannelsByChnIds(header *common.RequestHeader, channelids []ChannelId) (r map[ChannelId]*appinfo_types.AppInfoAppAndChannelResult, ae *AppInfoException, err error) {
	if err = p.sendGetAppsAndChannelsByChnIds(header, channelids); err != nil {
		return
	}
	return p.recvGetAppsAndChannelsByChnIds()
}

func (p *AppInfoServiceClient) sendGetAppsAndChannelsByChnIds(header *common.RequestHeader, channelids []ChannelId) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppsAndChannelsByChnIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewGetAppsAndChannelsByChnIdsArgs()
	args44.Header = header
	args44.Channelids = channelids
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppsAndChannelsByChnIds() (value map[ChannelId]*appinfo_types.AppInfoAppAndChannelResult, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewGetAppsAndChannelsByChnIdsResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.Ae != nil {
		ae = result45.Ae
	}
	return
}

// 获取所有的app，支持分页
//
// Parameters:
//  - Header: 请求信息结构体
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *AppInfoServiceClient) GetAllApps(header *common.RequestHeader, offset AppQueryInt, limit AppQueryInt, ascending bool) (r *common.QueryResult, ae *AppInfoException, err error) {
	if err = p.sendGetAllApps(header, offset, limit, ascending); err != nil {
		return
	}
	return p.recvGetAllApps()
}

func (p *AppInfoServiceClient) sendGetAllApps(header *common.RequestHeader, offset AppQueryInt, limit AppQueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAllApps", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewGetAllAppsArgs()
	args48.Header = header
	args48.Offset = offset
	args48.Limit = limit
	args48.Ascending = ascending
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAllApps() (value *common.QueryResult, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewGetAllAppsResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Ae != nil {
		ae = result49.Ae
	}
	return
}

// 根据传入的字段和字段对应的值和app类型进行查询，支持分页
// queryParam可以为空，表示queryParam不作为查询条件
// platform可以为空，表示platform不作为查询条件
// 升序或降序由ascending指定
//
// Parameters:
//  - Header: 请求信息结构体
//  - QueryParam: 查询参数
//  - Platform: app类型
//  - Offset: 偏移量
//  - Limit: 数量限制
//  - Ascending: 是否升序
func (p *AppInfoServiceClient) GetAppsByQueryParamAndPlatform(header *common.RequestHeader, queryParam *appinfo_types.QueryParam, platform AppPlatform, offset AppQueryInt, limit AppQueryInt, ascending bool) (r *common.QueryResult, ae *AppInfoException, err error) {
	if err = p.sendGetAppsByQueryParamAndPlatform(header, queryParam, platform, offset, limit, ascending); err != nil {
		return
	}
	return p.recvGetAppsByQueryParamAndPlatform()
}

func (p *AppInfoServiceClient) sendGetAppsByQueryParamAndPlatform(header *common.RequestHeader, queryParam *appinfo_types.QueryParam, platform AppPlatform, offset AppQueryInt, limit AppQueryInt, ascending bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppsByQueryParamAndPlatform", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewGetAppsByQueryParamAndPlatformArgs()
	args52.Header = header
	args52.QueryParam = queryParam
	args52.Platform = platform
	args52.Offset = offset
	args52.Limit = limit
	args52.Ascending = ascending
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppsByQueryParamAndPlatform() (value *common.QueryResult, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewGetAppsByQueryParamAndPlatformResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.Ae != nil {
		ae = result53.Ae
	}
	return
}

// 根据应用包名及平台类型获取app信息，加入平台是因为有可能不同平台包名会重复，同步历史数据的时候需要用到
// 对于iOS来讲，包名是唯一的，对于Android来讲，相同包名可能存在不同版本的app（pkgName+version唯一）
// 不需要分页，每个包对应的app数量有限
//
// Parameters:
//  - Header: 请求信息结构体
//  - PackageName: 包名
//  - Platform: 平台类型
func (p *AppInfoServiceClient) GetAppsByPkgNameAndPlatform(header *common.RequestHeader, packageName string, platform AppPlatform) (r map[AppId]*appinfo_types.App, ae *AppInfoException, err error) {
	if err = p.sendGetAppsByPkgNameAndPlatform(header, packageName, platform); err != nil {
		return
	}
	return p.recvGetAppsByPkgNameAndPlatform()
}

func (p *AppInfoServiceClient) sendGetAppsByPkgNameAndPlatform(header *common.RequestHeader, packageName string, platform AppPlatform) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppsByPkgNameAndPlatform", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewGetAppsByPkgNameAndPlatformArgs()
	args56.Header = header
	args56.PackageName = packageName
	args56.Platform = platform
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvGetAppsByPkgNameAndPlatform() (value map[AppId]*appinfo_types.App, ae *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewGetAppsByPkgNameAndPlatformResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.Ae != nil {
		ae = result57.Ae
	}
	return
}

// 根据条件查询渠道信息
//
// Parameters:
//  - Header: 请求信息结构体
//  - Params: 搜索参数结构体 *
func (p *AppInfoServiceClient) SearchAppChannelsByParams(header *common.RequestHeader, params *appinfo_types.AppChannelParams) (r *common.QueryResult, err error) {
	if err = p.sendSearchAppChannelsByParams(header, params); err != nil {
		return
	}
	return p.recvSearchAppChannelsByParams()
}

func (p *AppInfoServiceClient) sendSearchAppChannelsByParams(header *common.RequestHeader, params *appinfo_types.AppChannelParams) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchAppChannelsByParams", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewSearchAppChannelsByParamsArgs()
	args60.Header = header
	args60.Params = params
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppInfoServiceClient) recvSearchAppChannelsByParams() (value *common.QueryResult, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewSearchAppChannelsByParamsResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result61.Success
	return
}

type AppInfoServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AppInfoService
}

func (p *AppInfoServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AppInfoServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AppInfoServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAppInfoServiceProcessor(handler AppInfoService) *AppInfoServiceProcessor {

	self64 := &AppInfoServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self64.processorMap["addApp"] = &appInfoServiceProcessorAddApp{handler: handler}
	self64.processorMap["editApp"] = &appInfoServiceProcessorEditApp{handler: handler}
	self64.processorMap["getAppById"] = &appInfoServiceProcessorGetAppById{handler: handler}
	self64.processorMap["getAppsByIds"] = &appInfoServiceProcessorGetAppsByIds{handler: handler}
	self64.processorMap["getAppsByPlatform"] = &appInfoServiceProcessorGetAppsByPlatform{handler: handler}
	self64.processorMap["addAppChannel"] = &appInfoServiceProcessorAddAppChannel{handler: handler}
	self64.processorMap["editAppChannel"] = &appInfoServiceProcessorEditAppChannel{handler: handler}
	self64.processorMap["getAppChannelsByAppId"] = &appInfoServiceProcessorGetAppChannelsByAppId{handler: handler}
	self64.processorMap["getAppChannelById"] = &appInfoServiceProcessorGetAppChannelById{handler: handler}
	self64.processorMap["getAppChannelsByIds"] = &appInfoServiceProcessorGetAppChannelsByIds{handler: handler}
	self64.processorMap["getAppAndChannelByChnId"] = &appInfoServiceProcessorGetAppAndChannelByChnId{handler: handler}
	self64.processorMap["getAppsAndChannelsByChnIds"] = &appInfoServiceProcessorGetAppsAndChannelsByChnIds{handler: handler}
	self64.processorMap["getAllApps"] = &appInfoServiceProcessorGetAllApps{handler: handler}
	self64.processorMap["getAppsByQueryParamAndPlatform"] = &appInfoServiceProcessorGetAppsByQueryParamAndPlatform{handler: handler}
	self64.processorMap["getAppsByPkgNameAndPlatform"] = &appInfoServiceProcessorGetAppsByPkgNameAndPlatform{handler: handler}
	self64.processorMap["searchAppChannelsByParams"] = &appInfoServiceProcessorSearchAppChannelsByParams{handler: handler}
	return self64
}

func (p *AppInfoServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x65 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x65.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x65

}

type appInfoServiceProcessorAddApp struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorAddApp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAppArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAppResult()
	if result.Success, result.Ae, err = p.handler.AddApp(args.Header, args.App); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addApp: "+err.Error())
		oprot.WriteMessageBegin("addApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addApp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorEditApp struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorEditApp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditAppArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditAppResult()
	if result.Ae, err = p.handler.EditApp(args.Header, args.App); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editApp: "+err.Error())
		oprot.WriteMessageBegin("editApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editApp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppById struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppByIdResult()
	if result.Success, result.Ae, err = p.handler.GetAppById(args.Header, args.Id); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppById: "+err.Error())
		oprot.WriteMessageBegin("getAppById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppsByIds struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppsByIdsResult()
	if result.Success, result.Ae, err = p.handler.GetAppsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppsByIds: "+err.Error())
		oprot.WriteMessageBegin("getAppsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppsByPlatform struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppsByPlatform) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppsByPlatformArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppsByPlatform", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppsByPlatformResult()
	if result.Success, result.Ae, err = p.handler.GetAppsByPlatform(args.Header, args.Platform, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppsByPlatform: "+err.Error())
		oprot.WriteMessageBegin("getAppsByPlatform", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppsByPlatform", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorAddAppChannel struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorAddAppChannel) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAppChannelArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addAppChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAppChannelResult()
	if result.Success, result.Ae, err = p.handler.AddAppChannel(args.Header, args.Appchannel); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addAppChannel: "+err.Error())
		oprot.WriteMessageBegin("addAppChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addAppChannel", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorEditAppChannel struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorEditAppChannel) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditAppChannelArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editAppChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditAppChannelResult()
	if result.Ae, err = p.handler.EditAppChannel(args.Header, args.Appchannel); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editAppChannel: "+err.Error())
		oprot.WriteMessageBegin("editAppChannel", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editAppChannel", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppChannelsByAppId struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppChannelsByAppId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppChannelsByAppIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppChannelsByAppId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppChannelsByAppIdResult()
	if result.Success, result.Ae, err = p.handler.GetAppChannelsByAppId(args.Header, args.Appid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppChannelsByAppId: "+err.Error())
		oprot.WriteMessageBegin("getAppChannelsByAppId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppChannelsByAppId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppChannelById struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppChannelById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppChannelByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppChannelById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppChannelByIdResult()
	if result.Success, result.Ae, err = p.handler.GetAppChannelById(args.Header, args.Id); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppChannelById: "+err.Error())
		oprot.WriteMessageBegin("getAppChannelById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppChannelById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppChannelsByIds struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppChannelsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppChannelsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppChannelsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppChannelsByIdsResult()
	if result.Success, result.Ae, err = p.handler.GetAppChannelsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppChannelsByIds: "+err.Error())
		oprot.WriteMessageBegin("getAppChannelsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppChannelsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppAndChannelByChnId struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppAndChannelByChnId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppAndChannelByChnIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppAndChannelByChnId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppAndChannelByChnIdResult()
	if result.Success, result.Ae, err = p.handler.GetAppAndChannelByChnId(args.Header, args.Chnid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppAndChannelByChnId: "+err.Error())
		oprot.WriteMessageBegin("getAppAndChannelByChnId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppAndChannelByChnId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppsAndChannelsByChnIds struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppsAndChannelsByChnIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppsAndChannelsByChnIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppsAndChannelsByChnIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppsAndChannelsByChnIdsResult()
	if result.Success, result.Ae, err = p.handler.GetAppsAndChannelsByChnIds(args.Header, args.Channelids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppsAndChannelsByChnIds: "+err.Error())
		oprot.WriteMessageBegin("getAppsAndChannelsByChnIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppsAndChannelsByChnIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAllApps struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAllApps) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllAppsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAllApps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllAppsResult()
	if result.Success, result.Ae, err = p.handler.GetAllApps(args.Header, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAllApps: "+err.Error())
		oprot.WriteMessageBegin("getAllApps", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAllApps", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppsByQueryParamAndPlatform struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppsByQueryParamAndPlatform) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppsByQueryParamAndPlatformArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppsByQueryParamAndPlatform", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppsByQueryParamAndPlatformResult()
	if result.Success, result.Ae, err = p.handler.GetAppsByQueryParamAndPlatform(args.Header, args.QueryParam, args.Platform, args.Offset, args.Limit, args.Ascending); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppsByQueryParamAndPlatform: "+err.Error())
		oprot.WriteMessageBegin("getAppsByQueryParamAndPlatform", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppsByQueryParamAndPlatform", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorGetAppsByPkgNameAndPlatform struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorGetAppsByPkgNameAndPlatform) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppsByPkgNameAndPlatformArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppsByPkgNameAndPlatform", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppsByPkgNameAndPlatformResult()
	if result.Success, result.Ae, err = p.handler.GetAppsByPkgNameAndPlatform(args.Header, args.PackageName, args.Platform); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppsByPkgNameAndPlatform: "+err.Error())
		oprot.WriteMessageBegin("getAppsByPkgNameAndPlatform", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppsByPkgNameAndPlatform", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appInfoServiceProcessorSearchAppChannelsByParams struct {
	handler AppInfoService
}

func (p *appInfoServiceProcessorSearchAppChannelsByParams) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchAppChannelsByParamsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchAppChannelsByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchAppChannelsByParamsResult()
	if result.Success, err = p.handler.SearchAppChannelsByParams(args.Header, args.Params); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchAppChannelsByParams: "+err.Error())
		oprot.WriteMessageBegin("searchAppChannelsByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchAppChannelsByParams", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddAppArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	App    *appinfo_types.App    `thrift:"app,2" json:"app"`
}

func NewAddAppArgs() *AddAppArgs {
	return &AddAppArgs{}
}

func (p *AddAppArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAppArgs) readField2(iprot thrift.TProtocol) error {
	p.App = appinfo_types.NewApp()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *AddAppArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addApp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAppArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app: %s", p, err)
		}
	}
	return err
}

func (p *AddAppArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppArgs(%+v)", *p)
}

type AddAppResult struct {
	Success AppId             `thrift:"success,0" json:"success"`
	Ae      *AppInfoException `thrift:"ae,1" json:"ae"`
}

func NewAddAppResult() *AddAppResult {
	return &AddAppResult{}
}

func (p *AddAppResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = AppId(v)
	}
	return nil
}

func (p *AddAppResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *AddAppResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addApp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddAppResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *AddAppResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppResult(%+v)", *p)
}

type EditAppArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	App    *appinfo_types.App    `thrift:"app,2" json:"app"`
}

func NewEditAppArgs() *EditAppArgs {
	return &EditAppArgs{}
}

func (p *EditAppArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAppArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditAppArgs) readField2(iprot thrift.TProtocol) error {
	p.App = appinfo_types.NewApp()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *EditAppArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editApp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAppArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditAppArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app: %s", p, err)
		}
	}
	return err
}

func (p *EditAppArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAppArgs(%+v)", *p)
}

type EditAppResult struct {
	Ae *AppInfoException `thrift:"ae,1" json:"ae"`
}

func NewEditAppResult() *EditAppResult {
	return &EditAppResult{}
}

func (p *EditAppResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAppResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *EditAppResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editApp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAppResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *EditAppResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAppResult(%+v)", *p)
}

type GetAppByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Id     AppId                 `thrift:"id,2" json:"id"`
}

func NewGetAppByIdArgs() *GetAppByIdArgs {
	return &GetAppByIdArgs{}
}

func (p *GetAppByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = AppId(v)
	}
	return nil
}

func (p *GetAppByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *GetAppByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppByIdArgs(%+v)", *p)
}

type GetAppByIdResult struct {
	Success *appinfo_types.App `thrift:"success,0" json:"success"`
	Ae      *AppInfoException  `thrift:"ae,1" json:"ae"`
}

func NewGetAppByIdResult() *GetAppByIdResult {
	return &GetAppByIdResult{}
}

func (p *GetAppByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appinfo_types.NewApp()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppByIdResult(%+v)", *p)
}

type GetAppsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []AppId               `thrift:"ids,2" json:"ids"`
}

func NewGetAppsByIdsArgs() *GetAppsByIdsArgs {
	return &GetAppsByIdsArgs{}
}

func (p *GetAppsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]AppId, 0, size)
	for i := 0; i < size; i++ {
		var _elem66 AppId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem66 = AppId(v)
		}
		p.Ids = append(p.Ids, _elem66)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByIdsArgs(%+v)", *p)
}

type GetAppsByIdsResult struct {
	Success map[AppId]*appinfo_types.App `thrift:"success,0" json:"success"`
	Ae      *AppInfoException            `thrift:"ae,1" json:"ae"`
}

func NewGetAppsByIdsResult() *GetAppsByIdsResult {
	return &GetAppsByIdsResult{}
}

func (p *GetAppsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[AppId]*appinfo_types.App, size)
	for i := 0; i < size; i++ {
		var _key67 AppId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key67 = AppId(v)
		}
		_val68 := appinfo_types.NewApp()
		if err := _val68.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val68)
		}
		p.Success[_key67] = _val68
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAppsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByIdsResult(%+v)", *p)
}

type GetAppsByPlatformArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Platform AppPlatform           `thrift:"platform,2" json:"platform"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    AppQueryInt `thrift:"offset,10" json:"offset"`
	Limit     AppQueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool        `thrift:"ascending,12" json:"ascending"`
}

func NewGetAppsByPlatformArgs() *GetAppsByPlatformArgs {
	return &GetAppsByPlatformArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetAppsByPlatformArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetAppsByPlatformArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPlatformArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppsByPlatformArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = AppPlatform(v)
	}
	return nil
}

func (p *GetAppsByPlatformArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = AppQueryInt(v)
	}
	return nil
}

func (p *GetAppsByPlatformArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = AppQueryInt(v)
	}
	return nil
}

func (p *GetAppsByPlatformArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *GetAppsByPlatformArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByPlatform_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPlatformArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByPlatformArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *GetAppsByPlatformArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *GetAppsByPlatformArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *GetAppsByPlatformArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *GetAppsByPlatformArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByPlatformArgs(%+v)", *p)
}

type GetAppsByPlatformResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ae      *AppInfoException   `thrift:"ae,1" json:"ae"`
}

func NewGetAppsByPlatformResult() *GetAppsByPlatformResult {
	return &GetAppsByPlatformResult{}
}

func (p *GetAppsByPlatformResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPlatformResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppsByPlatformResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppsByPlatformResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByPlatform_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPlatformResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByPlatformResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByPlatformResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByPlatformResult(%+v)", *p)
}

type AddAppChannelArgs struct {
	Header     *common.RequestHeader     `thrift:"header,1" json:"header"`
	Appchannel *appinfo_types.AppChannel `thrift:"appchannel,2" json:"appchannel"`
}

func NewAddAppChannelArgs() *AddAppChannelArgs {
	return &AddAppChannelArgs{}
}

func (p *AddAppChannelArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppChannelArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAppChannelArgs) readField2(iprot thrift.TProtocol) error {
	p.Appchannel = appinfo_types.NewAppChannel()
	if err := p.Appchannel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Appchannel)
	}
	return nil
}

func (p *AddAppChannelArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppChannel_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppChannelArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAppChannelArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Appchannel != nil {
		if err := oprot.WriteFieldBegin("appchannel", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:appchannel: %s", p, err)
		}
		if err := p.Appchannel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Appchannel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:appchannel: %s", p, err)
		}
	}
	return err
}

func (p *AddAppChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppChannelArgs(%+v)", *p)
}

type AddAppChannelResult struct {
	Success ChannelId         `thrift:"success,0" json:"success"`
	Ae      *AppInfoException `thrift:"ae,1" json:"ae"`
}

func NewAddAppChannelResult() *AddAppChannelResult {
	return &AddAppChannelResult{}
}

func (p *AddAppChannelResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppChannelResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = ChannelId(v)
	}
	return nil
}

func (p *AddAppChannelResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *AddAppChannelResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppChannel_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppChannelResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddAppChannelResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *AddAppChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppChannelResult(%+v)", *p)
}

type EditAppChannelArgs struct {
	Header     *common.RequestHeader     `thrift:"header,1" json:"header"`
	Appchannel *appinfo_types.AppChannel `thrift:"appchannel,2" json:"appchannel"`
}

func NewEditAppChannelArgs() *EditAppChannelArgs {
	return &EditAppChannelArgs{}
}

func (p *EditAppChannelArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAppChannelArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditAppChannelArgs) readField2(iprot thrift.TProtocol) error {
	p.Appchannel = appinfo_types.NewAppChannel()
	if err := p.Appchannel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Appchannel)
	}
	return nil
}

func (p *EditAppChannelArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editAppChannel_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAppChannelArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditAppChannelArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Appchannel != nil {
		if err := oprot.WriteFieldBegin("appchannel", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:appchannel: %s", p, err)
		}
		if err := p.Appchannel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Appchannel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:appchannel: %s", p, err)
		}
	}
	return err
}

func (p *EditAppChannelArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAppChannelArgs(%+v)", *p)
}

type EditAppChannelResult struct {
	Ae *AppInfoException `thrift:"ae,1" json:"ae"`
}

func NewEditAppChannelResult() *EditAppChannelResult {
	return &EditAppChannelResult{}
}

func (p *EditAppChannelResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAppChannelResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *EditAppChannelResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editAppChannel_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAppChannelResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *EditAppChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAppChannelResult(%+v)", *p)
}

type GetAppChannelsByAppIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Appid  AppId                 `thrift:"appid,2" json:"appid"`
}

func NewGetAppChannelsByAppIdArgs() *GetAppChannelsByAppIdArgs {
	return &GetAppChannelsByAppIdArgs{}
}

func (p *GetAppChannelsByAppIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByAppIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppChannelsByAppIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = AppId(v)
	}
	return nil
}

func (p *GetAppChannelsByAppIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppChannelsByAppId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByAppIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelsByAppIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *GetAppChannelsByAppIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppChannelsByAppIdArgs(%+v)", *p)
}

type GetAppChannelsByAppIdResult struct {
	Success map[ChannelId]*appinfo_types.AppChannel `thrift:"success,0" json:"success"`
	Ae      *AppInfoException                       `thrift:"ae,1" json:"ae"`
}

func NewGetAppChannelsByAppIdResult() *GetAppChannelsByAppIdResult {
	return &GetAppChannelsByAppIdResult{}
}

func (p *GetAppChannelsByAppIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByAppIdResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[ChannelId]*appinfo_types.AppChannel, size)
	for i := 0; i < size; i++ {
		var _key69 ChannelId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key69 = ChannelId(v)
		}
		_val70 := appinfo_types.NewAppChannel()
		if err := _val70.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val70)
		}
		p.Success[_key69] = _val70
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAppChannelsByAppIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppChannelsByAppIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppChannelsByAppId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByAppIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelsByAppIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelsByAppIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppChannelsByAppIdResult(%+v)", *p)
}

type GetAppChannelByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Id     ChannelId             `thrift:"id,2" json:"id"`
}

func NewGetAppChannelByIdArgs() *GetAppChannelByIdArgs {
	return &GetAppChannelByIdArgs{}
}

func (p *GetAppChannelByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppChannelByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = ChannelId(v)
	}
	return nil
}

func (p *GetAppChannelByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppChannelById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *GetAppChannelByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppChannelByIdArgs(%+v)", *p)
}

type GetAppChannelByIdResult struct {
	Success *appinfo_types.AppChannel `thrift:"success,0" json:"success"`
	Ae      *AppInfoException         `thrift:"ae,1" json:"ae"`
}

func NewGetAppChannelByIdResult() *GetAppChannelByIdResult {
	return &GetAppChannelByIdResult{}
}

func (p *GetAppChannelByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appinfo_types.NewAppChannel()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppChannelByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppChannelByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppChannelById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppChannelByIdResult(%+v)", *p)
}

type GetAppChannelsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []ChannelId           `thrift:"ids,2" json:"ids"`
}

func NewGetAppChannelsByIdsArgs() *GetAppChannelsByIdsArgs {
	return &GetAppChannelsByIdsArgs{}
}

func (p *GetAppChannelsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppChannelsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]ChannelId, 0, size)
	for i := 0; i < size; i++ {
		var _elem71 ChannelId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem71 = ChannelId(v)
		}
		p.Ids = append(p.Ids, _elem71)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppChannelsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppChannelsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppChannelsByIdsArgs(%+v)", *p)
}

type GetAppChannelsByIdsResult struct {
	Success map[ChannelId]*appinfo_types.AppChannel `thrift:"success,0" json:"success"`
	Ae      *AppInfoException                       `thrift:"ae,1" json:"ae"`
}

func NewGetAppChannelsByIdsResult() *GetAppChannelsByIdsResult {
	return &GetAppChannelsByIdsResult{}
}

func (p *GetAppChannelsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[ChannelId]*appinfo_types.AppChannel, size)
	for i := 0; i < size; i++ {
		var _key72 ChannelId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key72 = ChannelId(v)
		}
		_val73 := appinfo_types.NewAppChannel()
		if err := _val73.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val73)
		}
		p.Success[_key72] = _val73
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAppChannelsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppChannelsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppChannelsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppChannelsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppChannelsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppChannelsByIdsResult(%+v)", *p)
}

type GetAppAndChannelByChnIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Chnid  ChannelId             `thrift:"chnid,2" json:"chnid"`
}

func NewGetAppAndChannelByChnIdArgs() *GetAppAndChannelByChnIdArgs {
	return &GetAppAndChannelByChnIdArgs{}
}

func (p *GetAppAndChannelByChnIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Chnid = ChannelId(v)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppAndChannelByChnId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppAndChannelByChnIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:chnid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Chnid)); err != nil {
		return fmt.Errorf("%T.chnid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:chnid: %s", p, err)
	}
	return err
}

func (p *GetAppAndChannelByChnIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppAndChannelByChnIdArgs(%+v)", *p)
}

type GetAppAndChannelByChnIdResult struct {
	Success *appinfo_types.AppInfoAppAndChannelResult `thrift:"success,0" json:"success"`
	Ae      *AppInfoException                         `thrift:"ae,1" json:"ae"`
}

func NewGetAppAndChannelByChnIdResult() *GetAppAndChannelByChnIdResult {
	return &GetAppAndChannelByChnIdResult{}
}

func (p *GetAppAndChannelByChnIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appinfo_types.NewAppInfoAppAndChannelResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppAndChannelByChnId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppAndChannelByChnIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppAndChannelByChnIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppAndChannelByChnIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppAndChannelByChnIdResult(%+v)", *p)
}

type GetAppsAndChannelsByChnIdsArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	Channelids []ChannelId           `thrift:"channelids,2" json:"channelids"`
}

func NewGetAppsAndChannelsByChnIdsArgs() *GetAppsAndChannelsByChnIdsArgs {
	return &GetAppsAndChannelsByChnIdsArgs{}
}

func (p *GetAppsAndChannelsByChnIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Channelids = make([]ChannelId, 0, size)
	for i := 0; i < size; i++ {
		var _elem74 ChannelId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem74 = ChannelId(v)
		}
		p.Channelids = append(p.Channelids, _elem74)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsAndChannelsByChnIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsAndChannelsByChnIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Channelids != nil {
		if err := oprot.WriteFieldBegin("channelids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:channelids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Channelids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Channelids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:channelids: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsAndChannelsByChnIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsAndChannelsByChnIdsArgs(%+v)", *p)
}

type GetAppsAndChannelsByChnIdsResult struct {
	Success map[ChannelId]*appinfo_types.AppInfoAppAndChannelResult `thrift:"success,0" json:"success"`
	Ae      *AppInfoException                                       `thrift:"ae,1" json:"ae"`
}

func NewGetAppsAndChannelsByChnIdsResult() *GetAppsAndChannelsByChnIdsResult {
	return &GetAppsAndChannelsByChnIdsResult{}
}

func (p *GetAppsAndChannelsByChnIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[ChannelId]*appinfo_types.AppInfoAppAndChannelResult, size)
	for i := 0; i < size; i++ {
		var _key75 ChannelId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key75 = ChannelId(v)
		}
		_val76 := appinfo_types.NewAppInfoAppAndChannelResult()
		if err := _val76.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val76)
		}
		p.Success[_key75] = _val76
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsAndChannelsByChnIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsAndChannelsByChnIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsAndChannelsByChnIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsAndChannelsByChnIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsAndChannelsByChnIdsResult(%+v)", *p)
}

type GetAllAppsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    AppQueryInt `thrift:"offset,10" json:"offset"`
	Limit     AppQueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool        `thrift:"ascending,12" json:"ascending"`
}

func NewGetAllAppsArgs() *GetAllAppsArgs {
	return &GetAllAppsArgs{}
}

func (p *GetAllAppsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllAppsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAllAppsArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = AppQueryInt(v)
	}
	return nil
}

func (p *GetAllAppsArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = AppQueryInt(v)
	}
	return nil
}

func (p *GetAllAppsArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *GetAllAppsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllApps_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllAppsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAllAppsArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *GetAllAppsArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *GetAllAppsArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *GetAllAppsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllAppsArgs(%+v)", *p)
}

type GetAllAppsResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ae      *AppInfoException   `thrift:"ae,1" json:"ae"`
}

func NewGetAllAppsResult() *GetAllAppsResult {
	return &GetAllAppsResult{}
}

func (p *GetAllAppsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllAppsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAllAppsResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAllAppsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAllApps_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllAppsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllAppsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAllAppsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllAppsResult(%+v)", *p)
}

type GetAppsByQueryParamAndPlatformArgs struct {
	Header     *common.RequestHeader     `thrift:"header,1" json:"header"`
	QueryParam *appinfo_types.QueryParam `thrift:"queryParam,2" json:"queryParam"`
	Platform   AppPlatform               `thrift:"platform,3" json:"platform"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset    AppQueryInt `thrift:"offset,10" json:"offset"`
	Limit     AppQueryInt `thrift:"limit,11" json:"limit"`
	Ascending bool        `thrift:"ascending,12" json:"ascending"`
}

func NewGetAppsByQueryParamAndPlatformArgs() *GetAppsByQueryParamAndPlatformArgs {
	return &GetAppsByQueryParamAndPlatformArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetAppsByQueryParamAndPlatformArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetAppsByQueryParamAndPlatformArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) readField2(iprot thrift.TProtocol) error {
	p.QueryParam = appinfo_types.NewQueryParam()
	if err := p.QueryParam.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.QueryParam)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Platform = AppPlatform(v)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = AppQueryInt(v)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = AppQueryInt(v)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ascending = v
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByQueryParamAndPlatform_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.QueryParam != nil {
		if err := oprot.WriteFieldBegin("queryParam", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:queryParam: %s", p, err)
		}
		if err := p.QueryParam.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.QueryParam)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:queryParam: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:platform: %s", p, err)
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformArgs) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformArgs) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ascending", thrift.BOOL, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ascending: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Ascending)); err != nil {
		return fmt.Errorf("%T.ascending (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ascending: %s", p, err)
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByQueryParamAndPlatformArgs(%+v)", *p)
}

type GetAppsByQueryParamAndPlatformResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	Ae      *AppInfoException   `thrift:"ae,1" json:"ae"`
}

func NewGetAppsByQueryParamAndPlatformResult() *GetAppsByQueryParamAndPlatformResult {
	return &GetAppsByQueryParamAndPlatformResult{}
}

func (p *GetAppsByQueryParamAndPlatformResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByQueryParamAndPlatform_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByQueryParamAndPlatformResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByQueryParamAndPlatformResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByQueryParamAndPlatformResult(%+v)", *p)
}

type GetAppsByPkgNameAndPlatformArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	PackageName string                `thrift:"packageName,2" json:"packageName"`
	Platform    AppPlatform           `thrift:"platform,3" json:"platform"`
}

func NewGetAppsByPkgNameAndPlatformArgs() *GetAppsByPkgNameAndPlatformArgs {
	return &GetAppsByPkgNameAndPlatformArgs{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetAppsByPkgNameAndPlatformArgs) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *GetAppsByPkgNameAndPlatformArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Platform = AppPlatform(v)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByPkgNameAndPlatform_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByPkgNameAndPlatformArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:packageName: %s", p, err)
	}
	return err
}

func (p *GetAppsByPkgNameAndPlatformArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:platform: %s", p, err)
	}
	return err
}

func (p *GetAppsByPkgNameAndPlatformArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByPkgNameAndPlatformArgs(%+v)", *p)
}

type GetAppsByPkgNameAndPlatformResult struct {
	Success map[AppId]*appinfo_types.App `thrift:"success,0" json:"success"`
	Ae      *AppInfoException            `thrift:"ae,1" json:"ae"`
}

func NewGetAppsByPkgNameAndPlatformResult() *GetAppsByPkgNameAndPlatformResult {
	return &GetAppsByPkgNameAndPlatformResult{}
}

func (p *GetAppsByPkgNameAndPlatformResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[AppId]*appinfo_types.App, size)
	for i := 0; i < size; i++ {
		var _key77 AppId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key77 = AppId(v)
		}
		_val78 := appinfo_types.NewApp()
		if err := _val78.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val78)
		}
		p.Success[_key77] = _val78
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppInfoException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppsByPkgNameAndPlatform_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppsByPkgNameAndPlatformResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByPkgNameAndPlatformResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppsByPkgNameAndPlatformResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppsByPkgNameAndPlatformResult(%+v)", *p)
}

type SearchAppChannelsByParamsArgs struct {
	Header *common.RequestHeader           `thrift:"header,1" json:"header"`
	Params *appinfo_types.AppChannelParams `thrift:"params,2" json:"params"`
}

func NewSearchAppChannelsByParamsArgs() *SearchAppChannelsByParamsArgs {
	return &SearchAppChannelsByParamsArgs{}
}

func (p *SearchAppChannelsByParamsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchAppChannelsByParamsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchAppChannelsByParamsArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = appinfo_types.NewAppChannelParams()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *SearchAppChannelsByParamsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchAppChannelsByParams_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchAppChannelsByParamsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchAppChannelsByParamsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *SearchAppChannelsByParamsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchAppChannelsByParamsArgs(%+v)", *p)
}

type SearchAppChannelsByParamsResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
}

func NewSearchAppChannelsByParamsResult() *SearchAppChannelsByParamsResult {
	return &SearchAppChannelsByParamsResult{}
}

func (p *SearchAppChannelsByParamsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchAppChannelsByParamsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchAppChannelsByParamsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchAppChannelsByParams_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchAppChannelsByParamsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchAppChannelsByParamsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchAppChannelsByParamsResult(%+v)", *p)
}
