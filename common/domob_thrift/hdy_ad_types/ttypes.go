// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package hdy_ad_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/hdy_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = hdy_types.GoUnusedProtection__
var GoUnusedProtection__ int

type Ad struct {
	OrderId    int32  `thrift:"orderId,1" json:"orderId"`
	StrategyId int32  `thrift:"strategyId,2" json:"strategyId"`
	CreativeId int32  `thrift:"creativeId,3" json:"creativeId"`
	PromoteUrl string `thrift:"promoteUrl,4" json:"promoteUrl"`
	ImgUrl     string `thrift:"imgUrl,5" json:"imgUrl"`
	Content    string `thrift:"content,6" json:"content"`
	ActionText string `thrift:"actionText,7" json:"actionText"`
}

func NewAd() *Ad {
	return &Ad{}
}

func (p *Ad) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Ad) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *Ad) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *Ad) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *Ad) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PromoteUrl = v
	}
	return nil
}

func (p *Ad) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ImgUrl = v
	}
	return nil
}

func (p *Ad) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *Ad) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ActionText = v
	}
	return nil
}

func (p *Ad) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Ad"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Ad) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:orderId: %s", p, err)
	}
	return err
}

func (p *Ad) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:strategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategyId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:strategyId: %s", p, err)
	}
	return err
}

func (p *Ad) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creativeId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creativeId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creativeId: %s", p, err)
	}
	return err
}

func (p *Ad) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promoteUrl", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:promoteUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PromoteUrl)); err != nil {
		return fmt.Errorf("%T.promoteUrl (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:promoteUrl: %s", p, err)
	}
	return err
}

func (p *Ad) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imgUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:imgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgUrl)); err != nil {
		return fmt.Errorf("%T.imgUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:imgUrl: %s", p, err)
	}
	return err
}

func (p *Ad) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:content: %s", p, err)
	}
	return err
}

func (p *Ad) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionText", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:actionText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionText)); err != nil {
		return fmt.Errorf("%T.actionText (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:actionText: %s", p, err)
	}
	return err
}

func (p *Ad) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Ad(%+v)", *p)
}

type Landing struct {
	Id  int32  `thrift:"id,1" json:"id"`
	Url string `thrift:"url,2" json:"url"`
}

func NewLanding() *Landing {
	return &Landing{}
}

func (p *Landing) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Landing) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Landing) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *Landing) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Landing"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Landing) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Landing) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:url: %s", p, err)
	}
	return err
}

func (p *Landing) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Landing(%+v)", *p)
}

type AdStrategy struct {
	OrderId      int32            `thrift:"orderId,1" json:"orderId"`
	Id           int32            `thrift:"id,2" json:"id"`
	Pid          int32            `thrift:"pid,3" json:"pid"`
	LocationIds  []int32          `thrift:"locationIds,4" json:"locationIds"`
	Devices      []string         `thrift:"devices,5" json:"devices"`
	Os           hdy_types.OSType `thrift:"os,6" json:"os"`
	Landings     []int32          `thrift:"landings,7" json:"landings"`
	MediaBanList []string         `thrift:"mediaBanList,8" json:"mediaBanList"`
	Ads          []*Ad            `thrift:"ads,9" json:"ads"`
}

func NewAdStrategy() *AdStrategy {
	return &AdStrategy{
		Os: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdStrategy) IsSetOs() bool {
	return int64(p.Os) != math.MinInt32-1
}

func (p *AdStrategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdStrategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *AdStrategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AdStrategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *AdStrategy) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.LocationIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.LocationIds = append(p.LocationIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Devices = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.Devices = append(p.Devices, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Os = hdy_types.OSType(v)
	}
	return nil
}

func (p *AdStrategy) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Landings = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.Landings = append(p.Landings, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaBanList = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.MediaBanList = append(p.MediaBanList, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ads = make([]*Ad, 0, size)
	for i := 0; i < size; i++ {
		_elem4 := NewAd()
		if err := _elem4.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem4)
		}
		p.Ads = append(p.Ads, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdStrategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orderId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:orderId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
		return fmt.Errorf("%T.orderId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:orderId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pid: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if p.LocationIds != nil {
		if err := oprot.WriteFieldBegin("locationIds", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:locationIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.LocationIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.LocationIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:locationIds: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Devices != nil {
		if err := oprot.WriteFieldBegin("devices", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:devices: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Devices)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Devices {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:devices: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOs() {
		if err := oprot.WriteFieldBegin("os", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:os: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Os)); err != nil {
			return fmt.Errorf("%T.os (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:os: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Landings != nil {
		if err := oprot.WriteFieldBegin("landings", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:landings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Landings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Landings {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:landings: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField8(oprot thrift.TProtocol) (err error) {
	if p.MediaBanList != nil {
		if err := oprot.WriteFieldBegin("mediaBanList", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:mediaBanList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.MediaBanList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaBanList {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:mediaBanList: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField9(oprot thrift.TProtocol) (err error) {
	if p.Ads != nil {
		if err := oprot.WriteFieldBegin("ads", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:ads: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Ads)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ads {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:ads: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdStrategy(%+v)", *p)
}

type Location struct {
	RegionId   int32 `thrift:"regionId,1" json:"regionId"`
	CityId     int32 `thrift:"cityId,2" json:"cityId"`
	ProvinceId int32 `thrift:"provinceId,3" json:"provinceId"`
	IpStart    int64 `thrift:"ipStart,4" json:"ipStart"`
}

func NewLocation() *Location {
	return &Location{}
}

func (p *Location) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Location) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.RegionId = v
	}
	return nil
}

func (p *Location) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CityId = v
	}
	return nil
}

func (p *Location) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ProvinceId = v
	}
	return nil
}

func (p *Location) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IpStart = v
	}
	return nil
}

func (p *Location) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Location"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Location) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("regionId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:regionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionId)); err != nil {
		return fmt.Errorf("%T.regionId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:regionId: %s", p, err)
	}
	return err
}

func (p *Location) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cityId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cityId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityId)); err != nil {
		return fmt.Errorf("%T.cityId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cityId: %s", p, err)
	}
	return err
}

func (p *Location) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("provinceId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:provinceId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProvinceId)); err != nil {
		return fmt.Errorf("%T.provinceId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:provinceId: %s", p, err)
	}
	return err
}

func (p *Location) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipStart", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ipStart: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.IpStart)); err != nil {
		return fmt.Errorf("%T.ipStart (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ipStart: %s", p, err)
	}
	return err
}

func (p *Location) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Location(%+v)", *p)
}

type MediaRedirect struct {
	Id           int32                             `thrift:"id,1" json:"id"`
	MediaName    string                            `thrift:"mediaName,2" json:"mediaName"`
	MediaChId    int32                             `thrift:"mediaChId,3" json:"mediaChId"`
	RedirectType hdy_types.MediaRedirectType       `thrift:"redirectType,4" json:"redirectType"`
	TargetType   hdy_types.MediaRedirectTargetType `thrift:"targetType,5" json:"targetType"`
	Landings     []*Landing                        `thrift:"landings,6" json:"landings"`
	LocationIds  []int32                           `thrift:"locationIds,7" json:"locationIds"`
	Devices      []string                          `thrift:"devices,8" json:"devices"`
	Os           hdy_types.OSType                  `thrift:"os,9" json:"os"`
}

func NewMediaRedirect() *MediaRedirect {
	return &MediaRedirect{
		RedirectType: math.MinInt32 - 1, // unset sentinal value

		TargetType: math.MinInt32 - 1, // unset sentinal value

		Os: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaRedirect) IsSetRedirectType() bool {
	return int64(p.RedirectType) != math.MinInt32-1
}

func (p *MediaRedirect) IsSetTargetType() bool {
	return int64(p.TargetType) != math.MinInt32-1
}

func (p *MediaRedirect) IsSetOs() bool {
	return int64(p.Os) != math.MinInt32-1
}

func (p *MediaRedirect) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaRedirect) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *MediaRedirect) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaName = v
	}
	return nil
}

func (p *MediaRedirect) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaChId = v
	}
	return nil
}

func (p *MediaRedirect) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RedirectType = hdy_types.MediaRedirectType(v)
	}
	return nil
}

func (p *MediaRedirect) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TargetType = hdy_types.MediaRedirectTargetType(v)
	}
	return nil
}

func (p *MediaRedirect) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Landings = make([]*Landing, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewLanding()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.Landings = append(p.Landings, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaRedirect) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.LocationIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.LocationIds = append(p.LocationIds, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaRedirect) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Devices = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.Devices = append(p.Devices, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MediaRedirect) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Os = hdy_types.OSType(v)
	}
	return nil
}

func (p *MediaRedirect) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaRedirect"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaRedirect) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mediaName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaName)); err != nil {
		return fmt.Errorf("%T.mediaName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mediaName: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaChId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mediaChId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaChId)); err != nil {
		return fmt.Errorf("%T.mediaChId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mediaChId: %s", p, err)
	}
	return err
}

func (p *MediaRedirect) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetRedirectType() {
		if err := oprot.WriteFieldBegin("redirectType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:redirectType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RedirectType)); err != nil {
			return fmt.Errorf("%T.redirectType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:redirectType: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTargetType() {
		if err := oprot.WriteFieldBegin("targetType", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:targetType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TargetType)); err != nil {
			return fmt.Errorf("%T.targetType (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:targetType: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Landings != nil {
		if err := oprot.WriteFieldBegin("landings", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:landings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Landings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Landings {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:landings: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField7(oprot thrift.TProtocol) (err error) {
	if p.LocationIds != nil {
		if err := oprot.WriteFieldBegin("locationIds", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:locationIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.LocationIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.LocationIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:locationIds: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField8(oprot thrift.TProtocol) (err error) {
	if p.Devices != nil {
		if err := oprot.WriteFieldBegin("devices", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:devices: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Devices)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Devices {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:devices: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetOs() {
		if err := oprot.WriteFieldBegin("os", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:os: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Os)); err != nil {
			return fmt.Errorf("%T.os (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:os: %s", p, err)
		}
	}
	return err
}

func (p *MediaRedirect) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaRedirect(%+v)", *p)
}
