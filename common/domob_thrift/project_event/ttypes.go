// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package project_event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/project_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = project_types.GoUnusedProtection__
var GoUnusedProtection__ int

type UidInt project_types.UidInt

type IdInt project_types.IdInt

type Schedule *project_types.Schedule

type Project *project_types.Project

type SchedulesUpdateEvent struct {
	Uid       UidInt                    `thrift:"uid,1" json:"uid"`
	Schedules []*project_types.Schedule `thrift:"Schedules,2" json:"Schedules"`
}

func NewSchedulesUpdateEvent() *SchedulesUpdateEvent {
	return &SchedulesUpdateEvent{}
}

func (p *SchedulesUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SchedulesUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SchedulesUpdateEvent) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Schedules = make([]*project_types.Schedule, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := project_types.NewSchedule()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Schedules = append(p.Schedules, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SchedulesUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SchedulesUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SchedulesUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *SchedulesUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Schedules != nil {
		if err := oprot.WriteFieldBegin("Schedules", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:Schedules: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Schedules)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Schedules {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:Schedules: %s", p, err)
		}
	}
	return err
}

func (p *SchedulesUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SchedulesUpdateEvent(%+v)", *p)
}

type ProjectUpdateEvent struct {
	Id      IdInt                  `thrift:"id,1" json:"id"`
	Project *project_types.Project `thrift:"project,2" json:"project"`
}

func NewProjectUpdateEvent() *ProjectUpdateEvent {
	return &ProjectUpdateEvent{}
}

func (p *ProjectUpdateEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProjectUpdateEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *ProjectUpdateEvent) readField2(iprot thrift.TProtocol) error {
	p.Project = project_types.NewProject()
	if err := p.Project.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Project)
	}
	return nil
}

func (p *ProjectUpdateEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ProjectUpdateEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProjectUpdateEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ProjectUpdateEvent) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Project != nil {
		if err := oprot.WriteFieldBegin("project", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:project: %s", p, err)
		}
		if err := p.Project.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Project)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:project: %s", p, err)
		}
	}
	return err
}

func (p *ProjectUpdateEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProjectUpdateEvent(%+v)", *p)
}
