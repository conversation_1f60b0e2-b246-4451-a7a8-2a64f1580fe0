// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package fea_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__

type DmpFeaServer interface {
	dm303.DomobService

	// Parameters:
	//  - Feareq
	WriteFea(feareq *FeaServerRequest) (r *FeaServerResponse, err error)
}

type DmpFeaServerClient struct {
	*dm303.DomobServiceClient
}

func NewDmpFeaServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DmpFeaServerClient {
	return &DmpFeaServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDmpFeaServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DmpFeaServerClient {
	return &DmpFeaServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Feareq
func (p *DmpFeaServerClient) WriteFea(feareq *FeaServerRequest) (r *FeaServerResponse, err error) {
	if err = p.sendWriteFea(feareq); err != nil {
		return
	}
	return p.recvWriteFea()
}

func (p *DmpFeaServerClient) sendWriteFea(feareq *FeaServerRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("WriteFea", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewWriteFeaArgs()
	args0.Feareq = feareq
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DmpFeaServerClient) recvWriteFea() (value *FeaServerResponse, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewWriteFeaResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

type DmpFeaServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDmpFeaServerProcessor(handler DmpFeaServer) *DmpFeaServerProcessor {
	self4 := &DmpFeaServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self4.AddToProcessorMap("WriteFea", &dmpFeaServerProcessorWriteFea{handler: handler})
	return self4
}

type dmpFeaServerProcessorWriteFea struct {
	handler DmpFeaServer
}

func (p *dmpFeaServerProcessorWriteFea) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewWriteFeaArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("WriteFea", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewWriteFeaResult()
	if result.Success, err = p.handler.WriteFea(args.Feareq); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing WriteFea: "+err.Error())
		oprot.WriteMessageBegin("WriteFea", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("WriteFea", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type WriteFeaArgs struct {
	Feareq *FeaServerRequest `thrift:"feareq,1" json:"feareq"`
}

func NewWriteFeaArgs() *WriteFeaArgs {
	return &WriteFeaArgs{}
}

func (p *WriteFeaArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WriteFeaArgs) readField1(iprot thrift.TProtocol) error {
	p.Feareq = NewFeaServerRequest()
	if err := p.Feareq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Feareq)
	}
	return nil
}

func (p *WriteFeaArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("WriteFea_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WriteFeaArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Feareq != nil {
		if err := oprot.WriteFieldBegin("feareq", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:feareq: %s", p, err)
		}
		if err := p.Feareq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Feareq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:feareq: %s", p, err)
		}
	}
	return err
}

func (p *WriteFeaArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WriteFeaArgs(%+v)", *p)
}

type WriteFeaResult struct {
	Success *FeaServerResponse `thrift:"success,0" json:"success"`
}

func NewWriteFeaResult() *WriteFeaResult {
	return &WriteFeaResult{}
}

func (p *WriteFeaResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WriteFeaResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewFeaServerResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *WriteFeaResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("WriteFea_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WriteFeaResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *WriteFeaResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WriteFeaResult(%+v)", *p)
}
