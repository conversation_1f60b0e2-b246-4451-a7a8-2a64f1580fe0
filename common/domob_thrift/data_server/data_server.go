// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package data_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/airport_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = airport_types.GoUnusedProtection__

type DataServer interface {
	// 发送文件信息
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - FileInfo: 文件信息
	SendFileInfo(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, fileInfo *airport_types.FileInfo) (r *airport_types.ResultBool, err error)
	// 发送文件数据
	// 该方法保证数据块一定是顺序发送，即blockIndex递增
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - Data: 数据信息
	SendFileData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data *airport_types.DataInfo) (r *airport_types.ResultBool, err error)
	// 发送文件结束
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	SendFileDataEnd(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error)
	// 获取新接收到的文件
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicName: topic名称
	//  - StartTime: 开始时间戳
	//  - EndTime: 结束时间戳
	GetNewFile(requestHeader *common.RequestHeader, topicName string, startTime int32, endTime int32) (r *airport_types.ResultTopicFileInfoList, err error)
	// 获取文件数据
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - PerBlockSize: 分块每块大小，规定每块不得超过1M
	//  - BlockIndex: 数据块编号，从0开始
	GetFileData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, perBlockSize int32, blockIndex int32) (r *airport_types.ResultData, err error)
	// 获取信号，仅用于airport调用
	// 该方法除了作为airport向airport_server的心跳数据包之外，
	// 还可以从airport_server获取信号，做相关的如重启操作，
	// 方便每次升级airport时批量重启所有airport
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - Hostname: hostname
	//  - ExecuteDir: 运行目录，用hostname+executeDir区分每个airport实例
	//  - StartRunningTime: 开始运行时间
	//  - TopicList: topic列表
	GetAirportSignal(requestHeader *common.RequestHeader, hostname string, executeDir string, startRunningTime int32, topicList []string) (r *airport_types.ResultSignal, err error)
	// 发送通勤记录(通勤机场)
	// 该方法发送单条记录
	//
	// Parameters:
	//  - RequestHeader: 详情见common.thrift
	//  - TopicInfo: topic信息
	//  - Data: 记录数据
	SendCommuterData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data []byte) (r *airport_types.ResultBool, err error)
}

type DataServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDataServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DataServerClient {
	return &DataServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDataServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DataServerClient {
	return &DataServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 发送文件信息
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - FileInfo: 文件信息
func (p *DataServerClient) SendFileInfo(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, fileInfo *airport_types.FileInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendSendFileInfo(requestHeader, topicInfo, fileInfo); err != nil {
		return
	}
	return p.recvSendFileInfo()
}

func (p *DataServerClient) sendSendFileInfo(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, fileInfo *airport_types.FileInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sendFileInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewSendFileInfoArgs()
	args0.RequestHeader = requestHeader
	args0.TopicInfo = topicInfo
	args0.FileInfo = fileInfo
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataServerClient) recvSendFileInfo() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewSendFileInfoResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 发送文件数据
// 该方法保证数据块一定是顺序发送，即blockIndex递增
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - Data: 数据信息
func (p *DataServerClient) SendFileData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data *airport_types.DataInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendSendFileData(requestHeader, topicInfo, data); err != nil {
		return
	}
	return p.recvSendFileData()
}

func (p *DataServerClient) sendSendFileData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data *airport_types.DataInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sendFileData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewSendFileDataArgs()
	args4.RequestHeader = requestHeader
	args4.TopicInfo = topicInfo
	args4.Data = data
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataServerClient) recvSendFileData() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewSendFileDataResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

// 发送文件结束
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
func (p *DataServerClient) SendFileDataEnd(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (r *airport_types.ResultBool, err error) {
	if err = p.sendSendFileDataEnd(requestHeader, topicInfo); err != nil {
		return
	}
	return p.recvSendFileDataEnd()
}

func (p *DataServerClient) sendSendFileDataEnd(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sendFileDataEnd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewSendFileDataEndArgs()
	args8.RequestHeader = requestHeader
	args8.TopicInfo = topicInfo
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataServerClient) recvSendFileDataEnd() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewSendFileDataEndResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	return
}

// 获取新接收到的文件
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicName: topic名称
//  - StartTime: 开始时间戳
//  - EndTime: 结束时间戳
func (p *DataServerClient) GetNewFile(requestHeader *common.RequestHeader, topicName string, startTime int32, endTime int32) (r *airport_types.ResultTopicFileInfoList, err error) {
	if err = p.sendGetNewFile(requestHeader, topicName, startTime, endTime); err != nil {
		return
	}
	return p.recvGetNewFile()
}

func (p *DataServerClient) sendGetNewFile(requestHeader *common.RequestHeader, topicName string, startTime int32, endTime int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getNewFile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetNewFileArgs()
	args12.RequestHeader = requestHeader
	args12.TopicName = topicName
	args12.StartTime = startTime
	args12.EndTime = endTime
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataServerClient) recvGetNewFile() (value *airport_types.ResultTopicFileInfoList, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetNewFileResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	return
}

// 获取文件数据
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - PerBlockSize: 分块每块大小，规定每块不得超过1M
//  - BlockIndex: 数据块编号，从0开始
func (p *DataServerClient) GetFileData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, perBlockSize int32, blockIndex int32) (r *airport_types.ResultData, err error) {
	if err = p.sendGetFileData(requestHeader, topicInfo, perBlockSize, blockIndex); err != nil {
		return
	}
	return p.recvGetFileData()
}

func (p *DataServerClient) sendGetFileData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, perBlockSize int32, blockIndex int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getFileData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetFileDataArgs()
	args16.RequestHeader = requestHeader
	args16.TopicInfo = topicInfo
	args16.PerBlockSize = perBlockSize
	args16.BlockIndex = blockIndex
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataServerClient) recvGetFileData() (value *airport_types.ResultData, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetFileDataResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	return
}

// 获取信号，仅用于airport调用
// 该方法除了作为airport向airport_server的心跳数据包之外，
// 还可以从airport_server获取信号，做相关的如重启操作，
// 方便每次升级airport时批量重启所有airport
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - Hostname: hostname
//  - ExecuteDir: 运行目录，用hostname+executeDir区分每个airport实例
//  - StartRunningTime: 开始运行时间
//  - TopicList: topic列表
func (p *DataServerClient) GetAirportSignal(requestHeader *common.RequestHeader, hostname string, executeDir string, startRunningTime int32, topicList []string) (r *airport_types.ResultSignal, err error) {
	if err = p.sendGetAirportSignal(requestHeader, hostname, executeDir, startRunningTime, topicList); err != nil {
		return
	}
	return p.recvGetAirportSignal()
}

func (p *DataServerClient) sendGetAirportSignal(requestHeader *common.RequestHeader, hostname string, executeDir string, startRunningTime int32, topicList []string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAirportSignal", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetAirportSignalArgs()
	args20.RequestHeader = requestHeader
	args20.Hostname = hostname
	args20.ExecuteDir = executeDir
	args20.StartRunningTime = startRunningTime
	args20.TopicList = topicList
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataServerClient) recvGetAirportSignal() (value *airport_types.ResultSignal, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetAirportSignalResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	return
}

// 发送通勤记录(通勤机场)
// 该方法发送单条记录
//
// Parameters:
//  - RequestHeader: 详情见common.thrift
//  - TopicInfo: topic信息
//  - Data: 记录数据
func (p *DataServerClient) SendCommuterData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data []byte) (r *airport_types.ResultBool, err error) {
	if err = p.sendSendCommuterData(requestHeader, topicInfo, data); err != nil {
		return
	}
	return p.recvSendCommuterData()
}

func (p *DataServerClient) sendSendCommuterData(requestHeader *common.RequestHeader, topicInfo *airport_types.TopicInfo, data []byte) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("sendCommuterData", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewSendCommuterDataArgs()
	args24.RequestHeader = requestHeader
	args24.TopicInfo = topicInfo
	args24.Data = data
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DataServerClient) recvSendCommuterData() (value *airport_types.ResultBool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewSendCommuterDataResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	return
}

type DataServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DataServer
}

func (p *DataServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DataServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DataServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDataServerProcessor(handler DataServer) *DataServerProcessor {

	self28 := &DataServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self28.processorMap["sendFileInfo"] = &dataServerProcessorSendFileInfo{handler: handler}
	self28.processorMap["sendFileData"] = &dataServerProcessorSendFileData{handler: handler}
	self28.processorMap["sendFileDataEnd"] = &dataServerProcessorSendFileDataEnd{handler: handler}
	self28.processorMap["getNewFile"] = &dataServerProcessorGetNewFile{handler: handler}
	self28.processorMap["getFileData"] = &dataServerProcessorGetFileData{handler: handler}
	self28.processorMap["getAirportSignal"] = &dataServerProcessorGetAirportSignal{handler: handler}
	self28.processorMap["sendCommuterData"] = &dataServerProcessorSendCommuterData{handler: handler}
	return self28
}

func (p *DataServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x29 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x29.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x29

}

type dataServerProcessorSendFileInfo struct {
	handler DataServer
}

func (p *dataServerProcessorSendFileInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSendFileInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sendFileInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSendFileInfoResult()
	if result.Success, err = p.handler.SendFileInfo(args.RequestHeader, args.TopicInfo, args.FileInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendFileInfo: "+err.Error())
		oprot.WriteMessageBegin("sendFileInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sendFileInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataServerProcessorSendFileData struct {
	handler DataServer
}

func (p *dataServerProcessorSendFileData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSendFileDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sendFileData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSendFileDataResult()
	if result.Success, err = p.handler.SendFileData(args.RequestHeader, args.TopicInfo, args.Data); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendFileData: "+err.Error())
		oprot.WriteMessageBegin("sendFileData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sendFileData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataServerProcessorSendFileDataEnd struct {
	handler DataServer
}

func (p *dataServerProcessorSendFileDataEnd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSendFileDataEndArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sendFileDataEnd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSendFileDataEndResult()
	if result.Success, err = p.handler.SendFileDataEnd(args.RequestHeader, args.TopicInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendFileDataEnd: "+err.Error())
		oprot.WriteMessageBegin("sendFileDataEnd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sendFileDataEnd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataServerProcessorGetNewFile struct {
	handler DataServer
}

func (p *dataServerProcessorGetNewFile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetNewFileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getNewFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetNewFileResult()
	if result.Success, err = p.handler.GetNewFile(args.RequestHeader, args.TopicName, args.StartTime, args.EndTime); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getNewFile: "+err.Error())
		oprot.WriteMessageBegin("getNewFile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getNewFile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataServerProcessorGetFileData struct {
	handler DataServer
}

func (p *dataServerProcessorGetFileData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetFileDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getFileData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetFileDataResult()
	if result.Success, err = p.handler.GetFileData(args.RequestHeader, args.TopicInfo, args.PerBlockSize, args.BlockIndex); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getFileData: "+err.Error())
		oprot.WriteMessageBegin("getFileData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getFileData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataServerProcessorGetAirportSignal struct {
	handler DataServer
}

func (p *dataServerProcessorGetAirportSignal) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAirportSignalArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAirportSignal", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAirportSignalResult()
	if result.Success, err = p.handler.GetAirportSignal(args.RequestHeader, args.Hostname, args.ExecuteDir, args.StartRunningTime, args.TopicList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAirportSignal: "+err.Error())
		oprot.WriteMessageBegin("getAirportSignal", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAirportSignal", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dataServerProcessorSendCommuterData struct {
	handler DataServer
}

func (p *dataServerProcessorSendCommuterData) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSendCommuterDataArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("sendCommuterData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSendCommuterDataResult()
	if result.Success, err = p.handler.SendCommuterData(args.RequestHeader, args.TopicInfo, args.Data); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing sendCommuterData: "+err.Error())
		oprot.WriteMessageBegin("sendCommuterData", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("sendCommuterData", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type SendFileInfoArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
	FileInfo      *airport_types.FileInfo  `thrift:"fileInfo,3" json:"fileInfo"`
}

func NewSendFileInfoArgs() *SendFileInfoArgs {
	return &SendFileInfoArgs{}
}

func (p *SendFileInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendFileInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *SendFileInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *SendFileInfoArgs) readField3(iprot thrift.TProtocol) error {
	p.FileInfo = airport_types.NewFileInfo()
	if err := p.FileInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.FileInfo)
	}
	return nil
}

func (p *SendFileInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendFileInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendFileInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *SendFileInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *SendFileInfoArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.FileInfo != nil {
		if err := oprot.WriteFieldBegin("fileInfo", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:fileInfo: %s", p, err)
		}
		if err := p.FileInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.FileInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:fileInfo: %s", p, err)
		}
	}
	return err
}

func (p *SendFileInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendFileInfoArgs(%+v)", *p)
}

type SendFileInfoResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewSendFileInfoResult() *SendFileInfoResult {
	return &SendFileInfoResult{}
}

func (p *SendFileInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendFileInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SendFileInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendFileInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendFileInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SendFileInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendFileInfoResult(%+v)", *p)
}

type SendFileDataArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
	Data          *airport_types.DataInfo  `thrift:"data,3" json:"data"`
}

func NewSendFileDataArgs() *SendFileDataArgs {
	return &SendFileDataArgs{}
}

func (p *SendFileDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *SendFileDataArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *SendFileDataArgs) readField3(iprot thrift.TProtocol) error {
	p.Data = airport_types.NewDataInfo()
	if err := p.Data.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Data)
	}
	return nil
}

func (p *SendFileDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendFileData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *SendFileDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *SendFileDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:data: %s", p, err)
		}
		if err := p.Data.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Data)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:data: %s", p, err)
		}
	}
	return err
}

func (p *SendFileDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendFileDataArgs(%+v)", *p)
}

type SendFileDataResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewSendFileDataResult() *SendFileDataResult {
	return &SendFileDataResult{}
}

func (p *SendFileDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SendFileDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendFileData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SendFileDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendFileDataResult(%+v)", *p)
}

type SendFileDataEndArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
}

func NewSendFileDataEndArgs() *SendFileDataEndArgs {
	return &SendFileDataEndArgs{}
}

func (p *SendFileDataEndArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataEndArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *SendFileDataEndArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *SendFileDataEndArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendFileDataEnd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataEndArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *SendFileDataEndArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *SendFileDataEndArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendFileDataEndArgs(%+v)", *p)
}

type SendFileDataEndResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewSendFileDataEndResult() *SendFileDataEndResult {
	return &SendFileDataEndResult{}
}

func (p *SendFileDataEndResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataEndResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SendFileDataEndResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendFileDataEnd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendFileDataEndResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SendFileDataEndResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendFileDataEndResult(%+v)", *p)
}

type GetNewFileArgs struct {
	RequestHeader *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	TopicName     string                `thrift:"topicName,2" json:"topicName"`
	StartTime     int32                 `thrift:"startTime,3" json:"startTime"`
	EndTime       int32                 `thrift:"endTime,4" json:"endTime"`
}

func NewGetNewFileArgs() *GetNewFileArgs {
	return &GetNewFileArgs{}
}

func (p *GetNewFileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNewFileArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetNewFileArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TopicName = v
	}
	return nil
}

func (p *GetNewFileArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *GetNewFileArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *GetNewFileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNewFile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNewFileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetNewFileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicName", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:topicName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TopicName)); err != nil {
		return fmt.Errorf("%T.topicName (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:topicName: %s", p, err)
	}
	return err
}

func (p *GetNewFileArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:startTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:startTime: %s", p, err)
	}
	return err
}

func (p *GetNewFileArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:endTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:endTime: %s", p, err)
	}
	return err
}

func (p *GetNewFileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNewFileArgs(%+v)", *p)
}

type GetNewFileResult struct {
	Success *airport_types.ResultTopicFileInfoList `thrift:"success,0" json:"success"`
}

func NewGetNewFileResult() *GetNewFileResult {
	return &GetNewFileResult{}
}

func (p *GetNewFileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetNewFileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultTopicFileInfoList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetNewFileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getNewFile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetNewFileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetNewFileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetNewFileResult(%+v)", *p)
}

type GetFileDataArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
	PerBlockSize  int32                    `thrift:"perBlockSize,3" json:"perBlockSize"`
	BlockIndex    int32                    `thrift:"blockIndex,4" json:"blockIndex"`
}

func NewGetFileDataArgs() *GetFileDataArgs {
	return &GetFileDataArgs{}
}

func (p *GetFileDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetFileDataArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *GetFileDataArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PerBlockSize = v
	}
	return nil
}

func (p *GetFileDataArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.BlockIndex = v
	}
	return nil
}

func (p *GetFileDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetFileDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *GetFileDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("perBlockSize", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:perBlockSize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PerBlockSize)); err != nil {
		return fmt.Errorf("%T.perBlockSize (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:perBlockSize: %s", p, err)
	}
	return err
}

func (p *GetFileDataArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("blockIndex", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:blockIndex: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BlockIndex)); err != nil {
		return fmt.Errorf("%T.blockIndex (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:blockIndex: %s", p, err)
	}
	return err
}

func (p *GetFileDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileDataArgs(%+v)", *p)
}

type GetFileDataResult struct {
	Success *airport_types.ResultData `thrift:"success,0" json:"success"`
}

func NewGetFileDataResult() *GetFileDataResult {
	return &GetFileDataResult{}
}

func (p *GetFileDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetFileDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultData()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetFileDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getFileData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetFileDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetFileDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetFileDataResult(%+v)", *p)
}

type GetAirportSignalArgs struct {
	RequestHeader    *common.RequestHeader `thrift:"requestHeader,1" json:"requestHeader"`
	Hostname         string                `thrift:"hostname,2" json:"hostname"`
	ExecuteDir       string                `thrift:"executeDir,3" json:"executeDir"`
	StartRunningTime int32                 `thrift:"startRunningTime,4" json:"startRunningTime"`
	TopicList        []string              `thrift:"topicList,5" json:"topicList"`
}

func NewGetAirportSignalArgs() *GetAirportSignalArgs {
	return &GetAirportSignalArgs{}
}

func (p *GetAirportSignalArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAirportSignalArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *GetAirportSignalArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Hostname = v
	}
	return nil
}

func (p *GetAirportSignalArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExecuteDir = v
	}
	return nil
}

func (p *GetAirportSignalArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartRunningTime = v
	}
	return nil
}

func (p *GetAirportSignalArgs) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TopicList = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem30 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem30 = v
		}
		p.TopicList = append(p.TopicList, _elem30)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAirportSignalArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAirportSignal_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAirportSignalArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *GetAirportSignalArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostname", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:hostname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostname)); err != nil {
		return fmt.Errorf("%T.hostname (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:hostname: %s", p, err)
	}
	return err
}

func (p *GetAirportSignalArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("executeDir", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:executeDir: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExecuteDir)); err != nil {
		return fmt.Errorf("%T.executeDir (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:executeDir: %s", p, err)
	}
	return err
}

func (p *GetAirportSignalArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startRunningTime", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:startRunningTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartRunningTime)); err != nil {
		return fmt.Errorf("%T.startRunningTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:startRunningTime: %s", p, err)
	}
	return err
}

func (p *GetAirportSignalArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.TopicList != nil {
		if err := oprot.WriteFieldBegin("topicList", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:topicList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TopicList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TopicList {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:topicList: %s", p, err)
		}
	}
	return err
}

func (p *GetAirportSignalArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAirportSignalArgs(%+v)", *p)
}

type GetAirportSignalResult struct {
	Success *airport_types.ResultSignal `thrift:"success,0" json:"success"`
}

func NewGetAirportSignalResult() *GetAirportSignalResult {
	return &GetAirportSignalResult{}
}

func (p *GetAirportSignalResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAirportSignalResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultSignal()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAirportSignalResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAirportSignal_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAirportSignalResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAirportSignalResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAirportSignalResult(%+v)", *p)
}

type SendCommuterDataArgs struct {
	RequestHeader *common.RequestHeader    `thrift:"requestHeader,1" json:"requestHeader"`
	TopicInfo     *airport_types.TopicInfo `thrift:"topicInfo,2" json:"topicInfo"`
	Data          []byte                   `thrift:"data,3" json:"data"`
}

func NewSendCommuterDataArgs() *SendCommuterDataArgs {
	return &SendCommuterDataArgs{}
}

func (p *SendCommuterDataArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataArgs) readField1(iprot thrift.TProtocol) error {
	p.RequestHeader = common.NewRequestHeader()
	if err := p.RequestHeader.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestHeader)
	}
	return nil
}

func (p *SendCommuterDataArgs) readField2(iprot thrift.TProtocol) error {
	p.TopicInfo = airport_types.NewTopicInfo()
	if err := p.TopicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TopicInfo)
	}
	return nil
}

func (p *SendCommuterDataArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Data = v
	}
	return nil
}

func (p *SendCommuterDataArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendCommuterData_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.RequestHeader != nil {
		if err := oprot.WriteFieldBegin("requestHeader", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:requestHeader: %s", p, err)
		}
		if err := p.RequestHeader.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestHeader)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:requestHeader: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TopicInfo != nil {
		if err := oprot.WriteFieldBegin("topicInfo", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:topicInfo: %s", p, err)
		}
		if err := p.TopicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TopicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:topicInfo: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Data != nil {
		if err := oprot.WriteFieldBegin("data", thrift.STRING, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:data: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Data); err != nil {
			return fmt.Errorf("%T.data (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:data: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendCommuterDataArgs(%+v)", *p)
}

type SendCommuterDataResult struct {
	Success *airport_types.ResultBool `thrift:"success,0" json:"success"`
}

func NewSendCommuterDataResult() *SendCommuterDataResult {
	return &SendCommuterDataResult{}
}

func (p *SendCommuterDataResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataResult) readField0(iprot thrift.TProtocol) error {
	p.Success = airport_types.NewResultBool()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SendCommuterDataResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("sendCommuterData_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SendCommuterDataResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SendCommuterDataResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SendCommuterDataResult(%+v)", *p)
}
