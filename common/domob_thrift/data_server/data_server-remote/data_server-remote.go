// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"data_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  ResultBool sendFileInfo(RequestHeader requestHeader, TopicInfo topicInfo, FileInfo fileInfo)")
	fmt.Fprintln(os.Stderr, "  ResultBool sendFileData(RequestHeader requestHeader, TopicInfo topicInfo, DataInfo data)")
	fmt.Fprintln(os.Stderr, "  ResultBool sendFileDataEnd(RequestHeader requestHeader, TopicInfo topicInfo)")
	fmt.Fprintln(os.Stderr, "  ResultTopicFileInfoList getNewFile(RequestHeader requestHeader, string topicName, i32 startTime, i32 endTime)")
	fmt.Fprintln(os.Stderr, "  ResultData getFileData(RequestHeader requestHeader, TopicInfo topicInfo, i32 perBlockSize, i32 blockIndex)")
	fmt.Fprintln(os.Stderr, "  ResultSignal getAirportSignal(RequestHeader requestHeader, string hostname, string executeDir, i32 startRunningTime,  topicList)")
	fmt.Fprintln(os.Stderr, "  ResultBool sendCommuterData(RequestHeader requestHeader, TopicInfo topicInfo, string data)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := data_server.NewDataServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "sendFileInfo":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SendFileInfo requires 3 args")
			flag.Usage()
		}
		arg31 := flag.Arg(1)
		mbTrans32 := thrift.NewTMemoryBufferLen(len(arg31))
		defer mbTrans32.Close()
		_, err33 := mbTrans32.WriteString(arg31)
		if err33 != nil {
			Usage()
			return
		}
		factory34 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt35 := factory34.GetProtocol(mbTrans32)
		argvalue0 := data_server.NewRequestHeader()
		err36 := argvalue0.Read(jsProt35)
		if err36 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg37 := flag.Arg(2)
		mbTrans38 := thrift.NewTMemoryBufferLen(len(arg37))
		defer mbTrans38.Close()
		_, err39 := mbTrans38.WriteString(arg37)
		if err39 != nil {
			Usage()
			return
		}
		factory40 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt41 := factory40.GetProtocol(mbTrans38)
		argvalue1 := data_server.NewTopicInfo()
		err42 := argvalue1.Read(jsProt41)
		if err42 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg43 := flag.Arg(3)
		mbTrans44 := thrift.NewTMemoryBufferLen(len(arg43))
		defer mbTrans44.Close()
		_, err45 := mbTrans44.WriteString(arg43)
		if err45 != nil {
			Usage()
			return
		}
		factory46 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt47 := factory46.GetProtocol(mbTrans44)
		argvalue2 := data_server.NewFileInfo()
		err48 := argvalue2.Read(jsProt47)
		if err48 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.SendFileInfo(value0, value1, value2))
		fmt.Print("\n")
		break
	case "sendFileData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SendFileData requires 3 args")
			flag.Usage()
		}
		arg49 := flag.Arg(1)
		mbTrans50 := thrift.NewTMemoryBufferLen(len(arg49))
		defer mbTrans50.Close()
		_, err51 := mbTrans50.WriteString(arg49)
		if err51 != nil {
			Usage()
			return
		}
		factory52 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt53 := factory52.GetProtocol(mbTrans50)
		argvalue0 := data_server.NewRequestHeader()
		err54 := argvalue0.Read(jsProt53)
		if err54 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg55 := flag.Arg(2)
		mbTrans56 := thrift.NewTMemoryBufferLen(len(arg55))
		defer mbTrans56.Close()
		_, err57 := mbTrans56.WriteString(arg55)
		if err57 != nil {
			Usage()
			return
		}
		factory58 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt59 := factory58.GetProtocol(mbTrans56)
		argvalue1 := data_server.NewTopicInfo()
		err60 := argvalue1.Read(jsProt59)
		if err60 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg61 := flag.Arg(3)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue2 := data_server.NewDataInfo()
		err66 := argvalue2.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.SendFileData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "sendFileDataEnd":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SendFileDataEnd requires 2 args")
			flag.Usage()
		}
		arg67 := flag.Arg(1)
		mbTrans68 := thrift.NewTMemoryBufferLen(len(arg67))
		defer mbTrans68.Close()
		_, err69 := mbTrans68.WriteString(arg67)
		if err69 != nil {
			Usage()
			return
		}
		factory70 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt71 := factory70.GetProtocol(mbTrans68)
		argvalue0 := data_server.NewRequestHeader()
		err72 := argvalue0.Read(jsProt71)
		if err72 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg73 := flag.Arg(2)
		mbTrans74 := thrift.NewTMemoryBufferLen(len(arg73))
		defer mbTrans74.Close()
		_, err75 := mbTrans74.WriteString(arg73)
		if err75 != nil {
			Usage()
			return
		}
		factory76 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt77 := factory76.GetProtocol(mbTrans74)
		argvalue1 := data_server.NewTopicInfo()
		err78 := argvalue1.Read(jsProt77)
		if err78 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SendFileDataEnd(value0, value1))
		fmt.Print("\n")
		break
	case "getNewFile":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetNewFile requires 4 args")
			flag.Usage()
		}
		arg79 := flag.Arg(1)
		mbTrans80 := thrift.NewTMemoryBufferLen(len(arg79))
		defer mbTrans80.Close()
		_, err81 := mbTrans80.WriteString(arg79)
		if err81 != nil {
			Usage()
			return
		}
		factory82 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt83 := factory82.GetProtocol(mbTrans80)
		argvalue0 := data_server.NewRequestHeader()
		err84 := argvalue0.Read(jsProt83)
		if err84 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err86 := (strconv.Atoi(flag.Arg(3)))
		if err86 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err87 := (strconv.Atoi(flag.Arg(4)))
		if err87 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.GetNewFile(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getFileData":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "GetFileData requires 4 args")
			flag.Usage()
		}
		arg88 := flag.Arg(1)
		mbTrans89 := thrift.NewTMemoryBufferLen(len(arg88))
		defer mbTrans89.Close()
		_, err90 := mbTrans89.WriteString(arg88)
		if err90 != nil {
			Usage()
			return
		}
		factory91 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt92 := factory91.GetProtocol(mbTrans89)
		argvalue0 := data_server.NewRequestHeader()
		err93 := argvalue0.Read(jsProt92)
		if err93 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg94 := flag.Arg(2)
		mbTrans95 := thrift.NewTMemoryBufferLen(len(arg94))
		defer mbTrans95.Close()
		_, err96 := mbTrans95.WriteString(arg94)
		if err96 != nil {
			Usage()
			return
		}
		factory97 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt98 := factory97.GetProtocol(mbTrans95)
		argvalue1 := data_server.NewTopicInfo()
		err99 := argvalue1.Read(jsProt98)
		if err99 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err100 := (strconv.Atoi(flag.Arg(3)))
		if err100 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err101 := (strconv.Atoi(flag.Arg(4)))
		if err101 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.GetFileData(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAirportSignal":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetAirportSignal requires 5 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := data_server.NewRequestHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		tmp3, err110 := (strconv.Atoi(flag.Arg(4)))
		if err110 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg111 := flag.Arg(5)
		mbTrans112 := thrift.NewTMemoryBufferLen(len(arg111))
		defer mbTrans112.Close()
		_, err113 := mbTrans112.WriteString(arg111)
		if err113 != nil {
			Usage()
			return
		}
		factory114 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt115 := factory114.GetProtocol(mbTrans112)
		containerStruct4 := data_server.NewGetAirportSignalArgs()
		err116 := containerStruct4.ReadField5(jsProt115)
		if err116 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.TopicList
		value4 := argvalue4
		fmt.Print(client.GetAirportSignal(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "sendCommuterData":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SendCommuterData requires 3 args")
			flag.Usage()
		}
		arg117 := flag.Arg(1)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		argvalue0 := data_server.NewRequestHeader()
		err122 := argvalue0.Read(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg123 := flag.Arg(2)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue1 := data_server.NewTopicInfo()
		err128 := argvalue1.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.SendCommuterData(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
