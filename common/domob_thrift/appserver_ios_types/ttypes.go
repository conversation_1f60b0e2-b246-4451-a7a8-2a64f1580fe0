// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appserver_ios_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appserver_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = appserver_types.GoUnusedProtection__
var GoUnusedProtection__ int

type IosCategory int64

const (
	IosCategory_All       IosCategory = 0
	IosCategory_RELAX     IosCategory = 1
	IosCategory_ACTION    IosCategory = 2
	IosCategory_ONLINE    IosCategory = 3
	IosCategory_RPG       IosCategory = 4
	IosCategory_FLY_SHOOT IosCategory = 5
	IosCategory_RACE      IosCategory = 6
	IosCategory_CHESS     IosCategory = 7
	IosCategory_SPORTS    IosCategory = 8
	IosCategory_STRATEGY  IosCategory = 9
	IosCategory_PUZ       IosCategory = 10
	IosCategory_FIGHT     IosCategory = 11
	IosCategory_RISK      IosCategory = 12
	IosCategory_SIMULATE  IosCategory = 13
	IosCategory_CHILD     IosCategory = 14
	IosCategory_OTHER     IosCategory = 15
)

func (p IosCategory) String() string {
	switch p {
	case IosCategory_All:
		return "IosCategory_All"
	case IosCategory_RELAX:
		return "IosCategory_RELAX"
	case IosCategory_ACTION:
		return "IosCategory_ACTION"
	case IosCategory_ONLINE:
		return "IosCategory_ONLINE"
	case IosCategory_RPG:
		return "IosCategory_RPG"
	case IosCategory_FLY_SHOOT:
		return "IosCategory_FLY_SHOOT"
	case IosCategory_RACE:
		return "IosCategory_RACE"
	case IosCategory_CHESS:
		return "IosCategory_CHESS"
	case IosCategory_SPORTS:
		return "IosCategory_SPORTS"
	case IosCategory_STRATEGY:
		return "IosCategory_STRATEGY"
	case IosCategory_PUZ:
		return "IosCategory_PUZ"
	case IosCategory_FIGHT:
		return "IosCategory_FIGHT"
	case IosCategory_RISK:
		return "IosCategory_RISK"
	case IosCategory_SIMULATE:
		return "IosCategory_SIMULATE"
	case IosCategory_CHILD:
		return "IosCategory_CHILD"
	case IosCategory_OTHER:
		return "IosCategory_OTHER"
	}
	return "<UNSET>"
}

func IosCategoryFromString(s string) (IosCategory, error) {
	switch s {
	case "IosCategory_All":
		return IosCategory_All, nil
	case "IosCategory_RELAX":
		return IosCategory_RELAX, nil
	case "IosCategory_ACTION":
		return IosCategory_ACTION, nil
	case "IosCategory_ONLINE":
		return IosCategory_ONLINE, nil
	case "IosCategory_RPG":
		return IosCategory_RPG, nil
	case "IosCategory_FLY_SHOOT":
		return IosCategory_FLY_SHOOT, nil
	case "IosCategory_RACE":
		return IosCategory_RACE, nil
	case "IosCategory_CHESS":
		return IosCategory_CHESS, nil
	case "IosCategory_SPORTS":
		return IosCategory_SPORTS, nil
	case "IosCategory_STRATEGY":
		return IosCategory_STRATEGY, nil
	case "IosCategory_PUZ":
		return IosCategory_PUZ, nil
	case "IosCategory_FIGHT":
		return IosCategory_FIGHT, nil
	case "IosCategory_RISK":
		return IosCategory_RISK, nil
	case "IosCategory_SIMULATE":
		return IosCategory_SIMULATE, nil
	case "IosCategory_CHILD":
		return IosCategory_CHILD, nil
	case "IosCategory_OTHER":
		return IosCategory_OTHER, nil
	}
	return IosCategory(math.MinInt32 - 1), fmt.Errorf("not a valid IosCategory string")
}

type DeviceCode common.DeviceCode

type OSCode common.OSCode

type AccessTypeCode common.AccessTypeCode

type CarrierCode common.CarrierCode

type AdCategory common.AdCategory

type AppWallProt map[string]string

type IosDasGame struct {
	Id          int32    `thrift:"id,1" json:"id"`
	Name        string   `thrift:"name,2" json:"name"`
	Cat         string   `thrift:"cat,3" json:"cat"`
	Price       string   `thrift:"price,4" json:"price"`
	Score       string   `thrift:"score,5" json:"score"`
	IconUrl     string   `thrift:"icon_url,6" json:"icon_url"`
	DownloadUrl string   `thrift:"download_url,7" json:"download_url"`
	Developer   string   `thrift:"developer,8" json:"developer"`
	UpdateTime  int32    `thrift:"update_time,9" json:"update_time"`
	Version     string   `thrift:"version,10" json:"version"`
	SnapshotUrl []string `thrift:"snapshot_url,11" json:"snapshot_url"`
	Intro       string   `thrift:"intro,12" json:"intro"`
	AppSize     string   `thrift:"app_size,13" json:"app_size"`
	FreeDate    int32    `thrift:"free_date,14" json:"free_date"`
	// unused field # 15
	Downloads    int32  `thrift:"downloads,16" json:"downloads"`
	Appid        string `thrift:"appid,17" json:"appid"`
	DownloadType int32  `thrift:"download_type,18" json:"download_type"`
	VideoUrl     string `thrift:"video_url,19" json:"video_url"`
	AdUserId     int32  `thrift:"ad_user_id,20" json:"ad_user_id"`
}

func NewIosDasGame() *IosDasGame {
	return &IosDasGame{}
}

func (p *IosDasGame) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosDasGame) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *IosDasGame) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *IosDasGame) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cat = v
	}
	return nil
}

func (p *IosDasGame) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *IosDasGame) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Score = v
	}
	return nil
}

func (p *IosDasGame) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IconUrl = v
	}
	return nil
}

func (p *IosDasGame) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.DownloadUrl = v
	}
	return nil
}

func (p *IosDasGame) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Developer = v
	}
	return nil
}

func (p *IosDasGame) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UpdateTime = v
	}
	return nil
}

func (p *IosDasGame) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *IosDasGame) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SnapshotUrl = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.SnapshotUrl = append(p.SnapshotUrl, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IosDasGame) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Intro = v
	}
	return nil
}

func (p *IosDasGame) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.AppSize = v
	}
	return nil
}

func (p *IosDasGame) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.FreeDate = v
	}
	return nil
}

func (p *IosDasGame) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Downloads = v
	}
	return nil
}

func (p *IosDasGame) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *IosDasGame) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.DownloadType = v
	}
	return nil
}

func (p *IosDasGame) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.VideoUrl = v
	}
	return nil
}

func (p *IosDasGame) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.AdUserId = v
	}
	return nil
}

func (p *IosDasGame) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosDasGame"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosDasGame) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cat", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cat: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cat)); err != nil {
		return fmt.Errorf("%T.cat (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cat: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:price: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Price)); err != nil {
		return fmt.Errorf("%T.price (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:price: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("score", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:score: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Score)); err != nil {
		return fmt.Errorf("%T.score (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:score: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon_url", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:icon_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IconUrl)); err != nil {
		return fmt.Errorf("%T.icon_url (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:icon_url: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_url", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:download_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DownloadUrl)); err != nil {
		return fmt.Errorf("%T.download_url (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:download_url: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("developer", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:developer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Developer)); err != nil {
		return fmt.Errorf("%T.developer (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:developer: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update_time", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:update_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.update_time (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:update_time: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:version: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField11(oprot thrift.TProtocol) (err error) {
	if p.SnapshotUrl != nil {
		if err := oprot.WriteFieldBegin("snapshot_url", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:snapshot_url: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SnapshotUrl)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SnapshotUrl {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:snapshot_url: %s", p, err)
		}
	}
	return err
}

func (p *IosDasGame) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("intro", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:intro: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Intro)); err != nil {
		return fmt.Errorf("%T.intro (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:intro: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_size", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:app_size: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppSize)); err != nil {
		return fmt.Errorf("%T.app_size (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:app_size: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("free_date", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:free_date: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FreeDate)); err != nil {
		return fmt.Errorf("%T.free_date (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:free_date: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("downloads", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:downloads: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Downloads)); err != nil {
		return fmt.Errorf("%T.downloads (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:downloads: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:appid: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_type", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:download_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DownloadType)); err != nil {
		return fmt.Errorf("%T.download_type (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:download_type: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_url", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:video_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoUrl)); err != nil {
		return fmt.Errorf("%T.video_url (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:video_url: %s", p, err)
	}
	return err
}

func (p *IosDasGame) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_user_id", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:ad_user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUserId)); err != nil {
		return fmt.Errorf("%T.ad_user_id (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:ad_user_id: %s", p, err)
	}
	return err
}

func (p *IosDasGame) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosDasGame(%+v)", *p)
}

type IosWallGame struct {
	Id          int32    `thrift:"id,1" json:"id"`
	Name        string   `thrift:"name,2" json:"name"`
	Title       string   `thrift:"title,3" json:"title"`
	TypeA1      int32    `thrift:"type,4" json:"type"`
	Logo        string   `thrift:"logo,5" json:"logo"`
	Banner      string   `thrift:"banner,6" json:"banner"`
	Thumbnail   string   `thrift:"thumbnail,7" json:"thumbnail"`
	Text        string   `thrift:"text,8" json:"text"`
	Provider    string   `thrift:"provider,9" json:"provider"`
	Screenshot  []string `thrift:"screenshot,10" json:"screenshot"`
	Identifier  string   `thrift:"identifier,11" json:"identifier"`
	Vc          int32    `thrift:"vc,12" json:"vc"`
	Vn          string   `thrift:"vn,13" json:"vn"`
	ActionUrl   string   `thrift:"action_url,14" json:"action_url"`
	Size        int32    `thrift:"size,15" json:"size"`
	ReleaseTime int64    `thrift:"release_time,16" json:"release_time"`
	ActionType  int32    `thrift:"action_type,17" json:"action_type"`
	Tr          string   `thrift:"tr,18" json:"tr"`
	// unused field # 19
	ClickTracker string     `thrift:"click_tracker,20" json:"click_tracker"`
	AdCategory   AdCategory `thrift:"ad_category,21" json:"ad_category"`
	Desc         string     `thrift:"desc,22" json:"desc"`
	AdTag        []int32    `thrift:"ad_tag,23" json:"ad_tag"`
	Ex           string     `thrift:"ex,24" json:"ex"`
	Sid          string     `thrift:"sid,25" json:"sid"`
	Rating       int32      `thrift:"rating,26" json:"rating"`
	Price        string     `thrift:"price,27" json:"price"`
	Currency     string     `thrift:"currency,28" json:"currency"`
	UpdateTime   int32      `thrift:"update_time,29" json:"update_time"`
	VideoUrl     string     `thrift:"video_url,30" json:"video_url"`
	FreeDate     int32      `thrift:"free_date,31" json:"free_date"`
}

func NewIosWallGame() *IosWallGame {
	return &IosWallGame{
		AdCategory: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IosWallGame) IsSetAdCategory() bool {
	return int64(p.AdCategory) != math.MinInt32-1
}

func (p *IosWallGame) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.LIST {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosWallGame) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *IosWallGame) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *IosWallGame) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *IosWallGame) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *IosWallGame) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *IosWallGame) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Banner = v
	}
	return nil
}

func (p *IosWallGame) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Thumbnail = v
	}
	return nil
}

func (p *IosWallGame) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *IosWallGame) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Provider = v
	}
	return nil
}

func (p *IosWallGame) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Screenshot = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.Screenshot = append(p.Screenshot, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IosWallGame) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Identifier = v
	}
	return nil
}

func (p *IosWallGame) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Vc = v
	}
	return nil
}

func (p *IosWallGame) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Vn = v
	}
	return nil
}

func (p *IosWallGame) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *IosWallGame) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *IosWallGame) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ReleaseTime = v
	}
	return nil
}

func (p *IosWallGame) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ActionType = v
	}
	return nil
}

func (p *IosWallGame) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Tr = v
	}
	return nil
}

func (p *IosWallGame) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ClickTracker = v
	}
	return nil
}

func (p *IosWallGame) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.AdCategory = AdCategory(v)
	}
	return nil
}

func (p *IosWallGame) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *IosWallGame) readField23(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdTag = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.AdTag = append(p.AdTag, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IosWallGame) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Ex = v
	}
	return nil
}

func (p *IosWallGame) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *IosWallGame) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Rating = v
	}
	return nil
}

func (p *IosWallGame) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *IosWallGame) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Currency = v
	}
	return nil
}

func (p *IosWallGame) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.UpdateTime = v
	}
	return nil
}

func (p *IosWallGame) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.VideoUrl = v
	}
	return nil
}

func (p *IosWallGame) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.FreeDate = v
	}
	return nil
}

func (p *IosWallGame) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosWallGame"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosWallGame) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:title: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:type: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:logo: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("banner", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:banner: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Banner)); err != nil {
		return fmt.Errorf("%T.banner (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:banner: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thumbnail", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:thumbnail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Thumbnail)); err != nil {
		return fmt.Errorf("%T.thumbnail (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:thumbnail: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:text: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("provider", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:provider: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Provider)); err != nil {
		return fmt.Errorf("%T.provider (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:provider: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Screenshot != nil {
		if err := oprot.WriteFieldBegin("screenshot", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:screenshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Screenshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Screenshot {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:screenshot: %s", p, err)
		}
	}
	return err
}

func (p *IosWallGame) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("identifier", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:identifier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Identifier)); err != nil {
		return fmt.Errorf("%T.identifier (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:identifier: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vc", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:vc: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Vc)); err != nil {
		return fmt.Errorf("%T.vc (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:vc: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vn", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:vn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Vn)); err != nil {
		return fmt.Errorf("%T.vn (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:vn: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_url", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:action_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.action_url (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:action_url: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:size: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("release_time", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:release_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReleaseTime)); err != nil {
		return fmt.Errorf("%T.release_time (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:release_time: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_type", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:action_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return fmt.Errorf("%T.action_type (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:action_type: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tr", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:tr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tr)); err != nil {
		return fmt.Errorf("%T.tr (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:tr: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_tracker", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:click_tracker: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickTracker)); err != nil {
		return fmt.Errorf("%T.click_tracker (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:click_tracker: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_category", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:ad_category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdCategory)); err != nil {
		return fmt.Errorf("%T.ad_category (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:ad_category: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:desc: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField23(oprot thrift.TProtocol) (err error) {
	if p.AdTag != nil {
		if err := oprot.WriteFieldBegin("ad_tag", thrift.LIST, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:ad_tag: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AdTag)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdTag {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:ad_tag: %s", p, err)
		}
	}
	return err
}

func (p *IosWallGame) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ex", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:ex: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ex)); err != nil {
		return fmt.Errorf("%T.ex (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:ex: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:sid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:sid: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rating", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:rating: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rating)); err != nil {
		return fmt.Errorf("%T.rating (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:rating: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:price: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Price)); err != nil {
		return fmt.Errorf("%T.price (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:price: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currency", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:currency: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Currency)); err != nil {
		return fmt.Errorf("%T.currency (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:currency: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update_time", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:update_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.update_time (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:update_time: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_url", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:video_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoUrl)); err != nil {
		return fmt.Errorf("%T.video_url (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:video_url: %s", p, err)
	}
	return err
}

func (p *IosWallGame) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("free_date", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:free_date: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FreeDate)); err != nil {
		return fmt.Errorf("%T.free_date (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:free_date: %s", p, err)
	}
	return err
}

func (p *IosWallGame) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosWallGame(%+v)", *p)
}

type IosGame struct {
	DasGame  *IosDasGame  `thrift:"das_game,1" json:"das_game"`
	WallGame *IosWallGame `thrift:"wall_game,2" json:"wall_game"`
}

func NewIosGame() *IosGame {
	return &IosGame{}
}

func (p *IosGame) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosGame) readField1(iprot thrift.TProtocol) error {
	p.DasGame = NewIosDasGame()
	if err := p.DasGame.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DasGame)
	}
	return nil
}

func (p *IosGame) readField2(iprot thrift.TProtocol) error {
	p.WallGame = NewIosWallGame()
	if err := p.WallGame.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.WallGame)
	}
	return nil
}

func (p *IosGame) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosGame"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosGame) writeField1(oprot thrift.TProtocol) (err error) {
	if p.DasGame != nil {
		if err := oprot.WriteFieldBegin("das_game", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:das_game: %s", p, err)
		}
		if err := p.DasGame.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DasGame)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:das_game: %s", p, err)
		}
	}
	return err
}

func (p *IosGame) writeField2(oprot thrift.TProtocol) (err error) {
	if p.WallGame != nil {
		if err := oprot.WriteFieldBegin("wall_game", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:wall_game: %s", p, err)
		}
		if err := p.WallGame.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.WallGame)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:wall_game: %s", p, err)
		}
	}
	return err
}

func (p *IosGame) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosGame(%+v)", *p)
}

type IosGift struct {
	Id      int32  `thrift:"id,1" json:"id"`
	Name    string `thrift:"name,2" json:"name"`
	Scope   string `thrift:"scope,3" json:"scope"`
	Cat     string `thrift:"cat,4" json:"cat"`
	Bedate  int32  `thrift:"bedate,5" json:"bedate"`
	Eddate  int32  `thrift:"eddate,6" json:"eddate"`
	Content string `thrift:"content,7" json:"content"`
	Intro   string `thrift:"intro,8" json:"intro"`
	Tip     string `thrift:"tip,9" json:"tip"`
	Status  string `thrift:"status,10" json:"status"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	Game *IosGame `thrift:"game,21" json:"game"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Card        string `thrift:"card,31" json:"card"`
	Icon        string `thrift:"icon,32" json:"icon"`
	Appid       string `thrift:"appid,33" json:"appid"`
	DownloadUrl string `thrift:"download_url,34" json:"download_url"`
	GameId      int32  `thrift:"game_id,35" json:"game_id"`
}

func NewIosGift() *IosGift {
	return &IosGift{}
}

func (p *IosGift) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosGift) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *IosGift) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *IosGift) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Scope = v
	}
	return nil
}

func (p *IosGift) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cat = v
	}
	return nil
}

func (p *IosGift) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Bedate = v
	}
	return nil
}

func (p *IosGift) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Eddate = v
	}
	return nil
}

func (p *IosGift) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *IosGift) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Intro = v
	}
	return nil
}

func (p *IosGift) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Tip = v
	}
	return nil
}

func (p *IosGift) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *IosGift) readField21(iprot thrift.TProtocol) error {
	p.Game = NewIosGame()
	if err := p.Game.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Game)
	}
	return nil
}

func (p *IosGift) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Card = v
	}
	return nil
}

func (p *IosGift) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Icon = v
	}
	return nil
}

func (p *IosGift) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *IosGift) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.DownloadUrl = v
	}
	return nil
}

func (p *IosGift) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.GameId = v
	}
	return nil
}

func (p *IosGift) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosGift"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosGift) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("scope", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:scope: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Scope)); err != nil {
		return fmt.Errorf("%T.scope (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:scope: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cat", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cat: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cat)); err != nil {
		return fmt.Errorf("%T.cat (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cat: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bedate", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:bedate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Bedate)); err != nil {
		return fmt.Errorf("%T.bedate (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:bedate: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("eddate", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:eddate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Eddate)); err != nil {
		return fmt.Errorf("%T.eddate (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:eddate: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:content: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("intro", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:intro: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Intro)); err != nil {
		return fmt.Errorf("%T.intro (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:intro: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tip", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:tip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tip)); err != nil {
		return fmt.Errorf("%T.tip (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:tip: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Status)); err != nil {
		return fmt.Errorf("%T.status (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:status: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField21(oprot thrift.TProtocol) (err error) {
	if p.Game != nil {
		if err := oprot.WriteFieldBegin("game", thrift.STRUCT, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:game: %s", p, err)
		}
		if err := p.Game.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Game)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:game: %s", p, err)
		}
	}
	return err
}

func (p *IosGift) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("card", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:card: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Card)); err != nil {
		return fmt.Errorf("%T.card (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:card: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:icon: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Icon)); err != nil {
		return fmt.Errorf("%T.icon (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:icon: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:appid: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_url", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:download_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DownloadUrl)); err != nil {
		return fmt.Errorf("%T.download_url (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:download_url: %s", p, err)
	}
	return err
}

func (p *IosGift) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("game_id", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:game_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GameId)); err != nil {
		return fmt.Errorf("%T.game_id (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:game_id: %s", p, err)
	}
	return err
}

func (p *IosGift) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosGift(%+v)", *p)
}

type IosGiftResult struct {
	Gifts      []*IosGift `thrift:"gifts,1" json:"gifts"`
	Total      int32      `thrift:"total,2" json:"total"`
	IsLastPage bool       `thrift:"is_last_page,3" json:"is_last_page"`
}

func NewIosGiftResult() *IosGiftResult {
	return &IosGiftResult{}
}

func (p *IosGiftResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosGiftResult) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Gifts = make([]*IosGift, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewIosGift()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Gifts = append(p.Gifts, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IosGiftResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *IosGiftResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IsLastPage = v
	}
	return nil
}

func (p *IosGiftResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosGiftResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosGiftResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Gifts != nil {
		if err := oprot.WriteFieldBegin("gifts", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:gifts: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Gifts)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Gifts {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:gifts: %s", p, err)
		}
	}
	return err
}

func (p *IosGiftResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:total: %s", p, err)
	}
	return err
}

func (p *IosGiftResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_last_page", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:is_last_page: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsLastPage)); err != nil {
		return fmt.Errorf("%T.is_last_page (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:is_last_page: %s", p, err)
	}
	return err
}

func (p *IosGiftResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosGiftResult(%+v)", *p)
}

type IosGameResult struct {
	Games        []*IosGame `thrift:"games,1" json:"games"`
	Total        int32      `thrift:"total,2" json:"total"`
	IsLastPage   bool       `thrift:"is_last_page,3" json:"is_last_page"`
	WallSearchid string     `thrift:"wall_searchid,4" json:"wall_searchid"`
	Searchid     string     `thrift:"searchid,5" json:"searchid"`
}

func NewIosGameResult() *IosGameResult {
	return &IosGameResult{}
}

func (p *IosGameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosGameResult) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Games = make([]*IosGame, 0, size)
	for i := 0; i < size; i++ {
		_elem4 := NewIosGame()
		if err := _elem4.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem4)
		}
		p.Games = append(p.Games, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IosGameResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Total = v
	}
	return nil
}

func (p *IosGameResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IsLastPage = v
	}
	return nil
}

func (p *IosGameResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.WallSearchid = v
	}
	return nil
}

func (p *IosGameResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *IosGameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosGameResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosGameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Games != nil {
		if err := oprot.WriteFieldBegin("games", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:games: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Games)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Games {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:games: %s", p, err)
		}
	}
	return err
}

func (p *IosGameResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:total: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Total)); err != nil {
		return fmt.Errorf("%T.total (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:total: %s", p, err)
	}
	return err
}

func (p *IosGameResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_last_page", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:is_last_page: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsLastPage)); err != nil {
		return fmt.Errorf("%T.is_last_page (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:is_last_page: %s", p, err)
	}
	return err
}

func (p *IosGameResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("wall_searchid", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:wall_searchid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.WallSearchid)); err != nil {
		return fmt.Errorf("%T.wall_searchid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:wall_searchid: %s", p, err)
	}
	return err
}

func (p *IosGameResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:searchid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:searchid: %s", p, err)
	}
	return err
}

func (p *IosGameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosGameResult(%+v)", *p)
}

type IosTracker struct {
	Appid int32 `thrift:"appid,1" json:"appid"`
	// unused field # 2
	Refer     string `thrift:"refer,3" json:"refer"`
	AdUserId  int32  `thrift:"ad_user_id,4" json:"ad_user_id"`
	ChannelId string `thrift:"channel_id,5" json:"channel_id"`
	ReqTimeMs int32  `thrift:"req_time_ms,6" json:"req_time_ms"`
	Ipb       string `thrift:"ipb,7" json:"ipb"`
	Ppid      string `thrift:"ppid,8" json:"ppid"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Searchid    int64          `thrift:"searchid,30" json:"searchid"`
	Expid       int32          `thrift:"expid,31" json:"expid"`
	Device      DeviceCode     `thrift:"device,32" json:"device"`
	Os          OSCode         `thrift:"os,33" json:"os"`
	Region      int32          `thrift:"region,34" json:"region"`
	CityCode    int32          `thrift:"city_code,35" json:"city_code"`
	CarrierCode CarrierCode    `thrift:"carrier_code,36" json:"carrier_code"`
	AccessCode  AccessTypeCode `thrift:"access_code,37" json:"access_code"`
	Duid        int64          `thrift:"duid,38" json:"duid"`
	Imei        string         `thrift:"imei,39" json:"imei"`
	Imsi        string         `thrift:"imsi,40" json:"imsi"`
	Dmac        string         `thrift:"dmac,41" json:"dmac"`
}

func NewIosTracker() *IosTracker {
	return &IosTracker{
		CarrierCode: math.MinInt32 - 1, // unset sentinal value

		AccessCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IosTracker) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *IosTracker) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *IosTracker) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosTracker) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *IosTracker) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *IosTracker) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdUserId = v
	}
	return nil
}

func (p *IosTracker) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *IosTracker) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ReqTimeMs = v
	}
	return nil
}

func (p *IosTracker) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *IosTracker) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Ppid = v
	}
	return nil
}

func (p *IosTracker) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *IosTracker) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Expid = v
	}
	return nil
}

func (p *IosTracker) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Device = DeviceCode(v)
	}
	return nil
}

func (p *IosTracker) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Os = OSCode(v)
	}
	return nil
}

func (p *IosTracker) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Region = v
	}
	return nil
}

func (p *IosTracker) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.CityCode = v
	}
	return nil
}

func (p *IosTracker) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.CarrierCode = CarrierCode(v)
	}
	return nil
}

func (p *IosTracker) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AccessCode = AccessTypeCode(v)
	}
	return nil
}

func (p *IosTracker) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *IosTracker) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *IosTracker) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Imsi = v
	}
	return nil
}

func (p *IosTracker) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *IosTracker) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosTracker"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosTracker) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:appid: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:refer: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_user_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ad_user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUserId)); err != nil {
		return fmt.Errorf("%T.ad_user_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ad_user_id: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel_id", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:channel_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channel_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:channel_id: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_time_ms", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:req_time_ms: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqTimeMs)); err != nil {
		return fmt.Errorf("%T.req_time_ms (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:req_time_ms: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ipb: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ppid", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ppid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ppid)); err != nil {
		return fmt.Errorf("%T.ppid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ppid: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:searchid: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Expid)); err != nil {
		return fmt.Errorf("%T.expid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:expid: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:device: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:os: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:region: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityCode)); err != nil {
		return fmt.Errorf("%T.city_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:city_code: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:carrier_code: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:access_code: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:duid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:duid: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:imei: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:imsi: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:imsi: %s", p, err)
	}
	return err
}

func (p *IosTracker) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:dmac: %s", p, err)
	}
	return err
}

func (p *IosTracker) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosTracker(%+v)", *p)
}

type Request struct {
	Searchid int64  `thrift:"searchid,1" json:"searchid"`
	XUid     string `thrift:"x_uid,2" json:"x_uid"`
	Jb       int32  `thrift:"jb,3" json:"jb"`
	XUa      string `thrift:"x_ua,4" json:"x_ua"`
	// unused field # 5
	Pubid              string             `thrift:"pubid,6" json:"pubid"`
	Ppid               string             `thrift:"ppid,7" json:"ppid"`
	Refer              string             `thrift:"refer,8" json:"refer"`
	ReqTimeMs          int64              `thrift:"req_time_ms,9" json:"req_time_ms"`
	ClientIp           string             `thrift:"client_ip,10" json:"client_ip"`
	PackageName        string             `thrift:"package_name,11" json:"package_name"`
	PackageVersionCode int32              `thrift:"package_version_code,12" json:"package_version_code"`
	IosVersion         string             `thrift:"ios_version,13" json:"ios_version"`
	ChannelId          string             `thrift:"channel_id,14" json:"channel_id"`
	Carrier            string             `thrift:"carrier,15" json:"carrier"`
	Network            string             `thrift:"network,16" json:"network"`
	PhoneModel         string             `thrift:"phone_model,17" json:"phone_model"`
	CarrierCode        common.CarrierCode `thrift:"carrier_code,18" json:"carrier_code"`
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	Expid    int32      `thrift:"expid,31" json:"expid"`
	Device   DeviceCode `thrift:"device,32" json:"device"`
	Os       OSCode     `thrift:"os,33" json:"os"`
	Region   int32      `thrift:"region,34" json:"region"`
	CityCode int32      `thrift:"city_code,35" json:"city_code"`
	// unused field # 36
	AccessCode AccessTypeCode `thrift:"access_code,37" json:"access_code"`
	Duid       int64          `thrift:"duid,38" json:"duid"`
	Imei       string         `thrift:"imei,39" json:"imei"`
	Imsi       string         `thrift:"imsi,40" json:"imsi"`
	Dmac       string         `thrift:"dmac,41" json:"dmac"`
	MediaSpec  bool           `thrift:"media_spec,42" json:"media_spec"`
}

func NewRequest() *Request {
	return &Request{
		CarrierCode: math.MinInt32 - 1, // unset sentinal value

		AccessCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Request) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *Request) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *Request) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Request) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *Request) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.XUid = v
	}
	return nil
}

func (p *Request) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.XUa = v
	}
	return nil
}

func (p *Request) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Jb = v
	}
	return nil
}

func (p *Request) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *Request) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ppid = v
	}
	return nil
}

func (p *Request) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *Request) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ReqTimeMs = v
	}
	return nil
}

func (p *Request) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *Request) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *Request) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PackageVersionCode = v
	}
	return nil
}

func (p *Request) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.IosVersion = v
	}
	return nil
}

func (p *Request) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *Request) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *Request) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *Request) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.PhoneModel = v
	}
	return nil
}

func (p *Request) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.CarrierCode = common.CarrierCode(v)
	}
	return nil
}

func (p *Request) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Expid = v
	}
	return nil
}

func (p *Request) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Device = DeviceCode(v)
	}
	return nil
}

func (p *Request) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Os = OSCode(v)
	}
	return nil
}

func (p *Request) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Region = v
	}
	return nil
}

func (p *Request) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.CityCode = v
	}
	return nil
}

func (p *Request) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AccessCode = AccessTypeCode(v)
	}
	return nil
}

func (p *Request) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *Request) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *Request) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Imsi = v
	}
	return nil
}

func (p *Request) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *Request) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.MediaSpec = v
	}
	return nil
}

func (p *Request) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Request"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Request) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchid: %s", p, err)
	}
	return err
}

func (p *Request) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_uid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:x_uid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUid)); err != nil {
		return fmt.Errorf("%T.x_uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:x_uid: %s", p, err)
	}
	return err
}

func (p *Request) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jb", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:jb: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Jb)); err != nil {
		return fmt.Errorf("%T.jb (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:jb: %s", p, err)
	}
	return err
}

func (p *Request) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_ua", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:x_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUa)); err != nil {
		return fmt.Errorf("%T.x_ua (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:x_ua: %s", p, err)
	}
	return err
}

func (p *Request) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pubid: %s", p, err)
	}
	return err
}

func (p *Request) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ppid", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ppid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ppid)); err != nil {
		return fmt.Errorf("%T.ppid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ppid: %s", p, err)
	}
	return err
}

func (p *Request) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:refer: %s", p, err)
	}
	return err
}

func (p *Request) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_time_ms", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:req_time_ms: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReqTimeMs)); err != nil {
		return fmt.Errorf("%T.req_time_ms (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:req_time_ms: %s", p, err)
	}
	return err
}

func (p *Request) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client_ip", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:client_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.client_ip (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:client_ip: %s", p, err)
	}
	return err
}

func (p *Request) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:package_name: %s", p, err)
	}
	return err
}

func (p *Request) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_version_code", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:package_version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PackageVersionCode)); err != nil {
		return fmt.Errorf("%T.package_version_code (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:package_version_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_version", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ios_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IosVersion)); err != nil {
		return fmt.Errorf("%T.ios_version (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ios_version: %s", p, err)
	}
	return err
}

func (p *Request) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel_id", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:channel_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channel_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:channel_id: %s", p, err)
	}
	return err
}

func (p *Request) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:carrier: %s", p, err)
	}
	return err
}

func (p *Request) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:network: %s", p, err)
	}
	return err
}

func (p *Request) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_model", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:phone_model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneModel)); err != nil {
		return fmt.Errorf("%T.phone_model (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:phone_model: %s", p, err)
	}
	return err
}

func (p *Request) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:carrier_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Expid)); err != nil {
		return fmt.Errorf("%T.expid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:expid: %s", p, err)
	}
	return err
}

func (p *Request) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:device: %s", p, err)
	}
	return err
}

func (p *Request) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:os: %s", p, err)
	}
	return err
}

func (p *Request) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:region: %s", p, err)
	}
	return err
}

func (p *Request) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityCode)); err != nil {
		return fmt.Errorf("%T.city_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:city_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:access_code: %s", p, err)
	}
	return err
}

func (p *Request) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:duid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:duid: %s", p, err)
	}
	return err
}

func (p *Request) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:imei: %s", p, err)
	}
	return err
}

func (p *Request) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:imsi: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:imsi: %s", p, err)
	}
	return err
}

func (p *Request) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:dmac: %s", p, err)
	}
	return err
}

func (p *Request) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_spec", thrift.BOOL, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:media_spec: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaSpec)); err != nil {
		return fmt.Errorf("%T.media_spec (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:media_spec: %s", p, err)
	}
	return err
}

func (p *Request) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Request(%+v)", *p)
}
