// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package searchui

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type TimeInt common.TimeInt

type CgiReq struct {
	Body           string  `thrift:"body,1" json:"body"`
	Ip             string  `thrift:"ip,2" json:"ip"`
	Ua             string  `thrift:"ua,3" json:"ua"`
	Referer        string  `thrift:"referer,4" json:"referer"`
	XUpBearType    string  `thrift:"x_up_bear_type,5" json:"x_up_bear_type"`
	XForwardedFor  string  `thrift:"x_forwarded_for,6" json:"x_forwarded_for"`
	<PERSON>ie         string  `thrift:"cookie,7" json:"cookie"`
	Accept         string  `thrift:"accept,8" json:"accept"`
	AcceptCharset  string  `thrift:"accept_charset,9" json:"accept_charset"`
	AcceptEncoding string  `thrift:"accept_encoding,10" json:"accept_encoding"`
	ReqTime        TimeInt `thrift:"req_time,11" json:"req_time"`
}

func NewCgiReq() *CgiReq {
	return &CgiReq{}
}

func (p *CgiReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CgiReq) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Body = v
	}
	return nil
}

func (p *CgiReq) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *CgiReq) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ua = v
	}
	return nil
}

func (p *CgiReq) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Referer = v
	}
	return nil
}

func (p *CgiReq) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.XUpBearType = v
	}
	return nil
}

func (p *CgiReq) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.XForwardedFor = v
	}
	return nil
}

func (p *CgiReq) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Cookie = v
	}
	return nil
}

func (p *CgiReq) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Accept = v
	}
	return nil
}

func (p *CgiReq) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AcceptCharset = v
	}
	return nil
}

func (p *CgiReq) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AcceptEncoding = v
	}
	return nil
}

func (p *CgiReq) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ReqTime = TimeInt(v)
	}
	return nil
}

func (p *CgiReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CgiReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CgiReq) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("body", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:body: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Body)); err != nil {
		return fmt.Errorf("%T.body (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:body: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ip: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ua", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ua)); err != nil {
		return fmt.Errorf("%T.ua (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ua: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("referer", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:referer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Referer)); err != nil {
		return fmt.Errorf("%T.referer (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:referer: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_up_bear_type", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:x_up_bear_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XUpBearType)); err != nil {
		return fmt.Errorf("%T.x_up_bear_type (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:x_up_bear_type: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_forwarded_for", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:x_forwarded_for: %s", p, err)
	}
	if err := oprot.WriteString(string(p.XForwardedFor)); err != nil {
		return fmt.Errorf("%T.x_forwarded_for (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:x_forwarded_for: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookie", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:cookie: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cookie)); err != nil {
		return fmt.Errorf("%T.cookie (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:cookie: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accept", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:accept: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Accept)); err != nil {
		return fmt.Errorf("%T.accept (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:accept: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accept_charset", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:accept_charset: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AcceptCharset)); err != nil {
		return fmt.Errorf("%T.accept_charset (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:accept_charset: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accept_encoding", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:accept_encoding: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AcceptEncoding)); err != nil {
		return fmt.Errorf("%T.accept_encoding (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:accept_encoding: %s", p, err)
	}
	return err
}

func (p *CgiReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_time", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:req_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ReqTime)); err != nil {
		return fmt.Errorf("%T.req_time (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:req_time: %s", p, err)
	}
	return err
}

func (p *CgiReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CgiReq(%+v)", *p)
}

type UiRes struct {
	Status    int32  `thrift:"status,1" json:"status"`
	Content   string `thrift:"content,2" json:"content"`
	SetCookie string `thrift:"set_cookie,3" json:"set_cookie"`
}

func NewUiRes() *UiRes {
	return &UiRes{}
}

func (p *UiRes) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UiRes) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *UiRes) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *UiRes) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SetCookie = v
	}
	return nil
}

func (p *UiRes) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UiRes"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UiRes) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *UiRes) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:content: %s", p, err)
	}
	return err
}

func (p *UiRes) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("set_cookie", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:set_cookie: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SetCookie)); err != nil {
		return fmt.Errorf("%T.set_cookie (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:set_cookie: %s", p, err)
	}
	return err
}

func (p *UiRes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UiRes(%+v)", *p)
}
