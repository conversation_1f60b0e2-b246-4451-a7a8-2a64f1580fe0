// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_exception

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//vico统一异常错误码
type ExceptionCode int64

const (
	ExceptionCode_SYSTEM_ERROR                 ExceptionCode = 10000
	ExceptionCode_REQUEST_PARAMS_INVALID       ExceptionCode = 10001
	ExceptionCode_USER_PERMISSION_ERROR        ExceptionCode = 10002
	ExceptionCode_ACCOUNT_MODULE_EXPIRED       ExceptionCode = 10010
	ExceptionCode_ACCOUNT_MODULE_USED_LIMIT    ExceptionCode = 10011
	ExceptionCode_MOBILE_NUMBER_ERROR          ExceptionCode = 10101
	ExceptionCode_MOBILE_VCODE_SEND_ERROR      ExceptionCode = 10102
	ExceptionCode_MOBILE_VCODE_SEND_TOO_OFTEN  ExceptionCode = 10103
	ExceptionCode_MOBILE_VALIDATE_CODE_EXPIRED ExceptionCode = 10104
	ExceptionCode_MOBILE_VAILDATE_CODE_ERROR   ExceptionCode = 10105
	ExceptionCode_MOBILE_NUMBER_EXISTS         ExceptionCode = 10106
	ExceptionCode_USER_PASSWORD_ERROR          ExceptionCode = 10111
	ExceptionCode_USER_ACCOUNT_INVALID         ExceptionCode = 10112
	ExceptionCode_USER_ACCOUNT_STATUS_INVALID  ExceptionCode = 10113
	ExceptionCode_USER_ACCOUNT_BANED           ExceptionCode = 10114
	ExceptionCode_VICO_PRODUCT_EXISTS          ExceptionCode = 10201
)

func (p ExceptionCode) String() string {
	switch p {
	case ExceptionCode_SYSTEM_ERROR:
		return "ExceptionCode_SYSTEM_ERROR"
	case ExceptionCode_REQUEST_PARAMS_INVALID:
		return "ExceptionCode_REQUEST_PARAMS_INVALID"
	case ExceptionCode_USER_PERMISSION_ERROR:
		return "ExceptionCode_USER_PERMISSION_ERROR"
	case ExceptionCode_ACCOUNT_MODULE_EXPIRED:
		return "ExceptionCode_ACCOUNT_MODULE_EXPIRED"
	case ExceptionCode_ACCOUNT_MODULE_USED_LIMIT:
		return "ExceptionCode_ACCOUNT_MODULE_USED_LIMIT"
	case ExceptionCode_MOBILE_NUMBER_ERROR:
		return "ExceptionCode_MOBILE_NUMBER_ERROR"
	case ExceptionCode_MOBILE_VCODE_SEND_ERROR:
		return "ExceptionCode_MOBILE_VCODE_SEND_ERROR"
	case ExceptionCode_MOBILE_VCODE_SEND_TOO_OFTEN:
		return "ExceptionCode_MOBILE_VCODE_SEND_TOO_OFTEN"
	case ExceptionCode_MOBILE_VALIDATE_CODE_EXPIRED:
		return "ExceptionCode_MOBILE_VALIDATE_CODE_EXPIRED"
	case ExceptionCode_MOBILE_VAILDATE_CODE_ERROR:
		return "ExceptionCode_MOBILE_VAILDATE_CODE_ERROR"
	case ExceptionCode_MOBILE_NUMBER_EXISTS:
		return "ExceptionCode_MOBILE_NUMBER_EXISTS"
	case ExceptionCode_USER_PASSWORD_ERROR:
		return "ExceptionCode_USER_PASSWORD_ERROR"
	case ExceptionCode_USER_ACCOUNT_INVALID:
		return "ExceptionCode_USER_ACCOUNT_INVALID"
	case ExceptionCode_USER_ACCOUNT_STATUS_INVALID:
		return "ExceptionCode_USER_ACCOUNT_STATUS_INVALID"
	case ExceptionCode_USER_ACCOUNT_BANED:
		return "ExceptionCode_USER_ACCOUNT_BANED"
	case ExceptionCode_VICO_PRODUCT_EXISTS:
		return "ExceptionCode_VICO_PRODUCT_EXISTS"
	}
	return "<UNSET>"
}

func ExceptionCodeFromString(s string) (ExceptionCode, error) {
	switch s {
	case "ExceptionCode_SYSTEM_ERROR":
		return ExceptionCode_SYSTEM_ERROR, nil
	case "ExceptionCode_REQUEST_PARAMS_INVALID":
		return ExceptionCode_REQUEST_PARAMS_INVALID, nil
	case "ExceptionCode_USER_PERMISSION_ERROR":
		return ExceptionCode_USER_PERMISSION_ERROR, nil
	case "ExceptionCode_ACCOUNT_MODULE_EXPIRED":
		return ExceptionCode_ACCOUNT_MODULE_EXPIRED, nil
	case "ExceptionCode_ACCOUNT_MODULE_USED_LIMIT":
		return ExceptionCode_ACCOUNT_MODULE_USED_LIMIT, nil
	case "ExceptionCode_MOBILE_NUMBER_ERROR":
		return ExceptionCode_MOBILE_NUMBER_ERROR, nil
	case "ExceptionCode_MOBILE_VCODE_SEND_ERROR":
		return ExceptionCode_MOBILE_VCODE_SEND_ERROR, nil
	case "ExceptionCode_MOBILE_VCODE_SEND_TOO_OFTEN":
		return ExceptionCode_MOBILE_VCODE_SEND_TOO_OFTEN, nil
	case "ExceptionCode_MOBILE_VALIDATE_CODE_EXPIRED":
		return ExceptionCode_MOBILE_VALIDATE_CODE_EXPIRED, nil
	case "ExceptionCode_MOBILE_VAILDATE_CODE_ERROR":
		return ExceptionCode_MOBILE_VAILDATE_CODE_ERROR, nil
	case "ExceptionCode_MOBILE_NUMBER_EXISTS":
		return ExceptionCode_MOBILE_NUMBER_EXISTS, nil
	case "ExceptionCode_USER_PASSWORD_ERROR":
		return ExceptionCode_USER_PASSWORD_ERROR, nil
	case "ExceptionCode_USER_ACCOUNT_INVALID":
		return ExceptionCode_USER_ACCOUNT_INVALID, nil
	case "ExceptionCode_USER_ACCOUNT_STATUS_INVALID":
		return ExceptionCode_USER_ACCOUNT_STATUS_INVALID, nil
	case "ExceptionCode_USER_ACCOUNT_BANED":
		return ExceptionCode_USER_ACCOUNT_BANED, nil
	case "ExceptionCode_VICO_PRODUCT_EXISTS":
		return ExceptionCode_VICO_PRODUCT_EXISTS, nil
	}
	return ExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid ExceptionCode string")
}

type VicoException struct {
	Code    ExceptionCode `thrift:"code,1" json:"code"`
	Message string        `thrift:"message,2" json:"message"`
}

func NewVicoException() *VicoException {
	return &VicoException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *VicoException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *VicoException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VicoException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ExceptionCode(v)
	}
	return nil
}

func (p *VicoException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *VicoException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VicoException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VicoException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *VicoException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *VicoException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VicoException(%+v)", *p)
}
