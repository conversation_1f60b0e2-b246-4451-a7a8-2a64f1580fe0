// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package infoexporter_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

type ExportDataType int64

const (
	ExportDataType_EDT_AD_SUMMARY                   ExportDataType = 10
	ExportDataType_EDT_AD_USER_SUMMARY              ExportDataType = 11
	ExportDataType_EDT_AD_PLAN_UPDATE_EVENT         ExportDataType = 12
	ExportDataType_EDT_AD_STRATEGY_UPDATE_EVENT     ExportDataType = 13
	ExportDataType_EDT_AD_CREATIVE_UPDATE_EVENT     ExportDataType = 14
	ExportDataType_EDT_MEDIA_SUMMARY                ExportDataType = 20
	ExportDataType_EDT_MEDIA_USER_SUMMARY           ExportDataType = 21
	ExportDataType_EDT_MEDIA_UPDATE_EVENT           ExportDataType = 22
	ExportDataType_EDT_MEDIA_PLACEMENT_UPDATE_EVENT ExportDataType = 23
)

func (p ExportDataType) String() string {
	switch p {
	case ExportDataType_EDT_AD_SUMMARY:
		return "ExportDataType_EDT_AD_SUMMARY"
	case ExportDataType_EDT_AD_USER_SUMMARY:
		return "ExportDataType_EDT_AD_USER_SUMMARY"
	case ExportDataType_EDT_AD_PLAN_UPDATE_EVENT:
		return "ExportDataType_EDT_AD_PLAN_UPDATE_EVENT"
	case ExportDataType_EDT_AD_STRATEGY_UPDATE_EVENT:
		return "ExportDataType_EDT_AD_STRATEGY_UPDATE_EVENT"
	case ExportDataType_EDT_AD_CREATIVE_UPDATE_EVENT:
		return "ExportDataType_EDT_AD_CREATIVE_UPDATE_EVENT"
	case ExportDataType_EDT_MEDIA_SUMMARY:
		return "ExportDataType_EDT_MEDIA_SUMMARY"
	case ExportDataType_EDT_MEDIA_USER_SUMMARY:
		return "ExportDataType_EDT_MEDIA_USER_SUMMARY"
	case ExportDataType_EDT_MEDIA_UPDATE_EVENT:
		return "ExportDataType_EDT_MEDIA_UPDATE_EVENT"
	case ExportDataType_EDT_MEDIA_PLACEMENT_UPDATE_EVENT:
		return "ExportDataType_EDT_MEDIA_PLACEMENT_UPDATE_EVENT"
	}
	return "<UNSET>"
}

func ExportDataTypeFromString(s string) (ExportDataType, error) {
	switch s {
	case "ExportDataType_EDT_AD_SUMMARY":
		return ExportDataType_EDT_AD_SUMMARY, nil
	case "ExportDataType_EDT_AD_USER_SUMMARY":
		return ExportDataType_EDT_AD_USER_SUMMARY, nil
	case "ExportDataType_EDT_AD_PLAN_UPDATE_EVENT":
		return ExportDataType_EDT_AD_PLAN_UPDATE_EVENT, nil
	case "ExportDataType_EDT_AD_STRATEGY_UPDATE_EVENT":
		return ExportDataType_EDT_AD_STRATEGY_UPDATE_EVENT, nil
	case "ExportDataType_EDT_AD_CREATIVE_UPDATE_EVENT":
		return ExportDataType_EDT_AD_CREATIVE_UPDATE_EVENT, nil
	case "ExportDataType_EDT_MEDIA_SUMMARY":
		return ExportDataType_EDT_MEDIA_SUMMARY, nil
	case "ExportDataType_EDT_MEDIA_USER_SUMMARY":
		return ExportDataType_EDT_MEDIA_USER_SUMMARY, nil
	case "ExportDataType_EDT_MEDIA_UPDATE_EVENT":
		return ExportDataType_EDT_MEDIA_UPDATE_EVENT, nil
	case "ExportDataType_EDT_MEDIA_PLACEMENT_UPDATE_EVENT":
		return ExportDataType_EDT_MEDIA_PLACEMENT_UPDATE_EVENT, nil
	}
	return ExportDataType(math.MinInt32 - 1), fmt.Errorf("not a valid ExportDataType string")
}

type AdExportSummary struct {
	UserCount       common.QueryInt `thrift:"userCount,1" json:"userCount"`
	AdPlanCount     common.QueryInt `thrift:"adPlanCount,2" json:"adPlanCount"`
	AdStrategyCount common.QueryInt `thrift:"adStrategyCount,3" json:"adStrategyCount"`
	AdCreativeCount common.QueryInt `thrift:"adCreativeCount,4" json:"adCreativeCount"`
	SummaryOffset   int64           `thrift:"summaryOffset,5" json:"summaryOffset"`
	UserOffset      int64           `thrift:"userOffset,6" json:"userOffset"`
	PlanOffset      int64           `thrift:"planOffset,7" json:"planOffset"`
	StrategyOffset  int64           `thrift:"strategyOffset,8" json:"strategyOffset"`
	CreativeOffset  int64           `thrift:"creativeOffset,9" json:"creativeOffset"`
}

func NewAdExportSummary() *AdExportSummary {
	return &AdExportSummary{}
}

func (p *AdExportSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdExportSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.UserCount = common.QueryInt(v)
	}
	return nil
}

func (p *AdExportSummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdPlanCount = common.QueryInt(v)
	}
	return nil
}

func (p *AdExportSummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AdStrategyCount = common.QueryInt(v)
	}
	return nil
}

func (p *AdExportSummary) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdCreativeCount = common.QueryInt(v)
	}
	return nil
}

func (p *AdExportSummary) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SummaryOffset = v
	}
	return nil
}

func (p *AdExportSummary) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.UserOffset = v
	}
	return nil
}

func (p *AdExportSummary) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PlanOffset = v
	}
	return nil
}

func (p *AdExportSummary) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.StrategyOffset = v
	}
	return nil
}

func (p *AdExportSummary) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CreativeOffset = v
	}
	return nil
}

func (p *AdExportSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdExportSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdExportSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:userCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserCount)); err != nil {
		return fmt.Errorf("%T.userCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:userCount: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPlanCount", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:adPlanCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdPlanCount)); err != nil {
		return fmt.Errorf("%T.adPlanCount (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:adPlanCount: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adStrategyCount", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:adStrategyCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdStrategyCount)); err != nil {
		return fmt.Errorf("%T.adStrategyCount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:adStrategyCount: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adCreativeCount", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:adCreativeCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdCreativeCount)); err != nil {
		return fmt.Errorf("%T.adCreativeCount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:adCreativeCount: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryOffset", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:summaryOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryOffset)); err != nil {
		return fmt.Errorf("%T.summaryOffset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:summaryOffset: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userOffset", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:userOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UserOffset)); err != nil {
		return fmt.Errorf("%T.userOffset (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:userOffset: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planOffset", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:planOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PlanOffset)); err != nil {
		return fmt.Errorf("%T.planOffset (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:planOffset: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyOffset", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:strategyOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StrategyOffset)); err != nil {
		return fmt.Errorf("%T.strategyOffset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:strategyOffset: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creativeOffset", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:creativeOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreativeOffset)); err != nil {
		return fmt.Errorf("%T.creativeOffset (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:creativeOffset: %s", p, err)
	}
	return err
}

func (p *AdExportSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdExportSummary(%+v)", *p)
}

type MediaExportSummary struct {
	UserCount           common.QueryInt `thrift:"userCount,1" json:"userCount"`
	MediaCount          common.QueryInt `thrift:"mediaCount,2" json:"mediaCount"`
	MediaPlacementCount common.QueryInt `thrift:"mediaPlacementCount,3" json:"mediaPlacementCount"`
	SummaryOffset       int64           `thrift:"summaryOffset,4" json:"summaryOffset"`
	UserOffset          int64           `thrift:"userOffset,5" json:"userOffset"`
	MediaOffset         int64           `thrift:"mediaOffset,6" json:"mediaOffset"`
	PlacementOffset     int64           `thrift:"placementOffset,7" json:"placementOffset"`
}

func NewMediaExportSummary() *MediaExportSummary {
	return &MediaExportSummary{}
}

func (p *MediaExportSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaExportSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.UserCount = common.QueryInt(v)
	}
	return nil
}

func (p *MediaExportSummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaCount = common.QueryInt(v)
	}
	return nil
}

func (p *MediaExportSummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaPlacementCount = common.QueryInt(v)
	}
	return nil
}

func (p *MediaExportSummary) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SummaryOffset = v
	}
	return nil
}

func (p *MediaExportSummary) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UserOffset = v
	}
	return nil
}

func (p *MediaExportSummary) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MediaOffset = v
	}
	return nil
}

func (p *MediaExportSummary) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PlacementOffset = v
	}
	return nil
}

func (p *MediaExportSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaExportSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaExportSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:userCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserCount)); err != nil {
		return fmt.Errorf("%T.userCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:userCount: %s", p, err)
	}
	return err
}

func (p *MediaExportSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaCount", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mediaCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaCount)); err != nil {
		return fmt.Errorf("%T.mediaCount (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mediaCount: %s", p, err)
	}
	return err
}

func (p *MediaExportSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaPlacementCount", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mediaPlacementCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaPlacementCount)); err != nil {
		return fmt.Errorf("%T.mediaPlacementCount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mediaPlacementCount: %s", p, err)
	}
	return err
}

func (p *MediaExportSummary) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("summaryOffset", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:summaryOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SummaryOffset)); err != nil {
		return fmt.Errorf("%T.summaryOffset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:summaryOffset: %s", p, err)
	}
	return err
}

func (p *MediaExportSummary) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userOffset", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:userOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UserOffset)); err != nil {
		return fmt.Errorf("%T.userOffset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:userOffset: %s", p, err)
	}
	return err
}

func (p *MediaExportSummary) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaOffset", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:mediaOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaOffset)); err != nil {
		return fmt.Errorf("%T.mediaOffset (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:mediaOffset: %s", p, err)
	}
	return err
}

func (p *MediaExportSummary) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementOffset", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:placementOffset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PlacementOffset)); err != nil {
		return fmt.Errorf("%T.placementOffset (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:placementOffset: %s", p, err)
	}
	return err
}

func (p *MediaExportSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaExportSummary(%+v)", *p)
}

type AdUserSummary struct {
	Uid        common.UidInt `thrift:"uid,1" json:"uid"`
	Recharged  common.Amount `thrift:"recharged,2" json:"recharged"`
	Transfered common.Amount `thrift:"transfered,3" json:"transfered"`
	Consumed   common.Amount `thrift:"consumed,4" json:"consumed"`
	Balance    common.Amount `thrift:"balance,5" json:"balance"`
	Current    common.Amount `thrift:"current,6" json:"current"`
	Credit     common.Amount `thrift:"credit,7" json:"credit"`
	// unused field # 8
	// unused field # 9
	Role              common.UserRole      `thrift:"role,10" json:"role"`
	AutoTransferRate  common.PercentageInt `thrift:"autoTransferRate,11" json:"autoTransferRate"`
	TransferAwardRate common.PercentageInt `thrift:"transferAwardRate,12" json:"transferAwardRate"`
	RechargeAwardRate common.PercentageInt `thrift:"rechargeAwardRate,13" json:"rechargeAwardRate"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	SponsorType enums.SponsorType `thrift:"sponsorType,20" json:"sponsorType"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	ExtInfo map[string]string `thrift:"extInfo,30" json:"extInfo"`
}

func NewAdUserSummary() *AdUserSummary {
	return &AdUserSummary{
		Role: math.MinInt32 - 1, // unset sentinal value

		SponsorType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdUserSummary) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *AdUserSummary) IsSetSponsorType() bool {
	return int64(p.SponsorType) != math.MinInt32-1
}

func (p *AdUserSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.MAP {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdUserSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *AdUserSummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Recharged = common.Amount(v)
	}
	return nil
}

func (p *AdUserSummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Transfered = common.Amount(v)
	}
	return nil
}

func (p *AdUserSummary) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Consumed = common.Amount(v)
	}
	return nil
}

func (p *AdUserSummary) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Balance = common.Amount(v)
	}
	return nil
}

func (p *AdUserSummary) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Current = common.Amount(v)
	}
	return nil
}

func (p *AdUserSummary) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Credit = common.Amount(v)
	}
	return nil
}

func (p *AdUserSummary) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Role = common.UserRole(v)
	}
	return nil
}

func (p *AdUserSummary) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AutoTransferRate = common.PercentageInt(v)
	}
	return nil
}

func (p *AdUserSummary) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TransferAwardRate = common.PercentageInt(v)
	}
	return nil
}

func (p *AdUserSummary) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RechargeAwardRate = common.PercentageInt(v)
	}
	return nil
}

func (p *AdUserSummary) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.SponsorType = enums.SponsorType(v)
	}
	return nil
}

func (p *AdUserSummary) readField30(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.ExtInfo[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdUserSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdUserSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdUserSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("recharged", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:recharged: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Recharged)); err != nil {
		return fmt.Errorf("%T.recharged (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:recharged: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transfered", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:transfered: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Transfered)); err != nil {
		return fmt.Errorf("%T.transfered (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:transfered: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumed", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consumed)); err != nil {
		return fmt.Errorf("%T.consumed (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:consumed: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:balance: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("current", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:current: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Current)); err != nil {
		return fmt.Errorf("%T.current (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:current: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("credit", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:credit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Credit)); err != nil {
		return fmt.Errorf("%T.credit (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:credit: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("role", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:role: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Role)); err != nil {
		return fmt.Errorf("%T.role (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:role: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("autoTransferRate", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:autoTransferRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AutoTransferRate)); err != nil {
		return fmt.Errorf("%T.autoTransferRate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:autoTransferRate: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transferAwardRate", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:transferAwardRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TransferAwardRate)); err != nil {
		return fmt.Errorf("%T.transferAwardRate (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:transferAwardRate: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rechargeAwardRate", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:rechargeAwardRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RechargeAwardRate)); err != nil {
		return fmt.Errorf("%T.rechargeAwardRate (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:rechargeAwardRate: %s", p, err)
	}
	return err
}

func (p *AdUserSummary) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetSponsorType() {
		if err := oprot.WriteFieldBegin("sponsorType", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:sponsorType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SponsorType)); err != nil {
			return fmt.Errorf("%T.sponsorType (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:sponsorType: %s", p, err)
		}
	}
	return err
}

func (p *AdUserSummary) writeField30(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdUserSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdUserSummary(%+v)", *p)
}

type MediaUserSummary struct {
	Uid        common.UidInt `thrift:"uid,1" json:"uid"`
	Income     common.Amount `thrift:"income,2" json:"income"`
	Withdrawn  common.Amount `thrift:"withdrawn,3" json:"withdrawn"`
	Transfered common.Amount `thrift:"transfered,4" json:"transfered"`
	Balance    common.Amount `thrift:"balance,5" json:"balance"`
	Current    common.Amount `thrift:"current,6" json:"current"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Role              common.UserRole      `thrift:"role,10" json:"role"`
	AutoTransferRate  common.PercentageInt `thrift:"autoTransferRate,11" json:"autoTransferRate"`
	TransferAwardRate common.PercentageInt `thrift:"transferAwardRate,12" json:"transferAwardRate"`
	RechargeAwardRate common.PercentageInt `thrift:"rechargeAwardRate,13" json:"rechargeAwardRate"`
}

func NewMediaUserSummary() *MediaUserSummary {
	return &MediaUserSummary{
		Role: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MediaUserSummary) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *MediaUserSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaUserSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *MediaUserSummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Income = common.Amount(v)
	}
	return nil
}

func (p *MediaUserSummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Withdrawn = common.Amount(v)
	}
	return nil
}

func (p *MediaUserSummary) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Transfered = common.Amount(v)
	}
	return nil
}

func (p *MediaUserSummary) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Balance = common.Amount(v)
	}
	return nil
}

func (p *MediaUserSummary) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Current = common.Amount(v)
	}
	return nil
}

func (p *MediaUserSummary) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Role = common.UserRole(v)
	}
	return nil
}

func (p *MediaUserSummary) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AutoTransferRate = common.PercentageInt(v)
	}
	return nil
}

func (p *MediaUserSummary) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TransferAwardRate = common.PercentageInt(v)
	}
	return nil
}

func (p *MediaUserSummary) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.RechargeAwardRate = common.PercentageInt(v)
	}
	return nil
}

func (p *MediaUserSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaUserSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaUserSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("income", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:income: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Income)); err != nil {
		return fmt.Errorf("%T.income (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:income: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("withdrawn", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:withdrawn: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Withdrawn)); err != nil {
		return fmt.Errorf("%T.withdrawn (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:withdrawn: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transfered", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:transfered: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Transfered)); err != nil {
		return fmt.Errorf("%T.transfered (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:transfered: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:balance: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("current", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:current: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Current)); err != nil {
		return fmt.Errorf("%T.current (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:current: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("role", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:role: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Role)); err != nil {
		return fmt.Errorf("%T.role (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:role: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("autoTransferRate", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:autoTransferRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AutoTransferRate)); err != nil {
		return fmt.Errorf("%T.autoTransferRate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:autoTransferRate: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transferAwardRate", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:transferAwardRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TransferAwardRate)); err != nil {
		return fmt.Errorf("%T.transferAwardRate (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:transferAwardRate: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rechargeAwardRate", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:rechargeAwardRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RechargeAwardRate)); err != nil {
		return fmt.Errorf("%T.rechargeAwardRate (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:rechargeAwardRate: %s", p, err)
	}
	return err
}

func (p *MediaUserSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaUserSummary(%+v)", *p)
}
