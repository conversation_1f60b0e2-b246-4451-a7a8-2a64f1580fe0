// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package compass_passport_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//账户角色
type CompassAccountRole int64

const (
	CompassAccountRole_VAR_UNKNOWN CompassAccountRole = 0
	CompassAccountRole_VAR_ADMIN   CompassAccountRole = 1
)

func (p CompassAccountRole) String() string {
	switch p {
	case CompassAccountRole_VAR_UNKNOWN:
		return "CompassAccountRole_VAR_UNKNOWN"
	case CompassAccountRole_VAR_ADMIN:
		return "CompassAccountRole_VAR_ADMIN"
	}
	return "<UNSET>"
}

func CompassAccountRoleFromString(s string) (CompassAccountRole, error) {
	switch s {
	case "CompassAccountRole_VAR_UNKNOWN":
		return CompassAccountRole_VAR_UNKNOWN, nil
	case "CompassAccountRole_VAR_ADMIN":
		return CompassAccountRole_VAR_ADMIN, nil
	}
	return CompassAccountRole(math.MinInt32 - 1), fmt.Errorf("not a valid CompassAccountRole string")
}

//账户状态
type CompassAccountStatus int64

const (
	CompassAccountStatus_VAS_RUNNING CompassAccountStatus = 0
	CompassAccountStatus_VAS_BANNED  CompassAccountStatus = 1
	CompassAccountStatus_VAS_DELETED CompassAccountStatus = 2
)

func (p CompassAccountStatus) String() string {
	switch p {
	case CompassAccountStatus_VAS_RUNNING:
		return "CompassAccountStatus_VAS_RUNNING"
	case CompassAccountStatus_VAS_BANNED:
		return "CompassAccountStatus_VAS_BANNED"
	case CompassAccountStatus_VAS_DELETED:
		return "CompassAccountStatus_VAS_DELETED"
	}
	return "<UNSET>"
}

func CompassAccountStatusFromString(s string) (CompassAccountStatus, error) {
	switch s {
	case "CompassAccountStatus_VAS_RUNNING":
		return CompassAccountStatus_VAS_RUNNING, nil
	case "CompassAccountStatus_VAS_BANNED":
		return CompassAccountStatus_VAS_BANNED, nil
	case "CompassAccountStatus_VAS_DELETED":
		return CompassAccountStatus_VAS_DELETED, nil
	}
	return CompassAccountStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CompassAccountStatus string")
}

type UserRegisterInfo struct {
	Email           string `thrift:"email,1" json:"email"`
	Password        string `thrift:"password,2" json:"password"`
	ConfirmPassword string `thrift:"confirmPassword,3" json:"confirmPassword"`
}

func NewUserRegisterInfo() *UserRegisterInfo {
	return &UserRegisterInfo{}
}

func (p *UserRegisterInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserRegisterInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *UserRegisterInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *UserRegisterInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ConfirmPassword = v
	}
	return nil
}

func (p *UserRegisterInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserRegisterInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserRegisterInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:email: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:password: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("confirmPassword", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:confirmPassword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ConfirmPassword)); err != nil {
		return fmt.Errorf("%T.confirmPassword (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:confirmPassword: %s", p, err)
	}
	return err
}

func (p *UserRegisterInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserRegisterInfo(%+v)", *p)
}

type CompassAccount struct {
	Id    int64              `thrift:"id,1" json:"id"`
	Email string             `thrift:"email,2" json:"email"`
	Role  CompassAccountRole `thrift:"role,3" json:"role"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	RealName string `thrift:"realName,10" json:"realName"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	Status CompassAccountStatus `thrift:"status,21" json:"status"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	CreateTime int64 `thrift:"createTime,90" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,91" json:"lastUpdate"`
}

func NewCompassAccount() *CompassAccount {
	return &CompassAccount{
		Role: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CompassAccount) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *CompassAccount) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *CompassAccount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 90:
			if fieldTypeId == thrift.I64 {
				if err := p.readField90(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 91:
			if fieldTypeId == thrift.I64 {
				if err := p.readField91(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompassAccount) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *CompassAccount) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *CompassAccount) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Role = CompassAccountRole(v)
	}
	return nil
}

func (p *CompassAccount) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RealName = v
	}
	return nil
}

func (p *CompassAccount) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Status = CompassAccountStatus(v)
	}
	return nil
}

func (p *CompassAccount) readField90(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 90: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *CompassAccount) readField91(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 91: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *CompassAccount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CompassAccount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField90(oprot); err != nil {
		return err
	}
	if err := p.writeField91(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompassAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *CompassAccount) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:email: %s", p, err)
	}
	return err
}

func (p *CompassAccount) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRole() {
		if err := oprot.WriteFieldBegin("role", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:role: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Role)); err != nil {
			return fmt.Errorf("%T.role (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:role: %s", p, err)
		}
	}
	return err
}

func (p *CompassAccount) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("realName", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:realName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RealName)); err != nil {
		return fmt.Errorf("%T.realName (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:realName: %s", p, err)
	}
	return err
}

func (p *CompassAccount) writeField21(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (21) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:status: %s", p, err)
		}
	}
	return err
}

func (p *CompassAccount) writeField90(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 90); err != nil {
		return fmt.Errorf("%T write field begin error 90:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (90) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 90:createTime: %s", p, err)
	}
	return err
}

func (p *CompassAccount) writeField91(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 91); err != nil {
		return fmt.Errorf("%T write field begin error 91:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (91) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 91:lastUpdate: %s", p, err)
	}
	return err
}

func (p *CompassAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompassAccount(%+v)", *p)
}
