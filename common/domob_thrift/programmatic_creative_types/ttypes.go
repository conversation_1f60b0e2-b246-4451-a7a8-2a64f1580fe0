// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package programmatic_creative_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/common/domob_thrift/ugc"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = rtb_types.GoUnusedProtection__
var _ = ugc.GoUnusedProtection__
var GoUnusedProtection__ int

type PCSStatus int64

const (
	PCSStatus_PCSS_SUCCESS      PCSStatus = 200
	PCSStatus_PCSS_REQ_ERROR    PCSStatus = 400
	PCSStatus_PCSS_NO_MATERIAL  PCSStatus = 404
	PCSStatus_PCSS_SERVER_ERROR PCSStatus = 500
)

func (p PCSStatus) String() string {
	switch p {
	case PCSStatus_PCSS_SUCCESS:
		return "PCSStatus_PCSS_SUCCESS"
	case PCSStatus_PCSS_REQ_ERROR:
		return "PCSStatus_PCSS_REQ_ERROR"
	case PCSStatus_PCSS_NO_MATERIAL:
		return "PCSStatus_PCSS_NO_MATERIAL"
	case PCSStatus_PCSS_SERVER_ERROR:
		return "PCSStatus_PCSS_SERVER_ERROR"
	}
	return "<UNSET>"
}

func PCSStatusFromString(s string) (PCSStatus, error) {
	switch s {
	case "PCSStatus_PCSS_SUCCESS":
		return PCSStatus_PCSS_SUCCESS, nil
	case "PCSStatus_PCSS_REQ_ERROR":
		return PCSStatus_PCSS_REQ_ERROR, nil
	case "PCSStatus_PCSS_NO_MATERIAL":
		return PCSStatus_PCSS_NO_MATERIAL, nil
	case "PCSStatus_PCSS_SERVER_ERROR":
		return PCSStatus_PCSS_SERVER_ERROR, nil
	}
	return PCSStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PCSStatus string")
}

type PCMaterialType int64

const (
	PCMaterialType_PCMT_IMAGE PCMaterialType = 1
	PCMaterialType_PCMT_TEXT  PCMaterialType = 2
	PCMaterialType_PCMT_VIDEO PCMaterialType = 3
)

func (p PCMaterialType) String() string {
	switch p {
	case PCMaterialType_PCMT_IMAGE:
		return "PCMaterialType_PCMT_IMAGE"
	case PCMaterialType_PCMT_TEXT:
		return "PCMaterialType_PCMT_TEXT"
	case PCMaterialType_PCMT_VIDEO:
		return "PCMaterialType_PCMT_VIDEO"
	}
	return "<UNSET>"
}

func PCMaterialTypeFromString(s string) (PCMaterialType, error) {
	switch s {
	case "PCMaterialType_PCMT_IMAGE":
		return PCMaterialType_PCMT_IMAGE, nil
	case "PCMaterialType_PCMT_TEXT":
		return PCMaterialType_PCMT_TEXT, nil
	case "PCMaterialType_PCMT_VIDEO":
		return PCMaterialType_PCMT_VIDEO, nil
	}
	return PCMaterialType(math.MinInt32 - 1), fmt.Errorf("not a valid PCMaterialType string")
}

//图像区域类型
type PCImageRegionType int64

const (
	PCImageRegionType_PCIRT_UNKNOWN    PCImageRegionType = 0
	PCImageRegionType_PCIRT_FOREGROUND PCImageRegionType = 1
	PCImageRegionType_PCIRT_BACKGROUND PCImageRegionType = 2
)

func (p PCImageRegionType) String() string {
	switch p {
	case PCImageRegionType_PCIRT_UNKNOWN:
		return "PCImageRegionType_PCIRT_UNKNOWN"
	case PCImageRegionType_PCIRT_FOREGROUND:
		return "PCImageRegionType_PCIRT_FOREGROUND"
	case PCImageRegionType_PCIRT_BACKGROUND:
		return "PCImageRegionType_PCIRT_BACKGROUND"
	}
	return "<UNSET>"
}

func PCImageRegionTypeFromString(s string) (PCImageRegionType, error) {
	switch s {
	case "PCImageRegionType_PCIRT_UNKNOWN":
		return PCImageRegionType_PCIRT_UNKNOWN, nil
	case "PCImageRegionType_PCIRT_FOREGROUND":
		return PCImageRegionType_PCIRT_FOREGROUND, nil
	case "PCImageRegionType_PCIRT_BACKGROUND":
		return PCImageRegionType_PCIRT_BACKGROUND, nil
	}
	return PCImageRegionType(math.MinInt32 - 1), fmt.Errorf("not a valid PCImageRegionType string")
}

type PCImageType int64

const (
	PCImageType_PCIT_UNKONOWN                 PCImageType = 0
	PCImageType_PCIT_LOGO                     PCImageType = 1
	PCImageType_PCIT_ROLE_HEAD                PCImageType = 2
	PCImageType_PCIT_ROLE_FULL                PCImageType = 3
	PCImageType_PCIT_TEXT                     PCImageType = 4
	PCImageType_PCIT_FULL_BACKGROUND          PCImageType = 5
	PCImageType_PCIT_ELE_BACKGROUND           PCImageType = 6
	PCImageType_PCIT_ELE                      PCImageType = 7
	PCImageType_PCIT_BTN                      PCImageType = 8
	PCImageType_PCIT_ANIMATION                PCImageType = 9
	PCImageType_PCIT_ITUNES_ICON              PCImageType = 10
	PCImageType_PCIT_ITUNES_ICON_THUMBNAIL    PCImageType = 11
	PCImageType_PCIT_ITUNES_IPHONE_SCREENSHOT PCImageType = 12
	PCImageType_PCIT_ITUNES_IPAD_SCRRENSHOT   PCImageType = 13
	PCImageType_PCIT_TEMPLATE_ELE             PCImageType = 20
	PCImageType_PCIT_MIXED                    PCImageType = 50
	PCImageType_PCIT_VIDEO_LP                 PCImageType = 60
	PCImageType_PCIT_CREATIVE                 PCImageType = 100
	PCImageType_PCIT_THUMBNAIL                PCImageType = 101
)

func (p PCImageType) String() string {
	switch p {
	case PCImageType_PCIT_UNKONOWN:
		return "PCImageType_PCIT_UNKONOWN"
	case PCImageType_PCIT_LOGO:
		return "PCImageType_PCIT_LOGO"
	case PCImageType_PCIT_ROLE_HEAD:
		return "PCImageType_PCIT_ROLE_HEAD"
	case PCImageType_PCIT_ROLE_FULL:
		return "PCImageType_PCIT_ROLE_FULL"
	case PCImageType_PCIT_TEXT:
		return "PCImageType_PCIT_TEXT"
	case PCImageType_PCIT_FULL_BACKGROUND:
		return "PCImageType_PCIT_FULL_BACKGROUND"
	case PCImageType_PCIT_ELE_BACKGROUND:
		return "PCImageType_PCIT_ELE_BACKGROUND"
	case PCImageType_PCIT_ELE:
		return "PCImageType_PCIT_ELE"
	case PCImageType_PCIT_BTN:
		return "PCImageType_PCIT_BTN"
	case PCImageType_PCIT_ANIMATION:
		return "PCImageType_PCIT_ANIMATION"
	case PCImageType_PCIT_ITUNES_ICON:
		return "PCImageType_PCIT_ITUNES_ICON"
	case PCImageType_PCIT_ITUNES_ICON_THUMBNAIL:
		return "PCImageType_PCIT_ITUNES_ICON_THUMBNAIL"
	case PCImageType_PCIT_ITUNES_IPHONE_SCREENSHOT:
		return "PCImageType_PCIT_ITUNES_IPHONE_SCREENSHOT"
	case PCImageType_PCIT_ITUNES_IPAD_SCRRENSHOT:
		return "PCImageType_PCIT_ITUNES_IPAD_SCRRENSHOT"
	case PCImageType_PCIT_TEMPLATE_ELE:
		return "PCImageType_PCIT_TEMPLATE_ELE"
	case PCImageType_PCIT_MIXED:
		return "PCImageType_PCIT_MIXED"
	case PCImageType_PCIT_VIDEO_LP:
		return "PCImageType_PCIT_VIDEO_LP"
	case PCImageType_PCIT_CREATIVE:
		return "PCImageType_PCIT_CREATIVE"
	case PCImageType_PCIT_THUMBNAIL:
		return "PCImageType_PCIT_THUMBNAIL"
	}
	return "<UNSET>"
}

func PCImageTypeFromString(s string) (PCImageType, error) {
	switch s {
	case "PCImageType_PCIT_UNKONOWN":
		return PCImageType_PCIT_UNKONOWN, nil
	case "PCImageType_PCIT_LOGO":
		return PCImageType_PCIT_LOGO, nil
	case "PCImageType_PCIT_ROLE_HEAD":
		return PCImageType_PCIT_ROLE_HEAD, nil
	case "PCImageType_PCIT_ROLE_FULL":
		return PCImageType_PCIT_ROLE_FULL, nil
	case "PCImageType_PCIT_TEXT":
		return PCImageType_PCIT_TEXT, nil
	case "PCImageType_PCIT_FULL_BACKGROUND":
		return PCImageType_PCIT_FULL_BACKGROUND, nil
	case "PCImageType_PCIT_ELE_BACKGROUND":
		return PCImageType_PCIT_ELE_BACKGROUND, nil
	case "PCImageType_PCIT_ELE":
		return PCImageType_PCIT_ELE, nil
	case "PCImageType_PCIT_BTN":
		return PCImageType_PCIT_BTN, nil
	case "PCImageType_PCIT_ANIMATION":
		return PCImageType_PCIT_ANIMATION, nil
	case "PCImageType_PCIT_ITUNES_ICON":
		return PCImageType_PCIT_ITUNES_ICON, nil
	case "PCImageType_PCIT_ITUNES_ICON_THUMBNAIL":
		return PCImageType_PCIT_ITUNES_ICON_THUMBNAIL, nil
	case "PCImageType_PCIT_ITUNES_IPHONE_SCREENSHOT":
		return PCImageType_PCIT_ITUNES_IPHONE_SCREENSHOT, nil
	case "PCImageType_PCIT_ITUNES_IPAD_SCRRENSHOT":
		return PCImageType_PCIT_ITUNES_IPAD_SCRRENSHOT, nil
	case "PCImageType_PCIT_TEMPLATE_ELE":
		return PCImageType_PCIT_TEMPLATE_ELE, nil
	case "PCImageType_PCIT_MIXED":
		return PCImageType_PCIT_MIXED, nil
	case "PCImageType_PCIT_VIDEO_LP":
		return PCImageType_PCIT_VIDEO_LP, nil
	case "PCImageType_PCIT_CREATIVE":
		return PCImageType_PCIT_CREATIVE, nil
	case "PCImageType_PCIT_THUMBNAIL":
		return PCImageType_PCIT_THUMBNAIL, nil
	}
	return PCImageType(math.MinInt32 - 1), fmt.Errorf("not a valid PCImageType string")
}

type PCImageStyle int64

const (
	PCImageStyle_PCIS_UNKNOWN PCImageStyle = 0
	PCImageStyle_PCIS_REALISM PCImageStyle = 1
	PCImageStyle_PCIS_CARTOON PCImageStyle = 2
)

func (p PCImageStyle) String() string {
	switch p {
	case PCImageStyle_PCIS_UNKNOWN:
		return "PCImageStyle_PCIS_UNKNOWN"
	case PCImageStyle_PCIS_REALISM:
		return "PCImageStyle_PCIS_REALISM"
	case PCImageStyle_PCIS_CARTOON:
		return "PCImageStyle_PCIS_CARTOON"
	}
	return "<UNSET>"
}

func PCImageStyleFromString(s string) (PCImageStyle, error) {
	switch s {
	case "PCImageStyle_PCIS_UNKNOWN":
		return PCImageStyle_PCIS_UNKNOWN, nil
	case "PCImageStyle_PCIS_REALISM":
		return PCImageStyle_PCIS_REALISM, nil
	case "PCImageStyle_PCIS_CARTOON":
		return PCImageStyle_PCIS_CARTOON, nil
	}
	return PCImageStyle(math.MinInt32 - 1), fmt.Errorf("not a valid PCImageStyle string")
}

type PCImageSourceType int64

const (
	PCImageSourceType_PCISRC_UNKNOWN           PCImageSourceType = 0
	PCImageSourceType_PCISRC_UPLOAD            PCImageSourceType = 1
	PCImageSourceType_PCISRC_COVERT            PCImageSourceType = 2
	PCImageSourceType_PCISRC_RM_WM             PCImageSourceType = 5
	PCImageSourceType_PCISRC_ITUNES            PCImageSourceType = 10
	PCImageSourceType_PCISRC_PLAY              PCImageSourceType = 11
	PCImageSourceType_PCISRC_CRAWLER_BAIDU     PCImageSourceType = 20
	PCImageSourceType_PCISRC_CRAWLER_OW        PCImageSourceType = 21
	PCImageSourceType_PCISRC_TEMPLATE_ASSEMBLY PCImageSourceType = 30
	PCImageSourceType_PCISRC_VIDEO_SNAPSHOT    PCImageSourceType = 35
)

func (p PCImageSourceType) String() string {
	switch p {
	case PCImageSourceType_PCISRC_UNKNOWN:
		return "PCImageSourceType_PCISRC_UNKNOWN"
	case PCImageSourceType_PCISRC_UPLOAD:
		return "PCImageSourceType_PCISRC_UPLOAD"
	case PCImageSourceType_PCISRC_COVERT:
		return "PCImageSourceType_PCISRC_COVERT"
	case PCImageSourceType_PCISRC_RM_WM:
		return "PCImageSourceType_PCISRC_RM_WM"
	case PCImageSourceType_PCISRC_ITUNES:
		return "PCImageSourceType_PCISRC_ITUNES"
	case PCImageSourceType_PCISRC_PLAY:
		return "PCImageSourceType_PCISRC_PLAY"
	case PCImageSourceType_PCISRC_CRAWLER_BAIDU:
		return "PCImageSourceType_PCISRC_CRAWLER_BAIDU"
	case PCImageSourceType_PCISRC_CRAWLER_OW:
		return "PCImageSourceType_PCISRC_CRAWLER_OW"
	case PCImageSourceType_PCISRC_TEMPLATE_ASSEMBLY:
		return "PCImageSourceType_PCISRC_TEMPLATE_ASSEMBLY"
	case PCImageSourceType_PCISRC_VIDEO_SNAPSHOT:
		return "PCImageSourceType_PCISRC_VIDEO_SNAPSHOT"
	}
	return "<UNSET>"
}

func PCImageSourceTypeFromString(s string) (PCImageSourceType, error) {
	switch s {
	case "PCImageSourceType_PCISRC_UNKNOWN":
		return PCImageSourceType_PCISRC_UNKNOWN, nil
	case "PCImageSourceType_PCISRC_UPLOAD":
		return PCImageSourceType_PCISRC_UPLOAD, nil
	case "PCImageSourceType_PCISRC_COVERT":
		return PCImageSourceType_PCISRC_COVERT, nil
	case "PCImageSourceType_PCISRC_RM_WM":
		return PCImageSourceType_PCISRC_RM_WM, nil
	case "PCImageSourceType_PCISRC_ITUNES":
		return PCImageSourceType_PCISRC_ITUNES, nil
	case "PCImageSourceType_PCISRC_PLAY":
		return PCImageSourceType_PCISRC_PLAY, nil
	case "PCImageSourceType_PCISRC_CRAWLER_BAIDU":
		return PCImageSourceType_PCISRC_CRAWLER_BAIDU, nil
	case "PCImageSourceType_PCISRC_CRAWLER_OW":
		return PCImageSourceType_PCISRC_CRAWLER_OW, nil
	case "PCImageSourceType_PCISRC_TEMPLATE_ASSEMBLY":
		return PCImageSourceType_PCISRC_TEMPLATE_ASSEMBLY, nil
	case "PCImageSourceType_PCISRC_VIDEO_SNAPSHOT":
		return PCImageSourceType_PCISRC_VIDEO_SNAPSHOT, nil
	}
	return PCImageSourceType(math.MinInt32 - 1), fmt.Errorf("not a valid PCImageSourceType string")
}

type PCTextType int64

const (
	PCTextType_PCTT_UNKONWN         PCTextType = 0
	PCTextType_PCTT_MODEL           PCTextType = 1
	PCTextType_PCTT_CRAWL_COMPETING PCTextType = 2
	PCTextType_PCTT_APP_FEATURE     PCTextType = 10
	PCTextType_PCTT_MOMO_TITLE      PCTextType = 20
)

func (p PCTextType) String() string {
	switch p {
	case PCTextType_PCTT_UNKONWN:
		return "PCTextType_PCTT_UNKONWN"
	case PCTextType_PCTT_MODEL:
		return "PCTextType_PCTT_MODEL"
	case PCTextType_PCTT_CRAWL_COMPETING:
		return "PCTextType_PCTT_CRAWL_COMPETING"
	case PCTextType_PCTT_APP_FEATURE:
		return "PCTextType_PCTT_APP_FEATURE"
	case PCTextType_PCTT_MOMO_TITLE:
		return "PCTextType_PCTT_MOMO_TITLE"
	}
	return "<UNSET>"
}

func PCTextTypeFromString(s string) (PCTextType, error) {
	switch s {
	case "PCTextType_PCTT_UNKONWN":
		return PCTextType_PCTT_UNKONWN, nil
	case "PCTextType_PCTT_MODEL":
		return PCTextType_PCTT_MODEL, nil
	case "PCTextType_PCTT_CRAWL_COMPETING":
		return PCTextType_PCTT_CRAWL_COMPETING, nil
	case "PCTextType_PCTT_APP_FEATURE":
		return PCTextType_PCTT_APP_FEATURE, nil
	case "PCTextType_PCTT_MOMO_TITLE":
		return PCTextType_PCTT_MOMO_TITLE, nil
	}
	return PCTextType(math.MinInt32 - 1), fmt.Errorf("not a valid PCTextType string")
}

type PCVideoType int64

const (
	PCVideoType_PCVT_UNKNOWN  PCVideoType = 0
	PCVideoType_PCVT_ORIGINAL PCVideoType = 1
	PCVideoType_PCVT_ADSVIDEO PCVideoType = 10
	PCVideoType_PCVT_DEMO     PCVideoType = 20
)

func (p PCVideoType) String() string {
	switch p {
	case PCVideoType_PCVT_UNKNOWN:
		return "PCVideoType_PCVT_UNKNOWN"
	case PCVideoType_PCVT_ORIGINAL:
		return "PCVideoType_PCVT_ORIGINAL"
	case PCVideoType_PCVT_ADSVIDEO:
		return "PCVideoType_PCVT_ADSVIDEO"
	case PCVideoType_PCVT_DEMO:
		return "PCVideoType_PCVT_DEMO"
	}
	return "<UNSET>"
}

func PCVideoTypeFromString(s string) (PCVideoType, error) {
	switch s {
	case "PCVideoType_PCVT_UNKNOWN":
		return PCVideoType_PCVT_UNKNOWN, nil
	case "PCVideoType_PCVT_ORIGINAL":
		return PCVideoType_PCVT_ORIGINAL, nil
	case "PCVideoType_PCVT_ADSVIDEO":
		return PCVideoType_PCVT_ADSVIDEO, nil
	case "PCVideoType_PCVT_DEMO":
		return PCVideoType_PCVT_DEMO, nil
	}
	return PCVideoType(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoType string")
}

type PCVideoSourceType int64

const (
	PCVideoSourceType_PCVST_UNKNOWN PCVideoSourceType = 0
	PCVideoSourceType_PCVST_UPLOAD  PCVideoSourceType = 1
	PCVideoSourceType_PCVST_CONVERT PCVideoSourceType = 2
	PCVideoSourceType_PCVST_RM_WM   PCVideoSourceType = 3
	PCVideoSourceType_PCVST_CUT     PCVideoSourceType = 4
	PCVideoSourceType_PCVST_YOUKU   PCVideoSourceType = 10
	PCVideoSourceType_PCVST_17173   PCVideoSourceType = 11
	PCVideoSourceType_PCVST_YOUTUBE PCVideoSourceType = 12
)

func (p PCVideoSourceType) String() string {
	switch p {
	case PCVideoSourceType_PCVST_UNKNOWN:
		return "PCVideoSourceType_PCVST_UNKNOWN"
	case PCVideoSourceType_PCVST_UPLOAD:
		return "PCVideoSourceType_PCVST_UPLOAD"
	case PCVideoSourceType_PCVST_CONVERT:
		return "PCVideoSourceType_PCVST_CONVERT"
	case PCVideoSourceType_PCVST_RM_WM:
		return "PCVideoSourceType_PCVST_RM_WM"
	case PCVideoSourceType_PCVST_CUT:
		return "PCVideoSourceType_PCVST_CUT"
	case PCVideoSourceType_PCVST_YOUKU:
		return "PCVideoSourceType_PCVST_YOUKU"
	case PCVideoSourceType_PCVST_17173:
		return "PCVideoSourceType_PCVST_17173"
	case PCVideoSourceType_PCVST_YOUTUBE:
		return "PCVideoSourceType_PCVST_YOUTUBE"
	}
	return "<UNSET>"
}

func PCVideoSourceTypeFromString(s string) (PCVideoSourceType, error) {
	switch s {
	case "PCVideoSourceType_PCVST_UNKNOWN":
		return PCVideoSourceType_PCVST_UNKNOWN, nil
	case "PCVideoSourceType_PCVST_UPLOAD":
		return PCVideoSourceType_PCVST_UPLOAD, nil
	case "PCVideoSourceType_PCVST_CONVERT":
		return PCVideoSourceType_PCVST_CONVERT, nil
	case "PCVideoSourceType_PCVST_RM_WM":
		return PCVideoSourceType_PCVST_RM_WM, nil
	case "PCVideoSourceType_PCVST_CUT":
		return PCVideoSourceType_PCVST_CUT, nil
	case "PCVideoSourceType_PCVST_YOUKU":
		return PCVideoSourceType_PCVST_YOUKU, nil
	case "PCVideoSourceType_PCVST_17173":
		return PCVideoSourceType_PCVST_17173, nil
	case "PCVideoSourceType_PCVST_YOUTUBE":
		return PCVideoSourceType_PCVST_YOUTUBE, nil
	}
	return PCVideoSourceType(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoSourceType string")
}

type PCVideoCodecType int64

const (
	PCVideoCodecType_PCVCT_UNKNOWN PCVideoCodecType = 0
	PCVideoCodecType_PCVCT_MPEG4   PCVideoCodecType = 1
	PCVideoCodecType_PCVCT_WEBM    PCVideoCodecType = 2
	PCVideoCodecType_PCVCT_OGG     PCVideoCodecType = 3
	PCVideoCodecType_PCVCT_OTHER   PCVideoCodecType = 100
)

func (p PCVideoCodecType) String() string {
	switch p {
	case PCVideoCodecType_PCVCT_UNKNOWN:
		return "PCVideoCodecType_PCVCT_UNKNOWN"
	case PCVideoCodecType_PCVCT_MPEG4:
		return "PCVideoCodecType_PCVCT_MPEG4"
	case PCVideoCodecType_PCVCT_WEBM:
		return "PCVideoCodecType_PCVCT_WEBM"
	case PCVideoCodecType_PCVCT_OGG:
		return "PCVideoCodecType_PCVCT_OGG"
	case PCVideoCodecType_PCVCT_OTHER:
		return "PCVideoCodecType_PCVCT_OTHER"
	}
	return "<UNSET>"
}

func PCVideoCodecTypeFromString(s string) (PCVideoCodecType, error) {
	switch s {
	case "PCVideoCodecType_PCVCT_UNKNOWN":
		return PCVideoCodecType_PCVCT_UNKNOWN, nil
	case "PCVideoCodecType_PCVCT_MPEG4":
		return PCVideoCodecType_PCVCT_MPEG4, nil
	case "PCVideoCodecType_PCVCT_WEBM":
		return PCVideoCodecType_PCVCT_WEBM, nil
	case "PCVideoCodecType_PCVCT_OGG":
		return PCVideoCodecType_PCVCT_OGG, nil
	case "PCVideoCodecType_PCVCT_OTHER":
		return PCVideoCodecType_PCVCT_OTHER, nil
	}
	return PCVideoCodecType(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoCodecType string")
}

type PCVideoStatus int64

const (
	PCVideoStatus_PCVS_SUCCESS        PCVideoStatus = 0
	PCVideoStatus_PCVS_ERROR          PCVideoStatus = 1
	PCVideoStatus_PCVS_SIZE_ERROR     PCVideoStatus = 10
	PCVideoStatus_PCVS_DURATION_ERROR PCVideoStatus = 11
	PCVideoStatus_PCVS_CODEC_ERROR    PCVideoStatus = 12
	PCVideoStatus_PCVS_ADX_NOT_EXISTS PCVideoStatus = 20
)

func (p PCVideoStatus) String() string {
	switch p {
	case PCVideoStatus_PCVS_SUCCESS:
		return "PCVideoStatus_PCVS_SUCCESS"
	case PCVideoStatus_PCVS_ERROR:
		return "PCVideoStatus_PCVS_ERROR"
	case PCVideoStatus_PCVS_SIZE_ERROR:
		return "PCVideoStatus_PCVS_SIZE_ERROR"
	case PCVideoStatus_PCVS_DURATION_ERROR:
		return "PCVideoStatus_PCVS_DURATION_ERROR"
	case PCVideoStatus_PCVS_CODEC_ERROR:
		return "PCVideoStatus_PCVS_CODEC_ERROR"
	case PCVideoStatus_PCVS_ADX_NOT_EXISTS:
		return "PCVideoStatus_PCVS_ADX_NOT_EXISTS"
	}
	return "<UNSET>"
}

func PCVideoStatusFromString(s string) (PCVideoStatus, error) {
	switch s {
	case "PCVideoStatus_PCVS_SUCCESS":
		return PCVideoStatus_PCVS_SUCCESS, nil
	case "PCVideoStatus_PCVS_ERROR":
		return PCVideoStatus_PCVS_ERROR, nil
	case "PCVideoStatus_PCVS_SIZE_ERROR":
		return PCVideoStatus_PCVS_SIZE_ERROR, nil
	case "PCVideoStatus_PCVS_DURATION_ERROR":
		return PCVideoStatus_PCVS_DURATION_ERROR, nil
	case "PCVideoStatus_PCVS_CODEC_ERROR":
		return PCVideoStatus_PCVS_CODEC_ERROR, nil
	case "PCVideoStatus_PCVS_ADX_NOT_EXISTS":
		return PCVideoStatus_PCVS_ADX_NOT_EXISTS, nil
	}
	return PCVideoStatus(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoStatus string")
}

type CreativeType int64

const (
	CreativeType_PCCT_UNKOWN   CreativeType = 0
	CreativeType_PCCT_IMAGE    CreativeType = 10
	CreativeType_PCCT_TEXT     CreativeType = 20
	CreativeType_PCCT_HTML     CreativeType = 30
	CreativeType_PCCT_MIX      CreativeType = 40
	CreativeType_PCCT_AUTO_MIX CreativeType = 41
	CreativeType_PCCT_VIDEO    CreativeType = 50
)

func (p CreativeType) String() string {
	switch p {
	case CreativeType_PCCT_UNKOWN:
		return "CreativeType_PCCT_UNKOWN"
	case CreativeType_PCCT_IMAGE:
		return "CreativeType_PCCT_IMAGE"
	case CreativeType_PCCT_TEXT:
		return "CreativeType_PCCT_TEXT"
	case CreativeType_PCCT_HTML:
		return "CreativeType_PCCT_HTML"
	case CreativeType_PCCT_MIX:
		return "CreativeType_PCCT_MIX"
	case CreativeType_PCCT_AUTO_MIX:
		return "CreativeType_PCCT_AUTO_MIX"
	case CreativeType_PCCT_VIDEO:
		return "CreativeType_PCCT_VIDEO"
	}
	return "<UNSET>"
}

func CreativeTypeFromString(s string) (CreativeType, error) {
	switch s {
	case "CreativeType_PCCT_UNKOWN":
		return CreativeType_PCCT_UNKOWN, nil
	case "CreativeType_PCCT_IMAGE":
		return CreativeType_PCCT_IMAGE, nil
	case "CreativeType_PCCT_TEXT":
		return CreativeType_PCCT_TEXT, nil
	case "CreativeType_PCCT_HTML":
		return CreativeType_PCCT_HTML, nil
	case "CreativeType_PCCT_MIX":
		return CreativeType_PCCT_MIX, nil
	case "CreativeType_PCCT_AUTO_MIX":
		return CreativeType_PCCT_AUTO_MIX, nil
	case "CreativeType_PCCT_VIDEO":
		return CreativeType_PCCT_VIDEO, nil
	}
	return CreativeType(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeType string")
}

type PCCategoryizeCreativeType int64

const (
	PCCategoryizeCreativeType_ONLY_IMAGE           PCCategoryizeCreativeType = 1
	PCCategoryizeCreativeType_ONLY_VIDEO           PCCategoryizeCreativeType = 2
	PCCategoryizeCreativeType_BOTH_IMAGE_AND_VIDEO PCCategoryizeCreativeType = 3
)

func (p PCCategoryizeCreativeType) String() string {
	switch p {
	case PCCategoryizeCreativeType_ONLY_IMAGE:
		return "PCCategoryizeCreativeType_ONLY_IMAGE"
	case PCCategoryizeCreativeType_ONLY_VIDEO:
		return "PCCategoryizeCreativeType_ONLY_VIDEO"
	case PCCategoryizeCreativeType_BOTH_IMAGE_AND_VIDEO:
		return "PCCategoryizeCreativeType_BOTH_IMAGE_AND_VIDEO"
	}
	return "<UNSET>"
}

func PCCategoryizeCreativeTypeFromString(s string) (PCCategoryizeCreativeType, error) {
	switch s {
	case "PCCategoryizeCreativeType_ONLY_IMAGE":
		return PCCategoryizeCreativeType_ONLY_IMAGE, nil
	case "PCCategoryizeCreativeType_ONLY_VIDEO":
		return PCCategoryizeCreativeType_ONLY_VIDEO, nil
	case "PCCategoryizeCreativeType_BOTH_IMAGE_AND_VIDEO":
		return PCCategoryizeCreativeType_BOTH_IMAGE_AND_VIDEO, nil
	}
	return PCCategoryizeCreativeType(math.MinInt32 - 1), fmt.Errorf("not a valid PCCategoryizeCreativeType string")
}

type PCCategorizeSimilarCreativeType int64

const (
	PCCategorizeSimilarCreativeType_ONLY_MEDIA          PCCategorizeSimilarCreativeType = 1
	PCCategorizeSimilarCreativeType_ONLY_TEXT           PCCategorizeSimilarCreativeType = 2
	PCCategorizeSimilarCreativeType_BOTH_MEDIA_AND_TEXT PCCategorizeSimilarCreativeType = 3
)

func (p PCCategorizeSimilarCreativeType) String() string {
	switch p {
	case PCCategorizeSimilarCreativeType_ONLY_MEDIA:
		return "PCCategorizeSimilarCreativeType_ONLY_MEDIA"
	case PCCategorizeSimilarCreativeType_ONLY_TEXT:
		return "PCCategorizeSimilarCreativeType_ONLY_TEXT"
	case PCCategorizeSimilarCreativeType_BOTH_MEDIA_AND_TEXT:
		return "PCCategorizeSimilarCreativeType_BOTH_MEDIA_AND_TEXT"
	}
	return "<UNSET>"
}

func PCCategorizeSimilarCreativeTypeFromString(s string) (PCCategorizeSimilarCreativeType, error) {
	switch s {
	case "PCCategorizeSimilarCreativeType_ONLY_MEDIA":
		return PCCategorizeSimilarCreativeType_ONLY_MEDIA, nil
	case "PCCategorizeSimilarCreativeType_ONLY_TEXT":
		return PCCategorizeSimilarCreativeType_ONLY_TEXT, nil
	case "PCCategorizeSimilarCreativeType_BOTH_MEDIA_AND_TEXT":
		return PCCategorizeSimilarCreativeType_BOTH_MEDIA_AND_TEXT, nil
	}
	return PCCategorizeSimilarCreativeType(math.MinInt32 - 1), fmt.Errorf("not a valid PCCategorizeSimilarCreativeType string")
}

type PCHTML2ImageResultType int64

const (
	PCHTML2ImageResultType_PCHIRT_BINARY_CODE PCHTML2ImageResultType = 0
	PCHTML2ImageResultType_PCHIRT_UGC_URL     PCHTML2ImageResultType = 1
)

func (p PCHTML2ImageResultType) String() string {
	switch p {
	case PCHTML2ImageResultType_PCHIRT_BINARY_CODE:
		return "PCHTML2ImageResultType_PCHIRT_BINARY_CODE"
	case PCHTML2ImageResultType_PCHIRT_UGC_URL:
		return "PCHTML2ImageResultType_PCHIRT_UGC_URL"
	}
	return "<UNSET>"
}

func PCHTML2ImageResultTypeFromString(s string) (PCHTML2ImageResultType, error) {
	switch s {
	case "PCHTML2ImageResultType_PCHIRT_BINARY_CODE":
		return PCHTML2ImageResultType_PCHIRT_BINARY_CODE, nil
	case "PCHTML2ImageResultType_PCHIRT_UGC_URL":
		return PCHTML2ImageResultType_PCHIRT_UGC_URL, nil
	}
	return PCHTML2ImageResultType(math.MinInt32 - 1), fmt.Errorf("not a valid PCHTML2ImageResultType string")
}

type PCHTML2ImageFileFormat int64

const (
	PCHTML2ImageFileFormat_PCHIFT_PNG PCHTML2ImageFileFormat = 0
	PCHTML2ImageFileFormat_PCHIFT_JPG PCHTML2ImageFileFormat = 1
)

func (p PCHTML2ImageFileFormat) String() string {
	switch p {
	case PCHTML2ImageFileFormat_PCHIFT_PNG:
		return "PCHTML2ImageFileFormat_PCHIFT_PNG"
	case PCHTML2ImageFileFormat_PCHIFT_JPG:
		return "PCHTML2ImageFileFormat_PCHIFT_JPG"
	}
	return "<UNSET>"
}

func PCHTML2ImageFileFormatFromString(s string) (PCHTML2ImageFileFormat, error) {
	switch s {
	case "PCHTML2ImageFileFormat_PCHIFT_PNG":
		return PCHTML2ImageFileFormat_PCHIFT_PNG, nil
	case "PCHTML2ImageFileFormat_PCHIFT_JPG":
		return PCHTML2ImageFileFormat_PCHIFT_JPG, nil
	}
	return PCHTML2ImageFileFormat(math.MinInt32 - 1), fmt.Errorf("not a valid PCHTML2ImageFileFormat string")
}

type PCVideoWatermarkType int64

const (
	PCVideoWatermarkType_PCRWT_CUSTOM        PCVideoWatermarkType = 0
	PCVideoWatermarkType_PCRWT_YOUKU_BOTH    PCVideoWatermarkType = 10
	PCVideoWatermarkType_PCRWT_YOUKU_REGULAR PCVideoWatermarkType = 11
	PCVideoWatermarkType_PCRWT_17173_FIXED   PCVideoWatermarkType = 20
	PCVideoWatermarkType_PCRWT_OTHERS        PCVideoWatermarkType = 50
)

func (p PCVideoWatermarkType) String() string {
	switch p {
	case PCVideoWatermarkType_PCRWT_CUSTOM:
		return "PCVideoWatermarkType_PCRWT_CUSTOM"
	case PCVideoWatermarkType_PCRWT_YOUKU_BOTH:
		return "PCVideoWatermarkType_PCRWT_YOUKU_BOTH"
	case PCVideoWatermarkType_PCRWT_YOUKU_REGULAR:
		return "PCVideoWatermarkType_PCRWT_YOUKU_REGULAR"
	case PCVideoWatermarkType_PCRWT_17173_FIXED:
		return "PCVideoWatermarkType_PCRWT_17173_FIXED"
	case PCVideoWatermarkType_PCRWT_OTHERS:
		return "PCVideoWatermarkType_PCRWT_OTHERS"
	}
	return "<UNSET>"
}

func PCVideoWatermarkTypeFromString(s string) (PCVideoWatermarkType, error) {
	switch s {
	case "PCVideoWatermarkType_PCRWT_CUSTOM":
		return PCVideoWatermarkType_PCRWT_CUSTOM, nil
	case "PCVideoWatermarkType_PCRWT_YOUKU_BOTH":
		return PCVideoWatermarkType_PCRWT_YOUKU_BOTH, nil
	case "PCVideoWatermarkType_PCRWT_YOUKU_REGULAR":
		return PCVideoWatermarkType_PCRWT_YOUKU_REGULAR, nil
	case "PCVideoWatermarkType_PCRWT_17173_FIXED":
		return PCVideoWatermarkType_PCRWT_17173_FIXED, nil
	case "PCVideoWatermarkType_PCRWT_OTHERS":
		return PCVideoWatermarkType_PCRWT_OTHERS, nil
	}
	return PCVideoWatermarkType(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoWatermarkType string")
}

type PCVideoOverlayType int64

const (
	PCVideoOverlayType_PCVOT_BUTTON     PCVideoOverlayType = 1
	PCVideoOverlayType_PCVOT_LOGO       PCVideoOverlayType = 2
	PCVideoOverlayType_PCVOT_BAR        PCVideoOverlayType = 10
	PCVideoOverlayType_PCVOT_BOTTOM_BAR PCVideoOverlayType = 11
)

func (p PCVideoOverlayType) String() string {
	switch p {
	case PCVideoOverlayType_PCVOT_BUTTON:
		return "PCVideoOverlayType_PCVOT_BUTTON"
	case PCVideoOverlayType_PCVOT_LOGO:
		return "PCVideoOverlayType_PCVOT_LOGO"
	case PCVideoOverlayType_PCVOT_BAR:
		return "PCVideoOverlayType_PCVOT_BAR"
	case PCVideoOverlayType_PCVOT_BOTTOM_BAR:
		return "PCVideoOverlayType_PCVOT_BOTTOM_BAR"
	}
	return "<UNSET>"
}

func PCVideoOverlayTypeFromString(s string) (PCVideoOverlayType, error) {
	switch s {
	case "PCVideoOverlayType_PCVOT_BUTTON":
		return PCVideoOverlayType_PCVOT_BUTTON, nil
	case "PCVideoOverlayType_PCVOT_LOGO":
		return PCVideoOverlayType_PCVOT_LOGO, nil
	case "PCVideoOverlayType_PCVOT_BAR":
		return PCVideoOverlayType_PCVOT_BAR, nil
	case "PCVideoOverlayType_PCVOT_BOTTOM_BAR":
		return PCVideoOverlayType_PCVOT_BOTTOM_BAR, nil
	}
	return PCVideoOverlayType(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoOverlayType string")
}

type PCVideoOverlayFormat int64

const (
	PCVideoOverlayFormat_PCVOF_UNKNOWN PCVideoOverlayFormat = 0
	PCVideoOverlayFormat_PCVOF_JPG     PCVideoOverlayFormat = 1
	PCVideoOverlayFormat_PCVOF_PNG     PCVideoOverlayFormat = 2
	PCVideoOverlayFormat_PCVOF_MOV     PCVideoOverlayFormat = 10
	PCVideoOverlayFormat_PCVOF_OTHER   PCVideoOverlayFormat = 50
)

func (p PCVideoOverlayFormat) String() string {
	switch p {
	case PCVideoOverlayFormat_PCVOF_UNKNOWN:
		return "PCVideoOverlayFormat_PCVOF_UNKNOWN"
	case PCVideoOverlayFormat_PCVOF_JPG:
		return "PCVideoOverlayFormat_PCVOF_JPG"
	case PCVideoOverlayFormat_PCVOF_PNG:
		return "PCVideoOverlayFormat_PCVOF_PNG"
	case PCVideoOverlayFormat_PCVOF_MOV:
		return "PCVideoOverlayFormat_PCVOF_MOV"
	case PCVideoOverlayFormat_PCVOF_OTHER:
		return "PCVideoOverlayFormat_PCVOF_OTHER"
	}
	return "<UNSET>"
}

func PCVideoOverlayFormatFromString(s string) (PCVideoOverlayFormat, error) {
	switch s {
	case "PCVideoOverlayFormat_PCVOF_UNKNOWN":
		return PCVideoOverlayFormat_PCVOF_UNKNOWN, nil
	case "PCVideoOverlayFormat_PCVOF_JPG":
		return PCVideoOverlayFormat_PCVOF_JPG, nil
	case "PCVideoOverlayFormat_PCVOF_PNG":
		return PCVideoOverlayFormat_PCVOF_PNG, nil
	case "PCVideoOverlayFormat_PCVOF_MOV":
		return PCVideoOverlayFormat_PCVOF_MOV, nil
	case "PCVideoOverlayFormat_PCVOF_OTHER":
		return PCVideoOverlayFormat_PCVOF_OTHER, nil
	}
	return PCVideoOverlayFormat(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoOverlayFormat string")
}

type PCVideoVolumeType int64

const (
	PCVideoVolumeType_PCVVT_NORMAL    PCVideoVolumeType = 0
	PCVideoVolumeType_PCVVT_MUTE      PCVideoVolumeType = 1
	PCVideoVolumeType_PCVVT_FADE_IN   PCVideoVolumeType = 10
	PCVideoVolumeType_PCVVT_FADE_OUT  PCVideoVolumeType = 11
	PCVideoVolumeType_PCVVT_FADE_BOTH PCVideoVolumeType = 12
)

func (p PCVideoVolumeType) String() string {
	switch p {
	case PCVideoVolumeType_PCVVT_NORMAL:
		return "PCVideoVolumeType_PCVVT_NORMAL"
	case PCVideoVolumeType_PCVVT_MUTE:
		return "PCVideoVolumeType_PCVVT_MUTE"
	case PCVideoVolumeType_PCVVT_FADE_IN:
		return "PCVideoVolumeType_PCVVT_FADE_IN"
	case PCVideoVolumeType_PCVVT_FADE_OUT:
		return "PCVideoVolumeType_PCVVT_FADE_OUT"
	case PCVideoVolumeType_PCVVT_FADE_BOTH:
		return "PCVideoVolumeType_PCVVT_FADE_BOTH"
	}
	return "<UNSET>"
}

func PCVideoVolumeTypeFromString(s string) (PCVideoVolumeType, error) {
	switch s {
	case "PCVideoVolumeType_PCVVT_NORMAL":
		return PCVideoVolumeType_PCVVT_NORMAL, nil
	case "PCVideoVolumeType_PCVVT_MUTE":
		return PCVideoVolumeType_PCVVT_MUTE, nil
	case "PCVideoVolumeType_PCVVT_FADE_IN":
		return PCVideoVolumeType_PCVVT_FADE_IN, nil
	case "PCVideoVolumeType_PCVVT_FADE_OUT":
		return PCVideoVolumeType_PCVVT_FADE_OUT, nil
	case "PCVideoVolumeType_PCVVT_FADE_BOTH":
		return PCVideoVolumeType_PCVVT_FADE_BOTH, nil
	}
	return PCVideoVolumeType(math.MinInt32 - 1), fmt.Errorf("not a valid PCVideoVolumeType string")
}

type PCProcessImageType int64

const (
	PCProcessImageType_UNKNOOWN PCProcessImageType = 0
	PCProcessImageType_RESIZE   PCProcessImageType = 1
	PCProcessImageType_CROPPING PCProcessImageType = 2
)

func (p PCProcessImageType) String() string {
	switch p {
	case PCProcessImageType_UNKNOOWN:
		return "PCProcessImageType_UNKNOOWN"
	case PCProcessImageType_RESIZE:
		return "PCProcessImageType_RESIZE"
	case PCProcessImageType_CROPPING:
		return "PCProcessImageType_CROPPING"
	}
	return "<UNSET>"
}

func PCProcessImageTypeFromString(s string) (PCProcessImageType, error) {
	switch s {
	case "PCProcessImageType_UNKNOOWN":
		return PCProcessImageType_UNKNOOWN, nil
	case "PCProcessImageType_RESIZE":
		return PCProcessImageType_RESIZE, nil
	case "PCProcessImageType_CROPPING":
		return PCProcessImageType_CROPPING, nil
	}
	return PCProcessImageType(math.MinInt32 - 1), fmt.Errorf("not a valid PCProcessImageType string")
}

type PCMediaSeqVideoImageEffectType int64

const (
	PCMediaSeqVideoImageEffectType_PCET_ZOOM_OUT PCMediaSeqVideoImageEffectType = 1
)

func (p PCMediaSeqVideoImageEffectType) String() string {
	switch p {
	case PCMediaSeqVideoImageEffectType_PCET_ZOOM_OUT:
		return "PCMediaSeqVideoImageEffectType_PCET_ZOOM_OUT"
	}
	return "<UNSET>"
}

func PCMediaSeqVideoImageEffectTypeFromString(s string) (PCMediaSeqVideoImageEffectType, error) {
	switch s {
	case "PCMediaSeqVideoImageEffectType_PCET_ZOOM_OUT":
		return PCMediaSeqVideoImageEffectType_PCET_ZOOM_OUT, nil
	}
	return PCMediaSeqVideoImageEffectType(math.MinInt32 - 1), fmt.Errorf("not a valid PCMediaSeqVideoImageEffectType string")
}

type PCMediaSeqVideoTransitionEffectType int64

const (
	PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_FADE   PCMediaSeqVideoTransitionEffectType = 1
	PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_CIRCLE PCMediaSeqVideoTransitionEffectType = 2
)

func (p PCMediaSeqVideoTransitionEffectType) String() string {
	switch p {
	case PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_FADE:
		return "PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_FADE"
	case PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_CIRCLE:
		return "PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_CIRCLE"
	}
	return "<UNSET>"
}

func PCMediaSeqVideoTransitionEffectTypeFromString(s string) (PCMediaSeqVideoTransitionEffectType, error) {
	switch s {
	case "PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_FADE":
		return PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_FADE, nil
	case "PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_CIRCLE":
		return PCMediaSeqVideoTransitionEffectType_PCTET_CROSS_CIRCLE, nil
	}
	return PCMediaSeqVideoTransitionEffectType(math.MinInt32 - 1), fmt.Errorf("not a valid PCMediaSeqVideoTransitionEffectType string")
}

type PCMediaSeqVideoOverlayEffectType int64

const (
	PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN     PCMediaSeqVideoOverlayEffectType = 1
	PCMediaSeqVideoOverlayEffectType_PCOET_FADE_OUT    PCMediaSeqVideoOverlayEffectType = 2
	PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN_OUT PCMediaSeqVideoOverlayEffectType = 3
)

func (p PCMediaSeqVideoOverlayEffectType) String() string {
	switch p {
	case PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN:
		return "PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN"
	case PCMediaSeqVideoOverlayEffectType_PCOET_FADE_OUT:
		return "PCMediaSeqVideoOverlayEffectType_PCOET_FADE_OUT"
	case PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN_OUT:
		return "PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN_OUT"
	}
	return "<UNSET>"
}

func PCMediaSeqVideoOverlayEffectTypeFromString(s string) (PCMediaSeqVideoOverlayEffectType, error) {
	switch s {
	case "PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN":
		return PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN, nil
	case "PCMediaSeqVideoOverlayEffectType_PCOET_FADE_OUT":
		return PCMediaSeqVideoOverlayEffectType_PCOET_FADE_OUT, nil
	case "PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN_OUT":
		return PCMediaSeqVideoOverlayEffectType_PCOET_FADE_IN_OUT, nil
	}
	return PCMediaSeqVideoOverlayEffectType(math.MinInt32 - 1), fmt.Errorf("not a valid PCMediaSeqVideoOverlayEffectType string")
}

type PCMediaSeqMediaType int64

const (
	PCMediaSeqMediaType_PCSMT_VIDEO PCMediaSeqMediaType = 1
	PCMediaSeqMediaType_PCSMT_IMG   PCMediaSeqMediaType = 2
)

func (p PCMediaSeqMediaType) String() string {
	switch p {
	case PCMediaSeqMediaType_PCSMT_VIDEO:
		return "PCMediaSeqMediaType_PCSMT_VIDEO"
	case PCMediaSeqMediaType_PCSMT_IMG:
		return "PCMediaSeqMediaType_PCSMT_IMG"
	}
	return "<UNSET>"
}

func PCMediaSeqMediaTypeFromString(s string) (PCMediaSeqMediaType, error) {
	switch s {
	case "PCMediaSeqMediaType_PCSMT_VIDEO":
		return PCMediaSeqMediaType_PCSMT_VIDEO, nil
	case "PCMediaSeqMediaType_PCSMT_IMG":
		return PCMediaSeqMediaType_PCSMT_IMG, nil
	}
	return PCMediaSeqMediaType(math.MinInt32 - 1), fmt.Errorf("not a valid PCMediaSeqMediaType string")
}

type PCSException struct {
	Code    PCSStatus `thrift:"code,1" json:"code"`
	Message string    `thrift:"message,2" json:"message"`
}

func NewPCSException() *PCSException {
	return &PCSException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCSException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *PCSException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCSException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = PCSStatus(v)
	}
	return nil
}

func (p *PCSException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *PCSException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCSException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCSException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *PCSException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *PCSException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCSException(%+v)", *p)
}

type PCSSize struct {
	Width  int32 `thrift:"width,1" json:"width"`
	Height int32 `thrift:"height,2" json:"height"`
}

func NewPCSSize() *PCSSize {
	return &PCSSize{}
}

func (p *PCSSize) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCSSize) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *PCSSize) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *PCSSize) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCSSize"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCSSize) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:width: %s", p, err)
	}
	return err
}

func (p *PCSSize) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:height: %s", p, err)
	}
	return err
}

func (p *PCSSize) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCSSize(%+v)", *p)
}

type PCSPosition struct {
	Left int32 `thrift:"left,1" json:"left"`
	Top  int32 `thrift:"top,2" json:"top"`
}

func NewPCSPosition() *PCSPosition {
	return &PCSPosition{}
}

func (p *PCSPosition) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCSPosition) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Left = v
	}
	return nil
}

func (p *PCSPosition) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Top = v
	}
	return nil
}

func (p *PCSPosition) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCSPosition"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCSPosition) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("left", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:left: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Left)); err != nil {
		return fmt.Errorf("%T.left (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:left: %s", p, err)
	}
	return err
}

func (p *PCSPosition) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("top", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:top: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Top)); err != nil {
		return fmt.Errorf("%T.top (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:top: %s", p, err)
	}
	return err
}

func (p *PCSPosition) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCSPosition(%+v)", *p)
}

type PCImageColor struct {
	BgColor   int32 `thrift:"bg_color,1" json:"bg_color"`
	BgVisible bool  `thrift:"bg_visible,2" json:"bg_visible"`
	FgColor   int32 `thrift:"fg_color,3" json:"fg_color"`
}

func NewPCImageColor() *PCImageColor {
	return &PCImageColor{}
}

func (p *PCImageColor) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCImageColor) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BgColor = v
	}
	return nil
}

func (p *PCImageColor) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.BgVisible = v
	}
	return nil
}

func (p *PCImageColor) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FgColor = v
	}
	return nil
}

func (p *PCImageColor) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCImageColor"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCImageColor) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bg_color", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bg_color: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BgColor)); err != nil {
		return fmt.Errorf("%T.bg_color (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bg_color: %s", p, err)
	}
	return err
}

func (p *PCImageColor) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bg_visible", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:bg_visible: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.BgVisible)); err != nil {
		return fmt.Errorf("%T.bg_visible (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:bg_visible: %s", p, err)
	}
	return err
}

func (p *PCImageColor) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fg_color", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fg_color: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FgColor)); err != nil {
		return fmt.Errorf("%T.fg_color (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fg_color: %s", p, err)
	}
	return err
}

func (p *PCImageColor) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCImageColor(%+v)", *p)
}

type PCImagePixel struct {
	PosX    int32         `thrift:"pos_x,1" json:"pos_x"`
	PosY    int32         `thrift:"pos_y,2" json:"pos_y"`
	Channel int32         `thrift:"channel,3" json:"channel"`
	Color   *PCImageColor `thrift:"color,4" json:"color"`
}

func NewPCImagePixel() *PCImagePixel {
	return &PCImagePixel{}
}

func (p *PCImagePixel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCImagePixel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PosX = v
	}
	return nil
}

func (p *PCImagePixel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PosY = v
	}
	return nil
}

func (p *PCImagePixel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *PCImagePixel) readField4(iprot thrift.TProtocol) error {
	p.Color = NewPCImageColor()
	if err := p.Color.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Color)
	}
	return nil
}

func (p *PCImagePixel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCImagePixel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCImagePixel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pos_x", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pos_x: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PosX)); err != nil {
		return fmt.Errorf("%T.pos_x (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pos_x: %s", p, err)
	}
	return err
}

func (p *PCImagePixel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pos_y", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pos_y: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PosY)); err != nil {
		return fmt.Errorf("%T.pos_y (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pos_y: %s", p, err)
	}
	return err
}

func (p *PCImagePixel) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channel: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channel: %s", p, err)
	}
	return err
}

func (p *PCImagePixel) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Color != nil {
		if err := oprot.WriteFieldBegin("color", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:color: %s", p, err)
		}
		if err := p.Color.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Color)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:color: %s", p, err)
		}
	}
	return err
}

func (p *PCImagePixel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCImagePixel(%+v)", *p)
}

type PCImageRegionInfo struct {
	RegionType PCImageRegionType `thrift:"region_type,1" json:"region_type"`
	PixelInfo  []*PCImagePixel   `thrift:"pixel_info,2" json:"pixel_info"`
}

func NewPCImageRegionInfo() *PCImageRegionInfo {
	return &PCImageRegionInfo{
		RegionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCImageRegionInfo) IsSetRegionType() bool {
	return int64(p.RegionType) != math.MinInt32-1
}

func (p *PCImageRegionInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCImageRegionInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.RegionType = PCImageRegionType(v)
	}
	return nil
}

func (p *PCImageRegionInfo) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PixelInfo = make([]*PCImagePixel, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewPCImagePixel()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.PixelInfo = append(p.PixelInfo, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCImageRegionInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCImageRegionInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCImageRegionInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetRegionType() {
		if err := oprot.WriteFieldBegin("region_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:region_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RegionType)); err != nil {
			return fmt.Errorf("%T.region_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:region_type: %s", p, err)
		}
	}
	return err
}

func (p *PCImageRegionInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.PixelInfo != nil {
		if err := oprot.WriteFieldBegin("pixel_info", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:pixel_info: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PixelInfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PixelInfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:pixel_info: %s", p, err)
		}
	}
	return err
}

func (p *PCImageRegionInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCImageRegionInfo(%+v)", *p)
}

type PCImageSource struct {
	TypeA1      PCImageSourceType `thrift:"type,1" json:"type"`
	ParentHash  string            `thrift:"parent_hash,2" json:"parent_hash"`
	SourceUrl   string            `thrift:"source_url,3" json:"source_url"`
	SourceTitle string            `thrift:"source_title,4" json:"source_title"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	TemplateId        int32   `thrift:"template_id,10" json:"template_id"`
	SourceImageIdList []int32 `thrift:"source_image_id_list,11" json:"source_image_id_list"`
}

func NewPCImageSource() *PCImageSource {
	return &PCImageSource{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCImageSource) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PCImageSource) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCImageSource) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = PCImageSourceType(v)
	}
	return nil
}

func (p *PCImageSource) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ParentHash = v
	}
	return nil
}

func (p *PCImageSource) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SourceUrl = v
	}
	return nil
}

func (p *PCImageSource) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SourceTitle = v
	}
	return nil
}

func (p *PCImageSource) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TemplateId = v
	}
	return nil
}

func (p *PCImageSource) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SourceImageIdList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.SourceImageIdList = append(p.SourceImageIdList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCImageSource) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCImageSource"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCImageSource) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *PCImageSource) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_hash", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:parent_hash: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ParentHash)); err != nil {
		return fmt.Errorf("%T.parent_hash (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:parent_hash: %s", p, err)
	}
	return err
}

func (p *PCImageSource) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source_url", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:source_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SourceUrl)); err != nil {
		return fmt.Errorf("%T.source_url (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:source_url: %s", p, err)
	}
	return err
}

func (p *PCImageSource) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source_title", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:source_title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SourceTitle)); err != nil {
		return fmt.Errorf("%T.source_title (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:source_title: %s", p, err)
	}
	return err
}

func (p *PCImageSource) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:template_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TemplateId)); err != nil {
		return fmt.Errorf("%T.template_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:template_id: %s", p, err)
	}
	return err
}

func (p *PCImageSource) writeField11(oprot thrift.TProtocol) (err error) {
	if p.SourceImageIdList != nil {
		if err := oprot.WriteFieldBegin("source_image_id_list", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:source_image_id_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SourceImageIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SourceImageIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:source_image_id_list: %s", p, err)
		}
	}
	return err
}

func (p *PCImageSource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCImageSource(%+v)", *p)
}

type PCImage struct {
	Id            int32         `thrift:"id,1" json:"id"`
	Name          string        `thrift:"name,2" json:"name"`
	Width         int32         `thrift:"width,3" json:"width"`
	Height        int32         `thrift:"height,4" json:"height"`
	Color         *PCImageColor `thrift:"color,5" json:"color"`
	Style         PCImageStyle  `thrift:"style,6" json:"style"`
	TypeA1        PCImageType   `thrift:"type,7" json:"type"`
	UgcId         int32         `thrift:"ugc_id,8" json:"ugc_id"`
	UgcUrl        string        `thrift:"ugc_url,9" json:"ugc_url"`
	UgcFormatCode int32         `thrift:"ugc_format_code,10" json:"ugc_format_code"`
	EncodedUgcId  string        `thrift:"encoded_ugc_id,11" json:"encoded_ugc_id"`
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	ContentBytes     []byte         `thrift:"content_bytes,50" json:"content_bytes"`
	HashKey          string         `thrift:"hash_key,51" json:"hash_key"`
	Source           *PCImageSource `thrift:"source,52" json:"source"`
	ImageFingerprint string         `thrift:"image_fingerprint,53" json:"image_fingerprint"`
}

func NewPCImage() *PCImage {
	return &PCImage{
		Style: math.MinInt32 - 1, // unset sentinal value

		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCImage) IsSetStyle() bool {
	return int64(p.Style) != math.MinInt32-1
}

func (p *PCImage) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PCImage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.STRING {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCImage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PCImage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PCImage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *PCImage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *PCImage) readField5(iprot thrift.TProtocol) error {
	p.Color = NewPCImageColor()
	if err := p.Color.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Color)
	}
	return nil
}

func (p *PCImage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Style = PCImageStyle(v)
	}
	return nil
}

func (p *PCImage) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TypeA1 = PCImageType(v)
	}
	return nil
}

func (p *PCImage) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *PCImage) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.EncodedUgcId = v
	}
	return nil
}

func (p *PCImage) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UgcUrl = v
	}
	return nil
}

func (p *PCImage) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.UgcFormatCode = v
	}
	return nil
}

func (p *PCImage) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.ContentBytes = v
	}
	return nil
}

func (p *PCImage) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.HashKey = v
	}
	return nil
}

func (p *PCImage) readField52(iprot thrift.TProtocol) error {
	p.Source = NewPCImageSource()
	if err := p.Source.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Source)
	}
	return nil
}

func (p *PCImage) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.ImageFingerprint = v
	}
	return nil
}

func (p *PCImage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCImage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCImage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:width: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:height: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Color != nil {
		if err := oprot.WriteFieldBegin("color", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:color: %s", p, err)
		}
		if err := p.Color.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Color)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:color: %s", p, err)
		}
	}
	return err
}

func (p *PCImage) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetStyle() {
		if err := oprot.WriteFieldBegin("style", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:style: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Style)); err != nil {
			return fmt.Errorf("%T.style (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:style: %s", p, err)
		}
	}
	return err
}

func (p *PCImage) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:type: %s", p, err)
		}
	}
	return err
}

func (p *PCImage) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ugc_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugc_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ugc_id: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_url", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ugc_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcUrl)); err != nil {
		return fmt.Errorf("%T.ugc_url (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ugc_url: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_format_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:ugc_format_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UgcFormatCode)); err != nil {
		return fmt.Errorf("%T.ugc_format_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:ugc_format_code: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encoded_ugc_id", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:encoded_ugc_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodedUgcId)); err != nil {
		return fmt.Errorf("%T.encoded_ugc_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:encoded_ugc_id: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField50(oprot thrift.TProtocol) (err error) {
	if p.ContentBytes != nil {
		if err := oprot.WriteFieldBegin("content_bytes", thrift.STRING, 50); err != nil {
			return fmt.Errorf("%T write field begin error 50:content_bytes: %s", p, err)
		}
		if err := oprot.WriteBinary(p.ContentBytes); err != nil {
			return fmt.Errorf("%T.content_bytes (50) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 50:content_bytes: %s", p, err)
		}
	}
	return err
}

func (p *PCImage) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hash_key", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:hash_key: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HashKey)); err != nil {
		return fmt.Errorf("%T.hash_key (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:hash_key: %s", p, err)
	}
	return err
}

func (p *PCImage) writeField52(oprot thrift.TProtocol) (err error) {
	if p.Source != nil {
		if err := oprot.WriteFieldBegin("source", thrift.STRUCT, 52); err != nil {
			return fmt.Errorf("%T write field begin error 52:source: %s", p, err)
		}
		if err := p.Source.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Source)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 52:source: %s", p, err)
		}
	}
	return err
}

func (p *PCImage) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image_fingerprint", thrift.STRING, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:image_fingerprint: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImageFingerprint)); err != nil {
		return fmt.Errorf("%T.image_fingerprint (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:image_fingerprint: %s", p, err)
	}
	return err
}

func (p *PCImage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCImage(%+v)", *p)
}

type PCText struct {
	Id          int32      `thrift:"id,1" json:"id"`
	ModelId     int32      `thrift:"model_id,2" json:"model_id"`
	Content     string     `thrift:"content,3" json:"content"`
	Size        int32      `thrift:"size,4" json:"size"`
	HashKey     string     `thrift:"hash_key,5" json:"hash_key"`
	TypeA1      PCTextType `thrift:"type,6" json:"type"`
	Fingerprint string     `thrift:"fingerprint,7" json:"fingerprint"`
}

func NewPCText() *PCText {
	return &PCText{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCText) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PCText) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCText) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PCText) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ModelId = v
	}
	return nil
}

func (p *PCText) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *PCText) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *PCText) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.HashKey = v
	}
	return nil
}

func (p *PCText) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TypeA1 = PCTextType(v)
	}
	return nil
}

func (p *PCText) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Fingerprint = v
	}
	return nil
}

func (p *PCText) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCText"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCText) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PCText) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("model_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:model_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ModelId)); err != nil {
		return fmt.Errorf("%T.model_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:model_id: %s", p, err)
	}
	return err
}

func (p *PCText) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:content: %s", p, err)
	}
	return err
}

func (p *PCText) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *PCText) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hash_key", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:hash_key: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HashKey)); err != nil {
		return fmt.Errorf("%T.hash_key (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:hash_key: %s", p, err)
	}
	return err
}

func (p *PCText) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:type: %s", p, err)
		}
	}
	return err
}

func (p *PCText) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fingerprint", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:fingerprint: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Fingerprint)); err != nil {
		return fmt.Errorf("%T.fingerprint (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:fingerprint: %s", p, err)
	}
	return err
}

func (p *PCText) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCText(%+v)", *p)
}

type PCTextModel struct {
	Id                     int32      `thrift:"id,1" json:"id"`
	Model                  string     `thrift:"model,2" json:"model"`
	GenreCondition         []string   `thrift:"genre_condition,3" json:"genre_condition"`
	KeywordsCondition      [][]string `thrift:"keywords_condition,4" json:"keywords_condition"`
	PkgSizeCondition       string     `thrift:"pkg_size_condition,5" json:"pkg_size_condition"`
	DownloadCountCondition string     `thrift:"download_count_condition,6" json:"download_count_condition"`
	Name                   string     `thrift:"name,7" json:"name"`
}

func NewPCTextModel() *PCTextModel {
	return &PCTextModel{}
}

func (p *PCTextModel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCTextModel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PCTextModel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Model = v
	}
	return nil
}

func (p *PCTextModel) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GenreCondition = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.GenreCondition = append(p.GenreCondition, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCTextModel) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.KeywordsCondition = make([][]string, 0, size)
	for i := 0; i < size; i++ {
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_elem3 := make([]string, 0, size)
		for i := 0; i < size; i++ {
			var _elem4 string
			if v, err := iprot.ReadString(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_elem4 = v
			}
			_elem3 = append(_elem3, _elem4)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.KeywordsCondition = append(p.KeywordsCondition, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCTextModel) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PkgSizeCondition = v
	}
	return nil
}

func (p *PCTextModel) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DownloadCountCondition = v
	}
	return nil
}

func (p *PCTextModel) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PCTextModel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCTextModel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCTextModel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PCTextModel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("model", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:model: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Model)); err != nil {
		return fmt.Errorf("%T.model (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:model: %s", p, err)
	}
	return err
}

func (p *PCTextModel) writeField3(oprot thrift.TProtocol) (err error) {
	if p.GenreCondition != nil {
		if err := oprot.WriteFieldBegin("genre_condition", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:genre_condition: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.GenreCondition)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GenreCondition {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:genre_condition: %s", p, err)
		}
	}
	return err
}

func (p *PCTextModel) writeField4(oprot thrift.TProtocol) (err error) {
	if p.KeywordsCondition != nil {
		if err := oprot.WriteFieldBegin("keywords_condition", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:keywords_condition: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.LIST, len(p.KeywordsCondition)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.KeywordsCondition {
			if err := oprot.WriteListBegin(thrift.STRING, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteString(string(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:keywords_condition: %s", p, err)
		}
	}
	return err
}

func (p *PCTextModel) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_size_condition", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:pkg_size_condition: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgSizeCondition)); err != nil {
		return fmt.Errorf("%T.pkg_size_condition (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:pkg_size_condition: %s", p, err)
	}
	return err
}

func (p *PCTextModel) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_count_condition", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:download_count_condition: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DownloadCountCondition)); err != nil {
		return fmt.Errorf("%T.download_count_condition (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:download_count_condition: %s", p, err)
	}
	return err
}

func (p *PCTextModel) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:name: %s", p, err)
	}
	return err
}

func (p *PCTextModel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCTextModel(%+v)", *p)
}

type PCVideoCodecInfo struct {
	CodecType PCVideoCodecType `thrift:"codec_type,1" json:"codec_type"`
	VideoFps  int32            `thrift:"video_fps,2" json:"video_fps"`
}

func NewPCVideoCodecInfo() *PCVideoCodecInfo {
	return &PCVideoCodecInfo{
		CodecType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCVideoCodecInfo) IsSetCodecType() bool {
	return int64(p.CodecType) != math.MinInt32-1
}

func (p *PCVideoCodecInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoCodecInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CodecType = PCVideoCodecType(v)
	}
	return nil
}

func (p *PCVideoCodecInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.VideoFps = v
	}
	return nil
}

func (p *PCVideoCodecInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoCodecInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoCodecInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCodecType() {
		if err := oprot.WriteFieldBegin("codec_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:codec_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CodecType)); err != nil {
			return fmt.Errorf("%T.codec_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:codec_type: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoCodecInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_fps", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:video_fps: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoFps)); err != nil {
		return fmt.Errorf("%T.video_fps (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:video_fps: %s", p, err)
	}
	return err
}

func (p *PCVideoCodecInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoCodecInfo(%+v)", *p)
}

type PCVideoSource struct {
	ParentHash string            `thrift:"parent_hash,1" json:"parent_hash"`
	SourceUrl  string            `thrift:"source_url,2" json:"source_url"`
	TypeA1     PCVideoSourceType `thrift:"type,3" json:"type"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	ClipNo int32 `thrift:"clip_no,10" json:"clip_no"`
}

func NewPCVideoSource() *PCVideoSource {
	return &PCVideoSource{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCVideoSource) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PCVideoSource) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoSource) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ParentHash = v
	}
	return nil
}

func (p *PCVideoSource) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SourceUrl = v
	}
	return nil
}

func (p *PCVideoSource) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = PCVideoSourceType(v)
	}
	return nil
}

func (p *PCVideoSource) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ClipNo = v
	}
	return nil
}

func (p *PCVideoSource) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoSource"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoSource) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_hash", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:parent_hash: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ParentHash)); err != nil {
		return fmt.Errorf("%T.parent_hash (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:parent_hash: %s", p, err)
	}
	return err
}

func (p *PCVideoSource) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source_url", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:source_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SourceUrl)); err != nil {
		return fmt.Errorf("%T.source_url (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:source_url: %s", p, err)
	}
	return err
}

func (p *PCVideoSource) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoSource) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clip_no", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:clip_no: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClipNo)); err != nil {
		return fmt.Errorf("%T.clip_no (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:clip_no: %s", p, err)
	}
	return err
}

func (p *PCVideoSource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoSource(%+v)", *p)
}

type PCVideo struct {
	Id               int32             `thrift:"id,1" json:"id"`
	Name             string            `thrift:"name,2" json:"name"`
	Width            int32             `thrift:"width,3" json:"width"`
	Height           int32             `thrift:"height,4" json:"height"`
	Duration         int32             `thrift:"duration,5" json:"duration"`
	TypeA1           PCVideoType       `thrift:"type,6" json:"type"`
	UgcId            int32             `thrift:"ugc_id,7" json:"ugc_id"`
	UgcUrl           string            `thrift:"ugc_url,8" json:"ugc_url"`
	UgcFormatCode    int32             `thrift:"ugc_format_code,9" json:"ugc_format_code"`
	HashKey          string            `thrift:"hash_key,10" json:"hash_key"`
	Source           *PCVideoSource    `thrift:"source,11" json:"source"`
	CodecInfo        *PCVideoCodecInfo `thrift:"codec_info,12" json:"codec_info"`
	FileSize         int32             `thrift:"file_size,13" json:"file_size"`
	EncodedUgcId     string            `thrift:"encoded_ugc_id,14" json:"encoded_ugc_id"`
	VideoFingerprint string            `thrift:"video_fingerprint,15" json:"video_fingerprint"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Status PCVideoStatus `thrift:"status,20" json:"status"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	AdditionalAttribute map[string]string `thrift:"additional_attribute,40" json:"additional_attribute"`
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	VideoTag []string `thrift:"video_tag,50" json:"video_tag"`
}

func NewPCVideo() *PCVideo {
	return &PCVideo{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCVideo) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PCVideo) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *PCVideo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.MAP {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.LIST {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PCVideo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PCVideo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *PCVideo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *PCVideo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *PCVideo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TypeA1 = PCVideoType(v)
	}
	return nil
}

func (p *PCVideo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *PCVideo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.EncodedUgcId = v
	}
	return nil
}

func (p *PCVideo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.UgcUrl = v
	}
	return nil
}

func (p *PCVideo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.UgcFormatCode = v
	}
	return nil
}

func (p *PCVideo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.HashKey = v
	}
	return nil
}

func (p *PCVideo) readField11(iprot thrift.TProtocol) error {
	p.Source = NewPCVideoSource()
	if err := p.Source.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Source)
	}
	return nil
}

func (p *PCVideo) readField12(iprot thrift.TProtocol) error {
	p.CodecInfo = NewPCVideoCodecInfo()
	if err := p.CodecInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CodecInfo)
	}
	return nil
}

func (p *PCVideo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.FileSize = v
	}
	return nil
}

func (p *PCVideo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.VideoFingerprint = v
	}
	return nil
}

func (p *PCVideo) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Status = PCVideoStatus(v)
	}
	return nil
}

func (p *PCVideo) readField40(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AdditionalAttribute = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key5 = v
		}
		var _val6 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val6 = v
		}
		p.AdditionalAttribute[_key5] = _val6
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *PCVideo) readField50(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoTag = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.VideoTag = append(p.VideoTag, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCVideo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:width: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:height: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:duration: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:type: %s", p, err)
		}
	}
	return err
}

func (p *PCVideo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_id", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ugc_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugc_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ugc_id: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_url", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ugc_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcUrl)); err != nil {
		return fmt.Errorf("%T.ugc_url (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ugc_url: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_format_code", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ugc_format_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UgcFormatCode)); err != nil {
		return fmt.Errorf("%T.ugc_format_code (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ugc_format_code: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hash_key", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:hash_key: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HashKey)); err != nil {
		return fmt.Errorf("%T.hash_key (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:hash_key: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Source != nil {
		if err := oprot.WriteFieldBegin("source", thrift.STRUCT, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:source: %s", p, err)
		}
		if err := p.Source.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Source)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:source: %s", p, err)
		}
	}
	return err
}

func (p *PCVideo) writeField12(oprot thrift.TProtocol) (err error) {
	if p.CodecInfo != nil {
		if err := oprot.WriteFieldBegin("codec_info", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:codec_info: %s", p, err)
		}
		if err := p.CodecInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CodecInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:codec_info: %s", p, err)
		}
	}
	return err
}

func (p *PCVideo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("file_size", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:file_size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileSize)); err != nil {
		return fmt.Errorf("%T.file_size (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:file_size: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encoded_ugc_id", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:encoded_ugc_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodedUgcId)); err != nil {
		return fmt.Errorf("%T.encoded_ugc_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:encoded_ugc_id: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_fingerprint", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:video_fingerprint: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoFingerprint)); err != nil {
		return fmt.Errorf("%T.video_fingerprint (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:video_fingerprint: %s", p, err)
	}
	return err
}

func (p *PCVideo) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:status: %s", p, err)
		}
	}
	return err
}

func (p *PCVideo) writeField40(oprot thrift.TProtocol) (err error) {
	if p.AdditionalAttribute != nil {
		if err := oprot.WriteFieldBegin("additional_attribute", thrift.MAP, 40); err != nil {
			return fmt.Errorf("%T write field begin error 40:additional_attribute: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AdditionalAttribute)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AdditionalAttribute {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 40:additional_attribute: %s", p, err)
		}
	}
	return err
}

func (p *PCVideo) writeField50(oprot thrift.TProtocol) (err error) {
	if p.VideoTag != nil {
		if err := oprot.WriteFieldBegin("video_tag", thrift.LIST, 50); err != nil {
			return fmt.Errorf("%T write field begin error 50:video_tag: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.VideoTag)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoTag {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 50:video_tag: %s", p, err)
		}
	}
	return err
}

func (p *PCVideo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideo(%+v)", *p)
}

type MaterialsInfo struct {
	Status    PCSStatus  `thrift:"status,1" json:"status"`
	SearchId  int64      `thrift:"search_id,2" json:"search_id"`
	ImageList []*PCImage `thrift:"image_list,3" json:"image_list"`
	TextList  []*PCText  `thrift:"text_list,4" json:"text_list"`
	VideoList []*PCVideo `thrift:"video_list,5" json:"video_list"`
}

func NewMaterialsInfo() *MaterialsInfo {
	return &MaterialsInfo{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MaterialsInfo) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *MaterialsInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MaterialsInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = PCSStatus(v)
	}
	return nil
}

func (p *MaterialsInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *MaterialsInfo) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageList = make([]*PCImage, 0, size)
	for i := 0; i < size; i++ {
		_elem8 := NewPCImage()
		if err := _elem8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem8)
		}
		p.ImageList = append(p.ImageList, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MaterialsInfo) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TextList = make([]*PCText, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewPCText()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.TextList = append(p.TextList, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MaterialsInfo) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoList = make([]*PCVideo, 0, size)
	for i := 0; i < size; i++ {
		_elem10 := NewPCVideo()
		if err := _elem10.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem10)
		}
		p.VideoList = append(p.VideoList, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *MaterialsInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MaterialsInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MaterialsInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *MaterialsInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_id: %s", p, err)
	}
	return err
}

func (p *MaterialsInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ImageList != nil {
		if err := oprot.WriteFieldBegin("image_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:image_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImageList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:image_list: %s", p, err)
		}
	}
	return err
}

func (p *MaterialsInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.TextList != nil {
		if err := oprot.WriteFieldBegin("text_list", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:text_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TextList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TextList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:text_list: %s", p, err)
		}
	}
	return err
}

func (p *MaterialsInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.VideoList != nil {
		if err := oprot.WriteFieldBegin("video_list", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:video_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.VideoList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:video_list: %s", p, err)
		}
	}
	return err
}

func (p *MaterialsInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MaterialsInfo(%+v)", *p)
}

type AppIdInfo struct {
	Platform int32 `thrift:"platform,1" json:"platform"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	AppId       int32  `thrift:"app_id,10" json:"app_id"`
	ItunesId    int32  `thrift:"itunes_id,11" json:"itunes_id"`
	PackageName string `thrift:"package_name,12" json:"package_name"`
}

func NewAppIdInfo() *AppIdInfo {
	return &AppIdInfo{}
}

func (p *AppIdInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppIdInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *AppIdInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AppIdInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ItunesId = v
	}
	return nil
}

func (p *AppIdInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AppIdInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppIdInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppIdInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:platform: %s", p, err)
	}
	return err
}

func (p *AppIdInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:app_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:app_id: %s", p, err)
	}
	return err
}

func (p *AppIdInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunes_id", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:itunes_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunes_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:itunes_id: %s", p, err)
	}
	return err
}

func (p *AppIdInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:package_name: %s", p, err)
	}
	return err
}

func (p *AppIdInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppIdInfo(%+v)", *p)
}

type AppMaterialRequest struct {
	Searchid     int64            `thrift:"searchid,1" json:"searchid"`
	AppInfo      *AppIdInfo       `thrift:"app_info,2" json:"app_info"`
	MaterialType []PCMaterialType `thrift:"material_type,3" json:"material_type"`
}

func NewAppMaterialRequest() *AppMaterialRequest {
	return &AppMaterialRequest{}
}

func (p *AppMaterialRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppMaterialRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AppMaterialRequest) readField2(iprot thrift.TProtocol) error {
	p.AppInfo = NewAppIdInfo()
	if err := p.AppInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppInfo)
	}
	return nil
}

func (p *AppMaterialRequest) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MaterialType = make([]PCMaterialType, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 PCMaterialType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = PCMaterialType(v)
		}
		p.MaterialType = append(p.MaterialType, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppMaterialRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppMaterialRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppMaterialRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchid: %s", p, err)
	}
	return err
}

func (p *AppMaterialRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppInfo != nil {
		if err := oprot.WriteFieldBegin("app_info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_info: %s", p, err)
		}
		if err := p.AppInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_info: %s", p, err)
		}
	}
	return err
}

func (p *AppMaterialRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MaterialType != nil {
		if err := oprot.WriteFieldBegin("material_type", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:material_type: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MaterialType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MaterialType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:material_type: %s", p, err)
		}
	}
	return err
}

func (p *AppMaterialRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppMaterialRequest(%+v)", *p)
}

type ImageMaterialFilter struct {
	ImageTypeTarget       []PCImageType       `thrift:"image_type_target,1" json:"image_type_target"`
	ImageStyleTarget      []PCImageStyle      `thrift:"image_style_target,2" json:"image_style_target"`
	ImageFormatTarget     []int32             `thrift:"image_format_target,3" json:"image_format_target"`
	ImageSourceTypeTarget []PCImageSourceType `thrift:"image_source_type_target,4" json:"image_source_type_target"`
	ImageOutputQuantity   int32               `thrift:"image_output_quantity,5" json:"image_output_quantity"`
	ImageSizeTarget       []*PCSSize          `thrift:"image_size_target,6" json:"image_size_target"`
}

func NewImageMaterialFilter() *ImageMaterialFilter {
	return &ImageMaterialFilter{}
}

func (p *ImageMaterialFilter) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ImageMaterialFilter) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageTypeTarget = make([]PCImageType, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 PCImageType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = PCImageType(v)
		}
		p.ImageTypeTarget = append(p.ImageTypeTarget, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ImageMaterialFilter) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageStyleTarget = make([]PCImageStyle, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 PCImageStyle
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = PCImageStyle(v)
		}
		p.ImageStyleTarget = append(p.ImageStyleTarget, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ImageMaterialFilter) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageFormatTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.ImageFormatTarget = append(p.ImageFormatTarget, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ImageMaterialFilter) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageSourceTypeTarget = make([]PCImageSourceType, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 PCImageSourceType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = PCImageSourceType(v)
		}
		p.ImageSourceTypeTarget = append(p.ImageSourceTypeTarget, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ImageMaterialFilter) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ImageOutputQuantity = v
	}
	return nil
}

func (p *ImageMaterialFilter) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageSizeTarget = make([]*PCSSize, 0, size)
	for i := 0; i < size; i++ {
		_elem16 := NewPCSSize()
		if err := _elem16.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem16)
		}
		p.ImageSizeTarget = append(p.ImageSizeTarget, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ImageMaterialFilter) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ImageMaterialFilter"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ImageMaterialFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ImageTypeTarget != nil {
		if err := oprot.WriteFieldBegin("image_type_target", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:image_type_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ImageTypeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageTypeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:image_type_target: %s", p, err)
		}
	}
	return err
}

func (p *ImageMaterialFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ImageStyleTarget != nil {
		if err := oprot.WriteFieldBegin("image_style_target", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:image_style_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ImageStyleTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageStyleTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:image_style_target: %s", p, err)
		}
	}
	return err
}

func (p *ImageMaterialFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ImageFormatTarget != nil {
		if err := oprot.WriteFieldBegin("image_format_target", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:image_format_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ImageFormatTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageFormatTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:image_format_target: %s", p, err)
		}
	}
	return err
}

func (p *ImageMaterialFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ImageSourceTypeTarget != nil {
		if err := oprot.WriteFieldBegin("image_source_type_target", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:image_source_type_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ImageSourceTypeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageSourceTypeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:image_source_type_target: %s", p, err)
		}
	}
	return err
}

func (p *ImageMaterialFilter) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image_output_quantity", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:image_output_quantity: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImageOutputQuantity)); err != nil {
		return fmt.Errorf("%T.image_output_quantity (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:image_output_quantity: %s", p, err)
	}
	return err
}

func (p *ImageMaterialFilter) writeField6(oprot thrift.TProtocol) (err error) {
	if p.ImageSizeTarget != nil {
		if err := oprot.WriteFieldBegin("image_size_target", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:image_size_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImageSizeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageSizeTarget {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:image_size_target: %s", p, err)
		}
	}
	return err
}

func (p *ImageMaterialFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImageMaterialFilter(%+v)", *p)
}

type TextMaterialFilter struct {
	TextFilter         []string       `thrift:"text_filter,1" json:"text_filter"`
	TypeList           []PCTextType   `thrift:"type_list,2" json:"type_list"`
	TextOutputQuantity int32          `thrift:"text_output_quantity,3" json:"text_output_quantity"`
	TextMatchType      [][]PCTextType `thrift:"text_match_type,4" json:"text_match_type"`
}

func NewTextMaterialFilter() *TextMaterialFilter {
	return &TextMaterialFilter{}
}

func (p *TextMaterialFilter) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TextMaterialFilter) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TextFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem17 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem17 = v
		}
		p.TextFilter = append(p.TextFilter, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TextMaterialFilter) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TypeList = make([]PCTextType, 0, size)
	for i := 0; i < size; i++ {
		var _elem18 PCTextType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem18 = PCTextType(v)
		}
		p.TypeList = append(p.TypeList, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TextMaterialFilter) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TextOutputQuantity = v
	}
	return nil
}

func (p *TextMaterialFilter) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TextMatchType = make([][]PCTextType, 0, size)
	for i := 0; i < size; i++ {
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_elem19 := make([]PCTextType, 0, size)
		for i := 0; i < size; i++ {
			var _elem20 PCTextType
			if v, err := iprot.ReadI32(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_elem20 = PCTextType(v)
			}
			_elem19 = append(_elem19, _elem20)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.TextMatchType = append(p.TextMatchType, _elem19)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TextMaterialFilter) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TextMaterialFilter"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TextMaterialFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.TextFilter != nil {
		if err := oprot.WriteFieldBegin("text_filter", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:text_filter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TextFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TextFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:text_filter: %s", p, err)
		}
	}
	return err
}

func (p *TextMaterialFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TypeList != nil {
		if err := oprot.WriteFieldBegin("type_list", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.TypeList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TypeList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type_list: %s", p, err)
		}
	}
	return err
}

func (p *TextMaterialFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text_output_quantity", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:text_output_quantity: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TextOutputQuantity)); err != nil {
		return fmt.Errorf("%T.text_output_quantity (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:text_output_quantity: %s", p, err)
	}
	return err
}

func (p *TextMaterialFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if p.TextMatchType != nil {
		if err := oprot.WriteFieldBegin("text_match_type", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:text_match_type: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.LIST, len(p.TextMatchType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TextMatchType {
			if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteI32(int32(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:text_match_type: %s", p, err)
		}
	}
	return err
}

func (p *TextMaterialFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TextMaterialFilter(%+v)", *p)
}

type VideoMaterialFilter struct {
	VideoType        []PCVideoType       `thrift:"video_type,1" json:"video_type"`
	SourceType       []PCVideoSourceType `thrift:"source_type,2" json:"source_type"`
	VideoSize        []int32             `thrift:"video_size,3" json:"video_size"`
	CodecType        []PCVideoCodecType  `thrift:"codec_type,4" json:"codec_type"`
	VideoMaxDuration []int32             `thrift:"video_max_duration,5" json:"video_max_duration"`
	VideoMinDuration []int32             `thrift:"video_min_duration,6" json:"video_min_duration"`
	TagFilter        []string            `thrift:"tag_filter,7" json:"tag_filter"`
}

func NewVideoMaterialFilter() *VideoMaterialFilter {
	return &VideoMaterialFilter{}
}

func (p *VideoMaterialFilter) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoMaterialFilter) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoType = make([]PCVideoType, 0, size)
	for i := 0; i < size; i++ {
		var _elem21 PCVideoType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem21 = PCVideoType(v)
		}
		p.VideoType = append(p.VideoType, _elem21)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VideoMaterialFilter) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SourceType = make([]PCVideoSourceType, 0, size)
	for i := 0; i < size; i++ {
		var _elem22 PCVideoSourceType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem22 = PCVideoSourceType(v)
		}
		p.SourceType = append(p.SourceType, _elem22)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VideoMaterialFilter) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoSize = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem23 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem23 = v
		}
		p.VideoSize = append(p.VideoSize, _elem23)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VideoMaterialFilter) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CodecType = make([]PCVideoCodecType, 0, size)
	for i := 0; i < size; i++ {
		var _elem24 PCVideoCodecType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem24 = PCVideoCodecType(v)
		}
		p.CodecType = append(p.CodecType, _elem24)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VideoMaterialFilter) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoMaxDuration = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem25 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem25 = v
		}
		p.VideoMaxDuration = append(p.VideoMaxDuration, _elem25)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VideoMaterialFilter) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoMinDuration = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem26 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem26 = v
		}
		p.VideoMinDuration = append(p.VideoMinDuration, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VideoMaterialFilter) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TagFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem27 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem27 = v
		}
		p.TagFilter = append(p.TagFilter, _elem27)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *VideoMaterialFilter) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VideoMaterialFilter"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoMaterialFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.VideoType != nil {
		if err := oprot.WriteFieldBegin("video_type", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:video_type: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VideoType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:video_type: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterialFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SourceType != nil {
		if err := oprot.WriteFieldBegin("source_type", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:source_type: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SourceType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SourceType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:source_type: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterialFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.VideoSize != nil {
		if err := oprot.WriteFieldBegin("video_size", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:video_size: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VideoSize)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoSize {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:video_size: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterialFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if p.CodecType != nil {
		if err := oprot.WriteFieldBegin("codec_type", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:codec_type: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CodecType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CodecType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:codec_type: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterialFilter) writeField5(oprot thrift.TProtocol) (err error) {
	if p.VideoMaxDuration != nil {
		if err := oprot.WriteFieldBegin("video_max_duration", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:video_max_duration: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VideoMaxDuration)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoMaxDuration {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:video_max_duration: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterialFilter) writeField6(oprot thrift.TProtocol) (err error) {
	if p.VideoMinDuration != nil {
		if err := oprot.WriteFieldBegin("video_min_duration", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:video_min_duration: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.VideoMinDuration)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoMinDuration {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:video_min_duration: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterialFilter) writeField7(oprot thrift.TProtocol) (err error) {
	if p.TagFilter != nil {
		if err := oprot.WriteFieldBegin("tag_filter", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:tag_filter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TagFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TagFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:tag_filter: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterialFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoMaterialFilter(%+v)", *p)
}

type MaterialFilter struct {
	ImageFilter *ImageMaterialFilter `thrift:"image_filter,1" json:"image_filter"`
	TextFilter  *TextMaterialFilter  `thrift:"text_filter,2" json:"text_filter"`
	VideoFilter *VideoMaterialFilter `thrift:"video_filter,3" json:"video_filter"`
	AdMatchType int64                `thrift:"ad_match_type,4" json:"ad_match_type"`
}

func NewMaterialFilter() *MaterialFilter {
	return &MaterialFilter{}
}

func (p *MaterialFilter) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MaterialFilter) readField1(iprot thrift.TProtocol) error {
	p.ImageFilter = NewImageMaterialFilter()
	if err := p.ImageFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ImageFilter)
	}
	return nil
}

func (p *MaterialFilter) readField2(iprot thrift.TProtocol) error {
	p.TextFilter = NewTextMaterialFilter()
	if err := p.TextFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TextFilter)
	}
	return nil
}

func (p *MaterialFilter) readField3(iprot thrift.TProtocol) error {
	p.VideoFilter = NewVideoMaterialFilter()
	if err := p.VideoFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VideoFilter)
	}
	return nil
}

func (p *MaterialFilter) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *MaterialFilter) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MaterialFilter"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MaterialFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.ImageFilter != nil {
		if err := oprot.WriteFieldBegin("image_filter", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:image_filter: %s", p, err)
		}
		if err := p.ImageFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ImageFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:image_filter: %s", p, err)
		}
	}
	return err
}

func (p *MaterialFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TextFilter != nil {
		if err := oprot.WriteFieldBegin("text_filter", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:text_filter: %s", p, err)
		}
		if err := p.TextFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TextFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:text_filter: %s", p, err)
		}
	}
	return err
}

func (p *MaterialFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.VideoFilter != nil {
		if err := oprot.WriteFieldBegin("video_filter", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:video_filter: %s", p, err)
		}
		if err := p.VideoFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VideoFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:video_filter: %s", p, err)
		}
	}
	return err
}

func (p *MaterialFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_match_type", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ad_match_type: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.ad_match_type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ad_match_type: %s", p, err)
	}
	return err
}

func (p *MaterialFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MaterialFilter(%+v)", *p)
}

type TemplatePlaceholderInfo struct {
	Id        int32         `thrift:"id,1" json:"id"`
	ImageType []PCImageType `thrift:"image_type,2" json:"image_type"`
	Width     int32         `thrift:"width,3" json:"width"`
	Height    int32         `thrift:"height,4" json:"height"`
	RatioTol  float64       `thrift:"ratio_tol,5" json:"ratio_tol"`
	MinWidth  int32         `thrift:"min_width,6" json:"min_width"`
	MinHeight int32         `thrift:"min_height,7" json:"min_height"`
}

func NewTemplatePlaceholderInfo() *TemplatePlaceholderInfo {
	return &TemplatePlaceholderInfo{}
}

func (p *TemplatePlaceholderInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TemplatePlaceholderInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *TemplatePlaceholderInfo) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageType = make([]PCImageType, 0, size)
	for i := 0; i < size; i++ {
		var _elem28 PCImageType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem28 = PCImageType(v)
		}
		p.ImageType = append(p.ImageType, _elem28)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TemplatePlaceholderInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *TemplatePlaceholderInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *TemplatePlaceholderInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RatioTol = v
	}
	return nil
}

func (p *TemplatePlaceholderInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.MinWidth = v
	}
	return nil
}

func (p *TemplatePlaceholderInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.MinHeight = v
	}
	return nil
}

func (p *TemplatePlaceholderInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TemplatePlaceholderInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TemplatePlaceholderInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *TemplatePlaceholderInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ImageType != nil {
		if err := oprot.WriteFieldBegin("image_type", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:image_type: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ImageType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageType {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:image_type: %s", p, err)
		}
	}
	return err
}

func (p *TemplatePlaceholderInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:width: %s", p, err)
	}
	return err
}

func (p *TemplatePlaceholderInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:height: %s", p, err)
	}
	return err
}

func (p *TemplatePlaceholderInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ratio_tol", thrift.DOUBLE, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ratio_tol: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.RatioTol)); err != nil {
		return fmt.Errorf("%T.ratio_tol (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ratio_tol: %s", p, err)
	}
	return err
}

func (p *TemplatePlaceholderInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("min_width", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:min_width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MinWidth)); err != nil {
		return fmt.Errorf("%T.min_width (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:min_width: %s", p, err)
	}
	return err
}

func (p *TemplatePlaceholderInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("min_height", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:min_height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MinHeight)); err != nil {
		return fmt.Errorf("%T.min_height (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:min_height: %s", p, err)
	}
	return err
}

func (p *TemplatePlaceholderInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplatePlaceholderInfo(%+v)", *p)
}

type TemplateFilter struct {
	IdTarget          []int32  `thrift:"id_target,1" json:"id_target"`
	NameTarget        []string `thrift:"name_target,2" json:"name_target"`
	SizeTarget        []int32  `thrift:"size_target,3" json:"size_target"`
	DescriptionTarget []string `thrift:"description_target,4" json:"description_target"`
}

func NewTemplateFilter() *TemplateFilter {
	return &TemplateFilter{}
}

func (p *TemplateFilter) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TemplateFilter) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.IdTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem29 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem29 = v
		}
		p.IdTarget = append(p.IdTarget, _elem29)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TemplateFilter) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.NameTarget = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem30 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem30 = v
		}
		p.NameTarget = append(p.NameTarget, _elem30)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TemplateFilter) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SizeTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem31 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem31 = v
		}
		p.SizeTarget = append(p.SizeTarget, _elem31)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TemplateFilter) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DescriptionTarget = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem32 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem32 = v
		}
		p.DescriptionTarget = append(p.DescriptionTarget, _elem32)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TemplateFilter) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TemplateFilter"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TemplateFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IdTarget != nil {
		if err := oprot.WriteFieldBegin("id_target", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:id_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.IdTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.IdTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:id_target: %s", p, err)
		}
	}
	return err
}

func (p *TemplateFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.NameTarget != nil {
		if err := oprot.WriteFieldBegin("name_target", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:name_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.NameTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.NameTarget {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:name_target: %s", p, err)
		}
	}
	return err
}

func (p *TemplateFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SizeTarget != nil {
		if err := oprot.WriteFieldBegin("size_target", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:size_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SizeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SizeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:size_target: %s", p, err)
		}
	}
	return err
}

func (p *TemplateFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if p.DescriptionTarget != nil {
		if err := oprot.WriteFieldBegin("description_target", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:description_target: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.DescriptionTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DescriptionTarget {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:description_target: %s", p, err)
		}
	}
	return err
}

func (p *TemplateFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateFilter(%+v)", *p)
}

type TemplateInfo struct {
	Id              int32                      `thrift:"id,1" json:"id"`
	Name            string                     `thrift:"name,2" json:"name"`
	Size            int32                      `thrift:"size,3" json:"size"`
	PlaceholderType []*TemplatePlaceholderInfo `thrift:"placeholder_type,4" json:"placeholder_type"`
	Content         string                     `thrift:"content,5" json:"content"`
	Description     string                     `thrift:"description,6" json:"description"`
	Weight          int32                      `thrift:"weight,7" json:"weight"`
	OutputImageType PCImageType                `thrift:"output_image_type,8" json:"output_image_type"`
}

func NewTemplateInfo() *TemplateInfo {
	return &TemplateInfo{
		OutputImageType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TemplateInfo) IsSetOutputImageType() bool {
	return int64(p.OutputImageType) != math.MinInt32-1
}

func (p *TemplateInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TemplateInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *TemplateInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *TemplateInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *TemplateInfo) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PlaceholderType = make([]*TemplatePlaceholderInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem33 := NewTemplatePlaceholderInfo()
		if err := _elem33.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem33)
		}
		p.PlaceholderType = append(p.PlaceholderType, _elem33)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TemplateInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *TemplateInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *TemplateInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Weight = v
	}
	return nil
}

func (p *TemplateInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.OutputImageType = PCImageType(v)
	}
	return nil
}

func (p *TemplateInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TemplateInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TemplateInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *TemplateInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *TemplateInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:size: %s", p, err)
	}
	return err
}

func (p *TemplateInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.PlaceholderType != nil {
		if err := oprot.WriteFieldBegin("placeholder_type", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:placeholder_type: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PlaceholderType)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PlaceholderType {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:placeholder_type: %s", p, err)
		}
	}
	return err
}

func (p *TemplateInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("content", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:content: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Content)); err != nil {
		return fmt.Errorf("%T.content (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:content: %s", p, err)
	}
	return err
}

func (p *TemplateInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:description: %s", p, err)
	}
	return err
}

func (p *TemplateInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weight", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:weight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Weight)); err != nil {
		return fmt.Errorf("%T.weight (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:weight: %s", p, err)
	}
	return err
}

func (p *TemplateInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetOutputImageType() {
		if err := oprot.WriteFieldBegin("output_image_type", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:output_image_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OutputImageType)); err != nil {
			return fmt.Errorf("%T.output_image_type (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:output_image_type: %s", p, err)
		}
	}
	return err
}

func (p *TemplateInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateInfo(%+v)", *p)
}

type TemplateResultList struct {
	SearchId     int64           `thrift:"search_id,1" json:"search_id"`
	Status       PCSStatus       `thrift:"status,2" json:"status"`
	TemplateList []*TemplateInfo `thrift:"template_list,3" json:"template_list"`
}

func NewTemplateResultList() *TemplateResultList {
	return &TemplateResultList{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TemplateResultList) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *TemplateResultList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TemplateResultList) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *TemplateResultList) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = PCSStatus(v)
	}
	return nil
}

func (p *TemplateResultList) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TemplateList = make([]*TemplateInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem34 := NewTemplateInfo()
		if err := _elem34.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem34)
		}
		p.TemplateList = append(p.TemplateList, _elem34)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TemplateResultList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TemplateResultList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TemplateResultList) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:search_id: %s", p, err)
	}
	return err
}

func (p *TemplateResultList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *TemplateResultList) writeField3(oprot thrift.TProtocol) (err error) {
	if p.TemplateList != nil {
		if err := oprot.WriteFieldBegin("template_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:template_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TemplateList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TemplateList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:template_list: %s", p, err)
		}
	}
	return err
}

func (p *TemplateResultList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TemplateResultList(%+v)", *p)
}

type CreativeInfo struct {
	SearchId      int64        `thrift:"search_id,1" json:"search_id"`
	Status        PCSStatus    `thrift:"status,2" json:"status"`
	TypeA1        CreativeType `thrift:"type,3" json:"type"`
	CreativeText  []*PCText    `thrift:"creative_text,4" json:"creative_text"`
	CreativeImage []*PCImage   `thrift:"creative_image,5" json:"creative_image"`
	HtmlCode      string       `thrift:"html_code,6" json:"html_code"`
	HashKey       string       `thrift:"hash_key,7" json:"hash_key"`
	CreativeVideo []*PCVideo   `thrift:"creative_video,8" json:"creative_video"`
	CreativeId    int32        `thrift:"creative_id,9" json:"creative_id"`
	AdMatchType   int64        `thrift:"ad_match_type,10" json:"ad_match_type"`
}

func NewCreativeInfo() *CreativeInfo {
	return &CreativeInfo{
		Status: math.MinInt32 - 1, // unset sentinal value

		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CreativeInfo) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *CreativeInfo) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *CreativeInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *CreativeInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = PCSStatus(v)
	}
	return nil
}

func (p *CreativeInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = CreativeType(v)
	}
	return nil
}

func (p *CreativeInfo) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeText = make([]*PCText, 0, size)
	for i := 0; i < size; i++ {
		_elem35 := NewPCText()
		if err := _elem35.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem35)
		}
		p.CreativeText = append(p.CreativeText, _elem35)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeInfo) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeImage = make([]*PCImage, 0, size)
	for i := 0; i < size; i++ {
		_elem36 := NewPCImage()
		if err := _elem36.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem36)
		}
		p.CreativeImage = append(p.CreativeImage, _elem36)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.HtmlCode = v
	}
	return nil
}

func (p *CreativeInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.HashKey = v
	}
	return nil
}

func (p *CreativeInfo) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeVideo = make([]*PCVideo, 0, size)
	for i := 0; i < size; i++ {
		_elem37 := NewPCVideo()
		if err := _elem37.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem37)
		}
		p.CreativeVideo = append(p.CreativeVideo, _elem37)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *CreativeInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *CreativeInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:search_id: %s", p, err)
	}
	return err
}

func (p *CreativeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *CreativeInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *CreativeInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.CreativeText != nil {
		if err := oprot.WriteFieldBegin("creative_text", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:creative_text: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreativeText)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeText {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:creative_text: %s", p, err)
		}
	}
	return err
}

func (p *CreativeInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.CreativeImage != nil {
		if err := oprot.WriteFieldBegin("creative_image", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:creative_image: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreativeImage)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeImage {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:creative_image: %s", p, err)
		}
	}
	return err
}

func (p *CreativeInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("html_code", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:html_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HtmlCode)); err != nil {
		return fmt.Errorf("%T.html_code (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:html_code: %s", p, err)
	}
	return err
}

func (p *CreativeInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hash_key", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:hash_key: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HashKey)); err != nil {
		return fmt.Errorf("%T.hash_key (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:hash_key: %s", p, err)
	}
	return err
}

func (p *CreativeInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.CreativeVideo != nil {
		if err := oprot.WriteFieldBegin("creative_video", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:creative_video: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreativeVideo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeVideo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:creative_video: %s", p, err)
		}
	}
	return err
}

func (p *CreativeInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:creative_id: %s", p, err)
	}
	return err
}

func (p *CreativeInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_match_type", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:ad_match_type: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.ad_match_type (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:ad_match_type: %s", p, err)
	}
	return err
}

func (p *CreativeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeInfo(%+v)", *p)
}

type CreativeFilter struct {
	SearchId           int64                `thrift:"search_id,1" json:"search_id"`
	TypeA1             CreativeType         `thrift:"type,2" json:"type"`
	MaxImageSize       []int32              `thrift:"max_image_size,3" json:"max_image_size"`
	MinImageSize       []int32              `thrift:"min_image_size,4" json:"min_image_size"`
	MaxTextLength      []int32              `thrift:"max_text_length,5" json:"max_text_length"`
	MinTextLength      []int32              `thrift:"min_text_length,6" json:"min_text_length"`
	TemplateFilter     *TemplateFilter      `thrift:"template_filter,7" json:"template_filter"`
	VideoFilter        *VideoMaterialFilter `thrift:"video_filter,8" json:"video_filter"`
	CreativeHashFilter []string             `thrift:"creative_hash_filter,9" json:"creative_hash_filter"`
	MaxOutputQuantity  int32                `thrift:"max_output_quantity,10" json:"max_output_quantity"`
	MaterialReapeting  int32                `thrift:"material_reapeting,11" json:"material_reapeting"`
	AdMatchType        int64                `thrift:"ad_match_type,12" json:"ad_match_type"`
	ImageFilter        *ImageMaterialFilter `thrift:"image_filter,13" json:"image_filter"`
	TextFilter         *TextMaterialFilter  `thrift:"text_filter,14" json:"text_filter"`
}

func NewCreativeFilter() *CreativeFilter {
	return &CreativeFilter{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CreativeFilter) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *CreativeFilter) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeFilter) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *CreativeFilter) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = CreativeType(v)
	}
	return nil
}

func (p *CreativeFilter) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MaxImageSize = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem38 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem38 = v
		}
		p.MaxImageSize = append(p.MaxImageSize, _elem38)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeFilter) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MinImageSize = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem39 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem39 = v
		}
		p.MinImageSize = append(p.MinImageSize, _elem39)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeFilter) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MaxTextLength = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem40 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem40 = v
		}
		p.MaxTextLength = append(p.MaxTextLength, _elem40)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeFilter) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MinTextLength = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem41 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem41 = v
		}
		p.MinTextLength = append(p.MinTextLength, _elem41)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeFilter) readField7(iprot thrift.TProtocol) error {
	p.TemplateFilter = NewTemplateFilter()
	if err := p.TemplateFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TemplateFilter)
	}
	return nil
}

func (p *CreativeFilter) readField8(iprot thrift.TProtocol) error {
	p.VideoFilter = NewVideoMaterialFilter()
	if err := p.VideoFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VideoFilter)
	}
	return nil
}

func (p *CreativeFilter) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeHashFilter = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem42 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem42 = v
		}
		p.CreativeHashFilter = append(p.CreativeHashFilter, _elem42)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeFilter) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.MaxOutputQuantity = v
	}
	return nil
}

func (p *CreativeFilter) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.MaterialReapeting = v
	}
	return nil
}

func (p *CreativeFilter) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *CreativeFilter) readField13(iprot thrift.TProtocol) error {
	p.ImageFilter = NewImageMaterialFilter()
	if err := p.ImageFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ImageFilter)
	}
	return nil
}

func (p *CreativeFilter) readField14(iprot thrift.TProtocol) error {
	p.TextFilter = NewTextMaterialFilter()
	if err := p.TextFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TextFilter)
	}
	return nil
}

func (p *CreativeFilter) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeFilter"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeFilter) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:search_id: %s", p, err)
	}
	return err
}

func (p *CreativeFilter) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField3(oprot thrift.TProtocol) (err error) {
	if p.MaxImageSize != nil {
		if err := oprot.WriteFieldBegin("max_image_size", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:max_image_size: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MaxImageSize)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MaxImageSize {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:max_image_size: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField4(oprot thrift.TProtocol) (err error) {
	if p.MinImageSize != nil {
		if err := oprot.WriteFieldBegin("min_image_size", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:min_image_size: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MinImageSize)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MinImageSize {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:min_image_size: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField5(oprot thrift.TProtocol) (err error) {
	if p.MaxTextLength != nil {
		if err := oprot.WriteFieldBegin("max_text_length", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:max_text_length: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MaxTextLength)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MaxTextLength {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:max_text_length: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField6(oprot thrift.TProtocol) (err error) {
	if p.MinTextLength != nil {
		if err := oprot.WriteFieldBegin("min_text_length", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:min_text_length: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MinTextLength)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MinTextLength {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:min_text_length: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField7(oprot thrift.TProtocol) (err error) {
	if p.TemplateFilter != nil {
		if err := oprot.WriteFieldBegin("template_filter", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:template_filter: %s", p, err)
		}
		if err := p.TemplateFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TemplateFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:template_filter: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField8(oprot thrift.TProtocol) (err error) {
	if p.VideoFilter != nil {
		if err := oprot.WriteFieldBegin("video_filter", thrift.STRUCT, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:video_filter: %s", p, err)
		}
		if err := p.VideoFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VideoFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:video_filter: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField9(oprot thrift.TProtocol) (err error) {
	if p.CreativeHashFilter != nil {
		if err := oprot.WriteFieldBegin("creative_hash_filter", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:creative_hash_filter: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.CreativeHashFilter)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeHashFilter {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:creative_hash_filter: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_output_quantity", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:max_output_quantity: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxOutputQuantity)); err != nil {
		return fmt.Errorf("%T.max_output_quantity (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:max_output_quantity: %s", p, err)
	}
	return err
}

func (p *CreativeFilter) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("material_reapeting", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:material_reapeting: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaterialReapeting)); err != nil {
		return fmt.Errorf("%T.material_reapeting (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:material_reapeting: %s", p, err)
	}
	return err
}

func (p *CreativeFilter) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_match_type", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ad_match_type: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.ad_match_type (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ad_match_type: %s", p, err)
	}
	return err
}

func (p *CreativeFilter) writeField13(oprot thrift.TProtocol) (err error) {
	if p.ImageFilter != nil {
		if err := oprot.WriteFieldBegin("image_filter", thrift.STRUCT, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:image_filter: %s", p, err)
		}
		if err := p.ImageFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ImageFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:image_filter: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) writeField14(oprot thrift.TProtocol) (err error) {
	if p.TextFilter != nil {
		if err := oprot.WriteFieldBegin("text_filter", thrift.STRUCT, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:text_filter: %s", p, err)
		}
		if err := p.TextFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TextFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:text_filter: %s", p, err)
		}
	}
	return err
}

func (p *CreativeFilter) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeFilter(%+v)", *p)
}

type CreativeResultList struct {
	SearchId     int64           `thrift:"search_id,1" json:"search_id"`
	Status       PCSStatus       `thrift:"status,2" json:"status"`
	CreativeList []*CreativeInfo `thrift:"creative_list,3" json:"creative_list"`
}

func NewCreativeResultList() *CreativeResultList {
	return &CreativeResultList{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CreativeResultList) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *CreativeResultList) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeResultList) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *CreativeResultList) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = PCSStatus(v)
	}
	return nil
}

func (p *CreativeResultList) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeList = make([]*CreativeInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem43 := NewCreativeInfo()
		if err := _elem43.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem43)
		}
		p.CreativeList = append(p.CreativeList, _elem43)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeResultList) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeResultList"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeResultList) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:search_id: %s", p, err)
	}
	return err
}

func (p *CreativeResultList) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *CreativeResultList) writeField3(oprot thrift.TProtocol) (err error) {
	if p.CreativeList != nil {
		if err := oprot.WriteFieldBegin("creative_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:creative_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreativeList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:creative_list: %s", p, err)
		}
	}
	return err
}

func (p *CreativeResultList) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeResultList(%+v)", *p)
}

type Html2ImageResult struct {
	SearchId     int64     `thrift:"search_id,1" json:"search_id"`
	Status       PCSStatus `thrift:"status,2" json:"status"`
	BinaryCode   string    `thrift:"binary_code,3" json:"binary_code"`
	UgcUrl       string    `thrift:"ugc_url,4" json:"ugc_url"`
	UgcId        int32     `thrift:"ugc_id,5" json:"ugc_id"`
	EncodedUgcId string    `thrift:"encoded_ugc_id,6" json:"encoded_ugc_id"`
}

func NewHtml2ImageResult() *Html2ImageResult {
	return &Html2ImageResult{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Html2ImageResult) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *Html2ImageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Html2ImageResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *Html2ImageResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = PCSStatus(v)
	}
	return nil
}

func (p *Html2ImageResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.BinaryCode = v
	}
	return nil
}

func (p *Html2ImageResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UgcUrl = v
	}
	return nil
}

func (p *Html2ImageResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *Html2ImageResult) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EncodedUgcId = v
	}
	return nil
}

func (p *Html2ImageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Html2ImageResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Html2ImageResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:search_id: %s", p, err)
	}
	return err
}

func (p *Html2ImageResult) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *Html2ImageResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("binary_code", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:binary_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BinaryCode)); err != nil {
		return fmt.Errorf("%T.binary_code (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:binary_code: %s", p, err)
	}
	return err
}

func (p *Html2ImageResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_url", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ugc_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UgcUrl)); err != nil {
		return fmt.Errorf("%T.ugc_url (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ugc_url: %s", p, err)
	}
	return err
}

func (p *Html2ImageResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugc_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ugc_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugc_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ugc_id: %s", p, err)
	}
	return err
}

func (p *Html2ImageResult) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encoded_ugc_id", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:encoded_ugc_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodedUgcId)); err != nil {
		return fmt.Errorf("%T.encoded_ugc_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:encoded_ugc_id: %s", p, err)
	}
	return err
}

func (p *Html2ImageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Html2ImageResult(%+v)", *p)
}

type PCVideoProcResult struct {
	Status    PCSStatus  `thrift:"status,1" json:"status"`
	SearchId  int64      `thrift:"search_id,2" json:"search_id"`
	ProcVideo []*PCVideo `thrift:"proc_video,3" json:"proc_video"`
}

func NewPCVideoProcResult() *PCVideoProcResult {
	return &PCVideoProcResult{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCVideoProcResult) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *PCVideoProcResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoProcResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = PCSStatus(v)
	}
	return nil
}

func (p *PCVideoProcResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *PCVideoProcResult) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProcVideo = make([]*PCVideo, 0, size)
	for i := 0; i < size; i++ {
		_elem44 := NewPCVideo()
		if err := _elem44.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem44)
		}
		p.ProcVideo = append(p.ProcVideo, _elem44)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCVideoProcResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoProcResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoProcResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoProcResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_id: %s", p, err)
	}
	return err
}

func (p *PCVideoProcResult) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ProcVideo != nil {
		if err := oprot.WriteFieldBegin("proc_video", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:proc_video: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProcVideo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProcVideo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:proc_video: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoProcResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoProcResult(%+v)", *p)
}

type PCVideoClipInfo struct {
	StartTime float64 `thrift:"start_time,1" json:"start_time"`
	EndTime   float64 `thrift:"end_time,2" json:"end_time"`
	ClipNo    int32   `thrift:"clip_no,3" json:"clip_no"`
}

func NewPCVideoClipInfo() *PCVideoClipInfo {
	return &PCVideoClipInfo{}
}

func (p *PCVideoClipInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoClipInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *PCVideoClipInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *PCVideoClipInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ClipNo = v
	}
	return nil
}

func (p *PCVideoClipInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoClipInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoClipInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.DOUBLE, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:start_time: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:start_time: %s", p, err)
	}
	return err
}

func (p *PCVideoClipInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:end_time: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:end_time: %s", p, err)
	}
	return err
}

func (p *PCVideoClipInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clip_no", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:clip_no: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClipNo)); err != nil {
		return fmt.Errorf("%T.clip_no (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:clip_no: %s", p, err)
	}
	return err
}

func (p *PCVideoClipInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoClipInfo(%+v)", *p)
}

type PCVideoWatermarkInfo struct {
	WatermarkType PCVideoWatermarkType `thrift:"watermark_type,1" json:"watermark_type"`
	PosX          int32                `thrift:"pos_x,2" json:"pos_x"`
	PosY          int32                `thrift:"pos_y,3" json:"pos_y"`
	Width         int32                `thrift:"width,4" json:"width"`
	Height        int32                `thrift:"height,5" json:"height"`
	StartTime     int32                `thrift:"start_time,6" json:"start_time"`
	EndTime       int32                `thrift:"end_time,7" json:"end_time"`
}

func NewPCVideoWatermarkInfo() *PCVideoWatermarkInfo {
	return &PCVideoWatermarkInfo{
		WatermarkType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCVideoWatermarkInfo) IsSetWatermarkType() bool {
	return int64(p.WatermarkType) != math.MinInt32-1
}

func (p *PCVideoWatermarkInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoWatermarkInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.WatermarkType = PCVideoWatermarkType(v)
	}
	return nil
}

func (p *PCVideoWatermarkInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PosX = v
	}
	return nil
}

func (p *PCVideoWatermarkInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PosY = v
	}
	return nil
}

func (p *PCVideoWatermarkInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *PCVideoWatermarkInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *PCVideoWatermarkInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *PCVideoWatermarkInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *PCVideoWatermarkInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoWatermarkInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoWatermarkInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetWatermarkType() {
		if err := oprot.WriteFieldBegin("watermark_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:watermark_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.WatermarkType)); err != nil {
			return fmt.Errorf("%T.watermark_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:watermark_type: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoWatermarkInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pos_x", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pos_x: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PosX)); err != nil {
		return fmt.Errorf("%T.pos_x (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pos_x: %s", p, err)
	}
	return err
}

func (p *PCVideoWatermarkInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pos_y", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pos_y: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PosY)); err != nil {
		return fmt.Errorf("%T.pos_y (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pos_y: %s", p, err)
	}
	return err
}

func (p *PCVideoWatermarkInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:width: %s", p, err)
	}
	return err
}

func (p *PCVideoWatermarkInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:height: %s", p, err)
	}
	return err
}

func (p *PCVideoWatermarkInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:start_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:start_time: %s", p, err)
	}
	return err
}

func (p *PCVideoWatermarkInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:end_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:end_time: %s", p, err)
	}
	return err
}

func (p *PCVideoWatermarkInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoWatermarkInfo(%+v)", *p)
}

type PCVideoOverlayInfo struct {
	PosX          int32                `thrift:"pos_x,1" json:"pos_x"`
	PosY          int32                `thrift:"pos_y,2" json:"pos_y"`
	OverlayWidth  int32                `thrift:"overlay_width,3" json:"overlay_width"`
	OverlayHeight int32                `thrift:"overlay_height,4" json:"overlay_height"`
	StartTime     int32                `thrift:"start_time,5" json:"start_time"`
	EndTime       int32                `thrift:"end_time,6" json:"end_time"`
	Url           string               `thrift:"url,7" json:"url"`
	OverlayType   PCVideoOverlayType   `thrift:"overlay_type,8" json:"overlay_type"`
	OverlayFormat PCVideoOverlayFormat `thrift:"overlay_format,9" json:"overlay_format"`
}

func NewPCVideoOverlayInfo() *PCVideoOverlayInfo {
	return &PCVideoOverlayInfo{
		OverlayType: math.MinInt32 - 1, // unset sentinal value

		OverlayFormat: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCVideoOverlayInfo) IsSetOverlayType() bool {
	return int64(p.OverlayType) != math.MinInt32-1
}

func (p *PCVideoOverlayInfo) IsSetOverlayFormat() bool {
	return int64(p.OverlayFormat) != math.MinInt32-1
}

func (p *PCVideoOverlayInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PosX = v
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PosY = v
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OverlayWidth = v
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OverlayHeight = v
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.OverlayType = PCVideoOverlayType(v)
	}
	return nil
}

func (p *PCVideoOverlayInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.OverlayFormat = PCVideoOverlayFormat(v)
	}
	return nil
}

func (p *PCVideoOverlayInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoOverlayInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoOverlayInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pos_x", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pos_x: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PosX)); err != nil {
		return fmt.Errorf("%T.pos_x (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pos_x: %s", p, err)
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pos_y", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pos_y: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PosY)); err != nil {
		return fmt.Errorf("%T.pos_y (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pos_y: %s", p, err)
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("overlay_width", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:overlay_width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OverlayWidth)); err != nil {
		return fmt.Errorf("%T.overlay_width (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:overlay_width: %s", p, err)
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("overlay_height", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:overlay_height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OverlayHeight)); err != nil {
		return fmt.Errorf("%T.overlay_height (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:overlay_height: %s", p, err)
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:start_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:start_time: %s", p, err)
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:end_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:end_time: %s", p, err)
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:url: %s", p, err)
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetOverlayType() {
		if err := oprot.WriteFieldBegin("overlay_type", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:overlay_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OverlayType)); err != nil {
			return fmt.Errorf("%T.overlay_type (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:overlay_type: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoOverlayInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetOverlayFormat() {
		if err := oprot.WriteFieldBegin("overlay_format", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:overlay_format: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OverlayFormat)); err != nil {
			return fmt.Errorf("%T.overlay_format (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:overlay_format: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoOverlayInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoOverlayInfo(%+v)", *p)
}

type PCVideoVolumeInfo struct {
	TypeA1          PCVideoVolumeType `thrift:"type,1" json:"type"`
	Volume          int32             `thrift:"volume,2" json:"volume"`
	FadeInDuration  int32             `thrift:"fade_in_duration,3" json:"fade_in_duration"`
	FadeOutDuration int32             `thrift:"fade_out_duration,4" json:"fade_out_duration"`
}

func NewPCVideoVolumeInfo() *PCVideoVolumeInfo {
	return &PCVideoVolumeInfo{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCVideoVolumeInfo) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PCVideoVolumeInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoVolumeInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = PCVideoVolumeType(v)
	}
	return nil
}

func (p *PCVideoVolumeInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Volume = v
	}
	return nil
}

func (p *PCVideoVolumeInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FadeInDuration = v
	}
	return nil
}

func (p *PCVideoVolumeInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FadeOutDuration = v
	}
	return nil
}

func (p *PCVideoVolumeInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoVolumeInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoVolumeInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoVolumeInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("volume", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:volume: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Volume)); err != nil {
		return fmt.Errorf("%T.volume (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:volume: %s", p, err)
	}
	return err
}

func (p *PCVideoVolumeInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fade_in_duration", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fade_in_duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FadeInDuration)); err != nil {
		return fmt.Errorf("%T.fade_in_duration (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fade_in_duration: %s", p, err)
	}
	return err
}

func (p *PCVideoVolumeInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fade_out_duration", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:fade_out_duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FadeOutDuration)); err != nil {
		return fmt.Errorf("%T.fade_out_duration (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:fade_out_duration: %s", p, err)
	}
	return err
}

func (p *PCVideoVolumeInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoVolumeInfo(%+v)", *p)
}

type PCVideoThumbnailRequest struct {
	CaptureTime []int32 `thrift:"capture_time,1" json:"capture_time"`
}

func NewPCVideoThumbnailRequest() *PCVideoThumbnailRequest {
	return &PCVideoThumbnailRequest{}
}

func (p *PCVideoThumbnailRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCVideoThumbnailRequest) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CaptureTime = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem45 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem45 = v
		}
		p.CaptureTime = append(p.CaptureTime, _elem45)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCVideoThumbnailRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCVideoThumbnailRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCVideoThumbnailRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CaptureTime != nil {
		if err := oprot.WriteFieldBegin("capture_time", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:capture_time: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CaptureTime)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CaptureTime {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:capture_time: %s", p, err)
		}
	}
	return err
}

func (p *PCVideoThumbnailRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCVideoThumbnailRequest(%+v)", *p)
}

type PCProcessImageInfo struct {
	ProcessType  PCProcessImageType `thrift:"process_type,1" json:"process_type"`
	TargetSize   *PCSSize           `thrift:"target_size,2" json:"target_size"`
	AllowMinSize *PCSSize           `thrift:"allow_min_size,3" json:"allow_min_size"`
	RatioTol     float64            `thrift:"ratio_tol,4" json:"ratio_tol"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	TargetImageType PCImageType `thrift:"target_image_type,10" json:"target_image_type"`
}

func NewPCProcessImageInfo() *PCProcessImageInfo {
	return &PCProcessImageInfo{
		ProcessType: math.MinInt32 - 1, // unset sentinal value

		TargetImageType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCProcessImageInfo) IsSetProcessType() bool {
	return int64(p.ProcessType) != math.MinInt32-1
}

func (p *PCProcessImageInfo) IsSetTargetImageType() bool {
	return int64(p.TargetImageType) != math.MinInt32-1
}

func (p *PCProcessImageInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCProcessImageInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ProcessType = PCProcessImageType(v)
	}
	return nil
}

func (p *PCProcessImageInfo) readField2(iprot thrift.TProtocol) error {
	p.TargetSize = NewPCSSize()
	if err := p.TargetSize.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TargetSize)
	}
	return nil
}

func (p *PCProcessImageInfo) readField3(iprot thrift.TProtocol) error {
	p.AllowMinSize = NewPCSSize()
	if err := p.AllowMinSize.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AllowMinSize)
	}
	return nil
}

func (p *PCProcessImageInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RatioTol = v
	}
	return nil
}

func (p *PCProcessImageInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TargetImageType = PCImageType(v)
	}
	return nil
}

func (p *PCProcessImageInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCProcessImageInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCProcessImageInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetProcessType() {
		if err := oprot.WriteFieldBegin("process_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:process_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProcessType)); err != nil {
			return fmt.Errorf("%T.process_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:process_type: %s", p, err)
		}
	}
	return err
}

func (p *PCProcessImageInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TargetSize != nil {
		if err := oprot.WriteFieldBegin("target_size", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:target_size: %s", p, err)
		}
		if err := p.TargetSize.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TargetSize)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:target_size: %s", p, err)
		}
	}
	return err
}

func (p *PCProcessImageInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AllowMinSize != nil {
		if err := oprot.WriteFieldBegin("allow_min_size", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:allow_min_size: %s", p, err)
		}
		if err := p.AllowMinSize.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AllowMinSize)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:allow_min_size: %s", p, err)
		}
	}
	return err
}

func (p *PCProcessImageInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ratio_tol", thrift.DOUBLE, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ratio_tol: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.RatioTol)); err != nil {
		return fmt.Errorf("%T.ratio_tol (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ratio_tol: %s", p, err)
	}
	return err
}

func (p *PCProcessImageInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetTargetImageType() {
		if err := oprot.WriteFieldBegin("target_image_type", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:target_image_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TargetImageType)); err != nil {
			return fmt.Errorf("%T.target_image_type (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:target_image_type: %s", p, err)
		}
	}
	return err
}

func (p *PCProcessImageInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCProcessImageInfo(%+v)", *p)
}

type PC17173VideoInfo struct {
	VideoId      int32    `thrift:"video_id,1" json:"video_id"`
	PageUrl      string   `thrift:"page_url,2" json:"page_url"`
	VideoTitle   string   `thrift:"video_title,3" json:"video_title"`
	Duration     int32    `thrift:"duration,4" json:"duration"`
	VideoUrlList []string `thrift:"video_url_list,5" json:"video_url_list"`
	IsHq         bool     `thrift:"is_hq,6" json:"is_hq"`
	UpdateTime   int32    `thrift:"update_time,7" json:"update_time"`
}

func NewPC17173VideoInfo() *PC17173VideoInfo {
	return &PC17173VideoInfo{}
}

func (p *PC17173VideoInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PC17173VideoInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.VideoId = v
	}
	return nil
}

func (p *PC17173VideoInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PageUrl = v
	}
	return nil
}

func (p *PC17173VideoInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.VideoTitle = v
	}
	return nil
}

func (p *PC17173VideoInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *PC17173VideoInfo) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.VideoUrlList = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem46 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem46 = v
		}
		p.VideoUrlList = append(p.VideoUrlList, _elem46)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PC17173VideoInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IsHq = v
	}
	return nil
}

func (p *PC17173VideoInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.UpdateTime = v
	}
	return nil
}

func (p *PC17173VideoInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PC17173VideoInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PC17173VideoInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:video_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoId)); err != nil {
		return fmt.Errorf("%T.video_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:video_id: %s", p, err)
	}
	return err
}

func (p *PC17173VideoInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("page_url", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:page_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PageUrl)); err != nil {
		return fmt.Errorf("%T.page_url (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:page_url: %s", p, err)
	}
	return err
}

func (p *PC17173VideoInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_title", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:video_title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VideoTitle)); err != nil {
		return fmt.Errorf("%T.video_title (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:video_title: %s", p, err)
	}
	return err
}

func (p *PC17173VideoInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:duration: %s", p, err)
	}
	return err
}

func (p *PC17173VideoInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.VideoUrlList != nil {
		if err := oprot.WriteFieldBegin("video_url_list", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:video_url_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.VideoUrlList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.VideoUrlList {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:video_url_list: %s", p, err)
		}
	}
	return err
}

func (p *PC17173VideoInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_hq", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:is_hq: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsHq)); err != nil {
		return fmt.Errorf("%T.is_hq (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:is_hq: %s", p, err)
	}
	return err
}

func (p *PC17173VideoInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update_time", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:update_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.update_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:update_time: %s", p, err)
	}
	return err
}

func (p *PC17173VideoInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PC17173VideoInfo(%+v)", *p)
}

type PCAssemblyImageElementInfo struct {
	Url           string `thrift:"url,1" json:"url"`
	ImageBase64   string `thrift:"image_base64,2" json:"image_base64"`
	Width         int32  `thrift:"width,3" json:"width"`
	Height        int32  `thrift:"height,4" json:"height"`
	ImgTop        int32  `thrift:"img_top,5" json:"img_top"`
	ImgLeft       int32  `thrift:"img_left,6" json:"img_left"`
	WindowWidth   int32  `thrift:"window_width,7" json:"window_width"`
	WindowHeight  int32  `thrift:"window_height,8" json:"window_height"`
	WindowPosTop  int32  `thrift:"window_pos_top,9" json:"window_pos_top"`
	WindowPosLeft int32  `thrift:"window_pos_left,10" json:"window_pos_left"`
}

func NewPCAssemblyImageElementInfo() *PCAssemblyImageElementInfo {
	return &PCAssemblyImageElementInfo{}
}

func (p *PCAssemblyImageElementInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ImageBase64 = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ImgTop = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ImgLeft = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.WindowWidth = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.WindowHeight = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.WindowPosTop = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.WindowPosLeft = v
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCAssemblyImageElementInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCAssemblyImageElementInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:url: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image_base64", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:image_base64: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImageBase64)); err != nil {
		return fmt.Errorf("%T.image_base64 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:image_base64: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:width: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:height: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img_top", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:img_top: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImgTop)); err != nil {
		return fmt.Errorf("%T.img_top (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:img_top: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img_left", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:img_left: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImgLeft)); err != nil {
		return fmt.Errorf("%T.img_left (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:img_left: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("window_width", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:window_width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WindowWidth)); err != nil {
		return fmt.Errorf("%T.window_width (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:window_width: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("window_height", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:window_height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WindowHeight)); err != nil {
		return fmt.Errorf("%T.window_height (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:window_height: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("window_pos_top", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:window_pos_top: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WindowPosTop)); err != nil {
		return fmt.Errorf("%T.window_pos_top (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:window_pos_top: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("window_pos_left", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:window_pos_left: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WindowPosLeft)); err != nil {
		return fmt.Errorf("%T.window_pos_left (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:window_pos_left: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageElementInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCAssemblyImageElementInfo(%+v)", *p)
}

type PCAssemblyImageResult struct {
	Url         string          `thrift:"url,1" json:"url"`
	ImageBase64 string          `thrift:"image_base64,2" json:"image_base64"`
	UgcInfo     *ugc.FileStruct `thrift:"ugc_info,3" json:"ugc_info"`
}

func NewPCAssemblyImageResult() *PCAssemblyImageResult {
	return &PCAssemblyImageResult{}
}

func (p *PCAssemblyImageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCAssemblyImageResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *PCAssemblyImageResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ImageBase64 = v
	}
	return nil
}

func (p *PCAssemblyImageResult) readField3(iprot thrift.TProtocol) error {
	p.UgcInfo = ugc.NewFileStruct()
	if err := p.UgcInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UgcInfo)
	}
	return nil
}

func (p *PCAssemblyImageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCAssemblyImageResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCAssemblyImageResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:url: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image_base64", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:image_base64: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImageBase64)); err != nil {
		return fmt.Errorf("%T.image_base64 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:image_base64: %s", p, err)
	}
	return err
}

func (p *PCAssemblyImageResult) writeField3(oprot thrift.TProtocol) (err error) {
	if p.UgcInfo != nil {
		if err := oprot.WriteFieldBegin("ugc_info", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ugc_info: %s", p, err)
		}
		if err := p.UgcInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UgcInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ugc_info: %s", p, err)
		}
	}
	return err
}

func (p *PCAssemblyImageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCAssemblyImageResult(%+v)", *p)
}

type PCMediaSeqVideoAudioInfo struct {
	AudioUrl  string  `thrift:"audio_url,1" json:"audio_url"`
	StartTime float64 `thrift:"start_time,2" json:"start_time"`
	Duration  float64 `thrift:"duration,3" json:"duration"`
	XVolume   float64 `thrift:"x_volume,4" json:"x_volume"`
	IsRepeat  bool    `thrift:"is_repeat,5" json:"is_repeat"`
}

func NewPCMediaSeqVideoAudioInfo() *PCMediaSeqVideoAudioInfo {
	return &PCMediaSeqVideoAudioInfo{}
}

func (p *PCMediaSeqVideoAudioInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoAudioInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AudioUrl = v
	}
	return nil
}

func (p *PCMediaSeqVideoAudioInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *PCMediaSeqVideoAudioInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *PCMediaSeqVideoAudioInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.XVolume = v
	}
	return nil
}

func (p *PCMediaSeqVideoAudioInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IsRepeat = v
	}
	return nil
}

func (p *PCMediaSeqVideoAudioInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCMediaSeqVideoAudioInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoAudioInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("audio_url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:audio_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AudioUrl)); err != nil {
		return fmt.Errorf("%T.audio_url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:audio_url: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoAudioInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:start_time: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:start_time: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoAudioInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.DOUBLE, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:duration: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:duration: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoAudioInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("x_volume", thrift.DOUBLE, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:x_volume: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.XVolume)); err != nil {
		return fmt.Errorf("%T.x_volume (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:x_volume: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoAudioInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_repeat", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:is_repeat: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsRepeat)); err != nil {
		return fmt.Errorf("%T.is_repeat (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:is_repeat: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoAudioInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCMediaSeqVideoAudioInfo(%+v)", *p)
}

type PCMediaSeqVideoImageOverlayInfo struct {
	OverlayImgUrl string                           `thrift:"overlay_img_url,1" json:"overlay_img_url"`
	OverlayHtml   string                           `thrift:"overlay_html,2" json:"overlay_html"`
	Size          *PCSSize                         `thrift:"size,3" json:"size"`
	Position      *PCSPosition                     `thrift:"position,4" json:"position"`
	EffectType    PCMediaSeqVideoOverlayEffectType `thrift:"effect_type,5" json:"effect_type"`
	StartTime     float64                          `thrift:"start_time,6" json:"start_time"`
	Duration      float64                          `thrift:"duration,7" json:"duration"`
}

func NewPCMediaSeqVideoImageOverlayInfo() *PCMediaSeqVideoImageOverlayInfo {
	return &PCMediaSeqVideoImageOverlayInfo{
		EffectType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCMediaSeqVideoImageOverlayInfo) IsSetEffectType() bool {
	return int64(p.EffectType) != math.MinInt32-1
}

func (p *PCMediaSeqVideoImageOverlayInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OverlayImgUrl = v
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OverlayHtml = v
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) readField3(iprot thrift.TProtocol) error {
	p.Size = NewPCSSize()
	if err := p.Size.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Size)
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) readField4(iprot thrift.TProtocol) error {
	p.Position = NewPCSPosition()
	if err := p.Position.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Position)
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EffectType = PCMediaSeqVideoOverlayEffectType(v)
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCMediaSeqVideoImageOverlayInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoImageOverlayInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("overlay_img_url", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:overlay_img_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OverlayImgUrl)); err != nil {
		return fmt.Errorf("%T.overlay_img_url (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:overlay_img_url: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoImageOverlayInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("overlay_html", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:overlay_html: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OverlayHtml)); err != nil {
		return fmt.Errorf("%T.overlay_html (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:overlay_html: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoImageOverlayInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Size != nil {
		if err := oprot.WriteFieldBegin("size", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:size: %s", p, err)
		}
		if err := p.Size.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Size)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:size: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoImageOverlayInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Position != nil {
		if err := oprot.WriteFieldBegin("position", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:position: %s", p, err)
		}
		if err := p.Position.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Position)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:position: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoImageOverlayInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetEffectType() {
		if err := oprot.WriteFieldBegin("effect_type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:effect_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.EffectType)); err != nil {
			return fmt.Errorf("%T.effect_type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:effect_type: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoImageOverlayInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.DOUBLE, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:start_time: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:start_time: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoImageOverlayInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.DOUBLE, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:duration: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:duration: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoImageOverlayInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCMediaSeqVideoImageOverlayInfo(%+v)", *p)
}

type PCMediaSeqVideoTransitionEffectInfo struct {
	TypeA1         PCMediaSeqVideoTransitionEffectType `thrift:"type,1" json:"type"`
	EffectDuration float64                             `thrift:"effect_duration,2" json:"effect_duration"`
}

func NewPCMediaSeqVideoTransitionEffectInfo() *PCMediaSeqVideoTransitionEffectInfo {
	return &PCMediaSeqVideoTransitionEffectInfo{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCMediaSeqVideoTransitionEffectInfo) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PCMediaSeqVideoTransitionEffectInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoTransitionEffectInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TypeA1 = PCMediaSeqVideoTransitionEffectType(v)
	}
	return nil
}

func (p *PCMediaSeqVideoTransitionEffectInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EffectDuration = v
	}
	return nil
}

func (p *PCMediaSeqVideoTransitionEffectInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCMediaSeqVideoTransitionEffectInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoTransitionEffectInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:type: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoTransitionEffectInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effect_duration", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:effect_duration: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.EffectDuration)); err != nil {
		return fmt.Errorf("%T.effect_duration (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:effect_duration: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoTransitionEffectInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCMediaSeqVideoTransitionEffectInfo(%+v)", *p)
}

type PCMediaSeqVideoSceneInfo struct {
	Index            int32                                `thrift:"index,1" json:"index"`
	MediaType        PCMediaSeqMediaType                  `thrift:"media_type,2" json:"media_type"`
	StartTime        float64                              `thrift:"start_time,3" json:"start_time"`
	Duration         float64                              `thrift:"duration,4" json:"duration"`
	MediaMaterialUrl string                               `thrift:"media_material_url,5" json:"media_material_url"`
	OverlayInfo      []*PCMediaSeqVideoImageOverlayInfo   `thrift:"overlay_info,6" json:"overlay_info"`
	TransitionEffect *PCMediaSeqVideoTransitionEffectInfo `thrift:"transition_effect,7" json:"transition_effect"`
}

func NewPCMediaSeqVideoSceneInfo() *PCMediaSeqVideoSceneInfo {
	return &PCMediaSeqVideoSceneInfo{
		MediaType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PCMediaSeqVideoSceneInfo) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *PCMediaSeqVideoSceneInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Index = v
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaType = PCMediaSeqMediaType(v)
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MediaMaterialUrl = v
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OverlayInfo = make([]*PCMediaSeqVideoImageOverlayInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem47 := NewPCMediaSeqVideoImageOverlayInfo()
		if err := _elem47.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem47)
		}
		p.OverlayInfo = append(p.OverlayInfo, _elem47)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) readField7(iprot thrift.TProtocol) error {
	p.TransitionEffect = NewPCMediaSeqVideoTransitionEffectInfo()
	if err := p.TransitionEffect.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TransitionEffect)
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCMediaSeqVideoSceneInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoSceneInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("index", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:index: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Index)); err != nil {
		return fmt.Errorf("%T.index (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:index: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoSceneInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaType() {
		if err := oprot.WriteFieldBegin("media_type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:media_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
			return fmt.Errorf("%T.media_type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:media_type: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoSceneInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.DOUBLE, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:start_time: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:start_time: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoSceneInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.DOUBLE, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:duration: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:duration: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoSceneInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_material_url", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:media_material_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaMaterialUrl)); err != nil {
		return fmt.Errorf("%T.media_material_url (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:media_material_url: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoSceneInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.OverlayInfo != nil {
		if err := oprot.WriteFieldBegin("overlay_info", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:overlay_info: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.OverlayInfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OverlayInfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:overlay_info: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoSceneInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if p.TransitionEffect != nil {
		if err := oprot.WriteFieldBegin("transition_effect", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:transition_effect: %s", p, err)
		}
		if err := p.TransitionEffect.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TransitionEffect)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:transition_effect: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoSceneInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCMediaSeqVideoSceneInfo(%+v)", *p)
}

type PCMediaSeqVideoComposInfo struct {
	Id                      int32                       `thrift:"id,1" json:"id"`
	MediaSeqVideoTemplateId int32                       `thrift:"media_seq_video_template_id,2" json:"media_seq_video_template_id"`
	VideoSize               *PCSSize                    `thrift:"video_size,3" json:"video_size"`
	VideoDuration           float64                     `thrift:"video_duration,4" json:"video_duration"`
	VideoBitrate            int32                       `thrift:"video_bitrate,5" json:"video_bitrate"`
	AudioBitrate            int32                       `thrift:"audio_bitrate,6" json:"audio_bitrate"`
	Fps                     int32                       `thrift:"fps,7" json:"fps"`
	FileSize                int32                       `thrift:"file_size,8" json:"file_size"`
	AudioTrackInfo          *PCMediaSeqVideoAudioInfo   `thrift:"audio_track_info,9" json:"audio_track_info"`
	SceneList               []*PCMediaSeqVideoSceneInfo `thrift:"scene_list,10" json:"scene_list"`
}

func NewPCMediaSeqVideoComposInfo() *PCMediaSeqVideoComposInfo {
	return &PCMediaSeqVideoComposInfo{}
}

func (p *PCMediaSeqVideoComposInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MediaSeqVideoTemplateId = v
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField3(iprot thrift.TProtocol) error {
	p.VideoSize = NewPCSSize()
	if err := p.VideoSize.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.VideoSize)
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.VideoDuration = v
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.VideoBitrate = v
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AudioBitrate = v
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Fps = v
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.FileSize = v
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField9(iprot thrift.TProtocol) error {
	p.AudioTrackInfo = NewPCMediaSeqVideoAudioInfo()
	if err := p.AudioTrackInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AudioTrackInfo)
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SceneList = make([]*PCMediaSeqVideoSceneInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem48 := NewPCMediaSeqVideoSceneInfo()
		if err := _elem48.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem48)
		}
		p.SceneList = append(p.SceneList, _elem48)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PCMediaSeqVideoComposInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PCMediaSeqVideoComposInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_seq_video_template_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:media_seq_video_template_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaSeqVideoTemplateId)); err != nil {
		return fmt.Errorf("%T.media_seq_video_template_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:media_seq_video_template_id: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.VideoSize != nil {
		if err := oprot.WriteFieldBegin("video_size", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:video_size: %s", p, err)
		}
		if err := p.VideoSize.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.VideoSize)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:video_size: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_duration", thrift.DOUBLE, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:video_duration: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.VideoDuration)); err != nil {
		return fmt.Errorf("%T.video_duration (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:video_duration: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("video_bitrate", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:video_bitrate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VideoBitrate)); err != nil {
		return fmt.Errorf("%T.video_bitrate (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:video_bitrate: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("audio_bitrate", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:audio_bitrate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AudioBitrate)); err != nil {
		return fmt.Errorf("%T.audio_bitrate (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:audio_bitrate: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fps", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:fps: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Fps)); err != nil {
		return fmt.Errorf("%T.fps (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:fps: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("file_size", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:file_size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FileSize)); err != nil {
		return fmt.Errorf("%T.file_size (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:file_size: %s", p, err)
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.AudioTrackInfo != nil {
		if err := oprot.WriteFieldBegin("audio_track_info", thrift.STRUCT, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:audio_track_info: %s", p, err)
		}
		if err := p.AudioTrackInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AudioTrackInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:audio_track_info: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if p.SceneList != nil {
		if err := oprot.WriteFieldBegin("scene_list", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:scene_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SceneList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SceneList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:scene_list: %s", p, err)
		}
	}
	return err
}

func (p *PCMediaSeqVideoComposInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PCMediaSeqVideoComposInfo(%+v)", *p)
}
