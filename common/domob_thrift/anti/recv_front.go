// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package anti

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/as"
	"rtb_model_server/common/domob_thrift/mqbroker"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = mqbroker.GoUnusedProtection__
var _ = as.GoUnusedProtection__

type RecvFront interface {
	// Parameters:
	//  - Rh
	//  - Clk
	RecvClk(rh *common.RequestHeader, clk *searchui_types.AdClick) (err error)
	// Parameters:
	//  - Rh
	//  - Imp
	RecvImp(rh *common.RequestHeader, imp *searchui_types.AdImpression) (err error)
	// Parameters:
	//  - Rh
	//  - Download
	RecvDownload(rh *common.RequestHeader, download *searchui_types.AdDownload) (err error)
	// Parameters:
	//  - Rh
	//  - Install
	RecvInstall(rh *common.RequestHeader, install *searchui_types.AdInstall) (err error)
}

type RecvFrontClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewRecvFrontClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *RecvFrontClient {
	return &RecvFrontClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewRecvFrontClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *RecvFrontClient {
	return &RecvFrontClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// Parameters:
//  - Rh
//  - Clk
func (p *RecvFrontClient) RecvClk(rh *common.RequestHeader, clk *searchui_types.AdClick) (err error) {
	if err = p.sendRecvClk(rh, clk); err != nil {
		return
	}
	return p.recvRecvClk()
}

func (p *RecvFrontClient) sendRecvClk(rh *common.RequestHeader, clk *searchui_types.AdClick) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("recvClk", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewRecvClkArgs()
	args0.Rh = rh
	args0.Clk = clk
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RecvFrontClient) recvRecvClk() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewRecvClkResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

// Parameters:
//  - Rh
//  - Imp
func (p *RecvFrontClient) RecvImp(rh *common.RequestHeader, imp *searchui_types.AdImpression) (err error) {
	if err = p.sendRecvImp(rh, imp); err != nil {
		return
	}
	return p.recvRecvImp()
}

func (p *RecvFrontClient) sendRecvImp(rh *common.RequestHeader, imp *searchui_types.AdImpression) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("recvImp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewRecvImpArgs()
	args4.Rh = rh
	args4.Imp = imp
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RecvFrontClient) recvRecvImp() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewRecvImpResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

// Parameters:
//  - Rh
//  - Download
func (p *RecvFrontClient) RecvDownload(rh *common.RequestHeader, download *searchui_types.AdDownload) (err error) {
	if err = p.sendRecvDownload(rh, download); err != nil {
		return
	}
	return p.recvRecvDownload()
}

func (p *RecvFrontClient) sendRecvDownload(rh *common.RequestHeader, download *searchui_types.AdDownload) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("recvDownload", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewRecvDownloadArgs()
	args8.Rh = rh
	args8.Download = download
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RecvFrontClient) recvRecvDownload() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewRecvDownloadResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

// Parameters:
//  - Rh
//  - Install
func (p *RecvFrontClient) RecvInstall(rh *common.RequestHeader, install *searchui_types.AdInstall) (err error) {
	if err = p.sendRecvInstall(rh, install); err != nil {
		return
	}
	return p.recvRecvInstall()
}

func (p *RecvFrontClient) sendRecvInstall(rh *common.RequestHeader, install *searchui_types.AdInstall) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("recvInstall", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewRecvInstallArgs()
	args12.Rh = rh
	args12.Install = install
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RecvFrontClient) recvRecvInstall() (err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewRecvInstallResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	return
}

type RecvFrontProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      RecvFront
}

func (p *RecvFrontProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *RecvFrontProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *RecvFrontProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewRecvFrontProcessor(handler RecvFront) *RecvFrontProcessor {

	self16 := &RecvFrontProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self16.processorMap["recvClk"] = &recvFrontProcessorRecvClk{handler: handler}
	self16.processorMap["recvImp"] = &recvFrontProcessorRecvImp{handler: handler}
	self16.processorMap["recvDownload"] = &recvFrontProcessorRecvDownload{handler: handler}
	self16.processorMap["recvInstall"] = &recvFrontProcessorRecvInstall{handler: handler}
	return self16
}

func (p *RecvFrontProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x17 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x17.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x17

}

type recvFrontProcessorRecvClk struct {
	handler RecvFront
}

func (p *recvFrontProcessorRecvClk) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRecvClkArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("recvClk", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRecvClkResult()
	if err = p.handler.RecvClk(args.Rh, args.Clk); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing recvClk: "+err.Error())
		oprot.WriteMessageBegin("recvClk", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("recvClk", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type recvFrontProcessorRecvImp struct {
	handler RecvFront
}

func (p *recvFrontProcessorRecvImp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRecvImpArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("recvImp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRecvImpResult()
	if err = p.handler.RecvImp(args.Rh, args.Imp); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing recvImp: "+err.Error())
		oprot.WriteMessageBegin("recvImp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("recvImp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type recvFrontProcessorRecvDownload struct {
	handler RecvFront
}

func (p *recvFrontProcessorRecvDownload) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRecvDownloadArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("recvDownload", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRecvDownloadResult()
	if err = p.handler.RecvDownload(args.Rh, args.Download); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing recvDownload: "+err.Error())
		oprot.WriteMessageBegin("recvDownload", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("recvDownload", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type recvFrontProcessorRecvInstall struct {
	handler RecvFront
}

func (p *recvFrontProcessorRecvInstall) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRecvInstallArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("recvInstall", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRecvInstallResult()
	if err = p.handler.RecvInstall(args.Rh, args.Install); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing recvInstall: "+err.Error())
		oprot.WriteMessageBegin("recvInstall", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("recvInstall", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type RecvClkArgs struct {
	Rh  *common.RequestHeader   `thrift:"rh,1" json:"rh"`
	Clk *searchui_types.AdClick `thrift:"clk,2" json:"clk"`
}

func NewRecvClkArgs() *RecvClkArgs {
	return &RecvClkArgs{}
}

func (p *RecvClkArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvClkArgs) readField1(iprot thrift.TProtocol) error {
	p.Rh = common.NewRequestHeader()
	if err := p.Rh.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rh)
	}
	return nil
}

func (p *RecvClkArgs) readField2(iprot thrift.TProtocol) error {
	p.Clk = searchui_types.NewAdClick()
	if err := p.Clk.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Clk)
	}
	return nil
}

func (p *RecvClkArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvClk_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvClkArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rh != nil {
		if err := oprot.WriteFieldBegin("rh", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rh: %s", p, err)
		}
		if err := p.Rh.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rh)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rh: %s", p, err)
		}
	}
	return err
}

func (p *RecvClkArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Clk != nil {
		if err := oprot.WriteFieldBegin("clk", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:clk: %s", p, err)
		}
		if err := p.Clk.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Clk)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:clk: %s", p, err)
		}
	}
	return err
}

func (p *RecvClkArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvClkArgs(%+v)", *p)
}

type RecvClkResult struct {
}

func NewRecvClkResult() *RecvClkResult {
	return &RecvClkResult{}
}

func (p *RecvClkResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvClkResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvClk_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvClkResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvClkResult(%+v)", *p)
}

type RecvImpArgs struct {
	Rh  *common.RequestHeader        `thrift:"rh,1" json:"rh"`
	Imp *searchui_types.AdImpression `thrift:"imp,2" json:"imp"`
}

func NewRecvImpArgs() *RecvImpArgs {
	return &RecvImpArgs{}
}

func (p *RecvImpArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvImpArgs) readField1(iprot thrift.TProtocol) error {
	p.Rh = common.NewRequestHeader()
	if err := p.Rh.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rh)
	}
	return nil
}

func (p *RecvImpArgs) readField2(iprot thrift.TProtocol) error {
	p.Imp = searchui_types.NewAdImpression()
	if err := p.Imp.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Imp)
	}
	return nil
}

func (p *RecvImpArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvImp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvImpArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rh != nil {
		if err := oprot.WriteFieldBegin("rh", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rh: %s", p, err)
		}
		if err := p.Rh.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rh)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rh: %s", p, err)
		}
	}
	return err
}

func (p *RecvImpArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Imp != nil {
		if err := oprot.WriteFieldBegin("imp", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:imp: %s", p, err)
		}
		if err := p.Imp.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Imp)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:imp: %s", p, err)
		}
	}
	return err
}

func (p *RecvImpArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvImpArgs(%+v)", *p)
}

type RecvImpResult struct {
}

func NewRecvImpResult() *RecvImpResult {
	return &RecvImpResult{}
}

func (p *RecvImpResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvImpResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvImp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvImpResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvImpResult(%+v)", *p)
}

type RecvDownloadArgs struct {
	Rh       *common.RequestHeader      `thrift:"rh,1" json:"rh"`
	Download *searchui_types.AdDownload `thrift:"download,2" json:"download"`
}

func NewRecvDownloadArgs() *RecvDownloadArgs {
	return &RecvDownloadArgs{}
}

func (p *RecvDownloadArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvDownloadArgs) readField1(iprot thrift.TProtocol) error {
	p.Rh = common.NewRequestHeader()
	if err := p.Rh.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rh)
	}
	return nil
}

func (p *RecvDownloadArgs) readField2(iprot thrift.TProtocol) error {
	p.Download = searchui_types.NewAdDownload()
	if err := p.Download.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Download)
	}
	return nil
}

func (p *RecvDownloadArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvDownload_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvDownloadArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rh != nil {
		if err := oprot.WriteFieldBegin("rh", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rh: %s", p, err)
		}
		if err := p.Rh.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rh)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rh: %s", p, err)
		}
	}
	return err
}

func (p *RecvDownloadArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Download != nil {
		if err := oprot.WriteFieldBegin("download", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:download: %s", p, err)
		}
		if err := p.Download.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Download)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:download: %s", p, err)
		}
	}
	return err
}

func (p *RecvDownloadArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvDownloadArgs(%+v)", *p)
}

type RecvDownloadResult struct {
}

func NewRecvDownloadResult() *RecvDownloadResult {
	return &RecvDownloadResult{}
}

func (p *RecvDownloadResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvDownloadResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvDownload_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvDownloadResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvDownloadResult(%+v)", *p)
}

type RecvInstallArgs struct {
	Rh      *common.RequestHeader     `thrift:"rh,1" json:"rh"`
	Install *searchui_types.AdInstall `thrift:"install,2" json:"install"`
}

func NewRecvInstallArgs() *RecvInstallArgs {
	return &RecvInstallArgs{}
}

func (p *RecvInstallArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvInstallArgs) readField1(iprot thrift.TProtocol) error {
	p.Rh = common.NewRequestHeader()
	if err := p.Rh.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rh)
	}
	return nil
}

func (p *RecvInstallArgs) readField2(iprot thrift.TProtocol) error {
	p.Install = searchui_types.NewAdInstall()
	if err := p.Install.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Install)
	}
	return nil
}

func (p *RecvInstallArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvInstall_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvInstallArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rh != nil {
		if err := oprot.WriteFieldBegin("rh", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rh: %s", p, err)
		}
		if err := p.Rh.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rh)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rh: %s", p, err)
		}
	}
	return err
}

func (p *RecvInstallArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Install != nil {
		if err := oprot.WriteFieldBegin("install", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:install: %s", p, err)
		}
		if err := p.Install.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Install)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:install: %s", p, err)
		}
	}
	return err
}

func (p *RecvInstallArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvInstallArgs(%+v)", *p)
}

type RecvInstallResult struct {
}

func NewRecvInstallResult() *RecvInstallResult {
	return &RecvInstallResult{}
}

func (p *RecvInstallResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RecvInstallResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("recvInstall_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RecvInstallResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RecvInstallResult(%+v)", *p)
}
