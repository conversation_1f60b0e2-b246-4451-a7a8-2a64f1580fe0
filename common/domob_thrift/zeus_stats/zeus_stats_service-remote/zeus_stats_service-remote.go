// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"zeus_stats"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "   getAdsetForAutoStats(RequestHeader header, i32 start_dt, i32 start_hr,  adset_ids, i64 zeus_campaign_id,  ignore_dts)")
	fmt.Fprintln(os.<PERSON>, "  bool groupSatisfyMetricConditions(RequestHeader header, i32 start_dt, i32 start_hr, ZeusMetricComparer metric_comparer,  adset_ids, i64 zeus_campaign_id)")
	fmt.Fprintln(os.Stderr, "   filterAdSetsInAutoStatusByStats(RequestHeader header, i32 start_dt, i32 start_hr,  metric_conditions, ZeusMetricComparer metric_comparer,  adset_ids, i64 zeus_campaign_id)")
	fmt.Fprintln(os.Stderr, "   checkCampaignBudgetForPause(RequestHeader header,  campaign_id_budget_map, i32 dt, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "   checkCampaignInstallForPause(RequestHeader header,  campaign_id_install_map, i32 dt, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "  i32 updateZeusAdStats(RequestHeader header, i64 campaign_id,  ad_stats, i32 dt, i32 hr, i32 timezone_offset_hours_with_local)")
	fmt.Fprintln(os.Stderr, "   fetchAdSetByCondition(RequestHeader header, i32 start_dt, i32 start_hr,  metric_condition_list,  campaign_list, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "   queryMetricsByTargetInDt(RequestHeader header, string target_type,  metric_list, i64 campaign_id, i32 dt)")
	fmt.Fprintln(os.Stderr, "   queryMetricsByTargetInHr(RequestHeader header, string target_type,  metric_list, i64 campaign_id, i32 dt, i32 hr)")
	fmt.Fprintln(os.Stderr, "   getStatsByCampaignIdList(RequestHeader header,  campaign_id_list, i32 start_time, i32 end_time, i64 fb_account_id)")
	fmt.Fprintln(os.Stderr, "   getSortedCampaignStatsDataListByCampaignIds(RequestHeader header,  campaign_id_list, i32 start_time, i32 end_time,  order_columns, i64 fb_account_id, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getDtMappingCampaignDataByCampaignId(RequestHeader header, i64 campaign_id, i32 start_hr, i32 end_hr, ZeusDateType type)")
	fmt.Fprintln(os.Stderr, "   getDtMappingCampaignDataByAccountId(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i32 start_hr, i32 end_hr, ZeusDateType type)")
	fmt.Fprintln(os.Stderr, "   getImageIdMappingAdDataByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, string country, i32 start_time, i32 end_time, i64 offset, i64 limit,  order_columns)")
	fmt.Fprintln(os.Stderr, "   getAdStatsDataListByUserInfoImageId(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i64 image_id, i32 start_time, i32 end_time, string targeting, string order_by, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getAdStatsDataListByUserInfoImageIds(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id,  image_ids, i32 start_time, i32 end_time, string targeting, string order_by, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getImageIdMappingAdDataByCampaignId(RequestHeader header, i64 campaign_id, i32 start_time, i32 end_time, i64 offset, i64 limit,  order_columns)")
	fmt.Fprintln(os.Stderr, "   getAdStatsDataListByCampaignImageId(RequestHeader header, i64 campaign_id, i64 image_id, i32 start_time, i32 end_time, string targeting, string order_by)")
	fmt.Fprintln(os.Stderr, "   getVideoIdMappingAdDataByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, string country, i32 start_time, i32 end_time, i64 offset, i64 limit,  order_columns)")
	fmt.Fprintln(os.Stderr, "   getAdStatsDataListByUserInfoVideoId(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i64 video_id, i32 start_time, i32 end_time, string targeting, string order_by)")
	fmt.Fprintln(os.Stderr, "   getVideoIdMappingAdDataByCampaignId(RequestHeader header, i64 campaign_id, i32 start_time, i32 end_time, i64 offset, i64 limit,  order_columns)")
	fmt.Fprintln(os.Stderr, "   getAdStatsDataListByCampaignVideoId(RequestHeader header, i64 campaign_id, i64 video_id, i32 start_time, i32 end_time, string targeting, string order_by)")
	fmt.Fprintln(os.Stderr, "   getCreativeIdMappingAdDataByCampaignId(RequestHeader header, i64 campaign_id, i32 start_time, i32 end_time, i64 offset, i64 limit,  order_columns)")
	fmt.Fprintln(os.Stderr, "   getAdStatsDataListByCampaignCreativeId(RequestHeader header, i64 campaign_id, i64 creative_id, i32 start_time, i32 end_time, string targeting, string order_by)")
	fmt.Fprintln(os.Stderr, "   getCampaignStatsDataListByUserInfo(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i32 start_time, i32 end_time, string targeting, string order_by, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getCampaignStatsDataListByCampaignId(RequestHeader header, i64 campaign_id, i32 start_time, i32 end_time, string targeting, string order_by)")
	fmt.Fprintln(os.Stderr, "   getAccountCampaignStatsDataListByFbPromotionId(RequestHeader header, i64 zeus_user_id, i32 start_time, i32 end_time, i64 fb_promotion_id, string order_by)")
	fmt.Fprintln(os.Stderr, "   getDtMappingAdDataByImageIds(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id,  image_ids, i32 start_time, i32 end_time, ZeusDateType type)")
	fmt.Fprintln(os.Stderr, "   getDtMappingAdDataByVideoIds(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id,  video_ids, i32 start_time, i32 end_time, ZeusDateType type)")
	fmt.Fprintln(os.Stderr, "   getAdStatsDataListByCampaignId(RequestHeader header, i64 zeus_user_id, i64 fb_user_id, i64 fb_account_id, i64 campaign_id, i64 limit, i32 start_time, i32 end_time, ZeusDateType type, string order_by)")
	fmt.Fprintln(os.Stderr, "   getDtMappingCampaignDataByFbPromotionId(RequestHeader header, i64 zeus_user_id, i64 fb_promotion_id, i32 start_hr, i32 end_hr, ZeusDateType type)")
	fmt.Fprintln(os.Stderr, "   getCampaignStatsDataListByFbPromotionId(RequestHeader header, i64 zeus_user_id, i64 fb_promotion_id, i32 start_hr, i32 end_hr, string targeting, string order_by)")
	fmt.Fprintln(os.Stderr, "   getLibMappingAdDataByFbPromotionId(RequestHeader header, i64 zeus_user_id, i64 fb_promotion_id, i32 column, i32 start_time, i32 end_time, string country, i64 offset, i64 limit,  order_columns)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := zeus_stats.NewZeusStatsServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getAdsetForAutoStats":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetAdsetForAutoStats requires 6 args")
			flag.Usage()
		}
		arg205 := flag.Arg(1)
		mbTrans206 := thrift.NewTMemoryBufferLen(len(arg205))
		defer mbTrans206.Close()
		_, err207 := mbTrans206.WriteString(arg205)
		if err207 != nil {
			Usage()
			return
		}
		factory208 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt209 := factory208.GetProtocol(mbTrans206)
		argvalue0 := zeus_stats.NewRequestHeader()
		err210 := argvalue0.Read(jsProt209)
		if err210 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err211 := (strconv.Atoi(flag.Arg(2)))
		if err211 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err212 := (strconv.Atoi(flag.Arg(3)))
		if err212 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg213 := flag.Arg(4)
		mbTrans214 := thrift.NewTMemoryBufferLen(len(arg213))
		defer mbTrans214.Close()
		_, err215 := mbTrans214.WriteString(arg213)
		if err215 != nil {
			Usage()
			return
		}
		factory216 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt217 := factory216.GetProtocol(mbTrans214)
		containerStruct3 := zeus_stats.NewGetAdsetForAutoStatsArgs()
		err218 := containerStruct3.ReadField4(jsProt217)
		if err218 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.AdsetIds
		value3 := argvalue3
		argvalue4, err219 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err219 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg220 := flag.Arg(6)
		mbTrans221 := thrift.NewTMemoryBufferLen(len(arg220))
		defer mbTrans221.Close()
		_, err222 := mbTrans221.WriteString(arg220)
		if err222 != nil {
			Usage()
			return
		}
		factory223 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt224 := factory223.GetProtocol(mbTrans221)
		containerStruct5 := zeus_stats.NewGetAdsetForAutoStatsArgs()
		err225 := containerStruct5.ReadField6(jsProt224)
		if err225 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.IgnoreDts
		value5 := argvalue5
		fmt.Print(client.GetAdsetForAutoStats(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "groupSatisfyMetricConditions":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GroupSatisfyMetricConditions requires 6 args")
			flag.Usage()
		}
		arg226 := flag.Arg(1)
		mbTrans227 := thrift.NewTMemoryBufferLen(len(arg226))
		defer mbTrans227.Close()
		_, err228 := mbTrans227.WriteString(arg226)
		if err228 != nil {
			Usage()
			return
		}
		factory229 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt230 := factory229.GetProtocol(mbTrans227)
		argvalue0 := zeus_stats.NewRequestHeader()
		err231 := argvalue0.Read(jsProt230)
		if err231 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err232 := (strconv.Atoi(flag.Arg(2)))
		if err232 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err233 := (strconv.Atoi(flag.Arg(3)))
		if err233 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg234 := flag.Arg(4)
		mbTrans235 := thrift.NewTMemoryBufferLen(len(arg234))
		defer mbTrans235.Close()
		_, err236 := mbTrans235.WriteString(arg234)
		if err236 != nil {
			Usage()
			return
		}
		factory237 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt238 := factory237.GetProtocol(mbTrans235)
		argvalue3 := zeus_stats.NewZeusMetricComparer()
		err239 := argvalue3.Read(jsProt238)
		if err239 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg240 := flag.Arg(5)
		mbTrans241 := thrift.NewTMemoryBufferLen(len(arg240))
		defer mbTrans241.Close()
		_, err242 := mbTrans241.WriteString(arg240)
		if err242 != nil {
			Usage()
			return
		}
		factory243 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt244 := factory243.GetProtocol(mbTrans241)
		containerStruct4 := zeus_stats.NewGroupSatisfyMetricConditionsArgs()
		err245 := containerStruct4.ReadField5(jsProt244)
		if err245 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.AdsetIds
		value4 := argvalue4
		argvalue5, err246 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err246 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		fmt.Print(client.GroupSatisfyMetricConditions(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "filterAdSetsInAutoStatusByStats":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "FilterAdSetsInAutoStatusByStats requires 7 args")
			flag.Usage()
		}
		arg247 := flag.Arg(1)
		mbTrans248 := thrift.NewTMemoryBufferLen(len(arg247))
		defer mbTrans248.Close()
		_, err249 := mbTrans248.WriteString(arg247)
		if err249 != nil {
			Usage()
			return
		}
		factory250 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt251 := factory250.GetProtocol(mbTrans248)
		argvalue0 := zeus_stats.NewRequestHeader()
		err252 := argvalue0.Read(jsProt251)
		if err252 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err253 := (strconv.Atoi(flag.Arg(2)))
		if err253 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err254 := (strconv.Atoi(flag.Arg(3)))
		if err254 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg255 := flag.Arg(4)
		mbTrans256 := thrift.NewTMemoryBufferLen(len(arg255))
		defer mbTrans256.Close()
		_, err257 := mbTrans256.WriteString(arg255)
		if err257 != nil {
			Usage()
			return
		}
		factory258 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt259 := factory258.GetProtocol(mbTrans256)
		containerStruct3 := zeus_stats.NewFilterAdSetsInAutoStatusByStatsArgs()
		err260 := containerStruct3.ReadField4(jsProt259)
		if err260 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.MetricConditions
		value3 := argvalue3
		arg261 := flag.Arg(5)
		mbTrans262 := thrift.NewTMemoryBufferLen(len(arg261))
		defer mbTrans262.Close()
		_, err263 := mbTrans262.WriteString(arg261)
		if err263 != nil {
			Usage()
			return
		}
		factory264 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt265 := factory264.GetProtocol(mbTrans262)
		argvalue4 := zeus_stats.NewZeusMetricComparer()
		err266 := argvalue4.Read(jsProt265)
		if err266 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		arg267 := flag.Arg(6)
		mbTrans268 := thrift.NewTMemoryBufferLen(len(arg267))
		defer mbTrans268.Close()
		_, err269 := mbTrans268.WriteString(arg267)
		if err269 != nil {
			Usage()
			return
		}
		factory270 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt271 := factory270.GetProtocol(mbTrans268)
		containerStruct5 := zeus_stats.NewFilterAdSetsInAutoStatusByStatsArgs()
		err272 := containerStruct5.ReadField6(jsProt271)
		if err272 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.AdsetIds
		value5 := argvalue5
		argvalue6, err273 := (strconv.ParseInt(flag.Arg(7), 10, 64))
		if err273 != nil {
			Usage()
			return
		}
		value6 := argvalue6
		fmt.Print(client.FilterAdSetsInAutoStatusByStats(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "checkCampaignBudgetForPause":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "CheckCampaignBudgetForPause requires 4 args")
			flag.Usage()
		}
		arg274 := flag.Arg(1)
		mbTrans275 := thrift.NewTMemoryBufferLen(len(arg274))
		defer mbTrans275.Close()
		_, err276 := mbTrans275.WriteString(arg274)
		if err276 != nil {
			Usage()
			return
		}
		factory277 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt278 := factory277.GetProtocol(mbTrans275)
		argvalue0 := zeus_stats.NewRequestHeader()
		err279 := argvalue0.Read(jsProt278)
		if err279 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg280 := flag.Arg(2)
		mbTrans281 := thrift.NewTMemoryBufferLen(len(arg280))
		defer mbTrans281.Close()
		_, err282 := mbTrans281.WriteString(arg280)
		if err282 != nil {
			Usage()
			return
		}
		factory283 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt284 := factory283.GetProtocol(mbTrans281)
		containerStruct1 := zeus_stats.NewCheckCampaignBudgetForPauseArgs()
		err285 := containerStruct1.ReadField2(jsProt284)
		if err285 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIdBudgetMap
		value1 := argvalue1
		tmp2, err286 := (strconv.Atoi(flag.Arg(3)))
		if err286 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err287 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err287 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.CheckCampaignBudgetForPause(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "checkCampaignInstallForPause":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "CheckCampaignInstallForPause requires 4 args")
			flag.Usage()
		}
		arg288 := flag.Arg(1)
		mbTrans289 := thrift.NewTMemoryBufferLen(len(arg288))
		defer mbTrans289.Close()
		_, err290 := mbTrans289.WriteString(arg288)
		if err290 != nil {
			Usage()
			return
		}
		factory291 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt292 := factory291.GetProtocol(mbTrans289)
		argvalue0 := zeus_stats.NewRequestHeader()
		err293 := argvalue0.Read(jsProt292)
		if err293 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg294 := flag.Arg(2)
		mbTrans295 := thrift.NewTMemoryBufferLen(len(arg294))
		defer mbTrans295.Close()
		_, err296 := mbTrans295.WriteString(arg294)
		if err296 != nil {
			Usage()
			return
		}
		factory297 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt298 := factory297.GetProtocol(mbTrans295)
		containerStruct1 := zeus_stats.NewCheckCampaignInstallForPauseArgs()
		err299 := containerStruct1.ReadField2(jsProt298)
		if err299 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIdInstallMap
		value1 := argvalue1
		tmp2, err300 := (strconv.Atoi(flag.Arg(3)))
		if err300 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err301 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err301 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.CheckCampaignInstallForPause(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updateZeusAdStats":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "UpdateZeusAdStats requires 6 args")
			flag.Usage()
		}
		arg302 := flag.Arg(1)
		mbTrans303 := thrift.NewTMemoryBufferLen(len(arg302))
		defer mbTrans303.Close()
		_, err304 := mbTrans303.WriteString(arg302)
		if err304 != nil {
			Usage()
			return
		}
		factory305 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt306 := factory305.GetProtocol(mbTrans303)
		argvalue0 := zeus_stats.NewRequestHeader()
		err307 := argvalue0.Read(jsProt306)
		if err307 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err308 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err308 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg309 := flag.Arg(3)
		mbTrans310 := thrift.NewTMemoryBufferLen(len(arg309))
		defer mbTrans310.Close()
		_, err311 := mbTrans310.WriteString(arg309)
		if err311 != nil {
			Usage()
			return
		}
		factory312 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt313 := factory312.GetProtocol(mbTrans310)
		containerStruct2 := zeus_stats.NewUpdateZeusAdStatsArgs()
		err314 := containerStruct2.ReadField3(jsProt313)
		if err314 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AdStats
		value2 := argvalue2
		tmp3, err315 := (strconv.Atoi(flag.Arg(4)))
		if err315 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err316 := (strconv.Atoi(flag.Arg(5)))
		if err316 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err317 := (strconv.Atoi(flag.Arg(6)))
		if err317 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.UpdateZeusAdStats(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "fetchAdSetByCondition":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "FetchAdSetByCondition requires 6 args")
			flag.Usage()
		}
		arg318 := flag.Arg(1)
		mbTrans319 := thrift.NewTMemoryBufferLen(len(arg318))
		defer mbTrans319.Close()
		_, err320 := mbTrans319.WriteString(arg318)
		if err320 != nil {
			Usage()
			return
		}
		factory321 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt322 := factory321.GetProtocol(mbTrans319)
		argvalue0 := zeus_stats.NewRequestHeader()
		err323 := argvalue0.Read(jsProt322)
		if err323 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err324 := (strconv.Atoi(flag.Arg(2)))
		if err324 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err325 := (strconv.Atoi(flag.Arg(3)))
		if err325 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg326 := flag.Arg(4)
		mbTrans327 := thrift.NewTMemoryBufferLen(len(arg326))
		defer mbTrans327.Close()
		_, err328 := mbTrans327.WriteString(arg326)
		if err328 != nil {
			Usage()
			return
		}
		factory329 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt330 := factory329.GetProtocol(mbTrans327)
		containerStruct3 := zeus_stats.NewFetchAdSetByConditionArgs()
		err331 := containerStruct3.ReadField4(jsProt330)
		if err331 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.MetricConditionList
		value3 := argvalue3
		arg332 := flag.Arg(5)
		mbTrans333 := thrift.NewTMemoryBufferLen(len(arg332))
		defer mbTrans333.Close()
		_, err334 := mbTrans333.WriteString(arg332)
		if err334 != nil {
			Usage()
			return
		}
		factory335 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt336 := factory335.GetProtocol(mbTrans333)
		containerStruct4 := zeus_stats.NewFetchAdSetByConditionArgs()
		err337 := containerStruct4.ReadField5(jsProt336)
		if err337 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.CampaignList
		value4 := argvalue4
		argvalue5, err338 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err338 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		fmt.Print(client.FetchAdSetByCondition(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "queryMetricsByTargetInDt":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryMetricsByTargetInDt requires 5 args")
			flag.Usage()
		}
		arg339 := flag.Arg(1)
		mbTrans340 := thrift.NewTMemoryBufferLen(len(arg339))
		defer mbTrans340.Close()
		_, err341 := mbTrans340.WriteString(arg339)
		if err341 != nil {
			Usage()
			return
		}
		factory342 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt343 := factory342.GetProtocol(mbTrans340)
		argvalue0 := zeus_stats.NewRequestHeader()
		err344 := argvalue0.Read(jsProt343)
		if err344 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg346 := flag.Arg(3)
		mbTrans347 := thrift.NewTMemoryBufferLen(len(arg346))
		defer mbTrans347.Close()
		_, err348 := mbTrans347.WriteString(arg346)
		if err348 != nil {
			Usage()
			return
		}
		factory349 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt350 := factory349.GetProtocol(mbTrans347)
		containerStruct2 := zeus_stats.NewQueryMetricsByTargetInDtArgs()
		err351 := containerStruct2.ReadField3(jsProt350)
		if err351 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MetricList
		value2 := argvalue2
		argvalue3, err352 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err352 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err353 := (strconv.Atoi(flag.Arg(5)))
		if err353 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryMetricsByTargetInDt(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "queryMetricsByTargetInHr":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "QueryMetricsByTargetInHr requires 6 args")
			flag.Usage()
		}
		arg354 := flag.Arg(1)
		mbTrans355 := thrift.NewTMemoryBufferLen(len(arg354))
		defer mbTrans355.Close()
		_, err356 := mbTrans355.WriteString(arg354)
		if err356 != nil {
			Usage()
			return
		}
		factory357 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt358 := factory357.GetProtocol(mbTrans355)
		argvalue0 := zeus_stats.NewRequestHeader()
		err359 := argvalue0.Read(jsProt358)
		if err359 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		arg361 := flag.Arg(3)
		mbTrans362 := thrift.NewTMemoryBufferLen(len(arg361))
		defer mbTrans362.Close()
		_, err363 := mbTrans362.WriteString(arg361)
		if err363 != nil {
			Usage()
			return
		}
		factory364 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt365 := factory364.GetProtocol(mbTrans362)
		containerStruct2 := zeus_stats.NewQueryMetricsByTargetInHrArgs()
		err366 := containerStruct2.ReadField3(jsProt365)
		if err366 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MetricList
		value2 := argvalue2
		argvalue3, err367 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err367 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err368 := (strconv.Atoi(flag.Arg(5)))
		if err368 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err369 := (strconv.Atoi(flag.Arg(6)))
		if err369 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.QueryMetricsByTargetInHr(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getStatsByCampaignIdList":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetStatsByCampaignIdList requires 5 args")
			flag.Usage()
		}
		arg370 := flag.Arg(1)
		mbTrans371 := thrift.NewTMemoryBufferLen(len(arg370))
		defer mbTrans371.Close()
		_, err372 := mbTrans371.WriteString(arg370)
		if err372 != nil {
			Usage()
			return
		}
		factory373 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt374 := factory373.GetProtocol(mbTrans371)
		argvalue0 := zeus_stats.NewRequestHeader()
		err375 := argvalue0.Read(jsProt374)
		if err375 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg376 := flag.Arg(2)
		mbTrans377 := thrift.NewTMemoryBufferLen(len(arg376))
		defer mbTrans377.Close()
		_, err378 := mbTrans377.WriteString(arg376)
		if err378 != nil {
			Usage()
			return
		}
		factory379 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt380 := factory379.GetProtocol(mbTrans377)
		containerStruct1 := zeus_stats.NewGetStatsByCampaignIdListArgs()
		err381 := containerStruct1.ReadField2(jsProt380)
		if err381 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIdList
		value1 := argvalue1
		tmp2, err382 := (strconv.Atoi(flag.Arg(3)))
		if err382 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err383 := (strconv.Atoi(flag.Arg(4)))
		if err383 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4, err384 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err384 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		fmt.Print(client.GetStatsByCampaignIdList(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getSortedCampaignStatsDataListByCampaignIds":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "GetSortedCampaignStatsDataListByCampaignIds requires 8 args")
			flag.Usage()
		}
		arg385 := flag.Arg(1)
		mbTrans386 := thrift.NewTMemoryBufferLen(len(arg385))
		defer mbTrans386.Close()
		_, err387 := mbTrans386.WriteString(arg385)
		if err387 != nil {
			Usage()
			return
		}
		factory388 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt389 := factory388.GetProtocol(mbTrans386)
		argvalue0 := zeus_stats.NewRequestHeader()
		err390 := argvalue0.Read(jsProt389)
		if err390 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg391 := flag.Arg(2)
		mbTrans392 := thrift.NewTMemoryBufferLen(len(arg391))
		defer mbTrans392.Close()
		_, err393 := mbTrans392.WriteString(arg391)
		if err393 != nil {
			Usage()
			return
		}
		factory394 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt395 := factory394.GetProtocol(mbTrans392)
		containerStruct1 := zeus_stats.NewGetSortedCampaignStatsDataListByCampaignIdsArgs()
		err396 := containerStruct1.ReadField2(jsProt395)
		if err396 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIdList
		value1 := argvalue1
		tmp2, err397 := (strconv.Atoi(flag.Arg(3)))
		if err397 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err398 := (strconv.Atoi(flag.Arg(4)))
		if err398 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg399 := flag.Arg(5)
		mbTrans400 := thrift.NewTMemoryBufferLen(len(arg399))
		defer mbTrans400.Close()
		_, err401 := mbTrans400.WriteString(arg399)
		if err401 != nil {
			Usage()
			return
		}
		factory402 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt403 := factory402.GetProtocol(mbTrans400)
		containerStruct4 := zeus_stats.NewGetSortedCampaignStatsDataListByCampaignIdsArgs()
		err404 := containerStruct4.ReadField5(jsProt403)
		if err404 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.OrderColumns
		value4 := argvalue4
		argvalue5, err405 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err405 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		argvalue6, err406 := (strconv.ParseInt(flag.Arg(7), 10, 64))
		if err406 != nil {
			Usage()
			return
		}
		value6 := argvalue6
		argvalue7, err407 := (strconv.ParseInt(flag.Arg(8), 10, 64))
		if err407 != nil {
			Usage()
			return
		}
		value7 := argvalue7
		fmt.Print(client.GetSortedCampaignStatsDataListByCampaignIds(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getDtMappingCampaignDataByCampaignId":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetDtMappingCampaignDataByCampaignId requires 5 args")
			flag.Usage()
		}
		arg408 := flag.Arg(1)
		mbTrans409 := thrift.NewTMemoryBufferLen(len(arg408))
		defer mbTrans409.Close()
		_, err410 := mbTrans409.WriteString(arg408)
		if err410 != nil {
			Usage()
			return
		}
		factory411 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt412 := factory411.GetProtocol(mbTrans409)
		argvalue0 := zeus_stats.NewRequestHeader()
		err413 := argvalue0.Read(jsProt412)
		if err413 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err414 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err414 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err415 := (strconv.Atoi(flag.Arg(3)))
		if err415 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err416 := (strconv.Atoi(flag.Arg(4)))
		if err416 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := zeus_stats.ZeusDateType(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetDtMappingCampaignDataByCampaignId(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getDtMappingCampaignDataByAccountId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetDtMappingCampaignDataByAccountId requires 7 args")
			flag.Usage()
		}
		arg417 := flag.Arg(1)
		mbTrans418 := thrift.NewTMemoryBufferLen(len(arg417))
		defer mbTrans418.Close()
		_, err419 := mbTrans418.WriteString(arg417)
		if err419 != nil {
			Usage()
			return
		}
		factory420 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt421 := factory420.GetProtocol(mbTrans418)
		argvalue0 := zeus_stats.NewRequestHeader()
		err422 := argvalue0.Read(jsProt421)
		if err422 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err423 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err423 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err424 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err424 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err425 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err425 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err426 := (strconv.Atoi(flag.Arg(5)))
		if err426 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err427 := (strconv.Atoi(flag.Arg(6)))
		if err427 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err := (strconv.Atoi(flag.Arg(7)))
		if err != nil {
			Usage()
			return
		}
		argvalue6 := zeus_stats.ZeusDateType(tmp6)
		value6 := argvalue6
		fmt.Print(client.GetDtMappingCampaignDataByAccountId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getImageIdMappingAdDataByUserInfo":
		if flag.NArg()-1 != 10 {
			fmt.Fprintln(os.Stderr, "GetImageIdMappingAdDataByUserInfo requires 10 args")
			flag.Usage()
		}
		arg428 := flag.Arg(1)
		mbTrans429 := thrift.NewTMemoryBufferLen(len(arg428))
		defer mbTrans429.Close()
		_, err430 := mbTrans429.WriteString(arg428)
		if err430 != nil {
			Usage()
			return
		}
		factory431 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt432 := factory431.GetProtocol(mbTrans429)
		argvalue0 := zeus_stats.NewRequestHeader()
		err433 := argvalue0.Read(jsProt432)
		if err433 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err434 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err434 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err435 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err435 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err436 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err436 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		tmp5, err438 := (strconv.Atoi(flag.Arg(6)))
		if err438 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err439 := (strconv.Atoi(flag.Arg(7)))
		if err439 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		argvalue7, err440 := (strconv.ParseInt(flag.Arg(8), 10, 64))
		if err440 != nil {
			Usage()
			return
		}
		value7 := argvalue7
		argvalue8, err441 := (strconv.ParseInt(flag.Arg(9), 10, 64))
		if err441 != nil {
			Usage()
			return
		}
		value8 := argvalue8
		arg442 := flag.Arg(10)
		mbTrans443 := thrift.NewTMemoryBufferLen(len(arg442))
		defer mbTrans443.Close()
		_, err444 := mbTrans443.WriteString(arg442)
		if err444 != nil {
			Usage()
			return
		}
		factory445 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt446 := factory445.GetProtocol(mbTrans443)
		containerStruct9 := zeus_stats.NewGetImageIdMappingAdDataByUserInfoArgs()
		err447 := containerStruct9.ReadField10(jsProt446)
		if err447 != nil {
			Usage()
			return
		}
		argvalue9 := containerStruct9.OrderColumns
		value9 := argvalue9
		fmt.Print(client.GetImageIdMappingAdDataByUserInfo(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9))
		fmt.Print("\n")
		break
	case "getAdStatsDataListByUserInfoImageId":
		if flag.NArg()-1 != 10 {
			fmt.Fprintln(os.Stderr, "GetAdStatsDataListByUserInfoImageId requires 10 args")
			flag.Usage()
		}
		arg448 := flag.Arg(1)
		mbTrans449 := thrift.NewTMemoryBufferLen(len(arg448))
		defer mbTrans449.Close()
		_, err450 := mbTrans449.WriteString(arg448)
		if err450 != nil {
			Usage()
			return
		}
		factory451 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt452 := factory451.GetProtocol(mbTrans449)
		argvalue0 := zeus_stats.NewRequestHeader()
		err453 := argvalue0.Read(jsProt452)
		if err453 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err454 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err454 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err455 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err455 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err456 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err456 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err457 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err457 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		tmp5, err458 := (strconv.Atoi(flag.Arg(6)))
		if err458 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err459 := (strconv.Atoi(flag.Arg(7)))
		if err459 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		argvalue7 := flag.Arg(8)
		value7 := argvalue7
		argvalue8 := flag.Arg(9)
		value8 := argvalue8
		argvalue9, err462 := (strconv.ParseInt(flag.Arg(10), 10, 64))
		if err462 != nil {
			Usage()
			return
		}
		value9 := argvalue9
		fmt.Print(client.GetAdStatsDataListByUserInfoImageId(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9))
		fmt.Print("\n")
		break
	case "getAdStatsDataListByUserInfoImageIds":
		if flag.NArg()-1 != 10 {
			fmt.Fprintln(os.Stderr, "GetAdStatsDataListByUserInfoImageIds requires 10 args")
			flag.Usage()
		}
		arg463 := flag.Arg(1)
		mbTrans464 := thrift.NewTMemoryBufferLen(len(arg463))
		defer mbTrans464.Close()
		_, err465 := mbTrans464.WriteString(arg463)
		if err465 != nil {
			Usage()
			return
		}
		factory466 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt467 := factory466.GetProtocol(mbTrans464)
		argvalue0 := zeus_stats.NewRequestHeader()
		err468 := argvalue0.Read(jsProt467)
		if err468 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err469 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err469 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err470 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err470 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err471 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err471 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg472 := flag.Arg(5)
		mbTrans473 := thrift.NewTMemoryBufferLen(len(arg472))
		defer mbTrans473.Close()
		_, err474 := mbTrans473.WriteString(arg472)
		if err474 != nil {
			Usage()
			return
		}
		factory475 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt476 := factory475.GetProtocol(mbTrans473)
		containerStruct4 := zeus_stats.NewGetAdStatsDataListByUserInfoImageIdsArgs()
		err477 := containerStruct4.ReadField5(jsProt476)
		if err477 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.ImageIds
		value4 := argvalue4
		tmp5, err478 := (strconv.Atoi(flag.Arg(6)))
		if err478 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err479 := (strconv.Atoi(flag.Arg(7)))
		if err479 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		argvalue7 := flag.Arg(8)
		value7 := argvalue7
		argvalue8 := flag.Arg(9)
		value8 := argvalue8
		argvalue9, err482 := (strconv.ParseInt(flag.Arg(10), 10, 64))
		if err482 != nil {
			Usage()
			return
		}
		value9 := argvalue9
		fmt.Print(client.GetAdStatsDataListByUserInfoImageIds(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9))
		fmt.Print("\n")
		break
	case "getImageIdMappingAdDataByCampaignId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetImageIdMappingAdDataByCampaignId requires 7 args")
			flag.Usage()
		}
		arg483 := flag.Arg(1)
		mbTrans484 := thrift.NewTMemoryBufferLen(len(arg483))
		defer mbTrans484.Close()
		_, err485 := mbTrans484.WriteString(arg483)
		if err485 != nil {
			Usage()
			return
		}
		factory486 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt487 := factory486.GetProtocol(mbTrans484)
		argvalue0 := zeus_stats.NewRequestHeader()
		err488 := argvalue0.Read(jsProt487)
		if err488 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err489 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err489 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err490 := (strconv.Atoi(flag.Arg(3)))
		if err490 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err491 := (strconv.Atoi(flag.Arg(4)))
		if err491 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4, err492 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err492 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err493 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err493 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		arg494 := flag.Arg(7)
		mbTrans495 := thrift.NewTMemoryBufferLen(len(arg494))
		defer mbTrans495.Close()
		_, err496 := mbTrans495.WriteString(arg494)
		if err496 != nil {
			Usage()
			return
		}
		factory497 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt498 := factory497.GetProtocol(mbTrans495)
		containerStruct6 := zeus_stats.NewGetImageIdMappingAdDataByCampaignIdArgs()
		err499 := containerStruct6.ReadField7(jsProt498)
		if err499 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.OrderColumns
		value6 := argvalue6
		fmt.Print(client.GetImageIdMappingAdDataByCampaignId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getAdStatsDataListByCampaignImageId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetAdStatsDataListByCampaignImageId requires 7 args")
			flag.Usage()
		}
		arg500 := flag.Arg(1)
		mbTrans501 := thrift.NewTMemoryBufferLen(len(arg500))
		defer mbTrans501.Close()
		_, err502 := mbTrans501.WriteString(arg500)
		if err502 != nil {
			Usage()
			return
		}
		factory503 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt504 := factory503.GetProtocol(mbTrans501)
		argvalue0 := zeus_stats.NewRequestHeader()
		err505 := argvalue0.Read(jsProt504)
		if err505 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err506 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err506 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err507 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err507 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err508 := (strconv.Atoi(flag.Arg(4)))
		if err508 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err509 := (strconv.Atoi(flag.Arg(5)))
		if err509 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		fmt.Print(client.GetAdStatsDataListByCampaignImageId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getVideoIdMappingAdDataByUserInfo":
		if flag.NArg()-1 != 10 {
			fmt.Fprintln(os.Stderr, "GetVideoIdMappingAdDataByUserInfo requires 10 args")
			flag.Usage()
		}
		arg512 := flag.Arg(1)
		mbTrans513 := thrift.NewTMemoryBufferLen(len(arg512))
		defer mbTrans513.Close()
		_, err514 := mbTrans513.WriteString(arg512)
		if err514 != nil {
			Usage()
			return
		}
		factory515 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt516 := factory515.GetProtocol(mbTrans513)
		argvalue0 := zeus_stats.NewRequestHeader()
		err517 := argvalue0.Read(jsProt516)
		if err517 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err518 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err518 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err519 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err519 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err520 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err520 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		tmp5, err522 := (strconv.Atoi(flag.Arg(6)))
		if err522 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err523 := (strconv.Atoi(flag.Arg(7)))
		if err523 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		argvalue7, err524 := (strconv.ParseInt(flag.Arg(8), 10, 64))
		if err524 != nil {
			Usage()
			return
		}
		value7 := argvalue7
		argvalue8, err525 := (strconv.ParseInt(flag.Arg(9), 10, 64))
		if err525 != nil {
			Usage()
			return
		}
		value8 := argvalue8
		arg526 := flag.Arg(10)
		mbTrans527 := thrift.NewTMemoryBufferLen(len(arg526))
		defer mbTrans527.Close()
		_, err528 := mbTrans527.WriteString(arg526)
		if err528 != nil {
			Usage()
			return
		}
		factory529 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt530 := factory529.GetProtocol(mbTrans527)
		containerStruct9 := zeus_stats.NewGetVideoIdMappingAdDataByUserInfoArgs()
		err531 := containerStruct9.ReadField10(jsProt530)
		if err531 != nil {
			Usage()
			return
		}
		argvalue9 := containerStruct9.OrderColumns
		value9 := argvalue9
		fmt.Print(client.GetVideoIdMappingAdDataByUserInfo(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9))
		fmt.Print("\n")
		break
	case "getAdStatsDataListByUserInfoVideoId":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "GetAdStatsDataListByUserInfoVideoId requires 9 args")
			flag.Usage()
		}
		arg532 := flag.Arg(1)
		mbTrans533 := thrift.NewTMemoryBufferLen(len(arg532))
		defer mbTrans533.Close()
		_, err534 := mbTrans533.WriteString(arg532)
		if err534 != nil {
			Usage()
			return
		}
		factory535 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt536 := factory535.GetProtocol(mbTrans533)
		argvalue0 := zeus_stats.NewRequestHeader()
		err537 := argvalue0.Read(jsProt536)
		if err537 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err538 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err538 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err539 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err539 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err540 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err540 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err541 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err541 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		tmp5, err542 := (strconv.Atoi(flag.Arg(6)))
		if err542 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err543 := (strconv.Atoi(flag.Arg(7)))
		if err543 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		argvalue7 := flag.Arg(8)
		value7 := argvalue7
		argvalue8 := flag.Arg(9)
		value8 := argvalue8
		fmt.Print(client.GetAdStatsDataListByUserInfoVideoId(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "getVideoIdMappingAdDataByCampaignId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetVideoIdMappingAdDataByCampaignId requires 7 args")
			flag.Usage()
		}
		arg546 := flag.Arg(1)
		mbTrans547 := thrift.NewTMemoryBufferLen(len(arg546))
		defer mbTrans547.Close()
		_, err548 := mbTrans547.WriteString(arg546)
		if err548 != nil {
			Usage()
			return
		}
		factory549 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt550 := factory549.GetProtocol(mbTrans547)
		argvalue0 := zeus_stats.NewRequestHeader()
		err551 := argvalue0.Read(jsProt550)
		if err551 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err552 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err552 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err553 := (strconv.Atoi(flag.Arg(3)))
		if err553 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err554 := (strconv.Atoi(flag.Arg(4)))
		if err554 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4, err555 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err555 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err556 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err556 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		arg557 := flag.Arg(7)
		mbTrans558 := thrift.NewTMemoryBufferLen(len(arg557))
		defer mbTrans558.Close()
		_, err559 := mbTrans558.WriteString(arg557)
		if err559 != nil {
			Usage()
			return
		}
		factory560 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt561 := factory560.GetProtocol(mbTrans558)
		containerStruct6 := zeus_stats.NewGetVideoIdMappingAdDataByCampaignIdArgs()
		err562 := containerStruct6.ReadField7(jsProt561)
		if err562 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.OrderColumns
		value6 := argvalue6
		fmt.Print(client.GetVideoIdMappingAdDataByCampaignId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getAdStatsDataListByCampaignVideoId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetAdStatsDataListByCampaignVideoId requires 7 args")
			flag.Usage()
		}
		arg563 := flag.Arg(1)
		mbTrans564 := thrift.NewTMemoryBufferLen(len(arg563))
		defer mbTrans564.Close()
		_, err565 := mbTrans564.WriteString(arg563)
		if err565 != nil {
			Usage()
			return
		}
		factory566 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt567 := factory566.GetProtocol(mbTrans564)
		argvalue0 := zeus_stats.NewRequestHeader()
		err568 := argvalue0.Read(jsProt567)
		if err568 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err569 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err569 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err570 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err570 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err571 := (strconv.Atoi(flag.Arg(4)))
		if err571 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err572 := (strconv.Atoi(flag.Arg(5)))
		if err572 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		fmt.Print(client.GetAdStatsDataListByCampaignVideoId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getCreativeIdMappingAdDataByCampaignId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetCreativeIdMappingAdDataByCampaignId requires 7 args")
			flag.Usage()
		}
		arg575 := flag.Arg(1)
		mbTrans576 := thrift.NewTMemoryBufferLen(len(arg575))
		defer mbTrans576.Close()
		_, err577 := mbTrans576.WriteString(arg575)
		if err577 != nil {
			Usage()
			return
		}
		factory578 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt579 := factory578.GetProtocol(mbTrans576)
		argvalue0 := zeus_stats.NewRequestHeader()
		err580 := argvalue0.Read(jsProt579)
		if err580 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err581 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err581 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err582 := (strconv.Atoi(flag.Arg(3)))
		if err582 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err583 := (strconv.Atoi(flag.Arg(4)))
		if err583 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4, err584 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err584 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err585 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err585 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		arg586 := flag.Arg(7)
		mbTrans587 := thrift.NewTMemoryBufferLen(len(arg586))
		defer mbTrans587.Close()
		_, err588 := mbTrans587.WriteString(arg586)
		if err588 != nil {
			Usage()
			return
		}
		factory589 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt590 := factory589.GetProtocol(mbTrans587)
		containerStruct6 := zeus_stats.NewGetCreativeIdMappingAdDataByCampaignIdArgs()
		err591 := containerStruct6.ReadField7(jsProt590)
		if err591 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.OrderColumns
		value6 := argvalue6
		fmt.Print(client.GetCreativeIdMappingAdDataByCampaignId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getAdStatsDataListByCampaignCreativeId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetAdStatsDataListByCampaignCreativeId requires 7 args")
			flag.Usage()
		}
		arg592 := flag.Arg(1)
		mbTrans593 := thrift.NewTMemoryBufferLen(len(arg592))
		defer mbTrans593.Close()
		_, err594 := mbTrans593.WriteString(arg592)
		if err594 != nil {
			Usage()
			return
		}
		factory595 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt596 := factory595.GetProtocol(mbTrans593)
		argvalue0 := zeus_stats.NewRequestHeader()
		err597 := argvalue0.Read(jsProt596)
		if err597 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err598 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err598 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err599 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err599 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err600 := (strconv.Atoi(flag.Arg(4)))
		if err600 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err601 := (strconv.Atoi(flag.Arg(5)))
		if err601 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		fmt.Print(client.GetAdStatsDataListByCampaignCreativeId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getCampaignStatsDataListByUserInfo":
		if flag.NArg()-1 != 9 {
			fmt.Fprintln(os.Stderr, "GetCampaignStatsDataListByUserInfo requires 9 args")
			flag.Usage()
		}
		arg604 := flag.Arg(1)
		mbTrans605 := thrift.NewTMemoryBufferLen(len(arg604))
		defer mbTrans605.Close()
		_, err606 := mbTrans605.WriteString(arg604)
		if err606 != nil {
			Usage()
			return
		}
		factory607 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt608 := factory607.GetProtocol(mbTrans605)
		argvalue0 := zeus_stats.NewRequestHeader()
		err609 := argvalue0.Read(jsProt608)
		if err609 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err610 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err610 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err611 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err611 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err612 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err612 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		tmp4, err613 := (strconv.Atoi(flag.Arg(5)))
		if err613 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err614 := (strconv.Atoi(flag.Arg(6)))
		if err614 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		argvalue7 := flag.Arg(8)
		value7 := argvalue7
		argvalue8, err617 := (strconv.ParseInt(flag.Arg(9), 10, 64))
		if err617 != nil {
			Usage()
			return
		}
		value8 := argvalue8
		fmt.Print(client.GetCampaignStatsDataListByUserInfo(value0, value1, value2, value3, value4, value5, value6, value7, value8))
		fmt.Print("\n")
		break
	case "getCampaignStatsDataListByCampaignId":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetCampaignStatsDataListByCampaignId requires 6 args")
			flag.Usage()
		}
		arg618 := flag.Arg(1)
		mbTrans619 := thrift.NewTMemoryBufferLen(len(arg618))
		defer mbTrans619.Close()
		_, err620 := mbTrans619.WriteString(arg618)
		if err620 != nil {
			Usage()
			return
		}
		factory621 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt622 := factory621.GetProtocol(mbTrans619)
		argvalue0 := zeus_stats.NewRequestHeader()
		err623 := argvalue0.Read(jsProt622)
		if err623 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err624 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err624 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err625 := (strconv.Atoi(flag.Arg(3)))
		if err625 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err626 := (strconv.Atoi(flag.Arg(4)))
		if err626 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.GetCampaignStatsDataListByCampaignId(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getAccountCampaignStatsDataListByFbPromotionId":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetAccountCampaignStatsDataListByFbPromotionId requires 6 args")
			flag.Usage()
		}
		arg629 := flag.Arg(1)
		mbTrans630 := thrift.NewTMemoryBufferLen(len(arg629))
		defer mbTrans630.Close()
		_, err631 := mbTrans630.WriteString(arg629)
		if err631 != nil {
			Usage()
			return
		}
		factory632 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt633 := factory632.GetProtocol(mbTrans630)
		argvalue0 := zeus_stats.NewRequestHeader()
		err634 := argvalue0.Read(jsProt633)
		if err634 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err635 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err635 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err636 := (strconv.Atoi(flag.Arg(3)))
		if err636 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err637 := (strconv.Atoi(flag.Arg(4)))
		if err637 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4, err638 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err638 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.GetAccountCampaignStatsDataListByFbPromotionId(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getDtMappingAdDataByImageIds":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "GetDtMappingAdDataByImageIds requires 8 args")
			flag.Usage()
		}
		arg640 := flag.Arg(1)
		mbTrans641 := thrift.NewTMemoryBufferLen(len(arg640))
		defer mbTrans641.Close()
		_, err642 := mbTrans641.WriteString(arg640)
		if err642 != nil {
			Usage()
			return
		}
		factory643 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt644 := factory643.GetProtocol(mbTrans641)
		argvalue0 := zeus_stats.NewRequestHeader()
		err645 := argvalue0.Read(jsProt644)
		if err645 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err646 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err646 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err647 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err647 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err648 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err648 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg649 := flag.Arg(5)
		mbTrans650 := thrift.NewTMemoryBufferLen(len(arg649))
		defer mbTrans650.Close()
		_, err651 := mbTrans650.WriteString(arg649)
		if err651 != nil {
			Usage()
			return
		}
		factory652 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt653 := factory652.GetProtocol(mbTrans650)
		containerStruct4 := zeus_stats.NewGetDtMappingAdDataByImageIdsArgs()
		err654 := containerStruct4.ReadField5(jsProt653)
		if err654 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.ImageIds
		value4 := argvalue4
		tmp5, err655 := (strconv.Atoi(flag.Arg(6)))
		if err655 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err656 := (strconv.Atoi(flag.Arg(7)))
		if err656 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		tmp7, err := (strconv.Atoi(flag.Arg(8)))
		if err != nil {
			Usage()
			return
		}
		argvalue7 := zeus_stats.ZeusDateType(tmp7)
		value7 := argvalue7
		fmt.Print(client.GetDtMappingAdDataByImageIds(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getDtMappingAdDataByVideoIds":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "GetDtMappingAdDataByVideoIds requires 8 args")
			flag.Usage()
		}
		arg657 := flag.Arg(1)
		mbTrans658 := thrift.NewTMemoryBufferLen(len(arg657))
		defer mbTrans658.Close()
		_, err659 := mbTrans658.WriteString(arg657)
		if err659 != nil {
			Usage()
			return
		}
		factory660 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt661 := factory660.GetProtocol(mbTrans658)
		argvalue0 := zeus_stats.NewRequestHeader()
		err662 := argvalue0.Read(jsProt661)
		if err662 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err663 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err663 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err664 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err664 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err665 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err665 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		arg666 := flag.Arg(5)
		mbTrans667 := thrift.NewTMemoryBufferLen(len(arg666))
		defer mbTrans667.Close()
		_, err668 := mbTrans667.WriteString(arg666)
		if err668 != nil {
			Usage()
			return
		}
		factory669 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt670 := factory669.GetProtocol(mbTrans667)
		containerStruct4 := zeus_stats.NewGetDtMappingAdDataByVideoIdsArgs()
		err671 := containerStruct4.ReadField5(jsProt670)
		if err671 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.VideoIds
		value4 := argvalue4
		tmp5, err672 := (strconv.Atoi(flag.Arg(6)))
		if err672 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		tmp6, err673 := (strconv.Atoi(flag.Arg(7)))
		if err673 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		tmp7, err := (strconv.Atoi(flag.Arg(8)))
		if err != nil {
			Usage()
			return
		}
		argvalue7 := zeus_stats.ZeusDateType(tmp7)
		value7 := argvalue7
		fmt.Print(client.GetDtMappingAdDataByVideoIds(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getAdStatsDataListByCampaignId":
		if flag.NArg()-1 != 10 {
			fmt.Fprintln(os.Stderr, "GetAdStatsDataListByCampaignId requires 10 args")
			flag.Usage()
		}
		arg674 := flag.Arg(1)
		mbTrans675 := thrift.NewTMemoryBufferLen(len(arg674))
		defer mbTrans675.Close()
		_, err676 := mbTrans675.WriteString(arg674)
		if err676 != nil {
			Usage()
			return
		}
		factory677 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt678 := factory677.GetProtocol(mbTrans675)
		argvalue0 := zeus_stats.NewRequestHeader()
		err679 := argvalue0.Read(jsProt678)
		if err679 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err680 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err680 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err681 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err681 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err682 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err682 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err683 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err683 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err684 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err684 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		tmp6, err685 := (strconv.Atoi(flag.Arg(7)))
		if err685 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		tmp7, err686 := (strconv.Atoi(flag.Arg(8)))
		if err686 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := argvalue7
		tmp8, err := (strconv.Atoi(flag.Arg(9)))
		if err != nil {
			Usage()
			return
		}
		argvalue8 := zeus_stats.ZeusDateType(tmp8)
		value8 := argvalue8
		argvalue9 := flag.Arg(10)
		value9 := argvalue9
		fmt.Print(client.GetAdStatsDataListByCampaignId(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9))
		fmt.Print("\n")
		break
	case "getDtMappingCampaignDataByFbPromotionId":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetDtMappingCampaignDataByFbPromotionId requires 6 args")
			flag.Usage()
		}
		arg688 := flag.Arg(1)
		mbTrans689 := thrift.NewTMemoryBufferLen(len(arg688))
		defer mbTrans689.Close()
		_, err690 := mbTrans689.WriteString(arg688)
		if err690 != nil {
			Usage()
			return
		}
		factory691 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt692 := factory691.GetProtocol(mbTrans689)
		argvalue0 := zeus_stats.NewRequestHeader()
		err693 := argvalue0.Read(jsProt692)
		if err693 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err694 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err694 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err695 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err695 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err696 := (strconv.Atoi(flag.Arg(4)))
		if err696 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err697 := (strconv.Atoi(flag.Arg(5)))
		if err697 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := zeus_stats.ZeusDateType(tmp5)
		value5 := argvalue5
		fmt.Print(client.GetDtMappingCampaignDataByFbPromotionId(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getCampaignStatsDataListByFbPromotionId":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetCampaignStatsDataListByFbPromotionId requires 7 args")
			flag.Usage()
		}
		arg698 := flag.Arg(1)
		mbTrans699 := thrift.NewTMemoryBufferLen(len(arg698))
		defer mbTrans699.Close()
		_, err700 := mbTrans699.WriteString(arg698)
		if err700 != nil {
			Usage()
			return
		}
		factory701 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt702 := factory701.GetProtocol(mbTrans699)
		argvalue0 := zeus_stats.NewRequestHeader()
		err703 := argvalue0.Read(jsProt702)
		if err703 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err704 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err704 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err705 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err705 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err706 := (strconv.Atoi(flag.Arg(4)))
		if err706 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err707 := (strconv.Atoi(flag.Arg(5)))
		if err707 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		fmt.Print(client.GetCampaignStatsDataListByFbPromotionId(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getLibMappingAdDataByFbPromotionId":
		if flag.NArg()-1 != 10 {
			fmt.Fprintln(os.Stderr, "GetLibMappingAdDataByFbPromotionId requires 10 args")
			flag.Usage()
		}
		arg710 := flag.Arg(1)
		mbTrans711 := thrift.NewTMemoryBufferLen(len(arg710))
		defer mbTrans711.Close()
		_, err712 := mbTrans711.WriteString(arg710)
		if err712 != nil {
			Usage()
			return
		}
		factory713 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt714 := factory713.GetProtocol(mbTrans711)
		argvalue0 := zeus_stats.NewRequestHeader()
		err715 := argvalue0.Read(jsProt714)
		if err715 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err716 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err716 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err717 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err717 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err718 := (strconv.Atoi(flag.Arg(4)))
		if err718 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err719 := (strconv.Atoi(flag.Arg(5)))
		if err719 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err720 := (strconv.Atoi(flag.Arg(6)))
		if err720 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		argvalue7, err722 := (strconv.ParseInt(flag.Arg(8), 10, 64))
		if err722 != nil {
			Usage()
			return
		}
		value7 := argvalue7
		argvalue8, err723 := (strconv.ParseInt(flag.Arg(9), 10, 64))
		if err723 != nil {
			Usage()
			return
		}
		value8 := argvalue8
		arg724 := flag.Arg(10)
		mbTrans725 := thrift.NewTMemoryBufferLen(len(arg724))
		defer mbTrans725.Close()
		_, err726 := mbTrans725.WriteString(arg724)
		if err726 != nil {
			Usage()
			return
		}
		factory727 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt728 := factory727.GetProtocol(mbTrans725)
		containerStruct9 := zeus_stats.NewGetLibMappingAdDataByFbPromotionIdArgs()
		err729 := containerStruct9.ReadField10(jsProt728)
		if err729 != nil {
			Usage()
			return
		}
		argvalue9 := containerStruct9.OrderColumns
		value9 := argvalue9
		fmt.Print(client.GetLibMappingAdDataByFbPromotionId(value0, value1, value2, value3, value4, value5, value6, value7, value8, value9))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err734 := (strconv.Atoi(flag.Arg(1)))
		if err734 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
