// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package udp_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//媒体平台
//如腾讯、今日头条、快手等
type UDPMediaPlatform int64

const (
	UDPMediaPlatform_UDP_MP_TENCENT       UDPMediaPlatform = 1
	UDPMediaPlatform_UDP_MP_TOUTIAO       UDPMediaPlatform = 2
	UDPMediaPlatform_UDP_MP_KUAISHOU      UDPMediaPlatform = 3
	UDPMediaPlatform_UDP_MP_WEIBO         UDPMediaPlatform = 4
	UDPMediaPlatform_UDP_MP_WEIXIN        UDPMediaPlatform = 5
	UDPMediaPlatform_UDP_MP_UC            UDPMediaPlatform = 6
	UDPMediaPlatform_UDP_MP_BILIBILI      UDPMediaPlatform = 7
	UDPMediaPlatform_UDP_MP_BAIDU         UDPMediaPlatform = 8
	UDPMediaPlatform_UDP_MP_QVTOUTIAO     UDPMediaPlatform = 9
	UDPMediaPlatform_UDP_MP_QIANCHUAN     UDPMediaPlatform = 10
	UDPMediaPlatform_UDP_MP_SUPERHUICHUAN UDPMediaPlatform = 11
)

func (p UDPMediaPlatform) String() string {
	switch p {
	case UDPMediaPlatform_UDP_MP_TENCENT:
		return "UDPMediaPlatform_UDP_MP_TENCENT"
	case UDPMediaPlatform_UDP_MP_TOUTIAO:
		return "UDPMediaPlatform_UDP_MP_TOUTIAO"
	case UDPMediaPlatform_UDP_MP_KUAISHOU:
		return "UDPMediaPlatform_UDP_MP_KUAISHOU"
	case UDPMediaPlatform_UDP_MP_WEIBO:
		return "UDPMediaPlatform_UDP_MP_WEIBO"
	case UDPMediaPlatform_UDP_MP_WEIXIN:
		return "UDPMediaPlatform_UDP_MP_WEIXIN"
	case UDPMediaPlatform_UDP_MP_UC:
		return "UDPMediaPlatform_UDP_MP_UC"
	case UDPMediaPlatform_UDP_MP_BILIBILI:
		return "UDPMediaPlatform_UDP_MP_BILIBILI"
	case UDPMediaPlatform_UDP_MP_BAIDU:
		return "UDPMediaPlatform_UDP_MP_BAIDU"
	case UDPMediaPlatform_UDP_MP_QVTOUTIAO:
		return "UDPMediaPlatform_UDP_MP_QVTOUTIAO"
	case UDPMediaPlatform_UDP_MP_QIANCHUAN:
		return "UDPMediaPlatform_UDP_MP_QIANCHUAN"
	case UDPMediaPlatform_UDP_MP_SUPERHUICHUAN:
		return "UDPMediaPlatform_UDP_MP_SUPERHUICHUAN"
	}
	return "<UNSET>"
}

func UDPMediaPlatformFromString(s string) (UDPMediaPlatform, error) {
	switch s {
	case "UDPMediaPlatform_UDP_MP_TENCENT":
		return UDPMediaPlatform_UDP_MP_TENCENT, nil
	case "UDPMediaPlatform_UDP_MP_TOUTIAO":
		return UDPMediaPlatform_UDP_MP_TOUTIAO, nil
	case "UDPMediaPlatform_UDP_MP_KUAISHOU":
		return UDPMediaPlatform_UDP_MP_KUAISHOU, nil
	case "UDPMediaPlatform_UDP_MP_WEIBO":
		return UDPMediaPlatform_UDP_MP_WEIBO, nil
	case "UDPMediaPlatform_UDP_MP_WEIXIN":
		return UDPMediaPlatform_UDP_MP_WEIXIN, nil
	case "UDPMediaPlatform_UDP_MP_UC":
		return UDPMediaPlatform_UDP_MP_UC, nil
	case "UDPMediaPlatform_UDP_MP_BILIBILI":
		return UDPMediaPlatform_UDP_MP_BILIBILI, nil
	case "UDPMediaPlatform_UDP_MP_BAIDU":
		return UDPMediaPlatform_UDP_MP_BAIDU, nil
	case "UDPMediaPlatform_UDP_MP_QVTOUTIAO":
		return UDPMediaPlatform_UDP_MP_QVTOUTIAO, nil
	case "UDPMediaPlatform_UDP_MP_QIANCHUAN":
		return UDPMediaPlatform_UDP_MP_QIANCHUAN, nil
	case "UDPMediaPlatform_UDP_MP_SUPERHUICHUAN":
		return UDPMediaPlatform_UDP_MP_SUPERHUICHUAN, nil
	}
	return UDPMediaPlatform(math.MinInt32 - 1), fmt.Errorf("not a valid UDPMediaPlatform string")
}

type SyncTaskType int64

const (
	SyncTaskType_STT_META_X SyncTaskType = 1
	SyncTaskType_STT_STATS  SyncTaskType = 2
	SyncTaskType_STT_FUNDS  SyncTaskType = 3
)

func (p SyncTaskType) String() string {
	switch p {
	case SyncTaskType_STT_META_X:
		return "SyncTaskType_STT_META_X"
	case SyncTaskType_STT_STATS:
		return "SyncTaskType_STT_STATS"
	case SyncTaskType_STT_FUNDS:
		return "SyncTaskType_STT_FUNDS"
	}
	return "<UNSET>"
}

func SyncTaskTypeFromString(s string) (SyncTaskType, error) {
	switch s {
	case "SyncTaskType_STT_META_X":
		return SyncTaskType_STT_META_X, nil
	case "SyncTaskType_STT_STATS":
		return SyncTaskType_STT_STATS, nil
	case "SyncTaskType_STT_FUNDS":
		return SyncTaskType_STT_FUNDS, nil
	}
	return SyncTaskType(math.MinInt32 - 1), fmt.Errorf("not a valid SyncTaskType string")
}

type AuthInfo struct {
	Id                   int32            `thrift:"id,1" json:"id"`
	Platform             UDPMediaPlatform `thrift:"platform,2" json:"platform"`
	AppId                string           `thrift:"app_id,3" json:"app_id"`
	AuthCode             string           `thrift:"auth_code,4" json:"auth_code"`
	AuthorAccount        string           `thrift:"author_account,5" json:"author_account"`
	AuthorRole           string           `thrift:"author_role,6" json:"author_role"`
	AuthorName           string           `thrift:"author_name,7" json:"author_name"`
	AccessToken          string           `thrift:"access_token,8" json:"access_token"`
	AccessTokenExpireAt  int64            `thrift:"access_token_expire_at,9" json:"access_token_expire_at"`
	RefreshToken         string           `thrift:"refresh_token,10" json:"refresh_token"`
	RefreshTokenExpireAt int64            `thrift:"refresh_token_expire_at,11" json:"refresh_token_expire_at"`
	Username             string           `thrift:"username,12" json:"username"`
	Password             string           `thrift:"password,13" json:"password"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	CreateTime int64 `thrift:"create_time,21" json:"create_time"`
	LastUpdate int64 `thrift:"last_update,22" json:"last_update"`
	Status     int32 `thrift:"status,23" json:"status"`
}

func NewAuthInfo() *AuthInfo {
	return &AuthInfo{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AuthInfo) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *AuthInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AuthInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AuthInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = UDPMediaPlatform(v)
	}
	return nil
}

func (p *AuthInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AuthInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AuthCode = v
	}
	return nil
}

func (p *AuthInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AuthorAccount = v
	}
	return nil
}

func (p *AuthInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AuthorRole = v
	}
	return nil
}

func (p *AuthInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AuthorName = v
	}
	return nil
}

func (p *AuthInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AccessToken = v
	}
	return nil
}

func (p *AuthInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AccessTokenExpireAt = v
	}
	return nil
}

func (p *AuthInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.RefreshToken = v
	}
	return nil
}

func (p *AuthInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.RefreshTokenExpireAt = v
	}
	return nil
}

func (p *AuthInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *AuthInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *AuthInfo) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AuthInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *AuthInfo) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AuthInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AuthInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AuthInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *AuthInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:app_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:app_id: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auth_code", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:auth_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AuthCode)); err != nil {
		return fmt.Errorf("%T.auth_code (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:auth_code: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author_account", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:author_account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AuthorAccount)); err != nil {
		return fmt.Errorf("%T.author_account (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:author_account: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author_role", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:author_role: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AuthorRole)); err != nil {
		return fmt.Errorf("%T.author_role (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:author_role: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author_name", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:author_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AuthorName)); err != nil {
		return fmt.Errorf("%T.author_name (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:author_name: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:access_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessToken)); err != nil {
		return fmt.Errorf("%T.access_token (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:access_token: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token_expire_at", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:access_token_expire_at: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccessTokenExpireAt)); err != nil {
		return fmt.Errorf("%T.access_token_expire_at (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:access_token_expire_at: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refresh_token", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:refresh_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RefreshToken)); err != nil {
		return fmt.Errorf("%T.refresh_token (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:refresh_token: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refresh_token_expire_at", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:refresh_token_expire_at: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RefreshTokenExpireAt)); err != nil {
		return fmt.Errorf("%T.refresh_token_expire_at (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:refresh_token_expire_at: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:username: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:password: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:create_time: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_update", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:last_update: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.last_update (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:last_update: %s", p, err)
	}
	return err
}

func (p *AuthInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:status: %s", p, err)
	}
	return err
}

func (p *AuthInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AuthInfo(%+v)", *p)
}

type TokenInfo struct {
	Platform        UDPMediaPlatform `thrift:"platform,1" json:"platform"`
	AppId           string           `thrift:"app_id,2" json:"app_id"`
	AccountId       int64            `thrift:"account_id,3" json:"account_id"`
	AccountIdString string           `thrift:"account_id_string,4" json:"account_id_string"`
	AccessToken     string           `thrift:"access_token,5" json:"access_token"`
	ExpireAt        int64            `thrift:"expire_at,6" json:"expire_at"`
	Username        string           `thrift:"username,7" json:"username"`
	Password        string           `thrift:"password,8" json:"password"`
	// unused field # 9
	// unused field # 10
	LastUpdate int64 `thrift:"last_update,11" json:"last_update"`
}

func NewTokenInfo() *TokenInfo {
	return &TokenInfo{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TokenInfo) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *TokenInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TokenInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Platform = UDPMediaPlatform(v)
	}
	return nil
}

func (p *TokenInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *TokenInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *TokenInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AccountIdString = v
	}
	return nil
}

func (p *TokenInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AccessToken = v
	}
	return nil
}

func (p *TokenInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ExpireAt = v
	}
	return nil
}

func (p *TokenInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *TokenInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *TokenInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *TokenInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TokenInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TokenInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:platform: %s", p, err)
		}
	}
	return err
}

func (p *TokenInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:app_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:app_id: %s", p, err)
	}
	return err
}

func (p *TokenInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.account_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account_id: %s", p, err)
	}
	return err
}

func (p *TokenInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id_string", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:account_id_string: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountIdString)); err != nil {
		return fmt.Errorf("%T.account_id_string (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:account_id_string: %s", p, err)
	}
	return err
}

func (p *TokenInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:access_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessToken)); err != nil {
		return fmt.Errorf("%T.access_token (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:access_token: %s", p, err)
	}
	return err
}

func (p *TokenInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expire_at", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:expire_at: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExpireAt)); err != nil {
		return fmt.Errorf("%T.expire_at (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:expire_at: %s", p, err)
	}
	return err
}

func (p *TokenInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:username: %s", p, err)
	}
	return err
}

func (p *TokenInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:password: %s", p, err)
	}
	return err
}

func (p *TokenInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_update", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:last_update: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.last_update (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:last_update: %s", p, err)
	}
	return err
}

func (p *TokenInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TokenInfo(%+v)", *p)
}

type ResourceAccount struct {
	AuthId          int32            `thrift:"auth_id,1" json:"auth_id"`
	Platform        UDPMediaPlatform `thrift:"platform,2" json:"platform"`
	AccountId       int64            `thrift:"account_id,3" json:"account_id"`
	AccountIdString string           `thrift:"account_id_string,4" json:"account_id_string"`
	AccountName     string           `thrift:"account_name,5" json:"account_name"`
	AccountRole     string           `thrift:"account_role,6" json:"account_role"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	CreateTime int64 `thrift:"create_time,11" json:"create_time"`
	LastUpdate int64 `thrift:"last_update,12" json:"last_update"`
	Status     int32 `thrift:"status,13" json:"status"`
}

func NewResourceAccount() *ResourceAccount {
	return &ResourceAccount{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ResourceAccount) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *ResourceAccount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResourceAccount) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AuthId = v
	}
	return nil
}

func (p *ResourceAccount) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = UDPMediaPlatform(v)
	}
	return nil
}

func (p *ResourceAccount) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *ResourceAccount) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AccountIdString = v
	}
	return nil
}

func (p *ResourceAccount) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AccountName = v
	}
	return nil
}

func (p *ResourceAccount) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AccountRole = v
	}
	return nil
}

func (p *ResourceAccount) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *ResourceAccount) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *ResourceAccount) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *ResourceAccount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResourceAccount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResourceAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auth_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:auth_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AuthId)); err != nil {
		return fmt.Errorf("%T.auth_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:auth_id: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *ResourceAccount) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.account_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account_id: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_id_string", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:account_id_string: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountIdString)); err != nil {
		return fmt.Errorf("%T.account_id_string (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:account_id_string: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:account_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountName)); err != nil {
		return fmt.Errorf("%T.account_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:account_name: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account_role", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:account_role: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccountRole)); err != nil {
		return fmt.Errorf("%T.account_role (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:account_role: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:create_time: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_update", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:last_update: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.last_update (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:last_update: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:status: %s", p, err)
	}
	return err
}

func (p *ResourceAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResourceAccount(%+v)", *p)
}

type StatsSyncParam struct {
	DtStart int32    `thrift:"dt_start,1" json:"dt_start"`
	DtEnd   int32    `thrift:"dt_end,2" json:"dt_end"`
	Levels  []string `thrift:"levels,3" json:"levels"`
}

func NewStatsSyncParam() *StatsSyncParam {
	return &StatsSyncParam{}
}

func (p *StatsSyncParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StatsSyncParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DtStart = v
	}
	return nil
}

func (p *StatsSyncParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DtEnd = v
	}
	return nil
}

func (p *StatsSyncParam) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Levels = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Levels = append(p.Levels, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *StatsSyncParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StatsSyncParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StatsSyncParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt_start", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:dt_start: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DtStart)); err != nil {
		return fmt.Errorf("%T.dt_start (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:dt_start: %s", p, err)
	}
	return err
}

func (p *StatsSyncParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt_end", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dt_end: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DtEnd)); err != nil {
		return fmt.Errorf("%T.dt_end (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dt_end: %s", p, err)
	}
	return err
}

func (p *StatsSyncParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Levels != nil {
		if err := oprot.WriteFieldBegin("levels", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:levels: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Levels)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Levels {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:levels: %s", p, err)
		}
	}
	return err
}

func (p *StatsSyncParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StatsSyncParam(%+v)", *p)
}

type SyncTask struct {
	Id               int32            `thrift:"id,1" json:"id"`
	Platform         UDPMediaPlatform `thrift:"platform,2" json:"platform"`
	AccountIds       []int64          `thrift:"account_ids,3" json:"account_ids"`
	AccountIdStrings []string         `thrift:"account_id_strings,4" json:"account_id_strings"`
	Method           string           `thrift:"method,5" json:"method"`
	SyncParams       string           `thrift:"sync_params,6" json:"sync_params"`
	CreateTime       int64            `thrift:"create_time,7" json:"create_time"`
	LastUpdate       int64            `thrift:"last_update,8" json:"last_update"`
	Status           int32            `thrift:"status,9" json:"status"`
}

func NewSyncTask() *SyncTask {
	return &SyncTask{
		Platform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SyncTask) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *SyncTask) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SyncTask) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SyncTask) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = UDPMediaPlatform(v)
	}
	return nil
}

func (p *SyncTask) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccountIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.AccountIds = append(p.AccountIds, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SyncTask) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccountIdStrings = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.AccountIdStrings = append(p.AccountIdStrings, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SyncTask) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Method = v
	}
	return nil
}

func (p *SyncTask) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.SyncParams = v
	}
	return nil
}

func (p *SyncTask) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *SyncTask) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *SyncTask) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *SyncTask) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SyncTask"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SyncTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *SyncTask) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
		}
	}
	return err
}

func (p *SyncTask) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AccountIds != nil {
		if err := oprot.WriteFieldBegin("account_ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:account_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.AccountIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccountIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:account_ids: %s", p, err)
		}
	}
	return err
}

func (p *SyncTask) writeField4(oprot thrift.TProtocol) (err error) {
	if p.AccountIdStrings != nil {
		if err := oprot.WriteFieldBegin("account_id_strings", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:account_id_strings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.AccountIdStrings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccountIdStrings {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:account_id_strings: %s", p, err)
		}
	}
	return err
}

func (p *SyncTask) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("method", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:method: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Method)); err != nil {
		return fmt.Errorf("%T.method (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:method: %s", p, err)
	}
	return err
}

func (p *SyncTask) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sync_params", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:sync_params: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SyncParams)); err != nil {
		return fmt.Errorf("%T.sync_params (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:sync_params: %s", p, err)
	}
	return err
}

func (p *SyncTask) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:create_time: %s", p, err)
	}
	return err
}

func (p *SyncTask) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_update", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:last_update: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.last_update (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:last_update: %s", p, err)
	}
	return err
}

func (p *SyncTask) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:status: %s", p, err)
	}
	return err
}

func (p *SyncTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SyncTask(%+v)", *p)
}
