// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	"vico_server"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i64 addAdProductOrderInfo(RequestHeader header, AdProductOrderInfo info)")
	fmt.Fprintln(os.Stderr, "  i64 addProduct(RequestHeader header, Product product)")
	fmt.Fprintln(os.Stderr, "  void editProduct(RequestHeader header, Product product)")
	fmt.Fprintln(os.Stderr, "   getProductsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchProductsByParams(RequestHeader header, ProductParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  void addProductComments(RequestHeader header, i64 productId,  comments)")
	fmt.Fprintln(os.Stderr, "  void deleteProductComments(RequestHeader header, i64 productId,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 addProductPack(RequestHeader header, ProductPack info)")
	fmt.Fprintln(os.Stderr, "  void editProductPack(RequestHeader header, ProductPack info)")
	fmt.Fprintln(os.Stderr, "   getProductPacksByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchProductPacksByParams(RequestHeader header, ProductPackParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  void deleteProductPackByIds(RequestHeader header, i64 accountId,  ids)")
	fmt.Fprintln(os.Stderr, "   getProductPackImgUrl(RequestHeader header, i64 num)")
	fmt.Fprintln(os.Stderr, "  i64 addAdOrder(RequestHeader header, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "  void editAdOrder(RequestHeader header, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "   getAdOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdOrdersByParams(RequestHeader header, AdOrderParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdOrderByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void passAdOrderByIds(RequestHeader header,  ids, string note)")
	fmt.Fprintln(os.Stderr, "  void rejectAdOrderByIds(RequestHeader header,  ids, string note)")
	fmt.Fprintln(os.Stderr, "  void matchCampaignToAdOrder(RequestHeader header, i64 orderId,  campaigns)")
	fmt.Fprintln(os.Stderr, "  i64 addFtLanding(RequestHeader header, FtLanding info)")
	fmt.Fprintln(os.Stderr, "  void editFtLanding(RequestHeader header, FtLanding info)")
	fmt.Fprintln(os.Stderr, "  void deleteFtLandingByIds(RequestHeader header, i64 accountId,  ids)")
	fmt.Fprintln(os.Stderr, "   getFtLandingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryFtLandingByParams(RequestHeader header, FtLandingQueryParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  void updateOrderCampaignStatus(RequestHeader header, i64 orderId, RelatedAdCampaign info)")
	fmt.Fprintln(os.Stderr, "  void addFtLandingComments(RequestHeader header, i64 landingId,  comments)")
	fmt.Fprintln(os.Stderr, "  void deleteFtLandingComments(RequestHeader header, i64 landingId,  ids)")
	fmt.Fprintln(os.Stderr, "   getAllFtLandingTemplates(RequestHeader header, i64 accountId, FtLandingTemplateType templateType)")
	fmt.Fprintln(os.Stderr, "   getFtLandingTemplateByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void publishProduct(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void unPublishProduct(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void publishProductPack(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void unPublishProductPack(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void addCommentsToLibrary(RequestHeader header, i64 productId,  comments)")
	fmt.Fprintln(os.Stderr, "  void deleteProductsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i64 addSocialMediaPost(RequestHeader header, SocialMediaPost info)")
	fmt.Fprintln(os.Stderr, "  void editSocialMediaPost(RequestHeader header, SocialMediaPost info)")
	fmt.Fprintln(os.Stderr, "  void deleteSocialMediaPostByIds(RequestHeader header, i64 accountId,  ids)")
	fmt.Fprintln(os.Stderr, "   getSocialMediaPostsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult querySocialMediaPostsByParams(RequestHeader header, SocialMediaPostParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "  i64 addSocialMediaMaterialLibrary(RequestHeader header, SocialMediaMaterialLibrary info)")
	fmt.Fprintln(os.Stderr, "  void editSocialMediaMaterialLibrary(RequestHeader header, SocialMediaMaterialLibrary info)")
	fmt.Fprintln(os.Stderr, "  void deleteSocialMediaMaterialLibraryByIds(RequestHeader header, i64 accountId,  ids)")
	fmt.Fprintln(os.Stderr, "   getSocialMediaMaterialLibraryByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult querySocialMediaPostLibrariesByParams(RequestHeader header, SocialMediaPostParams params, i64 offset, i64 limit)")
	fmt.Fprintln(os.Stderr, "   getUgcByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   listRecommendUgcsByProductId(RequestHeader header, i64 productId)")
	fmt.Fprintln(os.Stderr, "   listRecommendTextsByProductId(RequestHeader header, i64 productId)")
	fmt.Fprintln(os.Stderr, "   listRecommendCommentsByProductId(RequestHeader header, i64 productId)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := vico_server.NewVicoServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addAdProductOrderInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdProductOrderInfo requires 2 args")
			flag.Usage()
		}
		arg263 := flag.Arg(1)
		mbTrans264 := thrift.NewTMemoryBufferLen(len(arg263))
		defer mbTrans264.Close()
		_, err265 := mbTrans264.WriteString(arg263)
		if err265 != nil {
			Usage()
			return
		}
		factory266 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt267 := factory266.GetProtocol(mbTrans264)
		argvalue0 := vico_server.NewRequestHeader()
		err268 := argvalue0.Read(jsProt267)
		if err268 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg269 := flag.Arg(2)
		mbTrans270 := thrift.NewTMemoryBufferLen(len(arg269))
		defer mbTrans270.Close()
		_, err271 := mbTrans270.WriteString(arg269)
		if err271 != nil {
			Usage()
			return
		}
		factory272 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt273 := factory272.GetProtocol(mbTrans270)
		argvalue1 := vico_server.NewAdProductOrderInfo()
		err274 := argvalue1.Read(jsProt273)
		if err274 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAdProductOrderInfo(value0, value1))
		fmt.Print("\n")
		break
	case "addProduct":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddProduct requires 2 args")
			flag.Usage()
		}
		arg275 := flag.Arg(1)
		mbTrans276 := thrift.NewTMemoryBufferLen(len(arg275))
		defer mbTrans276.Close()
		_, err277 := mbTrans276.WriteString(arg275)
		if err277 != nil {
			Usage()
			return
		}
		factory278 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt279 := factory278.GetProtocol(mbTrans276)
		argvalue0 := vico_server.NewRequestHeader()
		err280 := argvalue0.Read(jsProt279)
		if err280 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg281 := flag.Arg(2)
		mbTrans282 := thrift.NewTMemoryBufferLen(len(arg281))
		defer mbTrans282.Close()
		_, err283 := mbTrans282.WriteString(arg281)
		if err283 != nil {
			Usage()
			return
		}
		factory284 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt285 := factory284.GetProtocol(mbTrans282)
		argvalue1 := vico_server.NewProduct()
		err286 := argvalue1.Read(jsProt285)
		if err286 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddProduct(value0, value1))
		fmt.Print("\n")
		break
	case "editProduct":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditProduct requires 2 args")
			flag.Usage()
		}
		arg287 := flag.Arg(1)
		mbTrans288 := thrift.NewTMemoryBufferLen(len(arg287))
		defer mbTrans288.Close()
		_, err289 := mbTrans288.WriteString(arg287)
		if err289 != nil {
			Usage()
			return
		}
		factory290 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt291 := factory290.GetProtocol(mbTrans288)
		argvalue0 := vico_server.NewRequestHeader()
		err292 := argvalue0.Read(jsProt291)
		if err292 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg293 := flag.Arg(2)
		mbTrans294 := thrift.NewTMemoryBufferLen(len(arg293))
		defer mbTrans294.Close()
		_, err295 := mbTrans294.WriteString(arg293)
		if err295 != nil {
			Usage()
			return
		}
		factory296 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt297 := factory296.GetProtocol(mbTrans294)
		argvalue1 := vico_server.NewProduct()
		err298 := argvalue1.Read(jsProt297)
		if err298 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditProduct(value0, value1))
		fmt.Print("\n")
		break
	case "getProductsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProductsByIds requires 2 args")
			flag.Usage()
		}
		arg299 := flag.Arg(1)
		mbTrans300 := thrift.NewTMemoryBufferLen(len(arg299))
		defer mbTrans300.Close()
		_, err301 := mbTrans300.WriteString(arg299)
		if err301 != nil {
			Usage()
			return
		}
		factory302 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt303 := factory302.GetProtocol(mbTrans300)
		argvalue0 := vico_server.NewRequestHeader()
		err304 := argvalue0.Read(jsProt303)
		if err304 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg305 := flag.Arg(2)
		mbTrans306 := thrift.NewTMemoryBufferLen(len(arg305))
		defer mbTrans306.Close()
		_, err307 := mbTrans306.WriteString(arg305)
		if err307 != nil {
			Usage()
			return
		}
		factory308 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt309 := factory308.GetProtocol(mbTrans306)
		containerStruct1 := vico_server.NewGetProductsByIdsArgs()
		err310 := containerStruct1.ReadField2(jsProt309)
		if err310 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetProductsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchProductsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchProductsByParams requires 4 args")
			flag.Usage()
		}
		arg311 := flag.Arg(1)
		mbTrans312 := thrift.NewTMemoryBufferLen(len(arg311))
		defer mbTrans312.Close()
		_, err313 := mbTrans312.WriteString(arg311)
		if err313 != nil {
			Usage()
			return
		}
		factory314 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt315 := factory314.GetProtocol(mbTrans312)
		argvalue0 := vico_server.NewRequestHeader()
		err316 := argvalue0.Read(jsProt315)
		if err316 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg317 := flag.Arg(2)
		mbTrans318 := thrift.NewTMemoryBufferLen(len(arg317))
		defer mbTrans318.Close()
		_, err319 := mbTrans318.WriteString(arg317)
		if err319 != nil {
			Usage()
			return
		}
		factory320 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt321 := factory320.GetProtocol(mbTrans318)
		argvalue1 := vico_server.NewProductParams()
		err322 := argvalue1.Read(jsProt321)
		if err322 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err323 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err323 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err324 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err324 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchProductsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addProductComments":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddProductComments requires 3 args")
			flag.Usage()
		}
		arg325 := flag.Arg(1)
		mbTrans326 := thrift.NewTMemoryBufferLen(len(arg325))
		defer mbTrans326.Close()
		_, err327 := mbTrans326.WriteString(arg325)
		if err327 != nil {
			Usage()
			return
		}
		factory328 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt329 := factory328.GetProtocol(mbTrans326)
		argvalue0 := vico_server.NewRequestHeader()
		err330 := argvalue0.Read(jsProt329)
		if err330 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err331 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err331 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg332 := flag.Arg(3)
		mbTrans333 := thrift.NewTMemoryBufferLen(len(arg332))
		defer mbTrans333.Close()
		_, err334 := mbTrans333.WriteString(arg332)
		if err334 != nil {
			Usage()
			return
		}
		factory335 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt336 := factory335.GetProtocol(mbTrans333)
		containerStruct2 := vico_server.NewAddProductCommentsArgs()
		err337 := containerStruct2.ReadField3(jsProt336)
		if err337 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AddProductComments(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteProductComments":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteProductComments requires 3 args")
			flag.Usage()
		}
		arg338 := flag.Arg(1)
		mbTrans339 := thrift.NewTMemoryBufferLen(len(arg338))
		defer mbTrans339.Close()
		_, err340 := mbTrans339.WriteString(arg338)
		if err340 != nil {
			Usage()
			return
		}
		factory341 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt342 := factory341.GetProtocol(mbTrans339)
		argvalue0 := vico_server.NewRequestHeader()
		err343 := argvalue0.Read(jsProt342)
		if err343 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err344 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err344 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg345 := flag.Arg(3)
		mbTrans346 := thrift.NewTMemoryBufferLen(len(arg345))
		defer mbTrans346.Close()
		_, err347 := mbTrans346.WriteString(arg345)
		if err347 != nil {
			Usage()
			return
		}
		factory348 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt349 := factory348.GetProtocol(mbTrans346)
		containerStruct2 := vico_server.NewDeleteProductCommentsArgs()
		err350 := containerStruct2.ReadField3(jsProt349)
		if err350 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteProductComments(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addProductPack":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddProductPack requires 2 args")
			flag.Usage()
		}
		arg351 := flag.Arg(1)
		mbTrans352 := thrift.NewTMemoryBufferLen(len(arg351))
		defer mbTrans352.Close()
		_, err353 := mbTrans352.WriteString(arg351)
		if err353 != nil {
			Usage()
			return
		}
		factory354 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt355 := factory354.GetProtocol(mbTrans352)
		argvalue0 := vico_server.NewRequestHeader()
		err356 := argvalue0.Read(jsProt355)
		if err356 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg357 := flag.Arg(2)
		mbTrans358 := thrift.NewTMemoryBufferLen(len(arg357))
		defer mbTrans358.Close()
		_, err359 := mbTrans358.WriteString(arg357)
		if err359 != nil {
			Usage()
			return
		}
		factory360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt361 := factory360.GetProtocol(mbTrans358)
		argvalue1 := vico_server.NewProductPack()
		err362 := argvalue1.Read(jsProt361)
		if err362 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddProductPack(value0, value1))
		fmt.Print("\n")
		break
	case "editProductPack":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditProductPack requires 2 args")
			flag.Usage()
		}
		arg363 := flag.Arg(1)
		mbTrans364 := thrift.NewTMemoryBufferLen(len(arg363))
		defer mbTrans364.Close()
		_, err365 := mbTrans364.WriteString(arg363)
		if err365 != nil {
			Usage()
			return
		}
		factory366 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt367 := factory366.GetProtocol(mbTrans364)
		argvalue0 := vico_server.NewRequestHeader()
		err368 := argvalue0.Read(jsProt367)
		if err368 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg369 := flag.Arg(2)
		mbTrans370 := thrift.NewTMemoryBufferLen(len(arg369))
		defer mbTrans370.Close()
		_, err371 := mbTrans370.WriteString(arg369)
		if err371 != nil {
			Usage()
			return
		}
		factory372 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt373 := factory372.GetProtocol(mbTrans370)
		argvalue1 := vico_server.NewProductPack()
		err374 := argvalue1.Read(jsProt373)
		if err374 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditProductPack(value0, value1))
		fmt.Print("\n")
		break
	case "getProductPacksByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProductPacksByIds requires 2 args")
			flag.Usage()
		}
		arg375 := flag.Arg(1)
		mbTrans376 := thrift.NewTMemoryBufferLen(len(arg375))
		defer mbTrans376.Close()
		_, err377 := mbTrans376.WriteString(arg375)
		if err377 != nil {
			Usage()
			return
		}
		factory378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt379 := factory378.GetProtocol(mbTrans376)
		argvalue0 := vico_server.NewRequestHeader()
		err380 := argvalue0.Read(jsProt379)
		if err380 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg381 := flag.Arg(2)
		mbTrans382 := thrift.NewTMemoryBufferLen(len(arg381))
		defer mbTrans382.Close()
		_, err383 := mbTrans382.WriteString(arg381)
		if err383 != nil {
			Usage()
			return
		}
		factory384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt385 := factory384.GetProtocol(mbTrans382)
		containerStruct1 := vico_server.NewGetProductPacksByIdsArgs()
		err386 := containerStruct1.ReadField2(jsProt385)
		if err386 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetProductPacksByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchProductPacksByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchProductPacksByParams requires 4 args")
			flag.Usage()
		}
		arg387 := flag.Arg(1)
		mbTrans388 := thrift.NewTMemoryBufferLen(len(arg387))
		defer mbTrans388.Close()
		_, err389 := mbTrans388.WriteString(arg387)
		if err389 != nil {
			Usage()
			return
		}
		factory390 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt391 := factory390.GetProtocol(mbTrans388)
		argvalue0 := vico_server.NewRequestHeader()
		err392 := argvalue0.Read(jsProt391)
		if err392 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg393 := flag.Arg(2)
		mbTrans394 := thrift.NewTMemoryBufferLen(len(arg393))
		defer mbTrans394.Close()
		_, err395 := mbTrans394.WriteString(arg393)
		if err395 != nil {
			Usage()
			return
		}
		factory396 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt397 := factory396.GetProtocol(mbTrans394)
		argvalue1 := vico_server.NewProductPackParams()
		err398 := argvalue1.Read(jsProt397)
		if err398 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err399 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err399 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err400 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err400 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchProductPacksByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteProductPackByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteProductPackByIds requires 3 args")
			flag.Usage()
		}
		arg401 := flag.Arg(1)
		mbTrans402 := thrift.NewTMemoryBufferLen(len(arg401))
		defer mbTrans402.Close()
		_, err403 := mbTrans402.WriteString(arg401)
		if err403 != nil {
			Usage()
			return
		}
		factory404 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt405 := factory404.GetProtocol(mbTrans402)
		argvalue0 := vico_server.NewRequestHeader()
		err406 := argvalue0.Read(jsProt405)
		if err406 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err407 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err407 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg408 := flag.Arg(3)
		mbTrans409 := thrift.NewTMemoryBufferLen(len(arg408))
		defer mbTrans409.Close()
		_, err410 := mbTrans409.WriteString(arg408)
		if err410 != nil {
			Usage()
			return
		}
		factory411 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt412 := factory411.GetProtocol(mbTrans409)
		containerStruct2 := vico_server.NewDeleteProductPackByIdsArgs()
		err413 := containerStruct2.ReadField3(jsProt412)
		if err413 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteProductPackByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getProductPackImgUrl":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetProductPackImgUrl requires 2 args")
			flag.Usage()
		}
		arg414 := flag.Arg(1)
		mbTrans415 := thrift.NewTMemoryBufferLen(len(arg414))
		defer mbTrans415.Close()
		_, err416 := mbTrans415.WriteString(arg414)
		if err416 != nil {
			Usage()
			return
		}
		factory417 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt418 := factory417.GetProtocol(mbTrans415)
		argvalue0 := vico_server.NewRequestHeader()
		err419 := argvalue0.Read(jsProt418)
		if err419 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err420 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err420 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetProductPackImgUrl(value0, value1))
		fmt.Print("\n")
		break
	case "addAdOrder":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdOrder requires 2 args")
			flag.Usage()
		}
		arg421 := flag.Arg(1)
		mbTrans422 := thrift.NewTMemoryBufferLen(len(arg421))
		defer mbTrans422.Close()
		_, err423 := mbTrans422.WriteString(arg421)
		if err423 != nil {
			Usage()
			return
		}
		factory424 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt425 := factory424.GetProtocol(mbTrans422)
		argvalue0 := vico_server.NewRequestHeader()
		err426 := argvalue0.Read(jsProt425)
		if err426 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg427 := flag.Arg(2)
		mbTrans428 := thrift.NewTMemoryBufferLen(len(arg427))
		defer mbTrans428.Close()
		_, err429 := mbTrans428.WriteString(arg427)
		if err429 != nil {
			Usage()
			return
		}
		factory430 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt431 := factory430.GetProtocol(mbTrans428)
		argvalue1 := vico_server.NewAdOrder()
		err432 := argvalue1.Read(jsProt431)
		if err432 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAdOrder(value0, value1))
		fmt.Print("\n")
		break
	case "editAdOrder":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdOrder requires 2 args")
			flag.Usage()
		}
		arg433 := flag.Arg(1)
		mbTrans434 := thrift.NewTMemoryBufferLen(len(arg433))
		defer mbTrans434.Close()
		_, err435 := mbTrans434.WriteString(arg433)
		if err435 != nil {
			Usage()
			return
		}
		factory436 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt437 := factory436.GetProtocol(mbTrans434)
		argvalue0 := vico_server.NewRequestHeader()
		err438 := argvalue0.Read(jsProt437)
		if err438 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg439 := flag.Arg(2)
		mbTrans440 := thrift.NewTMemoryBufferLen(len(arg439))
		defer mbTrans440.Close()
		_, err441 := mbTrans440.WriteString(arg439)
		if err441 != nil {
			Usage()
			return
		}
		factory442 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt443 := factory442.GetProtocol(mbTrans440)
		argvalue1 := vico_server.NewAdOrder()
		err444 := argvalue1.Read(jsProt443)
		if err444 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditAdOrder(value0, value1))
		fmt.Print("\n")
		break
	case "getAdOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg445 := flag.Arg(1)
		mbTrans446 := thrift.NewTMemoryBufferLen(len(arg445))
		defer mbTrans446.Close()
		_, err447 := mbTrans446.WriteString(arg445)
		if err447 != nil {
			Usage()
			return
		}
		factory448 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt449 := factory448.GetProtocol(mbTrans446)
		argvalue0 := vico_server.NewRequestHeader()
		err450 := argvalue0.Read(jsProt449)
		if err450 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg451 := flag.Arg(2)
		mbTrans452 := thrift.NewTMemoryBufferLen(len(arg451))
		defer mbTrans452.Close()
		_, err453 := mbTrans452.WriteString(arg451)
		if err453 != nil {
			Usage()
			return
		}
		factory454 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt455 := factory454.GetProtocol(mbTrans452)
		containerStruct1 := vico_server.NewGetAdOrdersByIdsArgs()
		err456 := containerStruct1.ReadField2(jsProt455)
		if err456 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdOrdersByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdOrdersByParams requires 2 args")
			flag.Usage()
		}
		arg457 := flag.Arg(1)
		mbTrans458 := thrift.NewTMemoryBufferLen(len(arg457))
		defer mbTrans458.Close()
		_, err459 := mbTrans458.WriteString(arg457)
		if err459 != nil {
			Usage()
			return
		}
		factory460 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt461 := factory460.GetProtocol(mbTrans458)
		argvalue0 := vico_server.NewRequestHeader()
		err462 := argvalue0.Read(jsProt461)
		if err462 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg463 := flag.Arg(2)
		mbTrans464 := thrift.NewTMemoryBufferLen(len(arg463))
		defer mbTrans464.Close()
		_, err465 := mbTrans464.WriteString(arg463)
		if err465 != nil {
			Usage()
			return
		}
		factory466 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt467 := factory466.GetProtocol(mbTrans464)
		argvalue1 := vico_server.NewAdOrderParams()
		err468 := argvalue1.Read(jsProt467)
		if err468 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdOrdersByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseAdOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg469 := flag.Arg(1)
		mbTrans470 := thrift.NewTMemoryBufferLen(len(arg469))
		defer mbTrans470.Close()
		_, err471 := mbTrans470.WriteString(arg469)
		if err471 != nil {
			Usage()
			return
		}
		factory472 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt473 := factory472.GetProtocol(mbTrans470)
		argvalue0 := vico_server.NewRequestHeader()
		err474 := argvalue0.Read(jsProt473)
		if err474 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg475 := flag.Arg(2)
		mbTrans476 := thrift.NewTMemoryBufferLen(len(arg475))
		defer mbTrans476.Close()
		_, err477 := mbTrans476.WriteString(arg475)
		if err477 != nil {
			Usage()
			return
		}
		factory478 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt479 := factory478.GetProtocol(mbTrans476)
		containerStruct1 := vico_server.NewPauseAdOrdersByIdsArgs()
		err480 := containerStruct1.ReadField2(jsProt479)
		if err480 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseAdOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeAdOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeAdOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg481 := flag.Arg(1)
		mbTrans482 := thrift.NewTMemoryBufferLen(len(arg481))
		defer mbTrans482.Close()
		_, err483 := mbTrans482.WriteString(arg481)
		if err483 != nil {
			Usage()
			return
		}
		factory484 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt485 := factory484.GetProtocol(mbTrans482)
		argvalue0 := vico_server.NewRequestHeader()
		err486 := argvalue0.Read(jsProt485)
		if err486 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg487 := flag.Arg(2)
		mbTrans488 := thrift.NewTMemoryBufferLen(len(arg487))
		defer mbTrans488.Close()
		_, err489 := mbTrans488.WriteString(arg487)
		if err489 != nil {
			Usage()
			return
		}
		factory490 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt491 := factory490.GetProtocol(mbTrans488)
		containerStruct1 := vico_server.NewResumeAdOrdersByIdsArgs()
		err492 := containerStruct1.ReadField2(jsProt491)
		if err492 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeAdOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdOrderByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdOrderByIds requires 2 args")
			flag.Usage()
		}
		arg493 := flag.Arg(1)
		mbTrans494 := thrift.NewTMemoryBufferLen(len(arg493))
		defer mbTrans494.Close()
		_, err495 := mbTrans494.WriteString(arg493)
		if err495 != nil {
			Usage()
			return
		}
		factory496 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt497 := factory496.GetProtocol(mbTrans494)
		argvalue0 := vico_server.NewRequestHeader()
		err498 := argvalue0.Read(jsProt497)
		if err498 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg499 := flag.Arg(2)
		mbTrans500 := thrift.NewTMemoryBufferLen(len(arg499))
		defer mbTrans500.Close()
		_, err501 := mbTrans500.WriteString(arg499)
		if err501 != nil {
			Usage()
			return
		}
		factory502 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt503 := factory502.GetProtocol(mbTrans500)
		containerStruct1 := vico_server.NewDeleteAdOrderByIdsArgs()
		err504 := containerStruct1.ReadField2(jsProt503)
		if err504 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteAdOrderByIds(value0, value1))
		fmt.Print("\n")
		break
	case "passAdOrderByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PassAdOrderByIds requires 3 args")
			flag.Usage()
		}
		arg505 := flag.Arg(1)
		mbTrans506 := thrift.NewTMemoryBufferLen(len(arg505))
		defer mbTrans506.Close()
		_, err507 := mbTrans506.WriteString(arg505)
		if err507 != nil {
			Usage()
			return
		}
		factory508 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt509 := factory508.GetProtocol(mbTrans506)
		argvalue0 := vico_server.NewRequestHeader()
		err510 := argvalue0.Read(jsProt509)
		if err510 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg511 := flag.Arg(2)
		mbTrans512 := thrift.NewTMemoryBufferLen(len(arg511))
		defer mbTrans512.Close()
		_, err513 := mbTrans512.WriteString(arg511)
		if err513 != nil {
			Usage()
			return
		}
		factory514 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt515 := factory514.GetProtocol(mbTrans512)
		containerStruct1 := vico_server.NewPassAdOrderByIdsArgs()
		err516 := containerStruct1.ReadField2(jsProt515)
		if err516 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.PassAdOrderByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "rejectAdOrderByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "RejectAdOrderByIds requires 3 args")
			flag.Usage()
		}
		arg518 := flag.Arg(1)
		mbTrans519 := thrift.NewTMemoryBufferLen(len(arg518))
		defer mbTrans519.Close()
		_, err520 := mbTrans519.WriteString(arg518)
		if err520 != nil {
			Usage()
			return
		}
		factory521 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt522 := factory521.GetProtocol(mbTrans519)
		argvalue0 := vico_server.NewRequestHeader()
		err523 := argvalue0.Read(jsProt522)
		if err523 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg524 := flag.Arg(2)
		mbTrans525 := thrift.NewTMemoryBufferLen(len(arg524))
		defer mbTrans525.Close()
		_, err526 := mbTrans525.WriteString(arg524)
		if err526 != nil {
			Usage()
			return
		}
		factory527 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt528 := factory527.GetProtocol(mbTrans525)
		containerStruct1 := vico_server.NewRejectAdOrderByIdsArgs()
		err529 := containerStruct1.ReadField2(jsProt528)
		if err529 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.RejectAdOrderByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "matchCampaignToAdOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "MatchCampaignToAdOrder requires 3 args")
			flag.Usage()
		}
		arg531 := flag.Arg(1)
		mbTrans532 := thrift.NewTMemoryBufferLen(len(arg531))
		defer mbTrans532.Close()
		_, err533 := mbTrans532.WriteString(arg531)
		if err533 != nil {
			Usage()
			return
		}
		factory534 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt535 := factory534.GetProtocol(mbTrans532)
		argvalue0 := vico_server.NewRequestHeader()
		err536 := argvalue0.Read(jsProt535)
		if err536 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err537 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err537 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg538 := flag.Arg(3)
		mbTrans539 := thrift.NewTMemoryBufferLen(len(arg538))
		defer mbTrans539.Close()
		_, err540 := mbTrans539.WriteString(arg538)
		if err540 != nil {
			Usage()
			return
		}
		factory541 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt542 := factory541.GetProtocol(mbTrans539)
		containerStruct2 := vico_server.NewMatchCampaignToAdOrderArgs()
		err543 := containerStruct2.ReadField3(jsProt542)
		if err543 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Campaigns
		value2 := argvalue2
		fmt.Print(client.MatchCampaignToAdOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addFtLanding":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddFtLanding requires 2 args")
			flag.Usage()
		}
		arg544 := flag.Arg(1)
		mbTrans545 := thrift.NewTMemoryBufferLen(len(arg544))
		defer mbTrans545.Close()
		_, err546 := mbTrans545.WriteString(arg544)
		if err546 != nil {
			Usage()
			return
		}
		factory547 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt548 := factory547.GetProtocol(mbTrans545)
		argvalue0 := vico_server.NewRequestHeader()
		err549 := argvalue0.Read(jsProt548)
		if err549 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg550 := flag.Arg(2)
		mbTrans551 := thrift.NewTMemoryBufferLen(len(arg550))
		defer mbTrans551.Close()
		_, err552 := mbTrans551.WriteString(arg550)
		if err552 != nil {
			Usage()
			return
		}
		factory553 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt554 := factory553.GetProtocol(mbTrans551)
		argvalue1 := vico_server.NewFtLanding()
		err555 := argvalue1.Read(jsProt554)
		if err555 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddFtLanding(value0, value1))
		fmt.Print("\n")
		break
	case "editFtLanding":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditFtLanding requires 2 args")
			flag.Usage()
		}
		arg556 := flag.Arg(1)
		mbTrans557 := thrift.NewTMemoryBufferLen(len(arg556))
		defer mbTrans557.Close()
		_, err558 := mbTrans557.WriteString(arg556)
		if err558 != nil {
			Usage()
			return
		}
		factory559 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt560 := factory559.GetProtocol(mbTrans557)
		argvalue0 := vico_server.NewRequestHeader()
		err561 := argvalue0.Read(jsProt560)
		if err561 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg562 := flag.Arg(2)
		mbTrans563 := thrift.NewTMemoryBufferLen(len(arg562))
		defer mbTrans563.Close()
		_, err564 := mbTrans563.WriteString(arg562)
		if err564 != nil {
			Usage()
			return
		}
		factory565 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt566 := factory565.GetProtocol(mbTrans563)
		argvalue1 := vico_server.NewFtLanding()
		err567 := argvalue1.Read(jsProt566)
		if err567 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditFtLanding(value0, value1))
		fmt.Print("\n")
		break
	case "deleteFtLandingByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteFtLandingByIds requires 3 args")
			flag.Usage()
		}
		arg568 := flag.Arg(1)
		mbTrans569 := thrift.NewTMemoryBufferLen(len(arg568))
		defer mbTrans569.Close()
		_, err570 := mbTrans569.WriteString(arg568)
		if err570 != nil {
			Usage()
			return
		}
		factory571 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt572 := factory571.GetProtocol(mbTrans569)
		argvalue0 := vico_server.NewRequestHeader()
		err573 := argvalue0.Read(jsProt572)
		if err573 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err574 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err574 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg575 := flag.Arg(3)
		mbTrans576 := thrift.NewTMemoryBufferLen(len(arg575))
		defer mbTrans576.Close()
		_, err577 := mbTrans576.WriteString(arg575)
		if err577 != nil {
			Usage()
			return
		}
		factory578 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt579 := factory578.GetProtocol(mbTrans576)
		containerStruct2 := vico_server.NewDeleteFtLandingByIdsArgs()
		err580 := containerStruct2.ReadField3(jsProt579)
		if err580 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteFtLandingByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFtLandingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFtLandingByIds requires 2 args")
			flag.Usage()
		}
		arg581 := flag.Arg(1)
		mbTrans582 := thrift.NewTMemoryBufferLen(len(arg581))
		defer mbTrans582.Close()
		_, err583 := mbTrans582.WriteString(arg581)
		if err583 != nil {
			Usage()
			return
		}
		factory584 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt585 := factory584.GetProtocol(mbTrans582)
		argvalue0 := vico_server.NewRequestHeader()
		err586 := argvalue0.Read(jsProt585)
		if err586 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg587 := flag.Arg(2)
		mbTrans588 := thrift.NewTMemoryBufferLen(len(arg587))
		defer mbTrans588.Close()
		_, err589 := mbTrans588.WriteString(arg587)
		if err589 != nil {
			Usage()
			return
		}
		factory590 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt591 := factory590.GetProtocol(mbTrans588)
		containerStruct1 := vico_server.NewGetFtLandingByIdsArgs()
		err592 := containerStruct1.ReadField2(jsProt591)
		if err592 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetFtLandingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "queryFtLandingByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QueryFtLandingByParams requires 4 args")
			flag.Usage()
		}
		arg593 := flag.Arg(1)
		mbTrans594 := thrift.NewTMemoryBufferLen(len(arg593))
		defer mbTrans594.Close()
		_, err595 := mbTrans594.WriteString(arg593)
		if err595 != nil {
			Usage()
			return
		}
		factory596 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt597 := factory596.GetProtocol(mbTrans594)
		argvalue0 := vico_server.NewRequestHeader()
		err598 := argvalue0.Read(jsProt597)
		if err598 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg599 := flag.Arg(2)
		mbTrans600 := thrift.NewTMemoryBufferLen(len(arg599))
		defer mbTrans600.Close()
		_, err601 := mbTrans600.WriteString(arg599)
		if err601 != nil {
			Usage()
			return
		}
		factory602 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt603 := factory602.GetProtocol(mbTrans600)
		argvalue1 := vico_server.NewFtLandingQueryParams()
		err604 := argvalue1.Read(jsProt603)
		if err604 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err605 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err605 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err606 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err606 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.QueryFtLandingByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updateOrderCampaignStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateOrderCampaignStatus requires 3 args")
			flag.Usage()
		}
		arg607 := flag.Arg(1)
		mbTrans608 := thrift.NewTMemoryBufferLen(len(arg607))
		defer mbTrans608.Close()
		_, err609 := mbTrans608.WriteString(arg607)
		if err609 != nil {
			Usage()
			return
		}
		factory610 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt611 := factory610.GetProtocol(mbTrans608)
		argvalue0 := vico_server.NewRequestHeader()
		err612 := argvalue0.Read(jsProt611)
		if err612 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err613 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err613 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg614 := flag.Arg(3)
		mbTrans615 := thrift.NewTMemoryBufferLen(len(arg614))
		defer mbTrans615.Close()
		_, err616 := mbTrans615.WriteString(arg614)
		if err616 != nil {
			Usage()
			return
		}
		factory617 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt618 := factory617.GetProtocol(mbTrans615)
		argvalue2 := vico_server.NewRelatedAdCampaign()
		err619 := argvalue2.Read(jsProt618)
		if err619 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.UpdateOrderCampaignStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addFtLandingComments":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddFtLandingComments requires 3 args")
			flag.Usage()
		}
		arg620 := flag.Arg(1)
		mbTrans621 := thrift.NewTMemoryBufferLen(len(arg620))
		defer mbTrans621.Close()
		_, err622 := mbTrans621.WriteString(arg620)
		if err622 != nil {
			Usage()
			return
		}
		factory623 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt624 := factory623.GetProtocol(mbTrans621)
		argvalue0 := vico_server.NewRequestHeader()
		err625 := argvalue0.Read(jsProt624)
		if err625 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err626 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err626 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg627 := flag.Arg(3)
		mbTrans628 := thrift.NewTMemoryBufferLen(len(arg627))
		defer mbTrans628.Close()
		_, err629 := mbTrans628.WriteString(arg627)
		if err629 != nil {
			Usage()
			return
		}
		factory630 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt631 := factory630.GetProtocol(mbTrans628)
		containerStruct2 := vico_server.NewAddFtLandingCommentsArgs()
		err632 := containerStruct2.ReadField3(jsProt631)
		if err632 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AddFtLandingComments(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteFtLandingComments":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteFtLandingComments requires 3 args")
			flag.Usage()
		}
		arg633 := flag.Arg(1)
		mbTrans634 := thrift.NewTMemoryBufferLen(len(arg633))
		defer mbTrans634.Close()
		_, err635 := mbTrans634.WriteString(arg633)
		if err635 != nil {
			Usage()
			return
		}
		factory636 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt637 := factory636.GetProtocol(mbTrans634)
		argvalue0 := vico_server.NewRequestHeader()
		err638 := argvalue0.Read(jsProt637)
		if err638 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err639 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err639 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg640 := flag.Arg(3)
		mbTrans641 := thrift.NewTMemoryBufferLen(len(arg640))
		defer mbTrans641.Close()
		_, err642 := mbTrans641.WriteString(arg640)
		if err642 != nil {
			Usage()
			return
		}
		factory643 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt644 := factory643.GetProtocol(mbTrans641)
		containerStruct2 := vico_server.NewDeleteFtLandingCommentsArgs()
		err645 := containerStruct2.ReadField3(jsProt644)
		if err645 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteFtLandingComments(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAllFtLandingTemplates":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAllFtLandingTemplates requires 3 args")
			flag.Usage()
		}
		arg646 := flag.Arg(1)
		mbTrans647 := thrift.NewTMemoryBufferLen(len(arg646))
		defer mbTrans647.Close()
		_, err648 := mbTrans647.WriteString(arg646)
		if err648 != nil {
			Usage()
			return
		}
		factory649 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt650 := factory649.GetProtocol(mbTrans647)
		argvalue0 := vico_server.NewRequestHeader()
		err651 := argvalue0.Read(jsProt650)
		if err651 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err652 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err652 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := vico_server.FtLandingTemplateType(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetAllFtLandingTemplates(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFtLandingTemplateByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFtLandingTemplateByIds requires 2 args")
			flag.Usage()
		}
		arg653 := flag.Arg(1)
		mbTrans654 := thrift.NewTMemoryBufferLen(len(arg653))
		defer mbTrans654.Close()
		_, err655 := mbTrans654.WriteString(arg653)
		if err655 != nil {
			Usage()
			return
		}
		factory656 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt657 := factory656.GetProtocol(mbTrans654)
		argvalue0 := vico_server.NewRequestHeader()
		err658 := argvalue0.Read(jsProt657)
		if err658 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg659 := flag.Arg(2)
		mbTrans660 := thrift.NewTMemoryBufferLen(len(arg659))
		defer mbTrans660.Close()
		_, err661 := mbTrans660.WriteString(arg659)
		if err661 != nil {
			Usage()
			return
		}
		factory662 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt663 := factory662.GetProtocol(mbTrans660)
		containerStruct1 := vico_server.NewGetFtLandingTemplateByIdsArgs()
		err664 := containerStruct1.ReadField2(jsProt663)
		if err664 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetFtLandingTemplateByIds(value0, value1))
		fmt.Print("\n")
		break
	case "publishProduct":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PublishProduct requires 2 args")
			flag.Usage()
		}
		arg665 := flag.Arg(1)
		mbTrans666 := thrift.NewTMemoryBufferLen(len(arg665))
		defer mbTrans666.Close()
		_, err667 := mbTrans666.WriteString(arg665)
		if err667 != nil {
			Usage()
			return
		}
		factory668 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt669 := factory668.GetProtocol(mbTrans666)
		argvalue0 := vico_server.NewRequestHeader()
		err670 := argvalue0.Read(jsProt669)
		if err670 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg671 := flag.Arg(2)
		mbTrans672 := thrift.NewTMemoryBufferLen(len(arg671))
		defer mbTrans672.Close()
		_, err673 := mbTrans672.WriteString(arg671)
		if err673 != nil {
			Usage()
			return
		}
		factory674 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt675 := factory674.GetProtocol(mbTrans672)
		containerStruct1 := vico_server.NewPublishProductArgs()
		err676 := containerStruct1.ReadField2(jsProt675)
		if err676 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PublishProduct(value0, value1))
		fmt.Print("\n")
		break
	case "unPublishProduct":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UnPublishProduct requires 2 args")
			flag.Usage()
		}
		arg677 := flag.Arg(1)
		mbTrans678 := thrift.NewTMemoryBufferLen(len(arg677))
		defer mbTrans678.Close()
		_, err679 := mbTrans678.WriteString(arg677)
		if err679 != nil {
			Usage()
			return
		}
		factory680 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt681 := factory680.GetProtocol(mbTrans678)
		argvalue0 := vico_server.NewRequestHeader()
		err682 := argvalue0.Read(jsProt681)
		if err682 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg683 := flag.Arg(2)
		mbTrans684 := thrift.NewTMemoryBufferLen(len(arg683))
		defer mbTrans684.Close()
		_, err685 := mbTrans684.WriteString(arg683)
		if err685 != nil {
			Usage()
			return
		}
		factory686 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt687 := factory686.GetProtocol(mbTrans684)
		containerStruct1 := vico_server.NewUnPublishProductArgs()
		err688 := containerStruct1.ReadField2(jsProt687)
		if err688 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.UnPublishProduct(value0, value1))
		fmt.Print("\n")
		break
	case "publishProductPack":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PublishProductPack requires 2 args")
			flag.Usage()
		}
		arg689 := flag.Arg(1)
		mbTrans690 := thrift.NewTMemoryBufferLen(len(arg689))
		defer mbTrans690.Close()
		_, err691 := mbTrans690.WriteString(arg689)
		if err691 != nil {
			Usage()
			return
		}
		factory692 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt693 := factory692.GetProtocol(mbTrans690)
		argvalue0 := vico_server.NewRequestHeader()
		err694 := argvalue0.Read(jsProt693)
		if err694 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg695 := flag.Arg(2)
		mbTrans696 := thrift.NewTMemoryBufferLen(len(arg695))
		defer mbTrans696.Close()
		_, err697 := mbTrans696.WriteString(arg695)
		if err697 != nil {
			Usage()
			return
		}
		factory698 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt699 := factory698.GetProtocol(mbTrans696)
		containerStruct1 := vico_server.NewPublishProductPackArgs()
		err700 := containerStruct1.ReadField2(jsProt699)
		if err700 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PublishProductPack(value0, value1))
		fmt.Print("\n")
		break
	case "unPublishProductPack":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UnPublishProductPack requires 2 args")
			flag.Usage()
		}
		arg701 := flag.Arg(1)
		mbTrans702 := thrift.NewTMemoryBufferLen(len(arg701))
		defer mbTrans702.Close()
		_, err703 := mbTrans702.WriteString(arg701)
		if err703 != nil {
			Usage()
			return
		}
		factory704 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt705 := factory704.GetProtocol(mbTrans702)
		argvalue0 := vico_server.NewRequestHeader()
		err706 := argvalue0.Read(jsProt705)
		if err706 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg707 := flag.Arg(2)
		mbTrans708 := thrift.NewTMemoryBufferLen(len(arg707))
		defer mbTrans708.Close()
		_, err709 := mbTrans708.WriteString(arg707)
		if err709 != nil {
			Usage()
			return
		}
		factory710 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt711 := factory710.GetProtocol(mbTrans708)
		containerStruct1 := vico_server.NewUnPublishProductPackArgs()
		err712 := containerStruct1.ReadField2(jsProt711)
		if err712 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.UnPublishProductPack(value0, value1))
		fmt.Print("\n")
		break
	case "addCommentsToLibrary":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddCommentsToLibrary requires 3 args")
			flag.Usage()
		}
		arg713 := flag.Arg(1)
		mbTrans714 := thrift.NewTMemoryBufferLen(len(arg713))
		defer mbTrans714.Close()
		_, err715 := mbTrans714.WriteString(arg713)
		if err715 != nil {
			Usage()
			return
		}
		factory716 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt717 := factory716.GetProtocol(mbTrans714)
		argvalue0 := vico_server.NewRequestHeader()
		err718 := argvalue0.Read(jsProt717)
		if err718 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err719 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err719 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg720 := flag.Arg(3)
		mbTrans721 := thrift.NewTMemoryBufferLen(len(arg720))
		defer mbTrans721.Close()
		_, err722 := mbTrans721.WriteString(arg720)
		if err722 != nil {
			Usage()
			return
		}
		factory723 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt724 := factory723.GetProtocol(mbTrans721)
		containerStruct2 := vico_server.NewAddCommentsToLibraryArgs()
		err725 := containerStruct2.ReadField3(jsProt724)
		if err725 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Comments
		value2 := argvalue2
		fmt.Print(client.AddCommentsToLibrary(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteProductsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteProductsByIds requires 2 args")
			flag.Usage()
		}
		arg726 := flag.Arg(1)
		mbTrans727 := thrift.NewTMemoryBufferLen(len(arg726))
		defer mbTrans727.Close()
		_, err728 := mbTrans727.WriteString(arg726)
		if err728 != nil {
			Usage()
			return
		}
		factory729 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt730 := factory729.GetProtocol(mbTrans727)
		argvalue0 := vico_server.NewRequestHeader()
		err731 := argvalue0.Read(jsProt730)
		if err731 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg732 := flag.Arg(2)
		mbTrans733 := thrift.NewTMemoryBufferLen(len(arg732))
		defer mbTrans733.Close()
		_, err734 := mbTrans733.WriteString(arg732)
		if err734 != nil {
			Usage()
			return
		}
		factory735 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt736 := factory735.GetProtocol(mbTrans733)
		containerStruct1 := vico_server.NewDeleteProductsByIdsArgs()
		err737 := containerStruct1.ReadField2(jsProt736)
		if err737 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteProductsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addSocialMediaPost":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSocialMediaPost requires 2 args")
			flag.Usage()
		}
		arg738 := flag.Arg(1)
		mbTrans739 := thrift.NewTMemoryBufferLen(len(arg738))
		defer mbTrans739.Close()
		_, err740 := mbTrans739.WriteString(arg738)
		if err740 != nil {
			Usage()
			return
		}
		factory741 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt742 := factory741.GetProtocol(mbTrans739)
		argvalue0 := vico_server.NewRequestHeader()
		err743 := argvalue0.Read(jsProt742)
		if err743 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg744 := flag.Arg(2)
		mbTrans745 := thrift.NewTMemoryBufferLen(len(arg744))
		defer mbTrans745.Close()
		_, err746 := mbTrans745.WriteString(arg744)
		if err746 != nil {
			Usage()
			return
		}
		factory747 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt748 := factory747.GetProtocol(mbTrans745)
		argvalue1 := vico_server.NewSocialMediaPost()
		err749 := argvalue1.Read(jsProt748)
		if err749 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSocialMediaPost(value0, value1))
		fmt.Print("\n")
		break
	case "editSocialMediaPost":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditSocialMediaPost requires 2 args")
			flag.Usage()
		}
		arg750 := flag.Arg(1)
		mbTrans751 := thrift.NewTMemoryBufferLen(len(arg750))
		defer mbTrans751.Close()
		_, err752 := mbTrans751.WriteString(arg750)
		if err752 != nil {
			Usage()
			return
		}
		factory753 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt754 := factory753.GetProtocol(mbTrans751)
		argvalue0 := vico_server.NewRequestHeader()
		err755 := argvalue0.Read(jsProt754)
		if err755 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg756 := flag.Arg(2)
		mbTrans757 := thrift.NewTMemoryBufferLen(len(arg756))
		defer mbTrans757.Close()
		_, err758 := mbTrans757.WriteString(arg756)
		if err758 != nil {
			Usage()
			return
		}
		factory759 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt760 := factory759.GetProtocol(mbTrans757)
		argvalue1 := vico_server.NewSocialMediaPost()
		err761 := argvalue1.Read(jsProt760)
		if err761 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditSocialMediaPost(value0, value1))
		fmt.Print("\n")
		break
	case "deleteSocialMediaPostByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteSocialMediaPostByIds requires 3 args")
			flag.Usage()
		}
		arg762 := flag.Arg(1)
		mbTrans763 := thrift.NewTMemoryBufferLen(len(arg762))
		defer mbTrans763.Close()
		_, err764 := mbTrans763.WriteString(arg762)
		if err764 != nil {
			Usage()
			return
		}
		factory765 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt766 := factory765.GetProtocol(mbTrans763)
		argvalue0 := vico_server.NewRequestHeader()
		err767 := argvalue0.Read(jsProt766)
		if err767 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err768 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err768 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg769 := flag.Arg(3)
		mbTrans770 := thrift.NewTMemoryBufferLen(len(arg769))
		defer mbTrans770.Close()
		_, err771 := mbTrans770.WriteString(arg769)
		if err771 != nil {
			Usage()
			return
		}
		factory772 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt773 := factory772.GetProtocol(mbTrans770)
		containerStruct2 := vico_server.NewDeleteSocialMediaPostByIdsArgs()
		err774 := containerStruct2.ReadField3(jsProt773)
		if err774 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteSocialMediaPostByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getSocialMediaPostsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSocialMediaPostsByIds requires 2 args")
			flag.Usage()
		}
		arg775 := flag.Arg(1)
		mbTrans776 := thrift.NewTMemoryBufferLen(len(arg775))
		defer mbTrans776.Close()
		_, err777 := mbTrans776.WriteString(arg775)
		if err777 != nil {
			Usage()
			return
		}
		factory778 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt779 := factory778.GetProtocol(mbTrans776)
		argvalue0 := vico_server.NewRequestHeader()
		err780 := argvalue0.Read(jsProt779)
		if err780 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg781 := flag.Arg(2)
		mbTrans782 := thrift.NewTMemoryBufferLen(len(arg781))
		defer mbTrans782.Close()
		_, err783 := mbTrans782.WriteString(arg781)
		if err783 != nil {
			Usage()
			return
		}
		factory784 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt785 := factory784.GetProtocol(mbTrans782)
		containerStruct1 := vico_server.NewGetSocialMediaPostsByIdsArgs()
		err786 := containerStruct1.ReadField2(jsProt785)
		if err786 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSocialMediaPostsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "querySocialMediaPostsByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QuerySocialMediaPostsByParams requires 4 args")
			flag.Usage()
		}
		arg787 := flag.Arg(1)
		mbTrans788 := thrift.NewTMemoryBufferLen(len(arg787))
		defer mbTrans788.Close()
		_, err789 := mbTrans788.WriteString(arg787)
		if err789 != nil {
			Usage()
			return
		}
		factory790 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt791 := factory790.GetProtocol(mbTrans788)
		argvalue0 := vico_server.NewRequestHeader()
		err792 := argvalue0.Read(jsProt791)
		if err792 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg793 := flag.Arg(2)
		mbTrans794 := thrift.NewTMemoryBufferLen(len(arg793))
		defer mbTrans794.Close()
		_, err795 := mbTrans794.WriteString(arg793)
		if err795 != nil {
			Usage()
			return
		}
		factory796 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt797 := factory796.GetProtocol(mbTrans794)
		argvalue1 := vico_server.NewSocialMediaPostParams()
		err798 := argvalue1.Read(jsProt797)
		if err798 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err799 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err799 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err800 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err800 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.QuerySocialMediaPostsByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addSocialMediaMaterialLibrary":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSocialMediaMaterialLibrary requires 2 args")
			flag.Usage()
		}
		arg801 := flag.Arg(1)
		mbTrans802 := thrift.NewTMemoryBufferLen(len(arg801))
		defer mbTrans802.Close()
		_, err803 := mbTrans802.WriteString(arg801)
		if err803 != nil {
			Usage()
			return
		}
		factory804 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt805 := factory804.GetProtocol(mbTrans802)
		argvalue0 := vico_server.NewRequestHeader()
		err806 := argvalue0.Read(jsProt805)
		if err806 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg807 := flag.Arg(2)
		mbTrans808 := thrift.NewTMemoryBufferLen(len(arg807))
		defer mbTrans808.Close()
		_, err809 := mbTrans808.WriteString(arg807)
		if err809 != nil {
			Usage()
			return
		}
		factory810 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt811 := factory810.GetProtocol(mbTrans808)
		argvalue1 := vico_server.NewSocialMediaMaterialLibrary()
		err812 := argvalue1.Read(jsProt811)
		if err812 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSocialMediaMaterialLibrary(value0, value1))
		fmt.Print("\n")
		break
	case "editSocialMediaMaterialLibrary":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditSocialMediaMaterialLibrary requires 2 args")
			flag.Usage()
		}
		arg813 := flag.Arg(1)
		mbTrans814 := thrift.NewTMemoryBufferLen(len(arg813))
		defer mbTrans814.Close()
		_, err815 := mbTrans814.WriteString(arg813)
		if err815 != nil {
			Usage()
			return
		}
		factory816 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt817 := factory816.GetProtocol(mbTrans814)
		argvalue0 := vico_server.NewRequestHeader()
		err818 := argvalue0.Read(jsProt817)
		if err818 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg819 := flag.Arg(2)
		mbTrans820 := thrift.NewTMemoryBufferLen(len(arg819))
		defer mbTrans820.Close()
		_, err821 := mbTrans820.WriteString(arg819)
		if err821 != nil {
			Usage()
			return
		}
		factory822 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt823 := factory822.GetProtocol(mbTrans820)
		argvalue1 := vico_server.NewSocialMediaMaterialLibrary()
		err824 := argvalue1.Read(jsProt823)
		if err824 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditSocialMediaMaterialLibrary(value0, value1))
		fmt.Print("\n")
		break
	case "deleteSocialMediaMaterialLibraryByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteSocialMediaMaterialLibraryByIds requires 3 args")
			flag.Usage()
		}
		arg825 := flag.Arg(1)
		mbTrans826 := thrift.NewTMemoryBufferLen(len(arg825))
		defer mbTrans826.Close()
		_, err827 := mbTrans826.WriteString(arg825)
		if err827 != nil {
			Usage()
			return
		}
		factory828 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt829 := factory828.GetProtocol(mbTrans826)
		argvalue0 := vico_server.NewRequestHeader()
		err830 := argvalue0.Read(jsProt829)
		if err830 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err831 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err831 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg832 := flag.Arg(3)
		mbTrans833 := thrift.NewTMemoryBufferLen(len(arg832))
		defer mbTrans833.Close()
		_, err834 := mbTrans833.WriteString(arg832)
		if err834 != nil {
			Usage()
			return
		}
		factory835 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt836 := factory835.GetProtocol(mbTrans833)
		containerStruct2 := vico_server.NewDeleteSocialMediaMaterialLibraryByIdsArgs()
		err837 := containerStruct2.ReadField3(jsProt836)
		if err837 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteSocialMediaMaterialLibraryByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getSocialMediaMaterialLibraryByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSocialMediaMaterialLibraryByIds requires 2 args")
			flag.Usage()
		}
		arg838 := flag.Arg(1)
		mbTrans839 := thrift.NewTMemoryBufferLen(len(arg838))
		defer mbTrans839.Close()
		_, err840 := mbTrans839.WriteString(arg838)
		if err840 != nil {
			Usage()
			return
		}
		factory841 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt842 := factory841.GetProtocol(mbTrans839)
		argvalue0 := vico_server.NewRequestHeader()
		err843 := argvalue0.Read(jsProt842)
		if err843 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg844 := flag.Arg(2)
		mbTrans845 := thrift.NewTMemoryBufferLen(len(arg844))
		defer mbTrans845.Close()
		_, err846 := mbTrans845.WriteString(arg844)
		if err846 != nil {
			Usage()
			return
		}
		factory847 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt848 := factory847.GetProtocol(mbTrans845)
		containerStruct1 := vico_server.NewGetSocialMediaMaterialLibraryByIdsArgs()
		err849 := containerStruct1.ReadField2(jsProt848)
		if err849 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSocialMediaMaterialLibraryByIds(value0, value1))
		fmt.Print("\n")
		break
	case "querySocialMediaPostLibrariesByParams":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QuerySocialMediaPostLibrariesByParams requires 4 args")
			flag.Usage()
		}
		arg850 := flag.Arg(1)
		mbTrans851 := thrift.NewTMemoryBufferLen(len(arg850))
		defer mbTrans851.Close()
		_, err852 := mbTrans851.WriteString(arg850)
		if err852 != nil {
			Usage()
			return
		}
		factory853 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt854 := factory853.GetProtocol(mbTrans851)
		argvalue0 := vico_server.NewRequestHeader()
		err855 := argvalue0.Read(jsProt854)
		if err855 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg856 := flag.Arg(2)
		mbTrans857 := thrift.NewTMemoryBufferLen(len(arg856))
		defer mbTrans857.Close()
		_, err858 := mbTrans857.WriteString(arg856)
		if err858 != nil {
			Usage()
			return
		}
		factory859 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt860 := factory859.GetProtocol(mbTrans857)
		argvalue1 := vico_server.NewSocialMediaPostParams()
		err861 := argvalue1.Read(jsProt860)
		if err861 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2, err862 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err862 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err863 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err863 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.QuerySocialMediaPostLibrariesByParams(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getUgcByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetUgcByIds requires 2 args")
			flag.Usage()
		}
		arg864 := flag.Arg(1)
		mbTrans865 := thrift.NewTMemoryBufferLen(len(arg864))
		defer mbTrans865.Close()
		_, err866 := mbTrans865.WriteString(arg864)
		if err866 != nil {
			Usage()
			return
		}
		factory867 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt868 := factory867.GetProtocol(mbTrans865)
		argvalue0 := vico_server.NewRequestHeader()
		err869 := argvalue0.Read(jsProt868)
		if err869 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg870 := flag.Arg(2)
		mbTrans871 := thrift.NewTMemoryBufferLen(len(arg870))
		defer mbTrans871.Close()
		_, err872 := mbTrans871.WriteString(arg870)
		if err872 != nil {
			Usage()
			return
		}
		factory873 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt874 := factory873.GetProtocol(mbTrans871)
		containerStruct1 := vico_server.NewGetUgcByIdsArgs()
		err875 := containerStruct1.ReadField2(jsProt874)
		if err875 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetUgcByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listRecommendUgcsByProductId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListRecommendUgcsByProductId requires 2 args")
			flag.Usage()
		}
		arg876 := flag.Arg(1)
		mbTrans877 := thrift.NewTMemoryBufferLen(len(arg876))
		defer mbTrans877.Close()
		_, err878 := mbTrans877.WriteString(arg876)
		if err878 != nil {
			Usage()
			return
		}
		factory879 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt880 := factory879.GetProtocol(mbTrans877)
		argvalue0 := vico_server.NewRequestHeader()
		err881 := argvalue0.Read(jsProt880)
		if err881 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err882 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err882 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ListRecommendUgcsByProductId(value0, value1))
		fmt.Print("\n")
		break
	case "listRecommendTextsByProductId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListRecommendTextsByProductId requires 2 args")
			flag.Usage()
		}
		arg883 := flag.Arg(1)
		mbTrans884 := thrift.NewTMemoryBufferLen(len(arg883))
		defer mbTrans884.Close()
		_, err885 := mbTrans884.WriteString(arg883)
		if err885 != nil {
			Usage()
			return
		}
		factory886 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt887 := factory886.GetProtocol(mbTrans884)
		argvalue0 := vico_server.NewRequestHeader()
		err888 := argvalue0.Read(jsProt887)
		if err888 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err889 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err889 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ListRecommendTextsByProductId(value0, value1))
		fmt.Print("\n")
		break
	case "listRecommendCommentsByProductId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListRecommendCommentsByProductId requires 2 args")
			flag.Usage()
		}
		arg890 := flag.Arg(1)
		mbTrans891 := thrift.NewTMemoryBufferLen(len(arg890))
		defer mbTrans891.Close()
		_, err892 := mbTrans891.WriteString(arg890)
		if err892 != nil {
			Usage()
			return
		}
		factory893 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt894 := factory893.GetProtocol(mbTrans891)
		argvalue0 := vico_server.NewRequestHeader()
		err895 := argvalue0.Read(jsProt894)
		if err895 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err896 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err896 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ListRecommendCommentsByProductId(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
