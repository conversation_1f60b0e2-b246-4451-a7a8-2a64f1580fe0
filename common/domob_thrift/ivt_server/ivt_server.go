// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package ivt_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/ivt_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = ivt_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type IvtServer interface { //Intelligent Video Tools Server相关接口定义

	// 更新视频转码状态
	//
	// Parameters:
	//  - Header: 请求消息结构体 *
	//  - Video: 视频素材结构体 *
	UpdateVideoConvertInfo(header *common.RequestHeader, video *ivt_types.Video) (e *IvtServerException, err error)
}

//Intelligent Video Tools Server相关接口定义
type IvtServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewIvtServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *IvtServerClient {
	return &IvtServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewIvtServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *IvtServerClient {
	return &IvtServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 更新视频转码状态
//
// Parameters:
//  - Header: 请求消息结构体 *
//  - Video: 视频素材结构体 *
func (p *IvtServerClient) UpdateVideoConvertInfo(header *common.RequestHeader, video *ivt_types.Video) (e *IvtServerException, err error) {
	if err = p.sendUpdateVideoConvertInfo(header, video); err != nil {
		return
	}
	return p.recvUpdateVideoConvertInfo()
}

func (p *IvtServerClient) sendUpdateVideoConvertInfo(header *common.RequestHeader, video *ivt_types.Video) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateVideoConvertInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewUpdateVideoConvertInfoArgs()
	args0.Header = header
	args0.Video = video
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *IvtServerClient) recvUpdateVideoConvertInfo() (e *IvtServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewUpdateVideoConvertInfoResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result1.E != nil {
		e = result1.E
	}
	return
}

type IvtServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      IvtServer
}

func (p *IvtServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *IvtServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *IvtServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewIvtServerProcessor(handler IvtServer) *IvtServerProcessor {

	self4 := &IvtServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self4.processorMap["updateVideoConvertInfo"] = &ivtServerProcessorUpdateVideoConvertInfo{handler: handler}
	return self4
}

func (p *IvtServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x5.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x5

}

type ivtServerProcessorUpdateVideoConvertInfo struct {
	handler IvtServer
}

func (p *ivtServerProcessorUpdateVideoConvertInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateVideoConvertInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateVideoConvertInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateVideoConvertInfoResult()
	if result.E, err = p.handler.UpdateVideoConvertInfo(args.Header, args.Video); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateVideoConvertInfo: "+err.Error())
		oprot.WriteMessageBegin("updateVideoConvertInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateVideoConvertInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type UpdateVideoConvertInfoArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Video  *ivt_types.Video      `thrift:"video,2" json:"video"`
}

func NewUpdateVideoConvertInfoArgs() *UpdateVideoConvertInfoArgs {
	return &UpdateVideoConvertInfoArgs{}
}

func (p *UpdateVideoConvertInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateVideoConvertInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateVideoConvertInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.Video = ivt_types.NewVideo()
	if err := p.Video.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Video)
	}
	return nil
}

func (p *UpdateVideoConvertInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateVideoConvertInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateVideoConvertInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateVideoConvertInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Video != nil {
		if err := oprot.WriteFieldBegin("video", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:video: %s", p, err)
		}
		if err := p.Video.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Video)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:video: %s", p, err)
		}
	}
	return err
}

func (p *UpdateVideoConvertInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateVideoConvertInfoArgs(%+v)", *p)
}

type UpdateVideoConvertInfoResult struct {
	E *IvtServerException `thrift:"e,1" json:"e"`
}

func NewUpdateVideoConvertInfoResult() *UpdateVideoConvertInfoResult {
	return &UpdateVideoConvertInfoResult{}
}

func (p *UpdateVideoConvertInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateVideoConvertInfoResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewIvtServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *UpdateVideoConvertInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateVideoConvertInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateVideoConvertInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *UpdateVideoConvertInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateVideoConvertInfoResult(%+v)", *p)
}
