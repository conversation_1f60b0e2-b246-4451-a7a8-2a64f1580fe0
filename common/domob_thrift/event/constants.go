// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__

const EVENT_PROTOCOL_VERSION = 1

var EVENT_TYPE_MAP map[EventType][]EventCode
var EVENT_TYPE_NAME_MAP map[string]EventType
var EVENT_CODE_NAME_MAP map[string]EventCode

func init() {
	EVENT_TYPE_MAP = map[EventType][]EventCode{
		0: []EventCode{
			1, 11},
		1: []EventCode{
			101, 102, 107, 108, 109, 110, 111, 112, 113},
		10: []EventCode{
			301, 302, 303, 304, 305, 306, 307},
		11: []EventCode{
			401, 402, 403, 404, 405},
		12: []EventCode{
			501, 502, 503, 504, 505, 506, 507},
		20: []EventCode{
			701, 702, 703, 704, 705, 706, 707, 708, 709, 713, 710, 711, 712, 731, 732, 733, 734, 735, 736, 737, 738, 739, 751, 752, 753, 761, 762, 763, 764},
		2: []EventCode{
			201, 202, 203, 204, 206, 207, 208, 209, 210, 211, 220, 221, 222, 223, 230, 231, 232, 240, 241, 242, 250, 251, 252, 253},
		101: []EventCode{
			10101},
		102: []EventCode{
			10201},
		103: []EventCode{
			10301},
		104: []EventCode{
			10401},
		112: []EventCode{
			11101},
		113: []EventCode{
			11201},
		105: []EventCode{
			10501},
		106: []EventCode{
			10601},
		201: []EventCode{
			20101},
		107: []EventCode{
			10701},
		108: []EventCode{
			10801},
		109: []EventCode{
			10901},
		110: []EventCode{
			11001},
		114: []EventCode{
			765},
		301: []EventCode{
			30101, 30201, 30301, 30401, 30501},
		401: []EventCode{
			40101, 40102, 40103, 40104, 40105, 40106, 40107, 40108, 40109},
		402: []EventCode{
			40201, 40202, 40203, 40204},
		404: []EventCode{
			40401, 40402, 40403, 40404, 40406, 40408},
		403: []EventCode{
			40301, 40302},
		405: []EventCode{
			40501, 40502},
		406: []EventCode{
			40601, 40602},
		499: []EventCode{
			49901, 49902},
		407: []EventCode{
			49903},
		50: []EventCode{
			50101},
		501: []EventCode{
			50102},
		600: []EventCode{
			60001},
		650: []EventCode{
			65000},
		700: []EventCode{
			70001},
		701: []EventCode{
			70100},
		801: []EventCode{
			80101, 80102},
		802: []EventCode{
			80201, 80202},
		803: []EventCode{
			80301, 80302},
		804: []EventCode{
			80401, 80402},
		805: []EventCode{
			80501, 80502},
		900: []EventCode{
			90001},
		901: []EventCode{
			90101},
		1001: []EventCode{
			100101},
		1101: []EventCode{
			110001},
		1201: []EventCode{
			120001},
		1301: []EventCode{
			130001},
		1300: []EventCode{
			140001},
		13001: []EventCode{
			145001},
	}

	EVENT_TYPE_NAME_MAP = map[string]EventType{
		"int":                  0,
		"user":                 1,
		"finance":              2,
		"adplan":               10,
		"adstrategy":           11,
		"adcreative":           12,
		"media":                20,
		"cpci":                 101,
		"cpcc":                 102,
		"cpmi":                 103,
		"cpmc":                 104,
		"cpdc":                 112,
		"cpic":                 113,
		"clkfinish":            114,
		"antii":                105,
		"antic":                106,
		"antidl":               107,
		"antiis":               108,
		"settledl":             109,
		"settleis":             110,
		"owa":                  201,
		"app":                  301,
		"rtbcampaign":          401,
		"rtbcreative":          402,
		"rtbstrategy":          404,
		"rtbpromotion":         403,
		"rtbsponsor":           405,
		"rtbadtracking":        406,
		"rtbadexport":          499,
		"rtbadexportcmd":       407,
		"orderschedules":       50,
		"project":              501,
		"dbm":                  600,
		"dbmoperation":         650,
		"dmp":                  700,
		"dmpoperation":         701,
		"dpmpromotion":         801,
		"dpmpromotionproperty": 802,
		"dpmchannel":           803,
		"dpmtracking":          804,
		"dpmcompany":           805,
		"dos":                  900,
		"dosstats":             901,
		"dspstats":             1001,
		"vicocommon":           1101,
		"datapluscommon":       1201,
		"compasscommon":        1301,
		"bidmaster":            1300,
		"bidmasteroperation":   13001,
	}

	EVENT_CODE_NAME_MAP = map[string]EventCode{
		"int.check":                   1,
		"int.fileready":               11,
		"user.reg":                    101,
		"user.active":                 102,
		"user.ban":                    107,
		"user.unban":                  108,
		"user.addader":                109,
		"user.adddever":               110,
		"user.profile":                111,
		"user.becometx":               112,
		"user.changeemail":            113,
		"finance.update":              201,
		"finance.nob":                 202,
		"finance.bok":                 203,
		"finance.recharged":           204,
		"finance.lowb":                206,
		"finance.dbover":              207,
		"finance.tbover":              208,
		"finance.dbok":                209,
		"finance.tbok":                210,
		"finance.bpo":                 211,
		"finance.wdreq":               220,
		"finance.wdrej":               221,
		"finance.wdpass":              222,
		"finance.wdcomp":              223,
		"finance.mireq":               230,
		"finance.mirej":               231,
		"finance.micon":               232,
		"finance.idpass":              240,
		"finance.idreject":            241,
		"finance.idforbid":            242,
		"finance.payreq":              250,
		"finance.payrej":              251,
		"finance.paypass":             252,
		"finance.paycomp":             253,
		"adplan.add":                  301,
		"adplan.modify":               302,
		"adplan.delete":               303,
		"adplan.pause":                304,
		"adplan.resume":               305,
		"adplan.syspause":             306,
		"adplan.sysresume":            307,
		"adstrategy.add":              401,
		"adstrategy.modify":           402,
		"adstrategy.delete":           403,
		"adstrategy.pause":            404,
		"adstrategy.resume":           405,
		"adcreative.submit":           501,
		"adcreative.delete":           502,
		"adcreative.pause":            503,
		"adcreative.resume":           504,
		"adcreative.approve":          505,
		"adcreative.reject":           506,
		"adcreative.modify":           507,
		"media.submit":                701,
		"media.delele":                702,
		"media.pause":                 703,
		"media.resume":                704,
		"media.approve":               705,
		"media.reject":                706,
		"media.setting":               707,
		"media.update":                708,
		"media.upload":                709,
		"media.reupload":              713,
		"media.revoke":                710,
		"media.forbid":                711,
		"media.unforbid":              712,
		"media.appupsubmit":           731,
		"media.appupdelete":           732,
		"media.appuppause":            733,
		"media.appupresume":           734,
		"media.appupapprove":          735,
		"media.appupreject":           736,
		"media.appupforbid":           737,
		"media.appuprevoke":           738,
		"media.appupunforbid":         739,
		"media.ratepause":             751,
		"media.rateresume":            752,
		"media.rateupdate":            753,
		"media.pmtupdate":             761,
		"media.pmtdelete":             762,
		"media.pmtpause":              763,
		"media.pmtresume":             764,
		"cpci.impression":             10101,
		"cpcc.click":                  10201,
		"cpmi.impression":             10301,
		"cpmc.click":                  10401,
		"antii.impression":            10501,
		"antic.click":                 10601,
		"antidl.download":             10701,
		"antiis.install":              10801,
		"settledl.download":           10901,
		"settleis.install":            11001,
		"cpdc.download":               11101,
		"cpic.install":                11201,
		"owa.act":                     20101,
		"app.add":                     30101,
		"app.update":                  30201,
		"app.channel.add":             30301,
		"app.channel.update":          30401,
		"app.channel.delete":          30501,
		"rtbcampaign.update":          40101,
		"rtbcampaign.pause":           40102,
		"rtbcampaign.resume":          40103,
		"rtbcampaign.delete":          40104,
		"rtbcampaign.tbover":          40105,
		"rtbcampaign.dbover":          40106,
		"rtbcampaign.tbok":            40107,
		"rtbcampaign.dbok":            40108,
		"rtbcampaign.refresh":         40109,
		"rtbcreative.update":          40201,
		"rtbcreative.pause":           40202,
		"rtbcreative.resume":          40203,
		"rtbcreative.delete":          40204,
		"rtbstrategy.update":          40401,
		"rtbstrategy.pause":           40402,
		"rtbstrategy.resume":          40403,
		"rtbstrategy.delete":          40404,
		"rtbstrategy.dbover":          40406,
		"rtbstrategy.dbok":            40408,
		"rtbpromotion.update":         40301,
		"rtbpromotion.delete":         40302,
		"rtbsponsor.update":           40501,
		"rtbsponsor.delete":           40502,
		"rtbadtracking.update":        40601,
		"rtbadtracking.delete":        40602,
		"rtbadexport.adexp":           49901,
		"rtbadexport.adexpcmd":        49903,
		"rtbadexport.dumpflush":       49902,
		"orderschedules.update":       50101,
		"project.update":              50102,
		"dbm.common":                  60001,
		"dbm.operation":               65000,
		"dmp.common":                  70001,
		"dmp.operation":               70100,
		"dpmpromotion.update":         80101,
		"dpmpromotion.add":            80102,
		"dpmpromotionproperty.update": 80201,
		"dpmpromotionproperty.add":    80202,
		"dpmchannel.update":           80301,
		"dpmchannel.add":              80302,
		"dpmtracking.update":          80401,
		"dpmtracking.add":             80402,
		"dpmcompany.update":           80501,
		"dpmcompany.add":              80502,
		"dos.common":                  90001,
		"dosstats.update":             90101,
		"dspstats.update":             100101,
		"vico.common":                 110001,
		"dataplus.common":             120001,
		"compass.common":              130001,
		"bidmaster.common":            140001,
		"bidmaster.operation":         145001,
	}

}
