// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package event

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//系统事件所属的类型，如广告创意更新，或者财务信息更新，都是一个类型
type EventType int64

const (
	EventType_ET_INTERNAL                 EventType = 0
	EventType_ET_USER                     EventType = 1
	EventType_ET_FINANCE                  EventType = 2
	EventType_ET_AD_PLAN                  EventType = 10
	EventType_ET_AD_STRATEGY              EventType = 11
	EventType_ET_AD_CREATIVE              EventType = 12
	EventType_ET_MEDIA                    EventType = 20
	EventType_ET_CPC_IMPRESSION           EventType = 101
	EventType_ET_CPC_CLICK                EventType = 102
	EventType_ET_CPM_IMPRESSION           EventType = 103
	EventType_ET_CPM_CLICK                EventType = 104
	EventType_ET_ANTI_IMPRESSION          EventType = 105
	EventType_ET_ANTI_CLICK               EventType = 106
	EventType_ET_ANTI_DOWNLOAD            EventType = 107
	EventType_ET_ANTI_INSTALL             EventType = 108
	EventType_ET_SETTLE_DOWNLOAD          EventType = 109
	EventType_ET_SETTLE_INSTALL           EventType = 110
	EventType_ET_CPD_CLICK                EventType = 112
	EventType_ET_CPI_CLICK                EventType = 113
	EventType_ET_CLICK_FINISH             EventType = 114
	EventType_ET_OW_ACT                   EventType = 201
	EventType_ET_APP                      EventType = 301
	EventType_ET_RTB_CAMPAIGN             EventType = 401
	EventType_ET_RTB_CREATIVE             EventType = 402
	EventType_ET_RTB_PROMOTION            EventType = 403
	EventType_ET_RTB_STRATEGY             EventType = 404
	EventType_ET_RTB_SPONSOR              EventType = 405
	EventType_ET_RTB_ADTRACKING           EventType = 406
	EventType_ET_RTB_SCHEDULED_EXPORT_CMD EventType = 407
	EventType_ET_RTB_AD_EXPORT            EventType = 499
	EventType_ET_ORDER                    EventType = 50
	EventType_ET_PROJECT                  EventType = 501
	EventType_ET_DBM_COMMON               EventType = 600
	EventType_ET_DBM_OPERATION            EventType = 650
	EventType_ET_DMP_COMMON               EventType = 700
	EventType_ET_DMP_OPERATION            EventType = 701
	EventType_ET_DPM_PROMOTION            EventType = 801
	EventType_ET_DPM_PROMOTION_PROPERTY   EventType = 802
	EventType_ET_DPM_CHANNEL              EventType = 803
	EventType_ET_DPM_TRACKING             EventType = 804
	EventType_ET_DPM_COMPANY              EventType = 805
	EventType_ET_DOS_COMMON               EventType = 900
	EventType_ET_DOS_STATS                EventType = 901
	EventType_ET_DSP_STATS                EventType = 1001
	EventType_ET_VICO_COMMON              EventType = 1101
	EventType_ET_DATA_PLUS_COMMON         EventType = 1201
	EventType_ET_COMPASS_COMMON           EventType = 1301
	EventType_ET_BIDMASTER_COMMON         EventType = 1300
	EventType_ET_BIDMASTER_OPERATION      EventType = 13001
)

func (p EventType) String() string {
	switch p {
	case EventType_ET_INTERNAL:
		return "EventType_ET_INTERNAL"
	case EventType_ET_USER:
		return "EventType_ET_USER"
	case EventType_ET_FINANCE:
		return "EventType_ET_FINANCE"
	case EventType_ET_AD_PLAN:
		return "EventType_ET_AD_PLAN"
	case EventType_ET_AD_STRATEGY:
		return "EventType_ET_AD_STRATEGY"
	case EventType_ET_AD_CREATIVE:
		return "EventType_ET_AD_CREATIVE"
	case EventType_ET_MEDIA:
		return "EventType_ET_MEDIA"
	case EventType_ET_CPC_IMPRESSION:
		return "EventType_ET_CPC_IMPRESSION"
	case EventType_ET_CPC_CLICK:
		return "EventType_ET_CPC_CLICK"
	case EventType_ET_CPM_IMPRESSION:
		return "EventType_ET_CPM_IMPRESSION"
	case EventType_ET_CPM_CLICK:
		return "EventType_ET_CPM_CLICK"
	case EventType_ET_ANTI_IMPRESSION:
		return "EventType_ET_ANTI_IMPRESSION"
	case EventType_ET_ANTI_CLICK:
		return "EventType_ET_ANTI_CLICK"
	case EventType_ET_ANTI_DOWNLOAD:
		return "EventType_ET_ANTI_DOWNLOAD"
	case EventType_ET_ANTI_INSTALL:
		return "EventType_ET_ANTI_INSTALL"
	case EventType_ET_SETTLE_DOWNLOAD:
		return "EventType_ET_SETTLE_DOWNLOAD"
	case EventType_ET_SETTLE_INSTALL:
		return "EventType_ET_SETTLE_INSTALL"
	case EventType_ET_CPD_CLICK:
		return "EventType_ET_CPD_CLICK"
	case EventType_ET_CPI_CLICK:
		return "EventType_ET_CPI_CLICK"
	case EventType_ET_CLICK_FINISH:
		return "EventType_ET_CLICK_FINISH"
	case EventType_ET_OW_ACT:
		return "EventType_ET_OW_ACT"
	case EventType_ET_APP:
		return "EventType_ET_APP"
	case EventType_ET_RTB_CAMPAIGN:
		return "EventType_ET_RTB_CAMPAIGN"
	case EventType_ET_RTB_CREATIVE:
		return "EventType_ET_RTB_CREATIVE"
	case EventType_ET_RTB_PROMOTION:
		return "EventType_ET_RTB_PROMOTION"
	case EventType_ET_RTB_STRATEGY:
		return "EventType_ET_RTB_STRATEGY"
	case EventType_ET_RTB_SPONSOR:
		return "EventType_ET_RTB_SPONSOR"
	case EventType_ET_RTB_ADTRACKING:
		return "EventType_ET_RTB_ADTRACKING"
	case EventType_ET_RTB_SCHEDULED_EXPORT_CMD:
		return "EventType_ET_RTB_SCHEDULED_EXPORT_CMD"
	case EventType_ET_RTB_AD_EXPORT:
		return "EventType_ET_RTB_AD_EXPORT"
	case EventType_ET_ORDER:
		return "EventType_ET_ORDER"
	case EventType_ET_PROJECT:
		return "EventType_ET_PROJECT"
	case EventType_ET_DBM_COMMON:
		return "EventType_ET_DBM_COMMON"
	case EventType_ET_DBM_OPERATION:
		return "EventType_ET_DBM_OPERATION"
	case EventType_ET_DMP_COMMON:
		return "EventType_ET_DMP_COMMON"
	case EventType_ET_DMP_OPERATION:
		return "EventType_ET_DMP_OPERATION"
	case EventType_ET_DPM_PROMOTION:
		return "EventType_ET_DPM_PROMOTION"
	case EventType_ET_DPM_PROMOTION_PROPERTY:
		return "EventType_ET_DPM_PROMOTION_PROPERTY"
	case EventType_ET_DPM_CHANNEL:
		return "EventType_ET_DPM_CHANNEL"
	case EventType_ET_DPM_TRACKING:
		return "EventType_ET_DPM_TRACKING"
	case EventType_ET_DPM_COMPANY:
		return "EventType_ET_DPM_COMPANY"
	case EventType_ET_DOS_COMMON:
		return "EventType_ET_DOS_COMMON"
	case EventType_ET_DOS_STATS:
		return "EventType_ET_DOS_STATS"
	case EventType_ET_DSP_STATS:
		return "EventType_ET_DSP_STATS"
	case EventType_ET_VICO_COMMON:
		return "EventType_ET_VICO_COMMON"
	case EventType_ET_DATA_PLUS_COMMON:
		return "EventType_ET_DATA_PLUS_COMMON"
	case EventType_ET_COMPASS_COMMON:
		return "EventType_ET_COMPASS_COMMON"
	case EventType_ET_BIDMASTER_COMMON:
		return "EventType_ET_BIDMASTER_COMMON"
	case EventType_ET_BIDMASTER_OPERATION:
		return "EventType_ET_BIDMASTER_OPERATION"
	}
	return "<UNSET>"
}

func EventTypeFromString(s string) (EventType, error) {
	switch s {
	case "EventType_ET_INTERNAL":
		return EventType_ET_INTERNAL, nil
	case "EventType_ET_USER":
		return EventType_ET_USER, nil
	case "EventType_ET_FINANCE":
		return EventType_ET_FINANCE, nil
	case "EventType_ET_AD_PLAN":
		return EventType_ET_AD_PLAN, nil
	case "EventType_ET_AD_STRATEGY":
		return EventType_ET_AD_STRATEGY, nil
	case "EventType_ET_AD_CREATIVE":
		return EventType_ET_AD_CREATIVE, nil
	case "EventType_ET_MEDIA":
		return EventType_ET_MEDIA, nil
	case "EventType_ET_CPC_IMPRESSION":
		return EventType_ET_CPC_IMPRESSION, nil
	case "EventType_ET_CPC_CLICK":
		return EventType_ET_CPC_CLICK, nil
	case "EventType_ET_CPM_IMPRESSION":
		return EventType_ET_CPM_IMPRESSION, nil
	case "EventType_ET_CPM_CLICK":
		return EventType_ET_CPM_CLICK, nil
	case "EventType_ET_ANTI_IMPRESSION":
		return EventType_ET_ANTI_IMPRESSION, nil
	case "EventType_ET_ANTI_CLICK":
		return EventType_ET_ANTI_CLICK, nil
	case "EventType_ET_ANTI_DOWNLOAD":
		return EventType_ET_ANTI_DOWNLOAD, nil
	case "EventType_ET_ANTI_INSTALL":
		return EventType_ET_ANTI_INSTALL, nil
	case "EventType_ET_SETTLE_DOWNLOAD":
		return EventType_ET_SETTLE_DOWNLOAD, nil
	case "EventType_ET_SETTLE_INSTALL":
		return EventType_ET_SETTLE_INSTALL, nil
	case "EventType_ET_CPD_CLICK":
		return EventType_ET_CPD_CLICK, nil
	case "EventType_ET_CPI_CLICK":
		return EventType_ET_CPI_CLICK, nil
	case "EventType_ET_CLICK_FINISH":
		return EventType_ET_CLICK_FINISH, nil
	case "EventType_ET_OW_ACT":
		return EventType_ET_OW_ACT, nil
	case "EventType_ET_APP":
		return EventType_ET_APP, nil
	case "EventType_ET_RTB_CAMPAIGN":
		return EventType_ET_RTB_CAMPAIGN, nil
	case "EventType_ET_RTB_CREATIVE":
		return EventType_ET_RTB_CREATIVE, nil
	case "EventType_ET_RTB_PROMOTION":
		return EventType_ET_RTB_PROMOTION, nil
	case "EventType_ET_RTB_STRATEGY":
		return EventType_ET_RTB_STRATEGY, nil
	case "EventType_ET_RTB_SPONSOR":
		return EventType_ET_RTB_SPONSOR, nil
	case "EventType_ET_RTB_ADTRACKING":
		return EventType_ET_RTB_ADTRACKING, nil
	case "EventType_ET_RTB_SCHEDULED_EXPORT_CMD":
		return EventType_ET_RTB_SCHEDULED_EXPORT_CMD, nil
	case "EventType_ET_RTB_AD_EXPORT":
		return EventType_ET_RTB_AD_EXPORT, nil
	case "EventType_ET_ORDER":
		return EventType_ET_ORDER, nil
	case "EventType_ET_PROJECT":
		return EventType_ET_PROJECT, nil
	case "EventType_ET_DBM_COMMON":
		return EventType_ET_DBM_COMMON, nil
	case "EventType_ET_DBM_OPERATION":
		return EventType_ET_DBM_OPERATION, nil
	case "EventType_ET_DMP_COMMON":
		return EventType_ET_DMP_COMMON, nil
	case "EventType_ET_DMP_OPERATION":
		return EventType_ET_DMP_OPERATION, nil
	case "EventType_ET_DPM_PROMOTION":
		return EventType_ET_DPM_PROMOTION, nil
	case "EventType_ET_DPM_PROMOTION_PROPERTY":
		return EventType_ET_DPM_PROMOTION_PROPERTY, nil
	case "EventType_ET_DPM_CHANNEL":
		return EventType_ET_DPM_CHANNEL, nil
	case "EventType_ET_DPM_TRACKING":
		return EventType_ET_DPM_TRACKING, nil
	case "EventType_ET_DPM_COMPANY":
		return EventType_ET_DPM_COMPANY, nil
	case "EventType_ET_DOS_COMMON":
		return EventType_ET_DOS_COMMON, nil
	case "EventType_ET_DOS_STATS":
		return EventType_ET_DOS_STATS, nil
	case "EventType_ET_DSP_STATS":
		return EventType_ET_DSP_STATS, nil
	case "EventType_ET_VICO_COMMON":
		return EventType_ET_VICO_COMMON, nil
	case "EventType_ET_DATA_PLUS_COMMON":
		return EventType_ET_DATA_PLUS_COMMON, nil
	case "EventType_ET_COMPASS_COMMON":
		return EventType_ET_COMPASS_COMMON, nil
	case "EventType_ET_BIDMASTER_COMMON":
		return EventType_ET_BIDMASTER_COMMON, nil
	case "EventType_ET_BIDMASTER_OPERATION":
		return EventType_ET_BIDMASTER_OPERATION, nil
	}
	return EventType(math.MinInt32 - 1), fmt.Errorf("not a valid EventType string")
}

//具体的事件代码，同一性质事件对应一个独立的事件代码，如广告审核通过
type EventCode int64

const (
	EventCode_EC_INTERNAL_CHECK                  EventCode = 1
	EventCode_EC_INTERNAL_FILE_READY             EventCode = 11
	EventCode_EC_USER_REGISTER                   EventCode = 101
	EventCode_EC_USER_ACTIVATE                   EventCode = 102
	EventCode_EC_USER_BANNED                     EventCode = 107
	EventCode_EC_USER_UNBANNED                   EventCode = 108
	EventCode_EC_USER_ADD_ADER_ROLE              EventCode = 109
	EventCode_EC_USER_ADD_DEVER_ROLE             EventCode = 110
	EventCode_EC_USER_PROFILE_CHANGED            EventCode = 111
	EventCode_EC_USER_BECOME_TRAFFIC_EXCHANGE    EventCode = 112
	EventCode_EC_USER_CHANGE_EMAIL               EventCode = 113
	EventCode_EC_FINANCE_UPDATE                  EventCode = 201
	EventCode_EC_FINANCE_NO_BALANCE              EventCode = 202
	EventCode_EC_FINANCE_BALANCE_OK              EventCode = 203
	EventCode_EC_FINANCE_RECHARGED               EventCode = 204
	EventCode_EC_FINANCE_LOW_BALANCE             EventCode = 206
	EventCode_EC_FINANCE_DAILY_BUDGET_OVER       EventCode = 207
	EventCode_EC_FINANCE_TOTAL_BUDGET_OVER       EventCode = 208
	EventCode_EC_FINANCE_DAILY_BUDGET_OK         EventCode = 209
	EventCode_EC_FINANCE_TOTAL_BUDGET_OK         EventCode = 210
	EventCode_EC_FINANCE_BALANCE_PRE_OVER        EventCode = 211
	EventCode_EC_FINANCE_WITHDRAW_REQUEST        EventCode = 220
	EventCode_EC_FINANCE_WITHDRAW_REJECTED       EventCode = 221
	EventCode_EC_FINANCE_WITHDRAW_PASS           EventCode = 222
	EventCode_EC_FINANCE_WITHDRAW_COMPLETED      EventCode = 223
	EventCode_EC_FINANCE_MEDIA_INVOICE_REQUEST   EventCode = 230
	EventCode_EC_FINANCE_MEDIA_INVOICE_REJECTED  EventCode = 231
	EventCode_EC_FINANCE_MEDIA_INVOICE_CONFIRMED EventCode = 232
	EventCode_EC_FINANCE_IDENTITY_PASSED         EventCode = 240
	EventCode_EC_FINANCE_IDENTITY_REJECTED       EventCode = 241
	EventCode_EC_FINANCE_IDENTITY_FORBIDDEN      EventCode = 242
	EventCode_EC_FINANCE_PAYMENT_REQUEST         EventCode = 250
	EventCode_EC_FINANCE_PAYMENT_REJECTED        EventCode = 251
	EventCode_EC_FINANCE_PAYMENT_PASS            EventCode = 252
	EventCode_EC_FINANCE_PAYMENT_COMPLETED       EventCode = 253
	EventCode_EC_AD_PLAN_ADD                     EventCode = 301
	EventCode_EC_AD_PLAN_MODIFY                  EventCode = 302
	EventCode_EC_AD_PLAN_DELETE                  EventCode = 303
	EventCode_EC_AD_PLAN_PAUSE                   EventCode = 304
	EventCode_EC_AD_PLAN_RESUME                  EventCode = 305
	EventCode_EC_AD_PLAN_SYS_PAUSE               EventCode = 306
	EventCode_EC_AD_PLAN_SYS_RESUME              EventCode = 307
	EventCode_EC_AD_STRATEGY_ADD                 EventCode = 401
	EventCode_EC_AD_STRATEGY_MODIFY              EventCode = 402
	EventCode_EC_AD_STRATEGY_DELETE              EventCode = 403
	EventCode_EC_AD_STRATEGY_PAUSE               EventCode = 404
	EventCode_EC_AD_STRATEGY_RESUME              EventCode = 405
	EventCode_EC_AD_CREATIVE_SUBMIT              EventCode = 501
	EventCode_EC_AD_CREATIVE_DELETE              EventCode = 502
	EventCode_EC_AD_CREATIVE_PAUSE               EventCode = 503
	EventCode_EC_AD_CREATIVE_RESUME              EventCode = 504
	EventCode_EC_AD_CREATIVE_APPROVE             EventCode = 505
	EventCode_EC_AD_CREATIVE_REJECT              EventCode = 506
	EventCode_EC_AD_CREATIVE_MODIFY              EventCode = 507
	EventCode_EC_MEDIA_SUBMIT                    EventCode = 701
	EventCode_EC_MEDIA_DELETE                    EventCode = 702
	EventCode_EC_MEDIA_PAUSE                     EventCode = 703
	EventCode_EC_MEDIA_RESUME                    EventCode = 704
	EventCode_EC_MEDIA_APPROVE                   EventCode = 705
	EventCode_EC_MEDIA_REJECT                    EventCode = 706
	EventCode_EC_MEDIA_SETTING                   EventCode = 707
	EventCode_EC_MEDIA_UPDATE                    EventCode = 708
	EventCode_EC_MEDIA_UPLOAD                    EventCode = 709
	EventCode_EC_MEDIA_REVOKE                    EventCode = 710
	EventCode_EC_MEDIA_FORBIDDEN                 EventCode = 711
	EventCode_EC_MEDIA_UNFORBIDDEN               EventCode = 712
	EventCode_EC_MEDIA_REUPLOAD                  EventCode = 713
	EventCode_EC_MEDIA_APP_UPDATE_SUBMIT         EventCode = 731
	EventCode_EC_MEDIA_APP_UPDATE_DELETE         EventCode = 732
	EventCode_EC_MEDIA_APP_UPDATE_PAUSE          EventCode = 733
	EventCode_EC_MEDIA_APP_UPDATE_RESUME         EventCode = 734
	EventCode_EC_MEDIA_APP_UPDATE_APPROVE        EventCode = 735
	EventCode_EC_MEDIA_APP_UPDATE_REJECT         EventCode = 736
	EventCode_EC_MEDIA_APP_UPDATE_FORBIDDEN      EventCode = 737
	EventCode_EC_MEDIA_APP_UPDATE_REVOKE         EventCode = 738
	EventCode_EC_MEDIA_APP_UPDATE_UNFORBIDDEN    EventCode = 739
	EventCode_EC_MEDIA_APP_RATE_PAUSE            EventCode = 751
	EventCode_EC_MEDIA_APP_RATE_RESUME           EventCode = 752
	EventCode_EC_MEDIA_APP_RATE_UPDATE           EventCode = 753
	EventCode_EC_MEDIA_PLACEMENT_UPDATE          EventCode = 761
	EventCode_EC_MEDIA_PLACEMENT_DELETE          EventCode = 762
	EventCode_EC_MEDIA_PLACEMENT_PAUSE           EventCode = 763
	EventCode_EC_MEDIA_PLACEMENT_RESUME          EventCode = 764
	EventCode_EC_CLICK_FINISH                    EventCode = 765
	EventCode_EC_CPC_IMPRESSION                  EventCode = 10101
	EventCode_EC_CPC_CLICK                       EventCode = 10201
	EventCode_EC_CPM_IMPRESSION                  EventCode = 10301
	EventCode_EC_CPM_CLICK                       EventCode = 10401
	EventCode_EC_ANTI_IMPRESSION                 EventCode = 10501
	EventCode_EC_ANTI_CLICK                      EventCode = 10601
	EventCode_EC_ANTI_DOWNLOAD                   EventCode = 10701
	EventCode_EC_ANTI_INSTALL                    EventCode = 10801
	EventCode_EC_SETTLE_DOWNLOAD                 EventCode = 10901
	EventCode_EC_SETTLE_INSTALL                  EventCode = 11001
	EventCode_EC_CPD_CLICK                       EventCode = 11101
	EventCode_EC_CPI_CLICK                       EventCode = 11201
	EventCode_EC_OW_ACT                          EventCode = 20101
	EventCode_EC_APP_ADD                         EventCode = 30101
	EventCode_EC_APP_UPDATE                      EventCode = 30201
	EventCode_EC_APP_CHANNEL_ADD                 EventCode = 30301
	EventCode_EC_APP_CHANNEL_UPDATE              EventCode = 30401
	EventCode_EC_APP_CHANNEL_DELETE              EventCode = 30501
	EventCode_EC_RTB_CAMPAIGN_UPDATE             EventCode = 40101
	EventCode_EC_RTB_CAMPAIGN_PAUSE              EventCode = 40102
	EventCode_EC_RTB_CAMPAIGN_RESUME             EventCode = 40103
	EventCode_EC_RTB_CAMPAIGN_DELETE             EventCode = 40104
	EventCode_EC_RTB_CAMPAIGN_TB_OVER            EventCode = 40105
	EventCode_EC_RTB_CAMPAIGN_DB_OVER            EventCode = 40106
	EventCode_EC_RTB_CAMPAIGN_TB_OK              EventCode = 40107
	EventCode_EC_RTB_CAMPAIGN_DB_OK              EventCode = 40108
	EventCode_EC_RTB_CAMPAIGN_REFRESH            EventCode = 40109
	EventCode_EC_RTB_CREATIVE_UPDATE             EventCode = 40201
	EventCode_EC_RTB_CREATIVE_PAUSE              EventCode = 40202
	EventCode_EC_RTB_CREATIVE_RESUME             EventCode = 40203
	EventCode_EC_RTB_CREATIVE_DELETE             EventCode = 40204
	EventCode_EC_RTB_PROMOTION_UPDATE            EventCode = 40301
	EventCode_EC_RTB_PROMOTION_DELETE            EventCode = 40302
	EventCode_EC_RTB_STRATEGY_UPDATE             EventCode = 40401
	EventCode_EC_RTB_STRATEGY_PAUSE              EventCode = 40402
	EventCode_EC_RTB_STRATEGY_RESUME             EventCode = 40403
	EventCode_EC_RTB_STRATEGY_DELETE             EventCode = 40404
	EventCode_EC_RTB_STRATEGY_DB_OVER            EventCode = 40406
	EventCode_EC_RTB_STRATEGY_DB_OK              EventCode = 40408
	EventCode_EC_RTB_SPONSOR_UPDATE              EventCode = 40501
	EventCode_EC_RTB_SPONSOR_DELETE              EventCode = 40502
	EventCode_EC_RTB_ADTRACKING_UPDATE           EventCode = 40601
	EventCode_EC_RTB_ADTRACKING_DELETE           EventCode = 40602
	EventCode_EC_RTB_AD_EXPORT                   EventCode = 49901
	EventCode_EC_RTB_AD_DUMP_FLUSH               EventCode = 49902
	EventCode_EC_RTB_AD_SCHEDULED_EXPORT_CMD     EventCode = 49903
	EventCode_EC_ORDER_SCHEDULES_UPDATE          EventCode = 50101
	EventCode_EC_PROJECT_UPDATE                  EventCode = 50102
	EventCode_EC_DBM_COMMON                      EventCode = 60001
	EventCode_EC_DBM_OPERATION                   EventCode = 65000
	EventCode_EC_DMP_COMMON                      EventCode = 70001
	EventCode_EC_DMP_OPERATION                   EventCode = 70100
	EventCode_EC_DPM_PROMOTION_UPDATE            EventCode = 80101
	EventCode_EC_DPM_PROMOTION_ADD               EventCode = 80102
	EventCode_EC_DPM_PROMOTION_PROPERTY_UPDATE   EventCode = 80201
	EventCode_EC_DPM_PROMOTION_PROPERTY_ADD      EventCode = 80202
	EventCode_EC_DPM_CHANNEL_UPDATE              EventCode = 80301
	EventCode_EC_DPM_CHANNEL_ADD                 EventCode = 80302
	EventCode_EC_DPM_TRACKING_UPDATE             EventCode = 80401
	EventCode_EC_DPM_TRACKING_ADD                EventCode = 80402
	EventCode_EC_DPM_COMPANY_UPDATE              EventCode = 80501
	EventCode_EC_DPM_COMPANY_ADD                 EventCode = 80502
	EventCode_EC_DOS_COMMON                      EventCode = 90001
	EventCode_EC_DOS_STATS_UPDATE                EventCode = 90101
	EventCode_EC_DSP_STATS_UPDATE                EventCode = 100101
	EventCode_EC_VICO_COMMON                     EventCode = 110001
	EventCode_EC_DATA_PLUS_COMMON                EventCode = 120001
	EventCode_EC_COMPASS_COMMON                  EventCode = 130001
	EventCode_EC_BIDMASTER_COMMON                EventCode = 140001
	EventCode_EC_BIDMASTER_OPERATION             EventCode = 145001
)

func (p EventCode) String() string {
	switch p {
	case EventCode_EC_INTERNAL_CHECK:
		return "EventCode_EC_INTERNAL_CHECK"
	case EventCode_EC_INTERNAL_FILE_READY:
		return "EventCode_EC_INTERNAL_FILE_READY"
	case EventCode_EC_USER_REGISTER:
		return "EventCode_EC_USER_REGISTER"
	case EventCode_EC_USER_ACTIVATE:
		return "EventCode_EC_USER_ACTIVATE"
	case EventCode_EC_USER_BANNED:
		return "EventCode_EC_USER_BANNED"
	case EventCode_EC_USER_UNBANNED:
		return "EventCode_EC_USER_UNBANNED"
	case EventCode_EC_USER_ADD_ADER_ROLE:
		return "EventCode_EC_USER_ADD_ADER_ROLE"
	case EventCode_EC_USER_ADD_DEVER_ROLE:
		return "EventCode_EC_USER_ADD_DEVER_ROLE"
	case EventCode_EC_USER_PROFILE_CHANGED:
		return "EventCode_EC_USER_PROFILE_CHANGED"
	case EventCode_EC_USER_BECOME_TRAFFIC_EXCHANGE:
		return "EventCode_EC_USER_BECOME_TRAFFIC_EXCHANGE"
	case EventCode_EC_USER_CHANGE_EMAIL:
		return "EventCode_EC_USER_CHANGE_EMAIL"
	case EventCode_EC_FINANCE_UPDATE:
		return "EventCode_EC_FINANCE_UPDATE"
	case EventCode_EC_FINANCE_NO_BALANCE:
		return "EventCode_EC_FINANCE_NO_BALANCE"
	case EventCode_EC_FINANCE_BALANCE_OK:
		return "EventCode_EC_FINANCE_BALANCE_OK"
	case EventCode_EC_FINANCE_RECHARGED:
		return "EventCode_EC_FINANCE_RECHARGED"
	case EventCode_EC_FINANCE_LOW_BALANCE:
		return "EventCode_EC_FINANCE_LOW_BALANCE"
	case EventCode_EC_FINANCE_DAILY_BUDGET_OVER:
		return "EventCode_EC_FINANCE_DAILY_BUDGET_OVER"
	case EventCode_EC_FINANCE_TOTAL_BUDGET_OVER:
		return "EventCode_EC_FINANCE_TOTAL_BUDGET_OVER"
	case EventCode_EC_FINANCE_DAILY_BUDGET_OK:
		return "EventCode_EC_FINANCE_DAILY_BUDGET_OK"
	case EventCode_EC_FINANCE_TOTAL_BUDGET_OK:
		return "EventCode_EC_FINANCE_TOTAL_BUDGET_OK"
	case EventCode_EC_FINANCE_BALANCE_PRE_OVER:
		return "EventCode_EC_FINANCE_BALANCE_PRE_OVER"
	case EventCode_EC_FINANCE_WITHDRAW_REQUEST:
		return "EventCode_EC_FINANCE_WITHDRAW_REQUEST"
	case EventCode_EC_FINANCE_WITHDRAW_REJECTED:
		return "EventCode_EC_FINANCE_WITHDRAW_REJECTED"
	case EventCode_EC_FINANCE_WITHDRAW_PASS:
		return "EventCode_EC_FINANCE_WITHDRAW_PASS"
	case EventCode_EC_FINANCE_WITHDRAW_COMPLETED:
		return "EventCode_EC_FINANCE_WITHDRAW_COMPLETED"
	case EventCode_EC_FINANCE_MEDIA_INVOICE_REQUEST:
		return "EventCode_EC_FINANCE_MEDIA_INVOICE_REQUEST"
	case EventCode_EC_FINANCE_MEDIA_INVOICE_REJECTED:
		return "EventCode_EC_FINANCE_MEDIA_INVOICE_REJECTED"
	case EventCode_EC_FINANCE_MEDIA_INVOICE_CONFIRMED:
		return "EventCode_EC_FINANCE_MEDIA_INVOICE_CONFIRMED"
	case EventCode_EC_FINANCE_IDENTITY_PASSED:
		return "EventCode_EC_FINANCE_IDENTITY_PASSED"
	case EventCode_EC_FINANCE_IDENTITY_REJECTED:
		return "EventCode_EC_FINANCE_IDENTITY_REJECTED"
	case EventCode_EC_FINANCE_IDENTITY_FORBIDDEN:
		return "EventCode_EC_FINANCE_IDENTITY_FORBIDDEN"
	case EventCode_EC_FINANCE_PAYMENT_REQUEST:
		return "EventCode_EC_FINANCE_PAYMENT_REQUEST"
	case EventCode_EC_FINANCE_PAYMENT_REJECTED:
		return "EventCode_EC_FINANCE_PAYMENT_REJECTED"
	case EventCode_EC_FINANCE_PAYMENT_PASS:
		return "EventCode_EC_FINANCE_PAYMENT_PASS"
	case EventCode_EC_FINANCE_PAYMENT_COMPLETED:
		return "EventCode_EC_FINANCE_PAYMENT_COMPLETED"
	case EventCode_EC_AD_PLAN_ADD:
		return "EventCode_EC_AD_PLAN_ADD"
	case EventCode_EC_AD_PLAN_MODIFY:
		return "EventCode_EC_AD_PLAN_MODIFY"
	case EventCode_EC_AD_PLAN_DELETE:
		return "EventCode_EC_AD_PLAN_DELETE"
	case EventCode_EC_AD_PLAN_PAUSE:
		return "EventCode_EC_AD_PLAN_PAUSE"
	case EventCode_EC_AD_PLAN_RESUME:
		return "EventCode_EC_AD_PLAN_RESUME"
	case EventCode_EC_AD_PLAN_SYS_PAUSE:
		return "EventCode_EC_AD_PLAN_SYS_PAUSE"
	case EventCode_EC_AD_PLAN_SYS_RESUME:
		return "EventCode_EC_AD_PLAN_SYS_RESUME"
	case EventCode_EC_AD_STRATEGY_ADD:
		return "EventCode_EC_AD_STRATEGY_ADD"
	case EventCode_EC_AD_STRATEGY_MODIFY:
		return "EventCode_EC_AD_STRATEGY_MODIFY"
	case EventCode_EC_AD_STRATEGY_DELETE:
		return "EventCode_EC_AD_STRATEGY_DELETE"
	case EventCode_EC_AD_STRATEGY_PAUSE:
		return "EventCode_EC_AD_STRATEGY_PAUSE"
	case EventCode_EC_AD_STRATEGY_RESUME:
		return "EventCode_EC_AD_STRATEGY_RESUME"
	case EventCode_EC_AD_CREATIVE_SUBMIT:
		return "EventCode_EC_AD_CREATIVE_SUBMIT"
	case EventCode_EC_AD_CREATIVE_DELETE:
		return "EventCode_EC_AD_CREATIVE_DELETE"
	case EventCode_EC_AD_CREATIVE_PAUSE:
		return "EventCode_EC_AD_CREATIVE_PAUSE"
	case EventCode_EC_AD_CREATIVE_RESUME:
		return "EventCode_EC_AD_CREATIVE_RESUME"
	case EventCode_EC_AD_CREATIVE_APPROVE:
		return "EventCode_EC_AD_CREATIVE_APPROVE"
	case EventCode_EC_AD_CREATIVE_REJECT:
		return "EventCode_EC_AD_CREATIVE_REJECT"
	case EventCode_EC_AD_CREATIVE_MODIFY:
		return "EventCode_EC_AD_CREATIVE_MODIFY"
	case EventCode_EC_MEDIA_SUBMIT:
		return "EventCode_EC_MEDIA_SUBMIT"
	case EventCode_EC_MEDIA_DELETE:
		return "EventCode_EC_MEDIA_DELETE"
	case EventCode_EC_MEDIA_PAUSE:
		return "EventCode_EC_MEDIA_PAUSE"
	case EventCode_EC_MEDIA_RESUME:
		return "EventCode_EC_MEDIA_RESUME"
	case EventCode_EC_MEDIA_APPROVE:
		return "EventCode_EC_MEDIA_APPROVE"
	case EventCode_EC_MEDIA_REJECT:
		return "EventCode_EC_MEDIA_REJECT"
	case EventCode_EC_MEDIA_SETTING:
		return "EventCode_EC_MEDIA_SETTING"
	case EventCode_EC_MEDIA_UPDATE:
		return "EventCode_EC_MEDIA_UPDATE"
	case EventCode_EC_MEDIA_UPLOAD:
		return "EventCode_EC_MEDIA_UPLOAD"
	case EventCode_EC_MEDIA_REVOKE:
		return "EventCode_EC_MEDIA_REVOKE"
	case EventCode_EC_MEDIA_FORBIDDEN:
		return "EventCode_EC_MEDIA_FORBIDDEN"
	case EventCode_EC_MEDIA_UNFORBIDDEN:
		return "EventCode_EC_MEDIA_UNFORBIDDEN"
	case EventCode_EC_MEDIA_REUPLOAD:
		return "EventCode_EC_MEDIA_REUPLOAD"
	case EventCode_EC_MEDIA_APP_UPDATE_SUBMIT:
		return "EventCode_EC_MEDIA_APP_UPDATE_SUBMIT"
	case EventCode_EC_MEDIA_APP_UPDATE_DELETE:
		return "EventCode_EC_MEDIA_APP_UPDATE_DELETE"
	case EventCode_EC_MEDIA_APP_UPDATE_PAUSE:
		return "EventCode_EC_MEDIA_APP_UPDATE_PAUSE"
	case EventCode_EC_MEDIA_APP_UPDATE_RESUME:
		return "EventCode_EC_MEDIA_APP_UPDATE_RESUME"
	case EventCode_EC_MEDIA_APP_UPDATE_APPROVE:
		return "EventCode_EC_MEDIA_APP_UPDATE_APPROVE"
	case EventCode_EC_MEDIA_APP_UPDATE_REJECT:
		return "EventCode_EC_MEDIA_APP_UPDATE_REJECT"
	case EventCode_EC_MEDIA_APP_UPDATE_FORBIDDEN:
		return "EventCode_EC_MEDIA_APP_UPDATE_FORBIDDEN"
	case EventCode_EC_MEDIA_APP_UPDATE_REVOKE:
		return "EventCode_EC_MEDIA_APP_UPDATE_REVOKE"
	case EventCode_EC_MEDIA_APP_UPDATE_UNFORBIDDEN:
		return "EventCode_EC_MEDIA_APP_UPDATE_UNFORBIDDEN"
	case EventCode_EC_MEDIA_APP_RATE_PAUSE:
		return "EventCode_EC_MEDIA_APP_RATE_PAUSE"
	case EventCode_EC_MEDIA_APP_RATE_RESUME:
		return "EventCode_EC_MEDIA_APP_RATE_RESUME"
	case EventCode_EC_MEDIA_APP_RATE_UPDATE:
		return "EventCode_EC_MEDIA_APP_RATE_UPDATE"
	case EventCode_EC_MEDIA_PLACEMENT_UPDATE:
		return "EventCode_EC_MEDIA_PLACEMENT_UPDATE"
	case EventCode_EC_MEDIA_PLACEMENT_DELETE:
		return "EventCode_EC_MEDIA_PLACEMENT_DELETE"
	case EventCode_EC_MEDIA_PLACEMENT_PAUSE:
		return "EventCode_EC_MEDIA_PLACEMENT_PAUSE"
	case EventCode_EC_MEDIA_PLACEMENT_RESUME:
		return "EventCode_EC_MEDIA_PLACEMENT_RESUME"
	case EventCode_EC_CLICK_FINISH:
		return "EventCode_EC_CLICK_FINISH"
	case EventCode_EC_CPC_IMPRESSION:
		return "EventCode_EC_CPC_IMPRESSION"
	case EventCode_EC_CPC_CLICK:
		return "EventCode_EC_CPC_CLICK"
	case EventCode_EC_CPM_IMPRESSION:
		return "EventCode_EC_CPM_IMPRESSION"
	case EventCode_EC_CPM_CLICK:
		return "EventCode_EC_CPM_CLICK"
	case EventCode_EC_ANTI_IMPRESSION:
		return "EventCode_EC_ANTI_IMPRESSION"
	case EventCode_EC_ANTI_CLICK:
		return "EventCode_EC_ANTI_CLICK"
	case EventCode_EC_ANTI_DOWNLOAD:
		return "EventCode_EC_ANTI_DOWNLOAD"
	case EventCode_EC_ANTI_INSTALL:
		return "EventCode_EC_ANTI_INSTALL"
	case EventCode_EC_SETTLE_DOWNLOAD:
		return "EventCode_EC_SETTLE_DOWNLOAD"
	case EventCode_EC_SETTLE_INSTALL:
		return "EventCode_EC_SETTLE_INSTALL"
	case EventCode_EC_CPD_CLICK:
		return "EventCode_EC_CPD_CLICK"
	case EventCode_EC_CPI_CLICK:
		return "EventCode_EC_CPI_CLICK"
	case EventCode_EC_OW_ACT:
		return "EventCode_EC_OW_ACT"
	case EventCode_EC_APP_ADD:
		return "EventCode_EC_APP_ADD"
	case EventCode_EC_APP_UPDATE:
		return "EventCode_EC_APP_UPDATE"
	case EventCode_EC_APP_CHANNEL_ADD:
		return "EventCode_EC_APP_CHANNEL_ADD"
	case EventCode_EC_APP_CHANNEL_UPDATE:
		return "EventCode_EC_APP_CHANNEL_UPDATE"
	case EventCode_EC_APP_CHANNEL_DELETE:
		return "EventCode_EC_APP_CHANNEL_DELETE"
	case EventCode_EC_RTB_CAMPAIGN_UPDATE:
		return "EventCode_EC_RTB_CAMPAIGN_UPDATE"
	case EventCode_EC_RTB_CAMPAIGN_PAUSE:
		return "EventCode_EC_RTB_CAMPAIGN_PAUSE"
	case EventCode_EC_RTB_CAMPAIGN_RESUME:
		return "EventCode_EC_RTB_CAMPAIGN_RESUME"
	case EventCode_EC_RTB_CAMPAIGN_DELETE:
		return "EventCode_EC_RTB_CAMPAIGN_DELETE"
	case EventCode_EC_RTB_CAMPAIGN_TB_OVER:
		return "EventCode_EC_RTB_CAMPAIGN_TB_OVER"
	case EventCode_EC_RTB_CAMPAIGN_DB_OVER:
		return "EventCode_EC_RTB_CAMPAIGN_DB_OVER"
	case EventCode_EC_RTB_CAMPAIGN_TB_OK:
		return "EventCode_EC_RTB_CAMPAIGN_TB_OK"
	case EventCode_EC_RTB_CAMPAIGN_DB_OK:
		return "EventCode_EC_RTB_CAMPAIGN_DB_OK"
	case EventCode_EC_RTB_CAMPAIGN_REFRESH:
		return "EventCode_EC_RTB_CAMPAIGN_REFRESH"
	case EventCode_EC_RTB_CREATIVE_UPDATE:
		return "EventCode_EC_RTB_CREATIVE_UPDATE"
	case EventCode_EC_RTB_CREATIVE_PAUSE:
		return "EventCode_EC_RTB_CREATIVE_PAUSE"
	case EventCode_EC_RTB_CREATIVE_RESUME:
		return "EventCode_EC_RTB_CREATIVE_RESUME"
	case EventCode_EC_RTB_CREATIVE_DELETE:
		return "EventCode_EC_RTB_CREATIVE_DELETE"
	case EventCode_EC_RTB_PROMOTION_UPDATE:
		return "EventCode_EC_RTB_PROMOTION_UPDATE"
	case EventCode_EC_RTB_PROMOTION_DELETE:
		return "EventCode_EC_RTB_PROMOTION_DELETE"
	case EventCode_EC_RTB_STRATEGY_UPDATE:
		return "EventCode_EC_RTB_STRATEGY_UPDATE"
	case EventCode_EC_RTB_STRATEGY_PAUSE:
		return "EventCode_EC_RTB_STRATEGY_PAUSE"
	case EventCode_EC_RTB_STRATEGY_RESUME:
		return "EventCode_EC_RTB_STRATEGY_RESUME"
	case EventCode_EC_RTB_STRATEGY_DELETE:
		return "EventCode_EC_RTB_STRATEGY_DELETE"
	case EventCode_EC_RTB_STRATEGY_DB_OVER:
		return "EventCode_EC_RTB_STRATEGY_DB_OVER"
	case EventCode_EC_RTB_STRATEGY_DB_OK:
		return "EventCode_EC_RTB_STRATEGY_DB_OK"
	case EventCode_EC_RTB_SPONSOR_UPDATE:
		return "EventCode_EC_RTB_SPONSOR_UPDATE"
	case EventCode_EC_RTB_SPONSOR_DELETE:
		return "EventCode_EC_RTB_SPONSOR_DELETE"
	case EventCode_EC_RTB_ADTRACKING_UPDATE:
		return "EventCode_EC_RTB_ADTRACKING_UPDATE"
	case EventCode_EC_RTB_ADTRACKING_DELETE:
		return "EventCode_EC_RTB_ADTRACKING_DELETE"
	case EventCode_EC_RTB_AD_EXPORT:
		return "EventCode_EC_RTB_AD_EXPORT"
	case EventCode_EC_RTB_AD_DUMP_FLUSH:
		return "EventCode_EC_RTB_AD_DUMP_FLUSH"
	case EventCode_EC_RTB_AD_SCHEDULED_EXPORT_CMD:
		return "EventCode_EC_RTB_AD_SCHEDULED_EXPORT_CMD"
	case EventCode_EC_ORDER_SCHEDULES_UPDATE:
		return "EventCode_EC_ORDER_SCHEDULES_UPDATE"
	case EventCode_EC_PROJECT_UPDATE:
		return "EventCode_EC_PROJECT_UPDATE"
	case EventCode_EC_DBM_COMMON:
		return "EventCode_EC_DBM_COMMON"
	case EventCode_EC_DBM_OPERATION:
		return "EventCode_EC_DBM_OPERATION"
	case EventCode_EC_DMP_COMMON:
		return "EventCode_EC_DMP_COMMON"
	case EventCode_EC_DMP_OPERATION:
		return "EventCode_EC_DMP_OPERATION"
	case EventCode_EC_DPM_PROMOTION_UPDATE:
		return "EventCode_EC_DPM_PROMOTION_UPDATE"
	case EventCode_EC_DPM_PROMOTION_ADD:
		return "EventCode_EC_DPM_PROMOTION_ADD"
	case EventCode_EC_DPM_PROMOTION_PROPERTY_UPDATE:
		return "EventCode_EC_DPM_PROMOTION_PROPERTY_UPDATE"
	case EventCode_EC_DPM_PROMOTION_PROPERTY_ADD:
		return "EventCode_EC_DPM_PROMOTION_PROPERTY_ADD"
	case EventCode_EC_DPM_CHANNEL_UPDATE:
		return "EventCode_EC_DPM_CHANNEL_UPDATE"
	case EventCode_EC_DPM_CHANNEL_ADD:
		return "EventCode_EC_DPM_CHANNEL_ADD"
	case EventCode_EC_DPM_TRACKING_UPDATE:
		return "EventCode_EC_DPM_TRACKING_UPDATE"
	case EventCode_EC_DPM_TRACKING_ADD:
		return "EventCode_EC_DPM_TRACKING_ADD"
	case EventCode_EC_DPM_COMPANY_UPDATE:
		return "EventCode_EC_DPM_COMPANY_UPDATE"
	case EventCode_EC_DPM_COMPANY_ADD:
		return "EventCode_EC_DPM_COMPANY_ADD"
	case EventCode_EC_DOS_COMMON:
		return "EventCode_EC_DOS_COMMON"
	case EventCode_EC_DOS_STATS_UPDATE:
		return "EventCode_EC_DOS_STATS_UPDATE"
	case EventCode_EC_DSP_STATS_UPDATE:
		return "EventCode_EC_DSP_STATS_UPDATE"
	case EventCode_EC_VICO_COMMON:
		return "EventCode_EC_VICO_COMMON"
	case EventCode_EC_DATA_PLUS_COMMON:
		return "EventCode_EC_DATA_PLUS_COMMON"
	case EventCode_EC_COMPASS_COMMON:
		return "EventCode_EC_COMPASS_COMMON"
	case EventCode_EC_BIDMASTER_COMMON:
		return "EventCode_EC_BIDMASTER_COMMON"
	case EventCode_EC_BIDMASTER_OPERATION:
		return "EventCode_EC_BIDMASTER_OPERATION"
	}
	return "<UNSET>"
}

func EventCodeFromString(s string) (EventCode, error) {
	switch s {
	case "EventCode_EC_INTERNAL_CHECK":
		return EventCode_EC_INTERNAL_CHECK, nil
	case "EventCode_EC_INTERNAL_FILE_READY":
		return EventCode_EC_INTERNAL_FILE_READY, nil
	case "EventCode_EC_USER_REGISTER":
		return EventCode_EC_USER_REGISTER, nil
	case "EventCode_EC_USER_ACTIVATE":
		return EventCode_EC_USER_ACTIVATE, nil
	case "EventCode_EC_USER_BANNED":
		return EventCode_EC_USER_BANNED, nil
	case "EventCode_EC_USER_UNBANNED":
		return EventCode_EC_USER_UNBANNED, nil
	case "EventCode_EC_USER_ADD_ADER_ROLE":
		return EventCode_EC_USER_ADD_ADER_ROLE, nil
	case "EventCode_EC_USER_ADD_DEVER_ROLE":
		return EventCode_EC_USER_ADD_DEVER_ROLE, nil
	case "EventCode_EC_USER_PROFILE_CHANGED":
		return EventCode_EC_USER_PROFILE_CHANGED, nil
	case "EventCode_EC_USER_BECOME_TRAFFIC_EXCHANGE":
		return EventCode_EC_USER_BECOME_TRAFFIC_EXCHANGE, nil
	case "EventCode_EC_USER_CHANGE_EMAIL":
		return EventCode_EC_USER_CHANGE_EMAIL, nil
	case "EventCode_EC_FINANCE_UPDATE":
		return EventCode_EC_FINANCE_UPDATE, nil
	case "EventCode_EC_FINANCE_NO_BALANCE":
		return EventCode_EC_FINANCE_NO_BALANCE, nil
	case "EventCode_EC_FINANCE_BALANCE_OK":
		return EventCode_EC_FINANCE_BALANCE_OK, nil
	case "EventCode_EC_FINANCE_RECHARGED":
		return EventCode_EC_FINANCE_RECHARGED, nil
	case "EventCode_EC_FINANCE_LOW_BALANCE":
		return EventCode_EC_FINANCE_LOW_BALANCE, nil
	case "EventCode_EC_FINANCE_DAILY_BUDGET_OVER":
		return EventCode_EC_FINANCE_DAILY_BUDGET_OVER, nil
	case "EventCode_EC_FINANCE_TOTAL_BUDGET_OVER":
		return EventCode_EC_FINANCE_TOTAL_BUDGET_OVER, nil
	case "EventCode_EC_FINANCE_DAILY_BUDGET_OK":
		return EventCode_EC_FINANCE_DAILY_BUDGET_OK, nil
	case "EventCode_EC_FINANCE_TOTAL_BUDGET_OK":
		return EventCode_EC_FINANCE_TOTAL_BUDGET_OK, nil
	case "EventCode_EC_FINANCE_BALANCE_PRE_OVER":
		return EventCode_EC_FINANCE_BALANCE_PRE_OVER, nil
	case "EventCode_EC_FINANCE_WITHDRAW_REQUEST":
		return EventCode_EC_FINANCE_WITHDRAW_REQUEST, nil
	case "EventCode_EC_FINANCE_WITHDRAW_REJECTED":
		return EventCode_EC_FINANCE_WITHDRAW_REJECTED, nil
	case "EventCode_EC_FINANCE_WITHDRAW_PASS":
		return EventCode_EC_FINANCE_WITHDRAW_PASS, nil
	case "EventCode_EC_FINANCE_WITHDRAW_COMPLETED":
		return EventCode_EC_FINANCE_WITHDRAW_COMPLETED, nil
	case "EventCode_EC_FINANCE_MEDIA_INVOICE_REQUEST":
		return EventCode_EC_FINANCE_MEDIA_INVOICE_REQUEST, nil
	case "EventCode_EC_FINANCE_MEDIA_INVOICE_REJECTED":
		return EventCode_EC_FINANCE_MEDIA_INVOICE_REJECTED, nil
	case "EventCode_EC_FINANCE_MEDIA_INVOICE_CONFIRMED":
		return EventCode_EC_FINANCE_MEDIA_INVOICE_CONFIRMED, nil
	case "EventCode_EC_FINANCE_IDENTITY_PASSED":
		return EventCode_EC_FINANCE_IDENTITY_PASSED, nil
	case "EventCode_EC_FINANCE_IDENTITY_REJECTED":
		return EventCode_EC_FINANCE_IDENTITY_REJECTED, nil
	case "EventCode_EC_FINANCE_IDENTITY_FORBIDDEN":
		return EventCode_EC_FINANCE_IDENTITY_FORBIDDEN, nil
	case "EventCode_EC_FINANCE_PAYMENT_REQUEST":
		return EventCode_EC_FINANCE_PAYMENT_REQUEST, nil
	case "EventCode_EC_FINANCE_PAYMENT_REJECTED":
		return EventCode_EC_FINANCE_PAYMENT_REJECTED, nil
	case "EventCode_EC_FINANCE_PAYMENT_PASS":
		return EventCode_EC_FINANCE_PAYMENT_PASS, nil
	case "EventCode_EC_FINANCE_PAYMENT_COMPLETED":
		return EventCode_EC_FINANCE_PAYMENT_COMPLETED, nil
	case "EventCode_EC_AD_PLAN_ADD":
		return EventCode_EC_AD_PLAN_ADD, nil
	case "EventCode_EC_AD_PLAN_MODIFY":
		return EventCode_EC_AD_PLAN_MODIFY, nil
	case "EventCode_EC_AD_PLAN_DELETE":
		return EventCode_EC_AD_PLAN_DELETE, nil
	case "EventCode_EC_AD_PLAN_PAUSE":
		return EventCode_EC_AD_PLAN_PAUSE, nil
	case "EventCode_EC_AD_PLAN_RESUME":
		return EventCode_EC_AD_PLAN_RESUME, nil
	case "EventCode_EC_AD_PLAN_SYS_PAUSE":
		return EventCode_EC_AD_PLAN_SYS_PAUSE, nil
	case "EventCode_EC_AD_PLAN_SYS_RESUME":
		return EventCode_EC_AD_PLAN_SYS_RESUME, nil
	case "EventCode_EC_AD_STRATEGY_ADD":
		return EventCode_EC_AD_STRATEGY_ADD, nil
	case "EventCode_EC_AD_STRATEGY_MODIFY":
		return EventCode_EC_AD_STRATEGY_MODIFY, nil
	case "EventCode_EC_AD_STRATEGY_DELETE":
		return EventCode_EC_AD_STRATEGY_DELETE, nil
	case "EventCode_EC_AD_STRATEGY_PAUSE":
		return EventCode_EC_AD_STRATEGY_PAUSE, nil
	case "EventCode_EC_AD_STRATEGY_RESUME":
		return EventCode_EC_AD_STRATEGY_RESUME, nil
	case "EventCode_EC_AD_CREATIVE_SUBMIT":
		return EventCode_EC_AD_CREATIVE_SUBMIT, nil
	case "EventCode_EC_AD_CREATIVE_DELETE":
		return EventCode_EC_AD_CREATIVE_DELETE, nil
	case "EventCode_EC_AD_CREATIVE_PAUSE":
		return EventCode_EC_AD_CREATIVE_PAUSE, nil
	case "EventCode_EC_AD_CREATIVE_RESUME":
		return EventCode_EC_AD_CREATIVE_RESUME, nil
	case "EventCode_EC_AD_CREATIVE_APPROVE":
		return EventCode_EC_AD_CREATIVE_APPROVE, nil
	case "EventCode_EC_AD_CREATIVE_REJECT":
		return EventCode_EC_AD_CREATIVE_REJECT, nil
	case "EventCode_EC_AD_CREATIVE_MODIFY":
		return EventCode_EC_AD_CREATIVE_MODIFY, nil
	case "EventCode_EC_MEDIA_SUBMIT":
		return EventCode_EC_MEDIA_SUBMIT, nil
	case "EventCode_EC_MEDIA_DELETE":
		return EventCode_EC_MEDIA_DELETE, nil
	case "EventCode_EC_MEDIA_PAUSE":
		return EventCode_EC_MEDIA_PAUSE, nil
	case "EventCode_EC_MEDIA_RESUME":
		return EventCode_EC_MEDIA_RESUME, nil
	case "EventCode_EC_MEDIA_APPROVE":
		return EventCode_EC_MEDIA_APPROVE, nil
	case "EventCode_EC_MEDIA_REJECT":
		return EventCode_EC_MEDIA_REJECT, nil
	case "EventCode_EC_MEDIA_SETTING":
		return EventCode_EC_MEDIA_SETTING, nil
	case "EventCode_EC_MEDIA_UPDATE":
		return EventCode_EC_MEDIA_UPDATE, nil
	case "EventCode_EC_MEDIA_UPLOAD":
		return EventCode_EC_MEDIA_UPLOAD, nil
	case "EventCode_EC_MEDIA_REVOKE":
		return EventCode_EC_MEDIA_REVOKE, nil
	case "EventCode_EC_MEDIA_FORBIDDEN":
		return EventCode_EC_MEDIA_FORBIDDEN, nil
	case "EventCode_EC_MEDIA_UNFORBIDDEN":
		return EventCode_EC_MEDIA_UNFORBIDDEN, nil
	case "EventCode_EC_MEDIA_REUPLOAD":
		return EventCode_EC_MEDIA_REUPLOAD, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_SUBMIT":
		return EventCode_EC_MEDIA_APP_UPDATE_SUBMIT, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_DELETE":
		return EventCode_EC_MEDIA_APP_UPDATE_DELETE, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_PAUSE":
		return EventCode_EC_MEDIA_APP_UPDATE_PAUSE, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_RESUME":
		return EventCode_EC_MEDIA_APP_UPDATE_RESUME, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_APPROVE":
		return EventCode_EC_MEDIA_APP_UPDATE_APPROVE, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_REJECT":
		return EventCode_EC_MEDIA_APP_UPDATE_REJECT, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_FORBIDDEN":
		return EventCode_EC_MEDIA_APP_UPDATE_FORBIDDEN, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_REVOKE":
		return EventCode_EC_MEDIA_APP_UPDATE_REVOKE, nil
	case "EventCode_EC_MEDIA_APP_UPDATE_UNFORBIDDEN":
		return EventCode_EC_MEDIA_APP_UPDATE_UNFORBIDDEN, nil
	case "EventCode_EC_MEDIA_APP_RATE_PAUSE":
		return EventCode_EC_MEDIA_APP_RATE_PAUSE, nil
	case "EventCode_EC_MEDIA_APP_RATE_RESUME":
		return EventCode_EC_MEDIA_APP_RATE_RESUME, nil
	case "EventCode_EC_MEDIA_APP_RATE_UPDATE":
		return EventCode_EC_MEDIA_APP_RATE_UPDATE, nil
	case "EventCode_EC_MEDIA_PLACEMENT_UPDATE":
		return EventCode_EC_MEDIA_PLACEMENT_UPDATE, nil
	case "EventCode_EC_MEDIA_PLACEMENT_DELETE":
		return EventCode_EC_MEDIA_PLACEMENT_DELETE, nil
	case "EventCode_EC_MEDIA_PLACEMENT_PAUSE":
		return EventCode_EC_MEDIA_PLACEMENT_PAUSE, nil
	case "EventCode_EC_MEDIA_PLACEMENT_RESUME":
		return EventCode_EC_MEDIA_PLACEMENT_RESUME, nil
	case "EventCode_EC_CLICK_FINISH":
		return EventCode_EC_CLICK_FINISH, nil
	case "EventCode_EC_CPC_IMPRESSION":
		return EventCode_EC_CPC_IMPRESSION, nil
	case "EventCode_EC_CPC_CLICK":
		return EventCode_EC_CPC_CLICK, nil
	case "EventCode_EC_CPM_IMPRESSION":
		return EventCode_EC_CPM_IMPRESSION, nil
	case "EventCode_EC_CPM_CLICK":
		return EventCode_EC_CPM_CLICK, nil
	case "EventCode_EC_ANTI_IMPRESSION":
		return EventCode_EC_ANTI_IMPRESSION, nil
	case "EventCode_EC_ANTI_CLICK":
		return EventCode_EC_ANTI_CLICK, nil
	case "EventCode_EC_ANTI_DOWNLOAD":
		return EventCode_EC_ANTI_DOWNLOAD, nil
	case "EventCode_EC_ANTI_INSTALL":
		return EventCode_EC_ANTI_INSTALL, nil
	case "EventCode_EC_SETTLE_DOWNLOAD":
		return EventCode_EC_SETTLE_DOWNLOAD, nil
	case "EventCode_EC_SETTLE_INSTALL":
		return EventCode_EC_SETTLE_INSTALL, nil
	case "EventCode_EC_CPD_CLICK":
		return EventCode_EC_CPD_CLICK, nil
	case "EventCode_EC_CPI_CLICK":
		return EventCode_EC_CPI_CLICK, nil
	case "EventCode_EC_OW_ACT":
		return EventCode_EC_OW_ACT, nil
	case "EventCode_EC_APP_ADD":
		return EventCode_EC_APP_ADD, nil
	case "EventCode_EC_APP_UPDATE":
		return EventCode_EC_APP_UPDATE, nil
	case "EventCode_EC_APP_CHANNEL_ADD":
		return EventCode_EC_APP_CHANNEL_ADD, nil
	case "EventCode_EC_APP_CHANNEL_UPDATE":
		return EventCode_EC_APP_CHANNEL_UPDATE, nil
	case "EventCode_EC_APP_CHANNEL_DELETE":
		return EventCode_EC_APP_CHANNEL_DELETE, nil
	case "EventCode_EC_RTB_CAMPAIGN_UPDATE":
		return EventCode_EC_RTB_CAMPAIGN_UPDATE, nil
	case "EventCode_EC_RTB_CAMPAIGN_PAUSE":
		return EventCode_EC_RTB_CAMPAIGN_PAUSE, nil
	case "EventCode_EC_RTB_CAMPAIGN_RESUME":
		return EventCode_EC_RTB_CAMPAIGN_RESUME, nil
	case "EventCode_EC_RTB_CAMPAIGN_DELETE":
		return EventCode_EC_RTB_CAMPAIGN_DELETE, nil
	case "EventCode_EC_RTB_CAMPAIGN_TB_OVER":
		return EventCode_EC_RTB_CAMPAIGN_TB_OVER, nil
	case "EventCode_EC_RTB_CAMPAIGN_DB_OVER":
		return EventCode_EC_RTB_CAMPAIGN_DB_OVER, nil
	case "EventCode_EC_RTB_CAMPAIGN_TB_OK":
		return EventCode_EC_RTB_CAMPAIGN_TB_OK, nil
	case "EventCode_EC_RTB_CAMPAIGN_DB_OK":
		return EventCode_EC_RTB_CAMPAIGN_DB_OK, nil
	case "EventCode_EC_RTB_CAMPAIGN_REFRESH":
		return EventCode_EC_RTB_CAMPAIGN_REFRESH, nil
	case "EventCode_EC_RTB_CREATIVE_UPDATE":
		return EventCode_EC_RTB_CREATIVE_UPDATE, nil
	case "EventCode_EC_RTB_CREATIVE_PAUSE":
		return EventCode_EC_RTB_CREATIVE_PAUSE, nil
	case "EventCode_EC_RTB_CREATIVE_RESUME":
		return EventCode_EC_RTB_CREATIVE_RESUME, nil
	case "EventCode_EC_RTB_CREATIVE_DELETE":
		return EventCode_EC_RTB_CREATIVE_DELETE, nil
	case "EventCode_EC_RTB_PROMOTION_UPDATE":
		return EventCode_EC_RTB_PROMOTION_UPDATE, nil
	case "EventCode_EC_RTB_PROMOTION_DELETE":
		return EventCode_EC_RTB_PROMOTION_DELETE, nil
	case "EventCode_EC_RTB_STRATEGY_UPDATE":
		return EventCode_EC_RTB_STRATEGY_UPDATE, nil
	case "EventCode_EC_RTB_STRATEGY_PAUSE":
		return EventCode_EC_RTB_STRATEGY_PAUSE, nil
	case "EventCode_EC_RTB_STRATEGY_RESUME":
		return EventCode_EC_RTB_STRATEGY_RESUME, nil
	case "EventCode_EC_RTB_STRATEGY_DELETE":
		return EventCode_EC_RTB_STRATEGY_DELETE, nil
	case "EventCode_EC_RTB_STRATEGY_DB_OVER":
		return EventCode_EC_RTB_STRATEGY_DB_OVER, nil
	case "EventCode_EC_RTB_STRATEGY_DB_OK":
		return EventCode_EC_RTB_STRATEGY_DB_OK, nil
	case "EventCode_EC_RTB_SPONSOR_UPDATE":
		return EventCode_EC_RTB_SPONSOR_UPDATE, nil
	case "EventCode_EC_RTB_SPONSOR_DELETE":
		return EventCode_EC_RTB_SPONSOR_DELETE, nil
	case "EventCode_EC_RTB_ADTRACKING_UPDATE":
		return EventCode_EC_RTB_ADTRACKING_UPDATE, nil
	case "EventCode_EC_RTB_ADTRACKING_DELETE":
		return EventCode_EC_RTB_ADTRACKING_DELETE, nil
	case "EventCode_EC_RTB_AD_EXPORT":
		return EventCode_EC_RTB_AD_EXPORT, nil
	case "EventCode_EC_RTB_AD_DUMP_FLUSH":
		return EventCode_EC_RTB_AD_DUMP_FLUSH, nil
	case "EventCode_EC_RTB_AD_SCHEDULED_EXPORT_CMD":
		return EventCode_EC_RTB_AD_SCHEDULED_EXPORT_CMD, nil
	case "EventCode_EC_ORDER_SCHEDULES_UPDATE":
		return EventCode_EC_ORDER_SCHEDULES_UPDATE, nil
	case "EventCode_EC_PROJECT_UPDATE":
		return EventCode_EC_PROJECT_UPDATE, nil
	case "EventCode_EC_DBM_COMMON":
		return EventCode_EC_DBM_COMMON, nil
	case "EventCode_EC_DBM_OPERATION":
		return EventCode_EC_DBM_OPERATION, nil
	case "EventCode_EC_DMP_COMMON":
		return EventCode_EC_DMP_COMMON, nil
	case "EventCode_EC_DMP_OPERATION":
		return EventCode_EC_DMP_OPERATION, nil
	case "EventCode_EC_DPM_PROMOTION_UPDATE":
		return EventCode_EC_DPM_PROMOTION_UPDATE, nil
	case "EventCode_EC_DPM_PROMOTION_ADD":
		return EventCode_EC_DPM_PROMOTION_ADD, nil
	case "EventCode_EC_DPM_PROMOTION_PROPERTY_UPDATE":
		return EventCode_EC_DPM_PROMOTION_PROPERTY_UPDATE, nil
	case "EventCode_EC_DPM_PROMOTION_PROPERTY_ADD":
		return EventCode_EC_DPM_PROMOTION_PROPERTY_ADD, nil
	case "EventCode_EC_DPM_CHANNEL_UPDATE":
		return EventCode_EC_DPM_CHANNEL_UPDATE, nil
	case "EventCode_EC_DPM_CHANNEL_ADD":
		return EventCode_EC_DPM_CHANNEL_ADD, nil
	case "EventCode_EC_DPM_TRACKING_UPDATE":
		return EventCode_EC_DPM_TRACKING_UPDATE, nil
	case "EventCode_EC_DPM_TRACKING_ADD":
		return EventCode_EC_DPM_TRACKING_ADD, nil
	case "EventCode_EC_DPM_COMPANY_UPDATE":
		return EventCode_EC_DPM_COMPANY_UPDATE, nil
	case "EventCode_EC_DPM_COMPANY_ADD":
		return EventCode_EC_DPM_COMPANY_ADD, nil
	case "EventCode_EC_DOS_COMMON":
		return EventCode_EC_DOS_COMMON, nil
	case "EventCode_EC_DOS_STATS_UPDATE":
		return EventCode_EC_DOS_STATS_UPDATE, nil
	case "EventCode_EC_DSP_STATS_UPDATE":
		return EventCode_EC_DSP_STATS_UPDATE, nil
	case "EventCode_EC_VICO_COMMON":
		return EventCode_EC_VICO_COMMON, nil
	case "EventCode_EC_DATA_PLUS_COMMON":
		return EventCode_EC_DATA_PLUS_COMMON, nil
	case "EventCode_EC_COMPASS_COMMON":
		return EventCode_EC_COMPASS_COMMON, nil
	case "EventCode_EC_BIDMASTER_COMMON":
		return EventCode_EC_BIDMASTER_COMMON, nil
	case "EventCode_EC_BIDMASTER_OPERATION":
		return EventCode_EC_BIDMASTER_OPERATION, nil
	}
	return EventCode(math.MinInt32 - 1), fmt.Errorf("not a valid EventCode string")
}

type EventID common.LargeIdInt

type TimeInt common.TimeInt

type RequestHeader *common.RequestHeader

type EventHeader struct {
	Version   int32     `thrift:"version,1" json:"version"`
	TypeA1    EventType `thrift:"type,2" json:"type"`
	Code      EventCode `thrift:"code,3" json:"code"`
	Eid       int64     `thrift:"eid,4" json:"eid"`
	Timestamp int64     `thrift:"timestamp,5" json:"timestamp"`
	Provider  string    `thrift:"provider,6" json:"provider"`
}

func NewEventHeader() *EventHeader {
	return &EventHeader{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *EventHeader) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *EventHeader) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *EventHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EventHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *EventHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = EventType(v)
	}
	return nil
}

func (p *EventHeader) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Code = EventCode(v)
	}
	return nil
}

func (p *EventHeader) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Eid = v
	}
	return nil
}

func (p *EventHeader) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *EventHeader) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Provider = v
	}
	return nil
}

func (p *EventHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("EventHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EventHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:version: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Version)); err != nil {
		return fmt.Errorf("%T.version (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:version: %s", p, err)
	}
	return err
}

func (p *EventHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *EventHeader) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:code: %s", p, err)
		}
	}
	return err
}

func (p *EventHeader) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("eid", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:eid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Eid)); err != nil {
		return fmt.Errorf("%T.eid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:eid: %s", p, err)
	}
	return err
}

func (p *EventHeader) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:timestamp: %s", p, err)
	}
	return err
}

func (p *EventHeader) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("provider", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:provider: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Provider)); err != nil {
		return fmt.Errorf("%T.provider (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:provider: %s", p, err)
	}
	return err
}

func (p *EventHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EventHeader(%+v)", *p)
}

type ExtEvent struct {
	ExtType string `thrift:"extType,1" json:"extType"`
}

func NewExtEvent() *ExtEvent {
	return &ExtEvent{}
}

func (p *ExtEvent) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExtEvent) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ExtType = v
	}
	return nil
}

func (p *ExtEvent) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ExtEvent"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExtEvent) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extType", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:extType: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtType)); err != nil {
		return fmt.Errorf("%T.extType (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:extType: %s", p, err)
	}
	return err
}

func (p *ExtEvent) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtEvent(%+v)", *p)
}
