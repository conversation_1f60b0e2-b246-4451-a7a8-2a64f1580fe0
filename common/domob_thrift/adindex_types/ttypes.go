// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package adindex_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/tag_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = tag_types.GoUnusedProtection__
var GoUnusedProtection__ int

type CapabilityCode int64

const (
	CapabilityCode_CP_UNKNOWN CapabilityCode = 0
	CapabilityCode_CP_ALL     CapabilityCode = 1
	CapabilityCode_CP_CALL    CapabilityCode = 2
	CapabilityCode_CP_MAP     CapabilityCode = 3
	CapabilityCode_CP_BROWSER CapabilityCode = 4
	CapabilityCode_CP_SMS     CapabilityCode = 5
	CapabilityCode_CP_MAIL    CapabilityCode = 6
	CapabilityCode_CP_OTHER   CapabilityCode = 100
)

func (p CapabilityCode) String() string {
	switch p {
	case CapabilityCode_CP_UNKNOWN:
		return "CapabilityCode_CP_UNKNOWN"
	case CapabilityCode_CP_ALL:
		return "CapabilityCode_CP_ALL"
	case CapabilityCode_CP_CALL:
		return "CapabilityCode_CP_CALL"
	case CapabilityCode_CP_MAP:
		return "CapabilityCode_CP_MAP"
	case CapabilityCode_CP_BROWSER:
		return "CapabilityCode_CP_BROWSER"
	case CapabilityCode_CP_SMS:
		return "CapabilityCode_CP_SMS"
	case CapabilityCode_CP_MAIL:
		return "CapabilityCode_CP_MAIL"
	case CapabilityCode_CP_OTHER:
		return "CapabilityCode_CP_OTHER"
	}
	return "<UNSET>"
}

func CapabilityCodeFromString(s string) (CapabilityCode, error) {
	switch s {
	case "CapabilityCode_CP_UNKNOWN":
		return CapabilityCode_CP_UNKNOWN, nil
	case "CapabilityCode_CP_ALL":
		return CapabilityCode_CP_ALL, nil
	case "CapabilityCode_CP_CALL":
		return CapabilityCode_CP_CALL, nil
	case "CapabilityCode_CP_MAP":
		return CapabilityCode_CP_MAP, nil
	case "CapabilityCode_CP_BROWSER":
		return CapabilityCode_CP_BROWSER, nil
	case "CapabilityCode_CP_SMS":
		return CapabilityCode_CP_SMS, nil
	case "CapabilityCode_CP_MAIL":
		return CapabilityCode_CP_MAIL, nil
	case "CapabilityCode_CP_OTHER":
		return CapabilityCode_CP_OTHER, nil
	}
	return CapabilityCode(math.MinInt32 - 1), fmt.Errorf("not a valid CapabilityCode string")
}

type IdxIdInt common.IdInt

type IdxUidInt common.UidInt

type IdxAdPlanIdInt common.IdInt

type IdxAdStrategyIdInt common.IdInt

type IdxAdCreativeIdInt common.IdInt

type IdxCurrencyAmount common.Amount

type IdxImgIdInt common.IdInt

type IdxTimeInt common.TimeInt

type IdxAdQueryResult *common.QueryResult

type IdxAdQueryInt common.QueryInt

type IdxGenderCode common.GenderCode

type IdxRegionCode common.RegionCode

type IdxAgeCode common.AgeCode

type IdxCarrierCode common.CarrierCode

type IdxDeviceCode common.DeviceCode

type IdxOSCode common.OSCode

type IdxBrowserCode common.BrowserCode

type IdxAccessTypeCode common.AccessTypeCode

type IdxCostType common.CostType

type IdxAdCategory common.AdCategory

type IdxAdCreativeIconType common.AdCreativeIconType

type IdxTemplateSizeCode common.TemplateSizeCode

type IdxTemplateSizeCodeInt common.TemplateSizeCodeInt

type IdxAdStrategyType common.AdStrategyType

type IdxAdCreativeType common.AdCreativeType

type IdxAdActionType common.AdActionType

type IdxLandingDirection common.LandingDirection

type IdxAdPlacementType common.AdPlacementType

type IdxRateInt int32

type IdxAdUserRole common.UserRole

type MediaTargetType common.MediaTargetType

type RichMediaCodeInt int32

type IdxSDKUrlOpenType common.SDKUrlOpenType

type IdxImageType common.ImageType

type RegionCityInt int32

type DemoTagging *tag_types.DemoTagging

type ImpDecision *tag_types.ImpDecision

type DemoTagIdInt tag_types.DemoTagIdInt

type MediaTagIdInt tag_types.MediaTagIdInt

type TagIdInt common.IdInt

type PositionIdInt common.IdInt

type DeviceGroupIdInt common.DeviceGroupIdInt

type HtmlTemplateCodeInt int32

type ResIdInt int32

type TimeInt int32

type AdRenderType common.AdRenderType

type JailBreakCode common.JailBreakCode

type IdxWaterMarkPosition common.WaterMarkPosition

type CapabilityIdInt int32

type CloseButtonPosition common.CloseButtonPosition

type SponsorType common.SponsorType

type IdxFreqInfo *common.FreqInfo

type IdxResourceGroup *common.ResourceGroup

type IdxAdCreativeTemplateType common.AdCreativeTemplateType

type IdxAdCreativeTemplateSpecIdInt int32

type IndexAdUser struct {
	Uid        common.UidInt `thrift:"uid,1" json:"uid"`
	Recharged  common.Amount `thrift:"recharged,2" json:"recharged"`
	Transfered common.Amount `thrift:"transfered,3" json:"transfered"`
	Consumed   common.Amount `thrift:"consumed,4" json:"consumed"`
	Balance    common.Amount `thrift:"balance,5" json:"balance"`
	Current    common.Amount `thrift:"current,6" json:"current"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Role              IdxAdUserRole        `thrift:"role,10" json:"role"`
	AutoTransferRate  common.PercentageInt `thrift:"autoTransferRate,11" json:"autoTransferRate"`
	TransferAwardRate common.PercentageInt `thrift:"transferAwardRate,12" json:"transferAwardRate"`
	SponsorType       SponsorType          `thrift:"sponsorType,13" json:"sponsorType"`
	IsTactic          bool                 `thrift:"isTactic,14" json:"isTactic"`
	UserPriority      int32                `thrift:"userPriority,15" json:"userPriority"`
}

func NewIndexAdUser() *IndexAdUser {
	return &IndexAdUser{
		Role: math.MinInt32 - 1, // unset sentinal value

		SponsorType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexAdUser) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *IndexAdUser) IsSetSponsorType() bool {
	return int64(p.SponsorType) != math.MinInt32-1
}

func (p *IndexAdUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *IndexAdUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Recharged = common.Amount(v)
	}
	return nil
}

func (p *IndexAdUser) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Transfered = common.Amount(v)
	}
	return nil
}

func (p *IndexAdUser) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Consumed = common.Amount(v)
	}
	return nil
}

func (p *IndexAdUser) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Balance = common.Amount(v)
	}
	return nil
}

func (p *IndexAdUser) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Current = common.Amount(v)
	}
	return nil
}

func (p *IndexAdUser) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Role = IdxAdUserRole(v)
	}
	return nil
}

func (p *IndexAdUser) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AutoTransferRate = common.PercentageInt(v)
	}
	return nil
}

func (p *IndexAdUser) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.TransferAwardRate = common.PercentageInt(v)
	}
	return nil
}

func (p *IndexAdUser) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.SponsorType = SponsorType(v)
	}
	return nil
}

func (p *IndexAdUser) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.IsTactic = v
	}
	return nil
}

func (p *IndexAdUser) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.UserPriority = v
	}
	return nil
}

func (p *IndexAdUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("recharged", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:recharged: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Recharged)); err != nil {
		return fmt.Errorf("%T.recharged (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:recharged: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transfered", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:transfered: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Transfered)); err != nil {
		return fmt.Errorf("%T.transfered (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:transfered: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumed", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:consumed: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Consumed)); err != nil {
		return fmt.Errorf("%T.consumed (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:consumed: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:balance: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("current", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:current: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Current)); err != nil {
		return fmt.Errorf("%T.current (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:current: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("role", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:role: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Role)); err != nil {
		return fmt.Errorf("%T.role (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:role: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("autoTransferRate", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:autoTransferRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AutoTransferRate)); err != nil {
		return fmt.Errorf("%T.autoTransferRate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:autoTransferRate: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("transferAwardRate", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:transferAwardRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TransferAwardRate)); err != nil {
		return fmt.Errorf("%T.transferAwardRate (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:transferAwardRate: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorType", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:sponsorType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorType)); err != nil {
		return fmt.Errorf("%T.sponsorType (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:sponsorType: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isTactic", thrift.BOOL, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:isTactic: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsTactic)); err != nil {
		return fmt.Errorf("%T.isTactic (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:isTactic: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userPriority", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:userPriority: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserPriority)); err != nil {
		return fmt.Errorf("%T.userPriority (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:userPriority: %s", p, err)
	}
	return err
}

func (p *IndexAdUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdUser(%+v)", *p)
}

type IndexBrandAd struct {
	Uid     common.UidInt        `thrift:"uid,1" json:"uid"`
	CidList []IdxAdCreativeIdInt `thrift:"cidList,2" json:"cidList"`
}

func NewIndexBrandAd() *IndexBrandAd {
	return &IndexBrandAd{}
}

func (p *IndexBrandAd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexBrandAd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = common.UidInt(v)
	}
	return nil
}

func (p *IndexBrandAd) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CidList = make([]IdxAdCreativeIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 IdxAdCreativeIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = IdxAdCreativeIdInt(v)
		}
		p.CidList = append(p.CidList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexBrandAd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexBrandAd"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexBrandAd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexBrandAd) writeField2(oprot thrift.TProtocol) (err error) {
	if p.CidList != nil {
		if err := oprot.WriteFieldBegin("cidList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:cidList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CidList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CidList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:cidList: %s", p, err)
		}
	}
	return err
}

func (p *IndexBrandAd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexBrandAd(%+v)", *p)
}

type IndexAdPlan struct {
	Uid         IdxUidInt          `thrift:"uid,1" json:"uid"`
	Pid         IdxAdPlanIdInt     `thrift:"pid,2" json:"pid"`
	CostType    IdxCostType        `thrift:"costType,3" json:"costType"`
	Budget      IdxCurrencyAmount  `thrift:"budget,4" json:"budget"`
	Category    IdxAdCategory      `thrift:"category,5" json:"category"`
	CtaRate     IdxRateInt         `thrift:"ctaRate,6" json:"ctaRate"`
	EndTime     TimeInt            `thrift:"endTime,7" json:"endTime"`
	CpaCostType IdxCostType        `thrift:"cpaCostType,8" json:"cpaCostType"`
	StartTime   TimeInt            `thrift:"startTime,9" json:"startTime"`
	ImpFreqSet  []*common.FreqInfo `thrift:"impFreqSet,10" json:"impFreqSet"`
	ClkFreqSet  []*common.FreqInfo `thrift:"clkFreqSet,11" json:"clkFreqSet"`
	CptCostType IdxCostType        `thrift:"cptCostType,12" json:"cptCostType"`
	Name        string             `thrift:"name,13" json:"name"`
}

func NewIndexAdPlan() *IndexAdPlan {
	return &IndexAdPlan{
		CostType: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value

		CpaCostType: math.MinInt32 - 1, // unset sentinal value

		CptCostType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexAdPlan) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *IndexAdPlan) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *IndexAdPlan) IsSetCpaCostType() bool {
	return int64(p.CpaCostType) != math.MinInt32-1
}

func (p *IndexAdPlan) IsSetCptCostType() bool {
	return int64(p.CptCostType) != math.MinInt32-1
}

func (p *IndexAdPlan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdPlan) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = IdxUidInt(v)
	}
	return nil
}

func (p *IndexAdPlan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = IdxAdPlanIdInt(v)
	}
	return nil
}

func (p *IndexAdPlan) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CostType = IdxCostType(v)
	}
	return nil
}

func (p *IndexAdPlan) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Budget = IdxCurrencyAmount(v)
	}
	return nil
}

func (p *IndexAdPlan) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Category = IdxAdCategory(v)
	}
	return nil
}

func (p *IndexAdPlan) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CtaRate = IdxRateInt(v)
	}
	return nil
}

func (p *IndexAdPlan) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.EndTime = TimeInt(v)
	}
	return nil
}

func (p *IndexAdPlan) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CpaCostType = IdxCostType(v)
	}
	return nil
}

func (p *IndexAdPlan) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *IndexAdPlan) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpFreqSet = make([]*common.FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := common.NewFreqInfo()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.ImpFreqSet = append(p.ImpFreqSet, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdPlan) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClkFreqSet = make([]*common.FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := common.NewFreqInfo()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.ClkFreqSet = append(p.ClkFreqSet, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdPlan) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.CptCostType = IdxCostType(v)
	}
	return nil
}

func (p *IndexAdPlan) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *IndexAdPlan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdPlan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdPlan) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:costType: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:budget: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:category: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctaRate", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ctaRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CtaRate)); err != nil {
		return fmt.Errorf("%T.ctaRate (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ctaRate: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:endTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:endTime: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaCostType", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:cpaCostType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CpaCostType)); err != nil {
		return fmt.Errorf("%T.cpaCostType (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:cpaCostType: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:startTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:startTime: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField10(oprot thrift.TProtocol) (err error) {
	if p.ImpFreqSet != nil {
		if err := oprot.WriteFieldBegin("impFreqSet", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:impFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImpFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpFreqSet {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:impFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdPlan) writeField11(oprot thrift.TProtocol) (err error) {
	if p.ClkFreqSet != nil {
		if err := oprot.WriteFieldBegin("clkFreqSet", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:clkFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ClkFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClkFreqSet {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:clkFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdPlan) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cptCostType", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:cptCostType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CptCostType)); err != nil {
		return fmt.Errorf("%T.cptCostType (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:cptCostType: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:name: %s", p, err)
	}
	return err
}

func (p *IndexAdPlan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdPlan(%+v)", *p)
}

type IndexAdStrategy struct {
	Uid                    IdxUidInt                `thrift:"uid,1" json:"uid"`
	Pid                    IdxAdPlanIdInt           `thrift:"pid,2" json:"pid"`
	Sid                    IdxAdStrategyIdInt       `thrift:"sid,3" json:"sid"`
	TypeA1                 IdxAdStrategyType        `thrift:"type,4" json:"type"`
	OsTarget               []IdxOSCode              `thrift:"osTarget,5" json:"osTarget"`
	DeviceTarget           []IdxDeviceCode          `thrift:"deviceTarget,6" json:"deviceTarget"`
	AccessTarget           []IdxAccessTypeCode      `thrift:"accessTarget,7" json:"accessTarget"`
	CarrierTarget          []IdxCarrierCode         `thrift:"carrierTarget,8" json:"carrierTarget"`
	GeoTarget              []IdxRegionCode          `thrift:"geoTarget,9" json:"geoTarget"`
	AgeTarget              []IdxAgeCode             `thrift:"ageTarget,10" json:"ageTarget"`
	GenderTarget           IdxGenderCode            `thrift:"genderTarget,11" json:"genderTarget"`
	DefaultBid             IdxCurrencyAmount        `thrift:"defaultBid,12" json:"defaultBid"`
	MediatypeTarget        []MediaTargetType        `thrift:"mediatypeTarget,13" json:"mediatypeTarget"`
	GeoCityTarget          []RegionCityInt          `thrift:"geoCityTarget,14" json:"geoCityTarget"`
	DemoTagTarget          []TagIdInt               `thrift:"demoTagTarget,15" json:"demoTagTarget"`
	BlackDemoTagTarget     []DemoTagIdInt           `thrift:"blackDemoTagTarget,16" json:"blackDemoTagTarget"`
	ExtendDemoTagTarget    bool                     `thrift:"extendDemoTagTarget,17" json:"extendDemoTagTarget"`
	MediaTagTarget         []MediaTagIdInt          `thrift:"mediaTagTarget,18" json:"mediaTagTarget"`
	BlackMediaTagTarget    []MediaTagIdInt          `thrift:"blackMediaTagTarget,19" json:"blackMediaTagTarget"`
	ExtendMediaTagTarget   bool                     `thrift:"extendMediaTagTarget,20" json:"extendMediaTagTarget"`
	AdDemoTaggings         []*tag_types.DemoTagging `thrift:"adDemoTaggings,21" json:"adDemoTaggings"`
	DeviceGroupTarget      DeviceGroupIdInt         `thrift:"deviceGroupTarget,22" json:"deviceGroupTarget"`
	BlackDeviceGroupTarget DeviceGroupIdInt         `thrift:"blackDeviceGroupTarget,23" json:"blackDeviceGroupTarget"`
	JailBreakTarget        JailBreakCode            `thrift:"jailBreakTarget,24" json:"jailBreakTarget"`
	JailBreakBlack         JailBreakCode            `thrift:"jailBreakBlack,25" json:"jailBreakBlack"`
	RichMediaFormat        RichMediaCodeInt         `thrift:"richMediaFormat,26" json:"richMediaFormat"`
	AdLevel                int32                    `thrift:"adLevel,27" json:"adLevel"`
	TargetRatio            int32                    `thrift:"targetRatio,28" json:"targetRatio"`
	PositionTarget         []PositionIdInt          `thrift:"positionTarget,29" json:"positionTarget"`
	CpaBid                 IdxCurrencyAmount        `thrift:"cpaBid,30" json:"cpaBid"`
	CpaCostLimit           IdxCurrencyAmount        `thrift:"cpaCostLimit,31" json:"cpaCostLimit"`
	FirstVisitTimeTarget   TimeInt                  `thrift:"firstVisitTimeTarget,32" json:"firstVisitTimeTarget"`
	AdVisibleSetting       int32                    `thrift:"adVisibleSetting,33" json:"adVisibleSetting"`
	ExtendType             int32                    `thrift:"extendType,34" json:"extendType"`
	HasThirdPartyTracking  bool                     `thrift:"hasThirdPartyTracking,35" json:"hasThirdPartyTracking"`
}

func NewIndexAdStrategy() *IndexAdStrategy {
	return &IndexAdStrategy{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		GenderTarget: math.MinInt32 - 1, // unset sentinal value

		JailBreakTarget: math.MinInt32 - 1, // unset sentinal value

		JailBreakBlack: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexAdStrategy) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *IndexAdStrategy) IsSetGenderTarget() bool {
	return int64(p.GenderTarget) != math.MinInt32-1
}

func (p *IndexAdStrategy) IsSetJailBreakTarget() bool {
	return int64(p.JailBreakTarget) != math.MinInt32-1
}

func (p *IndexAdStrategy) IsSetJailBreakBlack() bool {
	return int64(p.JailBreakBlack) != math.MinInt32-1
}

func (p *IndexAdStrategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.LIST {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.LIST {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdStrategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = IdxUidInt(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = IdxAdPlanIdInt(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sid = IdxAdStrategyIdInt(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = IdxAdStrategyType(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OsTarget = make([]IdxOSCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 IdxOSCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = IdxOSCode(v)
		}
		p.OsTarget = append(p.OsTarget, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DeviceTarget = make([]IdxDeviceCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 IdxDeviceCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = IdxDeviceCode(v)
		}
		p.DeviceTarget = append(p.DeviceTarget, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccessTarget = make([]IdxAccessTypeCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 IdxAccessTypeCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = IdxAccessTypeCode(v)
		}
		p.AccessTarget = append(p.AccessTarget, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CarrierTarget = make([]IdxCarrierCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 IdxCarrierCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = IdxCarrierCode(v)
		}
		p.CarrierTarget = append(p.CarrierTarget, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GeoTarget = make([]IdxRegionCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 IdxRegionCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = IdxRegionCode(v)
		}
		p.GeoTarget = append(p.GeoTarget, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AgeTarget = make([]IdxAgeCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 IdxAgeCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = IdxAgeCode(v)
		}
		p.AgeTarget = append(p.AgeTarget, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.GenderTarget = IdxGenderCode(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.DefaultBid = IdxCurrencyAmount(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediatypeTarget = make([]MediaTargetType, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 MediaTargetType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = MediaTargetType(v)
		}
		p.MediatypeTarget = append(p.MediatypeTarget, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GeoCityTarget = make([]RegionCityInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 RegionCityInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = RegionCityInt(v)
		}
		p.GeoCityTarget = append(p.GeoCityTarget, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DemoTagTarget = make([]TagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 TagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = TagIdInt(v)
		}
		p.DemoTagTarget = append(p.DemoTagTarget, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.BlackDemoTagTarget = make([]DemoTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 DemoTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = DemoTagIdInt(v)
		}
		p.BlackDemoTagTarget = append(p.BlackDemoTagTarget, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.ExtendDemoTagTarget = v
	}
	return nil
}

func (p *IndexAdStrategy) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaTagTarget = make([]MediaTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 MediaTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = MediaTagIdInt(v)
		}
		p.MediaTagTarget = append(p.MediaTagTarget, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField19(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.BlackMediaTagTarget = make([]MediaTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 MediaTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = MediaTagIdInt(v)
		}
		p.BlackMediaTagTarget = append(p.BlackMediaTagTarget, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ExtendMediaTagTarget = v
	}
	return nil
}

func (p *IndexAdStrategy) readField21(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdDemoTaggings = make([]*tag_types.DemoTagging, 0, size)
	for i := 0; i < size; i++ {
		_elem15 := tag_types.NewDemoTagging()
		if err := _elem15.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem15)
		}
		p.AdDemoTaggings = append(p.AdDemoTaggings, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.DeviceGroupTarget = DeviceGroupIdInt(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.BlackDeviceGroupTarget = DeviceGroupIdInt(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.JailBreakTarget = JailBreakCode(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.JailBreakBlack = JailBreakCode(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.RichMediaFormat = RichMediaCodeInt(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.AdLevel = v
	}
	return nil
}

func (p *IndexAdStrategy) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.TargetRatio = v
	}
	return nil
}

func (p *IndexAdStrategy) readField29(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PositionTarget = make([]PositionIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem16 PositionIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem16 = PositionIdInt(v)
		}
		p.PositionTarget = append(p.PositionTarget, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdStrategy) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CpaBid = IdxCurrencyAmount(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.CpaCostLimit = IdxCurrencyAmount(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.FirstVisitTimeTarget = TimeInt(v)
	}
	return nil
}

func (p *IndexAdStrategy) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.AdVisibleSetting = v
	}
	return nil
}

func (p *IndexAdStrategy) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.ExtendType = v
	}
	return nil
}

func (p *IndexAdStrategy) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.HasThirdPartyTracking = v
	}
	return nil
}

func (p *IndexAdStrategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdStrategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sid: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:type: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField5(oprot thrift.TProtocol) (err error) {
	if p.OsTarget != nil {
		if err := oprot.WriteFieldBegin("osTarget", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:osTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OsTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OsTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:osTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField6(oprot thrift.TProtocol) (err error) {
	if p.DeviceTarget != nil {
		if err := oprot.WriteFieldBegin("deviceTarget", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:deviceTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DeviceTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DeviceTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:deviceTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField7(oprot thrift.TProtocol) (err error) {
	if p.AccessTarget != nil {
		if err := oprot.WriteFieldBegin("accessTarget", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:accessTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AccessTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccessTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:accessTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField8(oprot thrift.TProtocol) (err error) {
	if p.CarrierTarget != nil {
		if err := oprot.WriteFieldBegin("carrierTarget", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:carrierTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CarrierTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CarrierTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:carrierTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField9(oprot thrift.TProtocol) (err error) {
	if p.GeoTarget != nil {
		if err := oprot.WriteFieldBegin("geoTarget", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:geoTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.GeoTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GeoTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:geoTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField10(oprot thrift.TProtocol) (err error) {
	if p.AgeTarget != nil {
		if err := oprot.WriteFieldBegin("ageTarget", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:ageTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AgeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AgeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:ageTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("genderTarget", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:genderTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GenderTarget)); err != nil {
		return fmt.Errorf("%T.genderTarget (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:genderTarget: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("defaultBid", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:defaultBid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DefaultBid)); err != nil {
		return fmt.Errorf("%T.defaultBid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:defaultBid: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField13(oprot thrift.TProtocol) (err error) {
	if p.MediatypeTarget != nil {
		if err := oprot.WriteFieldBegin("mediatypeTarget", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:mediatypeTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediatypeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediatypeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:mediatypeTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField14(oprot thrift.TProtocol) (err error) {
	if p.GeoCityTarget != nil {
		if err := oprot.WriteFieldBegin("geoCityTarget", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:geoCityTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.GeoCityTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GeoCityTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:geoCityTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField15(oprot thrift.TProtocol) (err error) {
	if p.DemoTagTarget != nil {
		if err := oprot.WriteFieldBegin("demoTagTarget", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:demoTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DemoTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DemoTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:demoTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField16(oprot thrift.TProtocol) (err error) {
	if p.BlackDemoTagTarget != nil {
		if err := oprot.WriteFieldBegin("blackDemoTagTarget", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:blackDemoTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.BlackDemoTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.BlackDemoTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:blackDemoTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extendDemoTagTarget", thrift.BOOL, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:extendDemoTagTarget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExtendDemoTagTarget)); err != nil {
		return fmt.Errorf("%T.extendDemoTagTarget (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:extendDemoTagTarget: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField18(oprot thrift.TProtocol) (err error) {
	if p.MediaTagTarget != nil {
		if err := oprot.WriteFieldBegin("mediaTagTarget", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:mediaTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:mediaTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField19(oprot thrift.TProtocol) (err error) {
	if p.BlackMediaTagTarget != nil {
		if err := oprot.WriteFieldBegin("blackMediaTagTarget", thrift.LIST, 19); err != nil {
			return fmt.Errorf("%T write field begin error 19:blackMediaTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.BlackMediaTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.BlackMediaTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 19:blackMediaTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extendMediaTagTarget", thrift.BOOL, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:extendMediaTagTarget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExtendMediaTagTarget)); err != nil {
		return fmt.Errorf("%T.extendMediaTagTarget (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:extendMediaTagTarget: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField21(oprot thrift.TProtocol) (err error) {
	if p.AdDemoTaggings != nil {
		if err := oprot.WriteFieldBegin("adDemoTaggings", thrift.LIST, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:adDemoTaggings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdDemoTaggings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdDemoTaggings {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:adDemoTaggings: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceGroupTarget", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:deviceGroupTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceGroupTarget)); err != nil {
		return fmt.Errorf("%T.deviceGroupTarget (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:deviceGroupTarget: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("blackDeviceGroupTarget", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:blackDeviceGroupTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BlackDeviceGroupTarget)); err != nil {
		return fmt.Errorf("%T.blackDeviceGroupTarget (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:blackDeviceGroupTarget: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jailBreakTarget", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:jailBreakTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JailBreakTarget)); err != nil {
		return fmt.Errorf("%T.jailBreakTarget (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:jailBreakTarget: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jailBreakBlack", thrift.I32, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:jailBreakBlack: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.JailBreakBlack)); err != nil {
		return fmt.Errorf("%T.jailBreakBlack (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:jailBreakBlack: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("richMediaFormat", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:richMediaFormat: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RichMediaFormat)); err != nil {
		return fmt.Errorf("%T.richMediaFormat (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:richMediaFormat: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adLevel", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:adLevel: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdLevel)); err != nil {
		return fmt.Errorf("%T.adLevel (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:adLevel: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("targetRatio", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:targetRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetRatio)); err != nil {
		return fmt.Errorf("%T.targetRatio (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:targetRatio: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField29(oprot thrift.TProtocol) (err error) {
	if p.PositionTarget != nil {
		if err := oprot.WriteFieldBegin("positionTarget", thrift.LIST, 29); err != nil {
			return fmt.Errorf("%T write field begin error 29:positionTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PositionTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PositionTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 29:positionTarget: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdStrategy) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaBid", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:cpaBid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CpaBid)); err != nil {
		return fmt.Errorf("%T.cpaBid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:cpaBid: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaCostLimit", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:cpaCostLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CpaCostLimit)); err != nil {
		return fmt.Errorf("%T.cpaCostLimit (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:cpaCostLimit: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("firstVisitTimeTarget", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:firstVisitTimeTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FirstVisitTimeTarget)); err != nil {
		return fmt.Errorf("%T.firstVisitTimeTarget (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:firstVisitTimeTarget: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adVisibleSetting", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:adVisibleSetting: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdVisibleSetting)); err != nil {
		return fmt.Errorf("%T.adVisibleSetting (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:adVisibleSetting: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extendType", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:extendType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExtendType)); err != nil {
		return fmt.Errorf("%T.extendType (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:extendType: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hasThirdPartyTracking", thrift.BOOL, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:hasThirdPartyTracking: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.HasThirdPartyTracking)); err != nil {
		return fmt.Errorf("%T.hasThirdPartyTracking (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:hasThirdPartyTracking: %s", p, err)
	}
	return err
}

func (p *IndexAdStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdStrategy(%+v)", *p)
}

type IndexAdCreativeTemplate struct {
	Name          string                         `thrift:"name,1" json:"name"`
	TypeA1        IdxAdCreativeTemplateType      `thrift:"type,2" json:"type"`
	Specification IdxAdCreativeTemplateSpecIdInt `thrift:"specification,3" json:"specification"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Size      IdxTemplateSizeCode    `thrift:"size,10" json:"size"`
	Image     IdxImgIdInt            `thrift:"image,11" json:"image"`
	Icon      IdxImgIdInt            `thrift:"icon,12" json:"icon"`
	ImageType IdxImageType           `thrift:"imageType,13" json:"imageType"`
	Html      ResIdInt               `thrift:"html,14" json:"html"`
	Url       string                 `thrift:"url,15" json:"url"`
	Isize     IdxTemplateSizeCodeInt `thrift:"isize,16" json:"isize"`
	// unused field # 17
	// unused field # 18
	Title  string `thrift:"title,19" json:"title"`
	Brief  string `thrift:"brief,20" json:"brief"`
	Detail string `thrift:"detail,21" json:"detail"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	// unused field # 98
	// unused field # 99
	IsOriginal            bool                 `thrift:"isOriginal,100" json:"isOriginal"`
	ShouldIgnoreWaterMark bool                 `thrift:"shouldIgnoreWaterMark,101" json:"shouldIgnoreWaterMark"`
	WaterMarkPosition     IdxWaterMarkPosition `thrift:"waterMarkPosition,102" json:"waterMarkPosition"`
	// unused field # 103
	// unused field # 104
	// unused field # 105
	// unused field # 106
	// unused field # 107
	// unused field # 108
	// unused field # 109
	PreIssuedResource *common.ResourceGroup `thrift:"preIssuedResource,110" json:"preIssuedResource"`
}

func NewIndexAdCreativeTemplate() *IndexAdCreativeTemplate {
	return &IndexAdCreativeTemplate{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Size: math.MinInt32 - 1, // unset sentinal value

		ImageType: math.MinInt32 - 1, // unset sentinal value

		WaterMarkPosition: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexAdCreativeTemplate) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *IndexAdCreativeTemplate) IsSetSize() bool {
	return int64(p.Size) != math.MinInt32-1
}

func (p *IndexAdCreativeTemplate) IsSetImageType() bool {
	return int64(p.ImageType) != math.MinInt32-1
}

func (p *IndexAdCreativeTemplate) IsSetWaterMarkPosition() bool {
	return int64(p.WaterMarkPosition) != math.MinInt32-1
}

func (p *IndexAdCreativeTemplate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 100:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField100(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 101:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField101(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 102:
			if fieldTypeId == thrift.I32 {
				if err := p.readField102(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 110:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField110(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = IdxAdCreativeTemplateType(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Specification = IdxAdCreativeTemplateSpecIdInt(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Size = IdxTemplateSizeCode(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Image = IdxImgIdInt(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Icon = IdxImgIdInt(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField100(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 100: %s", err)
	} else {
		p.IsOriginal = v
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ImageType = IdxImageType(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Html = ResIdInt(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Isize = IdxTemplateSizeCodeInt(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Brief = v
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField101(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 101: %s", err)
	} else {
		p.ShouldIgnoreWaterMark = v
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField102(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 102: %s", err)
	} else {
		p.WaterMarkPosition = IdxWaterMarkPosition(v)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) readField110(iprot thrift.TProtocol) error {
	p.PreIssuedResource = common.NewResourceGroup()
	if err := p.PreIssuedResource.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PreIssuedResource)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdCreativeTemplate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField100(oprot); err != nil {
		return err
	}
	if err := p.writeField101(oprot); err != nil {
		return err
	}
	if err := p.writeField102(oprot); err != nil {
		return err
	}
	if err := p.writeField110(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCreativeTemplate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:type: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("specification", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:specification: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Specification)); err != nil {
		return fmt.Errorf("%T.specification (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:specification: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:size: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:image: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Image)); err != nil {
		return fmt.Errorf("%T.image (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:image: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:icon: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Icon)); err != nil {
		return fmt.Errorf("%T.icon (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:icon: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imageType", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:imageType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImageType)); err != nil {
		return fmt.Errorf("%T.imageType (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:imageType: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("html", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:html: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Html)); err != nil {
		return fmt.Errorf("%T.html (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:html: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:url: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isize", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:isize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Isize)); err != nil {
		return fmt.Errorf("%T.isize (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:isize: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:title: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brief", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:brief: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Brief)); err != nil {
		return fmt.Errorf("%T.brief (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:brief: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:detail: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField100(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOriginal", thrift.BOOL, 100); err != nil {
		return fmt.Errorf("%T write field begin error 100:isOriginal: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOriginal)); err != nil {
		return fmt.Errorf("%T.isOriginal (100) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 100:isOriginal: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField101(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shouldIgnoreWaterMark", thrift.BOOL, 101); err != nil {
		return fmt.Errorf("%T write field begin error 101:shouldIgnoreWaterMark: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ShouldIgnoreWaterMark)); err != nil {
		return fmt.Errorf("%T.shouldIgnoreWaterMark (101) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 101:shouldIgnoreWaterMark: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField102(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("waterMarkPosition", thrift.I32, 102); err != nil {
		return fmt.Errorf("%T write field begin error 102:waterMarkPosition: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WaterMarkPosition)); err != nil {
		return fmt.Errorf("%T.waterMarkPosition (102) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 102:waterMarkPosition: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeTemplate) writeField110(oprot thrift.TProtocol) (err error) {
	if p.PreIssuedResource != nil {
		if err := oprot.WriteFieldBegin("preIssuedResource", thrift.STRUCT, 110); err != nil {
			return fmt.Errorf("%T write field begin error 110:preIssuedResource: %s", p, err)
		}
		if err := p.PreIssuedResource.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PreIssuedResource)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 110:preIssuedResource: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreativeTemplate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdCreativeTemplate(%+v)", *p)
}

type IndexAdCreativeMaterial struct {
	Templates []*IndexAdCreativeTemplate `thrift:"templates,1" json:"templates"`
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	LongText         string                `thrift:"long_text,9" json:"long_text"`
	Text             string                `thrift:"text,10" json:"text"`
	IconType         IdxAdCreativeIconType `thrift:"iconType,11" json:"iconType"`
	ForeColor        string                `thrift:"foreColor,12" json:"foreColor"`
	BgColor          string                `thrift:"bgColor,13" json:"bgColor"`
	DisplayUrl       string                `thrift:"displayUrl,14" json:"displayUrl"`
	LandingDirection IdxLandingDirection   `thrift:"landingDirection,15" json:"landingDirection"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	JsonInfo string `thrift:"jsonInfo,20" json:"jsonInfo"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	AltText      string              `thrift:"altText,30" json:"altText"`
	TemplateCode HtmlTemplateCodeInt `thrift:"templateCode,31" json:"templateCode"`
	RenderTypes  []AdRenderType      `thrift:"renderTypes,32" json:"renderTypes"`
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	ActionUrl             string  `thrift:"actionUrl,64" json:"actionUrl"`
	Longitude             float64 `thrift:"longitude,65" json:"longitude"`
	Latitude              float64 `thrift:"latitude,66" json:"latitude"`
	ActionCall            string  `thrift:"actionCall,67" json:"actionCall"`
	ActionSMSNumber       string  `thrift:"actionSMSNumber,68" json:"actionSMSNumber"`
	ActionSMSMessage      string  `thrift:"actionSMSMessage,69" json:"actionSMSMessage"`
	ActionMailAddress     string  `thrift:"actionMailAddress,70" json:"actionMailAddress"`
	ActionMailSubject     string  `thrift:"actionMailSubject,71" json:"actionMailSubject"`
	ActionMailBody        string  `thrift:"actionMailBody,72" json:"actionMailBody"`
	ActionUrlFailsafe     string  `thrift:"actionUrlFailsafe,73" json:"actionUrlFailsafe"`
	ImpressionTrackingUrl string  `thrift:"impressionTrackingUrl,74" json:"impressionTrackingUrl"`
	ClickTrackingUrl      string  `thrift:"clickTrackingUrl,75" json:"clickTrackingUrl"`
}

func NewIndexAdCreativeMaterial() *IndexAdCreativeMaterial {
	return &IndexAdCreativeMaterial{
		IconType: math.MinInt32 - 1, // unset sentinal value

		LandingDirection: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexAdCreativeMaterial) IsSetIconType() bool {
	return int64(p.IconType) != math.MinInt32-1
}

func (p *IndexAdCreativeMaterial) IsSetLandingDirection() bool {
	return int64(p.LandingDirection) != math.MinInt32-1
}

func (p *IndexAdCreativeMaterial) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.LIST {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.STRING {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 65:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField65(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 66:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField66(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 67:
			if fieldTypeId == thrift.STRING {
				if err := p.readField67(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 68:
			if fieldTypeId == thrift.STRING {
				if err := p.readField68(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 69:
			if fieldTypeId == thrift.STRING {
				if err := p.readField69(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 70:
			if fieldTypeId == thrift.STRING {
				if err := p.readField70(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.STRING {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.STRING {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 73:
			if fieldTypeId == thrift.STRING {
				if err := p.readField73(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 74:
			if fieldTypeId == thrift.STRING {
				if err := p.readField74(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 75:
			if fieldTypeId == thrift.STRING {
				if err := p.readField75(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Templates = make([]*IndexAdCreativeTemplate, 0, size)
	for i := 0; i < size; i++ {
		_elem17 := NewIndexAdCreativeTemplate()
		if err := _elem17.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem17)
		}
		p.Templates = append(p.Templates, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.LongText = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.IconType = IdxAdCreativeIconType(v)
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ForeColor = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.BgColor = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DisplayUrl = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.LandingDirection = IdxLandingDirection(v)
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.JsonInfo = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.AltText = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.TemplateCode = HtmlTemplateCodeInt(v)
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField32(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RenderTypes = make([]AdRenderType, 0, size)
	for i := 0; i < size; i++ {
		var _elem18 AdRenderType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem18 = AdRenderType(v)
		}
		p.RenderTypes = append(p.RenderTypes, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField65(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 65: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField66(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 66: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField67(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 67: %s", err)
	} else {
		p.ActionCall = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField68(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 68: %s", err)
	} else {
		p.ActionSMSNumber = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField69(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 69: %s", err)
	} else {
		p.ActionSMSMessage = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField70(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 70: %s", err)
	} else {
		p.ActionMailAddress = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.ActionMailSubject = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField72(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 72: %s", err)
	} else {
		p.ActionMailBody = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField73(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 73: %s", err)
	} else {
		p.ActionUrlFailsafe = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField74(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 74: %s", err)
	} else {
		p.ImpressionTrackingUrl = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) readField75(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 75: %s", err)
	} else {
		p.ClickTrackingUrl = v
	}
	return nil
}

func (p *IndexAdCreativeMaterial) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdCreativeMaterial"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := p.writeField65(oprot); err != nil {
		return err
	}
	if err := p.writeField66(oprot); err != nil {
		return err
	}
	if err := p.writeField67(oprot); err != nil {
		return err
	}
	if err := p.writeField68(oprot); err != nil {
		return err
	}
	if err := p.writeField69(oprot); err != nil {
		return err
	}
	if err := p.writeField70(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField73(oprot); err != nil {
		return err
	}
	if err := p.writeField74(oprot); err != nil {
		return err
	}
	if err := p.writeField75(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCreativeMaterial) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Templates != nil {
		if err := oprot.WriteFieldBegin("templates", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:templates: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Templates)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Templates {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:templates: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("long_text", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:long_text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LongText)); err != nil {
		return fmt.Errorf("%T.long_text (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:long_text: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:text: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iconType", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:iconType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IconType)); err != nil {
		return fmt.Errorf("%T.iconType (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:iconType: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("foreColor", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:foreColor: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ForeColor)); err != nil {
		return fmt.Errorf("%T.foreColor (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:foreColor: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bgColor", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:bgColor: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BgColor)); err != nil {
		return fmt.Errorf("%T.bgColor (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:bgColor: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayUrl", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:displayUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayUrl)); err != nil {
		return fmt.Errorf("%T.displayUrl (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:displayUrl: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landingDirection", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:landingDirection: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LandingDirection)); err != nil {
		return fmt.Errorf("%T.landingDirection (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:landingDirection: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jsonInfo", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:jsonInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.JsonInfo)); err != nil {
		return fmt.Errorf("%T.jsonInfo (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:jsonInfo: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("altText", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:altText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AltText)); err != nil {
		return fmt.Errorf("%T.altText (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:altText: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("templateCode", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:templateCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TemplateCode)); err != nil {
		return fmt.Errorf("%T.templateCode (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:templateCode: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField32(oprot thrift.TProtocol) (err error) {
	if p.RenderTypes != nil {
		if err := oprot.WriteFieldBegin("renderTypes", thrift.LIST, 32); err != nil {
			return fmt.Errorf("%T write field begin error 32:renderTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RenderTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RenderTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 32:renderTypes: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionUrl", thrift.STRING, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:actionUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.actionUrl (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:actionUrl: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField65(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 65); err != nil {
		return fmt.Errorf("%T write field begin error 65:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (65) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 65:longitude: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField66(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 66); err != nil {
		return fmt.Errorf("%T write field begin error 66:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (66) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 66:latitude: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField67(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionCall", thrift.STRING, 67); err != nil {
		return fmt.Errorf("%T write field begin error 67:actionCall: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionCall)); err != nil {
		return fmt.Errorf("%T.actionCall (67) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 67:actionCall: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField68(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionSMSNumber", thrift.STRING, 68); err != nil {
		return fmt.Errorf("%T write field begin error 68:actionSMSNumber: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionSMSNumber)); err != nil {
		return fmt.Errorf("%T.actionSMSNumber (68) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 68:actionSMSNumber: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField69(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionSMSMessage", thrift.STRING, 69); err != nil {
		return fmt.Errorf("%T write field begin error 69:actionSMSMessage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionSMSMessage)); err != nil {
		return fmt.Errorf("%T.actionSMSMessage (69) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 69:actionSMSMessage: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField70(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailAddress", thrift.STRING, 70); err != nil {
		return fmt.Errorf("%T write field begin error 70:actionMailAddress: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailAddress)); err != nil {
		return fmt.Errorf("%T.actionMailAddress (70) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 70:actionMailAddress: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailSubject", thrift.STRING, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:actionMailSubject: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailSubject)); err != nil {
		return fmt.Errorf("%T.actionMailSubject (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:actionMailSubject: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField72(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailBody", thrift.STRING, 72); err != nil {
		return fmt.Errorf("%T write field begin error 72:actionMailBody: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailBody)); err != nil {
		return fmt.Errorf("%T.actionMailBody (72) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 72:actionMailBody: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField73(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionUrlFailsafe", thrift.STRING, 73); err != nil {
		return fmt.Errorf("%T write field begin error 73:actionUrlFailsafe: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrlFailsafe)); err != nil {
		return fmt.Errorf("%T.actionUrlFailsafe (73) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 73:actionUrlFailsafe: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField74(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionTrackingUrl", thrift.STRING, 74); err != nil {
		return fmt.Errorf("%T write field begin error 74:impressionTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpressionTrackingUrl)); err != nil {
		return fmt.Errorf("%T.impressionTrackingUrl (74) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 74:impressionTrackingUrl: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) writeField75(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickTrackingUrl", thrift.STRING, 75); err != nil {
		return fmt.Errorf("%T write field begin error 75:clickTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickTrackingUrl)); err != nil {
		return fmt.Errorf("%T.clickTrackingUrl (75) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 75:clickTrackingUrl: %s", p, err)
	}
	return err
}

func (p *IndexAdCreativeMaterial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdCreativeMaterial(%+v)", *p)
}

type IndexAdTermSign struct {
	Sign1 int32 `thrift:"sign1,1" json:"sign1"`
	Sign2 int32 `thrift:"sign2,2" json:"sign2"`
}

func NewIndexAdTermSign() *IndexAdTermSign {
	return &IndexAdTermSign{}
}

func (p *IndexAdTermSign) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdTermSign) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Sign1 = v
	}
	return nil
}

func (p *IndexAdTermSign) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sign2 = v
	}
	return nil
}

func (p *IndexAdTermSign) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdTermSign"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdTermSign) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign1", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sign1: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sign1)); err != nil {
		return fmt.Errorf("%T.sign1 (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sign1: %s", p, err)
	}
	return err
}

func (p *IndexAdTermSign) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign2", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sign2: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sign2)); err != nil {
		return fmt.Errorf("%T.sign2 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sign2: %s", p, err)
	}
	return err
}

func (p *IndexAdTermSign) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdTermSign(%+v)", *p)
}

type IndexAdTermInfo struct {
	Sign   *IndexAdTermSign `thrift:"sign,1" json:"sign"`
	Idx    int32            `thrift:"idx,2" json:"idx"`
	Weight float64          `thrift:"weight,3" json:"weight"`
}

func NewIndexAdTermInfo() *IndexAdTermInfo {
	return &IndexAdTermInfo{}
}

func (p *IndexAdTermInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdTermInfo) readField1(iprot thrift.TProtocol) error {
	p.Sign = NewIndexAdTermSign()
	if err := p.Sign.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Sign)
	}
	return nil
}

func (p *IndexAdTermInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Idx = v
	}
	return nil
}

func (p *IndexAdTermInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Weight = v
	}
	return nil
}

func (p *IndexAdTermInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdTermInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdTermInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Sign != nil {
		if err := oprot.WriteFieldBegin("sign", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sign: %s", p, err)
		}
		if err := p.Sign.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Sign)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sign: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdTermInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idx", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:idx: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Idx)); err != nil {
		return fmt.Errorf("%T.idx (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:idx: %s", p, err)
	}
	return err
}

func (p *IndexAdTermInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weight", thrift.DOUBLE, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:weight: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Weight)); err != nil {
		return fmt.Errorf("%T.weight (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:weight: %s", p, err)
	}
	return err
}

func (p *IndexAdTermInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdTermInfo(%+v)", *p)
}

type IndexAdCreative struct {
	Uid                   IdxUidInt                `thrift:"uid,1" json:"uid"`
	Pid                   IdxAdPlanIdInt           `thrift:"pid,2" json:"pid"`
	Sid                   IdxAdStrategyIdInt       `thrift:"sid,3" json:"sid"`
	Cid                   IdxAdCreativeIdInt       `thrift:"cid,4" json:"cid"`
	Keywords              string                   `thrift:"keywords,5" json:"keywords"`
	DisplayType           IdxAdCreativeType        `thrift:"displayType,6" json:"displayType"`
	Bid                   IdxCurrencyAmount        `thrift:"bid,7" json:"bid"`
	AppName               string                   `thrift:"appName,8" json:"appName"`
	ActionType            IdxAdActionType          `thrift:"actionType,9" json:"actionType"`
	Material              *IndexAdCreativeMaterial `thrift:"material,10" json:"material"`
	Addkeywords           string                   `thrift:"addkeywords,11" json:"addkeywords"`
	Terminfo              []*IndexAdTermInfo       `thrift:"terminfo,12" json:"terminfo"`
	TermWeight            float64                  `thrift:"term_weight,13" json:"term_weight"`
	UrlOpenType           IdxSDKUrlOpenType        `thrift:"urlOpenType,14" json:"urlOpenType"`
	IsPreload             bool                     `thrift:"is_preload,15" json:"is_preload"`
	IsAutoRun             bool                     `thrift:"is_auto_run,16" json:"is_auto_run"`
	IsClickComfirm        bool                     `thrift:"is_click_comfirm,17" json:"is_click_comfirm"`
	DownAppName           string                   `thrift:"down_app_name,18" json:"down_app_name"`
	PackageName           string                   `thrift:"packageName,19" json:"packageName"`
	Version               string                   `thrift:"version,20" json:"version"`
	MinRequiredSDKVersion map[int32]int32          `thrift:"minRequiredSDKVersion,21" json:"minRequiredSDKVersion"`
	MediaBid              IdxCurrencyAmount        `thrift:"media_bid,22" json:"media_bid"`
	PlacementType         IdxAdPlacementType       `thrift:"placementType,23" json:"placementType"`
	EffectiveShowTime     TimeInt                  `thrift:"effectiveShowTime,24" json:"effectiveShowTime"`
	CompleteShowTime      TimeInt                  `thrift:"completeShowTime,25" json:"completeShowTime"`
	AutoClose             bool                     `thrift:"autoClose,26" json:"autoClose"`
	DisableRotate         bool                     `thrift:"disableRotate,27" json:"disableRotate"`
	RealBid               IdxCurrencyAmount        `thrift:"real_bid,28" json:"real_bid"`
	RequiredCapability    []CapabilityIdInt        `thrift:"requiredCapability,29" json:"requiredCapability"`
	CloseButtonPosition   CloseButtonPosition      `thrift:"closeButtonPosition,30" json:"closeButtonPosition"`
	ExtInfo               map[string]string        `thrift:"extInfo,31" json:"extInfo"`
	VersionCode           IdxIdInt                 `thrift:"versionCode,32" json:"versionCode"`
	AppId                 int32                    `thrift:"appId,33" json:"appId"`
	ChnId                 int32                    `thrift:"chnId,34" json:"chnId"`
	IsTXO2Ad              bool                     `thrift:"isTXO2Ad,35" json:"isTXO2Ad"`
	AppkeySign            int32                    `thrift:"appkeySign,36" json:"appkeySign"`
}

func NewIndexAdCreative() *IndexAdCreative {
	return &IndexAdCreative{
		DisplayType: math.MinInt32 - 1, // unset sentinal value

		ActionType: math.MinInt32 - 1, // unset sentinal value

		UrlOpenType: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value

		CloseButtonPosition: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *IndexAdCreative) IsSetDisplayType() bool {
	return int64(p.DisplayType) != math.MinInt32-1
}

func (p *IndexAdCreative) IsSetActionType() bool {
	return int64(p.ActionType) != math.MinInt32-1
}

func (p *IndexAdCreative) IsSetUrlOpenType() bool {
	return int64(p.UrlOpenType) != math.MinInt32-1
}

func (p *IndexAdCreative) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *IndexAdCreative) IsSetCloseButtonPosition() bool {
	return int64(p.CloseButtonPosition) != math.MinInt32-1
}

func (p *IndexAdCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.MAP {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I64 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.LIST {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.MAP {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = IdxUidInt(v)
	}
	return nil
}

func (p *IndexAdCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = IdxAdPlanIdInt(v)
	}
	return nil
}

func (p *IndexAdCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sid = IdxAdStrategyIdInt(v)
	}
	return nil
}

func (p *IndexAdCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = IdxAdCreativeIdInt(v)
	}
	return nil
}

func (p *IndexAdCreative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Keywords = v
	}
	return nil
}

func (p *IndexAdCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DisplayType = IdxAdCreativeType(v)
	}
	return nil
}

func (p *IndexAdCreative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Bid = IdxCurrencyAmount(v)
	}
	return nil
}

func (p *IndexAdCreative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *IndexAdCreative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ActionType = IdxAdActionType(v)
	}
	return nil
}

func (p *IndexAdCreative) readField10(iprot thrift.TProtocol) error {
	p.Material = NewIndexAdCreativeMaterial()
	if err := p.Material.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Material)
	}
	return nil
}

func (p *IndexAdCreative) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Addkeywords = v
	}
	return nil
}

func (p *IndexAdCreative) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Terminfo = make([]*IndexAdTermInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem19 := NewIndexAdTermInfo()
		if err := _elem19.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem19)
		}
		p.Terminfo = append(p.Terminfo, _elem19)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdCreative) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.TermWeight = v
	}
	return nil
}

func (p *IndexAdCreative) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.UrlOpenType = IdxSDKUrlOpenType(v)
	}
	return nil
}

func (p *IndexAdCreative) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.IsPreload = v
	}
	return nil
}

func (p *IndexAdCreative) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.IsAutoRun = v
	}
	return nil
}

func (p *IndexAdCreative) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.IsClickComfirm = v
	}
	return nil
}

func (p *IndexAdCreative) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.DownAppName = v
	}
	return nil
}

func (p *IndexAdCreative) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *IndexAdCreative) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *IndexAdCreative) readField21(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.MinRequiredSDKVersion = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key20 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key20 = v
		}
		var _val21 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val21 = v
		}
		p.MinRequiredSDKVersion[_key20] = _val21
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *IndexAdCreative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.MediaBid = IdxCurrencyAmount(v)
	}
	return nil
}

func (p *IndexAdCreative) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.PlacementType = IdxAdPlacementType(v)
	}
	return nil
}

func (p *IndexAdCreative) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.EffectiveShowTime = TimeInt(v)
	}
	return nil
}

func (p *IndexAdCreative) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.CompleteShowTime = TimeInt(v)
	}
	return nil
}

func (p *IndexAdCreative) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.AutoClose = v
	}
	return nil
}

func (p *IndexAdCreative) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.DisableRotate = v
	}
	return nil
}

func (p *IndexAdCreative) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.RealBid = IdxCurrencyAmount(v)
	}
	return nil
}

func (p *IndexAdCreative) readField29(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RequiredCapability = make([]CapabilityIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem22 CapabilityIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem22 = CapabilityIdInt(v)
		}
		p.RequiredCapability = append(p.RequiredCapability, _elem22)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAdCreative) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CloseButtonPosition = CloseButtonPosition(v)
	}
	return nil
}

func (p *IndexAdCreative) readField31(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key23 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key23 = v
		}
		var _val24 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val24 = v
		}
		p.ExtInfo[_key23] = _val24
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *IndexAdCreative) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.VersionCode = IdxIdInt(v)
	}
	return nil
}

func (p *IndexAdCreative) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *IndexAdCreative) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *IndexAdCreative) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.IsTXO2Ad = v
	}
	return nil
}

func (p *IndexAdCreative) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.AppkeySign = v
	}
	return nil
}

func (p *IndexAdCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sid: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keywords", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:keywords: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keywords)); err != nil {
		return fmt.Errorf("%T.keywords (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:keywords: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayType", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:displayType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.displayType (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:displayType: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:bid: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appName", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:appName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.appName (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:appName: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionType", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:actionType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return fmt.Errorf("%T.actionType (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:actionType: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Material != nil {
		if err := oprot.WriteFieldBegin("material", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:material: %s", p, err)
		}
		if err := p.Material.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Material)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:material: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreative) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("addkeywords", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:addkeywords: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Addkeywords)); err != nil {
		return fmt.Errorf("%T.addkeywords (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:addkeywords: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField12(oprot thrift.TProtocol) (err error) {
	if p.Terminfo != nil {
		if err := oprot.WriteFieldBegin("terminfo", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:terminfo: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Terminfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Terminfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:terminfo: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreative) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("term_weight", thrift.DOUBLE, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:term_weight: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.TermWeight)); err != nil {
		return fmt.Errorf("%T.term_weight (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:term_weight: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("urlOpenType", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:urlOpenType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UrlOpenType)); err != nil {
		return fmt.Errorf("%T.urlOpenType (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:urlOpenType: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_preload", thrift.BOOL, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:is_preload: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsPreload)); err != nil {
		return fmt.Errorf("%T.is_preload (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:is_preload: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_auto_run", thrift.BOOL, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:is_auto_run: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAutoRun)); err != nil {
		return fmt.Errorf("%T.is_auto_run (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:is_auto_run: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_click_comfirm", thrift.BOOL, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:is_click_comfirm: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsClickComfirm)); err != nil {
		return fmt.Errorf("%T.is_click_comfirm (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:is_click_comfirm: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("down_app_name", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:down_app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DownAppName)); err != nil {
		return fmt.Errorf("%T.down_app_name (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:down_app_name: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:packageName: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:version: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField21(oprot thrift.TProtocol) (err error) {
	if p.MinRequiredSDKVersion != nil {
		if err := oprot.WriteFieldBegin("minRequiredSDKVersion", thrift.MAP, 21); err != nil {
			return fmt.Errorf("%T write field begin error 21:minRequiredSDKVersion: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.MinRequiredSDKVersion)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.MinRequiredSDKVersion {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 21:minRequiredSDKVersion: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreative) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_bid", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:media_bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaBid)); err != nil {
		return fmt.Errorf("%T.media_bid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:media_bid: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:placementType: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveShowTime", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:effectiveShowTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EffectiveShowTime)); err != nil {
		return fmt.Errorf("%T.effectiveShowTime (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:effectiveShowTime: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("completeShowTime", thrift.I32, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:completeShowTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CompleteShowTime)); err != nil {
		return fmt.Errorf("%T.completeShowTime (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:completeShowTime: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("autoClose", thrift.BOOL, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:autoClose: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AutoClose)); err != nil {
		return fmt.Errorf("%T.autoClose (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:autoClose: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disableRotate", thrift.BOOL, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:disableRotate: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.DisableRotate)); err != nil {
		return fmt.Errorf("%T.disableRotate (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:disableRotate: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("real_bid", thrift.I64, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:real_bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RealBid)); err != nil {
		return fmt.Errorf("%T.real_bid (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:real_bid: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField29(oprot thrift.TProtocol) (err error) {
	if p.RequiredCapability != nil {
		if err := oprot.WriteFieldBegin("requiredCapability", thrift.LIST, 29); err != nil {
			return fmt.Errorf("%T write field begin error 29:requiredCapability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RequiredCapability)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RequiredCapability {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 29:requiredCapability: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreative) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closeButtonPosition", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:closeButtonPosition: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CloseButtonPosition)); err != nil {
		return fmt.Errorf("%T.closeButtonPosition (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:closeButtonPosition: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField31(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *IndexAdCreative) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionCode", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:versionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.versionCode (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:versionCode: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:appId: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:chnId: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isTXO2Ad", thrift.BOOL, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:isTXO2Ad: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsTXO2Ad)); err != nil {
		return fmt.Errorf("%T.isTXO2Ad (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:isTXO2Ad: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appkeySign", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:appkeySign: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppkeySign)); err != nil {
		return fmt.Errorf("%T.appkeySign (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:appkeySign: %s", p, err)
	}
	return err
}

func (p *IndexAdCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdCreative(%+v)", *p)
}

type IndexAdMeta struct {
	UserNum     int32 `thrift:"user_num,1" json:"user_num"`
	PlanNum     int32 `thrift:"plan_num,2" json:"plan_num"`
	StryNum     int32 `thrift:"stry_num,3" json:"stry_num"`
	CreativeNum int32 `thrift:"creative_num,4" json:"creative_num"`
	BrandAdNum  int32 `thrift:"brand_ad_num,5" json:"brand_ad_num"`
}

func NewIndexAdMeta() *IndexAdMeta {
	return &IndexAdMeta{}
}

func (p *IndexAdMeta) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdMeta) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.UserNum = v
	}
	return nil
}

func (p *IndexAdMeta) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanNum = v
	}
	return nil
}

func (p *IndexAdMeta) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StryNum = v
	}
	return nil
}

func (p *IndexAdMeta) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CreativeNum = v
	}
	return nil
}

func (p *IndexAdMeta) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.BrandAdNum = v
	}
	return nil
}

func (p *IndexAdMeta) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdMeta"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_num", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:user_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserNum)); err != nil {
		return fmt.Errorf("%T.user_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:user_num: %s", p, err)
	}
	return err
}

func (p *IndexAdMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:plan_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanNum)); err != nil {
		return fmt.Errorf("%T.plan_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:plan_num: %s", p, err)
	}
	return err
}

func (p *IndexAdMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stry_num", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:stry_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StryNum)); err != nil {
		return fmt.Errorf("%T.stry_num (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:stry_num: %s", p, err)
	}
	return err
}

func (p *IndexAdMeta) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_num", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:creative_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeNum)); err != nil {
		return fmt.Errorf("%T.creative_num (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:creative_num: %s", p, err)
	}
	return err
}

func (p *IndexAdMeta) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brand_ad_num", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:brand_ad_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BrandAdNum)); err != nil {
		return fmt.Errorf("%T.brand_ad_num (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:brand_ad_num: %s", p, err)
	}
	return err
}

func (p *IndexAdMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdMeta(%+v)", *p)
}

type IndexAdCategoryMeta struct {
	FeaNum            int32 `thrift:"fea_num,1" json:"fea_num"`
	UnitNum           int32 `thrift:"unit_num,2" json:"unit_num"`
	MaxFeaLen         int32 `thrift:"max_fea_len,3" json:"max_fea_len"`
	StrategyFeaNum    int32 `thrift:"strategy_fea_num,4" json:"strategy_fea_num"`
	StrategyUnitNum   int32 `thrift:"strategy_unit_num,5" json:"strategy_unit_num"`
	StrategyMaxFeaLen int32 `thrift:"strategy_max_fea_len,6" json:"strategy_max_fea_len"`
}

func NewIndexAdCategoryMeta() *IndexAdCategoryMeta {
	return &IndexAdCategoryMeta{}
}

func (p *IndexAdCategoryMeta) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCategoryMeta) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FeaNum = v
	}
	return nil
}

func (p *IndexAdCategoryMeta) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.UnitNum = v
	}
	return nil
}

func (p *IndexAdCategoryMeta) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MaxFeaLen = v
	}
	return nil
}

func (p *IndexAdCategoryMeta) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StrategyFeaNum = v
	}
	return nil
}

func (p *IndexAdCategoryMeta) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StrategyUnitNum = v
	}
	return nil
}

func (p *IndexAdCategoryMeta) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StrategyMaxFeaLen = v
	}
	return nil
}

func (p *IndexAdCategoryMeta) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdCategoryMeta"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCategoryMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fea_num", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fea_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FeaNum)); err != nil {
		return fmt.Errorf("%T.fea_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fea_num: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unit_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:unit_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UnitNum)); err != nil {
		return fmt.Errorf("%T.unit_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:unit_num: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_fea_len", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:max_fea_len: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxFeaLen)); err != nil {
		return fmt.Errorf("%T.max_fea_len (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:max_fea_len: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryMeta) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_fea_num", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:strategy_fea_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyFeaNum)); err != nil {
		return fmt.Errorf("%T.strategy_fea_num (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:strategy_fea_num: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryMeta) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_unit_num", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:strategy_unit_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyUnitNum)); err != nil {
		return fmt.Errorf("%T.strategy_unit_num (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:strategy_unit_num: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryMeta) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_max_fea_len", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:strategy_max_fea_len: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyMaxFeaLen)); err != nil {
		return fmt.Errorf("%T.strategy_max_fea_len (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:strategy_max_fea_len: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdCategoryMeta(%+v)", *p)
}

type IndexAdCategoryFeaHeader struct {
	FeaSign int64 `thrift:"fea_sign,1" json:"fea_sign"`
	AdNum   int32 `thrift:"ad_num,2" json:"ad_num"`
}

func NewIndexAdCategoryFeaHeader() *IndexAdCategoryFeaHeader {
	return &IndexAdCategoryFeaHeader{}
}

func (p *IndexAdCategoryFeaHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCategoryFeaHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FeaSign = v
	}
	return nil
}

func (p *IndexAdCategoryFeaHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdNum = v
	}
	return nil
}

func (p *IndexAdCategoryFeaHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdCategoryFeaHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCategoryFeaHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fea_sign", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fea_sign: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FeaSign)); err != nil {
		return fmt.Errorf("%T.fea_sign (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fea_sign: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryFeaHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ad_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdNum)); err != nil {
		return fmt.Errorf("%T.ad_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ad_num: %s", p, err)
	}
	return err
}

func (p *IndexAdCategoryFeaHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdCategoryFeaHeader(%+v)", *p)
}

type IndexAdCategory struct {
	AdId    IdxIdInt `thrift:"ad_id,1" json:"ad_id"`
	AdLevel int32    `thrift:"ad_level,2" json:"ad_level"`
	Weight  int32    `thrift:"weight,3" json:"weight"`
}

func NewIndexAdCategory() *IndexAdCategory {
	return &IndexAdCategory{}
}

func (p *IndexAdCategory) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCategory) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdId = IdxIdInt(v)
	}
	return nil
}

func (p *IndexAdCategory) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdLevel = v
	}
	return nil
}

func (p *IndexAdCategory) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Weight = v
	}
	return nil
}

func (p *IndexAdCategory) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAdCategory"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAdCategory) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ad_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdId)); err != nil {
		return fmt.Errorf("%T.ad_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ad_id: %s", p, err)
	}
	return err
}

func (p *IndexAdCategory) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_level", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ad_level: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdLevel)); err != nil {
		return fmt.Errorf("%T.ad_level (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ad_level: %s", p, err)
	}
	return err
}

func (p *IndexAdCategory) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weight", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:weight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Weight)); err != nil {
		return fmt.Errorf("%T.weight (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:weight: %s", p, err)
	}
	return err
}

func (p *IndexAdCategory) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAdCategory(%+v)", *p)
}

type IndexNewSdkIndex struct {
	SdkIndex []int32 `thrift:"sdk_index,1" json:"sdk_index"`
}

func NewIndexNewSdkIndex() *IndexNewSdkIndex {
	return &IndexNewSdkIndex{}
}

func (p *IndexNewSdkIndex) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexNewSdkIndex) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SdkIndex = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem25 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem25 = v
		}
		p.SdkIndex = append(p.SdkIndex, _elem25)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexNewSdkIndex) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexNewSdkIndex"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexNewSdkIndex) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SdkIndex != nil {
		if err := oprot.WriteFieldBegin("sdk_index", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:sdk_index: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SdkIndex)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SdkIndex {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:sdk_index: %s", p, err)
		}
	}
	return err
}

func (p *IndexNewSdkIndex) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexNewSdkIndex(%+v)", *p)
}

type IndexStrategyCreativeMeta struct {
	StrategyNum    int32 `thrift:"strategy_num,1" json:"strategy_num"`
	TotCreativeNum int32 `thrift:"tot_creative_num,2" json:"tot_creative_num"`
	MaxListLen     int32 `thrift:"max_list_len,3" json:"max_list_len"`
}

func NewIndexStrategyCreativeMeta() *IndexStrategyCreativeMeta {
	return &IndexStrategyCreativeMeta{}
}

func (p *IndexStrategyCreativeMeta) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexStrategyCreativeMeta) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StrategyNum = v
	}
	return nil
}

func (p *IndexStrategyCreativeMeta) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TotCreativeNum = v
	}
	return nil
}

func (p *IndexStrategyCreativeMeta) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MaxListLen = v
	}
	return nil
}

func (p *IndexStrategyCreativeMeta) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexStrategyCreativeMeta"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexStrategyCreativeMeta) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_num", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:strategy_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyNum)); err != nil {
		return fmt.Errorf("%T.strategy_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:strategy_num: %s", p, err)
	}
	return err
}

func (p *IndexStrategyCreativeMeta) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tot_creative_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tot_creative_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotCreativeNum)); err != nil {
		return fmt.Errorf("%T.tot_creative_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tot_creative_num: %s", p, err)
	}
	return err
}

func (p *IndexStrategyCreativeMeta) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_list_len", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:max_list_len: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxListLen)); err != nil {
		return fmt.Errorf("%T.max_list_len (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:max_list_len: %s", p, err)
	}
	return err
}

func (p *IndexStrategyCreativeMeta) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexStrategyCreativeMeta(%+v)", *p)
}

type IndexStrategyCreativeHead struct {
	StrategyId IdxAdStrategyIdInt `thrift:"strategy_id,1" json:"strategy_id"`
	ListNum    int32              `thrift:"list_num,2" json:"list_num"`
}

func NewIndexStrategyCreativeHead() *IndexStrategyCreativeHead {
	return &IndexStrategyCreativeHead{}
}

func (p *IndexStrategyCreativeHead) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexStrategyCreativeHead) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StrategyId = IdxAdStrategyIdInt(v)
	}
	return nil
}

func (p *IndexStrategyCreativeHead) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ListNum = v
	}
	return nil
}

func (p *IndexStrategyCreativeHead) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexStrategyCreativeHead"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexStrategyCreativeHead) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:strategy_id: %s", p, err)
	}
	return err
}

func (p *IndexStrategyCreativeHead) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("list_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:list_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ListNum)); err != nil {
		return fmt.Errorf("%T.list_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:list_num: %s", p, err)
	}
	return err
}

func (p *IndexStrategyCreativeHead) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexStrategyCreativeHead(%+v)", *p)
}

type IndexStrategyCreativeIndex struct {
	CreativeId IdxAdCreativeIdInt `thrift:"creative_id,1" json:"creative_id"`
}

func NewIndexStrategyCreativeIndex() *IndexStrategyCreativeIndex {
	return &IndexStrategyCreativeIndex{}
}

func (p *IndexStrategyCreativeIndex) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexStrategyCreativeIndex) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CreativeId = IdxAdCreativeIdInt(v)
	}
	return nil
}

func (p *IndexStrategyCreativeIndex) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexStrategyCreativeIndex"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexStrategyCreativeIndex) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:creative_id: %s", p, err)
	}
	return err
}

func (p *IndexStrategyCreativeIndex) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexStrategyCreativeIndex(%+v)", *p)
}

type IndexAppWallStrategyIndex struct {
	Creatives  []int32 `thrift:"creatives,1" json:"creatives"`
	MaxRealBid int64   `thrift:"max_real_bid,2" json:"max_real_bid"`
}

func NewIndexAppWallStrategyIndex() *IndexAppWallStrategyIndex {
	return &IndexAppWallStrategyIndex{}
}

func (p *IndexAppWallStrategyIndex) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAppWallStrategyIndex) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Creatives = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem26 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem26 = v
		}
		p.Creatives = append(p.Creatives, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *IndexAppWallStrategyIndex) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxRealBid = v
	}
	return nil
}

func (p *IndexAppWallStrategyIndex) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAppWallStrategyIndex"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAppWallStrategyIndex) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Creatives != nil {
		if err := oprot.WriteFieldBegin("creatives", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:creatives: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Creatives)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Creatives {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:creatives: %s", p, err)
		}
	}
	return err
}

func (p *IndexAppWallStrategyIndex) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_real_bid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:max_real_bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MaxRealBid)); err != nil {
		return fmt.Errorf("%T.max_real_bid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:max_real_bid: %s", p, err)
	}
	return err
}

func (p *IndexAppWallStrategyIndex) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAppWallStrategyIndex(%+v)", *p)
}

type IndexAppWallAdIndex struct {
	StrategyNum        int32                                `thrift:"strategy_num,1" json:"strategy_num"`
	CreativeNum        int32                                `thrift:"creative_num,2" json:"creative_num"`
	Ads                map[int32]*IndexAppWallStrategyIndex `thrift:"ads,3" json:"ads"`
	CFlagStrategyIndex map[int32][]int32                    `thrift:"c_flag_strategy_index,4" json:"c_flag_strategy_index"`
}

func NewIndexAppWallAdIndex() *IndexAppWallAdIndex {
	return &IndexAppWallAdIndex{}
}

func (p *IndexAppWallAdIndex) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IndexAppWallAdIndex) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StrategyNum = v
	}
	return nil
}

func (p *IndexAppWallAdIndex) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CreativeNum = v
	}
	return nil
}

func (p *IndexAppWallAdIndex) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Ads = make(map[int32]*IndexAppWallStrategyIndex, size)
	for i := 0; i < size; i++ {
		var _key27 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key27 = v
		}
		_val28 := NewIndexAppWallStrategyIndex()
		if err := _val28.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val28)
		}
		p.Ads[_key27] = _val28
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *IndexAppWallAdIndex) readField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.CFlagStrategyIndex = make(map[int32][]int32, size)
	for i := 0; i < size; i++ {
		var _key29 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key29 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val30 := make([]int32, 0, size)
		for i := 0; i < size; i++ {
			var _elem31 int32
			if v, err := iprot.ReadI32(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_elem31 = v
			}
			_val30 = append(_val30, _elem31)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.CFlagStrategyIndex[_key29] = _val30
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *IndexAppWallAdIndex) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IndexAppWallAdIndex"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IndexAppWallAdIndex) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_num", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:strategy_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyNum)); err != nil {
		return fmt.Errorf("%T.strategy_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:strategy_num: %s", p, err)
	}
	return err
}

func (p *IndexAppWallAdIndex) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:creative_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeNum)); err != nil {
		return fmt.Errorf("%T.creative_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:creative_num: %s", p, err)
	}
	return err
}

func (p *IndexAppWallAdIndex) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ads != nil {
		if err := oprot.WriteFieldBegin("ads", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ads: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Ads)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Ads {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ads: %s", p, err)
		}
	}
	return err
}

func (p *IndexAppWallAdIndex) writeField4(oprot thrift.TProtocol) (err error) {
	if p.CFlagStrategyIndex != nil {
		if err := oprot.WriteFieldBegin("c_flag_strategy_index", thrift.MAP, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:c_flag_strategy_index: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.CFlagStrategyIndex)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.CFlagStrategyIndex {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteI32(int32(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:c_flag_strategy_index: %s", p, err)
		}
	}
	return err
}

func (p *IndexAppWallAdIndex) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IndexAppWallAdIndex(%+v)", *p)
}
