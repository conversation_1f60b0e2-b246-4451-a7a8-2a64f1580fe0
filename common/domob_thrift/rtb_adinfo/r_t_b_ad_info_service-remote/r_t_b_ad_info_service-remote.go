// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"rtb_adinfo"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addCampaign(RequestHeader header, RTBCampaign campaign)")
	fmt.Fprintln(os.Stderr, "  void editCampaign(RequestHeader header, RTBCampaign campaign)")
	fmt.Fprintln(os.<PERSON>, "  void pauseCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   listCampaignIdsByDspCampaignId(RequestHeader header, i32 dspCampaignId, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   listCampaignIdsByDspCampaignIdAndTaskFlag(RequestHeader header, i32 dspCampaignId, i32 taskFlag, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "  void sysPauseCampaignsByIds(RequestHeader header, i32 dspCampaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  void sysResumeCampaignsByIds(RequestHeader header, i32 dspCampaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addStrategy(RequestHeader header, RTBStrategy strategy)")
	fmt.Fprintln(os.Stderr, "  void editStrategy(RequestHeader header, RTBStrategy strategy)")
	fmt.Fprintln(os.Stderr, "  void batchEditStrategyPrice(RequestHeader header,  strategys)")
	fmt.Fprintln(os.Stderr, "  void batchEditStrategy(RequestHeader header,  strategys, BatchEditStrategyField field)")
	fmt.Fprintln(os.Stderr, "  void updateStrategyDetectRange(RequestHeader header, i32 id, i64 start_time, i64 quit_time)")
	fmt.Fprintln(os.Stderr, "  void updateStrategyOCPCPhase(RequestHeader header, i32 id, i32 target_ocpc_phase)")
	fmt.Fprintln(os.Stderr, "  void pauseStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   listStrategyIdsByCampaignId(RequestHeader header, i32 campaignId, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   getStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCreative(RequestHeader header, RTBCreative creative)")
	fmt.Fprintln(os.Stderr, "  void editCreative(RequestHeader header, RTBCreative creative)")
	fmt.Fprintln(os.Stderr, "  void pauseCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   listCreativesByExchangeAuditStatus(RequestHeader header, i32 exchangeId,  auditStatuses, i32 fromId, i32 limit)")
	fmt.Fprintln(os.Stderr, "   listCreativesByExchangeAuditStatusBid(RequestHeader header, i32 exchangeId,  auditStatuses, i32 fromId, i32 limit)")
	fmt.Fprintln(os.Stderr, "   listCreativeIdsByStrategyId(RequestHeader header, i32 strategyId, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "   listCreativesWithoutParentLimitByExchangeAuditStatus(RequestHeader header, i32 exchangeId,  auditStatuses, i32 fromId, i32 limit)")
	fmt.Fprintln(os.Stderr, "   listCreativesWithoutParentLimitByExchangeAuditStatusBid(RequestHeader header, i32 exchangeId,  auditStatuses, i32 fromId, i32 limit)")
	fmt.Fprintln(os.Stderr, "   listCompaniesByExchangeAuditStatus(RequestHeader header, i32 exchangeId,  auditStatuses, i32 fromId, i32 limit)")
	fmt.Fprintln(os.Stderr, "   listSponsorsByExchangeAuditStatus(RequestHeader header, i32 exchangeId,  auditStatuses, i32 fromId, i32 limit)")
	fmt.Fprintln(os.Stderr, "  void updateExchangeAuditStatus(RequestHeader header, i32 exchangeId,  audit_infos)")
	fmt.Fprintln(os.Stderr, "  void updateExchangeAuditStatusAndContainerAuditStatus(RequestHeader header,  audit_infos)")
	fmt.Fprintln(os.Stderr, "   getCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCompany(RequestHeader header, RTBCompany company)")
	fmt.Fprintln(os.Stderr, "  void editCompany(RequestHeader header, RTBCompany company)")
	fmt.Fprintln(os.Stderr, "  void deleteCompaniesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getCompaniesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getCompaniesByComNames(RequestHeader header,  comNames)")
	fmt.Fprintln(os.Stderr, "  i32 addSponsor(RequestHeader header, RTBSponsor sponsor)")
	fmt.Fprintln(os.Stderr, "  void editSponsor(RequestHeader header, RTBSponsor sponsor)")
	fmt.Fprintln(os.Stderr, "  void deleteSponsorsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void editSponsorAuditMaterials(RequestHeader header, i32 id,  audit_materials)")
	fmt.Fprintln(os.Stderr, "  void deleteSponsorAuditMaterials(RequestHeader header, i32 id,  exchange_ids)")
	fmt.Fprintln(os.Stderr, "   getSponsorsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addPromotion(RequestHeader header, RTBPromotion promotion)")
	fmt.Fprintln(os.Stderr, "  void editPromotion(RequestHeader header, RTBPromotion promotion)")
	fmt.Fprintln(os.Stderr, "  void deletePromotionsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getPromotionsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void exportAllRunnableAds(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "  void exportObjects(RequestHeader header,  ids, string type)")
	fmt.Fprintln(os.Stderr, "  void updateMedia(RequestHeader header,  media)")
	fmt.Fprintln(os.Stderr, "   getMediaByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteMediaByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addMediaGroup(RequestHeader header, RTBMediaGroup media_group)")
	fmt.Fprintln(os.Stderr, "   getMediaGroupsByIds(RequestHeader header,  media_group_ids)")
	fmt.Fprintln(os.Stderr, "  void deleteMediaGroupsByIds(RequestHeader header,  media_group_ids)")
	fmt.Fprintln(os.Stderr, "  void updateMediaGroup(RequestHeader header, RTBMediaGroup media_group)")
	fmt.Fprintln(os.Stderr, "  void updateMediaAndMediaGroupRelations(RequestHeader header, i32 media_group_id,  media_ids)")
	fmt.Fprintln(os.Stderr, "  void deleteMediaAndMediaGroupRelations(RequestHeader header, i32 media_group_id,  media_ids)")
	fmt.Fprintln(os.Stderr, "   listMediaByMediaGroupId(RequestHeader header, i32 media_group_id, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   listMediaGroupsByPage(RequestHeader header, i32 offset, i32 limit, i32 is_delete)")
	fmt.Fprintln(os.Stderr, "  void editCreativeCapabilities(RequestHeader header, i32 creative_id,  capabilities,  required_capabilities)")
	fmt.Fprintln(os.Stderr, "  void editCreativeIndustry(RequestHeader header, i32 creative_id, string industry_type)")
	fmt.Fprintln(os.Stderr, "  i32 addAdTracking(RequestHeader header, AdTrackingInfo ad_tracking)")
	fmt.Fprintln(os.Stderr, "  void editAdTracking(RequestHeader header, AdTrackingInfo ad_tracking)")
	fmt.Fprintln(os.Stderr, "  void updateFrequencyControl(RequestHeader header, i32 product_id,  product_id_freq_gp, i32 product_id_gp_imp_freq_target, i32 product_id_gp_clk_freq_target)")
	fmt.Fprintln(os.Stderr, "  void deleteAdTrackingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getAdTrackingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   listAdTrackingByPromotionId(RequestHeader header, i32 promotionId, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  i32 addDeal(RequestHeader header, RTBDeal rtb_deal)")
	fmt.Fprintln(os.Stderr, "  void editDeal(RequestHeader header, RTBDeal rtb_deal)")
	fmt.Fprintln(os.Stderr, "  i32 addExchangeDmp(RequestHeader header, RTBExchangeDmp exchange_dmp)")
	fmt.Fprintln(os.Stderr, "  void editExchangeDmp(RequestHeader header, RTBExchangeDmp exchange_dmp)")
	fmt.Fprintln(os.Stderr, "  void updateExchangeDmpResult(RequestHeader header, i32 exchange_dmp_id, i32 exchange_id, bool is_success,  exchange_audience_ids, i32 version)")
	fmt.Fprintln(os.Stderr, "  void updateExchangeDmpStatus(RequestHeader header, i32 exchange_dmp_id, i32 new_status)")
	fmt.Fprintln(os.Stderr, "   listExchangeDmpsByComputeStatus(RequestHeader header, i32 exchange_id,  compute_statuses,  create_sources, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getExchangeDmpsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void updateExchangeDmpAudienceCount(RequestHeader header, RTBExchangeDmpAudienceCount audience_count)")
	fmt.Fprintln(os.Stderr, "  RTBExchangeDmpAudienceCount getLatestExchangeDmpAudienceCount(RequestHeader header, i32 exchange_id, i16 source)")
	fmt.Fprintln(os.Stderr, "  i32 updateExchangeDmpComputeStatus(RequestHeader header, i32 exchange_dmp_id, i32 version, i32 new_compute_status)")
	fmt.Fprintln(os.Stderr, "   listExchangeDmpsByParams(RequestHeader header,  level,  create_sources, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  i32 addSponsorSendSetting(RequestHeader header, RTBSponsorSendSetting sponsorSendSetting)")
	fmt.Fprintln(os.Stderr, "  void editSponsorSendSetting(RequestHeader header, RTBSponsorSendSetting sponsorSendSetting)")
	fmt.Fprintln(os.Stderr, "  void deleteSponsorSendSettingsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getSponsorSendSettingsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getSponsorSendSettingsBySponsorIds(RequestHeader header,  sponsorIds)")
	fmt.Fprintln(os.Stderr, "   getBidCreativesByExchangeId(RequestHeader header, i32 exchangeId, i32 status, i32 limit, i32 offset)")
	fmt.Fprintln(os.Stderr, "   getBidStrategiesByExchangeId(RequestHeader header, i32 exchangeId, i32 status, i32 limit, i32 offset)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := rtb_adinfo.NewRTBAdInfoServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCampaign requires 2 args")
			flag.Usage()
		}
		arg464 := flag.Arg(1)
		mbTrans465 := thrift.NewTMemoryBufferLen(len(arg464))
		defer mbTrans465.Close()
		_, err466 := mbTrans465.WriteString(arg464)
		if err466 != nil {
			Usage()
			return
		}
		factory467 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt468 := factory467.GetProtocol(mbTrans465)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err469 := argvalue0.Read(jsProt468)
		if err469 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg470 := flag.Arg(2)
		mbTrans471 := thrift.NewTMemoryBufferLen(len(arg470))
		defer mbTrans471.Close()
		_, err472 := mbTrans471.WriteString(arg470)
		if err472 != nil {
			Usage()
			return
		}
		factory473 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt474 := factory473.GetProtocol(mbTrans471)
		argvalue1 := rtb_adinfo.NewRTBCampaign()
		err475 := argvalue1.Read(jsProt474)
		if err475 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "editCampaign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCampaign requires 2 args")
			flag.Usage()
		}
		arg476 := flag.Arg(1)
		mbTrans477 := thrift.NewTMemoryBufferLen(len(arg476))
		defer mbTrans477.Close()
		_, err478 := mbTrans477.WriteString(arg476)
		if err478 != nil {
			Usage()
			return
		}
		factory479 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt480 := factory479.GetProtocol(mbTrans477)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err481 := argvalue0.Read(jsProt480)
		if err481 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg482 := flag.Arg(2)
		mbTrans483 := thrift.NewTMemoryBufferLen(len(arg482))
		defer mbTrans483.Close()
		_, err484 := mbTrans483.WriteString(arg482)
		if err484 != nil {
			Usage()
			return
		}
		factory485 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt486 := factory485.GetProtocol(mbTrans483)
		argvalue1 := rtb_adinfo.NewRTBCampaign()
		err487 := argvalue1.Read(jsProt486)
		if err487 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCampaign(value0, value1))
		fmt.Print("\n")
		break
	case "pauseCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg488 := flag.Arg(1)
		mbTrans489 := thrift.NewTMemoryBufferLen(len(arg488))
		defer mbTrans489.Close()
		_, err490 := mbTrans489.WriteString(arg488)
		if err490 != nil {
			Usage()
			return
		}
		factory491 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt492 := factory491.GetProtocol(mbTrans489)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err493 := argvalue0.Read(jsProt492)
		if err493 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg494 := flag.Arg(2)
		mbTrans495 := thrift.NewTMemoryBufferLen(len(arg494))
		defer mbTrans495.Close()
		_, err496 := mbTrans495.WriteString(arg494)
		if err496 != nil {
			Usage()
			return
		}
		factory497 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt498 := factory497.GetProtocol(mbTrans495)
		containerStruct1 := rtb_adinfo.NewPauseCampaignsByIdsArgs()
		err499 := containerStruct1.ReadField2(jsProt498)
		if err499 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg500 := flag.Arg(1)
		mbTrans501 := thrift.NewTMemoryBufferLen(len(arg500))
		defer mbTrans501.Close()
		_, err502 := mbTrans501.WriteString(arg500)
		if err502 != nil {
			Usage()
			return
		}
		factory503 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt504 := factory503.GetProtocol(mbTrans501)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err505 := argvalue0.Read(jsProt504)
		if err505 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg506 := flag.Arg(2)
		mbTrans507 := thrift.NewTMemoryBufferLen(len(arg506))
		defer mbTrans507.Close()
		_, err508 := mbTrans507.WriteString(arg506)
		if err508 != nil {
			Usage()
			return
		}
		factory509 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt510 := factory509.GetProtocol(mbTrans507)
		containerStruct1 := rtb_adinfo.NewResumeCampaignsByIdsArgs()
		err511 := containerStruct1.ReadField2(jsProt510)
		if err511 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg512 := flag.Arg(1)
		mbTrans513 := thrift.NewTMemoryBufferLen(len(arg512))
		defer mbTrans513.Close()
		_, err514 := mbTrans513.WriteString(arg512)
		if err514 != nil {
			Usage()
			return
		}
		factory515 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt516 := factory515.GetProtocol(mbTrans513)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err517 := argvalue0.Read(jsProt516)
		if err517 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg518 := flag.Arg(2)
		mbTrans519 := thrift.NewTMemoryBufferLen(len(arg518))
		defer mbTrans519.Close()
		_, err520 := mbTrans519.WriteString(arg518)
		if err520 != nil {
			Usage()
			return
		}
		factory521 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt522 := factory521.GetProtocol(mbTrans519)
		containerStruct1 := rtb_adinfo.NewDeleteCampaignsByIdsArgs()
		err523 := containerStruct1.ReadField2(jsProt522)
		if err523 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg524 := flag.Arg(1)
		mbTrans525 := thrift.NewTMemoryBufferLen(len(arg524))
		defer mbTrans525.Close()
		_, err526 := mbTrans525.WriteString(arg524)
		if err526 != nil {
			Usage()
			return
		}
		factory527 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt528 := factory527.GetProtocol(mbTrans525)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err529 := argvalue0.Read(jsProt528)
		if err529 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg530 := flag.Arg(2)
		mbTrans531 := thrift.NewTMemoryBufferLen(len(arg530))
		defer mbTrans531.Close()
		_, err532 := mbTrans531.WriteString(arg530)
		if err532 != nil {
			Usage()
			return
		}
		factory533 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt534 := factory533.GetProtocol(mbTrans531)
		containerStruct1 := rtb_adinfo.NewGetCampaignsByIdsArgs()
		err535 := containerStruct1.ReadField2(jsProt534)
		if err535 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listCampaignIdsByDspCampaignId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListCampaignIdsByDspCampaignId requires 3 args")
			flag.Usage()
		}
		arg536 := flag.Arg(1)
		mbTrans537 := thrift.NewTMemoryBufferLen(len(arg536))
		defer mbTrans537.Close()
		_, err538 := mbTrans537.WriteString(arg536)
		if err538 != nil {
			Usage()
			return
		}
		factory539 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt540 := factory539.GetProtocol(mbTrans537)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err541 := argvalue0.Read(jsProt540)
		if err541 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err542 := (strconv.Atoi(flag.Arg(2)))
		if err542 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.ListCampaignIdsByDspCampaignId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listCampaignIdsByDspCampaignIdAndTaskFlag":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListCampaignIdsByDspCampaignIdAndTaskFlag requires 4 args")
			flag.Usage()
		}
		arg544 := flag.Arg(1)
		mbTrans545 := thrift.NewTMemoryBufferLen(len(arg544))
		defer mbTrans545.Close()
		_, err546 := mbTrans545.WriteString(arg544)
		if err546 != nil {
			Usage()
			return
		}
		factory547 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt548 := factory547.GetProtocol(mbTrans545)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err549 := argvalue0.Read(jsProt548)
		if err549 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err550 := (strconv.Atoi(flag.Arg(2)))
		if err550 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err551 := (strconv.Atoi(flag.Arg(3)))
		if err551 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.ListCampaignIdsByDspCampaignIdAndTaskFlag(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "sysPauseCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SysPauseCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg553 := flag.Arg(1)
		mbTrans554 := thrift.NewTMemoryBufferLen(len(arg553))
		defer mbTrans554.Close()
		_, err555 := mbTrans554.WriteString(arg553)
		if err555 != nil {
			Usage()
			return
		}
		factory556 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt557 := factory556.GetProtocol(mbTrans554)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err558 := argvalue0.Read(jsProt557)
		if err558 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err559 := (strconv.Atoi(flag.Arg(2)))
		if err559 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg560 := flag.Arg(3)
		mbTrans561 := thrift.NewTMemoryBufferLen(len(arg560))
		defer mbTrans561.Close()
		_, err562 := mbTrans561.WriteString(arg560)
		if err562 != nil {
			Usage()
			return
		}
		factory563 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt564 := factory563.GetProtocol(mbTrans561)
		containerStruct2 := rtb_adinfo.NewSysPauseCampaignsByIdsArgs()
		err565 := containerStruct2.ReadField3(jsProt564)
		if err565 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.SysPauseCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "sysResumeCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SysResumeCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg566 := flag.Arg(1)
		mbTrans567 := thrift.NewTMemoryBufferLen(len(arg566))
		defer mbTrans567.Close()
		_, err568 := mbTrans567.WriteString(arg566)
		if err568 != nil {
			Usage()
			return
		}
		factory569 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt570 := factory569.GetProtocol(mbTrans567)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err571 := argvalue0.Read(jsProt570)
		if err571 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err572 := (strconv.Atoi(flag.Arg(2)))
		if err572 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg573 := flag.Arg(3)
		mbTrans574 := thrift.NewTMemoryBufferLen(len(arg573))
		defer mbTrans574.Close()
		_, err575 := mbTrans574.WriteString(arg573)
		if err575 != nil {
			Usage()
			return
		}
		factory576 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt577 := factory576.GetProtocol(mbTrans574)
		containerStruct2 := rtb_adinfo.NewSysResumeCampaignsByIdsArgs()
		err578 := containerStruct2.ReadField3(jsProt577)
		if err578 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.SysResumeCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddStrategy requires 2 args")
			flag.Usage()
		}
		arg579 := flag.Arg(1)
		mbTrans580 := thrift.NewTMemoryBufferLen(len(arg579))
		defer mbTrans580.Close()
		_, err581 := mbTrans580.WriteString(arg579)
		if err581 != nil {
			Usage()
			return
		}
		factory582 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt583 := factory582.GetProtocol(mbTrans580)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err584 := argvalue0.Read(jsProt583)
		if err584 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg585 := flag.Arg(2)
		mbTrans586 := thrift.NewTMemoryBufferLen(len(arg585))
		defer mbTrans586.Close()
		_, err587 := mbTrans586.WriteString(arg585)
		if err587 != nil {
			Usage()
			return
		}
		factory588 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt589 := factory588.GetProtocol(mbTrans586)
		argvalue1 := rtb_adinfo.NewRTBStrategy()
		err590 := argvalue1.Read(jsProt589)
		if err590 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "editStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditStrategy requires 2 args")
			flag.Usage()
		}
		arg591 := flag.Arg(1)
		mbTrans592 := thrift.NewTMemoryBufferLen(len(arg591))
		defer mbTrans592.Close()
		_, err593 := mbTrans592.WriteString(arg591)
		if err593 != nil {
			Usage()
			return
		}
		factory594 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt595 := factory594.GetProtocol(mbTrans592)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err596 := argvalue0.Read(jsProt595)
		if err596 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg597 := flag.Arg(2)
		mbTrans598 := thrift.NewTMemoryBufferLen(len(arg597))
		defer mbTrans598.Close()
		_, err599 := mbTrans598.WriteString(arg597)
		if err599 != nil {
			Usage()
			return
		}
		factory600 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt601 := factory600.GetProtocol(mbTrans598)
		argvalue1 := rtb_adinfo.NewRTBStrategy()
		err602 := argvalue1.Read(jsProt601)
		if err602 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "batchEditStrategyPrice":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "BatchEditStrategyPrice requires 2 args")
			flag.Usage()
		}
		arg603 := flag.Arg(1)
		mbTrans604 := thrift.NewTMemoryBufferLen(len(arg603))
		defer mbTrans604.Close()
		_, err605 := mbTrans604.WriteString(arg603)
		if err605 != nil {
			Usage()
			return
		}
		factory606 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt607 := factory606.GetProtocol(mbTrans604)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err608 := argvalue0.Read(jsProt607)
		if err608 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg609 := flag.Arg(2)
		mbTrans610 := thrift.NewTMemoryBufferLen(len(arg609))
		defer mbTrans610.Close()
		_, err611 := mbTrans610.WriteString(arg609)
		if err611 != nil {
			Usage()
			return
		}
		factory612 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt613 := factory612.GetProtocol(mbTrans610)
		containerStruct1 := rtb_adinfo.NewBatchEditStrategyPriceArgs()
		err614 := containerStruct1.ReadField2(jsProt613)
		if err614 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Strategys
		value1 := argvalue1
		fmt.Print(client.BatchEditStrategyPrice(value0, value1))
		fmt.Print("\n")
		break
	case "batchEditStrategy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "BatchEditStrategy requires 3 args")
			flag.Usage()
		}
		arg615 := flag.Arg(1)
		mbTrans616 := thrift.NewTMemoryBufferLen(len(arg615))
		defer mbTrans616.Close()
		_, err617 := mbTrans616.WriteString(arg615)
		if err617 != nil {
			Usage()
			return
		}
		factory618 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt619 := factory618.GetProtocol(mbTrans616)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err620 := argvalue0.Read(jsProt619)
		if err620 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg621 := flag.Arg(2)
		mbTrans622 := thrift.NewTMemoryBufferLen(len(arg621))
		defer mbTrans622.Close()
		_, err623 := mbTrans622.WriteString(arg621)
		if err623 != nil {
			Usage()
			return
		}
		factory624 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt625 := factory624.GetProtocol(mbTrans622)
		containerStruct1 := rtb_adinfo.NewBatchEditStrategyArgs()
		err626 := containerStruct1.ReadField2(jsProt625)
		if err626 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Strategys
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := rtb_adinfo.BatchEditStrategyField(tmp2)
		value2 := argvalue2
		fmt.Print(client.BatchEditStrategy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateStrategyDetectRange":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "UpdateStrategyDetectRange requires 4 args")
			flag.Usage()
		}
		arg627 := flag.Arg(1)
		mbTrans628 := thrift.NewTMemoryBufferLen(len(arg627))
		defer mbTrans628.Close()
		_, err629 := mbTrans628.WriteString(arg627)
		if err629 != nil {
			Usage()
			return
		}
		factory630 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt631 := factory630.GetProtocol(mbTrans628)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err632 := argvalue0.Read(jsProt631)
		if err632 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err633 := (strconv.Atoi(flag.Arg(2)))
		if err633 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err634 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err634 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err635 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err635 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.UpdateStrategyDetectRange(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updateStrategyOCPCPhase":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateStrategyOCPCPhase requires 3 args")
			flag.Usage()
		}
		arg636 := flag.Arg(1)
		mbTrans637 := thrift.NewTMemoryBufferLen(len(arg636))
		defer mbTrans637.Close()
		_, err638 := mbTrans637.WriteString(arg636)
		if err638 != nil {
			Usage()
			return
		}
		factory639 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt640 := factory639.GetProtocol(mbTrans637)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err641 := argvalue0.Read(jsProt640)
		if err641 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err642 := (strconv.Atoi(flag.Arg(2)))
		if err642 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err643 := (strconv.Atoi(flag.Arg(3)))
		if err643 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.UpdateStrategyOCPCPhase(value0, value1, value2))
		fmt.Print("\n")
		break
	case "pauseStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg644 := flag.Arg(1)
		mbTrans645 := thrift.NewTMemoryBufferLen(len(arg644))
		defer mbTrans645.Close()
		_, err646 := mbTrans645.WriteString(arg644)
		if err646 != nil {
			Usage()
			return
		}
		factory647 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt648 := factory647.GetProtocol(mbTrans645)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err649 := argvalue0.Read(jsProt648)
		if err649 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg650 := flag.Arg(2)
		mbTrans651 := thrift.NewTMemoryBufferLen(len(arg650))
		defer mbTrans651.Close()
		_, err652 := mbTrans651.WriteString(arg650)
		if err652 != nil {
			Usage()
			return
		}
		factory653 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt654 := factory653.GetProtocol(mbTrans651)
		containerStruct1 := rtb_adinfo.NewPauseStrategiesByIdsArgs()
		err655 := containerStruct1.ReadField2(jsProt654)
		if err655 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg656 := flag.Arg(1)
		mbTrans657 := thrift.NewTMemoryBufferLen(len(arg656))
		defer mbTrans657.Close()
		_, err658 := mbTrans657.WriteString(arg656)
		if err658 != nil {
			Usage()
			return
		}
		factory659 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt660 := factory659.GetProtocol(mbTrans657)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err661 := argvalue0.Read(jsProt660)
		if err661 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg662 := flag.Arg(2)
		mbTrans663 := thrift.NewTMemoryBufferLen(len(arg662))
		defer mbTrans663.Close()
		_, err664 := mbTrans663.WriteString(arg662)
		if err664 != nil {
			Usage()
			return
		}
		factory665 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt666 := factory665.GetProtocol(mbTrans663)
		containerStruct1 := rtb_adinfo.NewResumeStrategiesByIdsArgs()
		err667 := containerStruct1.ReadField2(jsProt666)
		if err667 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg668 := flag.Arg(1)
		mbTrans669 := thrift.NewTMemoryBufferLen(len(arg668))
		defer mbTrans669.Close()
		_, err670 := mbTrans669.WriteString(arg668)
		if err670 != nil {
			Usage()
			return
		}
		factory671 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt672 := factory671.GetProtocol(mbTrans669)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err673 := argvalue0.Read(jsProt672)
		if err673 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg674 := flag.Arg(2)
		mbTrans675 := thrift.NewTMemoryBufferLen(len(arg674))
		defer mbTrans675.Close()
		_, err676 := mbTrans675.WriteString(arg674)
		if err676 != nil {
			Usage()
			return
		}
		factory677 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt678 := factory677.GetProtocol(mbTrans675)
		containerStruct1 := rtb_adinfo.NewDeleteStrategiesByIdsArgs()
		err679 := containerStruct1.ReadField2(jsProt678)
		if err679 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listStrategyIdsByCampaignId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListStrategyIdsByCampaignId requires 3 args")
			flag.Usage()
		}
		arg680 := flag.Arg(1)
		mbTrans681 := thrift.NewTMemoryBufferLen(len(arg680))
		defer mbTrans681.Close()
		_, err682 := mbTrans681.WriteString(arg680)
		if err682 != nil {
			Usage()
			return
		}
		factory683 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt684 := factory683.GetProtocol(mbTrans681)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err685 := argvalue0.Read(jsProt684)
		if err685 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err686 := (strconv.Atoi(flag.Arg(2)))
		if err686 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.ListStrategyIdsByCampaignId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg688 := flag.Arg(1)
		mbTrans689 := thrift.NewTMemoryBufferLen(len(arg688))
		defer mbTrans689.Close()
		_, err690 := mbTrans689.WriteString(arg688)
		if err690 != nil {
			Usage()
			return
		}
		factory691 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt692 := factory691.GetProtocol(mbTrans689)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err693 := argvalue0.Read(jsProt692)
		if err693 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg694 := flag.Arg(2)
		mbTrans695 := thrift.NewTMemoryBufferLen(len(arg694))
		defer mbTrans695.Close()
		_, err696 := mbTrans695.WriteString(arg694)
		if err696 != nil {
			Usage()
			return
		}
		factory697 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt698 := factory697.GetProtocol(mbTrans695)
		containerStruct1 := rtb_adinfo.NewGetStrategiesByIdsArgs()
		err699 := containerStruct1.ReadField2(jsProt698)
		if err699 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreative requires 2 args")
			flag.Usage()
		}
		arg700 := flag.Arg(1)
		mbTrans701 := thrift.NewTMemoryBufferLen(len(arg700))
		defer mbTrans701.Close()
		_, err702 := mbTrans701.WriteString(arg700)
		if err702 != nil {
			Usage()
			return
		}
		factory703 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt704 := factory703.GetProtocol(mbTrans701)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err705 := argvalue0.Read(jsProt704)
		if err705 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg706 := flag.Arg(2)
		mbTrans707 := thrift.NewTMemoryBufferLen(len(arg706))
		defer mbTrans707.Close()
		_, err708 := mbTrans707.WriteString(arg706)
		if err708 != nil {
			Usage()
			return
		}
		factory709 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt710 := factory709.GetProtocol(mbTrans707)
		argvalue1 := rtb_adinfo.NewRTBCreative()
		err711 := argvalue1.Read(jsProt710)
		if err711 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCreative(value0, value1))
		fmt.Print("\n")
		break
	case "editCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCreative requires 2 args")
			flag.Usage()
		}
		arg712 := flag.Arg(1)
		mbTrans713 := thrift.NewTMemoryBufferLen(len(arg712))
		defer mbTrans713.Close()
		_, err714 := mbTrans713.WriteString(arg712)
		if err714 != nil {
			Usage()
			return
		}
		factory715 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt716 := factory715.GetProtocol(mbTrans713)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err717 := argvalue0.Read(jsProt716)
		if err717 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg718 := flag.Arg(2)
		mbTrans719 := thrift.NewTMemoryBufferLen(len(arg718))
		defer mbTrans719.Close()
		_, err720 := mbTrans719.WriteString(arg718)
		if err720 != nil {
			Usage()
			return
		}
		factory721 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt722 := factory721.GetProtocol(mbTrans719)
		argvalue1 := rtb_adinfo.NewRTBCreative()
		err723 := argvalue1.Read(jsProt722)
		if err723 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCreative(value0, value1))
		fmt.Print("\n")
		break
	case "pauseCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg724 := flag.Arg(1)
		mbTrans725 := thrift.NewTMemoryBufferLen(len(arg724))
		defer mbTrans725.Close()
		_, err726 := mbTrans725.WriteString(arg724)
		if err726 != nil {
			Usage()
			return
		}
		factory727 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt728 := factory727.GetProtocol(mbTrans725)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err729 := argvalue0.Read(jsProt728)
		if err729 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg730 := flag.Arg(2)
		mbTrans731 := thrift.NewTMemoryBufferLen(len(arg730))
		defer mbTrans731.Close()
		_, err732 := mbTrans731.WriteString(arg730)
		if err732 != nil {
			Usage()
			return
		}
		factory733 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt734 := factory733.GetProtocol(mbTrans731)
		containerStruct1 := rtb_adinfo.NewPauseCreativesByIdsArgs()
		err735 := containerStruct1.ReadField2(jsProt734)
		if err735 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg736 := flag.Arg(1)
		mbTrans737 := thrift.NewTMemoryBufferLen(len(arg736))
		defer mbTrans737.Close()
		_, err738 := mbTrans737.WriteString(arg736)
		if err738 != nil {
			Usage()
			return
		}
		factory739 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt740 := factory739.GetProtocol(mbTrans737)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err741 := argvalue0.Read(jsProt740)
		if err741 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg742 := flag.Arg(2)
		mbTrans743 := thrift.NewTMemoryBufferLen(len(arg742))
		defer mbTrans743.Close()
		_, err744 := mbTrans743.WriteString(arg742)
		if err744 != nil {
			Usage()
			return
		}
		factory745 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt746 := factory745.GetProtocol(mbTrans743)
		containerStruct1 := rtb_adinfo.NewResumeCreativesByIdsArgs()
		err747 := containerStruct1.ReadField2(jsProt746)
		if err747 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg748 := flag.Arg(1)
		mbTrans749 := thrift.NewTMemoryBufferLen(len(arg748))
		defer mbTrans749.Close()
		_, err750 := mbTrans749.WriteString(arg748)
		if err750 != nil {
			Usage()
			return
		}
		factory751 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt752 := factory751.GetProtocol(mbTrans749)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err753 := argvalue0.Read(jsProt752)
		if err753 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg754 := flag.Arg(2)
		mbTrans755 := thrift.NewTMemoryBufferLen(len(arg754))
		defer mbTrans755.Close()
		_, err756 := mbTrans755.WriteString(arg754)
		if err756 != nil {
			Usage()
			return
		}
		factory757 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt758 := factory757.GetProtocol(mbTrans755)
		containerStruct1 := rtb_adinfo.NewDeleteCreativesByIdsArgs()
		err759 := containerStruct1.ReadField2(jsProt758)
		if err759 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listCreativesByExchangeAuditStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListCreativesByExchangeAuditStatus requires 5 args")
			flag.Usage()
		}
		arg760 := flag.Arg(1)
		mbTrans761 := thrift.NewTMemoryBufferLen(len(arg760))
		defer mbTrans761.Close()
		_, err762 := mbTrans761.WriteString(arg760)
		if err762 != nil {
			Usage()
			return
		}
		factory763 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt764 := factory763.GetProtocol(mbTrans761)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err765 := argvalue0.Read(jsProt764)
		if err765 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err766 := (strconv.Atoi(flag.Arg(2)))
		if err766 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg767 := flag.Arg(3)
		mbTrans768 := thrift.NewTMemoryBufferLen(len(arg767))
		defer mbTrans768.Close()
		_, err769 := mbTrans768.WriteString(arg767)
		if err769 != nil {
			Usage()
			return
		}
		factory770 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt771 := factory770.GetProtocol(mbTrans768)
		containerStruct2 := rtb_adinfo.NewListCreativesByExchangeAuditStatusArgs()
		err772 := containerStruct2.ReadField3(jsProt771)
		if err772 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditStatuses
		value2 := argvalue2
		tmp3, err773 := (strconv.Atoi(flag.Arg(4)))
		if err773 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err774 := (strconv.Atoi(flag.Arg(5)))
		if err774 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListCreativesByExchangeAuditStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listCreativesByExchangeAuditStatusBid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListCreativesByExchangeAuditStatusBid requires 5 args")
			flag.Usage()
		}
		arg775 := flag.Arg(1)
		mbTrans776 := thrift.NewTMemoryBufferLen(len(arg775))
		defer mbTrans776.Close()
		_, err777 := mbTrans776.WriteString(arg775)
		if err777 != nil {
			Usage()
			return
		}
		factory778 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt779 := factory778.GetProtocol(mbTrans776)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err780 := argvalue0.Read(jsProt779)
		if err780 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err781 := (strconv.Atoi(flag.Arg(2)))
		if err781 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg782 := flag.Arg(3)
		mbTrans783 := thrift.NewTMemoryBufferLen(len(arg782))
		defer mbTrans783.Close()
		_, err784 := mbTrans783.WriteString(arg782)
		if err784 != nil {
			Usage()
			return
		}
		factory785 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt786 := factory785.GetProtocol(mbTrans783)
		containerStruct2 := rtb_adinfo.NewListCreativesByExchangeAuditStatusBidArgs()
		err787 := containerStruct2.ReadField3(jsProt786)
		if err787 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditStatuses
		value2 := argvalue2
		tmp3, err788 := (strconv.Atoi(flag.Arg(4)))
		if err788 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err789 := (strconv.Atoi(flag.Arg(5)))
		if err789 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListCreativesByExchangeAuditStatusBid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listCreativeIdsByStrategyId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListCreativeIdsByStrategyId requires 3 args")
			flag.Usage()
		}
		arg790 := flag.Arg(1)
		mbTrans791 := thrift.NewTMemoryBufferLen(len(arg790))
		defer mbTrans791.Close()
		_, err792 := mbTrans791.WriteString(arg790)
		if err792 != nil {
			Usage()
			return
		}
		factory793 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt794 := factory793.GetProtocol(mbTrans791)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err795 := argvalue0.Read(jsProt794)
		if err795 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err796 := (strconv.Atoi(flag.Arg(2)))
		if err796 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.ListCreativeIdsByStrategyId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listCreativesWithoutParentLimitByExchangeAuditStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListCreativesWithoutParentLimitByExchangeAuditStatus requires 5 args")
			flag.Usage()
		}
		arg798 := flag.Arg(1)
		mbTrans799 := thrift.NewTMemoryBufferLen(len(arg798))
		defer mbTrans799.Close()
		_, err800 := mbTrans799.WriteString(arg798)
		if err800 != nil {
			Usage()
			return
		}
		factory801 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt802 := factory801.GetProtocol(mbTrans799)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err803 := argvalue0.Read(jsProt802)
		if err803 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err804 := (strconv.Atoi(flag.Arg(2)))
		if err804 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg805 := flag.Arg(3)
		mbTrans806 := thrift.NewTMemoryBufferLen(len(arg805))
		defer mbTrans806.Close()
		_, err807 := mbTrans806.WriteString(arg805)
		if err807 != nil {
			Usage()
			return
		}
		factory808 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt809 := factory808.GetProtocol(mbTrans806)
		containerStruct2 := rtb_adinfo.NewListCreativesWithoutParentLimitByExchangeAuditStatusArgs()
		err810 := containerStruct2.ReadField3(jsProt809)
		if err810 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditStatuses
		value2 := argvalue2
		tmp3, err811 := (strconv.Atoi(flag.Arg(4)))
		if err811 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err812 := (strconv.Atoi(flag.Arg(5)))
		if err812 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListCreativesWithoutParentLimitByExchangeAuditStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listCreativesWithoutParentLimitByExchangeAuditStatusBid":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListCreativesWithoutParentLimitByExchangeAuditStatusBid requires 5 args")
			flag.Usage()
		}
		arg813 := flag.Arg(1)
		mbTrans814 := thrift.NewTMemoryBufferLen(len(arg813))
		defer mbTrans814.Close()
		_, err815 := mbTrans814.WriteString(arg813)
		if err815 != nil {
			Usage()
			return
		}
		factory816 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt817 := factory816.GetProtocol(mbTrans814)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err818 := argvalue0.Read(jsProt817)
		if err818 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err819 := (strconv.Atoi(flag.Arg(2)))
		if err819 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg820 := flag.Arg(3)
		mbTrans821 := thrift.NewTMemoryBufferLen(len(arg820))
		defer mbTrans821.Close()
		_, err822 := mbTrans821.WriteString(arg820)
		if err822 != nil {
			Usage()
			return
		}
		factory823 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt824 := factory823.GetProtocol(mbTrans821)
		containerStruct2 := rtb_adinfo.NewListCreativesWithoutParentLimitByExchangeAuditStatusBidArgs()
		err825 := containerStruct2.ReadField3(jsProt824)
		if err825 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditStatuses
		value2 := argvalue2
		tmp3, err826 := (strconv.Atoi(flag.Arg(4)))
		if err826 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err827 := (strconv.Atoi(flag.Arg(5)))
		if err827 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListCreativesWithoutParentLimitByExchangeAuditStatusBid(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listCompaniesByExchangeAuditStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListCompaniesByExchangeAuditStatus requires 5 args")
			flag.Usage()
		}
		arg828 := flag.Arg(1)
		mbTrans829 := thrift.NewTMemoryBufferLen(len(arg828))
		defer mbTrans829.Close()
		_, err830 := mbTrans829.WriteString(arg828)
		if err830 != nil {
			Usage()
			return
		}
		factory831 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt832 := factory831.GetProtocol(mbTrans829)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err833 := argvalue0.Read(jsProt832)
		if err833 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err834 := (strconv.Atoi(flag.Arg(2)))
		if err834 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg835 := flag.Arg(3)
		mbTrans836 := thrift.NewTMemoryBufferLen(len(arg835))
		defer mbTrans836.Close()
		_, err837 := mbTrans836.WriteString(arg835)
		if err837 != nil {
			Usage()
			return
		}
		factory838 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt839 := factory838.GetProtocol(mbTrans836)
		containerStruct2 := rtb_adinfo.NewListCompaniesByExchangeAuditStatusArgs()
		err840 := containerStruct2.ReadField3(jsProt839)
		if err840 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditStatuses
		value2 := argvalue2
		tmp3, err841 := (strconv.Atoi(flag.Arg(4)))
		if err841 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err842 := (strconv.Atoi(flag.Arg(5)))
		if err842 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListCompaniesByExchangeAuditStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listSponsorsByExchangeAuditStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListSponsorsByExchangeAuditStatus requires 5 args")
			flag.Usage()
		}
		arg843 := flag.Arg(1)
		mbTrans844 := thrift.NewTMemoryBufferLen(len(arg843))
		defer mbTrans844.Close()
		_, err845 := mbTrans844.WriteString(arg843)
		if err845 != nil {
			Usage()
			return
		}
		factory846 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt847 := factory846.GetProtocol(mbTrans844)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err848 := argvalue0.Read(jsProt847)
		if err848 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err849 := (strconv.Atoi(flag.Arg(2)))
		if err849 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg850 := flag.Arg(3)
		mbTrans851 := thrift.NewTMemoryBufferLen(len(arg850))
		defer mbTrans851.Close()
		_, err852 := mbTrans851.WriteString(arg850)
		if err852 != nil {
			Usage()
			return
		}
		factory853 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt854 := factory853.GetProtocol(mbTrans851)
		containerStruct2 := rtb_adinfo.NewListSponsorsByExchangeAuditStatusArgs()
		err855 := containerStruct2.ReadField3(jsProt854)
		if err855 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditStatuses
		value2 := argvalue2
		tmp3, err856 := (strconv.Atoi(flag.Arg(4)))
		if err856 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err857 := (strconv.Atoi(flag.Arg(5)))
		if err857 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListSponsorsByExchangeAuditStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "updateExchangeAuditStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateExchangeAuditStatus requires 3 args")
			flag.Usage()
		}
		arg858 := flag.Arg(1)
		mbTrans859 := thrift.NewTMemoryBufferLen(len(arg858))
		defer mbTrans859.Close()
		_, err860 := mbTrans859.WriteString(arg858)
		if err860 != nil {
			Usage()
			return
		}
		factory861 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt862 := factory861.GetProtocol(mbTrans859)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err863 := argvalue0.Read(jsProt862)
		if err863 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err864 := (strconv.Atoi(flag.Arg(2)))
		if err864 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg865 := flag.Arg(3)
		mbTrans866 := thrift.NewTMemoryBufferLen(len(arg865))
		defer mbTrans866.Close()
		_, err867 := mbTrans866.WriteString(arg865)
		if err867 != nil {
			Usage()
			return
		}
		factory868 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt869 := factory868.GetProtocol(mbTrans866)
		containerStruct2 := rtb_adinfo.NewUpdateExchangeAuditStatusArgs()
		err870 := containerStruct2.ReadField3(jsProt869)
		if err870 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditInfos
		value2 := argvalue2
		fmt.Print(client.UpdateExchangeAuditStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateExchangeAuditStatusAndContainerAuditStatus":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateExchangeAuditStatusAndContainerAuditStatus requires 2 args")
			flag.Usage()
		}
		arg871 := flag.Arg(1)
		mbTrans872 := thrift.NewTMemoryBufferLen(len(arg871))
		defer mbTrans872.Close()
		_, err873 := mbTrans872.WriteString(arg871)
		if err873 != nil {
			Usage()
			return
		}
		factory874 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt875 := factory874.GetProtocol(mbTrans872)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err876 := argvalue0.Read(jsProt875)
		if err876 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg877 := flag.Arg(2)
		mbTrans878 := thrift.NewTMemoryBufferLen(len(arg877))
		defer mbTrans878.Close()
		_, err879 := mbTrans878.WriteString(arg877)
		if err879 != nil {
			Usage()
			return
		}
		factory880 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt881 := factory880.GetProtocol(mbTrans878)
		containerStruct1 := rtb_adinfo.NewUpdateExchangeAuditStatusAndContainerAuditStatusArgs()
		err882 := containerStruct1.ReadField2(jsProt881)
		if err882 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.AuditInfos
		value1 := argvalue1
		fmt.Print(client.UpdateExchangeAuditStatusAndContainerAuditStatus(value0, value1))
		fmt.Print("\n")
		break
	case "getCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg883 := flag.Arg(1)
		mbTrans884 := thrift.NewTMemoryBufferLen(len(arg883))
		defer mbTrans884.Close()
		_, err885 := mbTrans884.WriteString(arg883)
		if err885 != nil {
			Usage()
			return
		}
		factory886 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt887 := factory886.GetProtocol(mbTrans884)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err888 := argvalue0.Read(jsProt887)
		if err888 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg889 := flag.Arg(2)
		mbTrans890 := thrift.NewTMemoryBufferLen(len(arg889))
		defer mbTrans890.Close()
		_, err891 := mbTrans890.WriteString(arg889)
		if err891 != nil {
			Usage()
			return
		}
		factory892 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt893 := factory892.GetProtocol(mbTrans890)
		containerStruct1 := rtb_adinfo.NewGetCreativesByIdsArgs()
		err894 := containerStruct1.ReadField2(jsProt893)
		if err894 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCompany":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCompany requires 2 args")
			flag.Usage()
		}
		arg895 := flag.Arg(1)
		mbTrans896 := thrift.NewTMemoryBufferLen(len(arg895))
		defer mbTrans896.Close()
		_, err897 := mbTrans896.WriteString(arg895)
		if err897 != nil {
			Usage()
			return
		}
		factory898 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt899 := factory898.GetProtocol(mbTrans896)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err900 := argvalue0.Read(jsProt899)
		if err900 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg901 := flag.Arg(2)
		mbTrans902 := thrift.NewTMemoryBufferLen(len(arg901))
		defer mbTrans902.Close()
		_, err903 := mbTrans902.WriteString(arg901)
		if err903 != nil {
			Usage()
			return
		}
		factory904 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt905 := factory904.GetProtocol(mbTrans902)
		argvalue1 := rtb_adinfo.NewRTBCompany()
		err906 := argvalue1.Read(jsProt905)
		if err906 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCompany(value0, value1))
		fmt.Print("\n")
		break
	case "editCompany":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCompany requires 2 args")
			flag.Usage()
		}
		arg907 := flag.Arg(1)
		mbTrans908 := thrift.NewTMemoryBufferLen(len(arg907))
		defer mbTrans908.Close()
		_, err909 := mbTrans908.WriteString(arg907)
		if err909 != nil {
			Usage()
			return
		}
		factory910 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt911 := factory910.GetProtocol(mbTrans908)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err912 := argvalue0.Read(jsProt911)
		if err912 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg913 := flag.Arg(2)
		mbTrans914 := thrift.NewTMemoryBufferLen(len(arg913))
		defer mbTrans914.Close()
		_, err915 := mbTrans914.WriteString(arg913)
		if err915 != nil {
			Usage()
			return
		}
		factory916 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt917 := factory916.GetProtocol(mbTrans914)
		argvalue1 := rtb_adinfo.NewRTBCompany()
		err918 := argvalue1.Read(jsProt917)
		if err918 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCompany(value0, value1))
		fmt.Print("\n")
		break
	case "deleteCompaniesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteCompaniesByIds requires 2 args")
			flag.Usage()
		}
		arg919 := flag.Arg(1)
		mbTrans920 := thrift.NewTMemoryBufferLen(len(arg919))
		defer mbTrans920.Close()
		_, err921 := mbTrans920.WriteString(arg919)
		if err921 != nil {
			Usage()
			return
		}
		factory922 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt923 := factory922.GetProtocol(mbTrans920)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err924 := argvalue0.Read(jsProt923)
		if err924 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg925 := flag.Arg(2)
		mbTrans926 := thrift.NewTMemoryBufferLen(len(arg925))
		defer mbTrans926.Close()
		_, err927 := mbTrans926.WriteString(arg925)
		if err927 != nil {
			Usage()
			return
		}
		factory928 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt929 := factory928.GetProtocol(mbTrans926)
		containerStruct1 := rtb_adinfo.NewDeleteCompaniesByIdsArgs()
		err930 := containerStruct1.ReadField2(jsProt929)
		if err930 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteCompaniesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getCompaniesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCompaniesByIds requires 2 args")
			flag.Usage()
		}
		arg931 := flag.Arg(1)
		mbTrans932 := thrift.NewTMemoryBufferLen(len(arg931))
		defer mbTrans932.Close()
		_, err933 := mbTrans932.WriteString(arg931)
		if err933 != nil {
			Usage()
			return
		}
		factory934 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt935 := factory934.GetProtocol(mbTrans932)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err936 := argvalue0.Read(jsProt935)
		if err936 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg937 := flag.Arg(2)
		mbTrans938 := thrift.NewTMemoryBufferLen(len(arg937))
		defer mbTrans938.Close()
		_, err939 := mbTrans938.WriteString(arg937)
		if err939 != nil {
			Usage()
			return
		}
		factory940 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt941 := factory940.GetProtocol(mbTrans938)
		containerStruct1 := rtb_adinfo.NewGetCompaniesByIdsArgs()
		err942 := containerStruct1.ReadField2(jsProt941)
		if err942 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCompaniesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getCompaniesByComNames":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCompaniesByComNames requires 2 args")
			flag.Usage()
		}
		arg943 := flag.Arg(1)
		mbTrans944 := thrift.NewTMemoryBufferLen(len(arg943))
		defer mbTrans944.Close()
		_, err945 := mbTrans944.WriteString(arg943)
		if err945 != nil {
			Usage()
			return
		}
		factory946 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt947 := factory946.GetProtocol(mbTrans944)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err948 := argvalue0.Read(jsProt947)
		if err948 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg949 := flag.Arg(2)
		mbTrans950 := thrift.NewTMemoryBufferLen(len(arg949))
		defer mbTrans950.Close()
		_, err951 := mbTrans950.WriteString(arg949)
		if err951 != nil {
			Usage()
			return
		}
		factory952 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt953 := factory952.GetProtocol(mbTrans950)
		containerStruct1 := rtb_adinfo.NewGetCompaniesByComNamesArgs()
		err954 := containerStruct1.ReadField2(jsProt953)
		if err954 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ComNames
		value1 := argvalue1
		fmt.Print(client.GetCompaniesByComNames(value0, value1))
		fmt.Print("\n")
		break
	case "addSponsor":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSponsor requires 2 args")
			flag.Usage()
		}
		arg955 := flag.Arg(1)
		mbTrans956 := thrift.NewTMemoryBufferLen(len(arg955))
		defer mbTrans956.Close()
		_, err957 := mbTrans956.WriteString(arg955)
		if err957 != nil {
			Usage()
			return
		}
		factory958 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt959 := factory958.GetProtocol(mbTrans956)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err960 := argvalue0.Read(jsProt959)
		if err960 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg961 := flag.Arg(2)
		mbTrans962 := thrift.NewTMemoryBufferLen(len(arg961))
		defer mbTrans962.Close()
		_, err963 := mbTrans962.WriteString(arg961)
		if err963 != nil {
			Usage()
			return
		}
		factory964 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt965 := factory964.GetProtocol(mbTrans962)
		argvalue1 := rtb_adinfo.NewRTBSponsor()
		err966 := argvalue1.Read(jsProt965)
		if err966 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSponsor(value0, value1))
		fmt.Print("\n")
		break
	case "editSponsor":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditSponsor requires 2 args")
			flag.Usage()
		}
		arg967 := flag.Arg(1)
		mbTrans968 := thrift.NewTMemoryBufferLen(len(arg967))
		defer mbTrans968.Close()
		_, err969 := mbTrans968.WriteString(arg967)
		if err969 != nil {
			Usage()
			return
		}
		factory970 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt971 := factory970.GetProtocol(mbTrans968)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err972 := argvalue0.Read(jsProt971)
		if err972 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg973 := flag.Arg(2)
		mbTrans974 := thrift.NewTMemoryBufferLen(len(arg973))
		defer mbTrans974.Close()
		_, err975 := mbTrans974.WriteString(arg973)
		if err975 != nil {
			Usage()
			return
		}
		factory976 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt977 := factory976.GetProtocol(mbTrans974)
		argvalue1 := rtb_adinfo.NewRTBSponsor()
		err978 := argvalue1.Read(jsProt977)
		if err978 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditSponsor(value0, value1))
		fmt.Print("\n")
		break
	case "deleteSponsorsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteSponsorsByIds requires 2 args")
			flag.Usage()
		}
		arg979 := flag.Arg(1)
		mbTrans980 := thrift.NewTMemoryBufferLen(len(arg979))
		defer mbTrans980.Close()
		_, err981 := mbTrans980.WriteString(arg979)
		if err981 != nil {
			Usage()
			return
		}
		factory982 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt983 := factory982.GetProtocol(mbTrans980)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err984 := argvalue0.Read(jsProt983)
		if err984 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg985 := flag.Arg(2)
		mbTrans986 := thrift.NewTMemoryBufferLen(len(arg985))
		defer mbTrans986.Close()
		_, err987 := mbTrans986.WriteString(arg985)
		if err987 != nil {
			Usage()
			return
		}
		factory988 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt989 := factory988.GetProtocol(mbTrans986)
		containerStruct1 := rtb_adinfo.NewDeleteSponsorsByIdsArgs()
		err990 := containerStruct1.ReadField2(jsProt989)
		if err990 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteSponsorsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "editSponsorAuditMaterials":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditSponsorAuditMaterials requires 3 args")
			flag.Usage()
		}
		arg991 := flag.Arg(1)
		mbTrans992 := thrift.NewTMemoryBufferLen(len(arg991))
		defer mbTrans992.Close()
		_, err993 := mbTrans992.WriteString(arg991)
		if err993 != nil {
			Usage()
			return
		}
		factory994 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt995 := factory994.GetProtocol(mbTrans992)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err996 := argvalue0.Read(jsProt995)
		if err996 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err997 := (strconv.Atoi(flag.Arg(2)))
		if err997 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg998 := flag.Arg(3)
		mbTrans999 := thrift.NewTMemoryBufferLen(len(arg998))
		defer mbTrans999.Close()
		_, err1000 := mbTrans999.WriteString(arg998)
		if err1000 != nil {
			Usage()
			return
		}
		factory1001 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1002 := factory1001.GetProtocol(mbTrans999)
		containerStruct2 := rtb_adinfo.NewEditSponsorAuditMaterialsArgs()
		err1003 := containerStruct2.ReadField3(jsProt1002)
		if err1003 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AuditMaterials
		value2 := argvalue2
		fmt.Print(client.EditSponsorAuditMaterials(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteSponsorAuditMaterials":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteSponsorAuditMaterials requires 3 args")
			flag.Usage()
		}
		arg1004 := flag.Arg(1)
		mbTrans1005 := thrift.NewTMemoryBufferLen(len(arg1004))
		defer mbTrans1005.Close()
		_, err1006 := mbTrans1005.WriteString(arg1004)
		if err1006 != nil {
			Usage()
			return
		}
		factory1007 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1008 := factory1007.GetProtocol(mbTrans1005)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1009 := argvalue0.Read(jsProt1008)
		if err1009 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1010 := (strconv.Atoi(flag.Arg(2)))
		if err1010 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg1011 := flag.Arg(3)
		mbTrans1012 := thrift.NewTMemoryBufferLen(len(arg1011))
		defer mbTrans1012.Close()
		_, err1013 := mbTrans1012.WriteString(arg1011)
		if err1013 != nil {
			Usage()
			return
		}
		factory1014 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1015 := factory1014.GetProtocol(mbTrans1012)
		containerStruct2 := rtb_adinfo.NewDeleteSponsorAuditMaterialsArgs()
		err1016 := containerStruct2.ReadField3(jsProt1015)
		if err1016 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ExchangeIds
		value2 := argvalue2
		fmt.Print(client.DeleteSponsorAuditMaterials(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getSponsorsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorsByIds requires 2 args")
			flag.Usage()
		}
		arg1017 := flag.Arg(1)
		mbTrans1018 := thrift.NewTMemoryBufferLen(len(arg1017))
		defer mbTrans1018.Close()
		_, err1019 := mbTrans1018.WriteString(arg1017)
		if err1019 != nil {
			Usage()
			return
		}
		factory1020 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1021 := factory1020.GetProtocol(mbTrans1018)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1022 := argvalue0.Read(jsProt1021)
		if err1022 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1023 := flag.Arg(2)
		mbTrans1024 := thrift.NewTMemoryBufferLen(len(arg1023))
		defer mbTrans1024.Close()
		_, err1025 := mbTrans1024.WriteString(arg1023)
		if err1025 != nil {
			Usage()
			return
		}
		factory1026 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1027 := factory1026.GetProtocol(mbTrans1024)
		containerStruct1 := rtb_adinfo.NewGetSponsorsByIdsArgs()
		err1028 := containerStruct1.ReadField2(jsProt1027)
		if err1028 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSponsorsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPromotion requires 2 args")
			flag.Usage()
		}
		arg1029 := flag.Arg(1)
		mbTrans1030 := thrift.NewTMemoryBufferLen(len(arg1029))
		defer mbTrans1030.Close()
		_, err1031 := mbTrans1030.WriteString(arg1029)
		if err1031 != nil {
			Usage()
			return
		}
		factory1032 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1033 := factory1032.GetProtocol(mbTrans1030)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1034 := argvalue0.Read(jsProt1033)
		if err1034 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1035 := flag.Arg(2)
		mbTrans1036 := thrift.NewTMemoryBufferLen(len(arg1035))
		defer mbTrans1036.Close()
		_, err1037 := mbTrans1036.WriteString(arg1035)
		if err1037 != nil {
			Usage()
			return
		}
		factory1038 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1039 := factory1038.GetProtocol(mbTrans1036)
		argvalue1 := rtb_adinfo.NewRTBPromotion()
		err1040 := argvalue1.Read(jsProt1039)
		if err1040 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "editPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPromotion requires 2 args")
			flag.Usage()
		}
		arg1041 := flag.Arg(1)
		mbTrans1042 := thrift.NewTMemoryBufferLen(len(arg1041))
		defer mbTrans1042.Close()
		_, err1043 := mbTrans1042.WriteString(arg1041)
		if err1043 != nil {
			Usage()
			return
		}
		factory1044 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1045 := factory1044.GetProtocol(mbTrans1042)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1046 := argvalue0.Read(jsProt1045)
		if err1046 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1047 := flag.Arg(2)
		mbTrans1048 := thrift.NewTMemoryBufferLen(len(arg1047))
		defer mbTrans1048.Close()
		_, err1049 := mbTrans1048.WriteString(arg1047)
		if err1049 != nil {
			Usage()
			return
		}
		factory1050 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1051 := factory1050.GetProtocol(mbTrans1048)
		argvalue1 := rtb_adinfo.NewRTBPromotion()
		err1052 := argvalue1.Read(jsProt1051)
		if err1052 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "deletePromotionsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeletePromotionsByIds requires 2 args")
			flag.Usage()
		}
		arg1053 := flag.Arg(1)
		mbTrans1054 := thrift.NewTMemoryBufferLen(len(arg1053))
		defer mbTrans1054.Close()
		_, err1055 := mbTrans1054.WriteString(arg1053)
		if err1055 != nil {
			Usage()
			return
		}
		factory1056 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1057 := factory1056.GetProtocol(mbTrans1054)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1058 := argvalue0.Read(jsProt1057)
		if err1058 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1059 := flag.Arg(2)
		mbTrans1060 := thrift.NewTMemoryBufferLen(len(arg1059))
		defer mbTrans1060.Close()
		_, err1061 := mbTrans1060.WriteString(arg1059)
		if err1061 != nil {
			Usage()
			return
		}
		factory1062 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1063 := factory1062.GetProtocol(mbTrans1060)
		containerStruct1 := rtb_adinfo.NewDeletePromotionsByIdsArgs()
		err1064 := containerStruct1.ReadField2(jsProt1063)
		if err1064 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeletePromotionsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPromotionsByIds requires 2 args")
			flag.Usage()
		}
		arg1065 := flag.Arg(1)
		mbTrans1066 := thrift.NewTMemoryBufferLen(len(arg1065))
		defer mbTrans1066.Close()
		_, err1067 := mbTrans1066.WriteString(arg1065)
		if err1067 != nil {
			Usage()
			return
		}
		factory1068 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1069 := factory1068.GetProtocol(mbTrans1066)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1070 := argvalue0.Read(jsProt1069)
		if err1070 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1071 := flag.Arg(2)
		mbTrans1072 := thrift.NewTMemoryBufferLen(len(arg1071))
		defer mbTrans1072.Close()
		_, err1073 := mbTrans1072.WriteString(arg1071)
		if err1073 != nil {
			Usage()
			return
		}
		factory1074 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1075 := factory1074.GetProtocol(mbTrans1072)
		containerStruct1 := rtb_adinfo.NewGetPromotionsByIdsArgs()
		err1076 := containerStruct1.ReadField2(jsProt1075)
		if err1076 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPromotionsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "exportAllRunnableAds":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "ExportAllRunnableAds requires 1 args")
			flag.Usage()
		}
		arg1077 := flag.Arg(1)
		mbTrans1078 := thrift.NewTMemoryBufferLen(len(arg1077))
		defer mbTrans1078.Close()
		_, err1079 := mbTrans1078.WriteString(arg1077)
		if err1079 != nil {
			Usage()
			return
		}
		factory1080 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1081 := factory1080.GetProtocol(mbTrans1078)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1082 := argvalue0.Read(jsProt1081)
		if err1082 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.ExportAllRunnableAds(value0))
		fmt.Print("\n")
		break
	case "exportObjects":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExportObjects requires 3 args")
			flag.Usage()
		}
		arg1083 := flag.Arg(1)
		mbTrans1084 := thrift.NewTMemoryBufferLen(len(arg1083))
		defer mbTrans1084.Close()
		_, err1085 := mbTrans1084.WriteString(arg1083)
		if err1085 != nil {
			Usage()
			return
		}
		factory1086 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1087 := factory1086.GetProtocol(mbTrans1084)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1088 := argvalue0.Read(jsProt1087)
		if err1088 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1089 := flag.Arg(2)
		mbTrans1090 := thrift.NewTMemoryBufferLen(len(arg1089))
		defer mbTrans1090.Close()
		_, err1091 := mbTrans1090.WriteString(arg1089)
		if err1091 != nil {
			Usage()
			return
		}
		factory1092 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1093 := factory1092.GetProtocol(mbTrans1090)
		containerStruct1 := rtb_adinfo.NewExportObjectsArgs()
		err1094 := containerStruct1.ReadField2(jsProt1093)
		if err1094 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.ExportObjects(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateMedia":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateMedia requires 2 args")
			flag.Usage()
		}
		arg1096 := flag.Arg(1)
		mbTrans1097 := thrift.NewTMemoryBufferLen(len(arg1096))
		defer mbTrans1097.Close()
		_, err1098 := mbTrans1097.WriteString(arg1096)
		if err1098 != nil {
			Usage()
			return
		}
		factory1099 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1100 := factory1099.GetProtocol(mbTrans1097)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1101 := argvalue0.Read(jsProt1100)
		if err1101 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1102 := flag.Arg(2)
		mbTrans1103 := thrift.NewTMemoryBufferLen(len(arg1102))
		defer mbTrans1103.Close()
		_, err1104 := mbTrans1103.WriteString(arg1102)
		if err1104 != nil {
			Usage()
			return
		}
		factory1105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1106 := factory1105.GetProtocol(mbTrans1103)
		containerStruct1 := rtb_adinfo.NewUpdateMediaArgs()
		err1107 := containerStruct1.ReadField2(jsProt1106)
		if err1107 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Media
		value1 := argvalue1
		fmt.Print(client.UpdateMedia(value0, value1))
		fmt.Print("\n")
		break
	case "getMediaByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaByIds requires 2 args")
			flag.Usage()
		}
		arg1108 := flag.Arg(1)
		mbTrans1109 := thrift.NewTMemoryBufferLen(len(arg1108))
		defer mbTrans1109.Close()
		_, err1110 := mbTrans1109.WriteString(arg1108)
		if err1110 != nil {
			Usage()
			return
		}
		factory1111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1112 := factory1111.GetProtocol(mbTrans1109)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1113 := argvalue0.Read(jsProt1112)
		if err1113 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1114 := flag.Arg(2)
		mbTrans1115 := thrift.NewTMemoryBufferLen(len(arg1114))
		defer mbTrans1115.Close()
		_, err1116 := mbTrans1115.WriteString(arg1114)
		if err1116 != nil {
			Usage()
			return
		}
		factory1117 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1118 := factory1117.GetProtocol(mbTrans1115)
		containerStruct1 := rtb_adinfo.NewGetMediaByIdsArgs()
		err1119 := containerStruct1.ReadField2(jsProt1118)
		if err1119 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetMediaByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteMediaByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteMediaByIds requires 2 args")
			flag.Usage()
		}
		arg1120 := flag.Arg(1)
		mbTrans1121 := thrift.NewTMemoryBufferLen(len(arg1120))
		defer mbTrans1121.Close()
		_, err1122 := mbTrans1121.WriteString(arg1120)
		if err1122 != nil {
			Usage()
			return
		}
		factory1123 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1124 := factory1123.GetProtocol(mbTrans1121)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1125 := argvalue0.Read(jsProt1124)
		if err1125 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1126 := flag.Arg(2)
		mbTrans1127 := thrift.NewTMemoryBufferLen(len(arg1126))
		defer mbTrans1127.Close()
		_, err1128 := mbTrans1127.WriteString(arg1126)
		if err1128 != nil {
			Usage()
			return
		}
		factory1129 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1130 := factory1129.GetProtocol(mbTrans1127)
		containerStruct1 := rtb_adinfo.NewDeleteMediaByIdsArgs()
		err1131 := containerStruct1.ReadField2(jsProt1130)
		if err1131 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteMediaByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addMediaGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddMediaGroup requires 2 args")
			flag.Usage()
		}
		arg1132 := flag.Arg(1)
		mbTrans1133 := thrift.NewTMemoryBufferLen(len(arg1132))
		defer mbTrans1133.Close()
		_, err1134 := mbTrans1133.WriteString(arg1132)
		if err1134 != nil {
			Usage()
			return
		}
		factory1135 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1136 := factory1135.GetProtocol(mbTrans1133)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1137 := argvalue0.Read(jsProt1136)
		if err1137 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1138 := flag.Arg(2)
		mbTrans1139 := thrift.NewTMemoryBufferLen(len(arg1138))
		defer mbTrans1139.Close()
		_, err1140 := mbTrans1139.WriteString(arg1138)
		if err1140 != nil {
			Usage()
			return
		}
		factory1141 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1142 := factory1141.GetProtocol(mbTrans1139)
		argvalue1 := rtb_adinfo.NewRTBMediaGroup()
		err1143 := argvalue1.Read(jsProt1142)
		if err1143 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddMediaGroup(value0, value1))
		fmt.Print("\n")
		break
	case "getMediaGroupsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaGroupsByIds requires 2 args")
			flag.Usage()
		}
		arg1144 := flag.Arg(1)
		mbTrans1145 := thrift.NewTMemoryBufferLen(len(arg1144))
		defer mbTrans1145.Close()
		_, err1146 := mbTrans1145.WriteString(arg1144)
		if err1146 != nil {
			Usage()
			return
		}
		factory1147 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1148 := factory1147.GetProtocol(mbTrans1145)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1149 := argvalue0.Read(jsProt1148)
		if err1149 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1150 := flag.Arg(2)
		mbTrans1151 := thrift.NewTMemoryBufferLen(len(arg1150))
		defer mbTrans1151.Close()
		_, err1152 := mbTrans1151.WriteString(arg1150)
		if err1152 != nil {
			Usage()
			return
		}
		factory1153 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1154 := factory1153.GetProtocol(mbTrans1151)
		containerStruct1 := rtb_adinfo.NewGetMediaGroupsByIdsArgs()
		err1155 := containerStruct1.ReadField2(jsProt1154)
		if err1155 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.MediaGroupIds
		value1 := argvalue1
		fmt.Print(client.GetMediaGroupsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "deleteMediaGroupsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteMediaGroupsByIds requires 2 args")
			flag.Usage()
		}
		arg1156 := flag.Arg(1)
		mbTrans1157 := thrift.NewTMemoryBufferLen(len(arg1156))
		defer mbTrans1157.Close()
		_, err1158 := mbTrans1157.WriteString(arg1156)
		if err1158 != nil {
			Usage()
			return
		}
		factory1159 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1160 := factory1159.GetProtocol(mbTrans1157)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1161 := argvalue0.Read(jsProt1160)
		if err1161 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1162 := flag.Arg(2)
		mbTrans1163 := thrift.NewTMemoryBufferLen(len(arg1162))
		defer mbTrans1163.Close()
		_, err1164 := mbTrans1163.WriteString(arg1162)
		if err1164 != nil {
			Usage()
			return
		}
		factory1165 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1166 := factory1165.GetProtocol(mbTrans1163)
		containerStruct1 := rtb_adinfo.NewDeleteMediaGroupsByIdsArgs()
		err1167 := containerStruct1.ReadField2(jsProt1166)
		if err1167 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.MediaGroupIds
		value1 := argvalue1
		fmt.Print(client.DeleteMediaGroupsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "updateMediaGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateMediaGroup requires 2 args")
			flag.Usage()
		}
		arg1168 := flag.Arg(1)
		mbTrans1169 := thrift.NewTMemoryBufferLen(len(arg1168))
		defer mbTrans1169.Close()
		_, err1170 := mbTrans1169.WriteString(arg1168)
		if err1170 != nil {
			Usage()
			return
		}
		factory1171 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1172 := factory1171.GetProtocol(mbTrans1169)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1173 := argvalue0.Read(jsProt1172)
		if err1173 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1174 := flag.Arg(2)
		mbTrans1175 := thrift.NewTMemoryBufferLen(len(arg1174))
		defer mbTrans1175.Close()
		_, err1176 := mbTrans1175.WriteString(arg1174)
		if err1176 != nil {
			Usage()
			return
		}
		factory1177 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1178 := factory1177.GetProtocol(mbTrans1175)
		argvalue1 := rtb_adinfo.NewRTBMediaGroup()
		err1179 := argvalue1.Read(jsProt1178)
		if err1179 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateMediaGroup(value0, value1))
		fmt.Print("\n")
		break
	case "updateMediaAndMediaGroupRelations":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateMediaAndMediaGroupRelations requires 3 args")
			flag.Usage()
		}
		arg1180 := flag.Arg(1)
		mbTrans1181 := thrift.NewTMemoryBufferLen(len(arg1180))
		defer mbTrans1181.Close()
		_, err1182 := mbTrans1181.WriteString(arg1180)
		if err1182 != nil {
			Usage()
			return
		}
		factory1183 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1184 := factory1183.GetProtocol(mbTrans1181)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1185 := argvalue0.Read(jsProt1184)
		if err1185 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1186 := (strconv.Atoi(flag.Arg(2)))
		if err1186 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg1187 := flag.Arg(3)
		mbTrans1188 := thrift.NewTMemoryBufferLen(len(arg1187))
		defer mbTrans1188.Close()
		_, err1189 := mbTrans1188.WriteString(arg1187)
		if err1189 != nil {
			Usage()
			return
		}
		factory1190 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1191 := factory1190.GetProtocol(mbTrans1188)
		containerStruct2 := rtb_adinfo.NewUpdateMediaAndMediaGroupRelationsArgs()
		err1192 := containerStruct2.ReadField3(jsProt1191)
		if err1192 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MediaIds
		value2 := argvalue2
		fmt.Print(client.UpdateMediaAndMediaGroupRelations(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteMediaAndMediaGroupRelations":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteMediaAndMediaGroupRelations requires 3 args")
			flag.Usage()
		}
		arg1193 := flag.Arg(1)
		mbTrans1194 := thrift.NewTMemoryBufferLen(len(arg1193))
		defer mbTrans1194.Close()
		_, err1195 := mbTrans1194.WriteString(arg1193)
		if err1195 != nil {
			Usage()
			return
		}
		factory1196 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1197 := factory1196.GetProtocol(mbTrans1194)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1198 := argvalue0.Read(jsProt1197)
		if err1198 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1199 := (strconv.Atoi(flag.Arg(2)))
		if err1199 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg1200 := flag.Arg(3)
		mbTrans1201 := thrift.NewTMemoryBufferLen(len(arg1200))
		defer mbTrans1201.Close()
		_, err1202 := mbTrans1201.WriteString(arg1200)
		if err1202 != nil {
			Usage()
			return
		}
		factory1203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1204 := factory1203.GetProtocol(mbTrans1201)
		containerStruct2 := rtb_adinfo.NewDeleteMediaAndMediaGroupRelationsArgs()
		err1205 := containerStruct2.ReadField3(jsProt1204)
		if err1205 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.MediaIds
		value2 := argvalue2
		fmt.Print(client.DeleteMediaAndMediaGroupRelations(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listMediaByMediaGroupId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListMediaByMediaGroupId requires 4 args")
			flag.Usage()
		}
		arg1206 := flag.Arg(1)
		mbTrans1207 := thrift.NewTMemoryBufferLen(len(arg1206))
		defer mbTrans1207.Close()
		_, err1208 := mbTrans1207.WriteString(arg1206)
		if err1208 != nil {
			Usage()
			return
		}
		factory1209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1210 := factory1209.GetProtocol(mbTrans1207)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1211 := argvalue0.Read(jsProt1210)
		if err1211 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1212 := (strconv.Atoi(flag.Arg(2)))
		if err1212 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1213 := (strconv.Atoi(flag.Arg(3)))
		if err1213 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err1214 := (strconv.Atoi(flag.Arg(4)))
		if err1214 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListMediaByMediaGroupId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listMediaGroupsByPage":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListMediaGroupsByPage requires 4 args")
			flag.Usage()
		}
		arg1215 := flag.Arg(1)
		mbTrans1216 := thrift.NewTMemoryBufferLen(len(arg1215))
		defer mbTrans1216.Close()
		_, err1217 := mbTrans1216.WriteString(arg1215)
		if err1217 != nil {
			Usage()
			return
		}
		factory1218 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1219 := factory1218.GetProtocol(mbTrans1216)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1220 := argvalue0.Read(jsProt1219)
		if err1220 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1221 := (strconv.Atoi(flag.Arg(2)))
		if err1221 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1222 := (strconv.Atoi(flag.Arg(3)))
		if err1222 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err1223 := (strconv.Atoi(flag.Arg(4)))
		if err1223 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListMediaGroupsByPage(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "editCreativeCapabilities":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "EditCreativeCapabilities requires 4 args")
			flag.Usage()
		}
		arg1224 := flag.Arg(1)
		mbTrans1225 := thrift.NewTMemoryBufferLen(len(arg1224))
		defer mbTrans1225.Close()
		_, err1226 := mbTrans1225.WriteString(arg1224)
		if err1226 != nil {
			Usage()
			return
		}
		factory1227 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1228 := factory1227.GetProtocol(mbTrans1225)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1229 := argvalue0.Read(jsProt1228)
		if err1229 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1230 := (strconv.Atoi(flag.Arg(2)))
		if err1230 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg1231 := flag.Arg(3)
		mbTrans1232 := thrift.NewTMemoryBufferLen(len(arg1231))
		defer mbTrans1232.Close()
		_, err1233 := mbTrans1232.WriteString(arg1231)
		if err1233 != nil {
			Usage()
			return
		}
		factory1234 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1235 := factory1234.GetProtocol(mbTrans1232)
		containerStruct2 := rtb_adinfo.NewEditCreativeCapabilitiesArgs()
		err1236 := containerStruct2.ReadField3(jsProt1235)
		if err1236 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Capabilities
		value2 := argvalue2
		arg1237 := flag.Arg(4)
		mbTrans1238 := thrift.NewTMemoryBufferLen(len(arg1237))
		defer mbTrans1238.Close()
		_, err1239 := mbTrans1238.WriteString(arg1237)
		if err1239 != nil {
			Usage()
			return
		}
		factory1240 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1241 := factory1240.GetProtocol(mbTrans1238)
		containerStruct3 := rtb_adinfo.NewEditCreativeCapabilitiesArgs()
		err1242 := containerStruct3.ReadField4(jsProt1241)
		if err1242 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.RequiredCapabilities
		value3 := argvalue3
		fmt.Print(client.EditCreativeCapabilities(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "editCreativeIndustry":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCreativeIndustry requires 3 args")
			flag.Usage()
		}
		arg1243 := flag.Arg(1)
		mbTrans1244 := thrift.NewTMemoryBufferLen(len(arg1243))
		defer mbTrans1244.Close()
		_, err1245 := mbTrans1244.WriteString(arg1243)
		if err1245 != nil {
			Usage()
			return
		}
		factory1246 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1247 := factory1246.GetProtocol(mbTrans1244)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1248 := argvalue0.Read(jsProt1247)
		if err1248 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1249 := (strconv.Atoi(flag.Arg(2)))
		if err1249 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.EditCreativeIndustry(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdTracking":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdTracking requires 2 args")
			flag.Usage()
		}
		arg1251 := flag.Arg(1)
		mbTrans1252 := thrift.NewTMemoryBufferLen(len(arg1251))
		defer mbTrans1252.Close()
		_, err1253 := mbTrans1252.WriteString(arg1251)
		if err1253 != nil {
			Usage()
			return
		}
		factory1254 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1255 := factory1254.GetProtocol(mbTrans1252)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1256 := argvalue0.Read(jsProt1255)
		if err1256 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1257 := flag.Arg(2)
		mbTrans1258 := thrift.NewTMemoryBufferLen(len(arg1257))
		defer mbTrans1258.Close()
		_, err1259 := mbTrans1258.WriteString(arg1257)
		if err1259 != nil {
			Usage()
			return
		}
		factory1260 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1261 := factory1260.GetProtocol(mbTrans1258)
		argvalue1 := rtb_adinfo.NewAdTrackingInfo()
		err1262 := argvalue1.Read(jsProt1261)
		if err1262 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAdTracking(value0, value1))
		fmt.Print("\n")
		break
	case "editAdTracking":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdTracking requires 2 args")
			flag.Usage()
		}
		arg1263 := flag.Arg(1)
		mbTrans1264 := thrift.NewTMemoryBufferLen(len(arg1263))
		defer mbTrans1264.Close()
		_, err1265 := mbTrans1264.WriteString(arg1263)
		if err1265 != nil {
			Usage()
			return
		}
		factory1266 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1267 := factory1266.GetProtocol(mbTrans1264)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1268 := argvalue0.Read(jsProt1267)
		if err1268 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1269 := flag.Arg(2)
		mbTrans1270 := thrift.NewTMemoryBufferLen(len(arg1269))
		defer mbTrans1270.Close()
		_, err1271 := mbTrans1270.WriteString(arg1269)
		if err1271 != nil {
			Usage()
			return
		}
		factory1272 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1273 := factory1272.GetProtocol(mbTrans1270)
		argvalue1 := rtb_adinfo.NewAdTrackingInfo()
		err1274 := argvalue1.Read(jsProt1273)
		if err1274 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditAdTracking(value0, value1))
		fmt.Print("\n")
		break
	case "updateFrequencyControl":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "UpdateFrequencyControl requires 5 args")
			flag.Usage()
		}
		arg1275 := flag.Arg(1)
		mbTrans1276 := thrift.NewTMemoryBufferLen(len(arg1275))
		defer mbTrans1276.Close()
		_, err1277 := mbTrans1276.WriteString(arg1275)
		if err1277 != nil {
			Usage()
			return
		}
		factory1278 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1279 := factory1278.GetProtocol(mbTrans1276)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1280 := argvalue0.Read(jsProt1279)
		if err1280 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1281 := (strconv.Atoi(flag.Arg(2)))
		if err1281 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg1282 := flag.Arg(3)
		mbTrans1283 := thrift.NewTMemoryBufferLen(len(arg1282))
		defer mbTrans1283.Close()
		_, err1284 := mbTrans1283.WriteString(arg1282)
		if err1284 != nil {
			Usage()
			return
		}
		factory1285 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1286 := factory1285.GetProtocol(mbTrans1283)
		containerStruct2 := rtb_adinfo.NewUpdateFrequencyControlArgs()
		err1287 := containerStruct2.ReadField3(jsProt1286)
		if err1287 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ProductIdFreqGp
		value2 := argvalue2
		tmp3, err1288 := (strconv.Atoi(flag.Arg(4)))
		if err1288 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err1289 := (strconv.Atoi(flag.Arg(5)))
		if err1289 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.UpdateFrequencyControl(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "deleteAdTrackingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdTrackingByIds requires 2 args")
			flag.Usage()
		}
		arg1290 := flag.Arg(1)
		mbTrans1291 := thrift.NewTMemoryBufferLen(len(arg1290))
		defer mbTrans1291.Close()
		_, err1292 := mbTrans1291.WriteString(arg1290)
		if err1292 != nil {
			Usage()
			return
		}
		factory1293 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1294 := factory1293.GetProtocol(mbTrans1291)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1295 := argvalue0.Read(jsProt1294)
		if err1295 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1296 := flag.Arg(2)
		mbTrans1297 := thrift.NewTMemoryBufferLen(len(arg1296))
		defer mbTrans1297.Close()
		_, err1298 := mbTrans1297.WriteString(arg1296)
		if err1298 != nil {
			Usage()
			return
		}
		factory1299 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1300 := factory1299.GetProtocol(mbTrans1297)
		containerStruct1 := rtb_adinfo.NewDeleteAdTrackingByIdsArgs()
		err1301 := containerStruct1.ReadField2(jsProt1300)
		if err1301 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteAdTrackingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAdTrackingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdTrackingByIds requires 2 args")
			flag.Usage()
		}
		arg1302 := flag.Arg(1)
		mbTrans1303 := thrift.NewTMemoryBufferLen(len(arg1302))
		defer mbTrans1303.Close()
		_, err1304 := mbTrans1303.WriteString(arg1302)
		if err1304 != nil {
			Usage()
			return
		}
		factory1305 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1306 := factory1305.GetProtocol(mbTrans1303)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1307 := argvalue0.Read(jsProt1306)
		if err1307 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1308 := flag.Arg(2)
		mbTrans1309 := thrift.NewTMemoryBufferLen(len(arg1308))
		defer mbTrans1309.Close()
		_, err1310 := mbTrans1309.WriteString(arg1308)
		if err1310 != nil {
			Usage()
			return
		}
		factory1311 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1312 := factory1311.GetProtocol(mbTrans1309)
		containerStruct1 := rtb_adinfo.NewGetAdTrackingByIdsArgs()
		err1313 := containerStruct1.ReadField2(jsProt1312)
		if err1313 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdTrackingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listAdTrackingByPromotionId":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAdTrackingByPromotionId requires 4 args")
			flag.Usage()
		}
		arg1314 := flag.Arg(1)
		mbTrans1315 := thrift.NewTMemoryBufferLen(len(arg1314))
		defer mbTrans1315.Close()
		_, err1316 := mbTrans1315.WriteString(arg1314)
		if err1316 != nil {
			Usage()
			return
		}
		factory1317 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1318 := factory1317.GetProtocol(mbTrans1315)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1319 := argvalue0.Read(jsProt1318)
		if err1319 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1320 := (strconv.Atoi(flag.Arg(2)))
		if err1320 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1321 := (strconv.Atoi(flag.Arg(3)))
		if err1321 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err1322 := (strconv.Atoi(flag.Arg(4)))
		if err1322 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListAdTrackingByPromotionId(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addDeal":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddDeal requires 2 args")
			flag.Usage()
		}
		arg1323 := flag.Arg(1)
		mbTrans1324 := thrift.NewTMemoryBufferLen(len(arg1323))
		defer mbTrans1324.Close()
		_, err1325 := mbTrans1324.WriteString(arg1323)
		if err1325 != nil {
			Usage()
			return
		}
		factory1326 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1327 := factory1326.GetProtocol(mbTrans1324)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1328 := argvalue0.Read(jsProt1327)
		if err1328 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1329 := flag.Arg(2)
		mbTrans1330 := thrift.NewTMemoryBufferLen(len(arg1329))
		defer mbTrans1330.Close()
		_, err1331 := mbTrans1330.WriteString(arg1329)
		if err1331 != nil {
			Usage()
			return
		}
		factory1332 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1333 := factory1332.GetProtocol(mbTrans1330)
		argvalue1 := rtb_adinfo.NewRTBDeal()
		err1334 := argvalue1.Read(jsProt1333)
		if err1334 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddDeal(value0, value1))
		fmt.Print("\n")
		break
	case "editDeal":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditDeal requires 2 args")
			flag.Usage()
		}
		arg1335 := flag.Arg(1)
		mbTrans1336 := thrift.NewTMemoryBufferLen(len(arg1335))
		defer mbTrans1336.Close()
		_, err1337 := mbTrans1336.WriteString(arg1335)
		if err1337 != nil {
			Usage()
			return
		}
		factory1338 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1339 := factory1338.GetProtocol(mbTrans1336)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1340 := argvalue0.Read(jsProt1339)
		if err1340 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1341 := flag.Arg(2)
		mbTrans1342 := thrift.NewTMemoryBufferLen(len(arg1341))
		defer mbTrans1342.Close()
		_, err1343 := mbTrans1342.WriteString(arg1341)
		if err1343 != nil {
			Usage()
			return
		}
		factory1344 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1345 := factory1344.GetProtocol(mbTrans1342)
		argvalue1 := rtb_adinfo.NewRTBDeal()
		err1346 := argvalue1.Read(jsProt1345)
		if err1346 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditDeal(value0, value1))
		fmt.Print("\n")
		break
	case "addExchangeDmp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddExchangeDmp requires 2 args")
			flag.Usage()
		}
		arg1347 := flag.Arg(1)
		mbTrans1348 := thrift.NewTMemoryBufferLen(len(arg1347))
		defer mbTrans1348.Close()
		_, err1349 := mbTrans1348.WriteString(arg1347)
		if err1349 != nil {
			Usage()
			return
		}
		factory1350 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1351 := factory1350.GetProtocol(mbTrans1348)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1352 := argvalue0.Read(jsProt1351)
		if err1352 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1353 := flag.Arg(2)
		mbTrans1354 := thrift.NewTMemoryBufferLen(len(arg1353))
		defer mbTrans1354.Close()
		_, err1355 := mbTrans1354.WriteString(arg1353)
		if err1355 != nil {
			Usage()
			return
		}
		factory1356 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1357 := factory1356.GetProtocol(mbTrans1354)
		argvalue1 := rtb_adinfo.NewRTBExchangeDmp()
		err1358 := argvalue1.Read(jsProt1357)
		if err1358 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddExchangeDmp(value0, value1))
		fmt.Print("\n")
		break
	case "editExchangeDmp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditExchangeDmp requires 2 args")
			flag.Usage()
		}
		arg1359 := flag.Arg(1)
		mbTrans1360 := thrift.NewTMemoryBufferLen(len(arg1359))
		defer mbTrans1360.Close()
		_, err1361 := mbTrans1360.WriteString(arg1359)
		if err1361 != nil {
			Usage()
			return
		}
		factory1362 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1363 := factory1362.GetProtocol(mbTrans1360)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1364 := argvalue0.Read(jsProt1363)
		if err1364 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1365 := flag.Arg(2)
		mbTrans1366 := thrift.NewTMemoryBufferLen(len(arg1365))
		defer mbTrans1366.Close()
		_, err1367 := mbTrans1366.WriteString(arg1365)
		if err1367 != nil {
			Usage()
			return
		}
		factory1368 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1369 := factory1368.GetProtocol(mbTrans1366)
		argvalue1 := rtb_adinfo.NewRTBExchangeDmp()
		err1370 := argvalue1.Read(jsProt1369)
		if err1370 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditExchangeDmp(value0, value1))
		fmt.Print("\n")
		break
	case "updateExchangeDmpResult":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "UpdateExchangeDmpResult requires 6 args")
			flag.Usage()
		}
		arg1371 := flag.Arg(1)
		mbTrans1372 := thrift.NewTMemoryBufferLen(len(arg1371))
		defer mbTrans1372.Close()
		_, err1373 := mbTrans1372.WriteString(arg1371)
		if err1373 != nil {
			Usage()
			return
		}
		factory1374 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1375 := factory1374.GetProtocol(mbTrans1372)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1376 := argvalue0.Read(jsProt1375)
		if err1376 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1377 := (strconv.Atoi(flag.Arg(2)))
		if err1377 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1378 := (strconv.Atoi(flag.Arg(3)))
		if err1378 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		arg1380 := flag.Arg(5)
		mbTrans1381 := thrift.NewTMemoryBufferLen(len(arg1380))
		defer mbTrans1381.Close()
		_, err1382 := mbTrans1381.WriteString(arg1380)
		if err1382 != nil {
			Usage()
			return
		}
		factory1383 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1384 := factory1383.GetProtocol(mbTrans1381)
		containerStruct4 := rtb_adinfo.NewUpdateExchangeDmpResultArgs()
		err1385 := containerStruct4.ReadField5(jsProt1384)
		if err1385 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.ExchangeAudienceIds
		value4 := argvalue4
		tmp5, err1386 := (strconv.Atoi(flag.Arg(6)))
		if err1386 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.UpdateExchangeDmpResult(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "updateExchangeDmpStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateExchangeDmpStatus requires 3 args")
			flag.Usage()
		}
		arg1387 := flag.Arg(1)
		mbTrans1388 := thrift.NewTMemoryBufferLen(len(arg1387))
		defer mbTrans1388.Close()
		_, err1389 := mbTrans1388.WriteString(arg1387)
		if err1389 != nil {
			Usage()
			return
		}
		factory1390 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1391 := factory1390.GetProtocol(mbTrans1388)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1392 := argvalue0.Read(jsProt1391)
		if err1392 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1393 := (strconv.Atoi(flag.Arg(2)))
		if err1393 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1394 := (strconv.Atoi(flag.Arg(3)))
		if err1394 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.UpdateExchangeDmpStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listExchangeDmpsByComputeStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListExchangeDmpsByComputeStatus requires 6 args")
			flag.Usage()
		}
		arg1395 := flag.Arg(1)
		mbTrans1396 := thrift.NewTMemoryBufferLen(len(arg1395))
		defer mbTrans1396.Close()
		_, err1397 := mbTrans1396.WriteString(arg1395)
		if err1397 != nil {
			Usage()
			return
		}
		factory1398 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1399 := factory1398.GetProtocol(mbTrans1396)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1400 := argvalue0.Read(jsProt1399)
		if err1400 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1401 := (strconv.Atoi(flag.Arg(2)))
		if err1401 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg1402 := flag.Arg(3)
		mbTrans1403 := thrift.NewTMemoryBufferLen(len(arg1402))
		defer mbTrans1403.Close()
		_, err1404 := mbTrans1403.WriteString(arg1402)
		if err1404 != nil {
			Usage()
			return
		}
		factory1405 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1406 := factory1405.GetProtocol(mbTrans1403)
		containerStruct2 := rtb_adinfo.NewListExchangeDmpsByComputeStatusArgs()
		err1407 := containerStruct2.ReadField3(jsProt1406)
		if err1407 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ComputeStatuses
		value2 := argvalue2
		arg1408 := flag.Arg(4)
		mbTrans1409 := thrift.NewTMemoryBufferLen(len(arg1408))
		defer mbTrans1409.Close()
		_, err1410 := mbTrans1409.WriteString(arg1408)
		if err1410 != nil {
			Usage()
			return
		}
		factory1411 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1412 := factory1411.GetProtocol(mbTrans1409)
		containerStruct3 := rtb_adinfo.NewListExchangeDmpsByComputeStatusArgs()
		err1413 := containerStruct3.ReadField4(jsProt1412)
		if err1413 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.CreateSources
		value3 := argvalue3
		tmp4, err1414 := (strconv.Atoi(flag.Arg(5)))
		if err1414 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		tmp5, err1415 := (strconv.Atoi(flag.Arg(6)))
		if err1415 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := argvalue5
		fmt.Print(client.ListExchangeDmpsByComputeStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getExchangeDmpsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetExchangeDmpsByIds requires 2 args")
			flag.Usage()
		}
		arg1416 := flag.Arg(1)
		mbTrans1417 := thrift.NewTMemoryBufferLen(len(arg1416))
		defer mbTrans1417.Close()
		_, err1418 := mbTrans1417.WriteString(arg1416)
		if err1418 != nil {
			Usage()
			return
		}
		factory1419 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1420 := factory1419.GetProtocol(mbTrans1417)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1421 := argvalue0.Read(jsProt1420)
		if err1421 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1422 := flag.Arg(2)
		mbTrans1423 := thrift.NewTMemoryBufferLen(len(arg1422))
		defer mbTrans1423.Close()
		_, err1424 := mbTrans1423.WriteString(arg1422)
		if err1424 != nil {
			Usage()
			return
		}
		factory1425 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1426 := factory1425.GetProtocol(mbTrans1423)
		containerStruct1 := rtb_adinfo.NewGetExchangeDmpsByIdsArgs()
		err1427 := containerStruct1.ReadField2(jsProt1426)
		if err1427 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetExchangeDmpsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "updateExchangeDmpAudienceCount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateExchangeDmpAudienceCount requires 2 args")
			flag.Usage()
		}
		arg1428 := flag.Arg(1)
		mbTrans1429 := thrift.NewTMemoryBufferLen(len(arg1428))
		defer mbTrans1429.Close()
		_, err1430 := mbTrans1429.WriteString(arg1428)
		if err1430 != nil {
			Usage()
			return
		}
		factory1431 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1432 := factory1431.GetProtocol(mbTrans1429)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1433 := argvalue0.Read(jsProt1432)
		if err1433 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1434 := flag.Arg(2)
		mbTrans1435 := thrift.NewTMemoryBufferLen(len(arg1434))
		defer mbTrans1435.Close()
		_, err1436 := mbTrans1435.WriteString(arg1434)
		if err1436 != nil {
			Usage()
			return
		}
		factory1437 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1438 := factory1437.GetProtocol(mbTrans1435)
		argvalue1 := rtb_adinfo.NewRTBExchangeDmpAudienceCount()
		err1439 := argvalue1.Read(jsProt1438)
		if err1439 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UpdateExchangeDmpAudienceCount(value0, value1))
		fmt.Print("\n")
		break
	case "getLatestExchangeDmpAudienceCount":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetLatestExchangeDmpAudienceCount requires 3 args")
			flag.Usage()
		}
		arg1440 := flag.Arg(1)
		mbTrans1441 := thrift.NewTMemoryBufferLen(len(arg1440))
		defer mbTrans1441.Close()
		_, err1442 := mbTrans1441.WriteString(arg1440)
		if err1442 != nil {
			Usage()
			return
		}
		factory1443 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1444 := factory1443.GetProtocol(mbTrans1441)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1445 := argvalue0.Read(jsProt1444)
		if err1445 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1446 := (strconv.Atoi(flag.Arg(2)))
		if err1446 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1447 := (strconv.Atoi(flag.Arg(3)))
		if err1447 != nil {
			Usage()
			return
		}
		argvalue2 := byte(tmp2)
		value2 := argvalue2
		fmt.Print(client.GetLatestExchangeDmpAudienceCount(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateExchangeDmpComputeStatus":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "UpdateExchangeDmpComputeStatus requires 4 args")
			flag.Usage()
		}
		arg1448 := flag.Arg(1)
		mbTrans1449 := thrift.NewTMemoryBufferLen(len(arg1448))
		defer mbTrans1449.Close()
		_, err1450 := mbTrans1449.WriteString(arg1448)
		if err1450 != nil {
			Usage()
			return
		}
		factory1451 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1452 := factory1451.GetProtocol(mbTrans1449)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1453 := argvalue0.Read(jsProt1452)
		if err1453 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1454 := (strconv.Atoi(flag.Arg(2)))
		if err1454 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1455 := (strconv.Atoi(flag.Arg(3)))
		if err1455 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err1456 := (strconv.Atoi(flag.Arg(4)))
		if err1456 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.UpdateExchangeDmpComputeStatus(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listExchangeDmpsByParams":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListExchangeDmpsByParams requires 5 args")
			flag.Usage()
		}
		arg1457 := flag.Arg(1)
		mbTrans1458 := thrift.NewTMemoryBufferLen(len(arg1457))
		defer mbTrans1458.Close()
		_, err1459 := mbTrans1458.WriteString(arg1457)
		if err1459 != nil {
			Usage()
			return
		}
		factory1460 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1461 := factory1460.GetProtocol(mbTrans1458)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1462 := argvalue0.Read(jsProt1461)
		if err1462 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1463 := flag.Arg(2)
		mbTrans1464 := thrift.NewTMemoryBufferLen(len(arg1463))
		defer mbTrans1464.Close()
		_, err1465 := mbTrans1464.WriteString(arg1463)
		if err1465 != nil {
			Usage()
			return
		}
		factory1466 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1467 := factory1466.GetProtocol(mbTrans1464)
		containerStruct1 := rtb_adinfo.NewListExchangeDmpsByParamsArgs()
		err1468 := containerStruct1.ReadField2(jsProt1467)
		if err1468 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Level
		value1 := argvalue1
		arg1469 := flag.Arg(3)
		mbTrans1470 := thrift.NewTMemoryBufferLen(len(arg1469))
		defer mbTrans1470.Close()
		_, err1471 := mbTrans1470.WriteString(arg1469)
		if err1471 != nil {
			Usage()
			return
		}
		factory1472 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1473 := factory1472.GetProtocol(mbTrans1470)
		containerStruct2 := rtb_adinfo.NewListExchangeDmpsByParamsArgs()
		err1474 := containerStruct2.ReadField3(jsProt1473)
		if err1474 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.CreateSources
		value2 := argvalue2
		tmp3, err1475 := (strconv.Atoi(flag.Arg(4)))
		if err1475 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err1476 := (strconv.Atoi(flag.Arg(5)))
		if err1476 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListExchangeDmpsByParams(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addSponsorSendSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSponsorSendSetting requires 2 args")
			flag.Usage()
		}
		arg1477 := flag.Arg(1)
		mbTrans1478 := thrift.NewTMemoryBufferLen(len(arg1477))
		defer mbTrans1478.Close()
		_, err1479 := mbTrans1478.WriteString(arg1477)
		if err1479 != nil {
			Usage()
			return
		}
		factory1480 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1481 := factory1480.GetProtocol(mbTrans1478)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1482 := argvalue0.Read(jsProt1481)
		if err1482 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1483 := flag.Arg(2)
		mbTrans1484 := thrift.NewTMemoryBufferLen(len(arg1483))
		defer mbTrans1484.Close()
		_, err1485 := mbTrans1484.WriteString(arg1483)
		if err1485 != nil {
			Usage()
			return
		}
		factory1486 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1487 := factory1486.GetProtocol(mbTrans1484)
		argvalue1 := rtb_adinfo.NewRTBSponsorSendSetting()
		err1488 := argvalue1.Read(jsProt1487)
		if err1488 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSponsorSendSetting(value0, value1))
		fmt.Print("\n")
		break
	case "editSponsorSendSetting":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditSponsorSendSetting requires 2 args")
			flag.Usage()
		}
		arg1489 := flag.Arg(1)
		mbTrans1490 := thrift.NewTMemoryBufferLen(len(arg1489))
		defer mbTrans1490.Close()
		_, err1491 := mbTrans1490.WriteString(arg1489)
		if err1491 != nil {
			Usage()
			return
		}
		factory1492 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1493 := factory1492.GetProtocol(mbTrans1490)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1494 := argvalue0.Read(jsProt1493)
		if err1494 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1495 := flag.Arg(2)
		mbTrans1496 := thrift.NewTMemoryBufferLen(len(arg1495))
		defer mbTrans1496.Close()
		_, err1497 := mbTrans1496.WriteString(arg1495)
		if err1497 != nil {
			Usage()
			return
		}
		factory1498 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1499 := factory1498.GetProtocol(mbTrans1496)
		argvalue1 := rtb_adinfo.NewRTBSponsorSendSetting()
		err1500 := argvalue1.Read(jsProt1499)
		if err1500 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditSponsorSendSetting(value0, value1))
		fmt.Print("\n")
		break
	case "deleteSponsorSendSettingsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteSponsorSendSettingsByIds requires 2 args")
			flag.Usage()
		}
		arg1501 := flag.Arg(1)
		mbTrans1502 := thrift.NewTMemoryBufferLen(len(arg1501))
		defer mbTrans1502.Close()
		_, err1503 := mbTrans1502.WriteString(arg1501)
		if err1503 != nil {
			Usage()
			return
		}
		factory1504 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1505 := factory1504.GetProtocol(mbTrans1502)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1506 := argvalue0.Read(jsProt1505)
		if err1506 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1507 := flag.Arg(2)
		mbTrans1508 := thrift.NewTMemoryBufferLen(len(arg1507))
		defer mbTrans1508.Close()
		_, err1509 := mbTrans1508.WriteString(arg1507)
		if err1509 != nil {
			Usage()
			return
		}
		factory1510 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1511 := factory1510.GetProtocol(mbTrans1508)
		containerStruct1 := rtb_adinfo.NewDeleteSponsorSendSettingsByIdsArgs()
		err1512 := containerStruct1.ReadField2(jsProt1511)
		if err1512 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteSponsorSendSettingsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorSendSettingsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorSendSettingsByIds requires 2 args")
			flag.Usage()
		}
		arg1513 := flag.Arg(1)
		mbTrans1514 := thrift.NewTMemoryBufferLen(len(arg1513))
		defer mbTrans1514.Close()
		_, err1515 := mbTrans1514.WriteString(arg1513)
		if err1515 != nil {
			Usage()
			return
		}
		factory1516 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1517 := factory1516.GetProtocol(mbTrans1514)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1518 := argvalue0.Read(jsProt1517)
		if err1518 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1519 := flag.Arg(2)
		mbTrans1520 := thrift.NewTMemoryBufferLen(len(arg1519))
		defer mbTrans1520.Close()
		_, err1521 := mbTrans1520.WriteString(arg1519)
		if err1521 != nil {
			Usage()
			return
		}
		factory1522 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1523 := factory1522.GetProtocol(mbTrans1520)
		containerStruct1 := rtb_adinfo.NewGetSponsorSendSettingsByIdsArgs()
		err1524 := containerStruct1.ReadField2(jsProt1523)
		if err1524 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSponsorSendSettingsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorSendSettingsBySponsorIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorSendSettingsBySponsorIds requires 2 args")
			flag.Usage()
		}
		arg1525 := flag.Arg(1)
		mbTrans1526 := thrift.NewTMemoryBufferLen(len(arg1525))
		defer mbTrans1526.Close()
		_, err1527 := mbTrans1526.WriteString(arg1525)
		if err1527 != nil {
			Usage()
			return
		}
		factory1528 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1529 := factory1528.GetProtocol(mbTrans1526)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1530 := argvalue0.Read(jsProt1529)
		if err1530 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1531 := flag.Arg(2)
		mbTrans1532 := thrift.NewTMemoryBufferLen(len(arg1531))
		defer mbTrans1532.Close()
		_, err1533 := mbTrans1532.WriteString(arg1531)
		if err1533 != nil {
			Usage()
			return
		}
		factory1534 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1535 := factory1534.GetProtocol(mbTrans1532)
		containerStruct1 := rtb_adinfo.NewGetSponsorSendSettingsBySponsorIdsArgs()
		err1536 := containerStruct1.ReadField2(jsProt1535)
		if err1536 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.SponsorIds
		value1 := argvalue1
		fmt.Print(client.GetSponsorSendSettingsBySponsorIds(value0, value1))
		fmt.Print("\n")
		break
	case "getBidCreativesByExchangeId":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetBidCreativesByExchangeId requires 5 args")
			flag.Usage()
		}
		arg1537 := flag.Arg(1)
		mbTrans1538 := thrift.NewTMemoryBufferLen(len(arg1537))
		defer mbTrans1538.Close()
		_, err1539 := mbTrans1538.WriteString(arg1537)
		if err1539 != nil {
			Usage()
			return
		}
		factory1540 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1541 := factory1540.GetProtocol(mbTrans1538)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1542 := argvalue0.Read(jsProt1541)
		if err1542 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1543 := (strconv.Atoi(flag.Arg(2)))
		if err1543 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1544 := (strconv.Atoi(flag.Arg(3)))
		if err1544 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err1545 := (strconv.Atoi(flag.Arg(4)))
		if err1545 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err1546 := (strconv.Atoi(flag.Arg(5)))
		if err1546 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetBidCreativesByExchangeId(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getBidStrategiesByExchangeId":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetBidStrategiesByExchangeId requires 5 args")
			flag.Usage()
		}
		arg1547 := flag.Arg(1)
		mbTrans1548 := thrift.NewTMemoryBufferLen(len(arg1547))
		defer mbTrans1548.Close()
		_, err1549 := mbTrans1548.WriteString(arg1547)
		if err1549 != nil {
			Usage()
			return
		}
		factory1550 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1551 := factory1550.GetProtocol(mbTrans1548)
		argvalue0 := rtb_adinfo.NewRequestHeader()
		err1552 := argvalue0.Read(jsProt1551)
		if err1552 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err1553 := (strconv.Atoi(flag.Arg(2)))
		if err1553 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err1554 := (strconv.Atoi(flag.Arg(3)))
		if err1554 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err1555 := (strconv.Atoi(flag.Arg(4)))
		if err1555 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err1556 := (strconv.Atoi(flag.Arg(5)))
		if err1556 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.GetBidStrategiesByExchangeId(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err1561 := (strconv.Atoi(flag.Arg(1)))
		if err1561 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
