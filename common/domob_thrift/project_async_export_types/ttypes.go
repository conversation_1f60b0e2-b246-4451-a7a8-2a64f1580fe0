// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package project_async_export_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type UidInt common.UidInt

type IdInt common.IdInt

type Amount common.Amount

type TimeInt common.TimeInt

type QueryResult *common.QueryResult

type IncomeSysReceiveQueryParam struct {
	Month             string `thrift:"month,1" json:"month"`
	OrderId           int32  `thrift:"order_id,2" json:"order_id"`
	SdId              int32  `thrift:"sd_id,3" json:"sd_id"`
	CompanyName       string `thrift:"company_name,4" json:"company_name"`
	RemarkNum         string `thrift:"remark_num,5" json:"remark_num"`
	ProductLine       int32  `thrift:"product_line,6" json:"product_line"`
	OurCompany        int32  `thrift:"our_company,7" json:"our_company"`
	Department        int32  `thrift:"department,8" json:"department"`
	SaleMan           string `thrift:"sale_man,9" json:"sale_man"`
	Status            int32  `thrift:"status,10" json:"status"`
	IsSubtract        int32  `thrift:"is_subtract,11" json:"is_subtract"`
	MediaPlatformName string `thrift:"media_platform_name,12" json:"media_platform_name"`
	MediaIdentifying  string `thrift:"media_identifying,13" json:"media_identifying"`
}

func NewIncomeSysReceiveQueryParam() *IncomeSysReceiveQueryParam {
	return &IncomeSysReceiveQueryParam{}
}

func (p *IncomeSysReceiveQueryParam) IsSetMonth() bool {
	return p.Month != ""
}

func (p *IncomeSysReceiveQueryParam) IsSetOrderId() bool {
	return p.OrderId != 0
}

func (p *IncomeSysReceiveQueryParam) IsSetSdId() bool {
	return p.SdId != 0
}

func (p *IncomeSysReceiveQueryParam) IsSetCompanyName() bool {
	return p.CompanyName != ""
}

func (p *IncomeSysReceiveQueryParam) IsSetRemarkNum() bool {
	return p.RemarkNum != ""
}

func (p *IncomeSysReceiveQueryParam) IsSetProductLine() bool {
	return p.ProductLine != 0
}

func (p *IncomeSysReceiveQueryParam) IsSetOurCompany() bool {
	return p.OurCompany != 0
}

func (p *IncomeSysReceiveQueryParam) IsSetDepartment() bool {
	return p.Department != 0
}

func (p *IncomeSysReceiveQueryParam) IsSetSaleMan() bool {
	return p.SaleMan != ""
}

func (p *IncomeSysReceiveQueryParam) IsSetStatus() bool {
	return p.Status != 0
}

func (p *IncomeSysReceiveQueryParam) IsSetIsSubtract() bool {
	return p.IsSubtract != 0
}

func (p *IncomeSysReceiveQueryParam) IsSetMediaPlatformName() bool {
	return p.MediaPlatformName != ""
}

func (p *IncomeSysReceiveQueryParam) IsSetMediaIdentifying() bool {
	return p.MediaIdentifying != ""
}

func (p *IncomeSysReceiveQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Month = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CompanyName = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RemarkNum = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductLine = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.OurCompany = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Department = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SaleMan = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.IsSubtract = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.MediaPlatformName = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.MediaIdentifying = v
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IncomeSysReceiveQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncomeSysReceiveQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMonth() {
		if err := oprot.WriteFieldBegin("month", thrift.STRING, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:month: %s", p, err)
		}
		if err := oprot.WriteString(string(p.Month)); err != nil {
			return fmt.Errorf("%T.month (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:month: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderId() {
		if err := oprot.WriteFieldBegin("order_id", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:order_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
			return fmt.Errorf("%T.order_id (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:order_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSdId() {
		if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SdId)); err != nil {
			return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCompanyName() {
		if err := oprot.WriteFieldBegin("company_name", thrift.STRING, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:company_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.CompanyName)); err != nil {
			return fmt.Errorf("%T.company_name (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:company_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRemarkNum() {
		if err := oprot.WriteFieldBegin("remark_num", thrift.STRING, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:remark_num: %s", p, err)
		}
		if err := oprot.WriteString(string(p.RemarkNum)); err != nil {
			return fmt.Errorf("%T.remark_num (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:remark_num: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductLine() {
		if err := oprot.WriteFieldBegin("product_line", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:product_line: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProductLine)); err != nil {
			return fmt.Errorf("%T.product_line (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:product_line: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetOurCompany() {
		if err := oprot.WriteFieldBegin("our_company", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:our_company: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OurCompany)); err != nil {
			return fmt.Errorf("%T.our_company (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:our_company: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDepartment() {
		if err := oprot.WriteFieldBegin("department", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:department: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Department)); err != nil {
			return fmt.Errorf("%T.department (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:department: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSaleMan() {
		if err := oprot.WriteFieldBegin("sale_man", thrift.STRING, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:sale_man: %s", p, err)
		}
		if err := oprot.WriteString(string(p.SaleMan)); err != nil {
			return fmt.Errorf("%T.sale_man (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:sale_man: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:status: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetIsSubtract() {
		if err := oprot.WriteFieldBegin("is_subtract", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:is_subtract: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.IsSubtract)); err != nil {
			return fmt.Errorf("%T.is_subtract (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:is_subtract: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaPlatformName() {
		if err := oprot.WriteFieldBegin("media_platform_name", thrift.STRING, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:media_platform_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaPlatformName)); err != nil {
			return fmt.Errorf("%T.media_platform_name (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:media_platform_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaIdentifying() {
		if err := oprot.WriteFieldBegin("media_identifying", thrift.STRING, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:media_identifying: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaIdentifying)); err != nil {
			return fmt.Errorf("%T.media_identifying (13) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:media_identifying: %s", p, err)
		}
	}
	return err
}

func (p *IncomeSysReceiveQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncomeSysReceiveQueryParam(%+v)", *p)
}

type IncomeMyDeliveryQueryParam struct {
	Month             string `thrift:"month,1" json:"month"`
	OrderId           int32  `thrift:"order_id,2" json:"order_id"`
	SdId              int32  `thrift:"sd_id,3" json:"sd_id"`
	CompanyName       string `thrift:"company_name,4" json:"company_name"`
	RemarkNum         string `thrift:"remark_num,5" json:"remark_num"`
	ProductLine       int32  `thrift:"product_line,6" json:"product_line"`
	Status            int32  `thrift:"status,7" json:"status"`
	MediaPlatformName string `thrift:"media_platform_name,8" json:"media_platform_name"`
	MediaIdentifying  string `thrift:"media_identifying,9" json:"media_identifying"`
}

func NewIncomeMyDeliveryQueryParam() *IncomeMyDeliveryQueryParam {
	return &IncomeMyDeliveryQueryParam{}
}

func (p *IncomeMyDeliveryQueryParam) IsSetMonth() bool {
	return p.Month != ""
}

func (p *IncomeMyDeliveryQueryParam) IsSetOrderId() bool {
	return p.OrderId != 0
}

func (p *IncomeMyDeliveryQueryParam) IsSetSdId() bool {
	return p.SdId != 0
}

func (p *IncomeMyDeliveryQueryParam) IsSetCompanyName() bool {
	return p.CompanyName != ""
}

func (p *IncomeMyDeliveryQueryParam) IsSetRemarkNum() bool {
	return p.RemarkNum != ""
}

func (p *IncomeMyDeliveryQueryParam) IsSetProductLine() bool {
	return p.ProductLine != 0
}

func (p *IncomeMyDeliveryQueryParam) IsSetStatus() bool {
	return p.Status != 0
}

func (p *IncomeMyDeliveryQueryParam) IsSetMediaPlatformName() bool {
	return p.MediaPlatformName != ""
}

func (p *IncomeMyDeliveryQueryParam) IsSetMediaIdentifying() bool {
	return p.MediaIdentifying != ""
}

func (p *IncomeMyDeliveryQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Month = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CompanyName = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RemarkNum = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductLine = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.MediaPlatformName = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.MediaIdentifying = v
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IncomeMyDeliveryQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncomeMyDeliveryQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMonth() {
		if err := oprot.WriteFieldBegin("month", thrift.STRING, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:month: %s", p, err)
		}
		if err := oprot.WriteString(string(p.Month)); err != nil {
			return fmt.Errorf("%T.month (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:month: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderId() {
		if err := oprot.WriteFieldBegin("order_id", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:order_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
			return fmt.Errorf("%T.order_id (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:order_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSdId() {
		if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SdId)); err != nil {
			return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCompanyName() {
		if err := oprot.WriteFieldBegin("company_name", thrift.STRING, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:company_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.CompanyName)); err != nil {
			return fmt.Errorf("%T.company_name (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:company_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRemarkNum() {
		if err := oprot.WriteFieldBegin("remark_num", thrift.STRING, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:remark_num: %s", p, err)
		}
		if err := oprot.WriteString(string(p.RemarkNum)); err != nil {
			return fmt.Errorf("%T.remark_num (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:remark_num: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductLine() {
		if err := oprot.WriteFieldBegin("product_line", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:product_line: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProductLine)); err != nil {
			return fmt.Errorf("%T.product_line (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:product_line: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:status: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaPlatformName() {
		if err := oprot.WriteFieldBegin("media_platform_name", thrift.STRING, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:media_platform_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaPlatformName)); err != nil {
			return fmt.Errorf("%T.media_platform_name (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:media_platform_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaIdentifying() {
		if err := oprot.WriteFieldBegin("media_identifying", thrift.STRING, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:media_identifying: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaIdentifying)); err != nil {
			return fmt.Errorf("%T.media_identifying (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:media_identifying: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMyDeliveryQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncomeMyDeliveryQueryParam(%+v)", *p)
}

type IncomeFinanceSettleQueryParam struct {
	Month             string `thrift:"month,1" json:"month"`
	OrderId           int32  `thrift:"order_id,2" json:"order_id"`
	SdId              int32  `thrift:"sd_id,3" json:"sd_id"`
	CompanyName       string `thrift:"company_name,4" json:"company_name"`
	RemarkNum         string `thrift:"remark_num,5" json:"remark_num"`
	ProductLine       int32  `thrift:"product_line,6" json:"product_line"`
	OurCompany        int32  `thrift:"our_company,7" json:"our_company"`
	Department        int32  `thrift:"department,8" json:"department"`
	SaleMan           string `thrift:"sale_man,9" json:"sale_man"`
	SettleId          int32  `thrift:"settle_id,10" json:"settle_id"`
	MediaPlatformName string `thrift:"media_platform_name,11" json:"media_platform_name"`
	MediaIdentifying  string `thrift:"media_identifying,12" json:"media_identifying"`
}

func NewIncomeFinanceSettleQueryParam() *IncomeFinanceSettleQueryParam {
	return &IncomeFinanceSettleQueryParam{}
}

func (p *IncomeFinanceSettleQueryParam) IsSetMonth() bool {
	return p.Month != ""
}

func (p *IncomeFinanceSettleQueryParam) IsSetOrderId() bool {
	return p.OrderId != 0
}

func (p *IncomeFinanceSettleQueryParam) IsSetSdId() bool {
	return p.SdId != 0
}

func (p *IncomeFinanceSettleQueryParam) IsSetCompanyName() bool {
	return p.CompanyName != ""
}

func (p *IncomeFinanceSettleQueryParam) IsSetRemarkNum() bool {
	return p.RemarkNum != ""
}

func (p *IncomeFinanceSettleQueryParam) IsSetProductLine() bool {
	return p.ProductLine != 0
}

func (p *IncomeFinanceSettleQueryParam) IsSetOurCompany() bool {
	return p.OurCompany != 0
}

func (p *IncomeFinanceSettleQueryParam) IsSetDepartment() bool {
	return p.Department != 0
}

func (p *IncomeFinanceSettleQueryParam) IsSetSaleMan() bool {
	return p.SaleMan != ""
}

func (p *IncomeFinanceSettleQueryParam) IsSetSettleId() bool {
	return p.SettleId != 0
}

func (p *IncomeFinanceSettleQueryParam) IsSetMediaPlatformName() bool {
	return p.MediaPlatformName != ""
}

func (p *IncomeFinanceSettleQueryParam) IsSetMediaIdentifying() bool {
	return p.MediaIdentifying != ""
}

func (p *IncomeFinanceSettleQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Month = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CompanyName = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RemarkNum = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductLine = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.OurCompany = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Department = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SaleMan = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.SettleId = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.MediaPlatformName = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.MediaIdentifying = v
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IncomeFinanceSettleQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncomeFinanceSettleQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMonth() {
		if err := oprot.WriteFieldBegin("month", thrift.STRING, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:month: %s", p, err)
		}
		if err := oprot.WriteString(string(p.Month)); err != nil {
			return fmt.Errorf("%T.month (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:month: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderId() {
		if err := oprot.WriteFieldBegin("order_id", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:order_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
			return fmt.Errorf("%T.order_id (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:order_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSdId() {
		if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SdId)); err != nil {
			return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCompanyName() {
		if err := oprot.WriteFieldBegin("company_name", thrift.STRING, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:company_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.CompanyName)); err != nil {
			return fmt.Errorf("%T.company_name (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:company_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRemarkNum() {
		if err := oprot.WriteFieldBegin("remark_num", thrift.STRING, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:remark_num: %s", p, err)
		}
		if err := oprot.WriteString(string(p.RemarkNum)); err != nil {
			return fmt.Errorf("%T.remark_num (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:remark_num: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductLine() {
		if err := oprot.WriteFieldBegin("product_line", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:product_line: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProductLine)); err != nil {
			return fmt.Errorf("%T.product_line (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:product_line: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetOurCompany() {
		if err := oprot.WriteFieldBegin("our_company", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:our_company: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OurCompany)); err != nil {
			return fmt.Errorf("%T.our_company (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:our_company: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDepartment() {
		if err := oprot.WriteFieldBegin("department", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:department: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Department)); err != nil {
			return fmt.Errorf("%T.department (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:department: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSaleMan() {
		if err := oprot.WriteFieldBegin("sale_man", thrift.STRING, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:sale_man: %s", p, err)
		}
		if err := oprot.WriteString(string(p.SaleMan)); err != nil {
			return fmt.Errorf("%T.sale_man (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:sale_man: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSettleId() {
		if err := oprot.WriteFieldBegin("settle_id", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:settle_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SettleId)); err != nil {
			return fmt.Errorf("%T.settle_id (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:settle_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaPlatformName() {
		if err := oprot.WriteFieldBegin("media_platform_name", thrift.STRING, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:media_platform_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaPlatformName)); err != nil {
			return fmt.Errorf("%T.media_platform_name (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:media_platform_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaIdentifying() {
		if err := oprot.WriteFieldBegin("media_identifying", thrift.STRING, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:media_identifying: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaIdentifying)); err != nil {
			return fmt.Errorf("%T.media_identifying (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:media_identifying: %s", p, err)
		}
	}
	return err
}

func (p *IncomeFinanceSettleQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncomeFinanceSettleQueryParam(%+v)", *p)
}

type IncomePaybackQueryParam struct {
	Month         string `thrift:"month,1" json:"month"`
	OrderId       int32  `thrift:"order_id,2" json:"order_id"`
	SdId          int32  `thrift:"sd_id,3" json:"sd_id"`
	CompanyName   string `thrift:"company_name,4" json:"company_name"`
	RemarkNum     string `thrift:"remark_num,5" json:"remark_num"`
	ProductLine   int32  `thrift:"product_line,6" json:"product_line"`
	OurCompany    int32  `thrift:"our_company,7" json:"our_company"`
	Department    int32  `thrift:"department,8" json:"department"`
	SaleMan       string `thrift:"sale_man,9" json:"sale_man"`
	SettleId      int32  `thrift:"settle_id,10" json:"settle_id"`
	Currency      int32  `thrift:"currency,11" json:"currency"`
	CreateTimeBeg string `thrift:"create_time_beg,12" json:"create_time_beg"`
	CreateTimeEnd string `thrift:"create_time_end,13" json:"create_time_end"`
}

func NewIncomePaybackQueryParam() *IncomePaybackQueryParam {
	return &IncomePaybackQueryParam{}
}

func (p *IncomePaybackQueryParam) IsSetMonth() bool {
	return p.Month != ""
}

func (p *IncomePaybackQueryParam) IsSetOrderId() bool {
	return p.OrderId != 0
}

func (p *IncomePaybackQueryParam) IsSetSdId() bool {
	return p.SdId != 0
}

func (p *IncomePaybackQueryParam) IsSetCompanyName() bool {
	return p.CompanyName != ""
}

func (p *IncomePaybackQueryParam) IsSetRemarkNum() bool {
	return p.RemarkNum != ""
}

func (p *IncomePaybackQueryParam) IsSetProductLine() bool {
	return p.ProductLine != 0
}

func (p *IncomePaybackQueryParam) IsSetOurCompany() bool {
	return p.OurCompany != 0
}

func (p *IncomePaybackQueryParam) IsSetDepartment() bool {
	return p.Department != 0
}

func (p *IncomePaybackQueryParam) IsSetSaleMan() bool {
	return p.SaleMan != ""
}

func (p *IncomePaybackQueryParam) IsSetSettleId() bool {
	return p.SettleId != 0
}

func (p *IncomePaybackQueryParam) IsSetCurrency() bool {
	return p.Currency != 0
}

func (p *IncomePaybackQueryParam) IsSetCreateTimeBeg() bool {
	return p.CreateTimeBeg != ""
}

func (p *IncomePaybackQueryParam) IsSetCreateTimeEnd() bool {
	return p.CreateTimeEnd != ""
}

func (p *IncomePaybackQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Month = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CompanyName = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RemarkNum = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductLine = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.OurCompany = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Department = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SaleMan = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.SettleId = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Currency = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.CreateTimeBeg = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.CreateTimeEnd = v
	}
	return nil
}

func (p *IncomePaybackQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IncomePaybackQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncomePaybackQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMonth() {
		if err := oprot.WriteFieldBegin("month", thrift.STRING, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:month: %s", p, err)
		}
		if err := oprot.WriteString(string(p.Month)); err != nil {
			return fmt.Errorf("%T.month (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:month: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderId() {
		if err := oprot.WriteFieldBegin("order_id", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:order_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
			return fmt.Errorf("%T.order_id (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:order_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSdId() {
		if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sd_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SdId)); err != nil {
			return fmt.Errorf("%T.sd_id (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sd_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCompanyName() {
		if err := oprot.WriteFieldBegin("company_name", thrift.STRING, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:company_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.CompanyName)); err != nil {
			return fmt.Errorf("%T.company_name (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:company_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetRemarkNum() {
		if err := oprot.WriteFieldBegin("remark_num", thrift.STRING, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:remark_num: %s", p, err)
		}
		if err := oprot.WriteString(string(p.RemarkNum)); err != nil {
			return fmt.Errorf("%T.remark_num (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:remark_num: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductLine() {
		if err := oprot.WriteFieldBegin("product_line", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:product_line: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProductLine)); err != nil {
			return fmt.Errorf("%T.product_line (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:product_line: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetOurCompany() {
		if err := oprot.WriteFieldBegin("our_company", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:our_company: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OurCompany)); err != nil {
			return fmt.Errorf("%T.our_company (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:our_company: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDepartment() {
		if err := oprot.WriteFieldBegin("department", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:department: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Department)); err != nil {
			return fmt.Errorf("%T.department (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:department: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSaleMan() {
		if err := oprot.WriteFieldBegin("sale_man", thrift.STRING, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:sale_man: %s", p, err)
		}
		if err := oprot.WriteString(string(p.SaleMan)); err != nil {
			return fmt.Errorf("%T.sale_man (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:sale_man: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetSettleId() {
		if err := oprot.WriteFieldBegin("settle_id", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:settle_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SettleId)); err != nil {
			return fmt.Errorf("%T.settle_id (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:settle_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCurrency() {
		if err := oprot.WriteFieldBegin("currency", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:currency: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Currency)); err != nil {
			return fmt.Errorf("%T.currency (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:currency: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTimeBeg() {
		if err := oprot.WriteFieldBegin("create_time_beg", thrift.STRING, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:create_time_beg: %s", p, err)
		}
		if err := oprot.WriteString(string(p.CreateTimeBeg)); err != nil {
			return fmt.Errorf("%T.create_time_beg (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:create_time_beg: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateTimeEnd() {
		if err := oprot.WriteFieldBegin("create_time_end", thrift.STRING, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:create_time_end: %s", p, err)
		}
		if err := oprot.WriteString(string(p.CreateTimeEnd)); err != nil {
			return fmt.Errorf("%T.create_time_end (13) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:create_time_end: %s", p, err)
		}
	}
	return err
}

func (p *IncomePaybackQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncomePaybackQueryParam(%+v)", *p)
}

type IncomeNonAssociatedInvoiceQueryParam struct {
	InvoiceId  int32  `thrift:"invoice_id,1" json:"invoice_id"`
	OurCompany int32  `thrift:"our_company,2" json:"our_company"`
	InvTitle   string `thrift:"inv_title,3" json:"inv_title"`
	InvType    int32  `thrift:"inv_type,4" json:"inv_type"`
	InvContent string `thrift:"inv_content,5" json:"inv_content"`
	Applyer    string `thrift:"applyer,6" json:"applyer"`
	Source     int32  `thrift:"source,7" json:"source"`
}

func NewIncomeNonAssociatedInvoiceQueryParam() *IncomeNonAssociatedInvoiceQueryParam {
	return &IncomeNonAssociatedInvoiceQueryParam{}
}

func (p *IncomeNonAssociatedInvoiceQueryParam) IsSetInvoiceId() bool {
	return p.InvoiceId != 0
}

func (p *IncomeNonAssociatedInvoiceQueryParam) IsSetOurCompany() bool {
	return p.OurCompany != 0
}

func (p *IncomeNonAssociatedInvoiceQueryParam) IsSetInvTitle() bool {
	return p.InvTitle != ""
}

func (p *IncomeNonAssociatedInvoiceQueryParam) IsSetInvType() bool {
	return p.InvType != 0
}

func (p *IncomeNonAssociatedInvoiceQueryParam) IsSetInvContent() bool {
	return p.InvContent != ""
}

func (p *IncomeNonAssociatedInvoiceQueryParam) IsSetApplyer() bool {
	return p.Applyer != ""
}

func (p *IncomeNonAssociatedInvoiceQueryParam) IsSetSource() bool {
	return p.Source != 0
}

func (p *IncomeNonAssociatedInvoiceQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.InvoiceId = v
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OurCompany = v
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.InvTitle = v
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.InvType = v
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.InvContent = v
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Applyer = v
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IncomeNonAssociatedInvoiceQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncomeNonAssociatedInvoiceQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetInvoiceId() {
		if err := oprot.WriteFieldBegin("invoice_id", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:invoice_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.InvoiceId)); err != nil {
			return fmt.Errorf("%T.invoice_id (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:invoice_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeNonAssociatedInvoiceQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOurCompany() {
		if err := oprot.WriteFieldBegin("our_company", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:our_company: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OurCompany)); err != nil {
			return fmt.Errorf("%T.our_company (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:our_company: %s", p, err)
		}
	}
	return err
}

func (p *IncomeNonAssociatedInvoiceQueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetInvTitle() {
		if err := oprot.WriteFieldBegin("inv_title", thrift.STRING, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:inv_title: %s", p, err)
		}
		if err := oprot.WriteString(string(p.InvTitle)); err != nil {
			return fmt.Errorf("%T.inv_title (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:inv_title: %s", p, err)
		}
	}
	return err
}

func (p *IncomeNonAssociatedInvoiceQueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetInvType() {
		if err := oprot.WriteFieldBegin("inv_type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:inv_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.InvType)); err != nil {
			return fmt.Errorf("%T.inv_type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:inv_type: %s", p, err)
		}
	}
	return err
}

func (p *IncomeNonAssociatedInvoiceQueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInvContent() {
		if err := oprot.WriteFieldBegin("inv_content", thrift.STRING, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:inv_content: %s", p, err)
		}
		if err := oprot.WriteString(string(p.InvContent)); err != nil {
			return fmt.Errorf("%T.inv_content (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:inv_content: %s", p, err)
		}
	}
	return err
}

func (p *IncomeNonAssociatedInvoiceQueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetApplyer() {
		if err := oprot.WriteFieldBegin("applyer", thrift.STRING, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:applyer: %s", p, err)
		}
		if err := oprot.WriteString(string(p.Applyer)); err != nil {
			return fmt.Errorf("%T.applyer (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:applyer: %s", p, err)
		}
	}
	return err
}

func (p *IncomeNonAssociatedInvoiceQueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSource() {
		if err := oprot.WriteFieldBegin("source", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:source: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Source)); err != nil {
			return fmt.Errorf("%T.source (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:source: %s", p, err)
		}
	}
	return err
}

func (p *IncomeNonAssociatedInvoiceQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncomeNonAssociatedInvoiceQueryParam(%+v)", *p)
}

type IncomeMonthReportQueryParam struct {
	OrderId           int32  `thrift:"order_id,1" json:"order_id"`
	SdId              int32  `thrift:"sd_id,2" json:"sd_id"`
	Month             string `thrift:"month,3" json:"month"`
	UsMonth           string `thrift:"us_month,4" json:"us_month"`
	InvoiceHeader     string `thrift:"invoice_header,5" json:"invoice_header"`
	OurCompany        int32  `thrift:"our_company,6" json:"our_company"`
	SaleMan           string `thrift:"sale_man,7" json:"sale_man"`
	Department        int32  `thrift:"department,8" json:"department"`
	ProductLine       int32  `thrift:"product_line,9" json:"product_line"`
	MediaPlatformName string `thrift:"media_platform_name,10" json:"media_platform_name"`
	MediaIdentifying  string `thrift:"media_identifying,11" json:"media_identifying"`
}

func NewIncomeMonthReportQueryParam() *IncomeMonthReportQueryParam {
	return &IncomeMonthReportQueryParam{}
}

func (p *IncomeMonthReportQueryParam) IsSetOrderId() bool {
	return p.OrderId != 0
}

func (p *IncomeMonthReportQueryParam) IsSetSdId() bool {
	return p.SdId != 0
}

func (p *IncomeMonthReportQueryParam) IsSetMonth() bool {
	return p.Month != ""
}

func (p *IncomeMonthReportQueryParam) IsSetUsMonth() bool {
	return p.UsMonth != ""
}

func (p *IncomeMonthReportQueryParam) IsSetInvoiceHeader() bool {
	return p.InvoiceHeader != ""
}

func (p *IncomeMonthReportQueryParam) IsSetOurCompany() bool {
	return p.OurCompany != 0
}

func (p *IncomeMonthReportQueryParam) IsSetSaleMan() bool {
	return p.SaleMan != ""
}

func (p *IncomeMonthReportQueryParam) IsSetDepartment() bool {
	return p.Department != 0
}

func (p *IncomeMonthReportQueryParam) IsSetProductLine() bool {
	return p.ProductLine != 0
}

func (p *IncomeMonthReportQueryParam) IsSetMediaPlatformName() bool {
	return p.MediaPlatformName != ""
}

func (p *IncomeMonthReportQueryParam) IsSetMediaIdentifying() bool {
	return p.MediaIdentifying != ""
}

func (p *IncomeMonthReportQueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OrderId = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SdId = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Month = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UsMonth = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.InvoiceHeader = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OurCompany = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.SaleMan = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Department = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ProductLine = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.MediaPlatformName = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.MediaIdentifying = v
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IncomeMonthReportQueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IncomeMonthReportQueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderId() {
		if err := oprot.WriteFieldBegin("order_id", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:order_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderId)); err != nil {
			return fmt.Errorf("%T.order_id (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:order_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSdId() {
		if err := oprot.WriteFieldBegin("sd_id", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:sd_id: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SdId)); err != nil {
			return fmt.Errorf("%T.sd_id (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:sd_id: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetMonth() {
		if err := oprot.WriteFieldBegin("month", thrift.STRING, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:month: %s", p, err)
		}
		if err := oprot.WriteString(string(p.Month)); err != nil {
			return fmt.Errorf("%T.month (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:month: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetUsMonth() {
		if err := oprot.WriteFieldBegin("us_month", thrift.STRING, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:us_month: %s", p, err)
		}
		if err := oprot.WriteString(string(p.UsMonth)); err != nil {
			return fmt.Errorf("%T.us_month (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:us_month: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetInvoiceHeader() {
		if err := oprot.WriteFieldBegin("invoice_header", thrift.STRING, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:invoice_header: %s", p, err)
		}
		if err := oprot.WriteString(string(p.InvoiceHeader)); err != nil {
			return fmt.Errorf("%T.invoice_header (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:invoice_header: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetOurCompany() {
		if err := oprot.WriteFieldBegin("our_company", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:our_company: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OurCompany)); err != nil {
			return fmt.Errorf("%T.our_company (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:our_company: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetSaleMan() {
		if err := oprot.WriteFieldBegin("sale_man", thrift.STRING, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:sale_man: %s", p, err)
		}
		if err := oprot.WriteString(string(p.SaleMan)); err != nil {
			return fmt.Errorf("%T.sale_man (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:sale_man: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDepartment() {
		if err := oprot.WriteFieldBegin("department", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:department: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Department)); err != nil {
			return fmt.Errorf("%T.department (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:department: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductLine() {
		if err := oprot.WriteFieldBegin("product_line", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:product_line: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProductLine)); err != nil {
			return fmt.Errorf("%T.product_line (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:product_line: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaPlatformName() {
		if err := oprot.WriteFieldBegin("media_platform_name", thrift.STRING, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:media_platform_name: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaPlatformName)); err != nil {
			return fmt.Errorf("%T.media_platform_name (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:media_platform_name: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetMediaIdentifying() {
		if err := oprot.WriteFieldBegin("media_identifying", thrift.STRING, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:media_identifying: %s", p, err)
		}
		if err := oprot.WriteString(string(p.MediaIdentifying)); err != nil {
			return fmt.Errorf("%T.media_identifying (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:media_identifying: %s", p, err)
		}
	}
	return err
}

func (p *IncomeMonthReportQueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IncomeMonthReportQueryParam(%+v)", *p)
}
