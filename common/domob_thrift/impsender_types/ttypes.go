// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package impsender_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//点击数据来源渠道
type AdChannel int64

const (
	AdChannel_AC_ADN AdChannel = 1
	AdChannel_AC_RTB AdChannel = 2
	AdChannel_AC_DOS AdChannel = 3
)

func (p AdChannel) String() string {
	switch p {
	case AdChannel_AC_ADN:
		return "AdChannel_AC_ADN"
	case AdChannel_AC_RTB:
		return "AdChannel_AC_RTB"
	case AdChannel_AC_DOS:
		return "AdChannel_AC_DOS"
	}
	return "<UNSET>"
}

func AdChannelFromString(s string) (AdChannel, error) {
	switch s {
	case "AdChannel_AC_ADN":
		return AdChannel_AC_ADN, nil
	case "AdChannel_AC_RTB":
		return AdChannel_AC_RTB, nil
	case "AdChannel_AC_DOS":
		return AdChannel_AC_DOS, nil
	}
	return AdChannel(math.MinInt32 - 1), fmt.Errorf("not a valid AdChannel string")
}

type RegionCode common.RegionCode

type SDKPlatform common.SDKPlatform

type Imp struct {
	Channel      AdChannel `thrift:"channel,1" json:"channel"`
	ImpSenderUrl string    `thrift:"impSenderUrl,2" json:"impSenderUrl"`
	AppId        int32     `thrift:"appId,3" json:"appId"`
	ChnId        int32     `thrift:"chnId,4" json:"chnId"`
	CfgId        int32     `thrift:"cfgId,5" json:"cfgId"`
	ExchangeId   int32     `thrift:"exchangeId,6" json:"exchangeId"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	SearchId   int64 `thrift:"searchId,10" json:"searchId"`
	ImpTime    int64 `thrift:"impTime,11" json:"impTime"`
	SponsorId  int32 `thrift:"sponsorId,12" json:"sponsorId"`
	PlanId     int32 `thrift:"planId,13" json:"planId"`
	StrategyId int32 `thrift:"strategyId,14" json:"strategyId"`
	CreativeId int32 `thrift:"creativeId,15" json:"creativeId"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	UserAgent       string      `thrift:"userAgent,20" json:"userAgent"`
	ClientIp        string      `thrift:"clientIp,21" json:"clientIp"`
	Imei            string      `thrift:"imei,22" json:"imei"`
	Imsi            int64       `thrift:"imsi,23" json:"imsi"`
	Udid            string      `thrift:"udid,24" json:"udid"`
	Idfa            string      `thrift:"idfa,25" json:"idfa"`
	Pkg             string      `thrift:"pkg,26" json:"pkg"`
	SdkPlatform     SDKPlatform `thrift:"sdk_platform,27" json:"sdk_platform"`
	Dmac            string      `thrift:"dmac,28" json:"dmac"`
	Oid             string      `thrift:"oid,29" json:"oid"`
	DeviceJailBreak int32       `thrift:"deviceJailBreak,30" json:"deviceJailBreak"`
	Anid            string      `thrift:"anid,31" json:"anid"`
	Anid2           string      `thrift:"anid2,32" json:"anid2"`
	DeviceIdMD5     string      `thrift:"deviceIdMD5,33" json:"deviceIdMD5"`
	ReqId           string      `thrift:"reqId,34" json:"reqId"`
	Idfamd5         string      `thrift:"idfamd5,35" json:"idfamd5"`
	SendRound       int32       `thrift:"sendRound,36" json:"sendRound"`
	LastSendTime    int64       `thrift:"lastSendTime,37" json:"lastSendTime"`
	DmMediaId       int32       `thrift:"dmMediaId,38" json:"dmMediaId"`
	AdMatchType     int64       `thrift:"adMatchType,39" json:"adMatchType"`
	ExtInfo         string      `thrift:"extInfo,40" json:"extInfo"`
	ImeiMD5         string      `thrift:"imeiMD5,41" json:"imeiMD5"`
	AdpDim          string      `thrift:"adpDim,42" json:"adpDim"`
}

func NewImp() *Imp {
	return &Imp{
		Channel: math.MinInt32 - 1, // unset sentinal value

		SdkPlatform: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Imp) IsSetChannel() bool {
	return int64(p.Channel) != math.MinInt32-1
}

func (p *Imp) IsSetSdkPlatform() bool {
	return int64(p.SdkPlatform) != math.MinInt32-1
}

func (p *Imp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.STRING {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I64 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I64 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Imp) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Channel = AdChannel(v)
	}
	return nil
}

func (p *Imp) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ImpSenderUrl = v
	}
	return nil
}

func (p *Imp) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *Imp) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *Imp) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CfgId = v
	}
	return nil
}

func (p *Imp) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *Imp) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *Imp) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ImpTime = v
	}
	return nil
}

func (p *Imp) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *Imp) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *Imp) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *Imp) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *Imp) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.UserAgent = v
	}
	return nil
}

func (p *Imp) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *Imp) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *Imp) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Imsi = v
	}
	return nil
}

func (p *Imp) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Udid = v
	}
	return nil
}

func (p *Imp) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *Imp) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Pkg = v
	}
	return nil
}

func (p *Imp) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.SdkPlatform = SDKPlatform(v)
	}
	return nil
}

func (p *Imp) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *Imp) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *Imp) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.DeviceJailBreak = v
	}
	return nil
}

func (p *Imp) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Anid = v
	}
	return nil
}

func (p *Imp) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Anid2 = v
	}
	return nil
}

func (p *Imp) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.DeviceIdMD5 = v
	}
	return nil
}

func (p *Imp) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.ReqId = v
	}
	return nil
}

func (p *Imp) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Idfamd5 = v
	}
	return nil
}

func (p *Imp) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.SendRound = v
	}
	return nil
}

func (p *Imp) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.LastSendTime = v
	}
	return nil
}

func (p *Imp) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.DmMediaId = v
	}
	return nil
}

func (p *Imp) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *Imp) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *Imp) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.ImeiMD5 = v
	}
	return nil
}

func (p *Imp) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.AdpDim = v
	}
	return nil
}

func (p *Imp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Imp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Imp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetChannel() {
		if err := oprot.WriteFieldBegin("channel", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:channel: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Channel)); err != nil {
			return fmt.Errorf("%T.channel (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:channel: %s", p, err)
		}
	}
	return err
}

func (p *Imp) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impSenderUrl", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:impSenderUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpSenderUrl)); err != nil {
		return fmt.Errorf("%T.impSenderUrl (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:impSenderUrl: %s", p, err)
	}
	return err
}

func (p *Imp) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:chnId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cfgId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:cfgId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CfgId)); err != nil {
		return fmt.Errorf("%T.cfgId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:cfgId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:exchangeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:exchangeId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:searchId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impTime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:impTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpTime)); err != nil {
		return fmt.Errorf("%T.impTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:impTime: %s", p, err)
	}
	return err
}

func (p *Imp) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sponsorId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planId", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:planId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.planId (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:planId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategyId", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:strategyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategyId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:strategyId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creativeId", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:creativeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creativeId (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:creativeId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userAgent", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:userAgent: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserAgent)); err != nil {
		return fmt.Errorf("%T.userAgent (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:userAgent: %s", p, err)
	}
	return err
}

func (p *Imp) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clientIp", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:clientIp: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.clientIp (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:clientIp: %s", p, err)
	}
	return err
}

func (p *Imp) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:imei: %s", p, err)
	}
	return err
}

func (p *Imp) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:imsi: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:imsi: %s", p, err)
	}
	return err
}

func (p *Imp) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("udid", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:udid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Udid)); err != nil {
		return fmt.Errorf("%T.udid (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:udid: %s", p, err)
	}
	return err
}

func (p *Imp) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:idfa: %s", p, err)
	}
	return err
}

func (p *Imp) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:pkg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pkg)); err != nil {
		return fmt.Errorf("%T.pkg (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:pkg: %s", p, err)
	}
	return err
}

func (p *Imp) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_platform", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:sdk_platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkPlatform)); err != nil {
		return fmt.Errorf("%T.sdk_platform (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:sdk_platform: %s", p, err)
	}
	return err
}

func (p *Imp) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:dmac: %s", p, err)
	}
	return err
}

func (p *Imp) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:oid: %s", p, err)
	}
	return err
}

func (p *Imp) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceJailBreak", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:deviceJailBreak: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceJailBreak)); err != nil {
		return fmt.Errorf("%T.deviceJailBreak (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:deviceJailBreak: %s", p, err)
	}
	return err
}

func (p *Imp) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anid", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:anid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Anid)); err != nil {
		return fmt.Errorf("%T.anid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:anid: %s", p, err)
	}
	return err
}

func (p *Imp) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anid2", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:anid2: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Anid2)); err != nil {
		return fmt.Errorf("%T.anid2 (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:anid2: %s", p, err)
	}
	return err
}

func (p *Imp) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceIdMD5", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:deviceIdMD5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceIdMD5)); err != nil {
		return fmt.Errorf("%T.deviceIdMD5 (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:deviceIdMD5: %s", p, err)
	}
	return err
}

func (p *Imp) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reqId", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:reqId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReqId)); err != nil {
		return fmt.Errorf("%T.reqId (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:reqId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfamd5", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:idfamd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfamd5)); err != nil {
		return fmt.Errorf("%T.idfamd5 (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:idfamd5: %s", p, err)
	}
	return err
}

func (p *Imp) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sendRound", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:sendRound: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SendRound)); err != nil {
		return fmt.Errorf("%T.sendRound (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:sendRound: %s", p, err)
	}
	return err
}

func (p *Imp) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastSendTime", thrift.I64, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:lastSendTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastSendTime)); err != nil {
		return fmt.Errorf("%T.lastSendTime (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:lastSendTime: %s", p, err)
	}
	return err
}

func (p *Imp) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmMediaId", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:dmMediaId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DmMediaId)); err != nil {
		return fmt.Errorf("%T.dmMediaId (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:dmMediaId: %s", p, err)
	}
	return err
}

func (p *Imp) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adMatchType", thrift.I64, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:adMatchType: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.adMatchType (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:adMatchType: %s", p, err)
	}
	return err
}

func (p *Imp) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extInfo", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:extInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.extInfo (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:extInfo: %s", p, err)
	}
	return err
}

func (p *Imp) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imeiMD5", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:imeiMD5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiMD5)); err != nil {
		return fmt.Errorf("%T.imeiMD5 (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:imeiMD5: %s", p, err)
	}
	return err
}

func (p *Imp) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adpDim", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:adpDim: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdpDim)); err != nil {
		return fmt.Errorf("%T.adpDim (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:adpDim: %s", p, err)
	}
	return err
}

func (p *Imp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Imp(%+v)", *p)
}
