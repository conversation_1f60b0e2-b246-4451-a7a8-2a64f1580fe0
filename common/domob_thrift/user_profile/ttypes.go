// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package user_profile

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type IpAccuracy int64

const (
	IpAccuracy_NOT_VERIFY           IpAccuracy = 1
	IpAccuracy_ACCURACY_ON_CITY     IpAccuracy = 2
	IpAccuracy_NOT_ACCURACY_ON_CITY IpAccuracy = 3
	IpAccuracy_NOT_RECOG            IpAccuracy = 4
)

func (p IpAccuracy) String() string {
	switch p {
	case IpAccuracy_NOT_VERIFY:
		return "IpAccuracy_NOT_VERIFY"
	case IpAccuracy_ACCURACY_ON_CITY:
		return "IpAccuracy_ACCURACY_ON_CITY"
	case IpAccuracy_NOT_ACCURACY_ON_CITY:
		return "IpAccuracy_NOT_ACCURACY_ON_CITY"
	case IpAccuracy_NOT_RECOG:
		return "IpAccuracy_NOT_RECOG"
	}
	return "<UNSET>"
}

func IpAccuracyFromString(s string) (IpAccuracy, error) {
	switch s {
	case "IpAccuracy_NOT_VERIFY":
		return IpAccuracy_NOT_VERIFY, nil
	case "IpAccuracy_ACCURACY_ON_CITY":
		return IpAccuracy_ACCURACY_ON_CITY, nil
	case "IpAccuracy_NOT_ACCURACY_ON_CITY":
		return IpAccuracy_NOT_ACCURACY_ON_CITY, nil
	case "IpAccuracy_NOT_RECOG":
		return IpAccuracy_NOT_RECOG, nil
	}
	return IpAccuracy(math.MinInt32 - 1), fmt.Errorf("not a valid IpAccuracy string")
}

type GeoType int64

const (
	GeoType_POI_TYPE     GeoType = 1
	GeoType_AMAC_TYPE    GeoType = 2
	GeoType_IP_TYPE      GeoType = 3
	GeoType_BS_TYPE      GeoType = 4
	GeoType_QUERY_TYPE   GeoType = 5
	GeoType_THIRD_TYPE   GeoType = 6
	GeoType_USER_GPS     GeoType = 7
	GeoType_USER_WIFI_IP GeoType = 8
)

func (p GeoType) String() string {
	switch p {
	case GeoType_POI_TYPE:
		return "GeoType_POI_TYPE"
	case GeoType_AMAC_TYPE:
		return "GeoType_AMAC_TYPE"
	case GeoType_IP_TYPE:
		return "GeoType_IP_TYPE"
	case GeoType_BS_TYPE:
		return "GeoType_BS_TYPE"
	case GeoType_QUERY_TYPE:
		return "GeoType_QUERY_TYPE"
	case GeoType_THIRD_TYPE:
		return "GeoType_THIRD_TYPE"
	case GeoType_USER_GPS:
		return "GeoType_USER_GPS"
	case GeoType_USER_WIFI_IP:
		return "GeoType_USER_WIFI_IP"
	}
	return "<UNSET>"
}

func GeoTypeFromString(s string) (GeoType, error) {
	switch s {
	case "GeoType_POI_TYPE":
		return GeoType_POI_TYPE, nil
	case "GeoType_AMAC_TYPE":
		return GeoType_AMAC_TYPE, nil
	case "GeoType_IP_TYPE":
		return GeoType_IP_TYPE, nil
	case "GeoType_BS_TYPE":
		return GeoType_BS_TYPE, nil
	case "GeoType_QUERY_TYPE":
		return GeoType_QUERY_TYPE, nil
	case "GeoType_THIRD_TYPE":
		return GeoType_THIRD_TYPE, nil
	case "GeoType_USER_GPS":
		return GeoType_USER_GPS, nil
	case "GeoType_USER_WIFI_IP":
		return GeoType_USER_WIFI_IP, nil
	}
	return GeoType(math.MinInt32 - 1), fmt.Errorf("not a valid GeoType string")
}

type GpsInfo struct {
	Longitude   float64 `thrift:"longitude,1" json:"longitude"`
	Latitude    float64 `thrift:"latitude,2" json:"latitude"`
	City        int32   `thrift:"city,3" json:"city"`
	Country     int32   `thrift:"country,4" json:"country"`
	CountryCode string  `thrift:"country_code,5" json:"country_code"`
}

func NewGpsInfo() *GpsInfo {
	return &GpsInfo{}
}

func (p *GpsInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GpsInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *GpsInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *GpsInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.City = v
	}
	return nil
}

func (p *GpsInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Country = v
	}
	return nil
}

func (p *GpsInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CountryCode = v
	}
	return nil
}

func (p *GpsInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GpsInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GpsInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:longitude: %s", p, err)
	}
	return err
}

func (p *GpsInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:latitude: %s", p, err)
	}
	return err
}

func (p *GpsInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:city: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.City)); err != nil {
		return fmt.Errorf("%T.city (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:city: %s", p, err)
	}
	return err
}

func (p *GpsInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("country", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:country: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Country)); err != nil {
		return fmt.Errorf("%T.country (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:country: %s", p, err)
	}
	return err
}

func (p *GpsInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("country_code", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:country_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CountryCode)); err != nil {
		return fmt.Errorf("%T.country_code (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:country_code: %s", p, err)
	}
	return err
}

func (p *GpsInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GpsInfo(%+v)", *p)
}

type DeviceInfo struct {
	ModelName  string `thrift:"model_name,1" json:"model_name"`
	DeviceName string `thrift:"device_name,2" json:"device_name"`
	// unused field # 3
	// unused field # 4
	OsName    string `thrift:"os_name,5" json:"os_name"`
	OsVersion string `thrift:"os_version,6" json:"os_version"`
	Idfa      string `thrift:"idfa,7" json:"idfa"`
	Imei      string `thrift:"imei,8" json:"imei"`
	IdfaMd5   string `thrift:"idfa_md5,9" json:"idfa_md5"`
	ImeiMd5   string `thrift:"imei_md5,10" json:"imei_md5"`
}

func NewDeviceInfo() *DeviceInfo {
	return &DeviceInfo{}
}

func (p *DeviceInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeviceInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ModelName = v
	}
	return nil
}

func (p *DeviceInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeviceName = v
	}
	return nil
}

func (p *DeviceInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.OsName = v
	}
	return nil
}

func (p *DeviceInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OsVersion = v
	}
	return nil
}

func (p *DeviceInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *DeviceInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *DeviceInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.IdfaMd5 = v
	}
	return nil
}

func (p *DeviceInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ImeiMd5 = v
	}
	return nil
}

func (p *DeviceInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeviceInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeviceInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("model_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:model_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ModelName)); err != nil {
		return fmt.Errorf("%T.model_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:model_name: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:device_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceName)); err != nil {
		return fmt.Errorf("%T.device_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:device_name: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:os_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OsName)); err != nil {
		return fmt.Errorf("%T.os_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:os_name: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_version", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:os_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OsVersion)); err != nil {
		return fmt.Errorf("%T.os_version (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:os_version: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:idfa: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:imei: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa_md5", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:idfa_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IdfaMd5)); err != nil {
		return fmt.Errorf("%T.idfa_md5 (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:idfa_md5: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei_md5", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:imei_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiMd5)); err != nil {
		return fmt.Errorf("%T.imei_md5 (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:imei_md5: %s", p, err)
	}
	return err
}

func (p *DeviceInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeviceInfo(%+v)", *p)
}

type NetworkInfo struct {
	IpAddr    string   `thrift:"ip_addr,1" json:"ip_addr"`
	DeviceMac string   `thrift:"device_mac,2" json:"device_mac"`
	AccessMac string   `thrift:"access_mac,3" json:"access_mac"`
	ScanMac   []string `thrift:"scan_mac,4" json:"scan_mac"`
	ApName    string   `thrift:"ap_name,5" json:"ap_name"`
	Apn       string   `thrift:"apn,6" json:"apn"`
	Carrier   string   `thrift:"carrier,7" json:"carrier"`
	Network   string   `thrift:"network,8" json:"network"`
	AreaCode  string   `thrift:"area_code,9" json:"area_code"`
	CellId    string   `thrift:"cell_id,10" json:"cell_id"`
}

func NewNetworkInfo() *NetworkInfo {
	return &NetworkInfo{}
}

func (p *NetworkInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NetworkInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.IpAddr = v
	}
	return nil
}

func (p *NetworkInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeviceMac = v
	}
	return nil
}

func (p *NetworkInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccessMac = v
	}
	return nil
}

func (p *NetworkInfo) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ScanMac = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.ScanMac = append(p.ScanMac, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *NetworkInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ApName = v
	}
	return nil
}

func (p *NetworkInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Apn = v
	}
	return nil
}

func (p *NetworkInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *NetworkInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *NetworkInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AreaCode = v
	}
	return nil
}

func (p *NetworkInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CellId = v
	}
	return nil
}

func (p *NetworkInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("NetworkInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NetworkInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip_addr", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ip_addr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IpAddr)); err != nil {
		return fmt.Errorf("%T.ip_addr (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ip_addr: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_mac", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:device_mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceMac)); err != nil {
		return fmt.Errorf("%T.device_mac (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:device_mac: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_mac", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:access_mac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessMac)); err != nil {
		return fmt.Errorf("%T.access_mac (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:access_mac: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ScanMac != nil {
		if err := oprot.WriteFieldBegin("scan_mac", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:scan_mac: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ScanMac)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ScanMac {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:scan_mac: %s", p, err)
		}
	}
	return err
}

func (p *NetworkInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ap_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ap_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ApName)); err != nil {
		return fmt.Errorf("%T.ap_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ap_name: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("apn", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:apn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Apn)); err != nil {
		return fmt.Errorf("%T.apn (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:apn: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:carrier: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:network: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("area_code", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:area_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AreaCode)); err != nil {
		return fmt.Errorf("%T.area_code (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:area_code: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cell_id", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:cell_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CellId)); err != nil {
		return fmt.Errorf("%T.cell_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:cell_id: %s", p, err)
	}
	return err
}

func (p *NetworkInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NetworkInfo(%+v)", *p)
}

type UserAgentAndHttpInfo struct {
	// unused field # 1
	HttpUa     string            `thrift:"http_ua,2" json:"http_ua"`
	HttpHeader map[string]string `thrift:"http_header,3" json:"http_header"`
	RequestUrl string            `thrift:"request_url,4" json:"request_url"`
	SdkUa      string            `thrift:"sdk_ua,5" json:"sdk_ua"`
	Extention  map[string]string `thrift:"extention,6" json:"extention"`
}

func NewUserAgentAndHttpInfo() *UserAgentAndHttpInfo {
	return &UserAgentAndHttpInfo{}
}

func (p *UserAgentAndHttpInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.MAP {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserAgentAndHttpInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.HttpUa = v
	}
	return nil
}

func (p *UserAgentAndHttpInfo) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.HttpHeader = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = v
		}
		var _val2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = v
		}
		p.HttpHeader[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UserAgentAndHttpInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RequestUrl = v
	}
	return nil
}

func (p *UserAgentAndHttpInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SdkUa = v
	}
	return nil
}

func (p *UserAgentAndHttpInfo) readField6(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Extention = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key3 = v
		}
		var _val4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val4 = v
		}
		p.Extention[_key3] = _val4
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UserAgentAndHttpInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserAgentAndHttpInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserAgentAndHttpInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("http_ua", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:http_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HttpUa)); err != nil {
		return fmt.Errorf("%T.http_ua (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:http_ua: %s", p, err)
	}
	return err
}

func (p *UserAgentAndHttpInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.HttpHeader != nil {
		if err := oprot.WriteFieldBegin("http_header", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:http_header: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.HttpHeader)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.HttpHeader {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:http_header: %s", p, err)
		}
	}
	return err
}

func (p *UserAgentAndHttpInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("request_url", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:request_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RequestUrl)); err != nil {
		return fmt.Errorf("%T.request_url (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:request_url: %s", p, err)
	}
	return err
}

func (p *UserAgentAndHttpInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_ua", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sdk_ua: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SdkUa)); err != nil {
		return fmt.Errorf("%T.sdk_ua (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sdk_ua: %s", p, err)
	}
	return err
}

func (p *UserAgentAndHttpInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Extention != nil {
		if err := oprot.WriteFieldBegin("extention", thrift.MAP, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:extention: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Extention)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Extention {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:extention: %s", p, err)
		}
	}
	return err
}

func (p *UserAgentAndHttpInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserAgentAndHttpInfo(%+v)", *p)
}

type AdBusinessInfo struct {
	ScreenDim int32   `thrift:"screen_dim,1" json:"screen_dim"`
	Density   float64 `thrift:"density,2" json:"density"`
	MediaId   int32   `thrift:"media_id,3" json:"media_id"`
}

func NewAdBusinessInfo() *AdBusinessInfo {
	return &AdBusinessInfo{}
}

func (p *AdBusinessInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdBusinessInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ScreenDim = v
	}
	return nil
}

func (p *AdBusinessInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Density = v
	}
	return nil
}

func (p *AdBusinessInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaId = v
	}
	return nil
}

func (p *AdBusinessInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdBusinessInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdBusinessInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screen_dim", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:screen_dim: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ScreenDim)); err != nil {
		return fmt.Errorf("%T.screen_dim (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:screen_dim: %s", p, err)
	}
	return err
}

func (p *AdBusinessInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("density", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:density: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Density)); err != nil {
		return fmt.Errorf("%T.density (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:density: %s", p, err)
	}
	return err
}

func (p *AdBusinessInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:media_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.media_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:media_id: %s", p, err)
	}
	return err
}

func (p *AdBusinessInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdBusinessInfo(%+v)", *p)
}

type UserIdInfo struct {
	ExchangeUserId string `thrift:"exchange_user_id,1" json:"exchange_user_id"`
	IdfaMd5        string `thrift:"idfa_md5,2" json:"idfa_md5"`
	ImeiMd5        string `thrift:"imei_md5,3" json:"imei_md5"`
}

func NewUserIdInfo() *UserIdInfo {
	return &UserIdInfo{}
}

func (p *UserIdInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserIdInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ExchangeUserId = v
	}
	return nil
}

func (p *UserIdInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IdfaMd5 = v
	}
	return nil
}

func (p *UserIdInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ImeiMd5 = v
	}
	return nil
}

func (p *UserIdInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserIdInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserIdInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_user_id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:exchange_user_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExchangeUserId)); err != nil {
		return fmt.Errorf("%T.exchange_user_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:exchange_user_id: %s", p, err)
	}
	return err
}

func (p *UserIdInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa_md5", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:idfa_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IdfaMd5)); err != nil {
		return fmt.Errorf("%T.idfa_md5 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:idfa_md5: %s", p, err)
	}
	return err
}

func (p *UserIdInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei_md5", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:imei_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiMd5)); err != nil {
		return fmt.Errorf("%T.imei_md5 (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:imei_md5: %s", p, err)
	}
	return err
}

func (p *UserIdInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserIdInfo(%+v)", *p)
}

type OaidInfo struct {
	Oaid    string `thrift:"oaid,1" json:"oaid"`
	ImeiMd5 string `thrift:"imei_md5,2" json:"imei_md5"`
	OaidMd5 string `thrift:"oaid_md5,3" json:"oaid_md5"`
}

func NewOaidInfo() *OaidInfo {
	return &OaidInfo{}
}

func (p *OaidInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OaidInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Oaid = v
	}
	return nil
}

func (p *OaidInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ImeiMd5 = v
	}
	return nil
}

func (p *OaidInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OaidMd5 = v
	}
	return nil
}

func (p *OaidInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OaidInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OaidInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaid", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:oaid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oaid)); err != nil {
		return fmt.Errorf("%T.oaid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:oaid: %s", p, err)
	}
	return err
}

func (p *OaidInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei_md5", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:imei_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiMd5)); err != nil {
		return fmt.Errorf("%T.imei_md5 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:imei_md5: %s", p, err)
	}
	return err
}

func (p *OaidInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oaid_md5", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:oaid_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OaidMd5)); err != nil {
		return fmt.Errorf("%T.oaid_md5 (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:oaid_md5: %s", p, err)
	}
	return err
}

func (p *OaidInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OaidInfo(%+v)", *p)
}

type UserRawInfo struct {
	Gps        *GpsInfo              `thrift:"gps,1" json:"gps"`
	Device     *DeviceInfo           `thrift:"device,2" json:"device"`
	UserAgent  *UserAgentAndHttpInfo `thrift:"user_agent,3" json:"user_agent"`
	Network    *NetworkInfo          `thrift:"network,4" json:"network"`
	AdBusiness *AdBusinessInfo       `thrift:"ad_business,5" json:"ad_business"`
	VisitorId  string                `thrift:"visitor_id,6" json:"visitor_id"`
	SearchId   int64                 `thrift:"search_id,7" json:"search_id"`
	UserIdInfo *UserIdInfo           `thrift:"user_id_info,8" json:"user_id_info"`
	OaidInfo   *OaidInfo             `thrift:"oaid_info,9" json:"oaid_info"`
}

func NewUserRawInfo() *UserRawInfo {
	return &UserRawInfo{}
}

func (p *UserRawInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserRawInfo) readField1(iprot thrift.TProtocol) error {
	p.Gps = NewGpsInfo()
	if err := p.Gps.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Gps)
	}
	return nil
}

func (p *UserRawInfo) readField2(iprot thrift.TProtocol) error {
	p.Device = NewDeviceInfo()
	if err := p.Device.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Device)
	}
	return nil
}

func (p *UserRawInfo) readField3(iprot thrift.TProtocol) error {
	p.UserAgent = NewUserAgentAndHttpInfo()
	if err := p.UserAgent.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserAgent)
	}
	return nil
}

func (p *UserRawInfo) readField4(iprot thrift.TProtocol) error {
	p.Network = NewNetworkInfo()
	if err := p.Network.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Network)
	}
	return nil
}

func (p *UserRawInfo) readField5(iprot thrift.TProtocol) error {
	p.AdBusiness = NewAdBusinessInfo()
	if err := p.AdBusiness.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdBusiness)
	}
	return nil
}

func (p *UserRawInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.VisitorId = v
	}
	return nil
}

func (p *UserRawInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *UserRawInfo) readField8(iprot thrift.TProtocol) error {
	p.UserIdInfo = NewUserIdInfo()
	if err := p.UserIdInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserIdInfo)
	}
	return nil
}

func (p *UserRawInfo) readField9(iprot thrift.TProtocol) error {
	p.OaidInfo = NewOaidInfo()
	if err := p.OaidInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.OaidInfo)
	}
	return nil
}

func (p *UserRawInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserRawInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserRawInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Gps != nil {
		if err := oprot.WriteFieldBegin("gps", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:gps: %s", p, err)
		}
		if err := p.Gps.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Gps)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:gps: %s", p, err)
		}
	}
	return err
}

func (p *UserRawInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Device != nil {
		if err := oprot.WriteFieldBegin("device", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:device: %s", p, err)
		}
		if err := p.Device.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Device)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:device: %s", p, err)
		}
	}
	return err
}

func (p *UserRawInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.UserAgent != nil {
		if err := oprot.WriteFieldBegin("user_agent", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:user_agent: %s", p, err)
		}
		if err := p.UserAgent.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserAgent)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:user_agent: %s", p, err)
		}
	}
	return err
}

func (p *UserRawInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Network != nil {
		if err := oprot.WriteFieldBegin("network", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:network: %s", p, err)
		}
		if err := p.Network.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Network)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:network: %s", p, err)
		}
	}
	return err
}

func (p *UserRawInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if p.AdBusiness != nil {
		if err := oprot.WriteFieldBegin("ad_business", thrift.STRUCT, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:ad_business: %s", p, err)
		}
		if err := p.AdBusiness.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdBusiness)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:ad_business: %s", p, err)
		}
	}
	return err
}

func (p *UserRawInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("visitor_id", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:visitor_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.VisitorId)); err != nil {
		return fmt.Errorf("%T.visitor_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:visitor_id: %s", p, err)
	}
	return err
}

func (p *UserRawInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:search_id: %s", p, err)
	}
	return err
}

func (p *UserRawInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if p.UserIdInfo != nil {
		if err := oprot.WriteFieldBegin("user_id_info", thrift.STRUCT, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:user_id_info: %s", p, err)
		}
		if err := p.UserIdInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserIdInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:user_id_info: %s", p, err)
		}
	}
	return err
}

func (p *UserRawInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if p.OaidInfo != nil {
		if err := oprot.WriteFieldBegin("oaid_info", thrift.STRUCT, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:oaid_info: %s", p, err)
		}
		if err := p.OaidInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.OaidInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:oaid_info: %s", p, err)
		}
	}
	return err
}

func (p *UserRawInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserRawInfo(%+v)", *p)
}

type UPException struct {
	Code    int32  `thrift:"code,1" json:"code"`
	Message string `thrift:"message,2" json:"message"`
}

func NewUPException() *UPException {
	return &UPException{}
}

func (p *UPException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UPException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *UPException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *UPException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UPException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UPException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Code)); err != nil {
		return fmt.Errorf("%T.code (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:code: %s", p, err)
	}
	return err
}

func (p *UPException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *UPException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UPException(%+v)", *p)
}

type GeoResult struct {
	ProvinceCode     enums.RegionCode `thrift:"province_code,1" json:"province_code"`
	CityCode         int32            `thrift:"city_code,2" json:"city_code"`
	RegionSourceType int32            `thrift:"region_source_type,3" json:"region_source_type"`
	// unused field # 4
	CellId                 int32            `thrift:"cell_id,5" json:"cell_id"`
	LocationAreaCode       int32            `thrift:"location_area_code,6" json:"location_area_code"`
	Longitude              float64          `thrift:"longitude,7" json:"longitude"`
	Latitude               float64          `thrift:"latitude,8" json:"latitude"`
	ThirdpartyProvinceCode enums.RegionCode `thrift:"thirdparty_province_code,9" json:"thirdparty_province_code"`
	ThirdpartyCityCode     int32            `thrift:"thirdparty_city_code,10" json:"thirdparty_city_code"`
	GpsSource              int32            `thrift:"gps_source,11" json:"gps_source"`
	Ipac                   IpAccuracy       `thrift:"ipac,12" json:"ipac"`
}

func NewGeoResult() *GeoResult {
	return &GeoResult{
		ProvinceCode: math.MinInt32 - 1, // unset sentinal value

		ThirdpartyProvinceCode: math.MinInt32 - 1, // unset sentinal value

		Ipac: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GeoResult) IsSetProvinceCode() bool {
	return int64(p.ProvinceCode) != math.MinInt32-1
}

func (p *GeoResult) IsSetThirdpartyProvinceCode() bool {
	return int64(p.ThirdpartyProvinceCode) != math.MinInt32-1
}

func (p *GeoResult) IsSetIpac() bool {
	return int64(p.Ipac) != math.MinInt32-1
}

func (p *GeoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GeoResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ProvinceCode = enums.RegionCode(v)
	}
	return nil
}

func (p *GeoResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CityCode = v
	}
	return nil
}

func (p *GeoResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RegionSourceType = v
	}
	return nil
}

func (p *GeoResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CellId = v
	}
	return nil
}

func (p *GeoResult) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.LocationAreaCode = v
	}
	return nil
}

func (p *GeoResult) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *GeoResult) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *GeoResult) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ThirdpartyProvinceCode = enums.RegionCode(v)
	}
	return nil
}

func (p *GeoResult) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ThirdpartyCityCode = v
	}
	return nil
}

func (p *GeoResult) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.GpsSource = v
	}
	return nil
}

func (p *GeoResult) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ipac = IpAccuracy(v)
	}
	return nil
}

func (p *GeoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GeoResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GeoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetProvinceCode() {
		if err := oprot.WriteFieldBegin("province_code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:province_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProvinceCode)); err != nil {
			return fmt.Errorf("%T.province_code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:province_code: %s", p, err)
		}
	}
	return err
}

func (p *GeoResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_code", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityCode)); err != nil {
		return fmt.Errorf("%T.city_code (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:city_code: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_source_type", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:region_source_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionSourceType)); err != nil {
		return fmt.Errorf("%T.region_source_type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:region_source_type: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cell_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:cell_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CellId)); err != nil {
		return fmt.Errorf("%T.cell_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:cell_id: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("location_area_code", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:location_area_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LocationAreaCode)); err != nil {
		return fmt.Errorf("%T.location_area_code (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:location_area_code: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:longitude: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:latitude: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetThirdpartyProvinceCode() {
		if err := oprot.WriteFieldBegin("thirdparty_province_code", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:thirdparty_province_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ThirdpartyProvinceCode)); err != nil {
			return fmt.Errorf("%T.thirdparty_province_code (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:thirdparty_province_code: %s", p, err)
		}
	}
	return err
}

func (p *GeoResult) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thirdparty_city_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:thirdparty_city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ThirdpartyCityCode)); err != nil {
		return fmt.Errorf("%T.thirdparty_city_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:thirdparty_city_code: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gps_source", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:gps_source: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GpsSource)); err != nil {
		return fmt.Errorf("%T.gps_source (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:gps_source: %s", p, err)
	}
	return err
}

func (p *GeoResult) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetIpac() {
		if err := oprot.WriteFieldBegin("ipac", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:ipac: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Ipac)); err != nil {
			return fmt.Errorf("%T.ipac (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:ipac: %s", p, err)
		}
	}
	return err
}

func (p *GeoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GeoResult(%+v)", *p)
}

type DeviceResult struct {
	OsId           int32  `thrift:"os_id,1" json:"os_id"`
	DeviceId       int32  `thrift:"device_id,2" json:"device_id"`
	DeviceDispname string `thrift:"device_dispname,3" json:"device_dispname"`
	BrowserId      int32  `thrift:"browser_id,4" json:"browser_id"`
	BrowserVersion string `thrift:"browser_version,5" json:"browser_version"`
	// unused field # 6
	Determine          string `thrift:"determine,7" json:"determine"`
	OsdeviceDependency string `thrift:"osdevice_dependency,8" json:"osdevice_dependency"`
	Xheader            string `thrift:"xheader,9" json:"xheader"`
}

func NewDeviceResult() *DeviceResult {
	return &DeviceResult{}
}

func (p *DeviceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeviceResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.OsId = v
	}
	return nil
}

func (p *DeviceResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeviceId = v
	}
	return nil
}

func (p *DeviceResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DeviceDispname = v
	}
	return nil
}

func (p *DeviceResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.BrowserId = v
	}
	return nil
}

func (p *DeviceResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.BrowserVersion = v
	}
	return nil
}

func (p *DeviceResult) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Determine = v
	}
	return nil
}

func (p *DeviceResult) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.OsdeviceDependency = v
	}
	return nil
}

func (p *DeviceResult) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Xheader = v
	}
	return nil
}

func (p *DeviceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeviceResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeviceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:os_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsId)); err != nil {
		return fmt.Errorf("%T.os_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:os_id: %s", p, err)
	}
	return err
}

func (p *DeviceResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:device_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceId)); err != nil {
		return fmt.Errorf("%T.device_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:device_id: %s", p, err)
	}
	return err
}

func (p *DeviceResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_dispname", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:device_dispname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceDispname)); err != nil {
		return fmt.Errorf("%T.device_dispname (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:device_dispname: %s", p, err)
	}
	return err
}

func (p *DeviceResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("browser_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:browser_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BrowserId)); err != nil {
		return fmt.Errorf("%T.browser_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:browser_id: %s", p, err)
	}
	return err
}

func (p *DeviceResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("browser_version", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:browser_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BrowserVersion)); err != nil {
		return fmt.Errorf("%T.browser_version (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:browser_version: %s", p, err)
	}
	return err
}

func (p *DeviceResult) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("determine", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:determine: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Determine)); err != nil {
		return fmt.Errorf("%T.determine (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:determine: %s", p, err)
	}
	return err
}

func (p *DeviceResult) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("osdevice_dependency", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:osdevice_dependency: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OsdeviceDependency)); err != nil {
		return fmt.Errorf("%T.osdevice_dependency (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:osdevice_dependency: %s", p, err)
	}
	return err
}

func (p *DeviceResult) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("xheader", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:xheader: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Xheader)); err != nil {
		return fmt.Errorf("%T.xheader (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:xheader: %s", p, err)
	}
	return err
}

func (p *DeviceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeviceResult(%+v)", *p)
}

type NetworkResult struct {
	CarrierCode        enums.CarrierCode    `thrift:"carrier_code,1" json:"carrier_code"`
	NetworkCarrierCode enums.CarrierCode    `thrift:"network_carrier_code,2" json:"network_carrier_code"`
	AccessType         enums.AccessTypeCode `thrift:"access_type,3" json:"access_type"`
}

func NewNetworkResult() *NetworkResult {
	return &NetworkResult{
		CarrierCode: math.MinInt32 - 1, // unset sentinal value

		NetworkCarrierCode: math.MinInt32 - 1, // unset sentinal value

		AccessType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *NetworkResult) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *NetworkResult) IsSetNetworkCarrierCode() bool {
	return int64(p.NetworkCarrierCode) != math.MinInt32-1
}

func (p *NetworkResult) IsSetAccessType() bool {
	return int64(p.AccessType) != math.MinInt32-1
}

func (p *NetworkResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *NetworkResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CarrierCode = enums.CarrierCode(v)
	}
	return nil
}

func (p *NetworkResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.NetworkCarrierCode = enums.CarrierCode(v)
	}
	return nil
}

func (p *NetworkResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccessType = enums.AccessTypeCode(v)
	}
	return nil
}

func (p *NetworkResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("NetworkResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *NetworkResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCarrierCode() {
		if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:carrier_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
			return fmt.Errorf("%T.carrier_code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:carrier_code: %s", p, err)
		}
	}
	return err
}

func (p *NetworkResult) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetNetworkCarrierCode() {
		if err := oprot.WriteFieldBegin("network_carrier_code", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:network_carrier_code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.NetworkCarrierCode)); err != nil {
			return fmt.Errorf("%T.network_carrier_code (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:network_carrier_code: %s", p, err)
		}
	}
	return err
}

func (p *NetworkResult) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAccessType() {
		if err := oprot.WriteFieldBegin("access_type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:access_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AccessType)); err != nil {
			return fmt.Errorf("%T.access_type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:access_type: %s", p, err)
		}
	}
	return err
}

func (p *NetworkResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("NetworkResult(%+v)", *p)
}

type TrafficSelector struct {
	HitTraffic []int32 `thrift:"hit_traffic,1" json:"hit_traffic"`
}

func NewTrafficSelector() *TrafficSelector {
	return &TrafficSelector{}
}

func (p *TrafficSelector) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TrafficSelector) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.HitTraffic = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.HitTraffic = append(p.HitTraffic, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TrafficSelector) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TrafficSelector"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TrafficSelector) writeField1(oprot thrift.TProtocol) (err error) {
	if p.HitTraffic != nil {
		if err := oprot.WriteFieldBegin("hit_traffic", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:hit_traffic: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.HitTraffic)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.HitTraffic {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:hit_traffic: %s", p, err)
		}
	}
	return err
}

func (p *TrafficSelector) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TrafficSelector(%+v)", *p)
}

type UserIdResult struct {
	Idfa    string `thrift:"idfa,1" json:"idfa"`
	Imei    string `thrift:"imei,2" json:"imei"`
	IdfaMd5 string `thrift:"idfa_md5,3" json:"idfa_md5"`
	ImeiMd5 string `thrift:"imei_md5,4" json:"imei_md5"`
}

func NewUserIdResult() *UserIdResult {
	return &UserIdResult{}
}

func (p *UserIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserIdResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *UserIdResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *UserIdResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdfaMd5 = v
	}
	return nil
}

func (p *UserIdResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImeiMd5 = v
	}
	return nil
}

func (p *UserIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserIdResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:idfa: %s", p, err)
	}
	return err
}

func (p *UserIdResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:imei: %s", p, err)
	}
	return err
}

func (p *UserIdResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa_md5", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:idfa_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IdfaMd5)); err != nil {
		return fmt.Errorf("%T.idfa_md5 (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:idfa_md5: %s", p, err)
	}
	return err
}

func (p *UserIdResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei_md5", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:imei_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiMd5)); err != nil {
		return fmt.Errorf("%T.imei_md5 (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:imei_md5: %s", p, err)
	}
	return err
}

func (p *UserIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserIdResult(%+v)", *p)
}

type UserFact struct {
	Geo             *GeoResult       `thrift:"geo,1" json:"geo"`
	Network         *NetworkResult   `thrift:"network,2" json:"network"`
	Device          *DeviceResult    `thrift:"device,3" json:"device"`
	AckId           int64            `thrift:"ack_id,4" json:"ack_id"`
	UpExpid         int32            `thrift:"up_expid,5" json:"up_expid"`
	Ts              *TrafficSelector `thrift:"ts,6" json:"ts"`
	UserIdResult    *UserIdResult    `thrift:"user_id_result,7" json:"user_id_result"`
	InstalledAppIds []int32          `thrift:"installed_app_ids,8" json:"installed_app_ids"`
	InstallInfo     map[int32]int32  `thrift:"install_info,9" json:"install_info"`
}

func NewUserFact() *UserFact {
	return &UserFact{}
}

func (p *UserFact) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.MAP {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UserFact) readField1(iprot thrift.TProtocol) error {
	p.Geo = NewGeoResult()
	if err := p.Geo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Geo)
	}
	return nil
}

func (p *UserFact) readField2(iprot thrift.TProtocol) error {
	p.Network = NewNetworkResult()
	if err := p.Network.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Network)
	}
	return nil
}

func (p *UserFact) readField3(iprot thrift.TProtocol) error {
	p.Device = NewDeviceResult()
	if err := p.Device.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Device)
	}
	return nil
}

func (p *UserFact) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AckId = v
	}
	return nil
}

func (p *UserFact) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UpExpid = v
	}
	return nil
}

func (p *UserFact) readField6(iprot thrift.TProtocol) error {
	p.Ts = NewTrafficSelector()
	if err := p.Ts.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ts)
	}
	return nil
}

func (p *UserFact) readField7(iprot thrift.TProtocol) error {
	p.UserIdResult = NewUserIdResult()
	if err := p.UserIdResult.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UserIdResult)
	}
	return nil
}

func (p *UserFact) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.InstalledAppIds = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.InstalledAppIds = append(p.InstalledAppIds, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UserFact) readField9(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.InstallInfo = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key7 = v
		}
		var _val8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val8 = v
		}
		p.InstallInfo[_key7] = _val8
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UserFact) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UserFact"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UserFact) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Geo != nil {
		if err := oprot.WriteFieldBegin("geo", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:geo: %s", p, err)
		}
		if err := p.Geo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Geo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:geo: %s", p, err)
		}
	}
	return err
}

func (p *UserFact) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Network != nil {
		if err := oprot.WriteFieldBegin("network", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:network: %s", p, err)
		}
		if err := p.Network.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Network)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:network: %s", p, err)
		}
	}
	return err
}

func (p *UserFact) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Device != nil {
		if err := oprot.WriteFieldBegin("device", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:device: %s", p, err)
		}
		if err := p.Device.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Device)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:device: %s", p, err)
		}
	}
	return err
}

func (p *UserFact) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ack_id", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ack_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AckId)); err != nil {
		return fmt.Errorf("%T.ack_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ack_id: %s", p, err)
	}
	return err
}

func (p *UserFact) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("up_expid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:up_expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UpExpid)); err != nil {
		return fmt.Errorf("%T.up_expid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:up_expid: %s", p, err)
	}
	return err
}

func (p *UserFact) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Ts != nil {
		if err := oprot.WriteFieldBegin("ts", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:ts: %s", p, err)
		}
		if err := p.Ts.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ts)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:ts: %s", p, err)
		}
	}
	return err
}

func (p *UserFact) writeField7(oprot thrift.TProtocol) (err error) {
	if p.UserIdResult != nil {
		if err := oprot.WriteFieldBegin("user_id_result", thrift.STRUCT, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:user_id_result: %s", p, err)
		}
		if err := p.UserIdResult.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UserIdResult)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:user_id_result: %s", p, err)
		}
	}
	return err
}

func (p *UserFact) writeField8(oprot thrift.TProtocol) (err error) {
	if p.InstalledAppIds != nil {
		if err := oprot.WriteFieldBegin("installed_app_ids", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:installed_app_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.InstalledAppIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.InstalledAppIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:installed_app_ids: %s", p, err)
		}
	}
	return err
}

func (p *UserFact) writeField9(oprot thrift.TProtocol) (err error) {
	if p.InstallInfo != nil {
		if err := oprot.WriteFieldBegin("install_info", thrift.MAP, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:install_info: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.InstallInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.InstallInfo {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:install_info: %s", p, err)
		}
	}
	return err
}

func (p *UserFact) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UserFact(%+v)", *p)
}
