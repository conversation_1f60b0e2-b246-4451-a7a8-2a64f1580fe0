// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package user_profile

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type UserProfileService interface {
	dm303.DomobService

	// 分析一个用户的属性
	//
	// 所有的错误都通过异常抛出，message中是错误原因，code目前无用
	// 如果没有异常，那么UserFact就是分析的结果
	//
	// Parameters:
	//  - Userinfo
	Profile(userinfo *UserRawInfo) (r *UserFact, e *UPException, err error)
}

type UserProfileServiceClient struct {
	*dm303.DomobServiceClient
}

func NewUserProfileServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *UserProfileServiceClient {
	return &UserProfileServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewUserProfileServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *UserProfileServiceClient {
	return &UserProfileServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 分析一个用户的属性
//
// 所有的错误都通过异常抛出，message中是错误原因，code目前无用
// 如果没有异常，那么UserFact就是分析的结果
//
// Parameters:
//  - Userinfo
func (p *UserProfileServiceClient) Profile(userinfo *UserRawInfo) (r *UserFact, e *UPException, err error) {
	if err = p.sendProfile(userinfo); err != nil {
		return
	}
	return p.recvProfile()
}

func (p *UserProfileServiceClient) sendProfile(userinfo *UserRawInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("profile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args9 := NewProfileArgs()
	args9.Userinfo = userinfo
	if err = args9.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *UserProfileServiceClient) recvProfile() (value *UserFact, e *UPException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error11 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error12 error
		error12, err = error11.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error12
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result10 := NewProfileResult()
	if err = result10.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result10.Success
	if result10.E != nil {
		e = result10.E
	}
	return
}

type UserProfileServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewUserProfileServiceProcessor(handler UserProfileService) *UserProfileServiceProcessor {
	self13 := &UserProfileServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self13.AddToProcessorMap("profile", &userProfileServiceProcessorProfile{handler: handler})
	return self13
}

type userProfileServiceProcessorProfile struct {
	handler UserProfileService
}

func (p *userProfileServiceProcessorProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewProfileResult()
	if result.Success, result.E, err = p.handler.Profile(args.Userinfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing profile: "+err.Error())
		oprot.WriteMessageBegin("profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("profile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ProfileArgs struct {
	Userinfo *UserRawInfo `thrift:"userinfo,1" json:"userinfo"`
}

func NewProfileArgs() *ProfileArgs {
	return &ProfileArgs{}
}

func (p *ProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.Userinfo = NewUserRawInfo()
	if err := p.Userinfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Userinfo)
	}
	return nil
}

func (p *ProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("profile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Userinfo != nil {
		if err := oprot.WriteFieldBegin("userinfo", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:userinfo: %s", p, err)
		}
		if err := p.Userinfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Userinfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:userinfo: %s", p, err)
		}
	}
	return err
}

func (p *ProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProfileArgs(%+v)", *p)
}

type ProfileResult struct {
	Success *UserFact    `thrift:"success,0" json:"success"`
	E       *UPException `thrift:"e,1" json:"e"`
}

func NewProfileResult() *ProfileResult {
	return &ProfileResult{}
}

func (p *ProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProfileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewUserFact()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ProfileResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewUPException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("profile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProfileResult(%+v)", *p)
}
