// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package ct_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = programmatic_creative_types.GoUnusedProtection__
var GoUnusedProtection__ int

//视频素材来源 *
type VideoSourceType int64

const (
	VideoSourceType_VST_UNKNOWN          VideoSourceType = 0
	VideoSourceType_VST_CUSTOMER_PROVIDE VideoSourceType = 1
	VideoSourceType_VST_NETWORK_MATERIAL VideoSourceType = 2
	VideoSourceType_VST_SCREENSHOT       VideoSourceType = 3
	VideoSourceType_VST_FREE_ISLAND      VideoSourceType = 4
)

func (p VideoSourceType) String() string {
	switch p {
	case VideoSourceType_VST_UNKNOWN:
		return "VideoSourceType_VST_UNKNOWN"
	case VideoSourceType_VST_CUSTOMER_PROVIDE:
		return "VideoSourceType_VST_CUSTOMER_PROVIDE"
	case VideoSourceType_VST_NETWORK_MATERIAL:
		return "VideoSourceType_VST_NETWORK_MATERIAL"
	case VideoSourceType_VST_SCREENSHOT:
		return "VideoSourceType_VST_SCREENSHOT"
	case VideoSourceType_VST_FREE_ISLAND:
		return "VideoSourceType_VST_FREE_ISLAND"
	}
	return "<UNSET>"
}

func VideoSourceTypeFromString(s string) (VideoSourceType, error) {
	switch s {
	case "VideoSourceType_VST_UNKNOWN":
		return VideoSourceType_VST_UNKNOWN, nil
	case "VideoSourceType_VST_CUSTOMER_PROVIDE":
		return VideoSourceType_VST_CUSTOMER_PROVIDE, nil
	case "VideoSourceType_VST_NETWORK_MATERIAL":
		return VideoSourceType_VST_NETWORK_MATERIAL, nil
	case "VideoSourceType_VST_SCREENSHOT":
		return VideoSourceType_VST_SCREENSHOT, nil
	case "VideoSourceType_VST_FREE_ISLAND":
		return VideoSourceType_VST_FREE_ISLAND, nil
	}
	return VideoSourceType(math.MinInt32 - 1), fmt.Errorf("not a valid VideoSourceType string")
}

//视频转换状态
type VideoTranscodingStatus int64

const (
	VideoTranscodingStatus_VTS_UNKNOWN                VideoTranscodingStatus = 0
	VideoTranscodingStatus_VTS_PENDING                VideoTranscodingStatus = 1
	VideoTranscodingStatus_VTS_TRANSCODING            VideoTranscodingStatus = 2
	VideoTranscodingStatus_VTS_COMPLETE               VideoTranscodingStatus = 3
	VideoTranscodingStatus_VTS_FAIL                   VideoTranscodingStatus = 4
	VideoTranscodingStatus_VTS_YOUKU_UPLOAD_COMPLETE  VideoTranscodingStatus = 10
	VideoTranscodingStatus_VTS_YOUKU_TRANSCODING      VideoTranscodingStatus = 11
	VideoTranscodingStatus_VTS_YOUKU_TRANSCODING_FAIL VideoTranscodingStatus = 12
	VideoTranscodingStatus_VTS_YOUKU_AUDITING         VideoTranscodingStatus = 13
	VideoTranscodingStatus_VTS_YOUKU_AUDIT_FAIL       VideoTranscodingStatus = 14
	VideoTranscodingStatus_VTS_YOUKU_AUDIT_SUCCESS    VideoTranscodingStatus = 15
)

func (p VideoTranscodingStatus) String() string {
	switch p {
	case VideoTranscodingStatus_VTS_UNKNOWN:
		return "VideoTranscodingStatus_VTS_UNKNOWN"
	case VideoTranscodingStatus_VTS_PENDING:
		return "VideoTranscodingStatus_VTS_PENDING"
	case VideoTranscodingStatus_VTS_TRANSCODING:
		return "VideoTranscodingStatus_VTS_TRANSCODING"
	case VideoTranscodingStatus_VTS_COMPLETE:
		return "VideoTranscodingStatus_VTS_COMPLETE"
	case VideoTranscodingStatus_VTS_FAIL:
		return "VideoTranscodingStatus_VTS_FAIL"
	case VideoTranscodingStatus_VTS_YOUKU_UPLOAD_COMPLETE:
		return "VideoTranscodingStatus_VTS_YOUKU_UPLOAD_COMPLETE"
	case VideoTranscodingStatus_VTS_YOUKU_TRANSCODING:
		return "VideoTranscodingStatus_VTS_YOUKU_TRANSCODING"
	case VideoTranscodingStatus_VTS_YOUKU_TRANSCODING_FAIL:
		return "VideoTranscodingStatus_VTS_YOUKU_TRANSCODING_FAIL"
	case VideoTranscodingStatus_VTS_YOUKU_AUDITING:
		return "VideoTranscodingStatus_VTS_YOUKU_AUDITING"
	case VideoTranscodingStatus_VTS_YOUKU_AUDIT_FAIL:
		return "VideoTranscodingStatus_VTS_YOUKU_AUDIT_FAIL"
	case VideoTranscodingStatus_VTS_YOUKU_AUDIT_SUCCESS:
		return "VideoTranscodingStatus_VTS_YOUKU_AUDIT_SUCCESS"
	}
	return "<UNSET>"
}

func VideoTranscodingStatusFromString(s string) (VideoTranscodingStatus, error) {
	switch s {
	case "VideoTranscodingStatus_VTS_UNKNOWN":
		return VideoTranscodingStatus_VTS_UNKNOWN, nil
	case "VideoTranscodingStatus_VTS_PENDING":
		return VideoTranscodingStatus_VTS_PENDING, nil
	case "VideoTranscodingStatus_VTS_TRANSCODING":
		return VideoTranscodingStatus_VTS_TRANSCODING, nil
	case "VideoTranscodingStatus_VTS_COMPLETE":
		return VideoTranscodingStatus_VTS_COMPLETE, nil
	case "VideoTranscodingStatus_VTS_FAIL":
		return VideoTranscodingStatus_VTS_FAIL, nil
	case "VideoTranscodingStatus_VTS_YOUKU_UPLOAD_COMPLETE":
		return VideoTranscodingStatus_VTS_YOUKU_UPLOAD_COMPLETE, nil
	case "VideoTranscodingStatus_VTS_YOUKU_TRANSCODING":
		return VideoTranscodingStatus_VTS_YOUKU_TRANSCODING, nil
	case "VideoTranscodingStatus_VTS_YOUKU_TRANSCODING_FAIL":
		return VideoTranscodingStatus_VTS_YOUKU_TRANSCODING_FAIL, nil
	case "VideoTranscodingStatus_VTS_YOUKU_AUDITING":
		return VideoTranscodingStatus_VTS_YOUKU_AUDITING, nil
	case "VideoTranscodingStatus_VTS_YOUKU_AUDIT_FAIL":
		return VideoTranscodingStatus_VTS_YOUKU_AUDIT_FAIL, nil
	case "VideoTranscodingStatus_VTS_YOUKU_AUDIT_SUCCESS":
		return VideoTranscodingStatus_VTS_YOUKU_AUDIT_SUCCESS, nil
	}
	return VideoTranscodingStatus(math.MinInt32 - 1), fmt.Errorf("not a valid VideoTranscodingStatus string")
}

//视频生成状态
type VideoGenerationCode int64

const (
	VideoGenerationCode_VGS_SUCCESS         VideoGenerationCode = 0
	VideoGenerationCode_VGS_PARAMETER_ERROR VideoGenerationCode = 1
	VideoGenerationCode_VGS_SYSTEM_ERROR    VideoGenerationCode = 2
)

func (p VideoGenerationCode) String() string {
	switch p {
	case VideoGenerationCode_VGS_SUCCESS:
		return "VideoGenerationCode_VGS_SUCCESS"
	case VideoGenerationCode_VGS_PARAMETER_ERROR:
		return "VideoGenerationCode_VGS_PARAMETER_ERROR"
	case VideoGenerationCode_VGS_SYSTEM_ERROR:
		return "VideoGenerationCode_VGS_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func VideoGenerationCodeFromString(s string) (VideoGenerationCode, error) {
	switch s {
	case "VideoGenerationCode_VGS_SUCCESS":
		return VideoGenerationCode_VGS_SUCCESS, nil
	case "VideoGenerationCode_VGS_PARAMETER_ERROR":
		return VideoGenerationCode_VGS_PARAMETER_ERROR, nil
	case "VideoGenerationCode_VGS_SYSTEM_ERROR":
		return VideoGenerationCode_VGS_SYSTEM_ERROR, nil
	}
	return VideoGenerationCode(math.MinInt32 - 1), fmt.Errorf("not a valid VideoGenerationCode string")
}

type VideoGenerationInfo struct {
	Code    VideoGenerationCode `thrift:"code,1" json:"code"`
	Message string              `thrift:"message,2" json:"message"`
}

func NewVideoGenerationInfo() *VideoGenerationInfo {
	return &VideoGenerationInfo{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *VideoGenerationInfo) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *VideoGenerationInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoGenerationInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = VideoGenerationCode(v)
	}
	return nil
}

func (p *VideoGenerationInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *VideoGenerationInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VideoGenerationInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoGenerationInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *VideoGenerationInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *VideoGenerationInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoGenerationInfo(%+v)", *p)
}

type SourceMaterial struct {
	Id         int32           `thrift:"id,1" json:"id"`
	Name       string          `thrift:"name,2" json:"name"`
	UgcId      int64           `thrift:"ugcId,3" json:"ugcId"`
	EncodeId   string          `thrift:"encodeId,4" json:"encodeId"`
	Duration   int32           `thrift:"duration,5" json:"duration"`
	Thumb      string          `thrift:"thumb,6" json:"thumb"`
	Uploader   int32           `thrift:"uploader,7" json:"uploader"`
	Author     int32           `thrift:"author,8" json:"author"`
	SourceType VideoSourceType `thrift:"sourceType,9" json:"sourceType"`
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewSourceMaterial() *SourceMaterial {
	return &SourceMaterial{
		SourceType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SourceMaterial) IsSetSourceType() bool {
	return int64(p.SourceType) != math.MinInt32-1
}

func (p *SourceMaterial) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SourceMaterial) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SourceMaterial) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *SourceMaterial) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *SourceMaterial) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EncodeId = v
	}
	return nil
}

func (p *SourceMaterial) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *SourceMaterial) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Thumb = v
	}
	return nil
}

func (p *SourceMaterial) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Uploader = v
	}
	return nil
}

func (p *SourceMaterial) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Author = v
	}
	return nil
}

func (p *SourceMaterial) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SourceType = VideoSourceType(v)
	}
	return nil
}

func (p *SourceMaterial) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *SourceMaterial) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *SourceMaterial) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *SourceMaterial) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SourceMaterial"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SourceMaterial) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ugcId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ugcId: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encodeId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:encodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodeId)); err != nil {
		return fmt.Errorf("%T.encodeId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:encodeId: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:duration: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thumb", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:thumb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Thumb)); err != nil {
		return fmt.Errorf("%T.thumb (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:thumb: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uploader", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:uploader: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uploader)); err != nil {
		return fmt.Errorf("%T.uploader (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:uploader: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:author: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Author)); err != nil {
		return fmt.Errorf("%T.author (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:author: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetSourceType() {
		if err := oprot.WriteFieldBegin("sourceType", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:sourceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SourceType)); err != nil {
			return fmt.Errorf("%T.sourceType (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:sourceType: %s", p, err)
		}
	}
	return err
}

func (p *SourceMaterial) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *SourceMaterial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SourceMaterial(%+v)", *p)
}

type SourceMaterialParams struct {
	Terms       map[string]string `thrift:"terms,1" json:"terms"`
	SourceTypes []VideoSourceType `thrift:"sourceTypes,2" json:"sourceTypes"`
	Offset      int32             `thrift:"offset,3" json:"offset"`
	Limit       int32             `thrift:"limit,4" json:"limit"`
}

func NewSourceMaterialParams() *SourceMaterialParams {
	return &SourceMaterialParams{}
}

func (p *SourceMaterialParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SourceMaterialParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.Terms[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *SourceMaterialParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SourceTypes = make([]VideoSourceType, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 VideoSourceType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = VideoSourceType(v)
		}
		p.SourceTypes = append(p.SourceTypes, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SourceMaterialParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SourceMaterialParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SourceMaterialParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SourceMaterialParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SourceMaterialParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *SourceMaterialParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SourceTypes != nil {
		if err := oprot.WriteFieldBegin("sourceTypes", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:sourceTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SourceTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SourceTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:sourceTypes: %s", p, err)
		}
	}
	return err
}

func (p *SourceMaterialParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *SourceMaterialParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *SourceMaterialParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SourceMaterialParams(%+v)", *p)
}

type DeliveryMaterial struct {
	Id            int32             `thrift:"id,1" json:"id"`
	SourceId      int32             `thrift:"sourceId,2" json:"sourceId"`
	Name          string            `thrift:"name,3" json:"name"`
	UgcId         int64             `thrift:"ugcId,4" json:"ugcId"`
	EncodeId      string            `thrift:"encodeId,5" json:"encodeId"`
	Author        int32             `thrift:"author,6" json:"author"`
	AppId         int64             `thrift:"appId,7" json:"appId"`
	Duration      int32             `thrift:"duration,8" json:"duration"`
	ClipStartTime int32             `thrift:"clipStartTime,9" json:"clipStartTime"`
	ClipEndTime   int32             `thrift:"clipEndTime,10" json:"clipEndTime"`
	LandingPage   string            `thrift:"landingPage,11" json:"landingPage"`
	ButtonText    string            `thrift:"buttonText,12" json:"buttonText"`
	ButtonType    string            `thrift:"buttonType,13" json:"buttonType"`
	Channels      map[int32][]int32 `thrift:"channels,14" json:"channels"`
	Thumb         string            `thrift:"thumb,15" json:"thumb"`
	WaterMark     int32             `thrift:"waterMark,16" json:"waterMark"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewDeliveryMaterial() *DeliveryMaterial {
	return &DeliveryMaterial{}
}

func (p *DeliveryMaterial) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.MAP {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeliveryMaterial) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DeliveryMaterial) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SourceId = v
	}
	return nil
}

func (p *DeliveryMaterial) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DeliveryMaterial) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *DeliveryMaterial) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EncodeId = v
	}
	return nil
}

func (p *DeliveryMaterial) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Author = v
	}
	return nil
}

func (p *DeliveryMaterial) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *DeliveryMaterial) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *DeliveryMaterial) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ClipStartTime = v
	}
	return nil
}

func (p *DeliveryMaterial) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ClipEndTime = v
	}
	return nil
}

func (p *DeliveryMaterial) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.LandingPage = v
	}
	return nil
}

func (p *DeliveryMaterial) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ButtonText = v
	}
	return nil
}

func (p *DeliveryMaterial) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ButtonType = v
	}
	return nil
}

func (p *DeliveryMaterial) readField14(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Channels = make(map[int32][]int32, size)
	for i := 0; i < size; i++ {
		var _key3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key3 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val4 := make([]int32, 0, size)
		for i := 0; i < size; i++ {
			var _elem5 int32
			if v, err := iprot.ReadI32(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_elem5 = v
			}
			_val4 = append(_val4, _elem5)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Channels[_key3] = _val4
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DeliveryMaterial) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Thumb = v
	}
	return nil
}

func (p *DeliveryMaterial) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.WaterMark = v
	}
	return nil
}

func (p *DeliveryMaterial) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *DeliveryMaterial) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DeliveryMaterial) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *DeliveryMaterial) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeliveryMaterial"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeliveryMaterial) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sourceId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sourceId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SourceId)); err != nil {
		return fmt.Errorf("%T.sourceId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sourceId: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ugcId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ugcId: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encodeId", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:encodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodeId)); err != nil {
		return fmt.Errorf("%T.encodeId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:encodeId: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:author: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Author)); err != nil {
		return fmt.Errorf("%T.author (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:author: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:appId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:appId: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:duration: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clipStartTime", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:clipStartTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClipStartTime)); err != nil {
		return fmt.Errorf("%T.clipStartTime (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:clipStartTime: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clipEndTime", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:clipEndTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClipEndTime)); err != nil {
		return fmt.Errorf("%T.clipEndTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:clipEndTime: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landingPage", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:landingPage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LandingPage)); err != nil {
		return fmt.Errorf("%T.landingPage (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:landingPage: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("buttonText", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:buttonText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ButtonText)); err != nil {
		return fmt.Errorf("%T.buttonText (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:buttonText: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("buttonType", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:buttonType: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ButtonType)); err != nil {
		return fmt.Errorf("%T.buttonType (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:buttonType: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField14(oprot thrift.TProtocol) (err error) {
	if p.Channels != nil {
		if err := oprot.WriteFieldBegin("channels", thrift.MAP, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:channels: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.Channels)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Channels {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteI32(int32(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:channels: %s", p, err)
		}
	}
	return err
}

func (p *DeliveryMaterial) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thumb", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:thumb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Thumb)); err != nil {
		return fmt.Errorf("%T.thumb (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:thumb: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("waterMark", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:waterMark: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WaterMark)); err != nil {
		return fmt.Errorf("%T.waterMark (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:waterMark: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeliveryMaterial(%+v)", *p)
}

type DeliveryMaterialParams struct {
	Terms     map[string]string `thrift:"terms,1" json:"terms"`
	Durations []int32           `thrift:"durations,2" json:"durations"`
	Offset    int32             `thrift:"offset,3" json:"offset"`
	Limit     int32             `thrift:"limit,4" json:"limit"`
}

func NewDeliveryMaterialParams() *DeliveryMaterialParams {
	return &DeliveryMaterialParams{}
}

func (p *DeliveryMaterialParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeliveryMaterialParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key6 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key6 = v
		}
		var _val7 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val7 = v
		}
		p.Terms[_key6] = _val7
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DeliveryMaterialParams) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Durations = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.Durations = append(p.Durations, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DeliveryMaterialParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *DeliveryMaterialParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *DeliveryMaterialParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeliveryMaterialParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeliveryMaterialParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *DeliveryMaterialParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Durations != nil {
		if err := oprot.WriteFieldBegin("durations", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:durations: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Durations)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Durations {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:durations: %s", p, err)
		}
	}
	return err
}

func (p *DeliveryMaterialParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterialParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *DeliveryMaterialParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeliveryMaterialParams(%+v)", *p)
}

type DeliveryChannel struct {
	Id         int32                  `thrift:"id,1" json:"id"`
	MaterialId int32                  `thrift:"materialId,2" json:"materialId"`
	Channel    int32                  `thrift:"channel,3" json:"channel"`
	Status     VideoTranscodingStatus `thrift:"status,4" json:"status"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewDeliveryChannel() *DeliveryChannel {
	return &DeliveryChannel{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DeliveryChannel) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DeliveryChannel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeliveryChannel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DeliveryChannel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaterialId = v
	}
	return nil
}

func (p *DeliveryChannel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *DeliveryChannel) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = VideoTranscodingStatus(v)
	}
	return nil
}

func (p *DeliveryChannel) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DeliveryChannel) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *DeliveryChannel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DeliveryChannel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeliveryChannel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DeliveryChannel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("materialId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:materialId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaterialId)); err != nil {
		return fmt.Errorf("%T.materialId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:materialId: %s", p, err)
	}
	return err
}

func (p *DeliveryChannel) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channel: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channel: %s", p, err)
	}
	return err
}

func (p *DeliveryChannel) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:status: %s", p, err)
		}
	}
	return err
}

func (p *DeliveryChannel) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *DeliveryChannel) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *DeliveryChannel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeliveryChannel(%+v)", *p)
}

type VideoMaterialParams struct {
	Id         int32  `thrift:"id,1" json:"id"`
	Name       string `thrift:"name,2" json:"name"`
	AppId      int32  `thrift:"appId,3" json:"appId"`
	ExchangeId int32  `thrift:"exchangeId,4" json:"exchangeId"`
	Duration   int32  `thrift:"duration,5" json:"duration"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	Offset int32 `thrift:"offset,98" json:"offset"`
	Limit  int32 `thrift:"limit,99" json:"limit"`
}

func NewVideoMaterialParams() *VideoMaterialParams {
	return &VideoMaterialParams{}
}

func (p *VideoMaterialParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I32 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I32 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoMaterialParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *VideoMaterialParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *VideoMaterialParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *VideoMaterialParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *VideoMaterialParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *VideoMaterialParams) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *VideoMaterialParams) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *VideoMaterialParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VideoMaterialParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoMaterialParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *VideoMaterialParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *VideoMaterialParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appId: %s", p, err)
	}
	return err
}

func (p *VideoMaterialParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:exchangeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:exchangeId: %s", p, err)
	}
	return err
}

func (p *VideoMaterialParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:duration: %s", p, err)
	}
	return err
}

func (p *VideoMaterialParams) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:offset: %s", p, err)
	}
	return err
}

func (p *VideoMaterialParams) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:limit: %s", p, err)
	}
	return err
}

func (p *VideoMaterialParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoMaterialParams(%+v)", *p)
}

type VideoMaterial struct {
	Id         int32                  `thrift:"id,1" json:"id"`
	ChannelId  int32                  `thrift:"channelId,2" json:"channelId"`
	UgcId      int64                  `thrift:"ugcId,3" json:"ugcId"`
	EncodeId   string                 `thrift:"encodeId,4" json:"encodeId"`
	ExchangeId int32                  `thrift:"exchangeId,5" json:"exchangeId"`
	Status     VideoTranscodingStatus `thrift:"status,6" json:"status"`
	ErrorCode  int32                  `thrift:"errorCode,7" json:"errorCode"`
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewVideoMaterial() *VideoMaterial {
	return &VideoMaterial{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *VideoMaterial) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *VideoMaterial) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoMaterial) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *VideoMaterial) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *VideoMaterial) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *VideoMaterial) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EncodeId = v
	}
	return nil
}

func (p *VideoMaterial) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *VideoMaterial) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Status = VideoTranscodingStatus(v)
	}
	return nil
}

func (p *VideoMaterial) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ErrorCode = v
	}
	return nil
}

func (p *VideoMaterial) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *VideoMaterial) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *VideoMaterial) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VideoMaterial"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoMaterial) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:channelId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channelId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:channelId: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ugcId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ugcId: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encodeId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:encodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodeId)); err != nil {
		return fmt.Errorf("%T.encodeId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:encodeId: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:exchangeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:exchangeId: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:status: %s", p, err)
		}
	}
	return err
}

func (p *VideoMaterial) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("errorCode", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:errorCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ErrorCode)); err != nil {
		return fmt.Errorf("%T.errorCode (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:errorCode: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *VideoMaterial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoMaterial(%+v)", *p)
}

type VideoInfo struct {
	Id         int32  `thrift:"id,1" json:"id"`
	Name       string `thrift:"name,2" json:"name"`
	AppId      int32  `thrift:"appId,3" json:"appId"`
	ExchangeId int32  `thrift:"exchangeId,4" json:"exchangeId"`
	Duration   int32  `thrift:"duration,5" json:"duration"`
	Thumb      string `thrift:"thumb,6" json:"thumb"`
	UgcId      int64  `thrift:"ugcId,7" json:"ugcId"`
	EncodeId   string `thrift:"encodeId,8" json:"encodeId"`
}

func NewVideoInfo() *VideoInfo {
	return &VideoInfo{}
}

func (p *VideoInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *VideoInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *VideoInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *VideoInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *VideoInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *VideoInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *VideoInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Thumb = v
	}
	return nil
}

func (p *VideoInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *VideoInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.EncodeId = v
	}
	return nil
}

func (p *VideoInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("VideoInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *VideoInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *VideoInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *VideoInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appId: %s", p, err)
	}
	return err
}

func (p *VideoInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchangeId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:exchangeId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchangeId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:exchangeId: %s", p, err)
	}
	return err
}

func (p *VideoInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:duration: %s", p, err)
	}
	return err
}

func (p *VideoInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thumb", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:thumb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Thumb)); err != nil {
		return fmt.Errorf("%T.thumb (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:thumb: %s", p, err)
	}
	return err
}

func (p *VideoInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ugcId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ugcId: %s", p, err)
	}
	return err
}

func (p *VideoInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encodeId", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:encodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodeId)); err != nil {
		return fmt.Errorf("%T.encodeId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:encodeId: %s", p, err)
	}
	return err
}

func (p *VideoInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("VideoInfo(%+v)", *p)
}

type ConvertHtmlToImage struct {
	Code       string                                             `thrift:"code,1" json:"code"`
	Size       int32                                              `thrift:"size,2" json:"size"`
	Format     programmatic_creative_types.PCHTML2ImageFileFormat `thrift:"format,3" json:"format"`
	ResultType programmatic_creative_types.PCHTML2ImageResultType `thrift:"resultType,4" json:"resultType"`
	UgcId      int32                                              `thrift:"ugcId,5" json:"ugcId"`
	EncodeId   string                                             `thrift:"encodeId,6" json:"encodeId"`
}

func NewConvertHtmlToImage() *ConvertHtmlToImage {
	return &ConvertHtmlToImage{
		Format: math.MinInt32 - 1, // unset sentinal value

		ResultType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ConvertHtmlToImage) IsSetFormat() bool {
	return int64(p.Format) != math.MinInt32-1
}

func (p *ConvertHtmlToImage) IsSetResultType() bool {
	return int64(p.ResultType) != math.MinInt32-1
}

func (p *ConvertHtmlToImage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConvertHtmlToImage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = v
	}
	return nil
}

func (p *ConvertHtmlToImage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *ConvertHtmlToImage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Format = programmatic_creative_types.PCHTML2ImageFileFormat(v)
	}
	return nil
}

func (p *ConvertHtmlToImage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ResultType = programmatic_creative_types.PCHTML2ImageResultType(v)
	}
	return nil
}

func (p *ConvertHtmlToImage) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *ConvertHtmlToImage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EncodeId = v
	}
	return nil
}

func (p *ConvertHtmlToImage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ConvertHtmlToImage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConvertHtmlToImage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("code", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Code)); err != nil {
		return fmt.Errorf("%T.code (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:code: %s", p, err)
	}
	return err
}

func (p *ConvertHtmlToImage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:size: %s", p, err)
	}
	return err
}

func (p *ConvertHtmlToImage) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetFormat() {
		if err := oprot.WriteFieldBegin("format", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:format: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Format)); err != nil {
			return fmt.Errorf("%T.format (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:format: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtmlToImage) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetResultType() {
		if err := oprot.WriteFieldBegin("resultType", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:resultType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResultType)); err != nil {
			return fmt.Errorf("%T.resultType (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:resultType: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtmlToImage) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ugcId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ugcId: %s", p, err)
	}
	return err
}

func (p *ConvertHtmlToImage) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encodeId", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:encodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodeId)); err != nil {
		return fmt.Errorf("%T.encodeId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:encodeId: %s", p, err)
	}
	return err
}

func (p *ConvertHtmlToImage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertHtmlToImage(%+v)", *p)
}
