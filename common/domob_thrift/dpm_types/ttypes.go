// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dpm_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//产品类型
type ProductType int64

const (
	ProductType_UNKNOWN  ProductType = 0
	ProductType_IOS      ProductType = 1
	ProductType_ANDROID  ProductType = 2
	ProductType_WEB_PAGE ProductType = 3
	ProductType_IOS_JB   ProductType = 4
)

func (p ProductType) String() string {
	switch p {
	case ProductType_UNKNOWN:
		return "ProductType_UNKNOWN"
	case ProductType_IOS:
		return "ProductType_IOS"
	case ProductType_ANDROID:
		return "ProductType_ANDROID"
	case ProductType_WEB_PAGE:
		return "ProductType_WEB_PAGE"
	case ProductType_IOS_JB:
		return "ProductType_IOS_JB"
	}
	return "<UNSET>"
}

func ProductTypeFromString(s string) (ProductType, error) {
	switch s {
	case "ProductType_UNKNOWN":
		return ProductType_UNKNOWN, nil
	case "ProductType_IOS":
		return ProductType_IOS, nil
	case "ProductType_ANDROID":
		return ProductType_ANDROID, nil
	case "ProductType_WEB_PAGE":
		return ProductType_WEB_PAGE, nil
	case "ProductType_IOS_JB":
		return ProductType_IOS_JB, nil
	}
	return ProductType(math.MinInt32 - 1), fmt.Errorf("not a valid ProductType string")
}

//渠道类型
type ChannelType int64

const (
	ChannelType_UNKNOWN   ChannelType = 0
	ChannelType_DSP       ChannelType = 1
	ChannelType_AGENT     ChannelType = 2
	ChannelType_OFFERWALL ChannelType = 3
)

func (p ChannelType) String() string {
	switch p {
	case ChannelType_UNKNOWN:
		return "ChannelType_UNKNOWN"
	case ChannelType_DSP:
		return "ChannelType_DSP"
	case ChannelType_AGENT:
		return "ChannelType_AGENT"
	case ChannelType_OFFERWALL:
		return "ChannelType_OFFERWALL"
	}
	return "<UNSET>"
}

func ChannelTypeFromString(s string) (ChannelType, error) {
	switch s {
	case "ChannelType_UNKNOWN":
		return ChannelType_UNKNOWN, nil
	case "ChannelType_DSP":
		return ChannelType_DSP, nil
	case "ChannelType_AGENT":
		return ChannelType_AGENT, nil
	case "ChannelType_OFFERWALL":
		return ChannelType_OFFERWALL, nil
	}
	return ChannelType(math.MinInt32 - 1), fmt.Errorf("not a valid ChannelType string")
}

//激活反馈方式
type PostbackType int64

const (
	PostbackType_UNKNOWN     PostbackType = 0
	PostbackType_TRACKINGSDK PostbackType = 1
	PostbackType_SERVERAPI   PostbackType = 2
	PostbackType_MANUAL      PostbackType = 3
	PostbackType_UNCHECK     PostbackType = 4
)

func (p PostbackType) String() string {
	switch p {
	case PostbackType_UNKNOWN:
		return "PostbackType_UNKNOWN"
	case PostbackType_TRACKINGSDK:
		return "PostbackType_TRACKINGSDK"
	case PostbackType_SERVERAPI:
		return "PostbackType_SERVERAPI"
	case PostbackType_MANUAL:
		return "PostbackType_MANUAL"
	case PostbackType_UNCHECK:
		return "PostbackType_UNCHECK"
	}
	return "<UNSET>"
}

func PostbackTypeFromString(s string) (PostbackType, error) {
	switch s {
	case "PostbackType_UNKNOWN":
		return PostbackType_UNKNOWN, nil
	case "PostbackType_TRACKINGSDK":
		return PostbackType_TRACKINGSDK, nil
	case "PostbackType_SERVERAPI":
		return PostbackType_SERVERAPI, nil
	case "PostbackType_MANUAL":
		return PostbackType_MANUAL, nil
	case "PostbackType_UNCHECK":
		return PostbackType_UNCHECK, nil
	}
	return PostbackType(math.MinInt32 - 1), fmt.Errorf("not a valid PostbackType string")
}

//激活事件类型
type ActType int64

const (
	ActType_UNKNOWN       ActType = 0
	ActType_APP           ActType = 2
	ActType_ACTIVE        ActType = 3
	ActType_PAYMENT       ActType = 4
	ActType_OPEN          ActType = 5
	ActType_REGISTER      ActType = 11
	ActType_FIRST_LOGIN   ActType = 12
	ActType_ROLE_CREATION ActType = 13
	ActType_LOGIN         ActType = 16
	ActType_FIRST_PLAY    ActType = 17
)

func (p ActType) String() string {
	switch p {
	case ActType_UNKNOWN:
		return "ActType_UNKNOWN"
	case ActType_APP:
		return "ActType_APP"
	case ActType_ACTIVE:
		return "ActType_ACTIVE"
	case ActType_PAYMENT:
		return "ActType_PAYMENT"
	case ActType_OPEN:
		return "ActType_OPEN"
	case ActType_REGISTER:
		return "ActType_REGISTER"
	case ActType_FIRST_LOGIN:
		return "ActType_FIRST_LOGIN"
	case ActType_ROLE_CREATION:
		return "ActType_ROLE_CREATION"
	case ActType_LOGIN:
		return "ActType_LOGIN"
	case ActType_FIRST_PLAY:
		return "ActType_FIRST_PLAY"
	}
	return "<UNSET>"
}

func ActTypeFromString(s string) (ActType, error) {
	switch s {
	case "ActType_UNKNOWN":
		return ActType_UNKNOWN, nil
	case "ActType_APP":
		return ActType_APP, nil
	case "ActType_ACTIVE":
		return ActType_ACTIVE, nil
	case "ActType_PAYMENT":
		return ActType_PAYMENT, nil
	case "ActType_OPEN":
		return ActType_OPEN, nil
	case "ActType_REGISTER":
		return ActType_REGISTER, nil
	case "ActType_FIRST_LOGIN":
		return ActType_FIRST_LOGIN, nil
	case "ActType_ROLE_CREATION":
		return ActType_ROLE_CREATION, nil
	case "ActType_LOGIN":
		return ActType_LOGIN, nil
	case "ActType_FIRST_PLAY":
		return ActType_FIRST_PLAY, nil
	}
	return ActType(math.MinInt32 - 1), fmt.Errorf("not a valid ActType string")
}

//公司类型
type CompanyType int64

const (
	CompanyType_UNKNOWN   CompanyType = 0
	CompanyType_IMMEDIATE CompanyType = 1
	CompanyType_AGENT     CompanyType = 2
)

func (p CompanyType) String() string {
	switch p {
	case CompanyType_UNKNOWN:
		return "CompanyType_UNKNOWN"
	case CompanyType_IMMEDIATE:
		return "CompanyType_IMMEDIATE"
	case CompanyType_AGENT:
		return "CompanyType_AGENT"
	}
	return "<UNSET>"
}

func CompanyTypeFromString(s string) (CompanyType, error) {
	switch s {
	case "CompanyType_UNKNOWN":
		return CompanyType_UNKNOWN, nil
	case "CompanyType_IMMEDIATE":
		return CompanyType_IMMEDIATE, nil
	case "CompanyType_AGENT":
		return CompanyType_AGENT, nil
	}
	return CompanyType(math.MinInt32 - 1), fmt.Errorf("not a valid CompanyType string")
}

type PromotionParam struct {
	Name      string      `thrift:"name,1" json:"name"`
	TypeA1    ProductType `thrift:"type,2" json:"type"`
	Creator   int32       `thrift:"creator,3" json:"creator"`
	CompanyId []int32     `thrift:"companyId,4" json:"companyId"`
	AgentId   int32       `thrift:"agentId,5" json:"agentId"`
	Id        int32       `thrift:"id,6" json:"id"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Offset int32 `thrift:"offset,20" json:"offset"`
	Limit  int32 `thrift:"limit,21" json:"limit"`
}

func NewPromotionParam() *PromotionParam {
	return &PromotionParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PromotionParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PromotionParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PromotionParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PromotionParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = ProductType(v)
	}
	return nil
}

func (p *PromotionParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *PromotionParam) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CompanyId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.CompanyId = append(p.CompanyId, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PromotionParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AgentId = v
	}
	return nil
}

func (p *PromotionParam) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PromotionParam) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *PromotionParam) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *PromotionParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PromotionParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PromotionParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *PromotionParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *PromotionParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creator: %s", p, err)
	}
	return err
}

func (p *PromotionParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.CompanyId != nil {
		if err := oprot.WriteFieldBegin("companyId", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:companyId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CompanyId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CompanyId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:companyId: %s", p, err)
		}
	}
	return err
}

func (p *PromotionParam) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:agentId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentId)); err != nil {
		return fmt.Errorf("%T.agentId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:agentId: %s", p, err)
	}
	return err
}

func (p *PromotionParam) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:id: %s", p, err)
	}
	return err
}

func (p *PromotionParam) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:offset: %s", p, err)
	}
	return err
}

func (p *PromotionParam) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:limit: %s", p, err)
	}
	return err
}

func (p *PromotionParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromotionParam(%+v)", *p)
}

type PromotionPropertyParam struct {
	PromotionId int32            `thrift:"promotionId,1" json:"promotionId"`
	Name        string           `thrift:"name,2" json:"name"`
	TypeA1      ProductType      `thrift:"type,3" json:"type"`
	Category    enums.AdCategory `thrift:"category,4" json:"category"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Offset int32 `thrift:"offset,20" json:"offset"`
	Limit  int32 `thrift:"limit,21" json:"limit"`
}

func NewPromotionPropertyParam() *PromotionPropertyParam {
	return &PromotionPropertyParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PromotionPropertyParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PromotionPropertyParam) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *PromotionPropertyParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PromotionPropertyParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *PromotionPropertyParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PromotionPropertyParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = ProductType(v)
	}
	return nil
}

func (p *PromotionPropertyParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Category = enums.AdCategory(v)
	}
	return nil
}

func (p *PromotionPropertyParam) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *PromotionPropertyParam) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *PromotionPropertyParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PromotionPropertyParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PromotionPropertyParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:promotionId: %s", p, err)
	}
	return err
}

func (p *PromotionPropertyParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *PromotionPropertyParam) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *PromotionPropertyParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:category: %s", p, err)
		}
	}
	return err
}

func (p *PromotionPropertyParam) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:offset: %s", p, err)
	}
	return err
}

func (p *PromotionPropertyParam) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:limit: %s", p, err)
	}
	return err
}

func (p *PromotionPropertyParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromotionPropertyParam(%+v)", *p)
}

type ChannelParam struct {
	PromotionId int32       `thrift:"promotionId,1" json:"promotionId"`
	CompanyId   int32       `thrift:"companyId,2" json:"companyId"`
	Name        string      `thrift:"name,3" json:"name"`
	TypeA1      ChannelType `thrift:"type,4" json:"type"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Offset int32 `thrift:"offset,20" json:"offset"`
	Limit  int32 `thrift:"limit,21" json:"limit"`
}

func NewChannelParam() *ChannelParam {
	return &ChannelParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ChannelParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *ChannelParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ChannelParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *ChannelParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CompanyId = v
	}
	return nil
}

func (p *ChannelParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ChannelParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = ChannelType(v)
	}
	return nil
}

func (p *ChannelParam) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ChannelParam) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ChannelParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ChannelParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ChannelParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:promotionId: %s", p, err)
	}
	return err
}

func (p *ChannelParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:companyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CompanyId)); err != nil {
		return fmt.Errorf("%T.companyId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:companyId: %s", p, err)
	}
	return err
}

func (p *ChannelParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *ChannelParam) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:type: %s", p, err)
		}
	}
	return err
}

func (p *ChannelParam) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:offset: %s", p, err)
	}
	return err
}

func (p *ChannelParam) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:limit: %s", p, err)
	}
	return err
}

func (p *ChannelParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ChannelParam(%+v)", *p)
}

type TrackingParam struct {
	ChannelId int32  `thrift:"channelId,1" json:"channelId"`
	Name      string `thrift:"name,2" json:"name"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Offset int32 `thrift:"offset,20" json:"offset"`
	Limit  int32 `thrift:"limit,21" json:"limit"`
}

func NewTrackingParam() *TrackingParam {
	return &TrackingParam{}
}

func (p *TrackingParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TrackingParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ChannelId = v
	}
	return nil
}

func (p *TrackingParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *TrackingParam) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *TrackingParam) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *TrackingParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TrackingParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TrackingParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channelId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:channelId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChannelId)); err != nil {
		return fmt.Errorf("%T.channelId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:channelId: %s", p, err)
	}
	return err
}

func (p *TrackingParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *TrackingParam) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:offset: %s", p, err)
	}
	return err
}

func (p *TrackingParam) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:limit: %s", p, err)
	}
	return err
}

func (p *TrackingParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TrackingParam(%+v)", *p)
}

type CompanyParam struct {
	Name    string      `thrift:"name,1" json:"name"`
	TypeA1  CompanyType `thrift:"type,2" json:"type"`
	Creator int32       `thrift:"creator,3" json:"creator"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Offset int32 `thrift:"offset,20" json:"offset"`
	Limit  int32 `thrift:"limit,21" json:"limit"`
}

func NewCompanyParam() *CompanyParam {
	return &CompanyParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CompanyParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *CompanyParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompanyParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CompanyParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = CompanyType(v)
	}
	return nil
}

func (p *CompanyParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *CompanyParam) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *CompanyParam) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *CompanyParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CompanyParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompanyParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *CompanyParam) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *CompanyParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:creator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:creator: %s", p, err)
	}
	return err
}

func (p *CompanyParam) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:offset: %s", p, err)
	}
	return err
}

func (p *CompanyParam) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:limit: %s", p, err)
	}
	return err
}

func (p *CompanyParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompanyParam(%+v)", *p)
}

type PromotionProperty struct {
	Id             int32             `thrift:"id,1" json:"id"`
	Name           string            `thrift:"name,2" json:"name"`
	TypeA1         ProductType       `thrift:"type,3" json:"type"`
	DisplayName    string            `thrift:"displayName,4" json:"displayName"`
	ShortName      string            `thrift:"shortName,5" json:"shortName"`
	Description    string            `thrift:"description,6" json:"description"`
	Category       enums.AdCategory  `thrift:"category,7" json:"category"`
	PkgName        string            `thrift:"pkgName,8" json:"pkgName"`
	PkgSize        int64             `thrift:"pkgSize,9" json:"pkgSize"`
	PkgVersion     string            `thrift:"pkgVersion,10" json:"pkgVersion"`
	PkgVersionCode int32             `thrift:"pkgVersionCode,11" json:"pkgVersionCode"`
	PkgUrl         string            `thrift:"pkgUrl,12" json:"pkgUrl"`
	Logo           int32             `thrift:"logo,13" json:"logo"`
	ItunesId       string            `thrift:"itunesId,14" json:"itunesId"`
	PhoneSnapshot  []int32           `thrift:"phoneSnapshot,15" json:"phoneSnapshot"`
	PadSnapshot    []int32           `thrift:"padSnapshot,16" json:"padSnapshot"`
	GoogleplayUrl  string            `thrift:"googleplayUrl,17" json:"googleplayUrl"`
	Attrs          map[string]string `thrift:"attrs,18" json:"attrs"`
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	PubTime    int32 `thrift:"pubTime,30" json:"pubTime"`
	CreateTime int32 `thrift:"createTime,31" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,32" json:"lastUpdate"`
	Status     int8  `thrift:"status,33" json:"status"`
}

func NewPromotionProperty() *PromotionProperty {
	return &PromotionProperty{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PromotionProperty) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PromotionProperty) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *PromotionProperty) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.LIST {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.MAP {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PromotionProperty) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *PromotionProperty) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *PromotionProperty) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = ProductType(v)
	}
	return nil
}

func (p *PromotionProperty) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DisplayName = v
	}
	return nil
}

func (p *PromotionProperty) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ShortName = v
	}
	return nil
}

func (p *PromotionProperty) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *PromotionProperty) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Category = enums.AdCategory(v)
	}
	return nil
}

func (p *PromotionProperty) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *PromotionProperty) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PkgSize = v
	}
	return nil
}

func (p *PromotionProperty) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PkgVersion = v
	}
	return nil
}

func (p *PromotionProperty) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PkgVersionCode = v
	}
	return nil
}

func (p *PromotionProperty) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PkgUrl = v
	}
	return nil
}

func (p *PromotionProperty) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *PromotionProperty) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ItunesId = v
	}
	return nil
}

func (p *PromotionProperty) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PhoneSnapshot = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = v
		}
		p.PhoneSnapshot = append(p.PhoneSnapshot, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PromotionProperty) readField16(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PadSnapshot = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.PadSnapshot = append(p.PadSnapshot, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PromotionProperty) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.GoogleplayUrl = v
	}
	return nil
}

func (p *PromotionProperty) readField18(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Attrs = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key3 = v
		}
		var _val4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val4 = v
		}
		p.Attrs[_key3] = _val4
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *PromotionProperty) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.PubTime = v
	}
	return nil
}

func (p *PromotionProperty) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *PromotionProperty) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *PromotionProperty) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Status = int8(v)
	}
	return nil
}

func (p *PromotionProperty) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PromotionProperty"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PromotionProperty) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *PromotionProperty) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:displayName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayName)); err != nil {
		return fmt.Errorf("%T.displayName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:displayName: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shortName", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:shortName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShortName)); err != nil {
		return fmt.Errorf("%T.shortName (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:shortName: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:description: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategory() {
		if err := oprot.WriteFieldBegin("category", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:category: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Category)); err != nil {
			return fmt.Errorf("%T.category (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:category: %s", p, err)
		}
	}
	return err
}

func (p *PromotionProperty) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgName", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:pkgName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkgName (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:pkgName: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgSize", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:pkgSize: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PkgSize)); err != nil {
		return fmt.Errorf("%T.pkgSize (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:pkgSize: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgVersion", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:pkgVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgVersion)); err != nil {
		return fmt.Errorf("%T.pkgVersion (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:pkgVersion: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgVersionCode", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:pkgVersionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PkgVersionCode)); err != nil {
		return fmt.Errorf("%T.pkgVersionCode (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:pkgVersionCode: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgUrl", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:pkgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgUrl)); err != nil {
		return fmt.Errorf("%T.pkgUrl (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:pkgUrl: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:logo: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:logo: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesId", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:itunesId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunesId (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:itunesId: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField15(oprot thrift.TProtocol) (err error) {
	if p.PhoneSnapshot != nil {
		if err := oprot.WriteFieldBegin("phoneSnapshot", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:phoneSnapshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PhoneSnapshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PhoneSnapshot {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:phoneSnapshot: %s", p, err)
		}
	}
	return err
}

func (p *PromotionProperty) writeField16(oprot thrift.TProtocol) (err error) {
	if p.PadSnapshot != nil {
		if err := oprot.WriteFieldBegin("padSnapshot", thrift.LIST, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:padSnapshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PadSnapshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PadSnapshot {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:padSnapshot: %s", p, err)
		}
	}
	return err
}

func (p *PromotionProperty) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("googleplayUrl", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:googleplayUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.GoogleplayUrl)); err != nil {
		return fmt.Errorf("%T.googleplayUrl (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:googleplayUrl: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField18(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.MAP, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:attrs: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Attrs {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:attrs: %s", p, err)
		}
	}
	return err
}

func (p *PromotionProperty) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubTime", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:pubTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubTime)); err != nil {
		return fmt.Errorf("%T.pubTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:pubTime: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:createTime: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:lastUpdate: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BYTE, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:status: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Status)); err != nil {
		return fmt.Errorf("%T.status (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:status: %s", p, err)
	}
	return err
}

func (p *PromotionProperty) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PromotionProperty(%+v)", *p)
}

type Promotion struct {
	Name            string           `thrift:"name,1" json:"name"`
	Creator         int32            `thrift:"creator,2" json:"creator"`
	TypeA1          ProductType      `thrift:"type,3" json:"type"`
	HomepageUrl     string           `thrift:"homepageUrl,4" json:"homepageUrl"`
	EndorsementFile map[string]int32 `thrift:"endorsementFile,5" json:"endorsementFile"`
	CompanyId       int32            `thrift:"companyId,6" json:"companyId"`
	AgentId         []int32          `thrift:"agentId,7" json:"agentId"`
	Id              int32            `thrift:"id,8" json:"id"`
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int32              `thrift:"createTime,20" json:"createTime"`
	LastUpdate int32              `thrift:"lastUpdate,21" json:"lastUpdate"`
	Status     int8               `thrift:"status,22" json:"status"`
	Pproperty  *PromotionProperty `thrift:"pproperty,23" json:"pproperty"`
}

func NewPromotion() *Promotion {
	return &Promotion{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Promotion) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *Promotion) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Promotion) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Promotion) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *Promotion) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = ProductType(v)
	}
	return nil
}

func (p *Promotion) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.HomepageUrl = v
	}
	return nil
}

func (p *Promotion) readField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.EndorsementFile = make(map[string]int32, size)
	for i := 0; i < size; i++ {
		var _key5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key5 = v
		}
		var _val6 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val6 = v
		}
		p.EndorsementFile[_key5] = _val6
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *Promotion) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CompanyId = v
	}
	return nil
}

func (p *Promotion) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AgentId = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.AgentId = append(p.AgentId, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Promotion) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Promotion) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Promotion) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Promotion) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Status = int8(v)
	}
	return nil
}

func (p *Promotion) readField23(iprot thrift.TProtocol) error {
	p.Pproperty = NewPromotionProperty()
	if err := p.Pproperty.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pproperty)
	}
	return nil
}

func (p *Promotion) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Promotion"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Promotion) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:creator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:creator: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *Promotion) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("homepageUrl", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:homepageUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HomepageUrl)); err != nil {
		return fmt.Errorf("%T.homepageUrl (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:homepageUrl: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField5(oprot thrift.TProtocol) (err error) {
	if p.EndorsementFile != nil {
		if err := oprot.WriteFieldBegin("endorsementFile", thrift.MAP, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:endorsementFile: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.EndorsementFile)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.EndorsementFile {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:endorsementFile: %s", p, err)
		}
	}
	return err
}

func (p *Promotion) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:companyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CompanyId)); err != nil {
		return fmt.Errorf("%T.companyId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:companyId: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField7(oprot thrift.TProtocol) (err error) {
	if p.AgentId != nil {
		if err := oprot.WriteFieldBegin("agentId", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:agentId: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AgentId)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AgentId {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:agentId: %s", p, err)
		}
	}
	return err
}

func (p *Promotion) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:id: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BYTE, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:status: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Status)); err != nil {
		return fmt.Errorf("%T.status (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:status: %s", p, err)
	}
	return err
}

func (p *Promotion) writeField23(oprot thrift.TProtocol) (err error) {
	if p.Pproperty != nil {
		if err := oprot.WriteFieldBegin("pproperty", thrift.STRUCT, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:pproperty: %s", p, err)
		}
		if err := p.Pproperty.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pproperty)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:pproperty: %s", p, err)
		}
	}
	return err
}

func (p *Promotion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Promotion(%+v)", *p)
}

type Channel struct {
	PromotionId          int32        `thrift:"promotionId,1" json:"promotionId"`
	CompanyId            int32        `thrift:"companyId,2" json:"companyId"`
	Name                 string       `thrift:"name,3" json:"name"`
	TypeA1               ChannelType  `thrift:"type,4" json:"type"`
	PostbackType         PostbackType `thrift:"postbackType,5" json:"postbackType"`
	TrackingDeviceidType int8         `thrift:"trackingDeviceidType,6" json:"trackingDeviceidType"`
	ActType              ActType      `thrift:"actType,7" json:"actType"`
	Appkey               string       `thrift:"appkey,8" json:"appkey"`
	AppPrivateKey        string       `thrift:"appPrivateKey,9" json:"appPrivateKey"`
	PkgUrl               string       `thrift:"pkgUrl,10" json:"pkgUrl"`
	Logo                 int32        `thrift:"logo,11" json:"logo"`
	PhoneSnapshot        []int32      `thrift:"phoneSnapshot,12" json:"phoneSnapshot"`
	PadSnapshot          []int32      `thrift:"padSnapshot,13" json:"padSnapshot"`
	Creator              int32        `thrift:"creator,14" json:"creator"`
	TrackingCount        int32        `thrift:"trackingCount,15" json:"trackingCount"`
	Id                   int32        `thrift:"id,16" json:"id"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int32 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,31" json:"lastUpdate"`
	Status     int8  `thrift:"status,32" json:"status"`
}

func NewChannel() *Channel {
	return &Channel{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		PostbackType: math.MinInt32 - 1, // unset sentinal value

		ActType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Channel) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *Channel) IsSetPostbackType() bool {
	return int64(p.PostbackType) != math.MinInt32-1
}

func (p *Channel) IsSetActType() bool {
	return int64(p.ActType) != math.MinInt32-1
}

func (p *Channel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Channel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PromotionId = v
	}
	return nil
}

func (p *Channel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CompanyId = v
	}
	return nil
}

func (p *Channel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Channel) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = ChannelType(v)
	}
	return nil
}

func (p *Channel) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PostbackType = PostbackType(v)
	}
	return nil
}

func (p *Channel) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TrackingDeviceidType = int8(v)
	}
	return nil
}

func (p *Channel) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ActType = ActType(v)
	}
	return nil
}

func (p *Channel) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Appkey = v
	}
	return nil
}

func (p *Channel) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AppPrivateKey = v
	}
	return nil
}

func (p *Channel) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PkgUrl = v
	}
	return nil
}

func (p *Channel) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *Channel) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PhoneSnapshot = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.PhoneSnapshot = append(p.PhoneSnapshot, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Channel) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PadSnapshot = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.PadSnapshot = append(p.PadSnapshot, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Channel) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *Channel) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.TrackingCount = v
	}
	return nil
}

func (p *Channel) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Channel) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Channel) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Channel) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Status = int8(v)
	}
	return nil
}

func (p *Channel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Channel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Channel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("promotionId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:promotionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PromotionId)); err != nil {
		return fmt.Errorf("%T.promotionId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:promotionId: %s", p, err)
	}
	return err
}

func (p *Channel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("companyId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:companyId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CompanyId)); err != nil {
		return fmt.Errorf("%T.companyId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:companyId: %s", p, err)
	}
	return err
}

func (p *Channel) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *Channel) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:type: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPostbackType() {
		if err := oprot.WriteFieldBegin("postbackType", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:postbackType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PostbackType)); err != nil {
			return fmt.Errorf("%T.postbackType (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:postbackType: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingDeviceidType", thrift.BYTE, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:trackingDeviceidType: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.TrackingDeviceidType)); err != nil {
		return fmt.Errorf("%T.trackingDeviceidType (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:trackingDeviceidType: %s", p, err)
	}
	return err
}

func (p *Channel) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetActType() {
		if err := oprot.WriteFieldBegin("actType", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:actType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ActType)); err != nil {
			return fmt.Errorf("%T.actType (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:actType: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appkey", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:appkey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appkey)); err != nil {
		return fmt.Errorf("%T.appkey (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:appkey: %s", p, err)
	}
	return err
}

func (p *Channel) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appPrivateKey", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:appPrivateKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppPrivateKey)); err != nil {
		return fmt.Errorf("%T.appPrivateKey (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:appPrivateKey: %s", p, err)
	}
	return err
}

func (p *Channel) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgUrl", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:pkgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgUrl)); err != nil {
		return fmt.Errorf("%T.pkgUrl (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:pkgUrl: %s", p, err)
	}
	return err
}

func (p *Channel) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:logo: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:logo: %s", p, err)
	}
	return err
}

func (p *Channel) writeField12(oprot thrift.TProtocol) (err error) {
	if p.PhoneSnapshot != nil {
		if err := oprot.WriteFieldBegin("phoneSnapshot", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:phoneSnapshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PhoneSnapshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PhoneSnapshot {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:phoneSnapshot: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField13(oprot thrift.TProtocol) (err error) {
	if p.PadSnapshot != nil {
		if err := oprot.WriteFieldBegin("padSnapshot", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:padSnapshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PadSnapshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PadSnapshot {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:padSnapshot: %s", p, err)
		}
	}
	return err
}

func (p *Channel) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:creator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:creator: %s", p, err)
	}
	return err
}

func (p *Channel) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingCount", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:trackingCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TrackingCount)); err != nil {
		return fmt.Errorf("%T.trackingCount (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:trackingCount: %s", p, err)
	}
	return err
}

func (p *Channel) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:id: %s", p, err)
	}
	return err
}

func (p *Channel) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *Channel) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Channel) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BYTE, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:status: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Status)); err != nil {
		return fmt.Errorf("%T.status (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:status: %s", p, err)
	}
	return err
}

func (p *Channel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Channel(%+v)", *p)
}

type Tracking struct {
	Id                 int32                    `thrift:"id,1" json:"id"`
	Name               string                   `thrift:"name,2" json:"name"`
	ClickUrl           string                   `thrift:"clickUrl,3" json:"clickUrl"`
	ClickSenderUrl     string                   `thrift:"clickSenderUrl,4" json:"clickSenderUrl"`
	RawUrl             string                   `thrift:"rawUrl,5" json:"rawUrl"`
	ThirdPartyTracking enums.ThirdPartyTracking `thrift:"thirdPartyTracking,6" json:"thirdPartyTracking"`
	PkgUrl             string                   `thrift:"pkgUrl,7" json:"pkgUrl"`
	Attrs              map[string]string        `thrift:"attrs,8" json:"attrs"`
	ChnId              int32                    `thrift:"chnId,9" json:"chnId"`
	ImpTrackingUrl     string                   `thrift:"impTrackingUrl,10" json:"impTrackingUrl"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int32 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,31" json:"lastUpdate"`
	Status     int8  `thrift:"status,32" json:"status"`
}

func NewTracking() *Tracking {
	return &Tracking{
		ThirdPartyTracking: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Tracking) IsSetThirdPartyTracking() bool {
	return int64(p.ThirdPartyTracking) != math.MinInt32-1
}

func (p *Tracking) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Tracking) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Tracking) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Tracking) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ClickUrl = v
	}
	return nil
}

func (p *Tracking) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ClickSenderUrl = v
	}
	return nil
}

func (p *Tracking) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RawUrl = v
	}
	return nil
}

func (p *Tracking) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ThirdPartyTracking = enums.ThirdPartyTracking(v)
	}
	return nil
}

func (p *Tracking) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PkgUrl = v
	}
	return nil
}

func (p *Tracking) readField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Attrs = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key10 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key10 = v
		}
		var _val11 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val11 = v
		}
		p.Attrs[_key10] = _val11
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *Tracking) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *Tracking) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ImpTrackingUrl = v
	}
	return nil
}

func (p *Tracking) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Tracking) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Tracking) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Status = int8(v)
	}
	return nil
}

func (p *Tracking) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Tracking"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Tracking) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickUrl", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:clickUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickUrl)); err != nil {
		return fmt.Errorf("%T.clickUrl (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:clickUrl: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickSenderUrl", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:clickSenderUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickSenderUrl)); err != nil {
		return fmt.Errorf("%T.clickSenderUrl (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:clickSenderUrl: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rawUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:rawUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RawUrl)); err != nil {
		return fmt.Errorf("%T.rawUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:rawUrl: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetThirdPartyTracking() {
		if err := oprot.WriteFieldBegin("thirdPartyTracking", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:thirdPartyTracking: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ThirdPartyTracking)); err != nil {
			return fmt.Errorf("%T.thirdPartyTracking (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:thirdPartyTracking: %s", p, err)
		}
	}
	return err
}

func (p *Tracking) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgUrl", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:pkgUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgUrl)); err != nil {
		return fmt.Errorf("%T.pkgUrl (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:pkgUrl: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField8(oprot thrift.TProtocol) (err error) {
	if p.Attrs != nil {
		if err := oprot.WriteFieldBegin("attrs", thrift.MAP, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:attrs: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Attrs)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Attrs {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:attrs: %s", p, err)
		}
	}
	return err
}

func (p *Tracking) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:chnId: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impTrackingUrl", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:impTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpTrackingUrl)); err != nil {
		return fmt.Errorf("%T.impTrackingUrl (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:impTrackingUrl: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Tracking) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BYTE, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:status: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Status)); err != nil {
		return fmt.Errorf("%T.status (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:status: %s", p, err)
	}
	return err
}

func (p *Tracking) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Tracking(%+v)", *p)
}

type Company struct {
	Name              string           `thrift:"name,1" json:"name"`
	TypeA1            CompanyType      `thrift:"type,2" json:"type"`
	OfficialWebsite   string           `thrift:"officialWebsite,3" json:"officialWebsite"`
	Certificate       int32            `thrift:"certificate,4" json:"certificate"`
	OccNumber         string           `thrift:"occNumber,5" json:"occNumber"`
	OccUrl            int32            `thrift:"occUrl,6" json:"occUrl"`
	AuthorizationFile int32            `thrift:"authorizationFile,7" json:"authorizationFile"`
	OtherFiles        map[string]int32 `thrift:"otherFiles,8" json:"otherFiles"`
	Creator           int32            `thrift:"creator,9" json:"creator"`
	Id                int32            `thrift:"id,10" json:"id"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int32 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int32 `thrift:"lastUpdate,31" json:"lastUpdate"`
	Status     int8  `thrift:"status,32" json:"status"`
}

func NewCompany() *Company {
	return &Company{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Company) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *Company) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Company) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Company) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = CompanyType(v)
	}
	return nil
}

func (p *Company) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OfficialWebsite = v
	}
	return nil
}

func (p *Company) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Certificate = v
	}
	return nil
}

func (p *Company) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.OccNumber = v
	}
	return nil
}

func (p *Company) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OccUrl = v
	}
	return nil
}

func (p *Company) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AuthorizationFile = v
	}
	return nil
}

func (p *Company) readField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.OtherFiles = make(map[string]int32, size)
	for i := 0; i < size; i++ {
		var _key12 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key12 = v
		}
		var _val13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val13 = v
		}
		p.OtherFiles[_key12] = _val13
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *Company) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *Company) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Company) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Company) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Company) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Status = int8(v)
	}
	return nil
}

func (p *Company) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Company"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Company) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *Company) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *Company) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("officialWebsite", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:officialWebsite: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OfficialWebsite)); err != nil {
		return fmt.Errorf("%T.officialWebsite (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:officialWebsite: %s", p, err)
	}
	return err
}

func (p *Company) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("certificate", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:certificate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Certificate)); err != nil {
		return fmt.Errorf("%T.certificate (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:certificate: %s", p, err)
	}
	return err
}

func (p *Company) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("occNumber", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:occNumber: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OccNumber)); err != nil {
		return fmt.Errorf("%T.occNumber (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:occNumber: %s", p, err)
	}
	return err
}

func (p *Company) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("occUrl", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:occUrl: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OccUrl)); err != nil {
		return fmt.Errorf("%T.occUrl (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:occUrl: %s", p, err)
	}
	return err
}

func (p *Company) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("authorizationFile", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:authorizationFile: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AuthorizationFile)); err != nil {
		return fmt.Errorf("%T.authorizationFile (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:authorizationFile: %s", p, err)
	}
	return err
}

func (p *Company) writeField8(oprot thrift.TProtocol) (err error) {
	if p.OtherFiles != nil {
		if err := oprot.WriteFieldBegin("otherFiles", thrift.MAP, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:otherFiles: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.OtherFiles)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.OtherFiles {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:otherFiles: %s", p, err)
		}
	}
	return err
}

func (p *Company) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:creator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:creator: %s", p, err)
	}
	return err
}

func (p *Company) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:id: %s", p, err)
	}
	return err
}

func (p *Company) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *Company) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Company) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.BYTE, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:status: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.Status)); err != nil {
		return fmt.Errorf("%T.status (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:status: %s", p, err)
	}
	return err
}

func (p *Company) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Company(%+v)", *p)
}
