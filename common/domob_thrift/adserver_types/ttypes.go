// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package adserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/tag_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = tag_types.GoUnusedProtection__
var GoUnusedProtection__ int

type ReqType int64

const (
	ReqType_REQ_AD    ReqType = 1
	ReqType_REQ_DET   ReqType = 2
	ReqType_REQ_OTHER ReqType = 100
)

func (p ReqType) String() string {
	switch p {
	case ReqType_REQ_AD:
		return "ReqType_REQ_AD"
	case ReqType_REQ_DET:
		return "ReqType_REQ_DET"
	case ReqType_REQ_OTHER:
		return "ReqType_REQ_OTHER"
	}
	return "<UNSET>"
}

func ReqTypeFromString(s string) (ReqType, error) {
	switch s {
	case "ReqType_REQ_AD":
		return ReqType_REQ_AD, nil
	case "ReqType_REQ_DET":
		return ReqType_REQ_DET, nil
	case "ReqType_REQ_OTHER":
		return ReqType_REQ_OTHER, nil
	}
	return ReqType(math.MinInt32 - 1), fmt.Errorf("not a valid ReqType string")
}

type ResType int64

const (
	ResType_RES_AD    ResType = 1
	ResType_RES_DET   ResType = 2
	ResType_RES_OTHER ResType = 100
)

func (p ResType) String() string {
	switch p {
	case ResType_RES_AD:
		return "ResType_RES_AD"
	case ResType_RES_DET:
		return "ResType_RES_DET"
	case ResType_RES_OTHER:
		return "ResType_RES_OTHER"
	}
	return "<UNSET>"
}

func ResTypeFromString(s string) (ResType, error) {
	switch s {
	case "ResType_RES_AD":
		return ResType_RES_AD, nil
	case "ResType_RES_DET":
		return ResType_RES_DET, nil
	case "ResType_RES_OTHER":
		return ResType_RES_OTHER, nil
	}
	return ResType(math.MinInt32 - 1), fmt.Errorf("not a valid ResType string")
}

type ResStatus int64

const (
	ResStatus_STATUS_AD    ResStatus = 1
	ResStatus_STATUS_EMPTY ResStatus = 2
	ResStatus_STATUS_ERROR ResStatus = 3
)

func (p ResStatus) String() string {
	switch p {
	case ResStatus_STATUS_AD:
		return "ResStatus_STATUS_AD"
	case ResStatus_STATUS_EMPTY:
		return "ResStatus_STATUS_EMPTY"
	case ResStatus_STATUS_ERROR:
		return "ResStatus_STATUS_ERROR"
	}
	return "<UNSET>"
}

func ResStatusFromString(s string) (ResStatus, error) {
	switch s {
	case "ResStatus_STATUS_AD":
		return ResStatus_STATUS_AD, nil
	case "ResStatus_STATUS_EMPTY":
		return ResStatus_STATUS_EMPTY, nil
	case "ResStatus_STATUS_ERROR":
		return ResStatus_STATUS_ERROR, nil
	}
	return ResStatus(math.MinInt32 - 1), fmt.Errorf("not a valid ResStatus string")
}

type SearchErrorCode int64

const (
	SearchErrorCode_ERROR_INVALID_REQ_TYPE SearchErrorCode = 1
	SearchErrorCode_ERROR_ADSERVER_ERROR   SearchErrorCode = 2
)

func (p SearchErrorCode) String() string {
	switch p {
	case SearchErrorCode_ERROR_INVALID_REQ_TYPE:
		return "SearchErrorCode_ERROR_INVALID_REQ_TYPE"
	case SearchErrorCode_ERROR_ADSERVER_ERROR:
		return "SearchErrorCode_ERROR_ADSERVER_ERROR"
	}
	return "<UNSET>"
}

func SearchErrorCodeFromString(s string) (SearchErrorCode, error) {
	switch s {
	case "SearchErrorCode_ERROR_INVALID_REQ_TYPE":
		return SearchErrorCode_ERROR_INVALID_REQ_TYPE, nil
	case "SearchErrorCode_ERROR_ADSERVER_ERROR":
		return SearchErrorCode_ERROR_ADSERVER_ERROR, nil
	}
	return SearchErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid SearchErrorCode string")
}

type UidInt common.UidInt

type AdPlanIdInt common.IdInt

type AdStrategyIdInt common.IdInt

type AdCreativeIdInt common.IdInt

type ImgIdInt common.IdInt

type TimeInt common.TimeInt

type LargeIdInt common.LargeIdInt

type IpInt common.IpInt

type MediaIdInt common.IdInt

type Amount common.Amount

type GenderCode common.GenderCode

type RegionCode common.RegionCode

type AgeCode common.AgeCode

type CarrierCode common.CarrierCode

type DeviceCode common.DeviceCode

type OSCode common.OSCode

type BrowserCode common.BrowserCode

type AccessTypeCode common.AccessTypeCode

type SDKTypeCode common.SDKTypeCode

type LanguageType common.LanguageType

type ADResponseType common.ADResponseType

type EncodingCode common.EncodingCode

type AdPlacementType common.AdPlacementType

type LandingDirection common.LandingDirection

type AdCreativeType common.AdCreativeType

type AdCreativeIconType common.AdCreativeIconType

type TemplateSizeCode common.TemplateSizeCode

type TemplateSizeCodeInt common.TemplateSizeCodeInt

type RefreshIntervalInt int32

type MediaTestModeSetting common.MediaTestModeSetting

type MediaType common.MediaType

type CostType common.CostType

type AdActionType common.AdActionType

type SDKUrlOpenType common.SDKUrlOpenType

type ImageType common.ImageType

type CapabilityType common.CapabilityType

type SDKProtocolVersion common.SDKProtocolVersion

type RegionCityInt int32

type ResIdInt int32

type HtmlTemplateCodeInt int32

type AdRenderType common.AdRenderType

type DemoTagging *tag_types.DemoTagging

type ImpDecision *tag_types.ImpDecision

type DemoTagIdInt tag_types.DemoTagIdInt

type JailBreakCode common.JailBreakCode

type WaterMarkPosition common.WaterMarkPosition

type ResourceGroup *common.ResourceGroup

type RichMediaCodeInt int32

type CloseButtonPosition common.CloseButtonPosition

type SponsorType common.SponsorType

type AdCategory common.AdCategory

type AdStrategyType common.AdStrategyType

type Container *common.Container

type Channel *common.Channel

type FreqInfo *common.FreqInfo

type AdCreativeTemplateType common.AdCreativeTemplateType

type AdCreativeTemplateSpecIdInt int32

type POIInfo struct {
	Id        int32   `thrift:"id,1" json:"id"`
	Latitude  float64 `thrift:"latitude,2" json:"latitude"`
	Longitude float64 `thrift:"longitude,3" json:"longitude"`
	Distance  float64 `thrift:"distance,4" json:"distance"`
	PoiInfo   string  `thrift:"poi_info,5" json:"poi_info"`
}

func NewPOIInfo() *POIInfo {
	return &POIInfo{}
}

func (p *POIInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *POIInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *POIInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *POIInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *POIInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Distance = v
	}
	return nil
}

func (p *POIInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PoiInfo = v
	}
	return nil
}

func (p *POIInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("POIInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *POIInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *POIInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:latitude: %s", p, err)
	}
	return err
}

func (p *POIInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:longitude: %s", p, err)
	}
	return err
}

func (p *POIInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("distance", thrift.DOUBLE, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:distance: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Distance)); err != nil {
		return fmt.Errorf("%T.distance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:distance: %s", p, err)
	}
	return err
}

func (p *POIInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("poi_info", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:poi_info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PoiInfo)); err != nil {
		return fmt.Errorf("%T.poi_info (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:poi_info: %s", p, err)
	}
	return err
}

func (p *POIInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("POIInfo(%+v)", *p)
}

type AdSummary struct {
	AdId             int32  `thrift:"adId,1" json:"adId"`
	AdPackage        string `thrift:"adPackage,2" json:"adPackage"`
	AdPackageVersion string `thrift:"adPackageVersion,3" json:"adPackageVersion"`
	DetectPackage    string `thrift:"detectPackage,4" json:"detectPackage"`
	ExtendType       int32  `thrift:"extendType,5" json:"extendType"`
}

func NewAdSummary() *AdSummary {
	return &AdSummary{}
}

func (p *AdSummary) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdSummary) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdId = v
	}
	return nil
}

func (p *AdSummary) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AdPackage = v
	}
	return nil
}

func (p *AdSummary) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AdPackageVersion = v
	}
	return nil
}

func (p *AdSummary) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DetectPackage = v
	}
	return nil
}

func (p *AdSummary) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExtendType = v
	}
	return nil
}

func (p *AdSummary) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdSummary"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdSummary) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:adId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdId)); err != nil {
		return fmt.Errorf("%T.adId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:adId: %s", p, err)
	}
	return err
}

func (p *AdSummary) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPackage", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:adPackage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdPackage)); err != nil {
		return fmt.Errorf("%T.adPackage (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:adPackage: %s", p, err)
	}
	return err
}

func (p *AdSummary) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adPackageVersion", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:adPackageVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdPackageVersion)); err != nil {
		return fmt.Errorf("%T.adPackageVersion (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:adPackageVersion: %s", p, err)
	}
	return err
}

func (p *AdSummary) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detectPackage", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:detectPackage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DetectPackage)); err != nil {
		return fmt.Errorf("%T.detectPackage (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:detectPackage: %s", p, err)
	}
	return err
}

func (p *AdSummary) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extendType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:extendType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExtendType)); err != nil {
		return fmt.Errorf("%T.extendType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:extendType: %s", p, err)
	}
	return err
}

func (p *AdSummary) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdSummary(%+v)", *p)
}

type AdContainer struct {
	ContainerId      int32             `thrift:"containerId,1" json:"containerId"`
	ContainerSetting map[string]string `thrift:"containerSetting,2" json:"containerSetting"`
	AdSummary        []*AdSummary      `thrift:"adSummary,3" json:"adSummary"`
	ExtAdSummary     []*AdSummary      `thrift:"extAdSummary,4" json:"extAdSummary"`
}

func NewAdContainer() *AdContainer {
	return &AdContainer{}
}

func (p *AdContainer) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdContainer) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ContainerId = v
	}
	return nil
}

func (p *AdContainer) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ContainerSetting = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.ContainerSetting[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdContainer) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdSummary = make([]*AdSummary, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewAdSummary()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.AdSummary = append(p.AdSummary, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdContainer) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExtAdSummary = make([]*AdSummary, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewAdSummary()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.ExtAdSummary = append(p.ExtAdSummary, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdContainer) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdContainer"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdContainer) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("containerId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:containerId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ContainerId)); err != nil {
		return fmt.Errorf("%T.containerId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:containerId: %s", p, err)
	}
	return err
}

func (p *AdContainer) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ContainerSetting != nil {
		if err := oprot.WriteFieldBegin("containerSetting", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:containerSetting: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ContainerSetting)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ContainerSetting {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:containerSetting: %s", p, err)
		}
	}
	return err
}

func (p *AdContainer) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AdSummary != nil {
		if err := oprot.WriteFieldBegin("adSummary", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:adSummary: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdSummary)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdSummary {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:adSummary: %s", p, err)
		}
	}
	return err
}

func (p *AdContainer) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ExtAdSummary != nil {
		if err := oprot.WriteFieldBegin("extAdSummary", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:extAdSummary: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ExtAdSummary)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExtAdSummary {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:extAdSummary: %s", p, err)
		}
	}
	return err
}

func (p *AdContainer) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdContainer(%+v)", *p)
}

type UiAdReq struct {
	// unused field # 1
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	So               int32          `thrift:"so,15" json:"so"`
	Sw               int32          `thrift:"sw,16" json:"sw"`
	Sh               int32          `thrift:"sh,17" json:"sh"`
	Sd               float64        `thrift:"sd,18" json:"sd"`
	ActionCapability []AdActionType `thrift:"action_capability,19" json:"action_capability"`
	Keyword          string         `thrift:"keyword,20" json:"keyword"`
	Query            string         `thrift:"query,21" json:"query"`
	TestMode         bool           `thrift:"test_mode,22" json:"test_mode"`
	TestAction       AdActionType   `thrift:"test_action,23" json:"test_action"`
	CoordTime        TimeInt        `thrift:"coord_time,24" json:"coord_time"`
	// unused field # 25
	PostalCode int32          `thrift:"postal_code,26" json:"postal_code"`
	Birthday   int32          `thrift:"birthday,27" json:"birthday"`
	Gender     GenderCode     `thrift:"gender,28" json:"gender"`
	Age        AgeCode        `thrift:"age,29" json:"age"`
	AdSpot     string         `thrift:"ad_spot,30" json:"ad_spot"`
	Access     AccessTypeCode `thrift:"access,31" json:"access"`
	Carrier    CarrierCode    `thrift:"carrier,32" json:"carrier"`
	AppName    string         `thrift:"app_name,33" json:"app_name"`
	Latitude   float64        `thrift:"latitude,34" json:"latitude"`
	Longitude  float64        `thrift:"longitude,35" json:"longitude"`
	Reqh       int32          `thrift:"reqh,36" json:"reqh"`
	Reqw       int32          `thrift:"reqw,37" json:"reqw"`
	// unused field # 38
	// unused field # 39
	Expinfo         []int32          `thrift:"expinfo,40" json:"expinfo"`
	Capability      []CapabilityType `thrift:"capability,41" json:"capability"`
	TemplateList    []int64          `thrift:"template_list,42" json:"template_list"`
	SpecifiedAdList []int32          `thrift:"specified_ad_list,43" json:"specified_ad_list"`
	// unused field # 44
	ReqNum int32 `thrift:"req_num,45" json:"req_num"`
}

func NewUiAdReq() *UiAdReq {
	return &UiAdReq{
		TestAction: math.MinInt32 - 1, // unset sentinal value

		Gender: math.MinInt32 - 1, // unset sentinal value

		Age: math.MinInt32 - 1, // unset sentinal value

		Access: math.MinInt32 - 1, // unset sentinal value

		Carrier: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UiAdReq) IsSetTestAction() bool {
	return int64(p.TestAction) != math.MinInt32-1
}

func (p *UiAdReq) IsSetGender() bool {
	return int64(p.Gender) != math.MinInt32-1
}

func (p *UiAdReq) IsSetAge() bool {
	return int64(p.Age) != math.MinInt32-1
}

func (p *UiAdReq) IsSetAccess() bool {
	return int64(p.Access) != math.MinInt32-1
}

func (p *UiAdReq) IsSetCarrier() bool {
	return int64(p.Carrier) != math.MinInt32-1
}

func (p *UiAdReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 45:
			if fieldTypeId == thrift.I32 {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.LIST {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I64 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.LIST {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.LIST {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.LIST {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.LIST {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UiAdReq) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.ReqNum = v
	}
	return nil
}

func (p *UiAdReq) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.So = v
	}
	return nil
}

func (p *UiAdReq) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Sw = v
	}
	return nil
}

func (p *UiAdReq) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Sh = v
	}
	return nil
}

func (p *UiAdReq) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Sd = v
	}
	return nil
}

func (p *UiAdReq) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Reqh = v
	}
	return nil
}

func (p *UiAdReq) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Reqw = v
	}
	return nil
}

func (p *UiAdReq) readField19(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ActionCapability = make([]AdActionType, 0, size)
	for i := 0; i < size; i++ {
		var _elem4 AdActionType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem4 = AdActionType(v)
		}
		p.ActionCapability = append(p.ActionCapability, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiAdReq) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Keyword = v
	}
	return nil
}

func (p *UiAdReq) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Query = v
	}
	return nil
}

func (p *UiAdReq) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.TestMode = v
	}
	return nil
}

func (p *UiAdReq) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.TestAction = AdActionType(v)
	}
	return nil
}

func (p *UiAdReq) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.CoordTime = TimeInt(v)
	}
	return nil
}

func (p *UiAdReq) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *UiAdReq) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *UiAdReq) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.PostalCode = v
	}
	return nil
}

func (p *UiAdReq) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Birthday = v
	}
	return nil
}

func (p *UiAdReq) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Gender = GenderCode(v)
	}
	return nil
}

func (p *UiAdReq) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.Age = AgeCode(v)
	}
	return nil
}

func (p *UiAdReq) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.AdSpot = v
	}
	return nil
}

func (p *UiAdReq) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Access = AccessTypeCode(v)
	}
	return nil
}

func (p *UiAdReq) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Carrier = CarrierCode(v)
	}
	return nil
}

func (p *UiAdReq) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *UiAdReq) readField40(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Expinfo = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.Expinfo = append(p.Expinfo, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiAdReq) readField41(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Capability = make([]CapabilityType, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 CapabilityType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = CapabilityType(v)
		}
		p.Capability = append(p.Capability, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiAdReq) readField42(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TemplateList = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.TemplateList = append(p.TemplateList, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiAdReq) readField43(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SpecifiedAdList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = v
		}
		p.SpecifiedAdList = append(p.SpecifiedAdList, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiAdReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UiAdReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UiAdReq) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("so", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:so: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.So)); err != nil {
		return fmt.Errorf("%T.so (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:so: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sw", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sw: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sw)); err != nil {
		return fmt.Errorf("%T.sw (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sw: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sh", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:sh: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sh)); err != nil {
		return fmt.Errorf("%T.sh (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:sh: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd", thrift.DOUBLE, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:sd: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Sd)); err != nil {
		return fmt.Errorf("%T.sd (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:sd: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField19(oprot thrift.TProtocol) (err error) {
	if p.ActionCapability != nil {
		if err := oprot.WriteFieldBegin("action_capability", thrift.LIST, 19); err != nil {
			return fmt.Errorf("%T write field begin error 19:action_capability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ActionCapability)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ActionCapability {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 19:action_capability: %s", p, err)
		}
	}
	return err
}

func (p *UiAdReq) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyword", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:keyword: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keyword)); err != nil {
		return fmt.Errorf("%T.keyword (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:keyword: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("query", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:query: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Query)); err != nil {
		return fmt.Errorf("%T.query (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:query: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("test_mode", thrift.BOOL, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:test_mode: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.TestMode)); err != nil {
		return fmt.Errorf("%T.test_mode (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:test_mode: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("test_action", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:test_action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TestAction)); err != nil {
		return fmt.Errorf("%T.test_action (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:test_action: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_time", thrift.I64, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:coord_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CoordTime)); err != nil {
		return fmt.Errorf("%T.coord_time (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:coord_time: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("postal_code", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:postal_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PostalCode)); err != nil {
		return fmt.Errorf("%T.postal_code (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:postal_code: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("birthday", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:birthday: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Birthday)); err != nil {
		return fmt.Errorf("%T.birthday (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:birthday: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gender", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:gender: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Gender)); err != nil {
		return fmt.Errorf("%T.gender (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:gender: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("age", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:age: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Age)); err != nil {
		return fmt.Errorf("%T.age (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:age: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_spot", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:ad_spot: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdSpot)); err != nil {
		return fmt.Errorf("%T.ad_spot (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:ad_spot: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:access: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Access)); err != nil {
		return fmt.Errorf("%T.access (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:access: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:carrier: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:carrier: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_name", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.app_name (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:app_name: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:latitude: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:longitude: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reqh", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:reqh: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Reqh)); err != nil {
		return fmt.Errorf("%T.reqh (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:reqh: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reqw", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:reqw: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Reqw)); err != nil {
		return fmt.Errorf("%T.reqw (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:reqw: %s", p, err)
	}
	return err
}

func (p *UiAdReq) writeField40(oprot thrift.TProtocol) (err error) {
	if p.Expinfo != nil {
		if err := oprot.WriteFieldBegin("expinfo", thrift.LIST, 40); err != nil {
			return fmt.Errorf("%T write field begin error 40:expinfo: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Expinfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Expinfo {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 40:expinfo: %s", p, err)
		}
	}
	return err
}

func (p *UiAdReq) writeField41(oprot thrift.TProtocol) (err error) {
	if p.Capability != nil {
		if err := oprot.WriteFieldBegin("capability", thrift.LIST, 41); err != nil {
			return fmt.Errorf("%T write field begin error 41:capability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Capability)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Capability {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 41:capability: %s", p, err)
		}
	}
	return err
}

func (p *UiAdReq) writeField42(oprot thrift.TProtocol) (err error) {
	if p.TemplateList != nil {
		if err := oprot.WriteFieldBegin("template_list", thrift.LIST, 42); err != nil {
			return fmt.Errorf("%T write field begin error 42:template_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.TemplateList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TemplateList {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 42:template_list: %s", p, err)
		}
	}
	return err
}

func (p *UiAdReq) writeField43(oprot thrift.TProtocol) (err error) {
	if p.SpecifiedAdList != nil {
		if err := oprot.WriteFieldBegin("specified_ad_list", thrift.LIST, 43); err != nil {
			return fmt.Errorf("%T write field begin error 43:specified_ad_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SpecifiedAdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SpecifiedAdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 43:specified_ad_list: %s", p, err)
		}
	}
	return err
}

func (p *UiAdReq) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_num", thrift.I32, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:req_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqNum)); err != nil {
		return fmt.Errorf("%T.req_num (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:req_num: %s", p, err)
	}
	return err
}

func (p *UiAdReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UiAdReq(%+v)", *p)
}

type UiDetReq struct {
	// unused field # 1
	// unused field # 2
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	ConfigTimestamp   TimeInt `thrift:"config_timestamp,12" json:"config_timestamp"`
	ResourceTimestamp TimeInt `thrift:"resource_timestamp,13" json:"resource_timestamp"`
}

func NewUiDetReq() *UiDetReq {
	return &UiDetReq{}
}

func (p *UiDetReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UiDetReq) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ConfigTimestamp = TimeInt(v)
	}
	return nil
}

func (p *UiDetReq) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ResourceTimestamp = TimeInt(v)
	}
	return nil
}

func (p *UiDetReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UiDetReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UiDetReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("config_timestamp", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:config_timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConfigTimestamp)); err != nil {
		return fmt.Errorf("%T.config_timestamp (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:config_timestamp: %s", p, err)
	}
	return err
}

func (p *UiDetReq) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("resource_timestamp", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:resource_timestamp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ResourceTimestamp)); err != nil {
		return fmt.Errorf("%T.resource_timestamp (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:resource_timestamp: %s", p, err)
	}
	return err
}

func (p *UiDetReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UiDetReq(%+v)", *p)
}

type UiReq struct {
	ReqType      ReqType        `thrift:"req_type,1" json:"req_type"`
	Ip           IpInt          `thrift:"ip,2" json:"ip"`
	Region       RegionCode     `thrift:"region,3" json:"region"`
	Device       DeviceCode     `thrift:"device,4" json:"device"`
	Os           OSCode         `thrift:"os,5" json:"os"`
	Cookie       string         `thrift:"cookie,6" json:"cookie"`
	Refer        string         `thrift:"refer,7" json:"refer"`
	MediaId      MediaIdInt     `thrift:"media_id,8" json:"media_id"`
	Imei         LargeIdInt     `thrift:"imei,9" json:"imei"`
	Imsi         LargeIdInt     `thrift:"imsi,10" json:"imsi"`
	SdkType      SDKTypeCode    `thrift:"sdk_type,11" json:"sdk_type"`
	Language     LanguageType   `thrift:"language,12" json:"language"`
	Format       ADResponseType `thrift:"format,13" json:"format"`
	EncodingType EncodingCode   `thrift:"encoding_type,14" json:"encoding_type"`
	Pubid        string         `thrift:"pubid,15" json:"pubid"`
	AdReq        *UiAdReq       `thrift:"ad_req,16" json:"ad_req"`
	DetReq       *UiDetReq      `thrift:"det_req,17" json:"det_req"`
	SearchId     LargeIdInt     `thrift:"search_id,18" json:"search_id"`
	DeverId      UidInt         `thrift:"dever_id,19" json:"dever_id"`
	// unused field # 20
	Browser            BrowserCode `thrift:"browser,21" json:"browser"`
	BrowserVersion     string      `thrift:"browser_version,22" json:"browser_version"`
	Feature            []int32     `thrift:"feature,23" json:"feature"`
	Determine          string      `thrift:"determine,24" json:"determine"`
	OsdeviceDependency string      `thrift:"osdevice_dependency,25" json:"osdevice_dependency"`
	Xheader            string      `thrift:"xheader,26" json:"xheader"`
	// unused field # 27
	SdkVersion       int32           `thrift:"sdk_version,28" json:"sdk_version"`
	SdkPlatform      int32           `thrift:"sdk_platform,29" json:"sdk_platform"`
	CityRegion       RegionCityInt   `thrift:"city_region,30" json:"city_region"`
	Uuid             string          `thrift:"uuid,31" json:"uuid"`
	Guid             int64           `thrift:"guid,32" json:"guid"`
	IsJailBreak      JailBreakCode   `thrift:"is_jail_break,33" json:"is_jail_break"`
	PlacementType    AdPlacementType `thrift:"placement_type,34" json:"placement_type"`
	DomobUid         int64           `thrift:"domob_uid,35" json:"domob_uid"`
	MediaPlacement   int32           `thrift:"media_placement,36" json:"media_placement"`
	RawPlacementType AdPlacementType `thrift:"raw_placement_type,37" json:"raw_placement_type"`
	Ifa              string          `thrift:"ifa,38" json:"ifa"`
	ImpFreqSet       []int32         `thrift:"impFreqSet,39" json:"impFreqSet"`
	ClkFreqSet       []int32         `thrift:"clkFreqSet,40" json:"clkFreqSet"`
	CachedResource   []int32         `thrift:"cachedResource,41" json:"cachedResource"`
	FirstVisitTime   TimeInt         `thrift:"firstVisitTime,42" json:"firstVisitTime"`
	Anid             string          `thrift:"anid,43" json:"anid"`
	Anid2            string          `thrift:"anid2,44" json:"anid2"`
	Amac             string          `thrift:"amac,45" json:"amac"`
	LastVisitTime    TimeInt         `thrift:"lastVisitTime,46" json:"lastVisitTime"`
	// unused field # 47
	// unused field # 48
	// unused field # 49
	ReqContainers          []int32                 `thrift:"reqContainers,50" json:"reqContainers"`
	InstalledPackages      []string                `thrift:"installedPackages,51" json:"installedPackages"`
	Channel                string                  `thrift:"channel,52" json:"channel"`
	SubChannel             string                  `thrift:"sub_channel,53" json:"sub_channel"`
	Title                  string                  `thrift:"title,54" json:"title"`
	PartnerId              int32                   `thrift:"partner_id,55" json:"partner_id"`
	RequestSourceType      enums.RequestSourceType `thrift:"request_source_type,56" json:"request_source_type"`
	ReqChannelId           int32                   `thrift:"req_channel_id,57" json:"req_channel_id"`
	ReqSubChannelId        int32                   `thrift:"req_sub_channel_id,58" json:"req_sub_channel_id"`
	ReqTitleId             int32                   `thrift:"req_title_id,59" json:"req_title_id"`
	ThirdpartyProvinceCode int32                   `thrift:"thirdparty_province_code,60" json:"thirdparty_province_code"`
	ThirdpartyCityCode     int32                   `thrift:"thirdparty_city_code,61" json:"thirdparty_city_code"`
}

func NewUiReq() *UiReq {
	return &UiReq{
		ReqType: math.MinInt32 - 1, // unset sentinal value

		Region: math.MinInt32 - 1, // unset sentinal value

		SdkType: math.MinInt32 - 1, // unset sentinal value

		Language: math.MinInt32 - 1, // unset sentinal value

		Format: math.MinInt32 - 1, // unset sentinal value

		EncodingType: math.MinInt32 - 1, // unset sentinal value

		IsJailBreak: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value

		RawPlacementType: math.MinInt32 - 1, // unset sentinal value

		RequestSourceType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UiReq) IsSetReqType() bool {
	return int64(p.ReqType) != math.MinInt32-1
}

func (p *UiReq) IsSetRegion() bool {
	return int64(p.Region) != math.MinInt32-1
}

func (p *UiReq) IsSetSdkType() bool {
	return int64(p.SdkType) != math.MinInt32-1
}

func (p *UiReq) IsSetLanguage() bool {
	return int64(p.Language) != math.MinInt32-1
}

func (p *UiReq) IsSetFormat() bool {
	return int64(p.Format) != math.MinInt32-1
}

func (p *UiReq) IsSetEncodingType() bool {
	return int64(p.EncodingType) != math.MinInt32-1
}

func (p *UiReq) IsSetIsJailBreak() bool {
	return int64(p.IsJailBreak) != math.MinInt32-1
}

func (p *UiReq) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *UiReq) IsSetRawPlacementType() bool {
	return int64(p.RawPlacementType) != math.MinInt32-1
}

func (p *UiReq) IsSetRequestSourceType() bool {
	return int64(p.RequestSourceType) != math.MinInt32-1
}

func (p *UiReq) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I64 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.LIST {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I64 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.LIST {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.LIST {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.LIST {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I64 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.STRING {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.STRING {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.I64 {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.LIST {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.LIST {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.STRING {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.STRING {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.STRING {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I32 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.I32 {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.I32 {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.I32 {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.I32 {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I32 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UiReq) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ReqType = ReqType(v)
	}
	return nil
}

func (p *UiReq) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Ip = IpInt(v)
	}
	return nil
}

func (p *UiReq) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Region = RegionCode(v)
	}
	return nil
}

func (p *UiReq) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Device = DeviceCode(v)
	}
	return nil
}

func (p *UiReq) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Os = OSCode(v)
	}
	return nil
}

func (p *UiReq) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Cookie = v
	}
	return nil
}

func (p *UiReq) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Refer = v
	}
	return nil
}

func (p *UiReq) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.MediaId = MediaIdInt(v)
	}
	return nil
}

func (p *UiReq) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Imei = LargeIdInt(v)
	}
	return nil
}

func (p *UiReq) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Imsi = LargeIdInt(v)
	}
	return nil
}

func (p *UiReq) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SdkType = SDKTypeCode(v)
	}
	return nil
}

func (p *UiReq) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Language = LanguageType(v)
	}
	return nil
}

func (p *UiReq) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Format = ADResponseType(v)
	}
	return nil
}

func (p *UiReq) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.EncodingType = EncodingCode(v)
	}
	return nil
}

func (p *UiReq) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *UiReq) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.SearchId = LargeIdInt(v)
	}
	return nil
}

func (p *UiReq) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.DeverId = UidInt(v)
	}
	return nil
}

func (p *UiReq) readField16(iprot thrift.TProtocol) error {
	p.AdReq = NewUiAdReq()
	if err := p.AdReq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdReq)
	}
	return nil
}

func (p *UiReq) readField17(iprot thrift.TProtocol) error {
	p.DetReq = NewUiDetReq()
	if err := p.DetReq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DetReq)
	}
	return nil
}

func (p *UiReq) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Browser = BrowserCode(v)
	}
	return nil
}

func (p *UiReq) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.BrowserVersion = v
	}
	return nil
}

func (p *UiReq) readField23(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Feature = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = v
		}
		p.Feature = append(p.Feature, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiReq) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Determine = v
	}
	return nil
}

func (p *UiReq) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.OsdeviceDependency = v
	}
	return nil
}

func (p *UiReq) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Xheader = v
	}
	return nil
}

func (p *UiReq) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.SdkVersion = v
	}
	return nil
}

func (p *UiReq) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.SdkPlatform = v
	}
	return nil
}

func (p *UiReq) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CityRegion = RegionCityInt(v)
	}
	return nil
}

func (p *UiReq) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *UiReq) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Guid = v
	}
	return nil
}

func (p *UiReq) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.IsJailBreak = JailBreakCode(v)
	}
	return nil
}

func (p *UiReq) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.PlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *UiReq) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.DomobUid = v
	}
	return nil
}

func (p *UiReq) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.MediaPlacement = v
	}
	return nil
}

func (p *UiReq) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.RawPlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *UiReq) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Ifa = v
	}
	return nil
}

func (p *UiReq) readField39(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpFreqSet = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.ImpFreqSet = append(p.ImpFreqSet, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiReq) readField40(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClkFreqSet = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = v
		}
		p.ClkFreqSet = append(p.ClkFreqSet, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiReq) readField41(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CachedResource = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = v
		}
		p.CachedResource = append(p.CachedResource, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiReq) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.FirstVisitTime = TimeInt(v)
	}
	return nil
}

func (p *UiReq) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Anid = v
	}
	return nil
}

func (p *UiReq) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.Anid2 = v
	}
	return nil
}

func (p *UiReq) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *UiReq) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.LastVisitTime = TimeInt(v)
	}
	return nil
}

func (p *UiReq) readField50(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ReqContainers = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = v
		}
		p.ReqContainers = append(p.ReqContainers, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiReq) readField51(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.InstalledPackages = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.InstalledPackages = append(p.InstalledPackages, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UiReq) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *UiReq) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.SubChannel = v
	}
	return nil
}

func (p *UiReq) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *UiReq) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.PartnerId = v
	}
	return nil
}

func (p *UiReq) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.RequestSourceType = enums.RequestSourceType(v)
	}
	return nil
}

func (p *UiReq) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.ReqChannelId = v
	}
	return nil
}

func (p *UiReq) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.ReqSubChannelId = v
	}
	return nil
}

func (p *UiReq) readField59(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 59: %s", err)
	} else {
		p.ReqTitleId = v
	}
	return nil
}

func (p *UiReq) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.ThirdpartyProvinceCode = v
	}
	return nil
}

func (p *UiReq) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.ThirdpartyCityCode = v
	}
	return nil
}

func (p *UiReq) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("UiReq"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UiReq) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetReqType() {
		if err := oprot.WriteFieldBegin("req_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:req_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ReqType)); err != nil {
			return fmt.Errorf("%T.req_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:req_type: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:ip: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:ip: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Region)); err != nil {
		return fmt.Errorf("%T.region (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:region: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:device: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Device)); err != nil {
		return fmt.Errorf("%T.device (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:device: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:os: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Os)); err != nil {
		return fmt.Errorf("%T.os (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:os: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cookie", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:cookie: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cookie)); err != nil {
		return fmt.Errorf("%T.cookie (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:cookie: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refer", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:refer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Refer)); err != nil {
		return fmt.Errorf("%T.refer (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:refer: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:media_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.media_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:media_id: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:imei: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:imei: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:imsi: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:imsi: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_type", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:sdk_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkType)); err != nil {
		return fmt.Errorf("%T.sdk_type (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:sdk_type: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("language", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:language: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Language)); err != nil {
		return fmt.Errorf("%T.language (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:language: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("format", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:format: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Format)); err != nil {
		return fmt.Errorf("%T.format (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:format: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encoding_type", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:encoding_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EncodingType)); err != nil {
		return fmt.Errorf("%T.encoding_type (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:encoding_type: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:pubid: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField16(oprot thrift.TProtocol) (err error) {
	if p.AdReq != nil {
		if err := oprot.WriteFieldBegin("ad_req", thrift.STRUCT, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:ad_req: %s", p, err)
		}
		if err := p.AdReq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdReq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:ad_req: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField17(oprot thrift.TProtocol) (err error) {
	if p.DetReq != nil {
		if err := oprot.WriteFieldBegin("det_req", thrift.STRUCT, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:det_req: %s", p, err)
		}
		if err := p.DetReq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DetReq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:det_req: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:search_id: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dever_id", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:dever_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverId)); err != nil {
		return fmt.Errorf("%T.dever_id (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:dever_id: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("browser", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:browser: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Browser)); err != nil {
		return fmt.Errorf("%T.browser (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:browser: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("browser_version", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:browser_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BrowserVersion)); err != nil {
		return fmt.Errorf("%T.browser_version (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:browser_version: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField23(oprot thrift.TProtocol) (err error) {
	if p.Feature != nil {
		if err := oprot.WriteFieldBegin("feature", thrift.LIST, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:feature: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Feature)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Feature {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:feature: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("determine", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:determine: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Determine)); err != nil {
		return fmt.Errorf("%T.determine (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:determine: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("osdevice_dependency", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:osdevice_dependency: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OsdeviceDependency)); err != nil {
		return fmt.Errorf("%T.osdevice_dependency (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:osdevice_dependency: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("xheader", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:xheader: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Xheader)); err != nil {
		return fmt.Errorf("%T.xheader (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:xheader: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_version", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:sdk_version: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkVersion)); err != nil {
		return fmt.Errorf("%T.sdk_version (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:sdk_version: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sdk_platform", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:sdk_platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SdkPlatform)); err != nil {
		return fmt.Errorf("%T.sdk_platform (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:sdk_platform: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("city_region", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:city_region: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CityRegion)); err != nil {
		return fmt.Errorf("%T.city_region (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:city_region: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:uuid: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("guid", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:guid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Guid)); err != nil {
		return fmt.Errorf("%T.guid (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:guid: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_jail_break", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:is_jail_break: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsJailBreak)); err != nil {
		return fmt.Errorf("%T.is_jail_break (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:is_jail_break: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placement_type", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:placement_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placement_type (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:placement_type: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("domob_uid", thrift.I64, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:domob_uid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DomobUid)); err != nil {
		return fmt.Errorf("%T.domob_uid (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:domob_uid: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_placement", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:media_placement: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaPlacement)); err != nil {
		return fmt.Errorf("%T.media_placement (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:media_placement: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("raw_placement_type", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:raw_placement_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RawPlacementType)); err != nil {
		return fmt.Errorf("%T.raw_placement_type (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:raw_placement_type: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ifa", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:ifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ifa)); err != nil {
		return fmt.Errorf("%T.ifa (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:ifa: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField39(oprot thrift.TProtocol) (err error) {
	if p.ImpFreqSet != nil {
		if err := oprot.WriteFieldBegin("impFreqSet", thrift.LIST, 39); err != nil {
			return fmt.Errorf("%T write field begin error 39:impFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ImpFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpFreqSet {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 39:impFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField40(oprot thrift.TProtocol) (err error) {
	if p.ClkFreqSet != nil {
		if err := oprot.WriteFieldBegin("clkFreqSet", thrift.LIST, 40); err != nil {
			return fmt.Errorf("%T write field begin error 40:clkFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ClkFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClkFreqSet {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 40:clkFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField41(oprot thrift.TProtocol) (err error) {
	if p.CachedResource != nil {
		if err := oprot.WriteFieldBegin("cachedResource", thrift.LIST, 41); err != nil {
			return fmt.Errorf("%T write field begin error 41:cachedResource: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CachedResource)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CachedResource {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 41:cachedResource: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("firstVisitTime", thrift.I64, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:firstVisitTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FirstVisitTime)); err != nil {
		return fmt.Errorf("%T.firstVisitTime (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:firstVisitTime: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anid", thrift.STRING, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:anid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Anid)); err != nil {
		return fmt.Errorf("%T.anid (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:anid: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anid2", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:anid2: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Anid2)); err != nil {
		return fmt.Errorf("%T.anid2 (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:anid2: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:amac: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastVisitTime", thrift.I64, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:lastVisitTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastVisitTime)); err != nil {
		return fmt.Errorf("%T.lastVisitTime (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:lastVisitTime: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField50(oprot thrift.TProtocol) (err error) {
	if p.ReqContainers != nil {
		if err := oprot.WriteFieldBegin("reqContainers", thrift.LIST, 50); err != nil {
			return fmt.Errorf("%T write field begin error 50:reqContainers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ReqContainers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ReqContainers {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 50:reqContainers: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField51(oprot thrift.TProtocol) (err error) {
	if p.InstalledPackages != nil {
		if err := oprot.WriteFieldBegin("installedPackages", thrift.LIST, 51); err != nil {
			return fmt.Errorf("%T write field begin error 51:installedPackages: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.InstalledPackages)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.InstalledPackages {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 51:installedPackages: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:channel: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sub_channel", thrift.STRING, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:sub_channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubChannel)); err != nil {
		return fmt.Errorf("%T.sub_channel (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:sub_channel: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:title: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("partner_id", thrift.I32, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:partner_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PartnerId)); err != nil {
		return fmt.Errorf("%T.partner_id (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:partner_id: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField56(oprot thrift.TProtocol) (err error) {
	if p.IsSetRequestSourceType() {
		if err := oprot.WriteFieldBegin("request_source_type", thrift.I32, 56); err != nil {
			return fmt.Errorf("%T write field begin error 56:request_source_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RequestSourceType)); err != nil {
			return fmt.Errorf("%T.request_source_type (56) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 56:request_source_type: %s", p, err)
		}
	}
	return err
}

func (p *UiReq) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_channel_id", thrift.I32, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:req_channel_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqChannelId)); err != nil {
		return fmt.Errorf("%T.req_channel_id (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:req_channel_id: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_sub_channel_id", thrift.I32, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:req_sub_channel_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqSubChannelId)); err != nil {
		return fmt.Errorf("%T.req_sub_channel_id (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:req_sub_channel_id: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField59(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_title_id", thrift.I32, 59); err != nil {
		return fmt.Errorf("%T write field begin error 59:req_title_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqTitleId)); err != nil {
		return fmt.Errorf("%T.req_title_id (59) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 59:req_title_id: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thirdparty_province_code", thrift.I32, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:thirdparty_province_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ThirdpartyProvinceCode)); err != nil {
		return fmt.Errorf("%T.thirdparty_province_code (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:thirdparty_province_code: %s", p, err)
	}
	return err
}

func (p *UiReq) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thirdparty_city_code", thrift.I32, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:thirdparty_city_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ThirdpartyCityCode)); err != nil {
		return fmt.Errorf("%T.thirdparty_city_code (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:thirdparty_city_code: %s", p, err)
	}
	return err
}

func (p *UiReq) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UiReq(%+v)", *p)
}

type ResAdTemplate struct {
	Name          string                      `thrift:"name,1" json:"name"`
	TypeA1        AdCreativeTemplateType      `thrift:"type,2" json:"type"`
	Specification AdCreativeTemplateSpecIdInt `thrift:"specification,3" json:"specification"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Size      TemplateSizeCode    `thrift:"size,10" json:"size"`
	Image     ImgIdInt            `thrift:"image,11" json:"image"`
	Icon      ImgIdInt            `thrift:"icon,12" json:"icon"`
	Density   float64             `thrift:"density,13" json:"density"`
	ImageType ImageType           `thrift:"image_type,14" json:"image_type"`
	Html      ResIdInt            `thrift:"html,15" json:"html"`
	Isize     TemplateSizeCodeInt `thrift:"isize,16" json:"isize"`
	HDicon    ImgIdInt            `thrift:"HDicon,17" json:"HDicon"`
	// unused field # 18
	Title  string `thrift:"title,19" json:"title"`
	Brief  string `thrift:"brief,20" json:"brief"`
	Detail string `thrift:"detail,21" json:"detail"`
	Width  int32  `thrift:"width,22" json:"width"`
	Height int32  `thrift:"height,23" json:"height"`
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	// unused field # 98
	// unused field # 99
	IsOriginal            bool                  `thrift:"isOriginal,100" json:"isOriginal"`
	ShouldIgnoreWaterMark bool                  `thrift:"shouldIgnoreWaterMark,101" json:"shouldIgnoreWaterMark"`
	WaterMarkPosition     WaterMarkPosition     `thrift:"waterMarkPosition,102" json:"waterMarkPosition"`
	Url                   string                `thrift:"url,103" json:"url"`
	ResourceGroup         *common.ResourceGroup `thrift:"resourceGroup,104" json:"resourceGroup"`
}

func NewResAdTemplate() *ResAdTemplate {
	return &ResAdTemplate{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Size: math.MinInt32 - 1, // unset sentinal value

		ImageType: math.MinInt32 - 1, // unset sentinal value

		WaterMarkPosition: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ResAdTemplate) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *ResAdTemplate) IsSetSize() bool {
	return int64(p.Size) != math.MinInt32-1
}

func (p *ResAdTemplate) IsSetImageType() bool {
	return int64(p.ImageType) != math.MinInt32-1
}

func (p *ResAdTemplate) IsSetWaterMarkPosition() bool {
	return int64(p.WaterMarkPosition) != math.MinInt32-1
}

func (p *ResAdTemplate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 100:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField100(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 101:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField101(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 102:
			if fieldTypeId == thrift.I32 {
				if err := p.readField102(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 103:
			if fieldTypeId == thrift.STRING {
				if err := p.readField103(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 104:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField104(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResAdTemplate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ResAdTemplate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = AdCreativeTemplateType(v)
	}
	return nil
}

func (p *ResAdTemplate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Specification = AdCreativeTemplateSpecIdInt(v)
	}
	return nil
}

func (p *ResAdTemplate) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Size = TemplateSizeCode(v)
	}
	return nil
}

func (p *ResAdTemplate) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Image = ImgIdInt(v)
	}
	return nil
}

func (p *ResAdTemplate) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Icon = ImgIdInt(v)
	}
	return nil
}

func (p *ResAdTemplate) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Density = v
	}
	return nil
}

func (p *ResAdTemplate) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ImageType = ImageType(v)
	}
	return nil
}

func (p *ResAdTemplate) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Html = ResIdInt(v)
	}
	return nil
}

func (p *ResAdTemplate) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Isize = TemplateSizeCodeInt(v)
	}
	return nil
}

func (p *ResAdTemplate) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.HDicon = ImgIdInt(v)
	}
	return nil
}

func (p *ResAdTemplate) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *ResAdTemplate) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Brief = v
	}
	return nil
}

func (p *ResAdTemplate) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *ResAdTemplate) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *ResAdTemplate) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *ResAdTemplate) readField100(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 100: %s", err)
	} else {
		p.IsOriginal = v
	}
	return nil
}

func (p *ResAdTemplate) readField101(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 101: %s", err)
	} else {
		p.ShouldIgnoreWaterMark = v
	}
	return nil
}

func (p *ResAdTemplate) readField102(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 102: %s", err)
	} else {
		p.WaterMarkPosition = WaterMarkPosition(v)
	}
	return nil
}

func (p *ResAdTemplate) readField103(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 103: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *ResAdTemplate) readField104(iprot thrift.TProtocol) error {
	p.ResourceGroup = common.NewResourceGroup()
	if err := p.ResourceGroup.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.ResourceGroup)
	}
	return nil
}

func (p *ResAdTemplate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResAdTemplate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField100(oprot); err != nil {
		return err
	}
	if err := p.writeField101(oprot); err != nil {
		return err
	}
	if err := p.writeField102(oprot); err != nil {
		return err
	}
	if err := p.writeField103(oprot); err != nil {
		return err
	}
	if err := p.writeField104(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResAdTemplate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:type: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("specification", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:specification: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Specification)); err != nil {
		return fmt.Errorf("%T.specification (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:specification: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:size: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:image: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Image)); err != nil {
		return fmt.Errorf("%T.image (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:image: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:icon: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Icon)); err != nil {
		return fmt.Errorf("%T.icon (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:icon: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("density", thrift.DOUBLE, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:density: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Density)); err != nil {
		return fmt.Errorf("%T.density (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:density: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image_type", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:image_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImageType)); err != nil {
		return fmt.Errorf("%T.image_type (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:image_type: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("html", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:html: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Html)); err != nil {
		return fmt.Errorf("%T.html (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:html: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isize", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:isize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Isize)); err != nil {
		return fmt.Errorf("%T.isize (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:isize: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("HDicon", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:HDicon: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.HDicon)); err != nil {
		return fmt.Errorf("%T.HDicon (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:HDicon: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:title: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brief", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:brief: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Brief)); err != nil {
		return fmt.Errorf("%T.brief (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:brief: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:detail: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:width: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:height: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField100(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOriginal", thrift.BOOL, 100); err != nil {
		return fmt.Errorf("%T write field begin error 100:isOriginal: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOriginal)); err != nil {
		return fmt.Errorf("%T.isOriginal (100) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 100:isOriginal: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField101(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shouldIgnoreWaterMark", thrift.BOOL, 101); err != nil {
		return fmt.Errorf("%T write field begin error 101:shouldIgnoreWaterMark: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ShouldIgnoreWaterMark)); err != nil {
		return fmt.Errorf("%T.shouldIgnoreWaterMark (101) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 101:shouldIgnoreWaterMark: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField102(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("waterMarkPosition", thrift.I32, 102); err != nil {
		return fmt.Errorf("%T write field begin error 102:waterMarkPosition: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WaterMarkPosition)); err != nil {
		return fmt.Errorf("%T.waterMarkPosition (102) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 102:waterMarkPosition: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField103(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 103); err != nil {
		return fmt.Errorf("%T write field begin error 103:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (103) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 103:url: %s", p, err)
	}
	return err
}

func (p *ResAdTemplate) writeField104(oprot thrift.TProtocol) (err error) {
	if p.ResourceGroup != nil {
		if err := oprot.WriteFieldBegin("resourceGroup", thrift.STRUCT, 104); err != nil {
			return fmt.Errorf("%T write field begin error 104:resourceGroup: %s", p, err)
		}
		if err := p.ResourceGroup.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.ResourceGroup)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 104:resourceGroup: %s", p, err)
		}
	}
	return err
}

func (p *ResAdTemplate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResAdTemplate(%+v)", *p)
}

type ResAdCreative struct {
	Templates    *ResAdTemplate   `thrift:"templates,1" json:"templates"`
	TemplateList []*ResAdTemplate `thrift:"templateList,2" json:"templateList"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	LongText          string             `thrift:"long_text,9" json:"long_text"`
	Text              string             `thrift:"text,10" json:"text"`
	IconType          AdCreativeIconType `thrift:"icon_type,11" json:"icon_type"`
	ForeColor         string             `thrift:"fore_color,12" json:"fore_color"`
	BgColor           string             `thrift:"bg_color,13" json:"bg_color"`
	DisplayUrl        string             `thrift:"display_url,14" json:"display_url"`
	UrlOpenType       SDKUrlOpenType     `thrift:"url_open_type,15" json:"url_open_type"`
	DownAppName       string             `thrift:"down_app_name,16" json:"down_app_name"`
	IsPreload         bool               `thrift:"is_preload,17" json:"is_preload"`
	IsAutoRun         bool               `thrift:"is_auto_run,18" json:"is_auto_run"`
	IsClickComfirm    bool               `thrift:"is_click_comfirm,19" json:"is_click_comfirm"`
	PackageName       string             `thrift:"package_name,20" json:"package_name"`
	PackageVersion    string             `thrift:"package_version,21" json:"package_version"`
	DetectPackageName string             `thrift:"detect_package_name,22" json:"detect_package_name"`
	DetectPackageCode int32              `thrift:"detect_package_code,23" json:"detect_package_code"`
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	AltText            string           `thrift:"alt_text,30" json:"alt_text"`
	LandingDirection   LandingDirection `thrift:"landing_direction,31" json:"landing_direction"`
	PackageVersionCode int32            `thrift:"package_version_code,32" json:"package_version_code"`
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	TemplateCode      HtmlTemplateCodeInt `thrift:"template_code,41" json:"template_code"`
	RenderType        AdRenderType        `thrift:"render_type,42" json:"render_type"`
	PlacementType     AdPlacementType     `thrift:"placement_type,43" json:"placement_type"`
	EffectiveShowTime TimeInt             `thrift:"effective_show_time,44" json:"effective_show_time"`
	CompleteShowTime  TimeInt             `thrift:"complete_show_time,45" json:"complete_show_time"`
	AutoClose         bool                `thrift:"auto_close,46" json:"auto_close"`
	DisableRotate     bool                `thrift:"disable_rotate,47" json:"disable_rotate"`
	EndTime           TimeInt             `thrift:"end_time,48" json:"end_time"`
	PoiInfoList       []*POIInfo          `thrift:"poi_info_list,49" json:"poi_info_list"`
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	ActionUrl             string              `thrift:"actionUrl,64" json:"actionUrl"`
	Longitude             float64             `thrift:"longitude,65" json:"longitude"`
	Latitude              float64             `thrift:"latitude,66" json:"latitude"`
	ActionCall            string              `thrift:"actionCall,67" json:"actionCall"`
	ActionSMSNumber       string              `thrift:"actionSMSNumber,68" json:"actionSMSNumber"`
	ActionSMSMessage      string              `thrift:"actionSMSMessage,69" json:"actionSMSMessage"`
	ActionMailAddress     string              `thrift:"actionMailAddress,70" json:"actionMailAddress"`
	ActionMailSubject     string              `thrift:"actionMailSubject,71" json:"actionMailSubject"`
	ActionMailBody        string              `thrift:"actionMailBody,72" json:"actionMailBody"`
	ActionUrlFailsafe     string              `thrift:"actionUrlFailsafe,73" json:"actionUrlFailsafe"`
	ImpressionTrackingUrl string              `thrift:"impressionTrackingUrl,74" json:"impressionTrackingUrl"`
	ClickTrackingUrl      string              `thrift:"clickTrackingUrl,75" json:"clickTrackingUrl"`
	CloseButtonPosition   CloseButtonPosition `thrift:"closeButtonPosition,76" json:"closeButtonPosition"`
	TemplateConfig        int64               `thrift:"templateConfig,77" json:"templateConfig"`
	JsonInfo              string              `thrift:"jsonInfo,78" json:"jsonInfo"`
	StartTime             TimeInt             `thrift:"start_time,79" json:"start_time"`
	AppId                 int32               `thrift:"app_id,80" json:"app_id"`
	AppChannelId          int32               `thrift:"app_channel_id,81" json:"app_channel_id"`
	AdLevel               int32               `thrift:"ad_level,82" json:"ad_level"`
}

func NewResAdCreative() *ResAdCreative {
	return &ResAdCreative{
		IconType: math.MinInt32 - 1, // unset sentinal value

		UrlOpenType: math.MinInt32 - 1, // unset sentinal value

		LandingDirection: math.MinInt32 - 1, // unset sentinal value

		RenderType: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value

		CloseButtonPosition: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ResAdCreative) IsSetIconType() bool {
	return int64(p.IconType) != math.MinInt32-1
}

func (p *ResAdCreative) IsSetUrlOpenType() bool {
	return int64(p.UrlOpenType) != math.MinInt32-1
}

func (p *ResAdCreative) IsSetLandingDirection() bool {
	return int64(p.LandingDirection) != math.MinInt32-1
}

func (p *ResAdCreative) IsSetRenderType() bool {
	return int64(p.RenderType) != math.MinInt32-1
}

func (p *ResAdCreative) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *ResAdCreative) IsSetCloseButtonPosition() bool {
	return int64(p.CloseButtonPosition) != math.MinInt32-1
}

func (p *ResAdCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I32 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I64 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.I64 {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.I64 {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.LIST {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.STRING {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 65:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField65(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 66:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField66(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 67:
			if fieldTypeId == thrift.STRING {
				if err := p.readField67(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 68:
			if fieldTypeId == thrift.STRING {
				if err := p.readField68(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 69:
			if fieldTypeId == thrift.STRING {
				if err := p.readField69(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 70:
			if fieldTypeId == thrift.STRING {
				if err := p.readField70(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.STRING {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.STRING {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 73:
			if fieldTypeId == thrift.STRING {
				if err := p.readField73(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 74:
			if fieldTypeId == thrift.STRING {
				if err := p.readField74(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 75:
			if fieldTypeId == thrift.STRING {
				if err := p.readField75(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 76:
			if fieldTypeId == thrift.I32 {
				if err := p.readField76(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 77:
			if fieldTypeId == thrift.I64 {
				if err := p.readField77(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 78:
			if fieldTypeId == thrift.STRING {
				if err := p.readField78(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 79:
			if fieldTypeId == thrift.I64 {
				if err := p.readField79(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 80:
			if fieldTypeId == thrift.I32 {
				if err := p.readField80(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 81:
			if fieldTypeId == thrift.I32 {
				if err := p.readField81(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 82:
			if fieldTypeId == thrift.I32 {
				if err := p.readField82(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResAdCreative) readField1(iprot thrift.TProtocol) error {
	p.Templates = NewResAdTemplate()
	if err := p.Templates.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Templates)
	}
	return nil
}

func (p *ResAdCreative) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TemplateList = make([]*ResAdTemplate, 0, size)
	for i := 0; i < size; i++ {
		_elem15 := NewResAdTemplate()
		if err := _elem15.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem15)
		}
		p.TemplateList = append(p.TemplateList, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResAdCreative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.LongText = v
	}
	return nil
}

func (p *ResAdCreative) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *ResAdCreative) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.IconType = AdCreativeIconType(v)
	}
	return nil
}

func (p *ResAdCreative) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ForeColor = v
	}
	return nil
}

func (p *ResAdCreative) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.BgColor = v
	}
	return nil
}

func (p *ResAdCreative) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DisplayUrl = v
	}
	return nil
}

func (p *ResAdCreative) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.UrlOpenType = SDKUrlOpenType(v)
	}
	return nil
}

func (p *ResAdCreative) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.DownAppName = v
	}
	return nil
}

func (p *ResAdCreative) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.IsPreload = v
	}
	return nil
}

func (p *ResAdCreative) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.IsAutoRun = v
	}
	return nil
}

func (p *ResAdCreative) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.IsClickComfirm = v
	}
	return nil
}

func (p *ResAdCreative) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *ResAdCreative) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.PackageVersion = v
	}
	return nil
}

func (p *ResAdCreative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.DetectPackageName = v
	}
	return nil
}

func (p *ResAdCreative) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.DetectPackageCode = v
	}
	return nil
}

func (p *ResAdCreative) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.AltText = v
	}
	return nil
}

func (p *ResAdCreative) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LandingDirection = LandingDirection(v)
	}
	return nil
}

func (p *ResAdCreative) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.PackageVersionCode = v
	}
	return nil
}

func (p *ResAdCreative) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.TemplateCode = HtmlTemplateCodeInt(v)
	}
	return nil
}

func (p *ResAdCreative) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.RenderType = AdRenderType(v)
	}
	return nil
}

func (p *ResAdCreative) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.PlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *ResAdCreative) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.EffectiveShowTime = TimeInt(v)
	}
	return nil
}

func (p *ResAdCreative) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.CompleteShowTime = TimeInt(v)
	}
	return nil
}

func (p *ResAdCreative) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.AutoClose = v
	}
	return nil
}

func (p *ResAdCreative) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.DisableRotate = v
	}
	return nil
}

func (p *ResAdCreative) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.EndTime = TimeInt(v)
	}
	return nil
}

func (p *ResAdCreative) readField49(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PoiInfoList = make([]*POIInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem16 := NewPOIInfo()
		if err := _elem16.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem16)
		}
		p.PoiInfoList = append(p.PoiInfoList, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResAdCreative) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *ResAdCreative) readField65(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 65: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *ResAdCreative) readField66(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 66: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *ResAdCreative) readField67(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 67: %s", err)
	} else {
		p.ActionCall = v
	}
	return nil
}

func (p *ResAdCreative) readField68(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 68: %s", err)
	} else {
		p.ActionSMSNumber = v
	}
	return nil
}

func (p *ResAdCreative) readField69(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 69: %s", err)
	} else {
		p.ActionSMSMessage = v
	}
	return nil
}

func (p *ResAdCreative) readField70(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 70: %s", err)
	} else {
		p.ActionMailAddress = v
	}
	return nil
}

func (p *ResAdCreative) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.ActionMailSubject = v
	}
	return nil
}

func (p *ResAdCreative) readField72(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 72: %s", err)
	} else {
		p.ActionMailBody = v
	}
	return nil
}

func (p *ResAdCreative) readField73(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 73: %s", err)
	} else {
		p.ActionUrlFailsafe = v
	}
	return nil
}

func (p *ResAdCreative) readField74(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 74: %s", err)
	} else {
		p.ImpressionTrackingUrl = v
	}
	return nil
}

func (p *ResAdCreative) readField75(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 75: %s", err)
	} else {
		p.ClickTrackingUrl = v
	}
	return nil
}

func (p *ResAdCreative) readField76(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 76: %s", err)
	} else {
		p.CloseButtonPosition = CloseButtonPosition(v)
	}
	return nil
}

func (p *ResAdCreative) readField77(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 77: %s", err)
	} else {
		p.TemplateConfig = v
	}
	return nil
}

func (p *ResAdCreative) readField78(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 78: %s", err)
	} else {
		p.JsonInfo = v
	}
	return nil
}

func (p *ResAdCreative) readField79(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 79: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *ResAdCreative) readField80(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 80: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *ResAdCreative) readField81(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 81: %s", err)
	} else {
		p.AppChannelId = v
	}
	return nil
}

func (p *ResAdCreative) readField82(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 82: %s", err)
	} else {
		p.AdLevel = v
	}
	return nil
}

func (p *ResAdCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResAdCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := p.writeField65(oprot); err != nil {
		return err
	}
	if err := p.writeField66(oprot); err != nil {
		return err
	}
	if err := p.writeField67(oprot); err != nil {
		return err
	}
	if err := p.writeField68(oprot); err != nil {
		return err
	}
	if err := p.writeField69(oprot); err != nil {
		return err
	}
	if err := p.writeField70(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField73(oprot); err != nil {
		return err
	}
	if err := p.writeField74(oprot); err != nil {
		return err
	}
	if err := p.writeField75(oprot); err != nil {
		return err
	}
	if err := p.writeField76(oprot); err != nil {
		return err
	}
	if err := p.writeField77(oprot); err != nil {
		return err
	}
	if err := p.writeField78(oprot); err != nil {
		return err
	}
	if err := p.writeField79(oprot); err != nil {
		return err
	}
	if err := p.writeField80(oprot); err != nil {
		return err
	}
	if err := p.writeField81(oprot); err != nil {
		return err
	}
	if err := p.writeField82(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResAdCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Templates != nil {
		if err := oprot.WriteFieldBegin("templates", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:templates: %s", p, err)
		}
		if err := p.Templates.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Templates)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:templates: %s", p, err)
		}
	}
	return err
}

func (p *ResAdCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TemplateList != nil {
		if err := oprot.WriteFieldBegin("templateList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:templateList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TemplateList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TemplateList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:templateList: %s", p, err)
		}
	}
	return err
}

func (p *ResAdCreative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("long_text", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:long_text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LongText)); err != nil {
		return fmt.Errorf("%T.long_text (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:long_text: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:text: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon_type", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:icon_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IconType)); err != nil {
		return fmt.Errorf("%T.icon_type (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:icon_type: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fore_color", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:fore_color: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ForeColor)); err != nil {
		return fmt.Errorf("%T.fore_color (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:fore_color: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bg_color", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:bg_color: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BgColor)); err != nil {
		return fmt.Errorf("%T.bg_color (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:bg_color: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_url", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:display_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayUrl)); err != nil {
		return fmt.Errorf("%T.display_url (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:display_url: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url_open_type", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:url_open_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UrlOpenType)); err != nil {
		return fmt.Errorf("%T.url_open_type (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:url_open_type: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("down_app_name", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:down_app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DownAppName)); err != nil {
		return fmt.Errorf("%T.down_app_name (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:down_app_name: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_preload", thrift.BOOL, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:is_preload: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsPreload)); err != nil {
		return fmt.Errorf("%T.is_preload (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:is_preload: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_auto_run", thrift.BOOL, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:is_auto_run: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAutoRun)); err != nil {
		return fmt.Errorf("%T.is_auto_run (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:is_auto_run: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_click_comfirm", thrift.BOOL, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:is_click_comfirm: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsClickComfirm)); err != nil {
		return fmt.Errorf("%T.is_click_comfirm (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:is_click_comfirm: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:package_name: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_version", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:package_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageVersion)); err != nil {
		return fmt.Errorf("%T.package_version (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:package_version: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detect_package_name", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:detect_package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DetectPackageName)); err != nil {
		return fmt.Errorf("%T.detect_package_name (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:detect_package_name: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detect_package_code", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:detect_package_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DetectPackageCode)); err != nil {
		return fmt.Errorf("%T.detect_package_code (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:detect_package_code: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("alt_text", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:alt_text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AltText)); err != nil {
		return fmt.Errorf("%T.alt_text (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:alt_text: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landing_direction", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:landing_direction: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LandingDirection)); err != nil {
		return fmt.Errorf("%T.landing_direction (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:landing_direction: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_version_code", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:package_version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PackageVersionCode)); err != nil {
		return fmt.Errorf("%T.package_version_code (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:package_version_code: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("template_code", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:template_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TemplateCode)); err != nil {
		return fmt.Errorf("%T.template_code (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:template_code: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("render_type", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:render_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RenderType)); err != nil {
		return fmt.Errorf("%T.render_type (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:render_type: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placement_type", thrift.I32, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:placement_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placement_type (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:placement_type: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effective_show_time", thrift.I64, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:effective_show_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EffectiveShowTime)); err != nil {
		return fmt.Errorf("%T.effective_show_time (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:effective_show_time: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("complete_show_time", thrift.I64, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:complete_show_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CompleteShowTime)); err != nil {
		return fmt.Errorf("%T.complete_show_time (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:complete_show_time: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auto_close", thrift.BOOL, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:auto_close: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AutoClose)); err != nil {
		return fmt.Errorf("%T.auto_close (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:auto_close: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disable_rotate", thrift.BOOL, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:disable_rotate: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.DisableRotate)); err != nil {
		return fmt.Errorf("%T.disable_rotate (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:disable_rotate: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I64, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:end_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:end_time: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField49(oprot thrift.TProtocol) (err error) {
	if p.PoiInfoList != nil {
		if err := oprot.WriteFieldBegin("poi_info_list", thrift.LIST, 49); err != nil {
			return fmt.Errorf("%T write field begin error 49:poi_info_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PoiInfoList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PoiInfoList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 49:poi_info_list: %s", p, err)
		}
	}
	return err
}

func (p *ResAdCreative) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionUrl", thrift.STRING, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:actionUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.actionUrl (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:actionUrl: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField65(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 65); err != nil {
		return fmt.Errorf("%T write field begin error 65:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (65) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 65:longitude: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField66(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 66); err != nil {
		return fmt.Errorf("%T write field begin error 66:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (66) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 66:latitude: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField67(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionCall", thrift.STRING, 67); err != nil {
		return fmt.Errorf("%T write field begin error 67:actionCall: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionCall)); err != nil {
		return fmt.Errorf("%T.actionCall (67) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 67:actionCall: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField68(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionSMSNumber", thrift.STRING, 68); err != nil {
		return fmt.Errorf("%T write field begin error 68:actionSMSNumber: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionSMSNumber)); err != nil {
		return fmt.Errorf("%T.actionSMSNumber (68) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 68:actionSMSNumber: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField69(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionSMSMessage", thrift.STRING, 69); err != nil {
		return fmt.Errorf("%T write field begin error 69:actionSMSMessage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionSMSMessage)); err != nil {
		return fmt.Errorf("%T.actionSMSMessage (69) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 69:actionSMSMessage: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField70(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailAddress", thrift.STRING, 70); err != nil {
		return fmt.Errorf("%T write field begin error 70:actionMailAddress: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailAddress)); err != nil {
		return fmt.Errorf("%T.actionMailAddress (70) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 70:actionMailAddress: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailSubject", thrift.STRING, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:actionMailSubject: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailSubject)); err != nil {
		return fmt.Errorf("%T.actionMailSubject (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:actionMailSubject: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField72(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailBody", thrift.STRING, 72); err != nil {
		return fmt.Errorf("%T write field begin error 72:actionMailBody: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailBody)); err != nil {
		return fmt.Errorf("%T.actionMailBody (72) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 72:actionMailBody: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField73(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionUrlFailsafe", thrift.STRING, 73); err != nil {
		return fmt.Errorf("%T write field begin error 73:actionUrlFailsafe: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrlFailsafe)); err != nil {
		return fmt.Errorf("%T.actionUrlFailsafe (73) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 73:actionUrlFailsafe: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField74(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionTrackingUrl", thrift.STRING, 74); err != nil {
		return fmt.Errorf("%T write field begin error 74:impressionTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpressionTrackingUrl)); err != nil {
		return fmt.Errorf("%T.impressionTrackingUrl (74) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 74:impressionTrackingUrl: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField75(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickTrackingUrl", thrift.STRING, 75); err != nil {
		return fmt.Errorf("%T write field begin error 75:clickTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickTrackingUrl)); err != nil {
		return fmt.Errorf("%T.clickTrackingUrl (75) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 75:clickTrackingUrl: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField76(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closeButtonPosition", thrift.I32, 76); err != nil {
		return fmt.Errorf("%T write field begin error 76:closeButtonPosition: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CloseButtonPosition)); err != nil {
		return fmt.Errorf("%T.closeButtonPosition (76) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 76:closeButtonPosition: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField77(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("templateConfig", thrift.I64, 77); err != nil {
		return fmt.Errorf("%T write field begin error 77:templateConfig: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TemplateConfig)); err != nil {
		return fmt.Errorf("%T.templateConfig (77) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 77:templateConfig: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField78(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jsonInfo", thrift.STRING, 78); err != nil {
		return fmt.Errorf("%T write field begin error 78:jsonInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.JsonInfo)); err != nil {
		return fmt.Errorf("%T.jsonInfo (78) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 78:jsonInfo: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField79(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I64, 79); err != nil {
		return fmt.Errorf("%T write field begin error 79:start_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (79) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 79:start_time: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField80(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.I32, 80); err != nil {
		return fmt.Errorf("%T write field begin error 80:app_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (80) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 80:app_id: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField81(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_channel_id", thrift.I32, 81); err != nil {
		return fmt.Errorf("%T write field begin error 81:app_channel_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppChannelId)); err != nil {
		return fmt.Errorf("%T.app_channel_id (81) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 81:app_channel_id: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) writeField82(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_level", thrift.I32, 82); err != nil {
		return fmt.Errorf("%T write field begin error 82:ad_level: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdLevel)); err != nil {
		return fmt.Errorf("%T.ad_level (82) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 82:ad_level: %s", p, err)
	}
	return err
}

func (p *ResAdCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResAdCreative(%+v)", *p)
}

type ResAd struct {
	CostType     CostType       `thrift:"cost_type,1" json:"cost_type"`
	CreativeType AdCreativeType `thrift:"creative_type,2" json:"creative_type"`
	// unused field # 3
	CreativeId      AdCreativeIdInt          `thrift:"creative_id,4" json:"creative_id"`
	StryId          AdStrategyIdInt          `thrift:"stry_id,5" json:"stry_id"`
	PlanId          AdPlanIdInt              `thrift:"plan_id,6" json:"plan_id"`
	AdUid           UidInt                   `thrift:"ad_uid,7" json:"ad_uid"`
	Width           int32                    `thrift:"width,8" json:"width"`
	High            int32                    `thrift:"high,9" json:"high"`
	Bid             Amount                   `thrift:"bid,10" json:"bid"`
	Price           Amount                   `thrift:"price,11" json:"price"`
	MatchClass      int32                    `thrift:"match_class,12" json:"match_class"`
	Ctr             float64                  `thrift:"ctr,13" json:"ctr"`
	Word            string                   `thrift:"word,14" json:"word"`
	ActionType      AdActionType             `thrift:"action_type,15" json:"action_type"`
	Creative        *ResAdCreative           `thrift:"creative,16" json:"creative"`
	AdserverPrint   string                   `thrift:"adserver_print,17" json:"adserver_print"`
	UvImpcnt        int32                    `thrift:"uv_impcnt,18" json:"uv_impcnt"`
	UvClkcnt        int32                    `thrift:"uv_clkcnt,19" json:"uv_clkcnt"`
	UvAttDen        int32                    `thrift:"uv_att_den,20" json:"uv_att_den"`
	UvAttNum        int32                    `thrift:"uv_att_num,21" json:"uv_att_num"`
	MediaBid        Amount                   `thrift:"media_bid,22" json:"media_bid"`
	MediaPrice      Amount                   `thrift:"media_price,23" json:"media_price"`
	UserTagList     []*tag_types.DemoTagging `thrift:"user_tag_list,24" json:"user_tag_list"`
	AdTagList       []*tag_types.DemoTagging `thrift:"ad_tag_list,25" json:"ad_tag_list"`
	ImpDecision     []*tag_types.ImpDecision `thrift:"imp_decision,26" json:"imp_decision"`
	RichmediaFormat RichMediaCodeInt         `thrift:"richmedia_format,27" json:"richmedia_format"`
	RealBid         Amount                   `thrift:"real_bid,28" json:"real_bid"`
	RealPrice       Amount                   `thrift:"real_price,29" json:"real_price"`
	SponsorType     SponsorType              `thrift:"sponsor_type,30" json:"sponsor_type"`
	Category        AdCategory               `thrift:"category,31" json:"category"`
	StrategyType    AdStrategyType           `thrift:"strategy_type,32" json:"strategy_type"`
	ImpFreqSet      []*common.FreqInfo       `thrift:"impFreqSet,33" json:"impFreqSet"`
	ClkFreqSet      []*common.FreqInfo       `thrift:"clkFreqSet,34" json:"clkFreqSet"`
	// unused field # 35
	ExtInfo      map[string]string `thrift:"extInfo,36" json:"extInfo"`
	OtherExtInfo map[string]string `thrift:"otherExtInfo,37" json:"otherExtInfo"`
	AppkeySign   int32             `thrift:"appkeySign,38" json:"appkeySign"`
}

func NewResAd() *ResAd {
	return &ResAd{
		CostType: math.MinInt32 - 1, // unset sentinal value

		CreativeType: math.MinInt32 - 1, // unset sentinal value

		ActionType: math.MinInt32 - 1, // unset sentinal value

		SponsorType: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value

		StrategyType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ResAd) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *ResAd) IsSetCreativeType() bool {
	return int64(p.CreativeType) != math.MinInt32-1
}

func (p *ResAd) IsSetActionType() bool {
	return int64(p.ActionType) != math.MinInt32-1
}

func (p *ResAd) IsSetSponsorType() bool {
	return int64(p.SponsorType) != math.MinInt32-1
}

func (p *ResAd) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *ResAd) IsSetStrategyType() bool {
	return int64(p.StrategyType) != math.MinInt32-1
}

func (p *ResAd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.LIST {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.LIST {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.LIST {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I64 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I64 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.LIST {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.LIST {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.MAP {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.MAP {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResAd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *ResAd) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CreativeType = AdCreativeType(v)
	}
	return nil
}

func (p *ResAd) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CreativeId = AdCreativeIdInt(v)
	}
	return nil
}

func (p *ResAd) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StryId = AdStrategyIdInt(v)
	}
	return nil
}

func (p *ResAd) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PlanId = AdPlanIdInt(v)
	}
	return nil
}

func (p *ResAd) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AdUid = UidInt(v)
	}
	return nil
}

func (p *ResAd) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *ResAd) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.High = v
	}
	return nil
}

func (p *ResAd) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Bid = Amount(v)
	}
	return nil
}

func (p *ResAd) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Price = Amount(v)
	}
	return nil
}

func (p *ResAd) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.MatchClass = v
	}
	return nil
}

func (p *ResAd) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Ctr = v
	}
	return nil
}

func (p *ResAd) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Word = v
	}
	return nil
}

func (p *ResAd) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ActionType = AdActionType(v)
	}
	return nil
}

func (p *ResAd) readField16(iprot thrift.TProtocol) error {
	p.Creative = NewResAdCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *ResAd) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.AdserverPrint = v
	}
	return nil
}

func (p *ResAd) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.UvImpcnt = v
	}
	return nil
}

func (p *ResAd) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.UvClkcnt = v
	}
	return nil
}

func (p *ResAd) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.UvAttDen = v
	}
	return nil
}

func (p *ResAd) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.UvAttNum = v
	}
	return nil
}

func (p *ResAd) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.MediaBid = Amount(v)
	}
	return nil
}

func (p *ResAd) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.MediaPrice = Amount(v)
	}
	return nil
}

func (p *ResAd) readField24(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.UserTagList = make([]*tag_types.DemoTagging, 0, size)
	for i := 0; i < size; i++ {
		_elem17 := tag_types.NewDemoTagging()
		if err := _elem17.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem17)
		}
		p.UserTagList = append(p.UserTagList, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResAd) readField25(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdTagList = make([]*tag_types.DemoTagging, 0, size)
	for i := 0; i < size; i++ {
		_elem18 := tag_types.NewDemoTagging()
		if err := _elem18.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem18)
		}
		p.AdTagList = append(p.AdTagList, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResAd) readField26(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpDecision = make([]*tag_types.ImpDecision, 0, size)
	for i := 0; i < size; i++ {
		_elem19 := tag_types.NewImpDecision()
		if err := _elem19.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem19)
		}
		p.ImpDecision = append(p.ImpDecision, _elem19)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResAd) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.RichmediaFormat = RichMediaCodeInt(v)
	}
	return nil
}

func (p *ResAd) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.RealBid = Amount(v)
	}
	return nil
}

func (p *ResAd) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.RealPrice = Amount(v)
	}
	return nil
}

func (p *ResAd) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.SponsorType = SponsorType(v)
	}
	return nil
}

func (p *ResAd) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Category = AdCategory(v)
	}
	return nil
}

func (p *ResAd) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.StrategyType = AdStrategyType(v)
	}
	return nil
}

func (p *ResAd) readField33(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpFreqSet = make([]*common.FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem20 := common.NewFreqInfo()
		if err := _elem20.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem20)
		}
		p.ImpFreqSet = append(p.ImpFreqSet, _elem20)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResAd) readField34(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClkFreqSet = make([]*common.FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem21 := common.NewFreqInfo()
		if err := _elem21.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem21)
		}
		p.ClkFreqSet = append(p.ClkFreqSet, _elem21)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResAd) readField36(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key22 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key22 = v
		}
		var _val23 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val23 = v
		}
		p.ExtInfo[_key22] = _val23
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ResAd) readField37(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.OtherExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key24 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key24 = v
		}
		var _val25 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val25 = v
		}
		p.OtherExtInfo[_key24] = _val25
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ResAd) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.AppkeySign = v
	}
	return nil
}

func (p *ResAd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResAd"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResAd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cost_type: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_type", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:creative_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeType)); err != nil {
		return fmt.Errorf("%T.creative_type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:creative_type: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:creative_id: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stry_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:stry_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StryId)); err != nil {
		return fmt.Errorf("%T.stry_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:stry_id: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:plan_id: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_uid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ad_uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUid)); err != nil {
		return fmt.Errorf("%T.ad_uid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ad_uid: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:width: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("high", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:high: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.High)); err != nil {
		return fmt.Errorf("%T.high (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:high: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:bid: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:price: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("match_class", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:match_class: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MatchClass)); err != nil {
		return fmt.Errorf("%T.match_class (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:match_class: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctr", thrift.DOUBLE, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:ctr: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Ctr)); err != nil {
		return fmt.Errorf("%T.ctr (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:ctr: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("word", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:word: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Word)); err != nil {
		return fmt.Errorf("%T.word (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:word: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_type", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:action_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return fmt.Errorf("%T.action_type (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:action_type: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField16(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:creative: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adserver_print", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:adserver_print: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdserverPrint)); err != nil {
		return fmt.Errorf("%T.adserver_print (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:adserver_print: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uv_impcnt", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:uv_impcnt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvImpcnt)); err != nil {
		return fmt.Errorf("%T.uv_impcnt (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:uv_impcnt: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uv_clkcnt", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:uv_clkcnt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvClkcnt)); err != nil {
		return fmt.Errorf("%T.uv_clkcnt (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:uv_clkcnt: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uv_att_den", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:uv_att_den: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvAttDen)); err != nil {
		return fmt.Errorf("%T.uv_att_den (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:uv_att_den: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uv_att_num", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:uv_att_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvAttNum)); err != nil {
		return fmt.Errorf("%T.uv_att_num (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:uv_att_num: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_bid", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:media_bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaBid)); err != nil {
		return fmt.Errorf("%T.media_bid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:media_bid: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_price", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:media_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaPrice)); err != nil {
		return fmt.Errorf("%T.media_price (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:media_price: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField24(oprot thrift.TProtocol) (err error) {
	if p.UserTagList != nil {
		if err := oprot.WriteFieldBegin("user_tag_list", thrift.LIST, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:user_tag_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.UserTagList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.UserTagList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:user_tag_list: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField25(oprot thrift.TProtocol) (err error) {
	if p.AdTagList != nil {
		if err := oprot.WriteFieldBegin("ad_tag_list", thrift.LIST, 25); err != nil {
			return fmt.Errorf("%T write field begin error 25:ad_tag_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdTagList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdTagList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 25:ad_tag_list: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField26(oprot thrift.TProtocol) (err error) {
	if p.ImpDecision != nil {
		if err := oprot.WriteFieldBegin("imp_decision", thrift.LIST, 26); err != nil {
			return fmt.Errorf("%T write field begin error 26:imp_decision: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImpDecision)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpDecision {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 26:imp_decision: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("richmedia_format", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:richmedia_format: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RichmediaFormat)); err != nil {
		return fmt.Errorf("%T.richmedia_format (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:richmedia_format: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("real_bid", thrift.I64, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:real_bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RealBid)); err != nil {
		return fmt.Errorf("%T.real_bid (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:real_bid: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("real_price", thrift.I64, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:real_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RealPrice)); err != nil {
		return fmt.Errorf("%T.real_price (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:real_price: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_type", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:sponsor_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorType)); err != nil {
		return fmt.Errorf("%T.sponsor_type (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:sponsor_type: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:category: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_type", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:strategy_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyType)); err != nil {
		return fmt.Errorf("%T.strategy_type (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:strategy_type: %s", p, err)
	}
	return err
}

func (p *ResAd) writeField33(oprot thrift.TProtocol) (err error) {
	if p.ImpFreqSet != nil {
		if err := oprot.WriteFieldBegin("impFreqSet", thrift.LIST, 33); err != nil {
			return fmt.Errorf("%T write field begin error 33:impFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImpFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpFreqSet {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 33:impFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField34(oprot thrift.TProtocol) (err error) {
	if p.ClkFreqSet != nil {
		if err := oprot.WriteFieldBegin("clkFreqSet", thrift.LIST, 34); err != nil {
			return fmt.Errorf("%T write field begin error 34:clkFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ClkFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClkFreqSet {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 34:clkFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField36(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 36); err != nil {
			return fmt.Errorf("%T write field begin error 36:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 36:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField37(oprot thrift.TProtocol) (err error) {
	if p.OtherExtInfo != nil {
		if err := oprot.WriteFieldBegin("otherExtInfo", thrift.MAP, 37); err != nil {
			return fmt.Errorf("%T write field begin error 37:otherExtInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.OtherExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.OtherExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 37:otherExtInfo: %s", p, err)
		}
	}
	return err
}

func (p *ResAd) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appkeySign", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:appkeySign: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppkeySign)); err != nil {
		return fmt.Errorf("%T.appkeySign (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:appkeySign: %s", p, err)
	}
	return err
}

func (p *ResAd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResAd(%+v)", *p)
}

type AdserverAdRes struct {
	MediaId                    MediaIdInt              `thrift:"media_id,1" json:"media_id"`
	Mediauid                   UidInt                  `thrift:"mediauid,2" json:"mediauid"`
	MediaType                  MediaType               `thrift:"media_type,3" json:"media_type"`
	AdNum                      int32                   `thrift:"ad_num,4" json:"ad_num"`
	AdInfo                     []*ResAd                `thrift:"ad_info,5" json:"ad_info"`
	ResStatus                  ResStatus               `thrift:"res_status,6" json:"res_status"`
	PlacementSettingExtInfo    string                  `thrift:"placementSettingExtInfo,7" json:"placementSettingExtInfo"`
	EmptyType                  int32                   `thrift:"empty_type,8" json:"empty_type"`
	ExtInfo                    string                  `thrift:"ext_info,9" json:"ext_info"`
	MuteVideoSetting           int32                   `thrift:"muteVideoSetting,10" json:"muteVideoSetting"`
	PreIssuedResource          []*common.ResourceGroup `thrift:"preIssuedResource,11" json:"preIssuedResource"`
	PlacementSettingExtInfoMap map[string]string       `thrift:"placementSettingExtInfoMap,12" json:"placementSettingExtInfoMap"`
	MediaPlacementType         AdPlacementType         `thrift:"media_placement_type,13" json:"media_placement_type"`
	AdContainers               []*AdContainer          `thrift:"ad_containers,14" json:"ad_containers"`
	AdChannels                 []*common.Channel       `thrift:"ad_channels,15" json:"ad_channels"`
}

func NewAdserverAdRes() *AdserverAdRes {
	return &AdserverAdRes{
		MediaType: math.MinInt32 - 1, // unset sentinal value

		ResStatus: math.MinInt32 - 1, // unset sentinal value

		MediaPlacementType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdserverAdRes) IsSetMediaType() bool {
	return int64(p.MediaType) != math.MinInt32-1
}

func (p *AdserverAdRes) IsSetResStatus() bool {
	return int64(p.ResStatus) != math.MinInt32-1
}

func (p *AdserverAdRes) IsSetMediaPlacementType() bool {
	return int64(p.MediaPlacementType) != math.MinInt32-1
}

func (p *AdserverAdRes) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.MAP {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdserverAdRes) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MediaId = MediaIdInt(v)
	}
	return nil
}

func (p *AdserverAdRes) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mediauid = UidInt(v)
	}
	return nil
}

func (p *AdserverAdRes) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaType = MediaType(v)
	}
	return nil
}

func (p *AdserverAdRes) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdNum = v
	}
	return nil
}

func (p *AdserverAdRes) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdInfo = make([]*ResAd, 0, size)
	for i := 0; i < size; i++ {
		_elem26 := NewResAd()
		if err := _elem26.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem26)
		}
		p.AdInfo = append(p.AdInfo, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdserverAdRes) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ResStatus = ResStatus(v)
	}
	return nil
}

func (p *AdserverAdRes) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PlacementSettingExtInfo = v
	}
	return nil
}

func (p *AdserverAdRes) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.EmptyType = v
	}
	return nil
}

func (p *AdserverAdRes) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ExtInfo = v
	}
	return nil
}

func (p *AdserverAdRes) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.MuteVideoSetting = v
	}
	return nil
}

func (p *AdserverAdRes) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PreIssuedResource = make([]*common.ResourceGroup, 0, size)
	for i := 0; i < size; i++ {
		_elem27 := common.NewResourceGroup()
		if err := _elem27.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem27)
		}
		p.PreIssuedResource = append(p.PreIssuedResource, _elem27)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdserverAdRes) readField12(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.PlacementSettingExtInfoMap = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key28 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key28 = v
		}
		var _val29 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val29 = v
		}
		p.PlacementSettingExtInfoMap[_key28] = _val29
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdserverAdRes) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.MediaPlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *AdserverAdRes) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdContainers = make([]*AdContainer, 0, size)
	for i := 0; i < size; i++ {
		_elem30 := NewAdContainer()
		if err := _elem30.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem30)
		}
		p.AdContainers = append(p.AdContainers, _elem30)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdserverAdRes) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdChannels = make([]*common.Channel, 0, size)
	for i := 0; i < size; i++ {
		_elem31 := common.NewChannel()
		if err := _elem31.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem31)
		}
		p.AdChannels = append(p.AdChannels, _elem31)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdserverAdRes) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdserverAdRes"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdserverAdRes) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:media_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaId)); err != nil {
		return fmt.Errorf("%T.media_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:media_id: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediauid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mediauid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mediauid)); err != nil {
		return fmt.Errorf("%T.mediauid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mediauid: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_type", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:media_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaType)); err != nil {
		return fmt.Errorf("%T.media_type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:media_type: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_num", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:ad_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdNum)); err != nil {
		return fmt.Errorf("%T.ad_num (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:ad_num: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField5(oprot thrift.TProtocol) (err error) {
	if p.AdInfo != nil {
		if err := oprot.WriteFieldBegin("ad_info", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:ad_info: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdInfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdInfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:ad_info: %s", p, err)
		}
	}
	return err
}

func (p *AdserverAdRes) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetResStatus() {
		if err := oprot.WriteFieldBegin("res_status", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:res_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResStatus)); err != nil {
			return fmt.Errorf("%T.res_status (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:res_status: %s", p, err)
		}
	}
	return err
}

func (p *AdserverAdRes) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementSettingExtInfo", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:placementSettingExtInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PlacementSettingExtInfo)); err != nil {
		return fmt.Errorf("%T.placementSettingExtInfo (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:placementSettingExtInfo: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("empty_type", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:empty_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EmptyType)); err != nil {
		return fmt.Errorf("%T.empty_type (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:empty_type: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext_info", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ext_info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExtInfo)); err != nil {
		return fmt.Errorf("%T.ext_info (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ext_info: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("muteVideoSetting", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:muteVideoSetting: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MuteVideoSetting)); err != nil {
		return fmt.Errorf("%T.muteVideoSetting (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:muteVideoSetting: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField11(oprot thrift.TProtocol) (err error) {
	if p.PreIssuedResource != nil {
		if err := oprot.WriteFieldBegin("preIssuedResource", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:preIssuedResource: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.PreIssuedResource)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PreIssuedResource {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:preIssuedResource: %s", p, err)
		}
	}
	return err
}

func (p *AdserverAdRes) writeField12(oprot thrift.TProtocol) (err error) {
	if p.PlacementSettingExtInfoMap != nil {
		if err := oprot.WriteFieldBegin("placementSettingExtInfoMap", thrift.MAP, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:placementSettingExtInfoMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.PlacementSettingExtInfoMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.PlacementSettingExtInfoMap {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:placementSettingExtInfoMap: %s", p, err)
		}
	}
	return err
}

func (p *AdserverAdRes) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_placement_type", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:media_placement_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaPlacementType)); err != nil {
		return fmt.Errorf("%T.media_placement_type (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:media_placement_type: %s", p, err)
	}
	return err
}

func (p *AdserverAdRes) writeField14(oprot thrift.TProtocol) (err error) {
	if p.AdContainers != nil {
		if err := oprot.WriteFieldBegin("ad_containers", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:ad_containers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdContainers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdContainers {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:ad_containers: %s", p, err)
		}
	}
	return err
}

func (p *AdserverAdRes) writeField15(oprot thrift.TProtocol) (err error) {
	if p.AdChannels != nil {
		if err := oprot.WriteFieldBegin("ad_channels", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:ad_channels: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdChannels)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdChannels {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:ad_channels: %s", p, err)
		}
	}
	return err
}

func (p *AdserverAdRes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdserverAdRes(%+v)", *p)
}

type AdserverDetRes struct {
	RefreshInterval int32                `thrift:"refresh_interval,1" json:"refresh_interval"`
	Disabled        bool                 `thrift:"disabled,2" json:"disabled"`
	ConfigTm        TimeInt              `thrift:"config_tm,3" json:"config_tm"`
	ResTm           TimeInt              `thrift:"res_tm,4" json:"res_tm"`
	TestMode        MediaTestModeSetting `thrift:"testMode,5" json:"testMode"`
	PrisionTm       TimeInt              `thrift:"prision_tm,6" json:"prision_tm"`
	ResName         []string             `thrift:"res_name,7" json:"res_name"`
}

func NewAdserverDetRes() *AdserverDetRes {
	return &AdserverDetRes{
		TestMode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdserverDetRes) IsSetTestMode() bool {
	return int64(p.TestMode) != math.MinInt32-1
}

func (p *AdserverDetRes) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdserverDetRes) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.RefreshInterval = v
	}
	return nil
}

func (p *AdserverDetRes) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Disabled = v
	}
	return nil
}

func (p *AdserverDetRes) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ConfigTm = TimeInt(v)
	}
	return nil
}

func (p *AdserverDetRes) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ResTm = TimeInt(v)
	}
	return nil
}

func (p *AdserverDetRes) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TestMode = MediaTestModeSetting(v)
	}
	return nil
}

func (p *AdserverDetRes) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PrisionTm = TimeInt(v)
	}
	return nil
}

func (p *AdserverDetRes) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResName = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem32 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem32 = v
		}
		p.ResName = append(p.ResName, _elem32)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdserverDetRes) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdserverDetRes"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdserverDetRes) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("refresh_interval", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:refresh_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RefreshInterval)); err != nil {
		return fmt.Errorf("%T.refresh_interval (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:refresh_interval: %s", p, err)
	}
	return err
}

func (p *AdserverDetRes) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disabled", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:disabled: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Disabled)); err != nil {
		return fmt.Errorf("%T.disabled (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:disabled: %s", p, err)
	}
	return err
}

func (p *AdserverDetRes) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("config_tm", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:config_tm: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConfigTm)); err != nil {
		return fmt.Errorf("%T.config_tm (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:config_tm: %s", p, err)
	}
	return err
}

func (p *AdserverDetRes) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("res_tm", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:res_tm: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ResTm)); err != nil {
		return fmt.Errorf("%T.res_tm (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:res_tm: %s", p, err)
	}
	return err
}

func (p *AdserverDetRes) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("testMode", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:testMode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TestMode)); err != nil {
		return fmt.Errorf("%T.testMode (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:testMode: %s", p, err)
	}
	return err
}

func (p *AdserverDetRes) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("prision_tm", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:prision_tm: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PrisionTm)); err != nil {
		return fmt.Errorf("%T.prision_tm (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:prision_tm: %s", p, err)
	}
	return err
}

func (p *AdserverDetRes) writeField7(oprot thrift.TProtocol) (err error) {
	if p.ResName != nil {
		if err := oprot.WriteFieldBegin("res_name", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:res_name: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ResName)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResName {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:res_name: %s", p, err)
		}
	}
	return err
}

func (p *AdserverDetRes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdserverDetRes(%+v)", *p)
}

type AdserverRes struct {
	ResType ResType         `thrift:"res_type,1" json:"res_type"`
	AdRes   *AdserverAdRes  `thrift:"ad_res,2" json:"ad_res"`
	DetRes  *AdserverDetRes `thrift:"det_res,3" json:"det_res"`
}

func NewAdserverRes() *AdserverRes {
	return &AdserverRes{
		ResType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdserverRes) IsSetResType() bool {
	return int64(p.ResType) != math.MinInt32-1
}

func (p *AdserverRes) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdserverRes) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ResType = ResType(v)
	}
	return nil
}

func (p *AdserverRes) readField2(iprot thrift.TProtocol) error {
	p.AdRes = NewAdserverAdRes()
	if err := p.AdRes.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AdRes)
	}
	return nil
}

func (p *AdserverRes) readField3(iprot thrift.TProtocol) error {
	p.DetRes = NewAdserverDetRes()
	if err := p.DetRes.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DetRes)
	}
	return nil
}

func (p *AdserverRes) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdserverRes"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdserverRes) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetResType() {
		if err := oprot.WriteFieldBegin("res_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:res_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResType)); err != nil {
			return fmt.Errorf("%T.res_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:res_type: %s", p, err)
		}
	}
	return err
}

func (p *AdserverRes) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AdRes != nil {
		if err := oprot.WriteFieldBegin("ad_res", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ad_res: %s", p, err)
		}
		if err := p.AdRes.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AdRes)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ad_res: %s", p, err)
		}
	}
	return err
}

func (p *AdserverRes) writeField3(oprot thrift.TProtocol) (err error) {
	if p.DetRes != nil {
		if err := oprot.WriteFieldBegin("det_res", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:det_res: %s", p, err)
		}
		if err := p.DetRes.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DetRes)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:det_res: %s", p, err)
		}
	}
	return err
}

func (p *AdserverRes) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdserverRes(%+v)", *p)
}

type SearchException struct {
	Code    SearchErrorCode `thrift:"code,1" json:"code"`
	Message string          `thrift:"message,2" json:"message"`
}

func NewSearchException() *SearchException {
	return &SearchException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SearchException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *SearchException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = SearchErrorCode(v)
	}
	return nil
}

func (p *SearchException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *SearchException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SearchException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *SearchException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *SearchException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchException(%+v)", *p)
}
