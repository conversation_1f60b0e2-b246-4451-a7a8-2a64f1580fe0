// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appserver

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appserver_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = appserver_types.GoUnusedProtection__

type AppServiceBackEnd interface {
	AppServiceFrontEnd
	//app后台服务
	//
	//前端提供app的只读服务，后端则可以编辑app信息

	// 创建app信息
	//
	// 后台web创建的app, domain='g.domob.cn'
	// 包名必须唯一，如果库中已经存在这个包，请使用edit接口
	//
	// @return 新创建的app的id
	//
	// Parameters:
	//  - Header
	//  - App
	CreateAppMain(header *common.RequestHeader, app *appserver_types.AppMain) (r int32, ae *AppException, err error)
	// 后台编辑app详细信息
	//
	// 包名和id不可以修改，是后台识别编辑哪个app的key
	// 如果找不到包名抛出异常,
	// app.appid可以为空或者0，如果设置则需要与系统中package_name对应的appid一致
	//
	// 被编辑过的包，内容不再自动更新，即抓取系统抓取的内容不会覆盖编辑过的内容。
	// 这也会可能导致我们的信息不够新。
	// 因此，如果后台抓取到更新的内容，会记录其信息到另外的表，并标记
	// app的has_update为True
	//
	// Parameters:
	//  - Header
	//  - App
	//  - SetEdited
	EditAppMain(header *common.RequestHeader, app *appserver_types.AppMain, set_edited bool) (ae *AppException, err error)
	// 更新appmain的信息
	// 这个接口用于抓取端更新库信息。update不会覆盖后台编辑的内容
	//
	// 如果找不到对应的包，会创建一条记录
	//
	// @return 如果创建或者更新了包，则返回那个app的id，否则返回0
	//
	// Parameters:
	//  - Header
	//  - App
	UpdateAppMain(header *common.RequestHeader, app *appserver_types.AppMain) (r int32, ae *AppException, err error)
	// 删除一个app，使之不在list中出现
	// permanent=False时标记删除，数据还在服务器中
	// 否则清理数据库，删除主记录以及所有附属资源
	//
	// Parameters:
	//  - Header
	//  - PackageName
	//  - Permanent
	DeleteAppMain(header *common.RequestHeader, package_name string, permanent bool) (ae *AppException, err error)
	// 获取标记删除了的记录
	//
	// Parameters:
	//  - Header
	//  - Category
	//  - Offset
	//  - Limit
	ListDeletedAppMain(header *common.RequestHeader, category Category, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error)
	// 获取有升级的app列表
	//
	// Parameters:
	//  - Header
	//  - Market
	//  - Category
	//  - Offset
	//  - Limit
	ListUpdatedAppMain(header *common.RequestHeader, market Market, category Category, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error)
	// 获取一段时间以内app的更新情况
	//
	// 返回的结果，按照每天一条数据
	//
	// Parameters:
	//  - Header
	//  - StartTime
	//  - EndTime
	GetAppUpdateSummary(header *common.RequestHeader, start_time TimeInt, end_time TimeInt) (r []*appserver_types.AppUpdateSummary, ae *AppException, err error)
	// * 获取某一天发布的app
	// * pubtime必须是一天的00:00:00，unix timestamp
	// *
	// * 通常一天发布的app都不会太多，在几个到几十个
	//    * 返回值按照游戏的市场，下载量排序
	//
	// Parameters:
	//  - Header
	//  - Pubtime
	//  - Offset
	//  - Limit
	ListAppMainByPubtime(header *common.RequestHeader, pubtime TimeInt, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error)
	// 获取与appid同一组的app
	// 返回值按照游戏的发布时间排序
	//
	// Parameters:
	//  - Header
	//  - Appid
	//  - Offset
	//  - Limit
	ListAppMainInGroup(header *common.RequestHeader, appid int32, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error)
	// 将appids对应的游戏，设定到parent_appid这个组
	// 这实际可以用编辑游戏实现，但这个api不会改变游戏的“编辑”状态
	//
	// parent_appid对应的游戏，其parent_appid将被设置为0
	// 当parent_appid为0时，意味将appids对应的游戏设定为独立游戏
	//
	// Parameters:
	//  - Header
	//  - ParentAppid
	//  - Appids
	SetGameGroup(header *common.RequestHeader, parent_appid int32, appids []int32) (ae *AppException, err error)
	// 上传app的资源
	// 资源的唯一标识是(appid,type,seqid)
	// 如果资源已经存在，则会忽略
	//
	// 如果content是空字符串，则表示删除这个资源
	// 对于外部存储的资源，content可以存储一些debug信息来避免空串
	//
	// 如果overwrite为true，或者对应的app.edited=False,则直接创建，
	// 删除或者修改app的资源
	// 否则（这个app是edited且overwrite=False)，只是将这个资源存起来备用。
	//
	// 后台调用此接口时，应设置overwrite为True。
	// 建库程序之后，自动更新这里的资源时，应设置false
	//
	// @return 创建的资源的id，主要用于debug
	//
	// Parameters:
	//  - Header
	//  - Res
	//  - Overwrite
	UploadResource(header *common.RequestHeader, res *appserver_types.AppResource, overwrite bool) (r int32, ae *AppException, err error)
	// 如果一个app被编辑过，且后台抓取了更新的数据，则资源存储在临时位置
	// 通过这个调用，使临时位置的资源替换到当前app上
	//
	// 服务端只会使用appid，type，seqid三个属性决定替换哪个资源，seqid=-1表示替换
	// 该类型的所有资源
	//
	// 返回的是该资源的的id，如果是批量替换截图，返回值无意义
	//
	// Parameters:
	//  - Header
	//  - Res
	UpdateResource(header *common.RequestHeader, res *appserver_types.AppResource) (r int32, ae *AppException, err error)
	// 获取一个app的资源
	//
	// appid和package_name至少有一个不为空
	// 当appid大于0时，优先使用appid，否则使用package_name
	//
	// 可以获取全部资源 ResourceType.ALL，此时忽略seqid
	// 如果seqid<0，则获取这个类别的全部资源
	//
	// 后台对于有升级的app，用相同的id存储app，update为true获取升级信息中的资源
	//
	// Parameters:
	//  - Header
	//  - Appid
	//  - PackageName
	//  - TypeA1
	//  - Seqid
	//  - WithContent
	//  - Update
	GetAppResource(header *common.RequestHeader, appid int32, package_name string, type_a1 ResourceType, seqid int32, with_content bool, update bool) (r []*appserver_types.AppResource, ae *AppException, err error)
	// 获取推广墙黑名单游戏列表
	//
	// Parameters:
	//  - Header
	//  - Criteria
	GetAppBlacklist(header *common.RequestHeader, criteria *ListCriteria) (r []*appserver_types.Game, ae *AppException, err error)
	// * app_main的信息有两个来源，一是来自市场抓取分析的信息，一是后台编辑的信息
	// * 抓取的信息不断更新，为了能发现这种更新，对于编辑过的包，存储如上两份数据。
	// *
	// * 如果一个app被后台编辑过，返回数组有两个item，第一个是抓取的信息，可能是随时更新
	// * 第二个是编辑过的信息，可能已经过时
	//    *
	// * 如果是后台创建的app，也会返回两个item，如果该包有抓取的信息，则第一个信息是抓取
	// * 的信息，否则是一个空的AppMain，即appid是null。第二个item是后台创建app的信息
	// *
	// * 如果app没有被编辑过，则返回数组只有一个元素，即抓取的信息
	// *
	// * 如果没有这个id的数据，返回一个空数组
	//
	// Parameters:
	//  - Header
	//  - Appid
	GetRawAppMainById(header *common.RequestHeader, appid int32) (r []*appserver_types.AppMain, ae *AppException, err error)
	// Parameters:
	//  - Header
	//  - PackageName
	GetRawAppMainByPname(header *common.RequestHeader, package_name string) (r []*appserver_types.AppMain, ae *AppException, err error)
	// 设置某一分类或者全局的appid列表
	//
	// 将会首先清空所有app的rank值，然后为appids指定的app设置rank值
	// 按照列表顺序，依次设置1，2...等值
	//
	// Parameters:
	//  - Header
	//  - Category
	//  - Appids
	//  - RankType
	//  - SaveHistory
	RankAppMain(header *common.RequestHeader, category Category, appids []int32, rank_type int32, save_history bool) (ae *AppException, err error)
	// 获取热门的app
	// 这个算法由服务端决定，可能会不断变化
	//
	// 目前的算法是，抓取若干市场的排行榜，根据名称和下载地址
	// 尝试在我们的库中寻找这个app，如果找到了，就在这里列出来
	// 返回的结果数通常在几十个
	//
	// Parameters:
	//  - Header
	//  - RankType
	//  - Category
	GetHotApp(header *common.RequestHeader, rank_type int32, category Category) (r []*appserver_types.HotApp, ae *AppException, err error)
}

//app后台服务
//
//前端提供app的只读服务，后端则可以编辑app信息
type AppServiceBackEndClient struct {
	*AppServiceFrontEndClient
}

func NewAppServiceBackEndClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AppServiceBackEndClient {
	return &AppServiceBackEndClient{AppServiceFrontEndClient: NewAppServiceFrontEndClientFactory(t, f)}
}

func NewAppServiceBackEndClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AppServiceBackEndClient {
	return &AppServiceBackEndClient{AppServiceFrontEndClient: NewAppServiceFrontEndClientProtocol(t, iprot, oprot)}
}

// 创建app信息
//
// 后台web创建的app, domain='g.domob.cn'
// 包名必须唯一，如果库中已经存在这个包，请使用edit接口
//
// @return 新创建的app的id
//
// Parameters:
//  - Header
//  - App
func (p *AppServiceBackEndClient) CreateAppMain(header *common.RequestHeader, app *appserver_types.AppMain) (r int32, ae *AppException, err error) {
	if err = p.sendCreateAppMain(header, app); err != nil {
		return
	}
	return p.recvCreateAppMain()
}

func (p *AppServiceBackEndClient) sendCreateAppMain(header *common.RequestHeader, app *appserver_types.AppMain) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("createAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args276 := NewCreateAppMainArgs()
	args276.Header = header
	args276.App = app
	if err = args276.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvCreateAppMain() (value int32, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error278 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error279 error
		error279, err = error278.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error279
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result277 := NewCreateAppMainResult()
	if err = result277.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result277.Success
	if result277.Ae != nil {
		ae = result277.Ae
	}
	return
}

// 后台编辑app详细信息
//
// 包名和id不可以修改，是后台识别编辑哪个app的key
// 如果找不到包名抛出异常,
// app.appid可以为空或者0，如果设置则需要与系统中package_name对应的appid一致
//
// 被编辑过的包，内容不再自动更新，即抓取系统抓取的内容不会覆盖编辑过的内容。
// 这也会可能导致我们的信息不够新。
// 因此，如果后台抓取到更新的内容，会记录其信息到另外的表，并标记
// app的has_update为True
//
// Parameters:
//  - Header
//  - App
//  - SetEdited
func (p *AppServiceBackEndClient) EditAppMain(header *common.RequestHeader, app *appserver_types.AppMain, set_edited bool) (ae *AppException, err error) {
	if err = p.sendEditAppMain(header, app, set_edited); err != nil {
		return
	}
	return p.recvEditAppMain()
}

func (p *AppServiceBackEndClient) sendEditAppMain(header *common.RequestHeader, app *appserver_types.AppMain, set_edited bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args280 := NewEditAppMainArgs()
	args280.Header = header
	args280.App = app
	args280.SetEdited = set_edited
	if err = args280.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvEditAppMain() (ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error282 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error283 error
		error283, err = error282.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error283
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result281 := NewEditAppMainResult()
	if err = result281.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result281.Ae != nil {
		ae = result281.Ae
	}
	return
}

// 更新appmain的信息
// 这个接口用于抓取端更新库信息。update不会覆盖后台编辑的内容
//
// 如果找不到对应的包，会创建一条记录
//
// @return 如果创建或者更新了包，则返回那个app的id，否则返回0
//
// Parameters:
//  - Header
//  - App
func (p *AppServiceBackEndClient) UpdateAppMain(header *common.RequestHeader, app *appserver_types.AppMain) (r int32, ae *AppException, err error) {
	if err = p.sendUpdateAppMain(header, app); err != nil {
		return
	}
	return p.recvUpdateAppMain()
}

func (p *AppServiceBackEndClient) sendUpdateAppMain(header *common.RequestHeader, app *appserver_types.AppMain) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args284 := NewUpdateAppMainArgs()
	args284.Header = header
	args284.App = app
	if err = args284.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvUpdateAppMain() (value int32, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error286 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error287 error
		error287, err = error286.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error287
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result285 := NewUpdateAppMainResult()
	if err = result285.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result285.Success
	if result285.Ae != nil {
		ae = result285.Ae
	}
	return
}

// 删除一个app，使之不在list中出现
// permanent=False时标记删除，数据还在服务器中
// 否则清理数据库，删除主记录以及所有附属资源
//
// Parameters:
//  - Header
//  - PackageName
//  - Permanent
func (p *AppServiceBackEndClient) DeleteAppMain(header *common.RequestHeader, package_name string, permanent bool) (ae *AppException, err error) {
	if err = p.sendDeleteAppMain(header, package_name, permanent); err != nil {
		return
	}
	return p.recvDeleteAppMain()
}

func (p *AppServiceBackEndClient) sendDeleteAppMain(header *common.RequestHeader, package_name string, permanent bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args288 := NewDeleteAppMainArgs()
	args288.Header = header
	args288.PackageName = package_name
	args288.Permanent = permanent
	if err = args288.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvDeleteAppMain() (ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error290 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error291 error
		error291, err = error290.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error291
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result289 := NewDeleteAppMainResult()
	if err = result289.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result289.Ae != nil {
		ae = result289.Ae
	}
	return
}

// 获取标记删除了的记录
//
// Parameters:
//  - Header
//  - Category
//  - Offset
//  - Limit
func (p *AppServiceBackEndClient) ListDeletedAppMain(header *common.RequestHeader, category Category, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error) {
	if err = p.sendListDeletedAppMain(header, category, offset, limit); err != nil {
		return
	}
	return p.recvListDeletedAppMain()
}

func (p *AppServiceBackEndClient) sendListDeletedAppMain(header *common.RequestHeader, category Category, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listDeletedAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args292 := NewListDeletedAppMainArgs()
	args292.Header = header
	args292.Category = category
	args292.Offset = offset
	args292.Limit = limit
	if err = args292.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvListDeletedAppMain() (value *appserver_types.MainList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error294 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error295 error
		error295, err = error294.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error295
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result293 := NewListDeletedAppMainResult()
	if err = result293.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result293.Success
	if result293.Ae != nil {
		ae = result293.Ae
	}
	return
}

// 获取有升级的app列表
//
// Parameters:
//  - Header
//  - Market
//  - Category
//  - Offset
//  - Limit
func (p *AppServiceBackEndClient) ListUpdatedAppMain(header *common.RequestHeader, market Market, category Category, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error) {
	if err = p.sendListUpdatedAppMain(header, market, category, offset, limit); err != nil {
		return
	}
	return p.recvListUpdatedAppMain()
}

func (p *AppServiceBackEndClient) sendListUpdatedAppMain(header *common.RequestHeader, market Market, category Category, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listUpdatedAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args296 := NewListUpdatedAppMainArgs()
	args296.Header = header
	args296.Market = market
	args296.Category = category
	args296.Offset = offset
	args296.Limit = limit
	if err = args296.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvListUpdatedAppMain() (value *appserver_types.MainList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error298 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error299 error
		error299, err = error298.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error299
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result297 := NewListUpdatedAppMainResult()
	if err = result297.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result297.Success
	if result297.Ae != nil {
		ae = result297.Ae
	}
	return
}

// 获取一段时间以内app的更新情况
//
// 返回的结果，按照每天一条数据
//
// Parameters:
//  - Header
//  - StartTime
//  - EndTime
func (p *AppServiceBackEndClient) GetAppUpdateSummary(header *common.RequestHeader, start_time TimeInt, end_time TimeInt) (r []*appserver_types.AppUpdateSummary, ae *AppException, err error) {
	if err = p.sendGetAppUpdateSummary(header, start_time, end_time); err != nil {
		return
	}
	return p.recvGetAppUpdateSummary()
}

func (p *AppServiceBackEndClient) sendGetAppUpdateSummary(header *common.RequestHeader, start_time TimeInt, end_time TimeInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppUpdateSummary", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args300 := NewGetAppUpdateSummaryArgs()
	args300.Header = header
	args300.StartTime = start_time
	args300.EndTime = end_time
	if err = args300.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvGetAppUpdateSummary() (value []*appserver_types.AppUpdateSummary, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error302 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error303 error
		error303, err = error302.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error303
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result301 := NewGetAppUpdateSummaryResult()
	if err = result301.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result301.Success
	if result301.Ae != nil {
		ae = result301.Ae
	}
	return
}

// * 获取某一天发布的app
// * pubtime必须是一天的00:00:00，unix timestamp
// *
// * 通常一天发布的app都不会太多，在几个到几十个
//    * 返回值按照游戏的市场，下载量排序
//
// Parameters:
//  - Header
//  - Pubtime
//  - Offset
//  - Limit
func (p *AppServiceBackEndClient) ListAppMainByPubtime(header *common.RequestHeader, pubtime TimeInt, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error) {
	if err = p.sendListAppMainByPubtime(header, pubtime, offset, limit); err != nil {
		return
	}
	return p.recvListAppMainByPubtime()
}

func (p *AppServiceBackEndClient) sendListAppMainByPubtime(header *common.RequestHeader, pubtime TimeInt, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listAppMainByPubtime", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args304 := NewListAppMainByPubtimeArgs()
	args304.Header = header
	args304.Pubtime = pubtime
	args304.Offset = offset
	args304.Limit = limit
	if err = args304.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvListAppMainByPubtime() (value *appserver_types.MainList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error306 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error307 error
		error307, err = error306.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error307
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result305 := NewListAppMainByPubtimeResult()
	if err = result305.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result305.Success
	if result305.Ae != nil {
		ae = result305.Ae
	}
	return
}

// 获取与appid同一组的app
// 返回值按照游戏的发布时间排序
//
// Parameters:
//  - Header
//  - Appid
//  - Offset
//  - Limit
func (p *AppServiceBackEndClient) ListAppMainInGroup(header *common.RequestHeader, appid int32, offset int32, limit int32) (r *appserver_types.MainList, ae *AppException, err error) {
	if err = p.sendListAppMainInGroup(header, appid, offset, limit); err != nil {
		return
	}
	return p.recvListAppMainInGroup()
}

func (p *AppServiceBackEndClient) sendListAppMainInGroup(header *common.RequestHeader, appid int32, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listAppMainInGroup", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args308 := NewListAppMainInGroupArgs()
	args308.Header = header
	args308.Appid = appid
	args308.Offset = offset
	args308.Limit = limit
	if err = args308.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvListAppMainInGroup() (value *appserver_types.MainList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error310 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error311 error
		error311, err = error310.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error311
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result309 := NewListAppMainInGroupResult()
	if err = result309.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result309.Success
	if result309.Ae != nil {
		ae = result309.Ae
	}
	return
}

// 将appids对应的游戏，设定到parent_appid这个组
// 这实际可以用编辑游戏实现，但这个api不会改变游戏的“编辑”状态
//
// parent_appid对应的游戏，其parent_appid将被设置为0
// 当parent_appid为0时，意味将appids对应的游戏设定为独立游戏
//
// Parameters:
//  - Header
//  - ParentAppid
//  - Appids
func (p *AppServiceBackEndClient) SetGameGroup(header *common.RequestHeader, parent_appid int32, appids []int32) (ae *AppException, err error) {
	if err = p.sendSetGameGroup(header, parent_appid, appids); err != nil {
		return
	}
	return p.recvSetGameGroup()
}

func (p *AppServiceBackEndClient) sendSetGameGroup(header *common.RequestHeader, parent_appid int32, appids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("setGameGroup", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args312 := NewSetGameGroupArgs()
	args312.Header = header
	args312.ParentAppid = parent_appid
	args312.Appids = appids
	if err = args312.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvSetGameGroup() (ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error314 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error315 error
		error315, err = error314.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error315
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result313 := NewSetGameGroupResult()
	if err = result313.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result313.Ae != nil {
		ae = result313.Ae
	}
	return
}

// 上传app的资源
// 资源的唯一标识是(appid,type,seqid)
// 如果资源已经存在，则会忽略
//
// 如果content是空字符串，则表示删除这个资源
// 对于外部存储的资源，content可以存储一些debug信息来避免空串
//
// 如果overwrite为true，或者对应的app.edited=False,则直接创建，
// 删除或者修改app的资源
// 否则（这个app是edited且overwrite=False)，只是将这个资源存起来备用。
//
// 后台调用此接口时，应设置overwrite为True。
// 建库程序之后，自动更新这里的资源时，应设置false
//
// @return 创建的资源的id，主要用于debug
//
// Parameters:
//  - Header
//  - Res
//  - Overwrite
func (p *AppServiceBackEndClient) UploadResource(header *common.RequestHeader, res *appserver_types.AppResource, overwrite bool) (r int32, ae *AppException, err error) {
	if err = p.sendUploadResource(header, res, overwrite); err != nil {
		return
	}
	return p.recvUploadResource()
}

func (p *AppServiceBackEndClient) sendUploadResource(header *common.RequestHeader, res *appserver_types.AppResource, overwrite bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("uploadResource", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args316 := NewUploadResourceArgs()
	args316.Header = header
	args316.Res = res
	args316.Overwrite = overwrite
	if err = args316.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvUploadResource() (value int32, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error318 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error319 error
		error319, err = error318.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error319
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result317 := NewUploadResourceResult()
	if err = result317.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result317.Success
	if result317.Ae != nil {
		ae = result317.Ae
	}
	return
}

// 如果一个app被编辑过，且后台抓取了更新的数据，则资源存储在临时位置
// 通过这个调用，使临时位置的资源替换到当前app上
//
// 服务端只会使用appid，type，seqid三个属性决定替换哪个资源，seqid=-1表示替换
// 该类型的所有资源
//
// 返回的是该资源的的id，如果是批量替换截图，返回值无意义
//
// Parameters:
//  - Header
//  - Res
func (p *AppServiceBackEndClient) UpdateResource(header *common.RequestHeader, res *appserver_types.AppResource) (r int32, ae *AppException, err error) {
	if err = p.sendUpdateResource(header, res); err != nil {
		return
	}
	return p.recvUpdateResource()
}

func (p *AppServiceBackEndClient) sendUpdateResource(header *common.RequestHeader, res *appserver_types.AppResource) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateResource", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args320 := NewUpdateResourceArgs()
	args320.Header = header
	args320.Res = res
	if err = args320.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvUpdateResource() (value int32, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error322 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error323 error
		error323, err = error322.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error323
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result321 := NewUpdateResourceResult()
	if err = result321.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result321.Success
	if result321.Ae != nil {
		ae = result321.Ae
	}
	return
}

// 获取一个app的资源
//
// appid和package_name至少有一个不为空
// 当appid大于0时，优先使用appid，否则使用package_name
//
// 可以获取全部资源 ResourceType.ALL，此时忽略seqid
// 如果seqid<0，则获取这个类别的全部资源
//
// 后台对于有升级的app，用相同的id存储app，update为true获取升级信息中的资源
//
// Parameters:
//  - Header
//  - Appid
//  - PackageName
//  - TypeA1
//  - Seqid
//  - WithContent
//  - Update
func (p *AppServiceBackEndClient) GetAppResource(header *common.RequestHeader, appid int32, package_name string, type_a1 ResourceType, seqid int32, with_content bool, update bool) (r []*appserver_types.AppResource, ae *AppException, err error) {
	if err = p.sendGetAppResource(header, appid, package_name, type_a1, seqid, with_content, update); err != nil {
		return
	}
	return p.recvGetAppResource()
}

func (p *AppServiceBackEndClient) sendGetAppResource(header *common.RequestHeader, appid int32, package_name string, type_a1 ResourceType, seqid int32, with_content bool, update bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppResource", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args324 := NewGetAppResourceArgs()
	args324.Header = header
	args324.Appid = appid
	args324.PackageName = package_name
	args324.TypeA1 = type_a1
	args324.Seqid = seqid
	args324.WithContent = with_content
	args324.Update = update
	if err = args324.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvGetAppResource() (value []*appserver_types.AppResource, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error326 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error327 error
		error327, err = error326.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error327
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result325 := NewGetAppResourceResult()
	if err = result325.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result325.Success
	if result325.Ae != nil {
		ae = result325.Ae
	}
	return
}

// 获取推广墙黑名单游戏列表
//
// Parameters:
//  - Header
//  - Criteria
func (p *AppServiceBackEndClient) GetAppBlacklist(header *common.RequestHeader, criteria *ListCriteria) (r []*appserver_types.Game, ae *AppException, err error) {
	if err = p.sendGetAppBlacklist(header, criteria); err != nil {
		return
	}
	return p.recvGetAppBlacklist()
}

func (p *AppServiceBackEndClient) sendGetAppBlacklist(header *common.RequestHeader, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppBlacklist", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args328 := NewGetAppBlacklistArgs()
	args328.Header = header
	args328.Criteria = criteria
	if err = args328.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvGetAppBlacklist() (value []*appserver_types.Game, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error330 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error331 error
		error331, err = error330.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error331
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result329 := NewGetAppBlacklistResult()
	if err = result329.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result329.Success
	if result329.Ae != nil {
		ae = result329.Ae
	}
	return
}

// * app_main的信息有两个来源，一是来自市场抓取分析的信息，一是后台编辑的信息
// * 抓取的信息不断更新，为了能发现这种更新，对于编辑过的包，存储如上两份数据。
// *
// * 如果一个app被后台编辑过，返回数组有两个item，第一个是抓取的信息，可能是随时更新
// * 第二个是编辑过的信息，可能已经过时
//    *
// * 如果是后台创建的app，也会返回两个item，如果该包有抓取的信息，则第一个信息是抓取
// * 的信息，否则是一个空的AppMain，即appid是null。第二个item是后台创建app的信息
// *
// * 如果app没有被编辑过，则返回数组只有一个元素，即抓取的信息
// *
// * 如果没有这个id的数据，返回一个空数组
//
// Parameters:
//  - Header
//  - Appid
func (p *AppServiceBackEndClient) GetRawAppMainById(header *common.RequestHeader, appid int32) (r []*appserver_types.AppMain, ae *AppException, err error) {
	if err = p.sendGetRawAppMainById(header, appid); err != nil {
		return
	}
	return p.recvGetRawAppMainById()
}

func (p *AppServiceBackEndClient) sendGetRawAppMainById(header *common.RequestHeader, appid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getRawAppMainById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args332 := NewGetRawAppMainByIdArgs()
	args332.Header = header
	args332.Appid = appid
	if err = args332.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvGetRawAppMainById() (value []*appserver_types.AppMain, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error334 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error335 error
		error335, err = error334.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error335
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result333 := NewGetRawAppMainByIdResult()
	if err = result333.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result333.Success
	if result333.Ae != nil {
		ae = result333.Ae
	}
	return
}

// Parameters:
//  - Header
//  - PackageName
func (p *AppServiceBackEndClient) GetRawAppMainByPname(header *common.RequestHeader, package_name string) (r []*appserver_types.AppMain, ae *AppException, err error) {
	if err = p.sendGetRawAppMainByPname(header, package_name); err != nil {
		return
	}
	return p.recvGetRawAppMainByPname()
}

func (p *AppServiceBackEndClient) sendGetRawAppMainByPname(header *common.RequestHeader, package_name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getRawAppMainByPname", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args336 := NewGetRawAppMainByPnameArgs()
	args336.Header = header
	args336.PackageName = package_name
	if err = args336.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvGetRawAppMainByPname() (value []*appserver_types.AppMain, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error338 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error339 error
		error339, err = error338.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error339
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result337 := NewGetRawAppMainByPnameResult()
	if err = result337.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result337.Success
	if result337.Ae != nil {
		ae = result337.Ae
	}
	return
}

// 设置某一分类或者全局的appid列表
//
// 将会首先清空所有app的rank值，然后为appids指定的app设置rank值
// 按照列表顺序，依次设置1，2...等值
//
// Parameters:
//  - Header
//  - Category
//  - Appids
//  - RankType
//  - SaveHistory
func (p *AppServiceBackEndClient) RankAppMain(header *common.RequestHeader, category Category, appids []int32, rank_type int32, save_history bool) (ae *AppException, err error) {
	if err = p.sendRankAppMain(header, category, appids, rank_type, save_history); err != nil {
		return
	}
	return p.recvRankAppMain()
}

func (p *AppServiceBackEndClient) sendRankAppMain(header *common.RequestHeader, category Category, appids []int32, rank_type int32, save_history bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("rankAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args340 := NewRankAppMainArgs()
	args340.Header = header
	args340.Category = category
	args340.Appids = appids
	args340.RankType = rank_type
	args340.SaveHistory = save_history
	if err = args340.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvRankAppMain() (ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error342 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error343 error
		error343, err = error342.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error343
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result341 := NewRankAppMainResult()
	if err = result341.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result341.Ae != nil {
		ae = result341.Ae
	}
	return
}

// 获取热门的app
// 这个算法由服务端决定，可能会不断变化
//
// 目前的算法是，抓取若干市场的排行榜，根据名称和下载地址
// 尝试在我们的库中寻找这个app，如果找到了，就在这里列出来
// 返回的结果数通常在几十个
//
// Parameters:
//  - Header
//  - RankType
//  - Category
func (p *AppServiceBackEndClient) GetHotApp(header *common.RequestHeader, rank_type int32, category Category) (r []*appserver_types.HotApp, ae *AppException, err error) {
	if err = p.sendGetHotApp(header, rank_type, category); err != nil {
		return
	}
	return p.recvGetHotApp()
}

func (p *AppServiceBackEndClient) sendGetHotApp(header *common.RequestHeader, rank_type int32, category Category) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getHotApp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args344 := NewGetHotAppArgs()
	args344.Header = header
	args344.RankType = rank_type
	args344.Category = category
	if err = args344.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceBackEndClient) recvGetHotApp() (value []*appserver_types.HotApp, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error346 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error347 error
		error347, err = error346.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error347
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result345 := NewGetHotAppResult()
	if err = result345.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result345.Success
	if result345.Ae != nil {
		ae = result345.Ae
	}
	return
}

type AppServiceBackEndProcessor struct {
	*AppServiceFrontEndProcessor
}

func NewAppServiceBackEndProcessor(handler AppServiceBackEnd) *AppServiceBackEndProcessor {
	self348 := &AppServiceBackEndProcessor{NewAppServiceFrontEndProcessor(handler)}
	self348.AddToProcessorMap("createAppMain", &appServiceBackEndProcessorCreateAppMain{handler: handler})
	self348.AddToProcessorMap("editAppMain", &appServiceBackEndProcessorEditAppMain{handler: handler})
	self348.AddToProcessorMap("updateAppMain", &appServiceBackEndProcessorUpdateAppMain{handler: handler})
	self348.AddToProcessorMap("deleteAppMain", &appServiceBackEndProcessorDeleteAppMain{handler: handler})
	self348.AddToProcessorMap("listDeletedAppMain", &appServiceBackEndProcessorListDeletedAppMain{handler: handler})
	self348.AddToProcessorMap("listUpdatedAppMain", &appServiceBackEndProcessorListUpdatedAppMain{handler: handler})
	self348.AddToProcessorMap("getAppUpdateSummary", &appServiceBackEndProcessorGetAppUpdateSummary{handler: handler})
	self348.AddToProcessorMap("listAppMainByPubtime", &appServiceBackEndProcessorListAppMainByPubtime{handler: handler})
	self348.AddToProcessorMap("listAppMainInGroup", &appServiceBackEndProcessorListAppMainInGroup{handler: handler})
	self348.AddToProcessorMap("setGameGroup", &appServiceBackEndProcessorSetGameGroup{handler: handler})
	self348.AddToProcessorMap("uploadResource", &appServiceBackEndProcessorUploadResource{handler: handler})
	self348.AddToProcessorMap("updateResource", &appServiceBackEndProcessorUpdateResource{handler: handler})
	self348.AddToProcessorMap("getAppResource", &appServiceBackEndProcessorGetAppResource{handler: handler})
	self348.AddToProcessorMap("getAppBlacklist", &appServiceBackEndProcessorGetAppBlacklist{handler: handler})
	self348.AddToProcessorMap("getRawAppMainById", &appServiceBackEndProcessorGetRawAppMainById{handler: handler})
	self348.AddToProcessorMap("getRawAppMainByPname", &appServiceBackEndProcessorGetRawAppMainByPname{handler: handler})
	self348.AddToProcessorMap("rankAppMain", &appServiceBackEndProcessorRankAppMain{handler: handler})
	self348.AddToProcessorMap("getHotApp", &appServiceBackEndProcessorGetHotApp{handler: handler})
	return self348
}

type appServiceBackEndProcessorCreateAppMain struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorCreateAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCreateAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("createAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCreateAppMainResult()
	if result.Success, result.Ae, err = p.handler.CreateAppMain(args.Header, args.App); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing createAppMain: "+err.Error())
		oprot.WriteMessageBegin("createAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("createAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorEditAppMain struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorEditAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditAppMainResult()
	if result.Ae, err = p.handler.EditAppMain(args.Header, args.App, args.SetEdited); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editAppMain: "+err.Error())
		oprot.WriteMessageBegin("editAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorUpdateAppMain struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorUpdateAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateAppMainResult()
	if result.Success, result.Ae, err = p.handler.UpdateAppMain(args.Header, args.App); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateAppMain: "+err.Error())
		oprot.WriteMessageBegin("updateAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorDeleteAppMain struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorDeleteAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteAppMainResult()
	if result.Ae, err = p.handler.DeleteAppMain(args.Header, args.PackageName, args.Permanent); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteAppMain: "+err.Error())
		oprot.WriteMessageBegin("deleteAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorListDeletedAppMain struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorListDeletedAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListDeletedAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listDeletedAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListDeletedAppMainResult()
	if result.Success, result.Ae, err = p.handler.ListDeletedAppMain(args.Header, args.Category, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listDeletedAppMain: "+err.Error())
		oprot.WriteMessageBegin("listDeletedAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listDeletedAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorListUpdatedAppMain struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorListUpdatedAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListUpdatedAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listUpdatedAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListUpdatedAppMainResult()
	if result.Success, result.Ae, err = p.handler.ListUpdatedAppMain(args.Header, args.Market, args.Category, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listUpdatedAppMain: "+err.Error())
		oprot.WriteMessageBegin("listUpdatedAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listUpdatedAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorGetAppUpdateSummary struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorGetAppUpdateSummary) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppUpdateSummaryArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppUpdateSummary", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppUpdateSummaryResult()
	if result.Success, result.Ae, err = p.handler.GetAppUpdateSummary(args.Header, args.StartTime, args.EndTime); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppUpdateSummary: "+err.Error())
		oprot.WriteMessageBegin("getAppUpdateSummary", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppUpdateSummary", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorListAppMainByPubtime struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorListAppMainByPubtime) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListAppMainByPubtimeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listAppMainByPubtime", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListAppMainByPubtimeResult()
	if result.Success, result.Ae, err = p.handler.ListAppMainByPubtime(args.Header, args.Pubtime, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listAppMainByPubtime: "+err.Error())
		oprot.WriteMessageBegin("listAppMainByPubtime", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listAppMainByPubtime", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorListAppMainInGroup struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorListAppMainInGroup) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListAppMainInGroupArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listAppMainInGroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListAppMainInGroupResult()
	if result.Success, result.Ae, err = p.handler.ListAppMainInGroup(args.Header, args.Appid, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listAppMainInGroup: "+err.Error())
		oprot.WriteMessageBegin("listAppMainInGroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listAppMainInGroup", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorSetGameGroup struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorSetGameGroup) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSetGameGroupArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("setGameGroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSetGameGroupResult()
	if result.Ae, err = p.handler.SetGameGroup(args.Header, args.ParentAppid, args.Appids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing setGameGroup: "+err.Error())
		oprot.WriteMessageBegin("setGameGroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("setGameGroup", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorUploadResource struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorUploadResource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUploadResourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("uploadResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUploadResourceResult()
	if result.Success, result.Ae, err = p.handler.UploadResource(args.Header, args.Res, args.Overwrite); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing uploadResource: "+err.Error())
		oprot.WriteMessageBegin("uploadResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("uploadResource", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorUpdateResource struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorUpdateResource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateResourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateResourceResult()
	if result.Success, result.Ae, err = p.handler.UpdateResource(args.Header, args.Res); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateResource: "+err.Error())
		oprot.WriteMessageBegin("updateResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateResource", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorGetAppResource struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorGetAppResource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppResourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppResourceResult()
	if result.Success, result.Ae, err = p.handler.GetAppResource(args.Header, args.Appid, args.PackageName, args.TypeA1, args.Seqid, args.WithContent, args.Update); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppResource: "+err.Error())
		oprot.WriteMessageBegin("getAppResource", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppResource", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorGetAppBlacklist struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorGetAppBlacklist) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppBlacklistArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppBlacklist", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppBlacklistResult()
	if result.Success, result.Ae, err = p.handler.GetAppBlacklist(args.Header, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppBlacklist: "+err.Error())
		oprot.WriteMessageBegin("getAppBlacklist", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppBlacklist", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorGetRawAppMainById struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorGetRawAppMainById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRawAppMainByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getRawAppMainById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRawAppMainByIdResult()
	if result.Success, result.Ae, err = p.handler.GetRawAppMainById(args.Header, args.Appid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getRawAppMainById: "+err.Error())
		oprot.WriteMessageBegin("getRawAppMainById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getRawAppMainById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorGetRawAppMainByPname struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorGetRawAppMainByPname) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRawAppMainByPnameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getRawAppMainByPname", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRawAppMainByPnameResult()
	if result.Success, result.Ae, err = p.handler.GetRawAppMainByPname(args.Header, args.PackageName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getRawAppMainByPname: "+err.Error())
		oprot.WriteMessageBegin("getRawAppMainByPname", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getRawAppMainByPname", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorRankAppMain struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorRankAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRankAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("rankAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRankAppMainResult()
	if result.Ae, err = p.handler.RankAppMain(args.Header, args.Category, args.Appids, args.RankType, args.SaveHistory); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing rankAppMain: "+err.Error())
		oprot.WriteMessageBegin("rankAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("rankAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceBackEndProcessorGetHotApp struct {
	handler AppServiceBackEnd
}

func (p *appServiceBackEndProcessorGetHotApp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetHotAppArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getHotApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetHotAppResult()
	if result.Success, result.Ae, err = p.handler.GetHotApp(args.Header, args.RankType, args.Category); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getHotApp: "+err.Error())
		oprot.WriteMessageBegin("getHotApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getHotApp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type CreateAppMainArgs struct {
	Header *common.RequestHeader    `thrift:"header,1" json:"header"`
	App    *appserver_types.AppMain `thrift:"app,2" json:"app"`
}

func NewCreateAppMainArgs() *CreateAppMainArgs {
	return &CreateAppMainArgs{}
}

func (p *CreateAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreateAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CreateAppMainArgs) readField2(iprot thrift.TProtocol) error {
	p.App = appserver_types.NewAppMain()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *CreateAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("createAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreateAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CreateAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app: %s", p, err)
		}
	}
	return err
}

func (p *CreateAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAppMainArgs(%+v)", *p)
}

type CreateAppMainResult struct {
	Success int32         `thrift:"success,0" json:"success"`
	Ae      *AppException `thrift:"ae,1" json:"ae"`
}

func NewCreateAppMainResult() *CreateAppMainResult {
	return &CreateAppMainResult{}
}

func (p *CreateAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreateAppMainResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *CreateAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *CreateAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("createAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreateAppMainResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *CreateAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *CreateAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreateAppMainResult(%+v)", *p)
}

type EditAppMainArgs struct {
	Header    *common.RequestHeader    `thrift:"header,1" json:"header"`
	App       *appserver_types.AppMain `thrift:"app,2" json:"app"`
	SetEdited bool                     `thrift:"set_edited,3" json:"set_edited"`
}

func NewEditAppMainArgs() *EditAppMainArgs {
	return &EditAppMainArgs{}
}

func (p *EditAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditAppMainArgs) readField2(iprot thrift.TProtocol) error {
	p.App = appserver_types.NewAppMain()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *EditAppMainArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SetEdited = v
	}
	return nil
}

func (p *EditAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app: %s", p, err)
		}
	}
	return err
}

func (p *EditAppMainArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("set_edited", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:set_edited: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.SetEdited)); err != nil {
		return fmt.Errorf("%T.set_edited (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:set_edited: %s", p, err)
	}
	return err
}

func (p *EditAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAppMainArgs(%+v)", *p)
}

type EditAppMainResult struct {
	Ae *AppException `thrift:"ae,1" json:"ae"`
}

func NewEditAppMainResult() *EditAppMainResult {
	return &EditAppMainResult{}
}

func (p *EditAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *EditAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *EditAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditAppMainResult(%+v)", *p)
}

type UpdateAppMainArgs struct {
	Header *common.RequestHeader    `thrift:"header,1" json:"header"`
	App    *appserver_types.AppMain `thrift:"app,2" json:"app"`
}

func NewUpdateAppMainArgs() *UpdateAppMainArgs {
	return &UpdateAppMainArgs{}
}

func (p *UpdateAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateAppMainArgs) readField2(iprot thrift.TProtocol) error {
	p.App = appserver_types.NewAppMain()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *UpdateAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAppMainArgs(%+v)", *p)
}

type UpdateAppMainResult struct {
	Success int32         `thrift:"success,0" json:"success"`
	Ae      *AppException `thrift:"ae,1" json:"ae"`
}

func NewUpdateAppMainResult() *UpdateAppMainResult {
	return &UpdateAppMainResult{}
}

func (p *UpdateAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppMainResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *UpdateAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *UpdateAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppMainResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *UpdateAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAppMainResult(%+v)", *p)
}

type DeleteAppMainArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	PackageName string                `thrift:"package_name,2" json:"package_name"`
	Permanent   bool                  `thrift:"permanent,3" json:"permanent"`
}

func NewDeleteAppMainArgs() *DeleteAppMainArgs {
	return &DeleteAppMainArgs{}
}

func (p *DeleteAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DeleteAppMainArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *DeleteAppMainArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Permanent = v
	}
	return nil
}

func (p *DeleteAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DeleteAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:package_name: %s", p, err)
	}
	return err
}

func (p *DeleteAppMainArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("permanent", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:permanent: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Permanent)); err != nil {
		return fmt.Errorf("%T.permanent (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:permanent: %s", p, err)
	}
	return err
}

func (p *DeleteAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAppMainArgs(%+v)", *p)
}

type DeleteAppMainResult struct {
	Ae *AppException `thrift:"ae,1" json:"ae"`
}

func NewDeleteAppMainResult() *DeleteAppMainResult {
	return &DeleteAppMainResult{}
}

func (p *DeleteAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *DeleteAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *DeleteAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteAppMainResult(%+v)", *p)
}

type ListDeletedAppMainArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Category Category              `thrift:"category,2" json:"category"`
	Offset   int32                 `thrift:"offset,3" json:"offset"`
	Limit    int32                 `thrift:"limit,4" json:"limit"`
}

func NewListDeletedAppMainArgs() *ListDeletedAppMainArgs {
	return &ListDeletedAppMainArgs{
		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListDeletedAppMainArgs) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *ListDeletedAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListDeletedAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListDeletedAppMainArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = Category(v)
	}
	return nil
}

func (p *ListDeletedAppMainArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListDeletedAppMainArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListDeletedAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listDeletedAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListDeletedAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListDeletedAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:category: %s", p, err)
	}
	return err
}

func (p *ListDeletedAppMainArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListDeletedAppMainArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListDeletedAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDeletedAppMainArgs(%+v)", *p)
}

type ListDeletedAppMainResult struct {
	Success *appserver_types.MainList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewListDeletedAppMainResult() *ListDeletedAppMainResult {
	return &ListDeletedAppMainResult{}
}

func (p *ListDeletedAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListDeletedAppMainResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewMainList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListDeletedAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListDeletedAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listDeletedAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListDeletedAppMainResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListDeletedAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListDeletedAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDeletedAppMainResult(%+v)", *p)
}

type ListUpdatedAppMainArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Market   Market                `thrift:"market,2" json:"market"`
	Category Category              `thrift:"category,3" json:"category"`
	Offset   int32                 `thrift:"offset,4" json:"offset"`
	Limit    int32                 `thrift:"limit,5" json:"limit"`
}

func NewListUpdatedAppMainArgs() *ListUpdatedAppMainArgs {
	return &ListUpdatedAppMainArgs{
		Market: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListUpdatedAppMainArgs) IsSetMarket() bool {
	return int64(p.Market) != math.MinInt32-1
}

func (p *ListUpdatedAppMainArgs) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *ListUpdatedAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUpdatedAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListUpdatedAppMainArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Market = Market(v)
	}
	return nil
}

func (p *ListUpdatedAppMainArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Category = Category(v)
	}
	return nil
}

func (p *ListUpdatedAppMainArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListUpdatedAppMainArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListUpdatedAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUpdatedAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUpdatedAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListUpdatedAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("market", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:market: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Market)); err != nil {
		return fmt.Errorf("%T.market (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:market: %s", p, err)
	}
	return err
}

func (p *ListUpdatedAppMainArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:category: %s", p, err)
	}
	return err
}

func (p *ListUpdatedAppMainArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *ListUpdatedAppMainArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *ListUpdatedAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUpdatedAppMainArgs(%+v)", *p)
}

type ListUpdatedAppMainResult struct {
	Success *appserver_types.MainList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewListUpdatedAppMainResult() *ListUpdatedAppMainResult {
	return &ListUpdatedAppMainResult{}
}

func (p *ListUpdatedAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListUpdatedAppMainResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewMainList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListUpdatedAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListUpdatedAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listUpdatedAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListUpdatedAppMainResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListUpdatedAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListUpdatedAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListUpdatedAppMainResult(%+v)", *p)
}

type GetAppUpdateSummaryArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	StartTime TimeInt               `thrift:"start_time,2" json:"start_time"`
	EndTime   TimeInt               `thrift:"end_time,3" json:"end_time"`
}

func NewGetAppUpdateSummaryArgs() *GetAppUpdateSummaryArgs {
	return &GetAppUpdateSummaryArgs{}
}

func (p *GetAppUpdateSummaryArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppUpdateSummaryArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppUpdateSummaryArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *GetAppUpdateSummaryArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.EndTime = TimeInt(v)
	}
	return nil
}

func (p *GetAppUpdateSummaryArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppUpdateSummary_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppUpdateSummaryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppUpdateSummaryArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:start_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:start_time: %s", p, err)
	}
	return err
}

func (p *GetAppUpdateSummaryArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:end_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:end_time: %s", p, err)
	}
	return err
}

func (p *GetAppUpdateSummaryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppUpdateSummaryArgs(%+v)", *p)
}

type GetAppUpdateSummaryResult struct {
	Success []*appserver_types.AppUpdateSummary `thrift:"success,0" json:"success"`
	Ae      *AppException                       `thrift:"ae,1" json:"ae"`
}

func NewGetAppUpdateSummaryResult() *GetAppUpdateSummaryResult {
	return &GetAppUpdateSummaryResult{}
}

func (p *GetAppUpdateSummaryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppUpdateSummaryResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.AppUpdateSummary, 0, size)
	for i := 0; i < size; i++ {
		_elem349 := appserver_types.NewAppUpdateSummary()
		if err := _elem349.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem349)
		}
		p.Success = append(p.Success, _elem349)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppUpdateSummaryResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppUpdateSummaryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppUpdateSummary_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppUpdateSummaryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppUpdateSummaryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppUpdateSummaryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppUpdateSummaryResult(%+v)", *p)
}

type ListAppMainByPubtimeArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	Pubtime TimeInt               `thrift:"pubtime,2" json:"pubtime"`
	Offset  int32                 `thrift:"offset,3" json:"offset"`
	Limit   int32                 `thrift:"limit,4" json:"limit"`
}

func NewListAppMainByPubtimeArgs() *ListAppMainByPubtimeArgs {
	return &ListAppMainByPubtimeArgs{}
}

func (p *ListAppMainByPubtimeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainByPubtimeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListAppMainByPubtimeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pubtime = TimeInt(v)
	}
	return nil
}

func (p *ListAppMainByPubtimeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListAppMainByPubtimeArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListAppMainByPubtimeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAppMainByPubtime_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainByPubtimeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListAppMainByPubtimeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubtime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pubtime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Pubtime)); err != nil {
		return fmt.Errorf("%T.pubtime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pubtime: %s", p, err)
	}
	return err
}

func (p *ListAppMainByPubtimeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListAppMainByPubtimeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListAppMainByPubtimeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppMainByPubtimeArgs(%+v)", *p)
}

type ListAppMainByPubtimeResult struct {
	Success *appserver_types.MainList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewListAppMainByPubtimeResult() *ListAppMainByPubtimeResult {
	return &ListAppMainByPubtimeResult{}
}

func (p *ListAppMainByPubtimeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainByPubtimeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewMainList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListAppMainByPubtimeResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListAppMainByPubtimeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAppMainByPubtime_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainByPubtimeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListAppMainByPubtimeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListAppMainByPubtimeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppMainByPubtimeResult(%+v)", *p)
}

type ListAppMainInGroupArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Appid  int32                 `thrift:"appid,2" json:"appid"`
	Offset int32                 `thrift:"offset,3" json:"offset"`
	Limit  int32                 `thrift:"limit,4" json:"limit"`
}

func NewListAppMainInGroupArgs() *ListAppMainInGroupArgs {
	return &ListAppMainInGroupArgs{}
}

func (p *ListAppMainInGroupArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainInGroupArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListAppMainInGroupArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *ListAppMainInGroupArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListAppMainInGroupArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListAppMainInGroupArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAppMainInGroup_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainInGroupArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListAppMainInGroupArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *ListAppMainInGroupArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *ListAppMainInGroupArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *ListAppMainInGroupArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppMainInGroupArgs(%+v)", *p)
}

type ListAppMainInGroupResult struct {
	Success *appserver_types.MainList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewListAppMainInGroupResult() *ListAppMainInGroupResult {
	return &ListAppMainInGroupResult{}
}

func (p *ListAppMainInGroupResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainInGroupResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewMainList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListAppMainInGroupResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListAppMainInGroupResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAppMainInGroup_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppMainInGroupResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListAppMainInGroupResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListAppMainInGroupResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppMainInGroupResult(%+v)", *p)
}

type SetGameGroupArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	ParentAppid int32                 `thrift:"parent_appid,2" json:"parent_appid"`
	Appids      []int32               `thrift:"appids,3" json:"appids"`
}

func NewSetGameGroupArgs() *SetGameGroupArgs {
	return &SetGameGroupArgs{}
}

func (p *SetGameGroupArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetGameGroupArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SetGameGroupArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ParentAppid = v
	}
	return nil
}

func (p *SetGameGroupArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Appids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem350 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem350 = v
		}
		p.Appids = append(p.Appids, _elem350)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SetGameGroupArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setGameGroup_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetGameGroupArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SetGameGroupArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:parent_appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ParentAppid)); err != nil {
		return fmt.Errorf("%T.parent_appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:parent_appid: %s", p, err)
	}
	return err
}

func (p *SetGameGroupArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Appids != nil {
		if err := oprot.WriteFieldBegin("appids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Appids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Appids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appids: %s", p, err)
		}
	}
	return err
}

func (p *SetGameGroupArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetGameGroupArgs(%+v)", *p)
}

type SetGameGroupResult struct {
	Ae *AppException `thrift:"ae,1" json:"ae"`
}

func NewSetGameGroupResult() *SetGameGroupResult {
	return &SetGameGroupResult{}
}

func (p *SetGameGroupResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetGameGroupResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *SetGameGroupResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setGameGroup_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetGameGroupResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *SetGameGroupResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetGameGroupResult(%+v)", *p)
}

type UploadResourceArgs struct {
	Header    *common.RequestHeader        `thrift:"header,1" json:"header"`
	Res       *appserver_types.AppResource `thrift:"res,2" json:"res"`
	Overwrite bool                         `thrift:"overwrite,3" json:"overwrite"`
}

func NewUploadResourceArgs() *UploadResourceArgs {
	return &UploadResourceArgs{}
}

func (p *UploadResourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UploadResourceArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UploadResourceArgs) readField2(iprot thrift.TProtocol) error {
	p.Res = appserver_types.NewAppResource()
	if err := p.Res.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Res)
	}
	return nil
}

func (p *UploadResourceArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Overwrite = v
	}
	return nil
}

func (p *UploadResourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("uploadResource_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UploadResourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UploadResourceArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Res != nil {
		if err := oprot.WriteFieldBegin("res", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:res: %s", p, err)
		}
		if err := p.Res.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Res)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:res: %s", p, err)
		}
	}
	return err
}

func (p *UploadResourceArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("overwrite", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:overwrite: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Overwrite)); err != nil {
		return fmt.Errorf("%T.overwrite (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:overwrite: %s", p, err)
	}
	return err
}

func (p *UploadResourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadResourceArgs(%+v)", *p)
}

type UploadResourceResult struct {
	Success int32         `thrift:"success,0" json:"success"`
	Ae      *AppException `thrift:"ae,1" json:"ae"`
}

func NewUploadResourceResult() *UploadResourceResult {
	return &UploadResourceResult{}
}

func (p *UploadResourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UploadResourceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *UploadResourceResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *UploadResourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("uploadResource_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UploadResourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *UploadResourceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *UploadResourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UploadResourceResult(%+v)", *p)
}

type UpdateResourceArgs struct {
	Header *common.RequestHeader        `thrift:"header,1" json:"header"`
	Res    *appserver_types.AppResource `thrift:"res,2" json:"res"`
}

func NewUpdateResourceArgs() *UpdateResourceArgs {
	return &UpdateResourceArgs{}
}

func (p *UpdateResourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateResourceArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateResourceArgs) readField2(iprot thrift.TProtocol) error {
	p.Res = appserver_types.NewAppResource()
	if err := p.Res.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Res)
	}
	return nil
}

func (p *UpdateResourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateResource_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateResourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateResourceArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Res != nil {
		if err := oprot.WriteFieldBegin("res", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:res: %s", p, err)
		}
		if err := p.Res.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Res)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:res: %s", p, err)
		}
	}
	return err
}

func (p *UpdateResourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateResourceArgs(%+v)", *p)
}

type UpdateResourceResult struct {
	Success int32         `thrift:"success,0" json:"success"`
	Ae      *AppException `thrift:"ae,1" json:"ae"`
}

func NewUpdateResourceResult() *UpdateResourceResult {
	return &UpdateResourceResult{}
}

func (p *UpdateResourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateResourceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *UpdateResourceResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *UpdateResourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateResource_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateResourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *UpdateResourceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *UpdateResourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateResourceResult(%+v)", *p)
}

type GetAppResourceArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Appid       int32                 `thrift:"appid,2" json:"appid"`
	PackageName string                `thrift:"package_name,3" json:"package_name"`
	TypeA1      ResourceType          `thrift:"type,4" json:"type"`
	Seqid       int32                 `thrift:"seqid,5" json:"seqid"`
	WithContent bool                  `thrift:"with_content,6" json:"with_content"`
	Update      bool                  `thrift:"update,7" json:"update"`
}

func NewGetAppResourceArgs() *GetAppResourceArgs {
	return &GetAppResourceArgs{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetAppResourceArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *GetAppResourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppResourceArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppResourceArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *GetAppResourceArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *GetAppResourceArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = ResourceType(v)
	}
	return nil
}

func (p *GetAppResourceArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Seqid = v
	}
	return nil
}

func (p *GetAppResourceArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.WithContent = v
	}
	return nil
}

func (p *GetAppResourceArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Update = v
	}
	return nil
}

func (p *GetAppResourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppResource_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppResourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppResourceArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *GetAppResourceArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:package_name: %s", p, err)
	}
	return err
}

func (p *GetAppResourceArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:type: %s", p, err)
	}
	return err
}

func (p *GetAppResourceArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seqid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:seqid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Seqid)); err != nil {
		return fmt.Errorf("%T.seqid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:seqid: %s", p, err)
	}
	return err
}

func (p *GetAppResourceArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_content", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:with_content: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithContent)); err != nil {
		return fmt.Errorf("%T.with_content (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:with_content: %s", p, err)
	}
	return err
}

func (p *GetAppResourceArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:update: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Update)); err != nil {
		return fmt.Errorf("%T.update (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:update: %s", p, err)
	}
	return err
}

func (p *GetAppResourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppResourceArgs(%+v)", *p)
}

type GetAppResourceResult struct {
	Success []*appserver_types.AppResource `thrift:"success,0" json:"success"`
	Ae      *AppException                  `thrift:"ae,1" json:"ae"`
}

func NewGetAppResourceResult() *GetAppResourceResult {
	return &GetAppResourceResult{}
}

func (p *GetAppResourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppResourceResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.AppResource, 0, size)
	for i := 0; i < size; i++ {
		_elem351 := appserver_types.NewAppResource()
		if err := _elem351.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem351)
		}
		p.Success = append(p.Success, _elem351)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppResourceResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppResourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppResource_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppResourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppResourceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppResourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppResourceResult(%+v)", *p)
}

type GetAppBlacklistArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Criteria *ListCriteria         `thrift:"criteria,2" json:"criteria"`
}

func NewGetAppBlacklistArgs() *GetAppBlacklistArgs {
	return &GetAppBlacklistArgs{}
}

func (p *GetAppBlacklistArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppBlacklistArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppBlacklistArgs) readField2(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *GetAppBlacklistArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppBlacklist_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppBlacklistArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppBlacklistArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:criteria: %s", p, err)
		}
	}
	return err
}

func (p *GetAppBlacklistArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppBlacklistArgs(%+v)", *p)
}

type GetAppBlacklistResult struct {
	Success []*appserver_types.Game `thrift:"success,0" json:"success"`
	Ae      *AppException           `thrift:"ae,1" json:"ae"`
}

func NewGetAppBlacklistResult() *GetAppBlacklistResult {
	return &GetAppBlacklistResult{}
}

func (p *GetAppBlacklistResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppBlacklistResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.Game, 0, size)
	for i := 0; i < size; i++ {
		_elem352 := appserver_types.NewGame()
		if err := _elem352.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem352)
		}
		p.Success = append(p.Success, _elem352)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppBlacklistResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppBlacklistResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppBlacklist_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppBlacklistResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppBlacklistResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppBlacklistResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppBlacklistResult(%+v)", *p)
}

type GetRawAppMainByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Appid  int32                 `thrift:"appid,2" json:"appid"`
}

func NewGetRawAppMainByIdArgs() *GetRawAppMainByIdArgs {
	return &GetRawAppMainByIdArgs{}
}

func (p *GetRawAppMainByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetRawAppMainByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *GetRawAppMainByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRawAppMainById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetRawAppMainByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *GetRawAppMainByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRawAppMainByIdArgs(%+v)", *p)
}

type GetRawAppMainByIdResult struct {
	Success []*appserver_types.AppMain `thrift:"success,0" json:"success"`
	Ae      *AppException              `thrift:"ae,1" json:"ae"`
}

func NewGetRawAppMainByIdResult() *GetRawAppMainByIdResult {
	return &GetRawAppMainByIdResult{}
}

func (p *GetRawAppMainByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.AppMain, 0, size)
	for i := 0; i < size; i++ {
		_elem353 := appserver_types.NewAppMain()
		if err := _elem353.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem353)
		}
		p.Success = append(p.Success, _elem353)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRawAppMainByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetRawAppMainByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRawAppMainById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRawAppMainByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetRawAppMainByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRawAppMainByIdResult(%+v)", *p)
}

type GetRawAppMainByPnameArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	PackageName string                `thrift:"package_name,2" json:"package_name"`
}

func NewGetRawAppMainByPnameArgs() *GetRawAppMainByPnameArgs {
	return &GetRawAppMainByPnameArgs{}
}

func (p *GetRawAppMainByPnameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByPnameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetRawAppMainByPnameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *GetRawAppMainByPnameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRawAppMainByPname_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByPnameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetRawAppMainByPnameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:package_name: %s", p, err)
	}
	return err
}

func (p *GetRawAppMainByPnameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRawAppMainByPnameArgs(%+v)", *p)
}

type GetRawAppMainByPnameResult struct {
	Success []*appserver_types.AppMain `thrift:"success,0" json:"success"`
	Ae      *AppException              `thrift:"ae,1" json:"ae"`
}

func NewGetRawAppMainByPnameResult() *GetRawAppMainByPnameResult {
	return &GetRawAppMainByPnameResult{}
}

func (p *GetRawAppMainByPnameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByPnameResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.AppMain, 0, size)
	for i := 0; i < size; i++ {
		_elem354 := appserver_types.NewAppMain()
		if err := _elem354.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem354)
		}
		p.Success = append(p.Success, _elem354)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRawAppMainByPnameResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetRawAppMainByPnameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRawAppMainByPname_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRawAppMainByPnameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRawAppMainByPnameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetRawAppMainByPnameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRawAppMainByPnameResult(%+v)", *p)
}

type RankAppMainArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Category    Category              `thrift:"category,2" json:"category"`
	Appids      []int32               `thrift:"appids,3" json:"appids"`
	RankType    int32                 `thrift:"rank_type,4" json:"rank_type"`
	SaveHistory bool                  `thrift:"save_history,5" json:"save_history"`
}

func NewRankAppMainArgs() *RankAppMainArgs {
	return &RankAppMainArgs{
		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RankAppMainArgs) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *RankAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RankAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RankAppMainArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Category = Category(v)
	}
	return nil
}

func (p *RankAppMainArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Appids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem355 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem355 = v
		}
		p.Appids = append(p.Appids, _elem355)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RankAppMainArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RankType = v
	}
	return nil
}

func (p *RankAppMainArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SaveHistory = v
	}
	return nil
}

func (p *RankAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rankAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RankAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RankAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:category: %s", p, err)
	}
	return err
}

func (p *RankAppMainArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Appids != nil {
		if err := oprot.WriteFieldBegin("appids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Appids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Appids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appids: %s", p, err)
		}
	}
	return err
}

func (p *RankAppMainArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank_type", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:rank_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RankType)); err != nil {
		return fmt.Errorf("%T.rank_type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:rank_type: %s", p, err)
	}
	return err
}

func (p *RankAppMainArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("save_history", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:save_history: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.SaveHistory)); err != nil {
		return fmt.Errorf("%T.save_history (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:save_history: %s", p, err)
	}
	return err
}

func (p *RankAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RankAppMainArgs(%+v)", *p)
}

type RankAppMainResult struct {
	Ae *AppException `thrift:"ae,1" json:"ae"`
}

func NewRankAppMainResult() *RankAppMainResult {
	return &RankAppMainResult{}
}

func (p *RankAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RankAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *RankAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rankAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RankAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *RankAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RankAppMainResult(%+v)", *p)
}

type GetHotAppArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	RankType int32                 `thrift:"rank_type,2" json:"rank_type"`
	Category Category              `thrift:"category,3" json:"category"`
}

func NewGetHotAppArgs() *GetHotAppArgs {
	return &GetHotAppArgs{
		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetHotAppArgs) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *GetHotAppArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetHotAppArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetHotAppArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RankType = v
	}
	return nil
}

func (p *GetHotAppArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Category = Category(v)
	}
	return nil
}

func (p *GetHotAppArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getHotApp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetHotAppArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetHotAppArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank_type", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:rank_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RankType)); err != nil {
		return fmt.Errorf("%T.rank_type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:rank_type: %s", p, err)
	}
	return err
}

func (p *GetHotAppArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:category: %s", p, err)
	}
	return err
}

func (p *GetHotAppArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetHotAppArgs(%+v)", *p)
}

type GetHotAppResult struct {
	Success []*appserver_types.HotApp `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewGetHotAppResult() *GetHotAppResult {
	return &GetHotAppResult{}
}

func (p *GetHotAppResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetHotAppResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.HotApp, 0, size)
	for i := 0; i < size; i++ {
		_elem356 := appserver_types.NewHotApp()
		if err := _elem356.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem356)
		}
		p.Success = append(p.Success, _elem356)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetHotAppResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetHotAppResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getHotApp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetHotAppResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetHotAppResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetHotAppResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetHotAppResult(%+v)", *p)
}
