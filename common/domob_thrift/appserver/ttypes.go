// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appserver

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appserver_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = appserver_types.GoUnusedProtection__
var GoUnusedProtection__ int

type SearchType int64

const (
	SearchType_ALL      SearchType = 0
	SearchType_PACKAGE  SearchType = 1
	SearchType_NAME     SearchType = 2
	SearchType_FULLTEXT SearchType = 3
)

func (p SearchType) String() string {
	switch p {
	case SearchType_ALL:
		return "SearchType_ALL"
	case SearchType_PACKAGE:
		return "SearchType_PACKAGE"
	case SearchType_NAME:
		return "SearchType_NAME"
	case SearchType_FULLTEXT:
		return "SearchType_FULLTEXT"
	}
	return "<UNSET>"
}

func SearchTypeFromString(s string) (SearchType, error) {
	switch s {
	case "SearchType_ALL":
		return SearchType_ALL, nil
	case "SearchType_PACKAGE":
		return SearchType_PACKAGE, nil
	case "SearchType_NAME":
		return SearchType_NAME, nil
	case "SearchType_FULLTEXT":
		return SearchType_FULLTEXT, nil
	}
	return SearchType(math.MinInt32 - 1), fmt.Errorf("not a valid SearchType string")
}

//list, search接口，允许的排序类型
//
//多游的数据，目前有3类，来自推广墙（appwall）的付费推广游戏，来自多游自身的
//联运或者付费推广游戏，来自市场抓取的免费游戏。
//
//这些游戏对用户都是免费下载的，会对广告主收费。
//无论何种排序，都会将付费游戏排在前面，后面是免费游戏。
//
//实际上这里已经并不仅仅是排序策略，不同的列表构成的方式可能是完全不同的
type ListOrderBy int64

const (
	ListOrderBy_ORDERBY_7DAYS_DOWNLOAD  ListOrderBy = 1
	ListOrderBy_ORDERBY_MARKET_DOWNLOAD ListOrderBy = 2
	ListOrderBy_ORDERBY_PUBTIME         ListOrderBy = 3
	ListOrderBy_ORDERBY_SELECTED_GAME   ListOrderBy = 4
	ListOrderBy_ORDERBY_APPWALL_APP     ListOrderBy = 5
)

func (p ListOrderBy) String() string {
	switch p {
	case ListOrderBy_ORDERBY_7DAYS_DOWNLOAD:
		return "ListOrderBy_ORDERBY_7DAYS_DOWNLOAD"
	case ListOrderBy_ORDERBY_MARKET_DOWNLOAD:
		return "ListOrderBy_ORDERBY_MARKET_DOWNLOAD"
	case ListOrderBy_ORDERBY_PUBTIME:
		return "ListOrderBy_ORDERBY_PUBTIME"
	case ListOrderBy_ORDERBY_SELECTED_GAME:
		return "ListOrderBy_ORDERBY_SELECTED_GAME"
	case ListOrderBy_ORDERBY_APPWALL_APP:
		return "ListOrderBy_ORDERBY_APPWALL_APP"
	}
	return "<UNSET>"
}

func ListOrderByFromString(s string) (ListOrderBy, error) {
	switch s {
	case "ListOrderBy_ORDERBY_7DAYS_DOWNLOAD":
		return ListOrderBy_ORDERBY_7DAYS_DOWNLOAD, nil
	case "ListOrderBy_ORDERBY_MARKET_DOWNLOAD":
		return ListOrderBy_ORDERBY_MARKET_DOWNLOAD, nil
	case "ListOrderBy_ORDERBY_PUBTIME":
		return ListOrderBy_ORDERBY_PUBTIME, nil
	case "ListOrderBy_ORDERBY_SELECTED_GAME":
		return ListOrderBy_ORDERBY_SELECTED_GAME, nil
	case "ListOrderBy_ORDERBY_APPWALL_APP":
		return ListOrderBy_ORDERBY_APPWALL_APP, nil
	}
	return ListOrderBy(math.MinInt32 - 1), fmt.Errorf("not a valid ListOrderBy string")
}

//推荐类型
type RecommendType int64

const (
	RecommendType_RECOM_INDEX  RecommendType = 1
	RecommendType_RECOM_SEARCH RecommendType = 2
	RecommendType_RECOM_DETAIL RecommendType = 3
	RecommendType_RECOM_ONLINE RecommendType = 4
)

func (p RecommendType) String() string {
	switch p {
	case RecommendType_RECOM_INDEX:
		return "RecommendType_RECOM_INDEX"
	case RecommendType_RECOM_SEARCH:
		return "RecommendType_RECOM_SEARCH"
	case RecommendType_RECOM_DETAIL:
		return "RecommendType_RECOM_DETAIL"
	case RecommendType_RECOM_ONLINE:
		return "RecommendType_RECOM_ONLINE"
	}
	return "<UNSET>"
}

func RecommendTypeFromString(s string) (RecommendType, error) {
	switch s {
	case "RecommendType_RECOM_INDEX":
		return RecommendType_RECOM_INDEX, nil
	case "RecommendType_RECOM_SEARCH":
		return RecommendType_RECOM_SEARCH, nil
	case "RecommendType_RECOM_DETAIL":
		return RecommendType_RECOM_DETAIL, nil
	case "RecommendType_RECOM_ONLINE":
		return RecommendType_RECOM_ONLINE, nil
	}
	return RecommendType(math.MinInt32 - 1), fmt.Errorf("not a valid RecommendType string")
}

type TimeInt common.TimeInt

type RequestHeader *common.RequestHeader

type Game *appserver_types.Game

type GameList *appserver_types.GameList

type AppMain *appserver_types.AppMain

type MainList *appserver_types.MainList

type AppWallGame *appserver_types.AppWallGame

type Market appserver_types.Market

type AppResource *appserver_types.AppResource

type ResourceType appserver_types.ResourceType

type Category appserver_types.Category

type CategorySummary *appserver_types.CategorySummary

type HotApp *appserver_types.HotApp

type Topic *appserver_types.Topic

type Banner *appserver_types.Banner

type AppUpdateSummary *appserver_types.AppUpdateSummary

type AppWallProt map[string]string

type GiftResult *appserver_types.GiftResult

type AppException struct {
	Message string `thrift:"message,1" json:"message"`
}

func NewAppException() *AppException {
	return &AppException{}
}

func (p *AppException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *AppException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:message: %s", p, err)
	}
	return err
}

func (p *AppException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppException(%+v)", *p)
}

type ListCriteria struct {
	Offset      int32              `thrift:"offset,1" json:"offset"`
	Limit       int32              `thrift:"limit,2" json:"limit"`
	Orderby     ListOrderBy        `thrift:"orderby,3" json:"orderby"`
	WithDeleted bool               `thrift:"with_deleted,4" json:"with_deleted"`
	WithCounter bool               `thrift:"with_counter,5" json:"with_counter"`
	Carrier     common.CarrierCode `thrift:"carrier,6" json:"carrier"`
	Category    Category           `thrift:"category,7" json:"category"`
	AppwallProt AppWallProt        `thrift:"appwall_prot,8" json:"appwall_prot"`
	Channel     string             `thrift:"channel,9" json:"channel"`
	MediaSpec   bool               `thrift:"media_spec,10" json:"media_spec"`
	AdUserId    int32              `thrift:"ad_user_id,11" json:"ad_user_id"`
	IsPaused    int32              `thrift:"is_paused,12" json:"is_paused"`
}

func NewListCriteria() *ListCriteria {
	return &ListCriteria{
		Orderby: math.MinInt32 - 1, // unset sentinal value

		WithDeleted: false,

		WithCounter: false,

		Carrier: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value

		MediaSpec: false,

		AdUserId: -1,

		IsPaused: 0,
	}
}

func (p *ListCriteria) IsSetOrderby() bool {
	return int64(p.Orderby) != math.MinInt32-1
}

func (p *ListCriteria) IsSetCarrier() bool {
	return int64(p.Carrier) != math.MinInt32-1
}

func (p *ListCriteria) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *ListCriteria) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListCriteria) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListCriteria) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListCriteria) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Orderby = ListOrderBy(v)
	}
	return nil
}

func (p *ListCriteria) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.WithDeleted = v
	}
	return nil
}

func (p *ListCriteria) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.WithCounter = v
	}
	return nil
}

func (p *ListCriteria) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Carrier = common.CarrierCode(v)
	}
	return nil
}

func (p *ListCriteria) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Category = Category(v)
	}
	return nil
}

func (p *ListCriteria) readField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.AppwallProt[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ListCriteria) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *ListCriteria) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.MediaSpec = v
	}
	return nil
}

func (p *ListCriteria) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AdUserId = v
	}
	return nil
}

func (p *ListCriteria) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.IsPaused = v
	}
	return nil
}

func (p *ListCriteria) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ListCriteria"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListCriteria) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:offset: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:limit: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderby() {
		if err := oprot.WriteFieldBegin("orderby", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:orderby: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Orderby)); err != nil {
			return fmt.Errorf("%T.orderby (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:orderby: %s", p, err)
		}
	}
	return err
}

func (p *ListCriteria) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_deleted", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:with_deleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithDeleted)); err != nil {
		return fmt.Errorf("%T.with_deleted (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:with_deleted: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_counter", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:with_counter: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithCounter)); err != nil {
		return fmt.Errorf("%T.with_counter (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:with_counter: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:carrier: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:carrier: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:category: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField8(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *ListCriteria) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:channel: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_spec", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:media_spec: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaSpec)); err != nil {
		return fmt.Errorf("%T.media_spec (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:media_spec: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_user_id", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:ad_user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdUserId)); err != nil {
		return fmt.Errorf("%T.ad_user_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:ad_user_id: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_paused", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:is_paused: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsPaused)); err != nil {
		return fmt.Errorf("%T.is_paused (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:is_paused: %s", p, err)
	}
	return err
}

func (p *ListCriteria) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCriteria(%+v)", *p)
}

type ListGiftCriteria struct {
	Offset      int32       `thrift:"offset,1" json:"offset"`
	Limit       int32       `thrift:"limit,2" json:"limit"`
	AppwallProt AppWallProt `thrift:"appwall_prot,3" json:"appwall_prot"`
	IsAll       bool        `thrift:"is_all,4" json:"is_all"`
	UserId      int32       `thrift:"user_id,5" json:"user_id"`
}

func NewListGiftCriteria() *ListGiftCriteria {
	return &ListGiftCriteria{}
}

func (p *ListGiftCriteria) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGiftCriteria) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListGiftCriteria) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListGiftCriteria) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = v
		}
		var _val3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = v
		}
		p.AppwallProt[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ListGiftCriteria) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.IsAll = v
	}
	return nil
}

func (p *ListGiftCriteria) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *ListGiftCriteria) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ListGiftCriteria"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGiftCriteria) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:offset: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:limit: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftCriteria) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_all", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:is_all: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAll)); err != nil {
		return fmt.Errorf("%T.is_all (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:is_all: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserId)); err != nil {
		return fmt.Errorf("%T.user_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:user_id: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGiftCriteria(%+v)", *p)
}
