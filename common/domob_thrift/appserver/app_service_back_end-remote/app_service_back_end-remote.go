// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"appserver"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 createAppMain(RequestHeader header, AppMain app)")
	fmt.Fprintln(os.Stderr, "  void editAppMain(RequestHeader header, AppMain app, bool set_edited)")
	fmt.Fprintln(os.Stderr, "  i32 updateAppMain(RequestHeader header, AppMain app)")
	fmt.Fprintln(os.Stderr, "  void deleteAppMain(RequestHeader header, string package_name, bool permanent)")
	fmt.Fprintln(os.Stderr, "  MainList listDeletedAppMain(RequestHeader header, Category category, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  MainList listUpdatedAppMain(RequestHeader header, Market market, Category category, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getAppUpdateSummary(RequestHeader header, TimeInt start_time, TimeInt end_time)")
	fmt.Fprintln(os.Stderr, "  MainList listAppMainByPubtime(RequestHeader header, TimeInt pubtime, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  MainList listAppMainInGroup(RequestHeader header, i32 appid, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  void setGameGroup(RequestHeader header, i32 parent_appid,  appids)")
	fmt.Fprintln(os.Stderr, "  i32 uploadResource(RequestHeader header, AppResource res, bool overwrite)")
	fmt.Fprintln(os.Stderr, "  i32 updateResource(RequestHeader header, AppResource res)")
	fmt.Fprintln(os.Stderr, "   getAppResource(RequestHeader header, i32 appid, string package_name, ResourceType type, i32 seqid, bool with_content, bool update)")
	fmt.Fprintln(os.Stderr, "   getAppBlacklist(RequestHeader header, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "   getRawAppMainById(RequestHeader header, i32 appid)")
	fmt.Fprintln(os.Stderr, "   getRawAppMainByPname(RequestHeader header, string package_name)")
	fmt.Fprintln(os.Stderr, "  void rankAppMain(RequestHeader header, Category category,  appids, i32 rank_type, bool save_history)")
	fmt.Fprintln(os.Stderr, "   getHotApp(RequestHeader header, i32 rank_type, Category category)")
	fmt.Fprintln(os.Stderr, "  AppMain getAppMain(RequestHeader header, i32 appid)")
	fmt.Fprintln(os.Stderr, "  AppMain getAppByPname(RequestHeader header, string pname)")
	fmt.Fprintln(os.Stderr, "  AppWallGame getWallGame(RequestHeader header, i32 id, AppWallProt appwall_prot)")
	fmt.Fprintln(os.Stderr, "   getCategorySummaries(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getTopics(RequestHeader header, bool with_disabled)")
	fmt.Fprintln(os.Stderr, "  Topic getTopic(RequestHeader header, i32 topic_id)")
	fmt.Fprintln(os.Stderr, "   listAppByTopic(RequestHeader header, i32 topicid, CarrierCode carrier, AppWallProt appwall_prot)")
	fmt.Fprintln(os.Stderr, "   getBanners(RequestHeader header, bool with_disabled, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "   getRecommendation(RequestHeader header, RecommendType recom_type, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  MainList searchDuoyou(RequestHeader header, string query, SearchType stype, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  GameList searchGame(RequestHeader header, string query, SearchType stype, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  MainList listDuoyou(RequestHeader header, Market market, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  GiftResult listGift(RequestHeader header, ListGiftCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  GameList listGame(RequestHeader header, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "   checkUpdate(RequestHeader header,  packages, bool withDesc)")
	fmt.Fprintln(os.Stderr, "   checkGameUpdate(RequestHeader header,  packages, AppWallProt appwall_prot)")
	fmt.Fprintln(os.Stderr, "  void supportApp(RequestHeader header, i32 appid)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := appserver.NewAppServiceBackEndClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "createAppMain":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CreateAppMain requires 2 args")
			flag.Usage()
		}
		arg357 := flag.Arg(1)
		mbTrans358 := thrift.NewTMemoryBufferLen(len(arg357))
		defer mbTrans358.Close()
		_, err359 := mbTrans358.WriteString(arg357)
		if err359 != nil {
			Usage()
			return
		}
		factory360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt361 := factory360.GetProtocol(mbTrans358)
		argvalue0 := appserver.NewRequestHeader()
		err362 := argvalue0.Read(jsProt361)
		if err362 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg363 := flag.Arg(2)
		mbTrans364 := thrift.NewTMemoryBufferLen(len(arg363))
		defer mbTrans364.Close()
		_, err365 := mbTrans364.WriteString(arg363)
		if err365 != nil {
			Usage()
			return
		}
		factory366 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt367 := factory366.GetProtocol(mbTrans364)
		argvalue1 := appserver.NewAppMain()
		err368 := argvalue1.Read(jsProt367)
		if err368 != nil {
			Usage()
			return
		}
		value1 := appserver.AppMain(argvalue1)
		fmt.Print(client.CreateAppMain(value0, value1))
		fmt.Print("\n")
		break
	case "editAppMain":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAppMain requires 3 args")
			flag.Usage()
		}
		arg369 := flag.Arg(1)
		mbTrans370 := thrift.NewTMemoryBufferLen(len(arg369))
		defer mbTrans370.Close()
		_, err371 := mbTrans370.WriteString(arg369)
		if err371 != nil {
			Usage()
			return
		}
		factory372 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt373 := factory372.GetProtocol(mbTrans370)
		argvalue0 := appserver.NewRequestHeader()
		err374 := argvalue0.Read(jsProt373)
		if err374 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg375 := flag.Arg(2)
		mbTrans376 := thrift.NewTMemoryBufferLen(len(arg375))
		defer mbTrans376.Close()
		_, err377 := mbTrans376.WriteString(arg375)
		if err377 != nil {
			Usage()
			return
		}
		factory378 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt379 := factory378.GetProtocol(mbTrans376)
		argvalue1 := appserver.NewAppMain()
		err380 := argvalue1.Read(jsProt379)
		if err380 != nil {
			Usage()
			return
		}
		value1 := appserver.AppMain(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.EditAppMain(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateAppMain":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateAppMain requires 2 args")
			flag.Usage()
		}
		arg382 := flag.Arg(1)
		mbTrans383 := thrift.NewTMemoryBufferLen(len(arg382))
		defer mbTrans383.Close()
		_, err384 := mbTrans383.WriteString(arg382)
		if err384 != nil {
			Usage()
			return
		}
		factory385 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt386 := factory385.GetProtocol(mbTrans383)
		argvalue0 := appserver.NewRequestHeader()
		err387 := argvalue0.Read(jsProt386)
		if err387 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg388 := flag.Arg(2)
		mbTrans389 := thrift.NewTMemoryBufferLen(len(arg388))
		defer mbTrans389.Close()
		_, err390 := mbTrans389.WriteString(arg388)
		if err390 != nil {
			Usage()
			return
		}
		factory391 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt392 := factory391.GetProtocol(mbTrans389)
		argvalue1 := appserver.NewAppMain()
		err393 := argvalue1.Read(jsProt392)
		if err393 != nil {
			Usage()
			return
		}
		value1 := appserver.AppMain(argvalue1)
		fmt.Print(client.UpdateAppMain(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAppMain":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAppMain requires 3 args")
			flag.Usage()
		}
		arg394 := flag.Arg(1)
		mbTrans395 := thrift.NewTMemoryBufferLen(len(arg394))
		defer mbTrans395.Close()
		_, err396 := mbTrans395.WriteString(arg394)
		if err396 != nil {
			Usage()
			return
		}
		factory397 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt398 := factory397.GetProtocol(mbTrans395)
		argvalue0 := appserver.NewRequestHeader()
		err399 := argvalue0.Read(jsProt398)
		if err399 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.DeleteAppMain(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listDeletedAppMain":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListDeletedAppMain requires 4 args")
			flag.Usage()
		}
		arg402 := flag.Arg(1)
		mbTrans403 := thrift.NewTMemoryBufferLen(len(arg402))
		defer mbTrans403.Close()
		_, err404 := mbTrans403.WriteString(arg402)
		if err404 != nil {
			Usage()
			return
		}
		factory405 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt406 := factory405.GetProtocol(mbTrans403)
		argvalue0 := appserver.NewRequestHeader()
		err407 := argvalue0.Read(jsProt406)
		if err407 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appserver.Category(tmp1)
		value1 := appserver.Category(argvalue1)
		tmp2, err408 := (strconv.Atoi(flag.Arg(3)))
		if err408 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err409 := (strconv.Atoi(flag.Arg(4)))
		if err409 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListDeletedAppMain(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listUpdatedAppMain":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListUpdatedAppMain requires 5 args")
			flag.Usage()
		}
		arg410 := flag.Arg(1)
		mbTrans411 := thrift.NewTMemoryBufferLen(len(arg410))
		defer mbTrans411.Close()
		_, err412 := mbTrans411.WriteString(arg410)
		if err412 != nil {
			Usage()
			return
		}
		factory413 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt414 := factory413.GetProtocol(mbTrans411)
		argvalue0 := appserver.NewRequestHeader()
		err415 := argvalue0.Read(jsProt414)
		if err415 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appserver.Market(tmp1)
		value1 := appserver.Market(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.Category(tmp2)
		value2 := appserver.Category(argvalue2)
		tmp3, err416 := (strconv.Atoi(flag.Arg(4)))
		if err416 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err417 := (strconv.Atoi(flag.Arg(5)))
		if err417 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.ListUpdatedAppMain(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getAppUpdateSummary":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAppUpdateSummary requires 3 args")
			flag.Usage()
		}
		arg418 := flag.Arg(1)
		mbTrans419 := thrift.NewTMemoryBufferLen(len(arg418))
		defer mbTrans419.Close()
		_, err420 := mbTrans419.WriteString(arg418)
		if err420 != nil {
			Usage()
			return
		}
		factory421 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt422 := factory421.GetProtocol(mbTrans419)
		argvalue0 := appserver.NewRequestHeader()
		err423 := argvalue0.Read(jsProt422)
		if err423 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1, err424 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err424 != nil {
			Usage()
			return
		}
		value1 := appserver.TimeInt(argvalue1)
		argvalue2, err425 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err425 != nil {
			Usage()
			return
		}
		value2 := appserver.TimeInt(argvalue2)
		fmt.Print(client.GetAppUpdateSummary(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAppMainByPubtime":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAppMainByPubtime requires 4 args")
			flag.Usage()
		}
		arg426 := flag.Arg(1)
		mbTrans427 := thrift.NewTMemoryBufferLen(len(arg426))
		defer mbTrans427.Close()
		_, err428 := mbTrans427.WriteString(arg426)
		if err428 != nil {
			Usage()
			return
		}
		factory429 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt430 := factory429.GetProtocol(mbTrans427)
		argvalue0 := appserver.NewRequestHeader()
		err431 := argvalue0.Read(jsProt430)
		if err431 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1, err432 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err432 != nil {
			Usage()
			return
		}
		value1 := appserver.TimeInt(argvalue1)
		tmp2, err433 := (strconv.Atoi(flag.Arg(3)))
		if err433 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err434 := (strconv.Atoi(flag.Arg(4)))
		if err434 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListAppMainByPubtime(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listAppMainInGroup":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAppMainInGroup requires 4 args")
			flag.Usage()
		}
		arg435 := flag.Arg(1)
		mbTrans436 := thrift.NewTMemoryBufferLen(len(arg435))
		defer mbTrans436.Close()
		_, err437 := mbTrans436.WriteString(arg435)
		if err437 != nil {
			Usage()
			return
		}
		factory438 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt439 := factory438.GetProtocol(mbTrans436)
		argvalue0 := appserver.NewRequestHeader()
		err440 := argvalue0.Read(jsProt439)
		if err440 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err441 := (strconv.Atoi(flag.Arg(2)))
		if err441 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err442 := (strconv.Atoi(flag.Arg(3)))
		if err442 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err443 := (strconv.Atoi(flag.Arg(4)))
		if err443 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListAppMainInGroup(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "setGameGroup":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SetGameGroup requires 3 args")
			flag.Usage()
		}
		arg444 := flag.Arg(1)
		mbTrans445 := thrift.NewTMemoryBufferLen(len(arg444))
		defer mbTrans445.Close()
		_, err446 := mbTrans445.WriteString(arg444)
		if err446 != nil {
			Usage()
			return
		}
		factory447 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt448 := factory447.GetProtocol(mbTrans445)
		argvalue0 := appserver.NewRequestHeader()
		err449 := argvalue0.Read(jsProt448)
		if err449 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err450 := (strconv.Atoi(flag.Arg(2)))
		if err450 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg451 := flag.Arg(3)
		mbTrans452 := thrift.NewTMemoryBufferLen(len(arg451))
		defer mbTrans452.Close()
		_, err453 := mbTrans452.WriteString(arg451)
		if err453 != nil {
			Usage()
			return
		}
		factory454 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt455 := factory454.GetProtocol(mbTrans452)
		containerStruct2 := appserver.NewSetGameGroupArgs()
		err456 := containerStruct2.ReadField3(jsProt455)
		if err456 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Appids
		value2 := argvalue2
		fmt.Print(client.SetGameGroup(value0, value1, value2))
		fmt.Print("\n")
		break
	case "uploadResource":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UploadResource requires 3 args")
			flag.Usage()
		}
		arg457 := flag.Arg(1)
		mbTrans458 := thrift.NewTMemoryBufferLen(len(arg457))
		defer mbTrans458.Close()
		_, err459 := mbTrans458.WriteString(arg457)
		if err459 != nil {
			Usage()
			return
		}
		factory460 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt461 := factory460.GetProtocol(mbTrans458)
		argvalue0 := appserver.NewRequestHeader()
		err462 := argvalue0.Read(jsProt461)
		if err462 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg463 := flag.Arg(2)
		mbTrans464 := thrift.NewTMemoryBufferLen(len(arg463))
		defer mbTrans464.Close()
		_, err465 := mbTrans464.WriteString(arg463)
		if err465 != nil {
			Usage()
			return
		}
		factory466 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt467 := factory466.GetProtocol(mbTrans464)
		argvalue1 := appserver.NewAppResource()
		err468 := argvalue1.Read(jsProt467)
		if err468 != nil {
			Usage()
			return
		}
		value1 := appserver.AppResource(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.UploadResource(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateResource":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateResource requires 2 args")
			flag.Usage()
		}
		arg470 := flag.Arg(1)
		mbTrans471 := thrift.NewTMemoryBufferLen(len(arg470))
		defer mbTrans471.Close()
		_, err472 := mbTrans471.WriteString(arg470)
		if err472 != nil {
			Usage()
			return
		}
		factory473 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt474 := factory473.GetProtocol(mbTrans471)
		argvalue0 := appserver.NewRequestHeader()
		err475 := argvalue0.Read(jsProt474)
		if err475 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg476 := flag.Arg(2)
		mbTrans477 := thrift.NewTMemoryBufferLen(len(arg476))
		defer mbTrans477.Close()
		_, err478 := mbTrans477.WriteString(arg476)
		if err478 != nil {
			Usage()
			return
		}
		factory479 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt480 := factory479.GetProtocol(mbTrans477)
		argvalue1 := appserver.NewAppResource()
		err481 := argvalue1.Read(jsProt480)
		if err481 != nil {
			Usage()
			return
		}
		value1 := appserver.AppResource(argvalue1)
		fmt.Print(client.UpdateResource(value0, value1))
		fmt.Print("\n")
		break
	case "getAppResource":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "GetAppResource requires 7 args")
			flag.Usage()
		}
		arg482 := flag.Arg(1)
		mbTrans483 := thrift.NewTMemoryBufferLen(len(arg482))
		defer mbTrans483.Close()
		_, err484 := mbTrans483.WriteString(arg482)
		if err484 != nil {
			Usage()
			return
		}
		factory485 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt486 := factory485.GetProtocol(mbTrans483)
		argvalue0 := appserver.NewRequestHeader()
		err487 := argvalue0.Read(jsProt486)
		if err487 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err488 := (strconv.Atoi(flag.Arg(2)))
		if err488 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := appserver.ResourceType(tmp3)
		value3 := appserver.ResourceType(argvalue3)
		tmp4, err490 := (strconv.Atoi(flag.Arg(5)))
		if err490 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		argvalue6 := flag.Arg(7) == "true"
		value6 := argvalue6
		fmt.Print(client.GetAppResource(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getAppBlacklist":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppBlacklist requires 2 args")
			flag.Usage()
		}
		arg493 := flag.Arg(1)
		mbTrans494 := thrift.NewTMemoryBufferLen(len(arg493))
		defer mbTrans494.Close()
		_, err495 := mbTrans494.WriteString(arg493)
		if err495 != nil {
			Usage()
			return
		}
		factory496 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt497 := factory496.GetProtocol(mbTrans494)
		argvalue0 := appserver.NewRequestHeader()
		err498 := argvalue0.Read(jsProt497)
		if err498 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg499 := flag.Arg(2)
		mbTrans500 := thrift.NewTMemoryBufferLen(len(arg499))
		defer mbTrans500.Close()
		_, err501 := mbTrans500.WriteString(arg499)
		if err501 != nil {
			Usage()
			return
		}
		factory502 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt503 := factory502.GetProtocol(mbTrans500)
		argvalue1 := appserver.NewListCriteria()
		err504 := argvalue1.Read(jsProt503)
		if err504 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetAppBlacklist(value0, value1))
		fmt.Print("\n")
		break
	case "getRawAppMainById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetRawAppMainById requires 2 args")
			flag.Usage()
		}
		arg505 := flag.Arg(1)
		mbTrans506 := thrift.NewTMemoryBufferLen(len(arg505))
		defer mbTrans506.Close()
		_, err507 := mbTrans506.WriteString(arg505)
		if err507 != nil {
			Usage()
			return
		}
		factory508 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt509 := factory508.GetProtocol(mbTrans506)
		argvalue0 := appserver.NewRequestHeader()
		err510 := argvalue0.Read(jsProt509)
		if err510 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err511 := (strconv.Atoi(flag.Arg(2)))
		if err511 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetRawAppMainById(value0, value1))
		fmt.Print("\n")
		break
	case "getRawAppMainByPname":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetRawAppMainByPname requires 2 args")
			flag.Usage()
		}
		arg512 := flag.Arg(1)
		mbTrans513 := thrift.NewTMemoryBufferLen(len(arg512))
		defer mbTrans513.Close()
		_, err514 := mbTrans513.WriteString(arg512)
		if err514 != nil {
			Usage()
			return
		}
		factory515 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt516 := factory515.GetProtocol(mbTrans513)
		argvalue0 := appserver.NewRequestHeader()
		err517 := argvalue0.Read(jsProt516)
		if err517 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetRawAppMainByPname(value0, value1))
		fmt.Print("\n")
		break
	case "rankAppMain":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "RankAppMain requires 5 args")
			flag.Usage()
		}
		arg519 := flag.Arg(1)
		mbTrans520 := thrift.NewTMemoryBufferLen(len(arg519))
		defer mbTrans520.Close()
		_, err521 := mbTrans520.WriteString(arg519)
		if err521 != nil {
			Usage()
			return
		}
		factory522 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt523 := factory522.GetProtocol(mbTrans520)
		argvalue0 := appserver.NewRequestHeader()
		err524 := argvalue0.Read(jsProt523)
		if err524 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appserver.Category(tmp1)
		value1 := appserver.Category(argvalue1)
		arg525 := flag.Arg(3)
		mbTrans526 := thrift.NewTMemoryBufferLen(len(arg525))
		defer mbTrans526.Close()
		_, err527 := mbTrans526.WriteString(arg525)
		if err527 != nil {
			Usage()
			return
		}
		factory528 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt529 := factory528.GetProtocol(mbTrans526)
		containerStruct2 := appserver.NewRankAppMainArgs()
		err530 := containerStruct2.ReadField3(jsProt529)
		if err530 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Appids
		value2 := argvalue2
		tmp3, err531 := (strconv.Atoi(flag.Arg(4)))
		if err531 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.RankAppMain(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getHotApp":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetHotApp requires 3 args")
			flag.Usage()
		}
		arg533 := flag.Arg(1)
		mbTrans534 := thrift.NewTMemoryBufferLen(len(arg533))
		defer mbTrans534.Close()
		_, err535 := mbTrans534.WriteString(arg533)
		if err535 != nil {
			Usage()
			return
		}
		factory536 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt537 := factory536.GetProtocol(mbTrans534)
		argvalue0 := appserver.NewRequestHeader()
		err538 := argvalue0.Read(jsProt537)
		if err538 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err539 := (strconv.Atoi(flag.Arg(2)))
		if err539 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.Category(tmp2)
		value2 := appserver.Category(argvalue2)
		fmt.Print(client.GetHotApp(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAppMain":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppMain requires 2 args")
			flag.Usage()
		}
		arg540 := flag.Arg(1)
		mbTrans541 := thrift.NewTMemoryBufferLen(len(arg540))
		defer mbTrans541.Close()
		_, err542 := mbTrans541.WriteString(arg540)
		if err542 != nil {
			Usage()
			return
		}
		factory543 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt544 := factory543.GetProtocol(mbTrans541)
		argvalue0 := appserver.NewRequestHeader()
		err545 := argvalue0.Read(jsProt544)
		if err545 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err546 := (strconv.Atoi(flag.Arg(2)))
		if err546 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetAppMain(value0, value1))
		fmt.Print("\n")
		break
	case "getAppByPname":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppByPname requires 2 args")
			flag.Usage()
		}
		arg547 := flag.Arg(1)
		mbTrans548 := thrift.NewTMemoryBufferLen(len(arg547))
		defer mbTrans548.Close()
		_, err549 := mbTrans548.WriteString(arg547)
		if err549 != nil {
			Usage()
			return
		}
		factory550 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt551 := factory550.GetProtocol(mbTrans548)
		argvalue0 := appserver.NewRequestHeader()
		err552 := argvalue0.Read(jsProt551)
		if err552 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetAppByPname(value0, value1))
		fmt.Print("\n")
		break
	case "getWallGame":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetWallGame requires 3 args")
			flag.Usage()
		}
		arg554 := flag.Arg(1)
		mbTrans555 := thrift.NewTMemoryBufferLen(len(arg554))
		defer mbTrans555.Close()
		_, err556 := mbTrans555.WriteString(arg554)
		if err556 != nil {
			Usage()
			return
		}
		factory557 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt558 := factory557.GetProtocol(mbTrans555)
		argvalue0 := appserver.NewRequestHeader()
		err559 := argvalue0.Read(jsProt558)
		if err559 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err560 := (strconv.Atoi(flag.Arg(2)))
		if err560 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg561 := flag.Arg(3)
		mbTrans562 := thrift.NewTMemoryBufferLen(len(arg561))
		defer mbTrans562.Close()
		_, err563 := mbTrans562.WriteString(arg561)
		if err563 != nil {
			Usage()
			return
		}
		factory564 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt565 := factory564.GetProtocol(mbTrans562)
		containerStruct2 := appserver.NewGetWallGameArgs()
		err566 := containerStruct2.ReadField3(jsProt565)
		if err566 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AppwallProt
		value2 := appserver.AppWallProt(argvalue2)
		fmt.Print(client.GetWallGame(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getCategorySummaries":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCategorySummaries requires 1 args")
			flag.Usage()
		}
		arg567 := flag.Arg(1)
		mbTrans568 := thrift.NewTMemoryBufferLen(len(arg567))
		defer mbTrans568.Close()
		_, err569 := mbTrans568.WriteString(arg567)
		if err569 != nil {
			Usage()
			return
		}
		factory570 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt571 := factory570.GetProtocol(mbTrans568)
		argvalue0 := appserver.NewRequestHeader()
		err572 := argvalue0.Read(jsProt571)
		if err572 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		fmt.Print(client.GetCategorySummaries(value0))
		fmt.Print("\n")
		break
	case "getTopics":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTopics requires 2 args")
			flag.Usage()
		}
		arg573 := flag.Arg(1)
		mbTrans574 := thrift.NewTMemoryBufferLen(len(arg573))
		defer mbTrans574.Close()
		_, err575 := mbTrans574.WriteString(arg573)
		if err575 != nil {
			Usage()
			return
		}
		factory576 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt577 := factory576.GetProtocol(mbTrans574)
		argvalue0 := appserver.NewRequestHeader()
		err578 := argvalue0.Read(jsProt577)
		if err578 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetTopics(value0, value1))
		fmt.Print("\n")
		break
	case "getTopic":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTopic requires 2 args")
			flag.Usage()
		}
		arg580 := flag.Arg(1)
		mbTrans581 := thrift.NewTMemoryBufferLen(len(arg580))
		defer mbTrans581.Close()
		_, err582 := mbTrans581.WriteString(arg580)
		if err582 != nil {
			Usage()
			return
		}
		factory583 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt584 := factory583.GetProtocol(mbTrans581)
		argvalue0 := appserver.NewRequestHeader()
		err585 := argvalue0.Read(jsProt584)
		if err585 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err586 := (strconv.Atoi(flag.Arg(2)))
		if err586 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetTopic(value0, value1))
		fmt.Print("\n")
		break
	case "listAppByTopic":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAppByTopic requires 4 args")
			flag.Usage()
		}
		arg587 := flag.Arg(1)
		mbTrans588 := thrift.NewTMemoryBufferLen(len(arg587))
		defer mbTrans588.Close()
		_, err589 := mbTrans588.WriteString(arg587)
		if err589 != nil {
			Usage()
			return
		}
		factory590 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt591 := factory590.GetProtocol(mbTrans588)
		argvalue0 := appserver.NewRequestHeader()
		err592 := argvalue0.Read(jsProt591)
		if err592 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err593 := (strconv.Atoi(flag.Arg(2)))
		if err593 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.CarrierCode(tmp2)
		value2 := appserver.CarrierCode(argvalue2)
		arg594 := flag.Arg(4)
		mbTrans595 := thrift.NewTMemoryBufferLen(len(arg594))
		defer mbTrans595.Close()
		_, err596 := mbTrans595.WriteString(arg594)
		if err596 != nil {
			Usage()
			return
		}
		factory597 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt598 := factory597.GetProtocol(mbTrans595)
		containerStruct3 := appserver.NewListAppByTopicArgs()
		err599 := containerStruct3.ReadField4(jsProt598)
		if err599 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.AppwallProt
		value3 := appserver.AppWallProt(argvalue3)
		fmt.Print(client.ListAppByTopic(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getBanners":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetBanners requires 3 args")
			flag.Usage()
		}
		arg600 := flag.Arg(1)
		mbTrans601 := thrift.NewTMemoryBufferLen(len(arg600))
		defer mbTrans601.Close()
		_, err602 := mbTrans601.WriteString(arg600)
		if err602 != nil {
			Usage()
			return
		}
		factory603 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt604 := factory603.GetProtocol(mbTrans601)
		argvalue0 := appserver.NewRequestHeader()
		err605 := argvalue0.Read(jsProt604)
		if err605 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		arg607 := flag.Arg(3)
		mbTrans608 := thrift.NewTMemoryBufferLen(len(arg607))
		defer mbTrans608.Close()
		_, err609 := mbTrans608.WriteString(arg607)
		if err609 != nil {
			Usage()
			return
		}
		factory610 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt611 := factory610.GetProtocol(mbTrans608)
		argvalue2 := appserver.NewListCriteria()
		err612 := argvalue2.Read(jsProt611)
		if err612 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetBanners(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getRecommendation":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetRecommendation requires 3 args")
			flag.Usage()
		}
		arg613 := flag.Arg(1)
		mbTrans614 := thrift.NewTMemoryBufferLen(len(arg613))
		defer mbTrans614.Close()
		_, err615 := mbTrans614.WriteString(arg613)
		if err615 != nil {
			Usage()
			return
		}
		factory616 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt617 := factory616.GetProtocol(mbTrans614)
		argvalue0 := appserver.NewRequestHeader()
		err618 := argvalue0.Read(jsProt617)
		if err618 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appserver.RecommendType(tmp1)
		value1 := argvalue1
		arg619 := flag.Arg(3)
		mbTrans620 := thrift.NewTMemoryBufferLen(len(arg619))
		defer mbTrans620.Close()
		_, err621 := mbTrans620.WriteString(arg619)
		if err621 != nil {
			Usage()
			return
		}
		factory622 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt623 := factory622.GetProtocol(mbTrans620)
		argvalue2 := appserver.NewListCriteria()
		err624 := argvalue2.Read(jsProt623)
		if err624 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetRecommendation(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchDuoyou":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchDuoyou requires 4 args")
			flag.Usage()
		}
		arg625 := flag.Arg(1)
		mbTrans626 := thrift.NewTMemoryBufferLen(len(arg625))
		defer mbTrans626.Close()
		_, err627 := mbTrans626.WriteString(arg625)
		if err627 != nil {
			Usage()
			return
		}
		factory628 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt629 := factory628.GetProtocol(mbTrans626)
		argvalue0 := appserver.NewRequestHeader()
		err630 := argvalue0.Read(jsProt629)
		if err630 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.SearchType(tmp2)
		value2 := argvalue2
		arg632 := flag.Arg(4)
		mbTrans633 := thrift.NewTMemoryBufferLen(len(arg632))
		defer mbTrans633.Close()
		_, err634 := mbTrans633.WriteString(arg632)
		if err634 != nil {
			Usage()
			return
		}
		factory635 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt636 := factory635.GetProtocol(mbTrans633)
		argvalue3 := appserver.NewListCriteria()
		err637 := argvalue3.Read(jsProt636)
		if err637 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchDuoyou(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "searchGame":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchGame requires 4 args")
			flag.Usage()
		}
		arg638 := flag.Arg(1)
		mbTrans639 := thrift.NewTMemoryBufferLen(len(arg638))
		defer mbTrans639.Close()
		_, err640 := mbTrans639.WriteString(arg638)
		if err640 != nil {
			Usage()
			return
		}
		factory641 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt642 := factory641.GetProtocol(mbTrans639)
		argvalue0 := appserver.NewRequestHeader()
		err643 := argvalue0.Read(jsProt642)
		if err643 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.SearchType(tmp2)
		value2 := argvalue2
		arg645 := flag.Arg(4)
		mbTrans646 := thrift.NewTMemoryBufferLen(len(arg645))
		defer mbTrans646.Close()
		_, err647 := mbTrans646.WriteString(arg645)
		if err647 != nil {
			Usage()
			return
		}
		factory648 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt649 := factory648.GetProtocol(mbTrans646)
		argvalue3 := appserver.NewListCriteria()
		err650 := argvalue3.Read(jsProt649)
		if err650 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchGame(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listDuoyou":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListDuoyou requires 3 args")
			flag.Usage()
		}
		arg651 := flag.Arg(1)
		mbTrans652 := thrift.NewTMemoryBufferLen(len(arg651))
		defer mbTrans652.Close()
		_, err653 := mbTrans652.WriteString(arg651)
		if err653 != nil {
			Usage()
			return
		}
		factory654 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt655 := factory654.GetProtocol(mbTrans652)
		argvalue0 := appserver.NewRequestHeader()
		err656 := argvalue0.Read(jsProt655)
		if err656 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appserver.Market(tmp1)
		value1 := appserver.Market(argvalue1)
		arg657 := flag.Arg(3)
		mbTrans658 := thrift.NewTMemoryBufferLen(len(arg657))
		defer mbTrans658.Close()
		_, err659 := mbTrans658.WriteString(arg657)
		if err659 != nil {
			Usage()
			return
		}
		factory660 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt661 := factory660.GetProtocol(mbTrans658)
		argvalue2 := appserver.NewListCriteria()
		err662 := argvalue2.Read(jsProt661)
		if err662 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ListDuoyou(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listGift":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListGift requires 2 args")
			flag.Usage()
		}
		arg663 := flag.Arg(1)
		mbTrans664 := thrift.NewTMemoryBufferLen(len(arg663))
		defer mbTrans664.Close()
		_, err665 := mbTrans664.WriteString(arg663)
		if err665 != nil {
			Usage()
			return
		}
		factory666 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt667 := factory666.GetProtocol(mbTrans664)
		argvalue0 := appserver.NewRequestHeader()
		err668 := argvalue0.Read(jsProt667)
		if err668 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg669 := flag.Arg(2)
		mbTrans670 := thrift.NewTMemoryBufferLen(len(arg669))
		defer mbTrans670.Close()
		_, err671 := mbTrans670.WriteString(arg669)
		if err671 != nil {
			Usage()
			return
		}
		factory672 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt673 := factory672.GetProtocol(mbTrans670)
		argvalue1 := appserver.NewListGiftCriteria()
		err674 := argvalue1.Read(jsProt673)
		if err674 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ListGift(value0, value1))
		fmt.Print("\n")
		break
	case "listGame":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListGame requires 2 args")
			flag.Usage()
		}
		arg675 := flag.Arg(1)
		mbTrans676 := thrift.NewTMemoryBufferLen(len(arg675))
		defer mbTrans676.Close()
		_, err677 := mbTrans676.WriteString(arg675)
		if err677 != nil {
			Usage()
			return
		}
		factory678 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt679 := factory678.GetProtocol(mbTrans676)
		argvalue0 := appserver.NewRequestHeader()
		err680 := argvalue0.Read(jsProt679)
		if err680 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg681 := flag.Arg(2)
		mbTrans682 := thrift.NewTMemoryBufferLen(len(arg681))
		defer mbTrans682.Close()
		_, err683 := mbTrans682.WriteString(arg681)
		if err683 != nil {
			Usage()
			return
		}
		factory684 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt685 := factory684.GetProtocol(mbTrans682)
		argvalue1 := appserver.NewListCriteria()
		err686 := argvalue1.Read(jsProt685)
		if err686 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ListGame(value0, value1))
		fmt.Print("\n")
		break
	case "checkUpdate":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CheckUpdate requires 3 args")
			flag.Usage()
		}
		arg687 := flag.Arg(1)
		mbTrans688 := thrift.NewTMemoryBufferLen(len(arg687))
		defer mbTrans688.Close()
		_, err689 := mbTrans688.WriteString(arg687)
		if err689 != nil {
			Usage()
			return
		}
		factory690 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt691 := factory690.GetProtocol(mbTrans688)
		argvalue0 := appserver.NewRequestHeader()
		err692 := argvalue0.Read(jsProt691)
		if err692 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg693 := flag.Arg(2)
		mbTrans694 := thrift.NewTMemoryBufferLen(len(arg693))
		defer mbTrans694.Close()
		_, err695 := mbTrans694.WriteString(arg693)
		if err695 != nil {
			Usage()
			return
		}
		factory696 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt697 := factory696.GetProtocol(mbTrans694)
		containerStruct1 := appserver.NewCheckUpdateArgs()
		err698 := containerStruct1.ReadField2(jsProt697)
		if err698 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Packages
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.CheckUpdate(value0, value1, value2))
		fmt.Print("\n")
		break
	case "checkGameUpdate":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CheckGameUpdate requires 3 args")
			flag.Usage()
		}
		arg700 := flag.Arg(1)
		mbTrans701 := thrift.NewTMemoryBufferLen(len(arg700))
		defer mbTrans701.Close()
		_, err702 := mbTrans701.WriteString(arg700)
		if err702 != nil {
			Usage()
			return
		}
		factory703 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt704 := factory703.GetProtocol(mbTrans701)
		argvalue0 := appserver.NewRequestHeader()
		err705 := argvalue0.Read(jsProt704)
		if err705 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg706 := flag.Arg(2)
		mbTrans707 := thrift.NewTMemoryBufferLen(len(arg706))
		defer mbTrans707.Close()
		_, err708 := mbTrans707.WriteString(arg706)
		if err708 != nil {
			Usage()
			return
		}
		factory709 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt710 := factory709.GetProtocol(mbTrans707)
		containerStruct1 := appserver.NewCheckGameUpdateArgs()
		err711 := containerStruct1.ReadField2(jsProt710)
		if err711 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Packages
		value1 := argvalue1
		arg712 := flag.Arg(3)
		mbTrans713 := thrift.NewTMemoryBufferLen(len(arg712))
		defer mbTrans713.Close()
		_, err714 := mbTrans713.WriteString(arg712)
		if err714 != nil {
			Usage()
			return
		}
		factory715 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt716 := factory715.GetProtocol(mbTrans713)
		containerStruct2 := appserver.NewCheckGameUpdateArgs()
		err717 := containerStruct2.ReadField3(jsProt716)
		if err717 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AppwallProt
		value2 := appserver.AppWallProt(argvalue2)
		fmt.Print(client.CheckGameUpdate(value0, value1, value2))
		fmt.Print("\n")
		break
	case "supportApp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SupportApp requires 2 args")
			flag.Usage()
		}
		arg718 := flag.Arg(1)
		mbTrans719 := thrift.NewTMemoryBufferLen(len(arg718))
		defer mbTrans719.Close()
		_, err720 := mbTrans719.WriteString(arg718)
		if err720 != nil {
			Usage()
			return
		}
		factory721 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt722 := factory721.GetProtocol(mbTrans719)
		argvalue0 := appserver.NewRequestHeader()
		err723 := argvalue0.Read(jsProt722)
		if err723 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err724 := (strconv.Atoi(flag.Arg(2)))
		if err724 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.SupportApp(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
