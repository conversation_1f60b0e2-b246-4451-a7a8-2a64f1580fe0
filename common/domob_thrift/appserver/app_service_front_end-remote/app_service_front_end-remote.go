// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"appserver"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  AppMain getAppMain(RequestHeader header, i32 appid)")
	fmt.Fprintln(os.Stderr, "  AppMain getAppByPname(RequestHeader header, string pname)")
	fmt.Fprintln(os.<PERSON>, "  AppWallGame getWallGame(RequestHeader header, i32 id, AppWallProt appwall_prot)")
	fmt.Fprintln(os.Stderr, "   getCategorySummaries(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   getTopics(RequestHeader header, bool with_disabled)")
	fmt.Fprintln(os.Stderr, "  Topic getTopic(RequestHeader header, i32 topic_id)")
	fmt.Fprintln(os.Stderr, "   listAppByTopic(RequestHeader header, i32 topicid, CarrierCode carrier, AppWallProt appwall_prot)")
	fmt.Fprintln(os.Stderr, "   getBanners(RequestHeader header, bool with_disabled, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "   getRecommendation(RequestHeader header, RecommendType recom_type, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  MainList searchDuoyou(RequestHeader header, string query, SearchType stype, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  GameList searchGame(RequestHeader header, string query, SearchType stype, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  MainList listDuoyou(RequestHeader header, Market market, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  GiftResult listGift(RequestHeader header, ListGiftCriteria criteria)")
	fmt.Fprintln(os.Stderr, "  GameList listGame(RequestHeader header, ListCriteria criteria)")
	fmt.Fprintln(os.Stderr, "   checkUpdate(RequestHeader header,  packages, bool withDesc)")
	fmt.Fprintln(os.Stderr, "   checkGameUpdate(RequestHeader header,  packages, AppWallProt appwall_prot)")
	fmt.Fprintln(os.Stderr, "  void supportApp(RequestHeader header, i32 appid)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := appserver.NewAppServiceFrontEndClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getAppMain":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppMain requires 2 args")
			flag.Usage()
		}
		arg91 := flag.Arg(1)
		mbTrans92 := thrift.NewTMemoryBufferLen(len(arg91))
		defer mbTrans92.Close()
		_, err93 := mbTrans92.WriteString(arg91)
		if err93 != nil {
			Usage()
			return
		}
		factory94 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt95 := factory94.GetProtocol(mbTrans92)
		argvalue0 := appserver.NewRequestHeader()
		err96 := argvalue0.Read(jsProt95)
		if err96 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err97 := (strconv.Atoi(flag.Arg(2)))
		if err97 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetAppMain(value0, value1))
		fmt.Print("\n")
		break
	case "getAppByPname":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppByPname requires 2 args")
			flag.Usage()
		}
		arg98 := flag.Arg(1)
		mbTrans99 := thrift.NewTMemoryBufferLen(len(arg98))
		defer mbTrans99.Close()
		_, err100 := mbTrans99.WriteString(arg98)
		if err100 != nil {
			Usage()
			return
		}
		factory101 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt102 := factory101.GetProtocol(mbTrans99)
		argvalue0 := appserver.NewRequestHeader()
		err103 := argvalue0.Read(jsProt102)
		if err103 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetAppByPname(value0, value1))
		fmt.Print("\n")
		break
	case "getWallGame":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetWallGame requires 3 args")
			flag.Usage()
		}
		arg105 := flag.Arg(1)
		mbTrans106 := thrift.NewTMemoryBufferLen(len(arg105))
		defer mbTrans106.Close()
		_, err107 := mbTrans106.WriteString(arg105)
		if err107 != nil {
			Usage()
			return
		}
		factory108 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt109 := factory108.GetProtocol(mbTrans106)
		argvalue0 := appserver.NewRequestHeader()
		err110 := argvalue0.Read(jsProt109)
		if err110 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err111 := (strconv.Atoi(flag.Arg(2)))
		if err111 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg112 := flag.Arg(3)
		mbTrans113 := thrift.NewTMemoryBufferLen(len(arg112))
		defer mbTrans113.Close()
		_, err114 := mbTrans113.WriteString(arg112)
		if err114 != nil {
			Usage()
			return
		}
		factory115 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt116 := factory115.GetProtocol(mbTrans113)
		containerStruct2 := appserver.NewGetWallGameArgs()
		err117 := containerStruct2.ReadField3(jsProt116)
		if err117 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AppwallProt
		value2 := appserver.AppWallProt(argvalue2)
		fmt.Print(client.GetWallGame(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getCategorySummaries":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCategorySummaries requires 1 args")
			flag.Usage()
		}
		arg118 := flag.Arg(1)
		mbTrans119 := thrift.NewTMemoryBufferLen(len(arg118))
		defer mbTrans119.Close()
		_, err120 := mbTrans119.WriteString(arg118)
		if err120 != nil {
			Usage()
			return
		}
		factory121 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt122 := factory121.GetProtocol(mbTrans119)
		argvalue0 := appserver.NewRequestHeader()
		err123 := argvalue0.Read(jsProt122)
		if err123 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		fmt.Print(client.GetCategorySummaries(value0))
		fmt.Print("\n")
		break
	case "getTopics":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTopics requires 2 args")
			flag.Usage()
		}
		arg124 := flag.Arg(1)
		mbTrans125 := thrift.NewTMemoryBufferLen(len(arg124))
		defer mbTrans125.Close()
		_, err126 := mbTrans125.WriteString(arg124)
		if err126 != nil {
			Usage()
			return
		}
		factory127 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt128 := factory127.GetProtocol(mbTrans125)
		argvalue0 := appserver.NewRequestHeader()
		err129 := argvalue0.Read(jsProt128)
		if err129 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		fmt.Print(client.GetTopics(value0, value1))
		fmt.Print("\n")
		break
	case "getTopic":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTopic requires 2 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := appserver.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err137 := (strconv.Atoi(flag.Arg(2)))
		if err137 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetTopic(value0, value1))
		fmt.Print("\n")
		break
	case "listAppByTopic":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAppByTopic requires 4 args")
			flag.Usage()
		}
		arg138 := flag.Arg(1)
		mbTrans139 := thrift.NewTMemoryBufferLen(len(arg138))
		defer mbTrans139.Close()
		_, err140 := mbTrans139.WriteString(arg138)
		if err140 != nil {
			Usage()
			return
		}
		factory141 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt142 := factory141.GetProtocol(mbTrans139)
		argvalue0 := appserver.NewRequestHeader()
		err143 := argvalue0.Read(jsProt142)
		if err143 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err144 := (strconv.Atoi(flag.Arg(2)))
		if err144 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.CarrierCode(tmp2)
		value2 := appserver.CarrierCode(argvalue2)
		arg145 := flag.Arg(4)
		mbTrans146 := thrift.NewTMemoryBufferLen(len(arg145))
		defer mbTrans146.Close()
		_, err147 := mbTrans146.WriteString(arg145)
		if err147 != nil {
			Usage()
			return
		}
		factory148 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt149 := factory148.GetProtocol(mbTrans146)
		containerStruct3 := appserver.NewListAppByTopicArgs()
		err150 := containerStruct3.ReadField4(jsProt149)
		if err150 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.AppwallProt
		value3 := appserver.AppWallProt(argvalue3)
		fmt.Print(client.ListAppByTopic(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getBanners":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetBanners requires 3 args")
			flag.Usage()
		}
		arg151 := flag.Arg(1)
		mbTrans152 := thrift.NewTMemoryBufferLen(len(arg151))
		defer mbTrans152.Close()
		_, err153 := mbTrans152.WriteString(arg151)
		if err153 != nil {
			Usage()
			return
		}
		factory154 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt155 := factory154.GetProtocol(mbTrans152)
		argvalue0 := appserver.NewRequestHeader()
		err156 := argvalue0.Read(jsProt155)
		if err156 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		arg158 := flag.Arg(3)
		mbTrans159 := thrift.NewTMemoryBufferLen(len(arg158))
		defer mbTrans159.Close()
		_, err160 := mbTrans159.WriteString(arg158)
		if err160 != nil {
			Usage()
			return
		}
		factory161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt162 := factory161.GetProtocol(mbTrans159)
		argvalue2 := appserver.NewListCriteria()
		err163 := argvalue2.Read(jsProt162)
		if err163 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetBanners(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getRecommendation":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetRecommendation requires 3 args")
			flag.Usage()
		}
		arg164 := flag.Arg(1)
		mbTrans165 := thrift.NewTMemoryBufferLen(len(arg164))
		defer mbTrans165.Close()
		_, err166 := mbTrans165.WriteString(arg164)
		if err166 != nil {
			Usage()
			return
		}
		factory167 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt168 := factory167.GetProtocol(mbTrans165)
		argvalue0 := appserver.NewRequestHeader()
		err169 := argvalue0.Read(jsProt168)
		if err169 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appserver.RecommendType(tmp1)
		value1 := argvalue1
		arg170 := flag.Arg(3)
		mbTrans171 := thrift.NewTMemoryBufferLen(len(arg170))
		defer mbTrans171.Close()
		_, err172 := mbTrans171.WriteString(arg170)
		if err172 != nil {
			Usage()
			return
		}
		factory173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt174 := factory173.GetProtocol(mbTrans171)
		argvalue2 := appserver.NewListCriteria()
		err175 := argvalue2.Read(jsProt174)
		if err175 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.GetRecommendation(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchDuoyou":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchDuoyou requires 4 args")
			flag.Usage()
		}
		arg176 := flag.Arg(1)
		mbTrans177 := thrift.NewTMemoryBufferLen(len(arg176))
		defer mbTrans177.Close()
		_, err178 := mbTrans177.WriteString(arg176)
		if err178 != nil {
			Usage()
			return
		}
		factory179 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt180 := factory179.GetProtocol(mbTrans177)
		argvalue0 := appserver.NewRequestHeader()
		err181 := argvalue0.Read(jsProt180)
		if err181 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.SearchType(tmp2)
		value2 := argvalue2
		arg183 := flag.Arg(4)
		mbTrans184 := thrift.NewTMemoryBufferLen(len(arg183))
		defer mbTrans184.Close()
		_, err185 := mbTrans184.WriteString(arg183)
		if err185 != nil {
			Usage()
			return
		}
		factory186 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt187 := factory186.GetProtocol(mbTrans184)
		argvalue3 := appserver.NewListCriteria()
		err188 := argvalue3.Read(jsProt187)
		if err188 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchDuoyou(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "searchGame":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchGame requires 4 args")
			flag.Usage()
		}
		arg189 := flag.Arg(1)
		mbTrans190 := thrift.NewTMemoryBufferLen(len(arg189))
		defer mbTrans190.Close()
		_, err191 := mbTrans190.WriteString(arg189)
		if err191 != nil {
			Usage()
			return
		}
		factory192 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt193 := factory192.GetProtocol(mbTrans190)
		argvalue0 := appserver.NewRequestHeader()
		err194 := argvalue0.Read(jsProt193)
		if err194 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := appserver.SearchType(tmp2)
		value2 := argvalue2
		arg196 := flag.Arg(4)
		mbTrans197 := thrift.NewTMemoryBufferLen(len(arg196))
		defer mbTrans197.Close()
		_, err198 := mbTrans197.WriteString(arg196)
		if err198 != nil {
			Usage()
			return
		}
		factory199 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt200 := factory199.GetProtocol(mbTrans197)
		argvalue3 := appserver.NewListCriteria()
		err201 := argvalue3.Read(jsProt200)
		if err201 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.SearchGame(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listDuoyou":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListDuoyou requires 3 args")
			flag.Usage()
		}
		arg202 := flag.Arg(1)
		mbTrans203 := thrift.NewTMemoryBufferLen(len(arg202))
		defer mbTrans203.Close()
		_, err204 := mbTrans203.WriteString(arg202)
		if err204 != nil {
			Usage()
			return
		}
		factory205 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt206 := factory205.GetProtocol(mbTrans203)
		argvalue0 := appserver.NewRequestHeader()
		err207 := argvalue0.Read(jsProt206)
		if err207 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := appserver.Market(tmp1)
		value1 := appserver.Market(argvalue1)
		arg208 := flag.Arg(3)
		mbTrans209 := thrift.NewTMemoryBufferLen(len(arg208))
		defer mbTrans209.Close()
		_, err210 := mbTrans209.WriteString(arg208)
		if err210 != nil {
			Usage()
			return
		}
		factory211 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt212 := factory211.GetProtocol(mbTrans209)
		argvalue2 := appserver.NewListCriteria()
		err213 := argvalue2.Read(jsProt212)
		if err213 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.ListDuoyou(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listGift":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListGift requires 2 args")
			flag.Usage()
		}
		arg214 := flag.Arg(1)
		mbTrans215 := thrift.NewTMemoryBufferLen(len(arg214))
		defer mbTrans215.Close()
		_, err216 := mbTrans215.WriteString(arg214)
		if err216 != nil {
			Usage()
			return
		}
		factory217 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt218 := factory217.GetProtocol(mbTrans215)
		argvalue0 := appserver.NewRequestHeader()
		err219 := argvalue0.Read(jsProt218)
		if err219 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg220 := flag.Arg(2)
		mbTrans221 := thrift.NewTMemoryBufferLen(len(arg220))
		defer mbTrans221.Close()
		_, err222 := mbTrans221.WriteString(arg220)
		if err222 != nil {
			Usage()
			return
		}
		factory223 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt224 := factory223.GetProtocol(mbTrans221)
		argvalue1 := appserver.NewListGiftCriteria()
		err225 := argvalue1.Read(jsProt224)
		if err225 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ListGift(value0, value1))
		fmt.Print("\n")
		break
	case "listGame":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListGame requires 2 args")
			flag.Usage()
		}
		arg226 := flag.Arg(1)
		mbTrans227 := thrift.NewTMemoryBufferLen(len(arg226))
		defer mbTrans227.Close()
		_, err228 := mbTrans227.WriteString(arg226)
		if err228 != nil {
			Usage()
			return
		}
		factory229 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt230 := factory229.GetProtocol(mbTrans227)
		argvalue0 := appserver.NewRequestHeader()
		err231 := argvalue0.Read(jsProt230)
		if err231 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg232 := flag.Arg(2)
		mbTrans233 := thrift.NewTMemoryBufferLen(len(arg232))
		defer mbTrans233.Close()
		_, err234 := mbTrans233.WriteString(arg232)
		if err234 != nil {
			Usage()
			return
		}
		factory235 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt236 := factory235.GetProtocol(mbTrans233)
		argvalue1 := appserver.NewListCriteria()
		err237 := argvalue1.Read(jsProt236)
		if err237 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ListGame(value0, value1))
		fmt.Print("\n")
		break
	case "checkUpdate":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CheckUpdate requires 3 args")
			flag.Usage()
		}
		arg238 := flag.Arg(1)
		mbTrans239 := thrift.NewTMemoryBufferLen(len(arg238))
		defer mbTrans239.Close()
		_, err240 := mbTrans239.WriteString(arg238)
		if err240 != nil {
			Usage()
			return
		}
		factory241 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt242 := factory241.GetProtocol(mbTrans239)
		argvalue0 := appserver.NewRequestHeader()
		err243 := argvalue0.Read(jsProt242)
		if err243 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg244 := flag.Arg(2)
		mbTrans245 := thrift.NewTMemoryBufferLen(len(arg244))
		defer mbTrans245.Close()
		_, err246 := mbTrans245.WriteString(arg244)
		if err246 != nil {
			Usage()
			return
		}
		factory247 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt248 := factory247.GetProtocol(mbTrans245)
		containerStruct1 := appserver.NewCheckUpdateArgs()
		err249 := containerStruct1.ReadField2(jsProt248)
		if err249 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Packages
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.CheckUpdate(value0, value1, value2))
		fmt.Print("\n")
		break
	case "checkGameUpdate":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CheckGameUpdate requires 3 args")
			flag.Usage()
		}
		arg251 := flag.Arg(1)
		mbTrans252 := thrift.NewTMemoryBufferLen(len(arg251))
		defer mbTrans252.Close()
		_, err253 := mbTrans252.WriteString(arg251)
		if err253 != nil {
			Usage()
			return
		}
		factory254 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt255 := factory254.GetProtocol(mbTrans252)
		argvalue0 := appserver.NewRequestHeader()
		err256 := argvalue0.Read(jsProt255)
		if err256 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		arg257 := flag.Arg(2)
		mbTrans258 := thrift.NewTMemoryBufferLen(len(arg257))
		defer mbTrans258.Close()
		_, err259 := mbTrans258.WriteString(arg257)
		if err259 != nil {
			Usage()
			return
		}
		factory260 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt261 := factory260.GetProtocol(mbTrans258)
		containerStruct1 := appserver.NewCheckGameUpdateArgs()
		err262 := containerStruct1.ReadField2(jsProt261)
		if err262 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Packages
		value1 := argvalue1
		arg263 := flag.Arg(3)
		mbTrans264 := thrift.NewTMemoryBufferLen(len(arg263))
		defer mbTrans264.Close()
		_, err265 := mbTrans264.WriteString(arg263)
		if err265 != nil {
			Usage()
			return
		}
		factory266 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt267 := factory266.GetProtocol(mbTrans264)
		containerStruct2 := appserver.NewCheckGameUpdateArgs()
		err268 := containerStruct2.ReadField3(jsProt267)
		if err268 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AppwallProt
		value2 := appserver.AppWallProt(argvalue2)
		fmt.Print(client.CheckGameUpdate(value0, value1, value2))
		fmt.Print("\n")
		break
	case "supportApp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SupportApp requires 2 args")
			flag.Usage()
		}
		arg269 := flag.Arg(1)
		mbTrans270 := thrift.NewTMemoryBufferLen(len(arg269))
		defer mbTrans270.Close()
		_, err271 := mbTrans270.WriteString(arg269)
		if err271 != nil {
			Usage()
			return
		}
		factory272 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt273 := factory272.GetProtocol(mbTrans270)
		argvalue0 := appserver.NewRequestHeader()
		err274 := argvalue0.Read(jsProt273)
		if err274 != nil {
			Usage()
			return
		}
		value0 := appserver.RequestHeader(argvalue0)
		tmp1, err275 := (strconv.Atoi(flag.Arg(2)))
		if err275 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.SupportApp(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
