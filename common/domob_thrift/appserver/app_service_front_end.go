// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appserver

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appserver_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = appserver_types.GoUnusedProtection__

type AppServiceFrontEnd interface { //对于这些查询，找不到对象是返回null而不是抛出异常

	// 根据id获取AppMain，如果找不到，抛出异常
	//
	// Parameters:
	//  - Header
	//  - Appid
	GetAppMain(header *common.RequestHeader, appid int32) (r *appserver_types.AppMain, ae *AppException, err error)
	// 根据package_name获取AppMain，如果找不到，抛出异常
	//
	// Parameters:
	//  - Header
	//  - Pname
	GetAppByPname(header *common.RequestHeader, pname string) (r *appserver_types.AppMain, ae *AppException, err error)
	// 根据id获取app wall的游戏
	//
	// Parameters:
	//  - Header
	//  - Id
	//  - AppwallProt
	GetWallGame(header *common.RequestHeader, id int32, appwall_prot AppWallProt) (r *appserver_types.AppWallGame, ae *AppException, err error)
	// 返回系统所有分类的摘要信息
	//
	// Parameters:
	//  - Header
	GetCategorySummaries(header *common.RequestHeader) (r []*appserver_types.CategorySummary, ae *AppException, err error)
	//    * 获取所有的topic列表
	//    *
	// * @param bool with_disabled 是否包含已经禁用的topic
	//
	// Parameters:
	//  - Header
	//  - WithDisabled
	GetTopics(header *common.RequestHeader, with_disabled bool) (r []*appserver_types.Topic, ae *AppException, err error)
	// Parameters:
	//  - Header
	//  - TopicId
	GetTopic(header *common.RequestHeader, topic_id int32) (r *appserver_types.Topic, ae *AppException, err error)
	// * 列出某个专题的所有游戏
	//    * 专题的游戏数量不多，不支持分页
	//
	// Parameters:
	//  - Header
	//  - Topicid
	//  - Carrier
	//  - AppwallProt
	ListAppByTopic(header *common.RequestHeader, topicid int32, carrier common.CarrierCode, appwall_prot AppWallProt) (r []*appserver_types.Game, ae *AppException, err error)
	// 列出所有的banner
	//
	// Parameters:
	//  - Header
	//  - WithDisabled
	//  - Criteria
	GetBanners(header *common.RequestHeader, with_disabled bool, criteria *ListCriteria) (r []*appserver_types.Banner, ae *AppException, err error)
	// 获取推荐游戏
	// 这些游戏是后台配置的，可能包含推广墙或者多游的游戏
	//
	// Parameters:
	//  - Header
	//  - RecomType
	//  - Criteria
	GetRecommendation(header *common.RequestHeader, recom_type RecommendType, criteria *ListCriteria) (r []*appserver_types.Game, ae *AppException, err error)
	// 根据app名称搜索
	//
	// 这个是旧的接口，目前给安卓优化大师用
	//
	// Parameters:
	//  - Header
	//  - Query: 要搜索的app名字
	//  - Stype: query name or package
	//  - Criteria: 检索条件
	SearchDuoyou(header *common.RequestHeader, query string, stype SearchType, criteria *ListCriteria) (r *appserver_types.MainList, ae *AppException, err error)
	// 搜索多游和推广墙的游戏
	//
	// Parameters:
	//  - Header
	//  - Query: 要搜索的app名字
	//  - Stype: query name or package
	//  - Criteria: 检索条件
	SearchGame(header *common.RequestHeader, query string, stype SearchType, criteria *ListCriteria) (r *appserver_types.GameList, ae *AppException, err error)
	// 获取App列表
	// 这个是旧的接口，目前给安卓优化大师用
	//
	// Parameters:
	//  - Header
	//  - Market: 来源市场
	//  - Criteria: 检索条件
	ListDuoyou(header *common.RequestHeader, market Market, criteria *ListCriteria) (r *appserver_types.MainList, ae *AppException, err error)
	// 这个接口用来获取礼包列表和我的礼包
	//
	// Parameters:
	//  - Header
	//  - Criteria
	ListGift(header *common.RequestHeader, criteria *ListGiftCriteria) (r *appserver_types.GiftResult, ae *AppException, err error)
	// 获取App列表
	//
	// Parameters:
	//  - Header
	//  - Criteria: 检索条件
	ListGame(header *common.RequestHeader, criteria *ListCriteria) (r *appserver_types.GameList, ae *AppException, err error)
	// * 检查更新. TODO: 删除这个函数，不用了，用下面的checkGameUpdate实现
	//    *
	//    * 客户端sdk获取手机上安装的所有软件包，发送到服务器端，服务器端将在
	//    * 数据库中的记录返回，客户端根据version_code判断是否需要升级。
	// *
	// * @return 在库中的app列表
	//
	// Parameters:
	//  - Header
	//  - Packages: 包名：版本的map，注意版本是apk中的version_code
	//  - WithDesc: 是否返回详细信息
	// 如果为false，则不返回描述信息和截图的url列表，节省带宽
	CheckUpdate(header *common.RequestHeader, packages map[string]int32, withDesc bool) (r []*appserver_types.AppMain, ae *AppException, err error)
	// * 检查更新
	//    *
	//    * 客户端sdk获取手机上安装的所有软件包，发送到服务器端，服务器端将在
	//    * 数据库中的记录和推广墙的数据返回，客户端根据version_code判断是否
	//    * 需要升级。
	//    *
	// * @return 在库中的app列表
	//
	// Parameters:
	//  - Header
	//  - Packages: 包名：版本的map，注意版本是apk中的version_code
	//  - AppwallProt: 用于请求推广墙的协议内容
	CheckGameUpdate(header *common.RequestHeader, packages map[string]int32, appwall_prot AppWallProt) (r []*appserver_types.Game, ae *AppException, err error)
	// 顶一个app，将其rate增加1
	//
	// Parameters:
	//  - Header
	//  - Appid
	SupportApp(header *common.RequestHeader, appid int32) (ae *AppException, err error)
}

//对于这些查询，找不到对象是返回null而不是抛出异常
type AppServiceFrontEndClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewAppServiceFrontEndClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *AppServiceFrontEndClient {
	return &AppServiceFrontEndClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewAppServiceFrontEndClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *AppServiceFrontEndClient {
	return &AppServiceFrontEndClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 根据id获取AppMain，如果找不到，抛出异常
//
// Parameters:
//  - Header
//  - Appid
func (p *AppServiceFrontEndClient) GetAppMain(header *common.RequestHeader, appid int32) (r *appserver_types.AppMain, ae *AppException, err error) {
	if err = p.sendGetAppMain(header, appid); err != nil {
		return
	}
	return p.recvGetAppMain()
}

func (p *AppServiceFrontEndClient) sendGetAppMain(header *common.RequestHeader, appid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppMain", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetAppMainArgs()
	args4.Header = header
	args4.Appid = appid
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetAppMain() (value *appserver_types.AppMain, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetAppMainResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Ae != nil {
		ae = result5.Ae
	}
	return
}

// 根据package_name获取AppMain，如果找不到，抛出异常
//
// Parameters:
//  - Header
//  - Pname
func (p *AppServiceFrontEndClient) GetAppByPname(header *common.RequestHeader, pname string) (r *appserver_types.AppMain, ae *AppException, err error) {
	if err = p.sendGetAppByPname(header, pname); err != nil {
		return
	}
	return p.recvGetAppByPname()
}

func (p *AppServiceFrontEndClient) sendGetAppByPname(header *common.RequestHeader, pname string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppByPname", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetAppByPnameArgs()
	args8.Header = header
	args8.Pname = pname
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetAppByPname() (value *appserver_types.AppMain, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetAppByPnameResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Ae != nil {
		ae = result9.Ae
	}
	return
}

// 根据id获取app wall的游戏
//
// Parameters:
//  - Header
//  - Id
//  - AppwallProt
func (p *AppServiceFrontEndClient) GetWallGame(header *common.RequestHeader, id int32, appwall_prot AppWallProt) (r *appserver_types.AppWallGame, ae *AppException, err error) {
	if err = p.sendGetWallGame(header, id, appwall_prot); err != nil {
		return
	}
	return p.recvGetWallGame()
}

func (p *AppServiceFrontEndClient) sendGetWallGame(header *common.RequestHeader, id int32, appwall_prot AppWallProt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getWallGame", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetWallGameArgs()
	args12.Header = header
	args12.Id = id
	args12.AppwallProt = appwall_prot
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetWallGame() (value *appserver_types.AppWallGame, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetWallGameResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Ae != nil {
		ae = result13.Ae
	}
	return
}

// 返回系统所有分类的摘要信息
//
// Parameters:
//  - Header
func (p *AppServiceFrontEndClient) GetCategorySummaries(header *common.RequestHeader) (r []*appserver_types.CategorySummary, ae *AppException, err error) {
	if err = p.sendGetCategorySummaries(header); err != nil {
		return
	}
	return p.recvGetCategorySummaries()
}

func (p *AppServiceFrontEndClient) sendGetCategorySummaries(header *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCategorySummaries", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetCategorySummariesArgs()
	args16.Header = header
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetCategorySummaries() (value []*appserver_types.CategorySummary, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetCategorySummariesResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Ae != nil {
		ae = result17.Ae
	}
	return
}

//    * 获取所有的topic列表
//    *
// * @param bool with_disabled 是否包含已经禁用的topic
//
// Parameters:
//  - Header
//  - WithDisabled
func (p *AppServiceFrontEndClient) GetTopics(header *common.RequestHeader, with_disabled bool) (r []*appserver_types.Topic, ae *AppException, err error) {
	if err = p.sendGetTopics(header, with_disabled); err != nil {
		return
	}
	return p.recvGetTopics()
}

func (p *AppServiceFrontEndClient) sendGetTopics(header *common.RequestHeader, with_disabled bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTopics", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetTopicsArgs()
	args20.Header = header
	args20.WithDisabled = with_disabled
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetTopics() (value []*appserver_types.Topic, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetTopicsResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Ae != nil {
		ae = result21.Ae
	}
	return
}

// Parameters:
//  - Header
//  - TopicId
func (p *AppServiceFrontEndClient) GetTopic(header *common.RequestHeader, topic_id int32) (r *appserver_types.Topic, ae *AppException, err error) {
	if err = p.sendGetTopic(header, topic_id); err != nil {
		return
	}
	return p.recvGetTopic()
}

func (p *AppServiceFrontEndClient) sendGetTopic(header *common.RequestHeader, topic_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTopic", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewGetTopicArgs()
	args24.Header = header
	args24.TopicId = topic_id
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetTopic() (value *appserver_types.Topic, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewGetTopicResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.Ae != nil {
		ae = result25.Ae
	}
	return
}

// * 列出某个专题的所有游戏
//    * 专题的游戏数量不多，不支持分页
//
// Parameters:
//  - Header
//  - Topicid
//  - Carrier
//  - AppwallProt
func (p *AppServiceFrontEndClient) ListAppByTopic(header *common.RequestHeader, topicid int32, carrier common.CarrierCode, appwall_prot AppWallProt) (r []*appserver_types.Game, ae *AppException, err error) {
	if err = p.sendListAppByTopic(header, topicid, carrier, appwall_prot); err != nil {
		return
	}
	return p.recvListAppByTopic()
}

func (p *AppServiceFrontEndClient) sendListAppByTopic(header *common.RequestHeader, topicid int32, carrier common.CarrierCode, appwall_prot AppWallProt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listAppByTopic", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewListAppByTopicArgs()
	args28.Header = header
	args28.Topicid = topicid
	args28.Carrier = carrier
	args28.AppwallProt = appwall_prot
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvListAppByTopic() (value []*appserver_types.Game, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewListAppByTopicResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Ae != nil {
		ae = result29.Ae
	}
	return
}

// 列出所有的banner
//
// Parameters:
//  - Header
//  - WithDisabled
//  - Criteria
func (p *AppServiceFrontEndClient) GetBanners(header *common.RequestHeader, with_disabled bool, criteria *ListCriteria) (r []*appserver_types.Banner, ae *AppException, err error) {
	if err = p.sendGetBanners(header, with_disabled, criteria); err != nil {
		return
	}
	return p.recvGetBanners()
}

func (p *AppServiceFrontEndClient) sendGetBanners(header *common.RequestHeader, with_disabled bool, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getBanners", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetBannersArgs()
	args32.Header = header
	args32.WithDisabled = with_disabled
	args32.Criteria = criteria
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetBanners() (value []*appserver_types.Banner, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetBannersResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Ae != nil {
		ae = result33.Ae
	}
	return
}

// 获取推荐游戏
// 这些游戏是后台配置的，可能包含推广墙或者多游的游戏
//
// Parameters:
//  - Header
//  - RecomType
//  - Criteria
func (p *AppServiceFrontEndClient) GetRecommendation(header *common.RequestHeader, recom_type RecommendType, criteria *ListCriteria) (r []*appserver_types.Game, ae *AppException, err error) {
	if err = p.sendGetRecommendation(header, recom_type, criteria); err != nil {
		return
	}
	return p.recvGetRecommendation()
}

func (p *AppServiceFrontEndClient) sendGetRecommendation(header *common.RequestHeader, recom_type RecommendType, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getRecommendation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewGetRecommendationArgs()
	args36.Header = header
	args36.RecomType = recom_type
	args36.Criteria = criteria
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvGetRecommendation() (value []*appserver_types.Game, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewGetRecommendationResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Ae != nil {
		ae = result37.Ae
	}
	return
}

// 根据app名称搜索
//
// 这个是旧的接口，目前给安卓优化大师用
//
// Parameters:
//  - Header
//  - Query: 要搜索的app名字
//  - Stype: query name or package
//  - Criteria: 检索条件
func (p *AppServiceFrontEndClient) SearchDuoyou(header *common.RequestHeader, query string, stype SearchType, criteria *ListCriteria) (r *appserver_types.MainList, ae *AppException, err error) {
	if err = p.sendSearchDuoyou(header, query, stype, criteria); err != nil {
		return
	}
	return p.recvSearchDuoyou()
}

func (p *AppServiceFrontEndClient) sendSearchDuoyou(header *common.RequestHeader, query string, stype SearchType, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchDuoyou", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewSearchDuoyouArgs()
	args40.Header = header
	args40.Query = query
	args40.Stype = stype
	args40.Criteria = criteria
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvSearchDuoyou() (value *appserver_types.MainList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewSearchDuoyouResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Ae != nil {
		ae = result41.Ae
	}
	return
}

// 搜索多游和推广墙的游戏
//
// Parameters:
//  - Header
//  - Query: 要搜索的app名字
//  - Stype: query name or package
//  - Criteria: 检索条件
func (p *AppServiceFrontEndClient) SearchGame(header *common.RequestHeader, query string, stype SearchType, criteria *ListCriteria) (r *appserver_types.GameList, ae *AppException, err error) {
	if err = p.sendSearchGame(header, query, stype, criteria); err != nil {
		return
	}
	return p.recvSearchGame()
}

func (p *AppServiceFrontEndClient) sendSearchGame(header *common.RequestHeader, query string, stype SearchType, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchGame", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewSearchGameArgs()
	args44.Header = header
	args44.Query = query
	args44.Stype = stype
	args44.Criteria = criteria
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvSearchGame() (value *appserver_types.GameList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewSearchGameResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.Ae != nil {
		ae = result45.Ae
	}
	return
}

// 获取App列表
// 这个是旧的接口，目前给安卓优化大师用
//
// Parameters:
//  - Header
//  - Market: 来源市场
//  - Criteria: 检索条件
func (p *AppServiceFrontEndClient) ListDuoyou(header *common.RequestHeader, market Market, criteria *ListCriteria) (r *appserver_types.MainList, ae *AppException, err error) {
	if err = p.sendListDuoyou(header, market, criteria); err != nil {
		return
	}
	return p.recvListDuoyou()
}

func (p *AppServiceFrontEndClient) sendListDuoyou(header *common.RequestHeader, market Market, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listDuoyou", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewListDuoyouArgs()
	args48.Header = header
	args48.Market = market
	args48.Criteria = criteria
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvListDuoyou() (value *appserver_types.MainList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewListDuoyouResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result49.Success
	if result49.Ae != nil {
		ae = result49.Ae
	}
	return
}

// 这个接口用来获取礼包列表和我的礼包
//
// Parameters:
//  - Header
//  - Criteria
func (p *AppServiceFrontEndClient) ListGift(header *common.RequestHeader, criteria *ListGiftCriteria) (r *appserver_types.GiftResult, ae *AppException, err error) {
	if err = p.sendListGift(header, criteria); err != nil {
		return
	}
	return p.recvListGift()
}

func (p *AppServiceFrontEndClient) sendListGift(header *common.RequestHeader, criteria *ListGiftCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listGift", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewListGiftArgs()
	args52.Header = header
	args52.Criteria = criteria
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvListGift() (value *appserver_types.GiftResult, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewListGiftResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.Ae != nil {
		ae = result53.Ae
	}
	return
}

// 获取App列表
//
// Parameters:
//  - Header
//  - Criteria: 检索条件
func (p *AppServiceFrontEndClient) ListGame(header *common.RequestHeader, criteria *ListCriteria) (r *appserver_types.GameList, ae *AppException, err error) {
	if err = p.sendListGame(header, criteria); err != nil {
		return
	}
	return p.recvListGame()
}

func (p *AppServiceFrontEndClient) sendListGame(header *common.RequestHeader, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listGame", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewListGameArgs()
	args56.Header = header
	args56.Criteria = criteria
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvListGame() (value *appserver_types.GameList, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewListGameResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.Ae != nil {
		ae = result57.Ae
	}
	return
}

// * 检查更新. TODO: 删除这个函数，不用了，用下面的checkGameUpdate实现
//    *
//    * 客户端sdk获取手机上安装的所有软件包，发送到服务器端，服务器端将在
//    * 数据库中的记录返回，客户端根据version_code判断是否需要升级。
// *
// * @return 在库中的app列表
//
// Parameters:
//  - Header
//  - Packages: 包名：版本的map，注意版本是apk中的version_code
//  - WithDesc: 是否返回详细信息
// 如果为false，则不返回描述信息和截图的url列表，节省带宽
func (p *AppServiceFrontEndClient) CheckUpdate(header *common.RequestHeader, packages map[string]int32, withDesc bool) (r []*appserver_types.AppMain, ae *AppException, err error) {
	if err = p.sendCheckUpdate(header, packages, withDesc); err != nil {
		return
	}
	return p.recvCheckUpdate()
}

func (p *AppServiceFrontEndClient) sendCheckUpdate(header *common.RequestHeader, packages map[string]int32, withDesc bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkUpdate", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewCheckUpdateArgs()
	args60.Header = header
	args60.Packages = packages
	args60.WithDesc = withDesc
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvCheckUpdate() (value []*appserver_types.AppMain, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewCheckUpdateResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result61.Success
	if result61.Ae != nil {
		ae = result61.Ae
	}
	return
}

// * 检查更新
//    *
//    * 客户端sdk获取手机上安装的所有软件包，发送到服务器端，服务器端将在
//    * 数据库中的记录和推广墙的数据返回，客户端根据version_code判断是否
//    * 需要升级。
//    *
// * @return 在库中的app列表
//
// Parameters:
//  - Header
//  - Packages: 包名：版本的map，注意版本是apk中的version_code
//  - AppwallProt: 用于请求推广墙的协议内容
func (p *AppServiceFrontEndClient) CheckGameUpdate(header *common.RequestHeader, packages map[string]int32, appwall_prot AppWallProt) (r []*appserver_types.Game, ae *AppException, err error) {
	if err = p.sendCheckGameUpdate(header, packages, appwall_prot); err != nil {
		return
	}
	return p.recvCheckGameUpdate()
}

func (p *AppServiceFrontEndClient) sendCheckGameUpdate(header *common.RequestHeader, packages map[string]int32, appwall_prot AppWallProt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("checkGameUpdate", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewCheckGameUpdateArgs()
	args64.Header = header
	args64.Packages = packages
	args64.AppwallProt = appwall_prot
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvCheckGameUpdate() (value []*appserver_types.Game, ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewCheckGameUpdateResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.Ae != nil {
		ae = result65.Ae
	}
	return
}

// 顶一个app，将其rate增加1
//
// Parameters:
//  - Header
//  - Appid
func (p *AppServiceFrontEndClient) SupportApp(header *common.RequestHeader, appid int32) (ae *AppException, err error) {
	if err = p.sendSupportApp(header, appid); err != nil {
		return
	}
	return p.recvSupportApp()
}

func (p *AppServiceFrontEndClient) sendSupportApp(header *common.RequestHeader, appid int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("supportApp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args68 := NewSupportAppArgs()
	args68.Header = header
	args68.Appid = appid
	if err = args68.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *AppServiceFrontEndClient) recvSupportApp() (ae *AppException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error70 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error71 error
		error71, err = error70.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error71
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result69 := NewSupportAppResult()
	if err = result69.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result69.Ae != nil {
		ae = result69.Ae
	}
	return
}

type AppServiceFrontEndProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      AppServiceFrontEnd
}

func (p *AppServiceFrontEndProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *AppServiceFrontEndProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *AppServiceFrontEndProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewAppServiceFrontEndProcessor(handler AppServiceFrontEnd) *AppServiceFrontEndProcessor {

	self72 := &AppServiceFrontEndProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self72.processorMap["getAppMain"] = &appServiceFrontEndProcessorGetAppMain{handler: handler}
	self72.processorMap["getAppByPname"] = &appServiceFrontEndProcessorGetAppByPname{handler: handler}
	self72.processorMap["getWallGame"] = &appServiceFrontEndProcessorGetWallGame{handler: handler}
	self72.processorMap["getCategorySummaries"] = &appServiceFrontEndProcessorGetCategorySummaries{handler: handler}
	self72.processorMap["getTopics"] = &appServiceFrontEndProcessorGetTopics{handler: handler}
	self72.processorMap["getTopic"] = &appServiceFrontEndProcessorGetTopic{handler: handler}
	self72.processorMap["listAppByTopic"] = &appServiceFrontEndProcessorListAppByTopic{handler: handler}
	self72.processorMap["getBanners"] = &appServiceFrontEndProcessorGetBanners{handler: handler}
	self72.processorMap["getRecommendation"] = &appServiceFrontEndProcessorGetRecommendation{handler: handler}
	self72.processorMap["searchDuoyou"] = &appServiceFrontEndProcessorSearchDuoyou{handler: handler}
	self72.processorMap["searchGame"] = &appServiceFrontEndProcessorSearchGame{handler: handler}
	self72.processorMap["listDuoyou"] = &appServiceFrontEndProcessorListDuoyou{handler: handler}
	self72.processorMap["listGift"] = &appServiceFrontEndProcessorListGift{handler: handler}
	self72.processorMap["listGame"] = &appServiceFrontEndProcessorListGame{handler: handler}
	self72.processorMap["checkUpdate"] = &appServiceFrontEndProcessorCheckUpdate{handler: handler}
	self72.processorMap["checkGameUpdate"] = &appServiceFrontEndProcessorCheckGameUpdate{handler: handler}
	self72.processorMap["supportApp"] = &appServiceFrontEndProcessorSupportApp{handler: handler}
	return self72
}

func (p *AppServiceFrontEndProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x73 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x73.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x73

}

type appServiceFrontEndProcessorGetAppMain struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetAppMain) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppMainArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppMainResult()
	if result.Success, result.Ae, err = p.handler.GetAppMain(args.Header, args.Appid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppMain: "+err.Error())
		oprot.WriteMessageBegin("getAppMain", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppMain", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorGetAppByPname struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetAppByPname) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppByPnameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppByPname", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppByPnameResult()
	if result.Success, result.Ae, err = p.handler.GetAppByPname(args.Header, args.Pname); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppByPname: "+err.Error())
		oprot.WriteMessageBegin("getAppByPname", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppByPname", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorGetWallGame struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetWallGame) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetWallGameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getWallGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetWallGameResult()
	if result.Success, result.Ae, err = p.handler.GetWallGame(args.Header, args.Id, args.AppwallProt); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getWallGame: "+err.Error())
		oprot.WriteMessageBegin("getWallGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getWallGame", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorGetCategorySummaries struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetCategorySummaries) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCategorySummariesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCategorySummaries", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCategorySummariesResult()
	if result.Success, result.Ae, err = p.handler.GetCategorySummaries(args.Header); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCategorySummaries: "+err.Error())
		oprot.WriteMessageBegin("getCategorySummaries", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCategorySummaries", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorGetTopics struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetTopics) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTopicsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTopics", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTopicsResult()
	if result.Success, result.Ae, err = p.handler.GetTopics(args.Header, args.WithDisabled); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTopics: "+err.Error())
		oprot.WriteMessageBegin("getTopics", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTopics", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorGetTopic struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetTopic) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTopicArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTopicResult()
	if result.Success, result.Ae, err = p.handler.GetTopic(args.Header, args.TopicId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTopic: "+err.Error())
		oprot.WriteMessageBegin("getTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTopic", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorListAppByTopic struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorListAppByTopic) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListAppByTopicArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listAppByTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListAppByTopicResult()
	if result.Success, result.Ae, err = p.handler.ListAppByTopic(args.Header, args.Topicid, args.Carrier, args.AppwallProt); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listAppByTopic: "+err.Error())
		oprot.WriteMessageBegin("listAppByTopic", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listAppByTopic", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorGetBanners struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetBanners) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetBannersArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getBanners", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetBannersResult()
	if result.Success, result.Ae, err = p.handler.GetBanners(args.Header, args.WithDisabled, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getBanners: "+err.Error())
		oprot.WriteMessageBegin("getBanners", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getBanners", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorGetRecommendation struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorGetRecommendation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetRecommendationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getRecommendation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetRecommendationResult()
	if result.Success, result.Ae, err = p.handler.GetRecommendation(args.Header, args.RecomType, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getRecommendation: "+err.Error())
		oprot.WriteMessageBegin("getRecommendation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getRecommendation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorSearchDuoyou struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorSearchDuoyou) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchDuoyouArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchDuoyou", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchDuoyouResult()
	if result.Success, result.Ae, err = p.handler.SearchDuoyou(args.Header, args.Query, args.Stype, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchDuoyou: "+err.Error())
		oprot.WriteMessageBegin("searchDuoyou", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchDuoyou", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorSearchGame struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorSearchGame) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchGameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchGameResult()
	if result.Success, result.Ae, err = p.handler.SearchGame(args.Header, args.Query, args.Stype, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchGame: "+err.Error())
		oprot.WriteMessageBegin("searchGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchGame", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorListDuoyou struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorListDuoyou) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListDuoyouArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listDuoyou", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListDuoyouResult()
	if result.Success, result.Ae, err = p.handler.ListDuoyou(args.Header, args.Market, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listDuoyou: "+err.Error())
		oprot.WriteMessageBegin("listDuoyou", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listDuoyou", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorListGift struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorListGift) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListGiftArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listGift", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListGiftResult()
	if result.Success, result.Ae, err = p.handler.ListGift(args.Header, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listGift: "+err.Error())
		oprot.WriteMessageBegin("listGift", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listGift", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorListGame struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorListGame) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListGameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListGameResult()
	if result.Success, result.Ae, err = p.handler.ListGame(args.Header, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listGame: "+err.Error())
		oprot.WriteMessageBegin("listGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listGame", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorCheckUpdate struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorCheckUpdate) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckUpdateArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkUpdate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckUpdateResult()
	if result.Success, result.Ae, err = p.handler.CheckUpdate(args.Header, args.Packages, args.WithDesc); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkUpdate: "+err.Error())
		oprot.WriteMessageBegin("checkUpdate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkUpdate", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorCheckGameUpdate struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorCheckGameUpdate) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckGameUpdateArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("checkGameUpdate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckGameUpdateResult()
	if result.Success, result.Ae, err = p.handler.CheckGameUpdate(args.Header, args.Packages, args.AppwallProt); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing checkGameUpdate: "+err.Error())
		oprot.WriteMessageBegin("checkGameUpdate", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("checkGameUpdate", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type appServiceFrontEndProcessorSupportApp struct {
	handler AppServiceFrontEnd
}

func (p *appServiceFrontEndProcessorSupportApp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSupportAppArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("supportApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSupportAppResult()
	if result.Ae, err = p.handler.SupportApp(args.Header, args.Appid); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing supportApp: "+err.Error())
		oprot.WriteMessageBegin("supportApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("supportApp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetAppMainArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Appid  int32                 `thrift:"appid,2" json:"appid"`
}

func NewGetAppMainArgs() *GetAppMainArgs {
	return &GetAppMainArgs{}
}

func (p *GetAppMainArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppMainArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppMainArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *GetAppMainArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppMain_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppMainArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMainArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *GetAppMainArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppMainArgs(%+v)", *p)
}

type GetAppMainResult struct {
	Success *appserver_types.AppMain `thrift:"success,0" json:"success"`
	Ae      *AppException            `thrift:"ae,1" json:"ae"`
}

func NewGetAppMainResult() *GetAppMainResult {
	return &GetAppMainResult{}
}

func (p *GetAppMainResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppMainResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewAppMain()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppMainResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppMainResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppMain_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppMainResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMainResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppMainResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppMainResult(%+v)", *p)
}

type GetAppByPnameArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Pname  string                `thrift:"pname,2" json:"pname"`
}

func NewGetAppByPnameArgs() *GetAppByPnameArgs {
	return &GetAppByPnameArgs{}
}

func (p *GetAppByPnameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppByPnameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppByPnameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pname = v
	}
	return nil
}

func (p *GetAppByPnameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppByPname_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppByPnameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByPnameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pname", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pname: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pname)); err != nil {
		return fmt.Errorf("%T.pname (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pname: %s", p, err)
	}
	return err
}

func (p *GetAppByPnameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppByPnameArgs(%+v)", *p)
}

type GetAppByPnameResult struct {
	Success *appserver_types.AppMain `thrift:"success,0" json:"success"`
	Ae      *AppException            `thrift:"ae,1" json:"ae"`
}

func NewGetAppByPnameResult() *GetAppByPnameResult {
	return &GetAppByPnameResult{}
}

func (p *GetAppByPnameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppByPnameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewAppMain()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppByPnameResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetAppByPnameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppByPname_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppByPnameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByPnameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByPnameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppByPnameResult(%+v)", *p)
}

type GetWallGameArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Id          int32                 `thrift:"id,2" json:"id"`
	AppwallProt AppWallProt           `thrift:"appwall_prot,3" json:"appwall_prot"`
}

func NewGetWallGameArgs() *GetWallGameArgs {
	return &GetWallGameArgs{}
}

func (p *GetWallGameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetWallGameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *GetWallGameArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key74 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key74 = v
		}
		var _val75 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val75 = v
		}
		p.AppwallProt[_key74] = _val75
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetWallGameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getWallGame_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *GetWallGameArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetWallGameArgs(%+v)", *p)
}

type GetWallGameResult struct {
	Success *appserver_types.AppWallGame `thrift:"success,0" json:"success"`
	Ae      *AppException                `thrift:"ae,1" json:"ae"`
}

func NewGetWallGameResult() *GetWallGameResult {
	return &GetWallGameResult{}
}

func (p *GetWallGameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewAppWallGame()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetWallGameResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetWallGameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getWallGame_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetWallGameResult(%+v)", *p)
}

type GetCategorySummariesArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
}

func NewGetCategorySummariesArgs() *GetCategorySummariesArgs {
	return &GetCategorySummariesArgs{}
}

func (p *GetCategorySummariesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCategorySummariesArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetCategorySummariesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCategorySummaries_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCategorySummariesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetCategorySummariesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCategorySummariesArgs(%+v)", *p)
}

type GetCategorySummariesResult struct {
	Success []*appserver_types.CategorySummary `thrift:"success,0" json:"success"`
	Ae      *AppException                      `thrift:"ae,1" json:"ae"`
}

func NewGetCategorySummariesResult() *GetCategorySummariesResult {
	return &GetCategorySummariesResult{}
}

func (p *GetCategorySummariesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCategorySummariesResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.CategorySummary, 0, size)
	for i := 0; i < size; i++ {
		_elem76 := appserver_types.NewCategorySummary()
		if err := _elem76.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem76)
		}
		p.Success = append(p.Success, _elem76)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetCategorySummariesResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetCategorySummariesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCategorySummaries_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCategorySummariesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCategorySummariesResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetCategorySummariesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCategorySummariesResult(%+v)", *p)
}

type GetTopicsArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	WithDisabled bool                  `thrift:"with_disabled,2" json:"with_disabled"`
}

func NewGetTopicsArgs() *GetTopicsArgs {
	return &GetTopicsArgs{}
}

func (p *GetTopicsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTopicsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.WithDisabled = v
	}
	return nil
}

func (p *GetTopicsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopics_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_disabled", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:with_disabled: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithDisabled)); err != nil {
		return fmt.Errorf("%T.with_disabled (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:with_disabled: %s", p, err)
	}
	return err
}

func (p *GetTopicsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicsArgs(%+v)", *p)
}

type GetTopicsResult struct {
	Success []*appserver_types.Topic `thrift:"success,0" json:"success"`
	Ae      *AppException            `thrift:"ae,1" json:"ae"`
}

func NewGetTopicsResult() *GetTopicsResult {
	return &GetTopicsResult{}
}

func (p *GetTopicsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicsResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.Topic, 0, size)
	for i := 0; i < size; i++ {
		_elem77 := appserver_types.NewTopic()
		if err := _elem77.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem77)
		}
		p.Success = append(p.Success, _elem77)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTopicsResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetTopicsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopics_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicsResult(%+v)", *p)
}

type GetTopicArgs struct {
	Header  *common.RequestHeader `thrift:"header,1" json:"header"`
	TopicId int32                 `thrift:"topic_id,2" json:"topic_id"`
}

func NewGetTopicArgs() *GetTopicArgs {
	return &GetTopicArgs{}
}

func (p *GetTopicArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTopicArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TopicId = v
	}
	return nil
}

func (p *GetTopicArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopic_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topic_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:topic_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TopicId)); err != nil {
		return fmt.Errorf("%T.topic_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:topic_id: %s", p, err)
	}
	return err
}

func (p *GetTopicArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicArgs(%+v)", *p)
}

type GetTopicResult struct {
	Success *appserver_types.Topic `thrift:"success,0" json:"success"`
	Ae      *AppException          `thrift:"ae,1" json:"ae"`
}

func NewGetTopicResult() *GetTopicResult {
	return &GetTopicResult{}
}

func (p *GetTopicResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewTopic()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetTopicResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetTopicResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopic_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicResult(%+v)", *p)
}

type ListAppByTopicArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Topicid     int32                 `thrift:"topicid,2" json:"topicid"`
	Carrier     common.CarrierCode    `thrift:"carrier,3" json:"carrier"`
	AppwallProt AppWallProt           `thrift:"appwall_prot,4" json:"appwall_prot"`
}

func NewListAppByTopicArgs() *ListAppByTopicArgs {
	return &ListAppByTopicArgs{
		Carrier: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListAppByTopicArgs) IsSetCarrier() bool {
	return int64(p.Carrier) != math.MinInt32-1
}

func (p *ListAppByTopicArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppByTopicArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListAppByTopicArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Topicid = v
	}
	return nil
}

func (p *ListAppByTopicArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Carrier = common.CarrierCode(v)
	}
	return nil
}

func (p *ListAppByTopicArgs) readField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key78 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key78 = v
		}
		var _val79 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val79 = v
		}
		p.AppwallProt[_key78] = _val79
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ListAppByTopicArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAppByTopic_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppByTopicArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListAppByTopicArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topicid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:topicid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Topicid)); err != nil {
		return fmt.Errorf("%T.topicid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:topicid: %s", p, err)
	}
	return err
}

func (p *ListAppByTopicArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:carrier: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:carrier: %s", p, err)
	}
	return err
}

func (p *ListAppByTopicArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *ListAppByTopicArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppByTopicArgs(%+v)", *p)
}

type ListAppByTopicResult struct {
	Success []*appserver_types.Game `thrift:"success,0" json:"success"`
	Ae      *AppException           `thrift:"ae,1" json:"ae"`
}

func NewListAppByTopicResult() *ListAppByTopicResult {
	return &ListAppByTopicResult{}
}

func (p *ListAppByTopicResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppByTopicResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.Game, 0, size)
	for i := 0; i < size; i++ {
		_elem80 := appserver_types.NewGame()
		if err := _elem80.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem80)
		}
		p.Success = append(p.Success, _elem80)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListAppByTopicResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListAppByTopicResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listAppByTopic_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppByTopicResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListAppByTopicResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListAppByTopicResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppByTopicResult(%+v)", *p)
}

type GetBannersArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	WithDisabled bool                  `thrift:"with_disabled,2" json:"with_disabled"`
	Criteria     *ListCriteria         `thrift:"criteria,3" json:"criteria"`
}

func NewGetBannersArgs() *GetBannersArgs {
	return &GetBannersArgs{}
}

func (p *GetBannersArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetBannersArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetBannersArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.WithDisabled = v
	}
	return nil
}

func (p *GetBannersArgs) readField3(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *GetBannersArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getBanners_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetBannersArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetBannersArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_disabled", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:with_disabled: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithDisabled)); err != nil {
		return fmt.Errorf("%T.with_disabled (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:with_disabled: %s", p, err)
	}
	return err
}

func (p *GetBannersArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:criteria: %s", p, err)
		}
	}
	return err
}

func (p *GetBannersArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBannersArgs(%+v)", *p)
}

type GetBannersResult struct {
	Success []*appserver_types.Banner `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewGetBannersResult() *GetBannersResult {
	return &GetBannersResult{}
}

func (p *GetBannersResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetBannersResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.Banner, 0, size)
	for i := 0; i < size; i++ {
		_elem81 := appserver_types.NewBanner()
		if err := _elem81.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem81)
		}
		p.Success = append(p.Success, _elem81)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetBannersResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetBannersResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getBanners_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetBannersResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetBannersResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetBannersResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetBannersResult(%+v)", *p)
}

type GetRecommendationArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	RecomType RecommendType         `thrift:"recom_type,2" json:"recom_type"`
	// unused field # 3
	Criteria *ListCriteria `thrift:"criteria,4" json:"criteria"`
}

func NewGetRecommendationArgs() *GetRecommendationArgs {
	return &GetRecommendationArgs{
		RecomType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *GetRecommendationArgs) IsSetRecomType() bool {
	return int64(p.RecomType) != math.MinInt32-1
}

func (p *GetRecommendationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRecommendationArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetRecommendationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.RecomType = RecommendType(v)
	}
	return nil
}

func (p *GetRecommendationArgs) readField4(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *GetRecommendationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRecommendation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRecommendationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetRecommendationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetRecomType() {
		if err := oprot.WriteFieldBegin("recom_type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:recom_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.RecomType)); err != nil {
			return fmt.Errorf("%T.recom_type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:recom_type: %s", p, err)
		}
	}
	return err
}

func (p *GetRecommendationArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:criteria: %s", p, err)
		}
	}
	return err
}

func (p *GetRecommendationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRecommendationArgs(%+v)", *p)
}

type GetRecommendationResult struct {
	Success []*appserver_types.Game `thrift:"success,0" json:"success"`
	Ae      *AppException           `thrift:"ae,1" json:"ae"`
}

func NewGetRecommendationResult() *GetRecommendationResult {
	return &GetRecommendationResult{}
}

func (p *GetRecommendationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetRecommendationResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.Game, 0, size)
	for i := 0; i < size; i++ {
		_elem82 := appserver_types.NewGame()
		if err := _elem82.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem82)
		}
		p.Success = append(p.Success, _elem82)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetRecommendationResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *GetRecommendationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getRecommendation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetRecommendationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetRecommendationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *GetRecommendationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetRecommendationResult(%+v)", *p)
}

type SearchDuoyouArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Query  string                `thrift:"query,2" json:"query"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	Stype SearchType `thrift:"stype,7" json:"stype"`
	// unused field # 8
	// unused field # 9
	Criteria *ListCriteria `thrift:"criteria,10" json:"criteria"`
}

func NewSearchDuoyouArgs() *SearchDuoyouArgs {
	return &SearchDuoyouArgs{
		Stype: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SearchDuoyouArgs) IsSetStype() bool {
	return int64(p.Stype) != math.MinInt32-1
}

func (p *SearchDuoyouArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDuoyouArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchDuoyouArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Query = v
	}
	return nil
}

func (p *SearchDuoyouArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Stype = SearchType(v)
	}
	return nil
}

func (p *SearchDuoyouArgs) readField10(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *SearchDuoyouArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDuoyou_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDuoyouArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchDuoyouArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("query", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:query: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Query)); err != nil {
		return fmt.Errorf("%T.query (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:query: %s", p, err)
	}
	return err
}

func (p *SearchDuoyouArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStype() {
		if err := oprot.WriteFieldBegin("stype", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:stype: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Stype)); err != nil {
			return fmt.Errorf("%T.stype (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:stype: %s", p, err)
		}
	}
	return err
}

func (p *SearchDuoyouArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:criteria: %s", p, err)
		}
	}
	return err
}

func (p *SearchDuoyouArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDuoyouArgs(%+v)", *p)
}

type SearchDuoyouResult struct {
	Success *appserver_types.MainList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewSearchDuoyouResult() *SearchDuoyouResult {
	return &SearchDuoyouResult{}
}

func (p *SearchDuoyouResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDuoyouResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewMainList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchDuoyouResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *SearchDuoyouResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDuoyou_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDuoyouResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchDuoyouResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *SearchDuoyouResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDuoyouResult(%+v)", *p)
}

type SearchGameArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Query  string                `thrift:"query,2" json:"query"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	Stype SearchType `thrift:"stype,7" json:"stype"`
	// unused field # 8
	// unused field # 9
	Criteria *ListCriteria `thrift:"criteria,10" json:"criteria"`
}

func NewSearchGameArgs() *SearchGameArgs {
	return &SearchGameArgs{
		Stype: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SearchGameArgs) IsSetStype() bool {
	return int64(p.Stype) != math.MinInt32-1
}

func (p *SearchGameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchGameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchGameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Query = v
	}
	return nil
}

func (p *SearchGameArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Stype = SearchType(v)
	}
	return nil
}

func (p *SearchGameArgs) readField10(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *SearchGameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchGame_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchGameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchGameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("query", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:query: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Query)); err != nil {
		return fmt.Errorf("%T.query (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:query: %s", p, err)
	}
	return err
}

func (p *SearchGameArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStype() {
		if err := oprot.WriteFieldBegin("stype", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:stype: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Stype)); err != nil {
			return fmt.Errorf("%T.stype (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:stype: %s", p, err)
		}
	}
	return err
}

func (p *SearchGameArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:criteria: %s", p, err)
		}
	}
	return err
}

func (p *SearchGameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchGameArgs(%+v)", *p)
}

type SearchGameResult struct {
	Success *appserver_types.GameList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewSearchGameResult() *SearchGameResult {
	return &SearchGameResult{}
}

func (p *SearchGameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchGameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewGameList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchGameResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *SearchGameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchGame_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchGameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchGameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *SearchGameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchGameResult(%+v)", *p)
}

type ListDuoyouArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	// unused field # 2
	Market Market `thrift:"market,3" json:"market"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Criteria *ListCriteria `thrift:"criteria,10" json:"criteria"`
}

func NewListDuoyouArgs() *ListDuoyouArgs {
	return &ListDuoyouArgs{
		Market: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListDuoyouArgs) IsSetMarket() bool {
	return int64(p.Market) != math.MinInt32-1
}

func (p *ListDuoyouArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListDuoyouArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListDuoyouArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Market = Market(v)
	}
	return nil
}

func (p *ListDuoyouArgs) readField10(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *ListDuoyouArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listDuoyou_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListDuoyouArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListDuoyouArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("market", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:market: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Market)); err != nil {
		return fmt.Errorf("%T.market (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:market: %s", p, err)
	}
	return err
}

func (p *ListDuoyouArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:criteria: %s", p, err)
		}
	}
	return err
}

func (p *ListDuoyouArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDuoyouArgs(%+v)", *p)
}

type ListDuoyouResult struct {
	Success *appserver_types.MainList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewListDuoyouResult() *ListDuoyouResult {
	return &ListDuoyouResult{}
}

func (p *ListDuoyouResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListDuoyouResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewMainList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListDuoyouResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListDuoyouResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listDuoyou_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListDuoyouResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListDuoyouResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListDuoyouResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListDuoyouResult(%+v)", *p)
}

type ListGiftArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Criteria *ListGiftCriteria     `thrift:"criteria,2" json:"criteria"`
}

func NewListGiftArgs() *ListGiftArgs {
	return &ListGiftArgs{}
}

func (p *ListGiftArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGiftArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListGiftArgs) readField2(iprot thrift.TProtocol) error {
	p.Criteria = NewListGiftCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *ListGiftArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listGift_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGiftArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:criteria: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGiftArgs(%+v)", *p)
}

type ListGiftResult struct {
	Success *appserver_types.GiftResult `thrift:"success,0" json:"success"`
	Ae      *AppException               `thrift:"ae,1" json:"ae"`
}

func NewListGiftResult() *ListGiftResult {
	return &ListGiftResult{}
}

func (p *ListGiftResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGiftResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewGiftResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListGiftResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListGiftResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listGift_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGiftResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGiftResult(%+v)", *p)
}

type ListGameArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Criteria *ListCriteria         `thrift:"criteria,2" json:"criteria"`
}

func NewListGameArgs() *ListGameArgs {
	return &ListGameArgs{}
}

func (p *ListGameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListGameArgs) readField2(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *ListGameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listGame_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListGameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:criteria: %s", p, err)
		}
	}
	return err
}

func (p *ListGameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGameArgs(%+v)", *p)
}

type ListGameResult struct {
	Success *appserver_types.GameList `thrift:"success,0" json:"success"`
	Ae      *AppException             `thrift:"ae,1" json:"ae"`
}

func NewListGameResult() *ListGameResult {
	return &ListGameResult{}
}

func (p *ListGameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_types.NewGameList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListGameResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *ListGameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listGame_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListGameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *ListGameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGameResult(%+v)", *p)
}

type CheckUpdateArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Packages map[string]int32      `thrift:"packages,2" json:"packages"`
	WithDesc bool                  `thrift:"withDesc,3" json:"withDesc"`
}

func NewCheckUpdateArgs() *CheckUpdateArgs {
	return &CheckUpdateArgs{}
}

func (p *CheckUpdateArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckUpdateArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CheckUpdateArgs) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Packages = make(map[string]int32, size)
	for i := 0; i < size; i++ {
		var _key83 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key83 = v
		}
		var _val84 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val84 = v
		}
		p.Packages[_key83] = _val84
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CheckUpdateArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.WithDesc = v
	}
	return nil
}

func (p *CheckUpdateArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkUpdate_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckUpdateArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CheckUpdateArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Packages != nil {
		if err := oprot.WriteFieldBegin("packages", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:packages: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.Packages)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Packages {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:packages: %s", p, err)
		}
	}
	return err
}

func (p *CheckUpdateArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("withDesc", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:withDesc: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithDesc)); err != nil {
		return fmt.Errorf("%T.withDesc (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:withDesc: %s", p, err)
	}
	return err
}

func (p *CheckUpdateArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckUpdateArgs(%+v)", *p)
}

type CheckUpdateResult struct {
	Success []*appserver_types.AppMain `thrift:"success,0" json:"success"`
	Ae      *AppException              `thrift:"ae,1" json:"ae"`
}

func NewCheckUpdateResult() *CheckUpdateResult {
	return &CheckUpdateResult{}
}

func (p *CheckUpdateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckUpdateResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.AppMain, 0, size)
	for i := 0; i < size; i++ {
		_elem85 := appserver_types.NewAppMain()
		if err := _elem85.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem85)
		}
		p.Success = append(p.Success, _elem85)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CheckUpdateResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *CheckUpdateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkUpdate_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckUpdateResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckUpdateResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *CheckUpdateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckUpdateResult(%+v)", *p)
}

type CheckGameUpdateArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Packages    map[string]int32      `thrift:"packages,2" json:"packages"`
	AppwallProt AppWallProt           `thrift:"appwall_prot,3" json:"appwall_prot"`
}

func NewCheckGameUpdateArgs() *CheckGameUpdateArgs {
	return &CheckGameUpdateArgs{}
}

func (p *CheckGameUpdateArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.MAP {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckGameUpdateArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CheckGameUpdateArgs) readField2(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Packages = make(map[string]int32, size)
	for i := 0; i < size; i++ {
		var _key86 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key86 = v
		}
		var _val87 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val87 = v
		}
		p.Packages[_key86] = _val87
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CheckGameUpdateArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key88 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key88 = v
		}
		var _val89 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val89 = v
		}
		p.AppwallProt[_key88] = _val89
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CheckGameUpdateArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkGameUpdate_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckGameUpdateArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CheckGameUpdateArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Packages != nil {
		if err := oprot.WriteFieldBegin("packages", thrift.MAP, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:packages: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.I32, len(p.Packages)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Packages {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:packages: %s", p, err)
		}
	}
	return err
}

func (p *CheckGameUpdateArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *CheckGameUpdateArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckGameUpdateArgs(%+v)", *p)
}

type CheckGameUpdateResult struct {
	Success []*appserver_types.Game `thrift:"success,0" json:"success"`
	Ae      *AppException           `thrift:"ae,1" json:"ae"`
}

func NewCheckGameUpdateResult() *CheckGameUpdateResult {
	return &CheckGameUpdateResult{}
}

func (p *CheckGameUpdateResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckGameUpdateResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*appserver_types.Game, 0, size)
	for i := 0; i < size; i++ {
		_elem90 := appserver_types.NewGame()
		if err := _elem90.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem90)
		}
		p.Success = append(p.Success, _elem90)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CheckGameUpdateResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *CheckGameUpdateResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("checkGameUpdate_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckGameUpdateResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckGameUpdateResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *CheckGameUpdateResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckGameUpdateResult(%+v)", *p)
}

type SupportAppArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Appid  int32                 `thrift:"appid,2" json:"appid"`
}

func NewSupportAppArgs() *SupportAppArgs {
	return &SupportAppArgs{}
}

func (p *SupportAppArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SupportAppArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SupportAppArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *SupportAppArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("supportApp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SupportAppArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SupportAppArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *SupportAppArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SupportAppArgs(%+v)", *p)
}

type SupportAppResult struct {
	Ae *AppException `thrift:"ae,1" json:"ae"`
}

func NewSupportAppResult() *SupportAppResult {
	return &SupportAppResult{}
}

func (p *SupportAppResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SupportAppResult) readField1(iprot thrift.TProtocol) error {
	p.Ae = NewAppException()
	if err := p.Ae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ae)
	}
	return nil
}

func (p *SupportAppResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("supportApp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SupportAppResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ae != nil {
		if err := oprot.WriteFieldBegin("ae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ae: %s", p, err)
		}
		if err := p.Ae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ae: %s", p, err)
		}
	}
	return err
}

func (p *SupportAppResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SupportAppResult(%+v)", *p)
}
