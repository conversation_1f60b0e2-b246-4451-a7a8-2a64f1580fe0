// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package vico_finance_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = enums.GoUnusedProtection__
var GoUnusedProtection__ int

//VICO开放平台各模块功能ID
//财务系统里将会按功能定义来验证权限
type VicoModuleId int64

const (
	VicoModuleId_VMI_UNKNOWN             VicoModuleId = 0
	VicoModuleId_VMI_PRODUCT             VicoModuleId = 1
	VicoModuleId_VMI_SEO_COLLECTION      VicoModuleId = 2
	VicoModuleId_VMI_SOCIAL_MEDIA_MANAGE VicoModuleId = 3
	VicoModuleId_VMI_AMAZON_PROMOTION    VicoModuleId = 4
	VicoModuleId_VMI_FT_TEMPLATE         VicoModuleId = 5
)

func (p VicoModuleId) String() string {
	switch p {
	case VicoModuleId_VMI_UNKNOWN:
		return "VicoModuleId_VMI_UNKNOWN"
	case VicoModuleId_VMI_PRODUCT:
		return "VicoModuleId_VMI_PRODUCT"
	case VicoModuleId_VMI_SEO_COLLECTION:
		return "VicoModuleId_VMI_SEO_COLLECTION"
	case VicoModuleId_VMI_SOCIAL_MEDIA_MANAGE:
		return "VicoModuleId_VMI_SOCIAL_MEDIA_MANAGE"
	case VicoModuleId_VMI_AMAZON_PROMOTION:
		return "VicoModuleId_VMI_AMAZON_PROMOTION"
	case VicoModuleId_VMI_FT_TEMPLATE:
		return "VicoModuleId_VMI_FT_TEMPLATE"
	}
	return "<UNSET>"
}

func VicoModuleIdFromString(s string) (VicoModuleId, error) {
	switch s {
	case "VicoModuleId_VMI_UNKNOWN":
		return VicoModuleId_VMI_UNKNOWN, nil
	case "VicoModuleId_VMI_PRODUCT":
		return VicoModuleId_VMI_PRODUCT, nil
	case "VicoModuleId_VMI_SEO_COLLECTION":
		return VicoModuleId_VMI_SEO_COLLECTION, nil
	case "VicoModuleId_VMI_SOCIAL_MEDIA_MANAGE":
		return VicoModuleId_VMI_SOCIAL_MEDIA_MANAGE, nil
	case "VicoModuleId_VMI_AMAZON_PROMOTION":
		return VicoModuleId_VMI_AMAZON_PROMOTION, nil
	case "VicoModuleId_VMI_FT_TEMPLATE":
		return VicoModuleId_VMI_FT_TEMPLATE, nil
	}
	return VicoModuleId(math.MinInt32 - 1), fmt.Errorf("not a valid VicoModuleId string")
}

//服务产品类型
type ServiceProductType int64

const (
	ServiceProductType_SPT_UNKNOWN     ServiceProductType = 0
	ServiceProductType_SPT_PRODUCT     ServiceProductType = 1
	ServiceProductType_SPT_PACKAGE     ServiceProductType = 2
	ServiceProductType_SPT_FT_TEMPLATE ServiceProductType = 3
)

func (p ServiceProductType) String() string {
	switch p {
	case ServiceProductType_SPT_UNKNOWN:
		return "ServiceProductType_SPT_UNKNOWN"
	case ServiceProductType_SPT_PRODUCT:
		return "ServiceProductType_SPT_PRODUCT"
	case ServiceProductType_SPT_PACKAGE:
		return "ServiceProductType_SPT_PACKAGE"
	case ServiceProductType_SPT_FT_TEMPLATE:
		return "ServiceProductType_SPT_FT_TEMPLATE"
	}
	return "<UNSET>"
}

func ServiceProductTypeFromString(s string) (ServiceProductType, error) {
	switch s {
	case "ServiceProductType_SPT_UNKNOWN":
		return ServiceProductType_SPT_UNKNOWN, nil
	case "ServiceProductType_SPT_PRODUCT":
		return ServiceProductType_SPT_PRODUCT, nil
	case "ServiceProductType_SPT_PACKAGE":
		return ServiceProductType_SPT_PACKAGE, nil
	case "ServiceProductType_SPT_FT_TEMPLATE":
		return ServiceProductType_SPT_FT_TEMPLATE, nil
	}
	return ServiceProductType(math.MinInt32 - 1), fmt.Errorf("not a valid ServiceProductType string")
}

//服务产品套装状态
type ServiceProductStatus int64

const (
	ServiceProductStatus_SPPS_ONLINE  ServiceProductStatus = 0
	ServiceProductStatus_SPPS_OFFLINE ServiceProductStatus = 1
	ServiceProductStatus_SPPS_DELETED ServiceProductStatus = 2
)

func (p ServiceProductStatus) String() string {
	switch p {
	case ServiceProductStatus_SPPS_ONLINE:
		return "ServiceProductStatus_SPPS_ONLINE"
	case ServiceProductStatus_SPPS_OFFLINE:
		return "ServiceProductStatus_SPPS_OFFLINE"
	case ServiceProductStatus_SPPS_DELETED:
		return "ServiceProductStatus_SPPS_DELETED"
	}
	return "<UNSET>"
}

func ServiceProductStatusFromString(s string) (ServiceProductStatus, error) {
	switch s {
	case "ServiceProductStatus_SPPS_ONLINE":
		return ServiceProductStatus_SPPS_ONLINE, nil
	case "ServiceProductStatus_SPPS_OFFLINE":
		return ServiceProductStatus_SPPS_OFFLINE, nil
	case "ServiceProductStatus_SPPS_DELETED":
		return ServiceProductStatus_SPPS_DELETED, nil
	}
	return ServiceProductStatus(math.MinInt32 - 1), fmt.Errorf("not a valid ServiceProductStatus string")
}

//财务订单类型
type FinanceOrderType int64

const (
	FinanceOrderType_FOT_UNKNOWN  FinanceOrderType = 0
	FinanceOrderType_FOT_PRODUCT  FinanceOrderType = 1
	FinanceOrderType_FOT_PACKAGE  FinanceOrderType = 2
	FinanceOrderType_FOT_TEMPLATE FinanceOrderType = 3
)

func (p FinanceOrderType) String() string {
	switch p {
	case FinanceOrderType_FOT_UNKNOWN:
		return "FinanceOrderType_FOT_UNKNOWN"
	case FinanceOrderType_FOT_PRODUCT:
		return "FinanceOrderType_FOT_PRODUCT"
	case FinanceOrderType_FOT_PACKAGE:
		return "FinanceOrderType_FOT_PACKAGE"
	case FinanceOrderType_FOT_TEMPLATE:
		return "FinanceOrderType_FOT_TEMPLATE"
	}
	return "<UNSET>"
}

func FinanceOrderTypeFromString(s string) (FinanceOrderType, error) {
	switch s {
	case "FinanceOrderType_FOT_UNKNOWN":
		return FinanceOrderType_FOT_UNKNOWN, nil
	case "FinanceOrderType_FOT_PRODUCT":
		return FinanceOrderType_FOT_PRODUCT, nil
	case "FinanceOrderType_FOT_PACKAGE":
		return FinanceOrderType_FOT_PACKAGE, nil
	case "FinanceOrderType_FOT_TEMPLATE":
		return FinanceOrderType_FOT_TEMPLATE, nil
	}
	return FinanceOrderType(math.MinInt32 - 1), fmt.Errorf("not a valid FinanceOrderType string")
}

//支付类型
type FinanceOrderPaymentType int64

const (
	FinanceOrderPaymentType_FOPT_UNKNOWN FinanceOrderPaymentType = 0
	FinanceOrderPaymentType_FOPT_ALIPAY  FinanceOrderPaymentType = 1
	FinanceOrderPaymentType_FOPT_WECHAT  FinanceOrderPaymentType = 2
)

func (p FinanceOrderPaymentType) String() string {
	switch p {
	case FinanceOrderPaymentType_FOPT_UNKNOWN:
		return "FinanceOrderPaymentType_FOPT_UNKNOWN"
	case FinanceOrderPaymentType_FOPT_ALIPAY:
		return "FinanceOrderPaymentType_FOPT_ALIPAY"
	case FinanceOrderPaymentType_FOPT_WECHAT:
		return "FinanceOrderPaymentType_FOPT_WECHAT"
	}
	return "<UNSET>"
}

func FinanceOrderPaymentTypeFromString(s string) (FinanceOrderPaymentType, error) {
	switch s {
	case "FinanceOrderPaymentType_FOPT_UNKNOWN":
		return FinanceOrderPaymentType_FOPT_UNKNOWN, nil
	case "FinanceOrderPaymentType_FOPT_ALIPAY":
		return FinanceOrderPaymentType_FOPT_ALIPAY, nil
	case "FinanceOrderPaymentType_FOPT_WECHAT":
		return FinanceOrderPaymentType_FOPT_WECHAT, nil
	}
	return FinanceOrderPaymentType(math.MinInt32 - 1), fmt.Errorf("not a valid FinanceOrderPaymentType string")
}

//财务订单状态
type FinanceOrderStatus int64

const (
	FinanceOrderStatus_FOS_UNKNOWN  FinanceOrderStatus = 0
	FinanceOrderStatus_FOS_UNPAID   FinanceOrderStatus = 1
	FinanceOrderStatus_FOS_COMPLETE FinanceOrderStatus = 2
	FinanceOrderStatus_FOS_CANCEL   FinanceOrderStatus = 3
	FinanceOrderStatus_FOS_EXPIRED  FinanceOrderStatus = 4
	FinanceOrderStatus_FOS_DELETED  FinanceOrderStatus = 5
)

func (p FinanceOrderStatus) String() string {
	switch p {
	case FinanceOrderStatus_FOS_UNKNOWN:
		return "FinanceOrderStatus_FOS_UNKNOWN"
	case FinanceOrderStatus_FOS_UNPAID:
		return "FinanceOrderStatus_FOS_UNPAID"
	case FinanceOrderStatus_FOS_COMPLETE:
		return "FinanceOrderStatus_FOS_COMPLETE"
	case FinanceOrderStatus_FOS_CANCEL:
		return "FinanceOrderStatus_FOS_CANCEL"
	case FinanceOrderStatus_FOS_EXPIRED:
		return "FinanceOrderStatus_FOS_EXPIRED"
	case FinanceOrderStatus_FOS_DELETED:
		return "FinanceOrderStatus_FOS_DELETED"
	}
	return "<UNSET>"
}

func FinanceOrderStatusFromString(s string) (FinanceOrderStatus, error) {
	switch s {
	case "FinanceOrderStatus_FOS_UNKNOWN":
		return FinanceOrderStatus_FOS_UNKNOWN, nil
	case "FinanceOrderStatus_FOS_UNPAID":
		return FinanceOrderStatus_FOS_UNPAID, nil
	case "FinanceOrderStatus_FOS_COMPLETE":
		return FinanceOrderStatus_FOS_COMPLETE, nil
	case "FinanceOrderStatus_FOS_CANCEL":
		return FinanceOrderStatus_FOS_CANCEL, nil
	case "FinanceOrderStatus_FOS_EXPIRED":
		return FinanceOrderStatus_FOS_EXPIRED, nil
	case "FinanceOrderStatus_FOS_DELETED":
		return FinanceOrderStatus_FOS_DELETED, nil
	}
	return FinanceOrderStatus(math.MinInt32 - 1), fmt.Errorf("not a valid FinanceOrderStatus string")
}

type FinanceOrderQueryParams struct {
	AccountId int64              `thrift:"accountId,1" json:"accountId"`
	OrderType FinanceOrderType   `thrift:"orderType,2" json:"orderType"`
	Status    FinanceOrderStatus `thrift:"status,3" json:"status"`
}

func NewFinanceOrderQueryParams() *FinanceOrderQueryParams {
	return &FinanceOrderQueryParams{
		OrderType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FinanceOrderQueryParams) IsSetOrderType() bool {
	return int64(p.OrderType) != math.MinInt32-1
}

func (p *FinanceOrderQueryParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FinanceOrderQueryParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceOrderQueryParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *FinanceOrderQueryParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.OrderType = FinanceOrderType(v)
	}
	return nil
}

func (p *FinanceOrderQueryParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = FinanceOrderStatus(v)
	}
	return nil
}

func (p *FinanceOrderQueryParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceOrderQueryParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceOrderQueryParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:accountId: %s", p, err)
	}
	return err
}

func (p *FinanceOrderQueryParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetOrderType() {
		if err := oprot.WriteFieldBegin("orderType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:orderType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.OrderType)); err != nil {
			return fmt.Errorf("%T.orderType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:orderType: %s", p, err)
		}
	}
	return err
}

func (p *FinanceOrderQueryParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:status: %s", p, err)
		}
	}
	return err
}

func (p *FinanceOrderQueryParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceOrderQueryParams(%+v)", *p)
}

type FinanceServiceProductParams struct {
	ProductType ServiceProductType   `thrift:"productType,1" json:"productType"`
	Name        string               `thrift:"name,2" json:"name"`
	Status      ServiceProductStatus `thrift:"status,3" json:"status"`
}

func NewFinanceServiceProductParams() *FinanceServiceProductParams {
	return &FinanceServiceProductParams{
		ProductType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FinanceServiceProductParams) IsSetProductType() bool {
	return int64(p.ProductType) != math.MinInt32-1
}

func (p *FinanceServiceProductParams) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FinanceServiceProductParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceServiceProductParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ProductType = ServiceProductType(v)
	}
	return nil
}

func (p *FinanceServiceProductParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *FinanceServiceProductParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Status = ServiceProductStatus(v)
	}
	return nil
}

func (p *FinanceServiceProductParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceServiceProductParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceServiceProductParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductType() {
		if err := oprot.WriteFieldBegin("productType", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:productType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProductType)); err != nil {
			return fmt.Errorf("%T.productType (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:productType: %s", p, err)
		}
	}
	return err
}

func (p *FinanceServiceProductParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProductParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:status: %s", p, err)
		}
	}
	return err
}

func (p *FinanceServiceProductParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceServiceProductParams(%+v)", *p)
}

type FinanceAccountSetting struct {
	AccountId int64        `thrift:"accountId,1" json:"accountId"`
	ModuleId  VicoModuleId `thrift:"moduleId,2" json:"moduleId"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	StartTime   int64   `thrift:"startTime,10" json:"startTime"`
	EndTime     int64   `thrift:"endTime,11" json:"endTime"`
	Limit       int32   `thrift:"limit,12" json:"limit"`
	Used        int32   `thrift:"used,13" json:"used"`
	TemplateIds []int64 `thrift:"templateIds,14" json:"templateIds"`
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime int64 `thrift:"createTime,30" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewFinanceAccountSetting() *FinanceAccountSetting {
	return &FinanceAccountSetting{
		ModuleId: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FinanceAccountSetting) IsSetModuleId() bool {
	return int64(p.ModuleId) != math.MinInt32-1
}

func (p *FinanceAccountSetting) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceAccountSetting) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *FinanceAccountSetting) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ModuleId = VicoModuleId(v)
	}
	return nil
}

func (p *FinanceAccountSetting) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *FinanceAccountSetting) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *FinanceAccountSetting) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *FinanceAccountSetting) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Used = v
	}
	return nil
}

func (p *FinanceAccountSetting) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TemplateIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.TemplateIds = append(p.TemplateIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *FinanceAccountSetting) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *FinanceAccountSetting) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *FinanceAccountSetting) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceAccountSetting"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceAccountSetting) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:accountId: %s", p, err)
	}
	return err
}

func (p *FinanceAccountSetting) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetModuleId() {
		if err := oprot.WriteFieldBegin("moduleId", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:moduleId: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ModuleId)); err != nil {
			return fmt.Errorf("%T.moduleId (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:moduleId: %s", p, err)
		}
	}
	return err
}

func (p *FinanceAccountSetting) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:startTime: %s", p, err)
	}
	return err
}

func (p *FinanceAccountSetting) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:endTime: %s", p, err)
	}
	return err
}

func (p *FinanceAccountSetting) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:limit: %s", p, err)
	}
	return err
}

func (p *FinanceAccountSetting) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("used", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:used: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Used)); err != nil {
		return fmt.Errorf("%T.used (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:used: %s", p, err)
	}
	return err
}

func (p *FinanceAccountSetting) writeField14(oprot thrift.TProtocol) (err error) {
	if p.TemplateIds != nil {
		if err := oprot.WriteFieldBegin("templateIds", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:templateIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.TemplateIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TemplateIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:templateIds: %s", p, err)
		}
	}
	return err
}

func (p *FinanceAccountSetting) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *FinanceAccountSetting) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *FinanceAccountSetting) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceAccountSetting(%+v)", *p)
}

type FinanceServiceProduct struct {
	Id         int64                  `thrift:"id,1" json:"id"`
	TypeA1     ServiceProductType     `thrift:"type,2" json:"type"`
	ModuleInfo map[VicoModuleId]int32 `thrift:"moduleInfo,3" json:"moduleInfo"`
	ModuleId   VicoModuleId           `thrift:"moduleId,4" json:"moduleId"`
	Limit      int32                  `thrift:"limit,5" json:"limit"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Name        string               `thrift:"name,10" json:"name"`
	Days        int32                `thrift:"days,11" json:"days"`
	Description string               `thrift:"description,12" json:"description"`
	Price       int64                `thrift:"price,13" json:"price"`
	Status      ServiceProductStatus `thrift:"status,14" json:"status"`
	Weight      int32                `thrift:"weight,15" json:"weight"`
	Ext         string               `thrift:"ext,16" json:"ext"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64 `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewFinanceServiceProduct() *FinanceServiceProduct {
	return &FinanceServiceProduct{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		ModuleId: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FinanceServiceProduct) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *FinanceServiceProduct) IsSetModuleId() bool {
	return int64(p.ModuleId) != math.MinInt32-1
}

func (p *FinanceServiceProduct) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FinanceServiceProduct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceServiceProduct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = ServiceProductType(v)
	}
	return nil
}

func (p *FinanceServiceProduct) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ModuleInfo = make(map[VicoModuleId]int32, size)
	for i := 0; i < size; i++ {
		var _key1 VicoModuleId
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = VicoModuleId(v)
		}
		var _val2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = v
		}
		p.ModuleInfo[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *FinanceServiceProduct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ModuleId = VicoModuleId(v)
	}
	return nil
}

func (p *FinanceServiceProduct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Days = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Status = ServiceProductStatus(v)
	}
	return nil
}

func (p *FinanceServiceProduct) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Weight = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Ext = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *FinanceServiceProduct) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *FinanceServiceProduct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceServiceProduct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceServiceProduct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *FinanceServiceProduct) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ModuleInfo != nil {
		if err := oprot.WriteFieldBegin("moduleInfo", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:moduleInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.ModuleInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ModuleInfo {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:moduleInfo: %s", p, err)
		}
	}
	return err
}

func (p *FinanceServiceProduct) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetModuleId() {
		if err := oprot.WriteFieldBegin("moduleId", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:moduleId: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ModuleId)); err != nil {
			return fmt.Errorf("%T.moduleId (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:moduleId: %s", p, err)
		}
	}
	return err
}

func (p *FinanceServiceProduct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:name: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("days", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:days: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Days)); err != nil {
		return fmt.Errorf("%T.days (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:days: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:description: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:price: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (14) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:status: %s", p, err)
		}
	}
	return err
}

func (p *FinanceServiceProduct) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("weight", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:weight: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Weight)); err != nil {
		return fmt.Errorf("%T.weight (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:weight: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:ext: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ext)); err != nil {
		return fmt.Errorf("%T.ext (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:ext: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *FinanceServiceProduct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceServiceProduct(%+v)", *p)
}

type FinanceOrder struct {
	Id          int64                   `thrift:"id,1" json:"id"`
	AccountId   int64                   `thrift:"accountId,2" json:"accountId"`
	TypeA1      FinanceOrderType        `thrift:"type,3" json:"type"`
	RelatedId   int64                   `thrift:"relatedId,4" json:"relatedId"`
	PaymentType FinanceOrderPaymentType `thrift:"paymentType,5" json:"paymentType"`
	OutTradeNo  string                  `thrift:"outTradeNo,6" json:"outTradeNo"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Title       string `thrift:"title,10" json:"title"`
	Detail      string `thrift:"detail,11" json:"detail"`
	StartTime   int64  `thrift:"startTime,12" json:"startTime"`
	EndTime     int64  `thrift:"endTime,13" json:"endTime"`
	Num         int32  `thrift:"num,14" json:"num"`
	TotalAmount int64  `thrift:"totalAmount,15" json:"totalAmount"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	Status     FinanceOrderStatus `thrift:"status,19" json:"status"`
	CreateTime int64              `thrift:"createTime,20" json:"createTime"`
	LastUpdate int64              `thrift:"lastUpdate,21" json:"lastUpdate"`
}

func NewFinanceOrder() *FinanceOrder {
	return &FinanceOrder{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		PaymentType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FinanceOrder) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *FinanceOrder) IsSetPaymentType() bool {
	return int64(p.PaymentType) != math.MinInt32-1
}

func (p *FinanceOrder) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FinanceOrder) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceOrder) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FinanceOrder) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AccountId = v
	}
	return nil
}

func (p *FinanceOrder) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = FinanceOrderType(v)
	}
	return nil
}

func (p *FinanceOrder) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.RelatedId = v
	}
	return nil
}

func (p *FinanceOrder) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PaymentType = FinanceOrderPaymentType(v)
	}
	return nil
}

func (p *FinanceOrder) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OutTradeNo = v
	}
	return nil
}

func (p *FinanceOrder) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *FinanceOrder) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *FinanceOrder) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *FinanceOrder) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *FinanceOrder) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Num = v
	}
	return nil
}

func (p *FinanceOrder) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.TotalAmount = v
	}
	return nil
}

func (p *FinanceOrder) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Status = FinanceOrderStatus(v)
	}
	return nil
}

func (p *FinanceOrder) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *FinanceOrder) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *FinanceOrder) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceOrder"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceOrder) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:accountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccountId)); err != nil {
		return fmt.Errorf("%T.accountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:accountId: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *FinanceOrder) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("relatedId", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:relatedId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RelatedId)); err != nil {
		return fmt.Errorf("%T.relatedId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:relatedId: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaymentType() {
		if err := oprot.WriteFieldBegin("paymentType", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:paymentType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PaymentType)); err != nil {
			return fmt.Errorf("%T.paymentType (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:paymentType: %s", p, err)
		}
	}
	return err
}

func (p *FinanceOrder) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("outTradeNo", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:outTradeNo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OutTradeNo)); err != nil {
		return fmt.Errorf("%T.outTradeNo (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:outTradeNo: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:title: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:detail: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:startTime: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:endTime: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("num", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Num)); err != nil {
		return fmt.Errorf("%T.num (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:num: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalAmount", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:totalAmount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalAmount)); err != nil {
		return fmt.Errorf("%T.totalAmount (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:totalAmount: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 19); err != nil {
			return fmt.Errorf("%T write field begin error 19:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (19) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 19:status: %s", p, err)
		}
	}
	return err
}

func (p *FinanceOrder) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:createTime: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:lastUpdate: %s", p, err)
	}
	return err
}

func (p *FinanceOrder) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceOrder(%+v)", *p)
}
