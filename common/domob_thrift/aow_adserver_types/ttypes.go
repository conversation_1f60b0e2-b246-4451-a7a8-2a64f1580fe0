// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package aow_adserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/aow_ui_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = aow_ui_types.GoUnusedProtection__
var GoUnusedProtection__ int

//when server return empty []offers
//this is y
type Emptype int64

const (
	Emptype_NOOFFERS   Emptype = 0
	Emptype_RESTRICTED Emptype = 1
	Emptype_ALLBEDONE  Emptype = 2
)

func (p Emptype) String() string {
	switch p {
	case Emptype_NOOFFERS:
		return "Emptype_NOOFFERS"
	case Emptype_RESTRICTED:
		return "Emptype_RESTRICTED"
	case Emptype_ALLBEDONE:
		return "Emptype_ALLBEDONE"
	}
	return "<UNSET>"
}

func EmptypeFromString(s string) (Emptype, error) {
	switch s {
	case "Emptype_NOOFFERS":
		return Emptype_NOOFFERS, nil
	case "Emptype_RESTRICTED":
		return Emptype_RESTRICTED, nil
	case "Emptype_ALLBEDONE":
		return Emptype_ALLBEDONE, nil
	}
	return Emptype(math.MinInt32 - 1), fmt.Errorf("not a valid Emptype string")
}

//消费请求处理状态
type AowConsumeStatus int64

const (
	AowConsumeStatus_NOT_A_CONSUME      AowConsumeStatus = 0
	AowConsumeStatus_CONSUME_SUCCESS    AowConsumeStatus = 1
	AowConsumeStatus_INSUFFICIENT_POINT AowConsumeStatus = 2
	AowConsumeStatus_REDUPLICATE_ORDER  AowConsumeStatus = 3
	AowConsumeStatus_INVALID_USER       AowConsumeStatus = 4
	AowConsumeStatus_REQUEST_ERROR      AowConsumeStatus = 5
	AowConsumeStatus_SERVER_ERROR       AowConsumeStatus = 6
)

func (p AowConsumeStatus) String() string {
	switch p {
	case AowConsumeStatus_NOT_A_CONSUME:
		return "AowConsumeStatus_NOT_A_CONSUME"
	case AowConsumeStatus_CONSUME_SUCCESS:
		return "AowConsumeStatus_CONSUME_SUCCESS"
	case AowConsumeStatus_INSUFFICIENT_POINT:
		return "AowConsumeStatus_INSUFFICIENT_POINT"
	case AowConsumeStatus_REDUPLICATE_ORDER:
		return "AowConsumeStatus_REDUPLICATE_ORDER"
	case AowConsumeStatus_INVALID_USER:
		return "AowConsumeStatus_INVALID_USER"
	case AowConsumeStatus_REQUEST_ERROR:
		return "AowConsumeStatus_REQUEST_ERROR"
	case AowConsumeStatus_SERVER_ERROR:
		return "AowConsumeStatus_SERVER_ERROR"
	}
	return "<UNSET>"
}

func AowConsumeStatusFromString(s string) (AowConsumeStatus, error) {
	switch s {
	case "AowConsumeStatus_NOT_A_CONSUME":
		return AowConsumeStatus_NOT_A_CONSUME, nil
	case "AowConsumeStatus_CONSUME_SUCCESS":
		return AowConsumeStatus_CONSUME_SUCCESS, nil
	case "AowConsumeStatus_INSUFFICIENT_POINT":
		return AowConsumeStatus_INSUFFICIENT_POINT, nil
	case "AowConsumeStatus_REDUPLICATE_ORDER":
		return AowConsumeStatus_REDUPLICATE_ORDER, nil
	case "AowConsumeStatus_INVALID_USER":
		return AowConsumeStatus_INVALID_USER, nil
	case "AowConsumeStatus_REQUEST_ERROR":
		return AowConsumeStatus_REQUEST_ERROR, nil
	case "AowConsumeStatus_SERVER_ERROR":
		return AowConsumeStatus_SERVER_ERROR, nil
	}
	return AowConsumeStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AowConsumeStatus string")
}

type AowAdPlan struct {
	// unused field # 1
	Mid                 int32          `thrift:"mid,2" json:"mid"`
	AccessTarget        string         `thrift:"access_target,3" json:"access_target"`
	DeviceTarget        string         `thrift:"device_target,4" json:"device_target"`
	MediaTagTarget      string         `thrift:"media_tag_target,5" json:"media_tag_target"`
	BlackMediaTagTarget string         `thrift:"black_media_tag_target,6" json:"black_media_tag_target"`
	RegionTarget        string         `thrift:"region_target,7" json:"region_target"`
	OsTarget            string         `thrift:"os_target,8" json:"os_target"`
	Timeslots           string         `thrift:"timeslots,9" json:"timeslots"`
	Gamma               int32          `thrift:"gamma,10" json:"gamma"`
	Starttime           common.TimeInt `thrift:"starttime,11" json:"starttime"`
	ClkNum              int32          `thrift:"clk_num,12" json:"clk_num"`
	OfferType           int16          `thrift:"offer_type,13" json:"offer_type"`
	CostType            int16          `thrift:"cost_type,14" json:"cost_type"`
	CarrierTarget       string         `thrift:"carrier_target,15" json:"carrier_target"`
	RootSimTarget       int32          `thrift:"root_sim_target,16" json:"root_sim_target"`
}

func NewAowAdPlan() *AowAdPlan {
	return &AowAdPlan{}
}

func (p *AowAdPlan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I16 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I16 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowAdPlan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *AowAdPlan) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AccessTarget = v
	}
	return nil
}

func (p *AowAdPlan) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DeviceTarget = v
	}
	return nil
}

func (p *AowAdPlan) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MediaTagTarget = v
	}
	return nil
}

func (p *AowAdPlan) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.BlackMediaTagTarget = v
	}
	return nil
}

func (p *AowAdPlan) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.RegionTarget = v
	}
	return nil
}

func (p *AowAdPlan) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.OsTarget = v
	}
	return nil
}

func (p *AowAdPlan) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Timeslots = v
	}
	return nil
}

func (p *AowAdPlan) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Gamma = v
	}
	return nil
}

func (p *AowAdPlan) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Starttime = common.TimeInt(v)
	}
	return nil
}

func (p *AowAdPlan) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ClkNum = v
	}
	return nil
}

func (p *AowAdPlan) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *AowAdPlan) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *AowAdPlan) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.CarrierTarget = v
	}
	return nil
}

func (p *AowAdPlan) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.RootSimTarget = v
	}
	return nil
}

func (p *AowAdPlan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowAdPlan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowAdPlan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:mid: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_target", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:access_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessTarget)); err != nil {
		return fmt.Errorf("%T.access_target (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:access_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_target", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:device_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceTarget)); err != nil {
		return fmt.Errorf("%T.device_target (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:device_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_tag_target", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:media_tag_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaTagTarget)); err != nil {
		return fmt.Errorf("%T.media_tag_target (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:media_tag_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("black_media_tag_target", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:black_media_tag_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BlackMediaTagTarget)); err != nil {
		return fmt.Errorf("%T.black_media_tag_target (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:black_media_tag_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_target", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:region_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.RegionTarget)); err != nil {
		return fmt.Errorf("%T.region_target (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:region_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_target", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:os_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OsTarget)); err != nil {
		return fmt.Errorf("%T.os_target (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:os_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timeslots", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:timeslots: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Timeslots)); err != nil {
		return fmt.Errorf("%T.timeslots (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:timeslots: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gamma", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:gamma: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Gamma)); err != nil {
		return fmt.Errorf("%T.gamma (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:gamma: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("starttime", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:starttime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Starttime)); err != nil {
		return fmt.Errorf("%T.starttime (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:starttime: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_num", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:clk_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkNum)); err != nil {
		return fmt.Errorf("%T.clk_num (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:clk_num: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I16, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:offer_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:offer_type: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I16, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:cost_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:cost_type: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_target", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:carrier_target: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CarrierTarget)); err != nil {
		return fmt.Errorf("%T.carrier_target (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:carrier_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("root_sim_target", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:root_sim_target: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RootSimTarget)); err != nil {
		return fmt.Errorf("%T.root_sim_target (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:root_sim_target: %s", p, err)
	}
	return err
}

func (p *AowAdPlan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowAdPlan(%+v)", *p)
}

type AowAdCreative struct {
	Cid         int32         `thrift:"cid,1" json:"cid"`
	Pid         int32         `thrift:"pid,2" json:"pid"`
	Uid         int32         `thrift:"uid,3" json:"uid"`
	Pkgid       int32         `thrift:"pkgid,4" json:"pkgid"`
	Name        string        `thrift:"name,5" json:"name"`
	Logo        int32         `thrift:"logo,6" json:"logo"`
	Description string        `thrift:"description,7" json:"description"`
	Price       common.Amount `thrift:"price,8" json:"price"`
	// unused field # 9
	// unused field # 10
	Url     string `thrift:"url,11" json:"url"`
	Detail  string `thrift:"detail,12" json:"detail"`
	Nopoint bool   `thrift:"nopoint,13" json:"nopoint"`
	// unused field # 14
	Text    string        `thrift:"text,15" json:"text"`
	SpPrice common.Amount `thrift:"sp_price,16" json:"sp_price"`
	// unused field # 17
	// unused field # 18
	DisplayName       string                  `thrift:"display_name,19" json:"display_name"`
	ClickButtonName   string                  `thrift:"click_button_name,20" json:"click_button_name"`
	UrlActionType     int8                    `thrift:"url_action_type,21" json:"url_action_type"`
	Screenshots       string                  `thrift:"screenshots,22" json:"screenshots"`
	CornerMark        int32                   `thrift:"corner_mark,23" json:"corner_mark"`
	AppDescription    string                  `thrift:"app_description,24" json:"app_description"`
	LaunchPrompt      string                  `thrift:"launch_prompt,25" json:"launch_prompt"`
	Tasks             []*aow_ui_types.Task    `thrift:"tasks,26" json:"tasks"`
	Prices            map[int32]common.Amount `thrift:"prices,27" json:"prices"`
	LogoUrl           string                  `thrift:"logo_url,28" json:"logo_url"`
	SimpleDescription string                  `thrift:"simple_description,29" json:"simple_description"`
	TaskPrice         int32                   `thrift:"taskPrice,30" json:"taskPrice"`
	Pkgids            []string                `thrift:"pkgids,31" json:"pkgids"`
	Notice            string                  `thrift:"notice,32" json:"notice"`
	Prompt            string                  `thrift:"prompt,33" json:"prompt"`
	IsSuperTask       bool                    `thrift:"is_super_task,34" json:"is_super_task"`
	Tag               string                  `thrift:"tag,35" json:"tag"`
}

func NewAowAdCreative() *AowAdCreative {
	return &AowAdCreative{}
}

func (p *AowAdCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I64 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.LIST {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.MAP {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.STRING {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.LIST {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowAdCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *AowAdCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *AowAdCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AowAdCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Pkgid = v
	}
	return nil
}

func (p *AowAdCreative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AowAdCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *AowAdCreative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *AowAdCreative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Price = common.Amount(v)
	}
	return nil
}

func (p *AowAdCreative) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *AowAdCreative) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *AowAdCreative) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Nopoint = v
	}
	return nil
}

func (p *AowAdCreative) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *AowAdCreative) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.SpPrice = common.Amount(v)
	}
	return nil
}

func (p *AowAdCreative) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.DisplayName = v
	}
	return nil
}

func (p *AowAdCreative) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ClickButtonName = v
	}
	return nil
}

func (p *AowAdCreative) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.UrlActionType = int8(v)
	}
	return nil
}

func (p *AowAdCreative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Screenshots = v
	}
	return nil
}

func (p *AowAdCreative) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.CornerMark = v
	}
	return nil
}

func (p *AowAdCreative) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.AppDescription = v
	}
	return nil
}

func (p *AowAdCreative) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.LaunchPrompt = v
	}
	return nil
}

func (p *AowAdCreative) readField26(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tasks = make([]*aow_ui_types.Task, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := aow_ui_types.NewTask()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Tasks = append(p.Tasks, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AowAdCreative) readField27(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Prices = make(map[int32]common.Amount, size)
	for i := 0; i < size; i++ {
		var _key1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = v
		}
		var _val2 common.Amount
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = common.Amount(v)
		}
		p.Prices[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AowAdCreative) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.LogoUrl = v
	}
	return nil
}

func (p *AowAdCreative) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.SimpleDescription = v
	}
	return nil
}

func (p *AowAdCreative) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.TaskPrice = v
	}
	return nil
}

func (p *AowAdCreative) readField31(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Pkgids = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.Pkgids = append(p.Pkgids, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AowAdCreative) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Notice = v
	}
	return nil
}

func (p *AowAdCreative) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Prompt = v
	}
	return nil
}

func (p *AowAdCreative) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.IsSuperTask = v
	}
	return nil
}

func (p *AowAdCreative) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Tag = v
	}
	return nil
}

func (p *AowAdCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowAdCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowAdCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cid: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pkgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pkgid)); err != nil {
		return fmt.Errorf("%T.pkgid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pkgid: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:logo: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:logo: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:description: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:price: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:url: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:detail: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("nopoint", thrift.BOOL, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:nopoint: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Nopoint)); err != nil {
		return fmt.Errorf("%T.nopoint (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:nopoint: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:text: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sp_price: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_name", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:display_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayName)); err != nil {
		return fmt.Errorf("%T.display_name (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:display_name: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_button_name", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:click_button_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickButtonName)); err != nil {
		return fmt.Errorf("%T.click_button_name (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:click_button_name: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url_action_type", thrift.BYTE, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:url_action_type: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.UrlActionType)); err != nil {
		return fmt.Errorf("%T.url_action_type (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:url_action_type: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screenshots", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:screenshots: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Screenshots)); err != nil {
		return fmt.Errorf("%T.screenshots (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:screenshots: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("corner_mark", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:corner_mark: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CornerMark)); err != nil {
		return fmt.Errorf("%T.corner_mark (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:corner_mark: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_description", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:app_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppDescription)); err != nil {
		return fmt.Errorf("%T.app_description (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:app_description: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("launch_prompt", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:launch_prompt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LaunchPrompt)); err != nil {
		return fmt.Errorf("%T.launch_prompt (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:launch_prompt: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField26(oprot thrift.TProtocol) (err error) {
	if p.Tasks != nil {
		if err := oprot.WriteFieldBegin("tasks", thrift.LIST, 26); err != nil {
			return fmt.Errorf("%T write field begin error 26:tasks: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tasks {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 26:tasks: %s", p, err)
		}
	}
	return err
}

func (p *AowAdCreative) writeField27(oprot thrift.TProtocol) (err error) {
	if p.Prices != nil {
		if err := oprot.WriteFieldBegin("prices", thrift.MAP, 27); err != nil {
			return fmt.Errorf("%T write field begin error 27:prices: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I64, len(p.Prices)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Prices {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 27:prices: %s", p, err)
		}
	}
	return err
}

func (p *AowAdCreative) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo_url", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:logo_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LogoUrl)); err != nil {
		return fmt.Errorf("%T.logo_url (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:logo_url: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("simple_description", thrift.STRING, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:simple_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SimpleDescription)); err != nil {
		return fmt.Errorf("%T.simple_description (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:simple_description: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskPrice", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:taskPrice: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskPrice)); err != nil {
		return fmt.Errorf("%T.taskPrice (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:taskPrice: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField31(oprot thrift.TProtocol) (err error) {
	if p.Pkgids != nil {
		if err := oprot.WriteFieldBegin("pkgids", thrift.LIST, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:pkgids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Pkgids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Pkgids {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:pkgids: %s", p, err)
		}
	}
	return err
}

func (p *AowAdCreative) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("notice", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:notice: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Notice)); err != nil {
		return fmt.Errorf("%T.notice (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:notice: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("prompt", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:prompt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Prompt)); err != nil {
		return fmt.Errorf("%T.prompt (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:prompt: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_super_task", thrift.BOOL, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:is_super_task: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsSuperTask)); err != nil {
		return fmt.Errorf("%T.is_super_task (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:is_super_task: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:tag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tag)); err != nil {
		return fmt.Errorf("%T.tag (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:tag: %s", p, err)
	}
	return err
}

func (p *AowAdCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowAdCreative(%+v)", *p)
}

type AowPackage struct {
	Name                      string `thrift:"name,1" json:"name"`
	Version                   string `thrift:"version,2" json:"version"`
	Appid                     string `thrift:"appid,3" json:"appid"`
	Size                      int64  `thrift:"size,4" json:"size"`
	ActivationSponsorCallback bool   `thrift:"activation_sponsor_callback,5" json:"activation_sponsor_callback"`
	AccountingType            int32  `thrift:"accounting_type,6" json:"accounting_type"`
}

func NewAowPackage() *AowPackage {
	return &AowPackage{}
}

func (p *AowPackage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowPackage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AowPackage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *AowPackage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowPackage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *AowPackage) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ActivationSponsorCallback = v
	}
	return nil
}

func (p *AowPackage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AccountingType = v
	}
	return nil
}

func (p *AowPackage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowPackage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowPackage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *AowPackage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:version: %s", p, err)
	}
	return err
}

func (p *AowPackage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:appid: %s", p, err)
	}
	return err
}

func (p *AowPackage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *AowPackage) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("activation_sponsor_callback", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:activation_sponsor_callback: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ActivationSponsorCallback)); err != nil {
		return fmt.Errorf("%T.activation_sponsor_callback (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:activation_sponsor_callback: %s", p, err)
	}
	return err
}

func (p *AowPackage) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("accounting_type", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:accounting_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccountingType)); err != nil {
		return fmt.Errorf("%T.accounting_type (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:accounting_type: %s", p, err)
	}
	return err
}

func (p *AowPackage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowPackage(%+v)", *p)
}

type AowOffer struct {
	Offer    *aow_ui_types.AowBriefOffer `thrift:"offer,1" json:"offer"`
	Plan     *AowAdPlan                  `thrift:"plan,2" json:"plan"`
	Creative *AowAdCreative              `thrift:"creative,3" json:"creative"`
	Pkg      *AowPackage                 `thrift:"pkg,4" json:"pkg"`
}

func NewAowOffer() *AowOffer {
	return &AowOffer{}
}

func (p *AowOffer) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowOffer) readField1(iprot thrift.TProtocol) error {
	p.Offer = aow_ui_types.NewAowBriefOffer()
	if err := p.Offer.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Offer)
	}
	return nil
}

func (p *AowOffer) readField2(iprot thrift.TProtocol) error {
	p.Plan = NewAowAdPlan()
	if err := p.Plan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Plan)
	}
	return nil
}

func (p *AowOffer) readField3(iprot thrift.TProtocol) error {
	p.Creative = NewAowAdCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *AowOffer) readField4(iprot thrift.TProtocol) error {
	p.Pkg = NewAowPackage()
	if err := p.Pkg.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Pkg)
	}
	return nil
}

func (p *AowOffer) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowOffer"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowOffer) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Offer != nil {
		if err := oprot.WriteFieldBegin("offer", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:offer: %s", p, err)
		}
		if err := p.Offer.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Offer)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:offer: %s", p, err)
		}
	}
	return err
}

func (p *AowOffer) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Plan != nil {
		if err := oprot.WriteFieldBegin("plan", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:plan: %s", p, err)
		}
		if err := p.Plan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Plan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:plan: %s", p, err)
		}
	}
	return err
}

func (p *AowOffer) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:creative: %s", p, err)
		}
	}
	return err
}

func (p *AowOffer) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Pkg != nil {
		if err := oprot.WriteFieldBegin("pkg", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:pkg: %s", p, err)
		}
		if err := p.Pkg.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Pkg)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:pkg: %s", p, err)
		}
	}
	return err
}

func (p *AowOffer) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowOffer(%+v)", *p)
}

type OfferResponse struct {
	Offers   []*AowOffer `thrift:"offers,1" json:"offers"`
	Offerids []int32     `thrift:"offerids,2" json:"offerids"`
	TypeA1   Emptype     `thrift:"type,3" json:"type"`
}

func NewOfferResponse() *OfferResponse {
	return &OfferResponse{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *OfferResponse) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *OfferResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *OfferResponse) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Offers = make([]*AowOffer, 0, size)
	for i := 0; i < size; i++ {
		_elem4 := NewAowOffer()
		if err := _elem4.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem4)
		}
		p.Offers = append(p.Offers, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OfferResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Offerids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem5 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem5 = v
		}
		p.Offerids = append(p.Offerids, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *OfferResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = Emptype(v)
	}
	return nil
}

func (p *OfferResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("OfferResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *OfferResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Offers != nil {
		if err := oprot.WriteFieldBegin("offers", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:offers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Offers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Offers {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:offers: %s", p, err)
		}
	}
	return err
}

func (p *OfferResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Offerids != nil {
		if err := oprot.WriteFieldBegin("offerids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:offerids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Offerids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Offerids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:offerids: %s", p, err)
		}
	}
	return err
}

func (p *OfferResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *OfferResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("OfferResponse(%+v)", *p)
}

type BonusResponse struct {
	Bonus  *aow_ui_types.AowAccumulatedBonus `thrift:"bonus,1" json:"bonus"`
	Offers []*AowOffer                       `thrift:"offers,2" json:"offers"`
}

func NewBonusResponse() *BonusResponse {
	return &BonusResponse{}
}

func (p *BonusResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BonusResponse) readField1(iprot thrift.TProtocol) error {
	p.Bonus = aow_ui_types.NewAowAccumulatedBonus()
	if err := p.Bonus.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Bonus)
	}
	return nil
}

func (p *BonusResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Offers = make([]*AowOffer, 0, size)
	for i := 0; i < size; i++ {
		_elem6 := NewAowOffer()
		if err := _elem6.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem6)
		}
		p.Offers = append(p.Offers, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *BonusResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BonusResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BonusResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Bonus != nil {
		if err := oprot.WriteFieldBegin("bonus", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:bonus: %s", p, err)
		}
		if err := p.Bonus.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Bonus)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:bonus: %s", p, err)
		}
	}
	return err
}

func (p *BonusResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Offers != nil {
		if err := oprot.WriteFieldBegin("offers", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:offers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Offers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Offers {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:offers: %s", p, err)
		}
	}
	return err
}

func (p *BonusResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BonusResponse(%+v)", *p)
}

type PointResponse struct {
	TotalPoint      int64            `thrift:"totalPoint,1" json:"totalPoint"`
	ConsumedPoint   int64            `thrift:"consumedPoint,2" json:"consumedPoint"`
	ConsumeStatus   AowConsumeStatus `thrift:"consumeStatus,3" json:"consumeStatus"`
	ErrMessage      string           `thrift:"err_message,4" json:"err_message"`
	DisTotalPoint   float64          `thrift:"dis_totalPoint,5" json:"dis_totalPoint"`
	DisConsumePoint float64          `thrift:"dis_consumePoint,6" json:"dis_consumePoint"`
}

func NewPointResponse() *PointResponse {
	return &PointResponse{
		ConsumeStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PointResponse) IsSetConsumeStatus() bool {
	return int64(p.ConsumeStatus) != math.MinInt32-1
}

func (p *PointResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PointResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalPoint = v
	}
	return nil
}

func (p *PointResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ConsumedPoint = v
	}
	return nil
}

func (p *PointResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ConsumeStatus = AowConsumeStatus(v)
	}
	return nil
}

func (p *PointResponse) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ErrMessage = v
	}
	return nil
}

func (p *PointResponse) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DisTotalPoint = v
	}
	return nil
}

func (p *PointResponse) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DisConsumePoint = v
	}
	return nil
}

func (p *PointResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PointResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PointResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalPoint", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalPoint: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalPoint)); err != nil {
		return fmt.Errorf("%T.totalPoint (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalPoint: %s", p, err)
	}
	return err
}

func (p *PointResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumedPoint", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:consumedPoint: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConsumedPoint)); err != nil {
		return fmt.Errorf("%T.consumedPoint (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:consumedPoint: %s", p, err)
	}
	return err
}

func (p *PointResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetConsumeStatus() {
		if err := oprot.WriteFieldBegin("consumeStatus", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:consumeStatus: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ConsumeStatus)); err != nil {
			return fmt.Errorf("%T.consumeStatus (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:consumeStatus: %s", p, err)
		}
	}
	return err
}

func (p *PointResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("err_message", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:err_message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ErrMessage)); err != nil {
		return fmt.Errorf("%T.err_message (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:err_message: %s", p, err)
	}
	return err
}

func (p *PointResponse) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dis_totalPoint", thrift.DOUBLE, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:dis_totalPoint: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.DisTotalPoint)); err != nil {
		return fmt.Errorf("%T.dis_totalPoint (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:dis_totalPoint: %s", p, err)
	}
	return err
}

func (p *PointResponse) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dis_consumePoint", thrift.DOUBLE, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:dis_consumePoint: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.DisConsumePoint)); err != nil {
		return fmt.Errorf("%T.dis_consumePoint (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:dis_consumePoint: %s", p, err)
	}
	return err
}

func (p *PointResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PointResponse(%+v)", *p)
}

type AowMediaInfo struct {
	Mid    int32  `thrift:"mid,1" json:"mid"`
	Uid    int32  `thrift:"uid,2" json:"uid"`
	Name   string `thrift:"name,3" json:"name"`
	Status int16  `thrift:"status,4" json:"status"`
	Paused int16  `thrift:"paused,5" json:"paused"`
	// unused field # 6
	CurrencyName   string `thrift:"currency_name,7" json:"currency_name"`
	ExchangeRate   int32  `thrift:"exchange_rate,8" json:"exchange_rate"`
	MediashareRate int32  `thrift:"mediashare_rate,9" json:"mediashare_rate"`
	Sr             int16  `thrift:"sr,10" json:"sr"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	PackageUgcId int32  `thrift:"package_ugc_id,14" json:"package_ugc_id"`
	PackageName  string `thrift:"package_name,15" json:"package_name"`
	// unused field # 16
	MediaTagGroup []int32 `thrift:"mediaTagGroup,17" json:"mediaTagGroup"`
	// unused field # 18
	IsLocation int16 `thrift:"is_location,19" json:"is_location"`
	// unused field # 20
	// unused field # 21
	AdFilter       string `thrift:"ad_filter,22" json:"ad_filter"`
	AdFilterStatus bool   `thrift:"ad_filter_status,23" json:"ad_filter_status"`
	ScoreBalance   bool   `thrift:"score_balance,24" json:"score_balance"`
	MediaType      int16  `thrift:"media_type,25" json:"media_type"`
	// unused field # 26
	// unused field # 27
	ActProportion      int16 `thrift:"act_proportion,28" json:"act_proportion"`
	DecimalNum         int16 `thrift:"decimal_num,29" json:"decimal_num"`
	TaskMediashareRate int16 `thrift:"task_mediashare_rate,30" json:"task_mediashare_rate"`
}

func NewAowMediaInfo() *AowMediaInfo {
	return &AowMediaInfo{}
}

func (p *AowMediaInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I16 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I16 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I16 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I16 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I16 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I16 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I16 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowMediaInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *AowMediaInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AowMediaInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AowMediaInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AowMediaInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Paused = v
	}
	return nil
}

func (p *AowMediaInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CurrencyName = v
	}
	return nil
}

func (p *AowMediaInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ExchangeRate = v
	}
	return nil
}

func (p *AowMediaInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.MediashareRate = v
	}
	return nil
}

func (p *AowMediaInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *AowMediaInfo) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PackageUgcId = v
	}
	return nil
}

func (p *AowMediaInfo) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AowMediaInfo) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaTagGroup = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.MediaTagGroup = append(p.MediaTagGroup, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AowMediaInfo) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.IsLocation = v
	}
	return nil
}

func (p *AowMediaInfo) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AdFilter = v
	}
	return nil
}

func (p *AowMediaInfo) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.AdFilterStatus = v
	}
	return nil
}

func (p *AowMediaInfo) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.ScoreBalance = v
	}
	return nil
}

func (p *AowMediaInfo) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.MediaType = v
	}
	return nil
}

func (p *AowMediaInfo) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.ActProportion = v
	}
	return nil
}

func (p *AowMediaInfo) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.DecimalNum = v
	}
	return nil
}

func (p *AowMediaInfo) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.TaskMediashareRate = v
	}
	return nil
}

func (p *AowMediaInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowMediaInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowMediaInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:mid: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I16, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Status)); err != nil {
		return fmt.Errorf("%T.status (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:status: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paused", thrift.I16, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:paused: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Paused)); err != nil {
		return fmt.Errorf("%T.paused (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:paused: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currency_name", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:currency_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CurrencyName)); err != nil {
		return fmt.Errorf("%T.currency_name (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:currency_name: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_rate", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:exchange_rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeRate)); err != nil {
		return fmt.Errorf("%T.exchange_rate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:exchange_rate: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare_rate", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:mediashare_rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediashareRate)); err != nil {
		return fmt.Errorf("%T.mediashare_rate (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:mediashare_rate: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I16, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:sr: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:sr: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_ugc_id", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:package_ugc_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PackageUgcId)); err != nil {
		return fmt.Errorf("%T.package_ugc_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:package_ugc_id: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:package_name: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField17(oprot thrift.TProtocol) (err error) {
	if p.MediaTagGroup != nil {
		if err := oprot.WriteFieldBegin("mediaTagGroup", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:mediaTagGroup: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaTagGroup)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaTagGroup {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:mediaTagGroup: %s", p, err)
		}
	}
	return err
}

func (p *AowMediaInfo) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_location", thrift.I16, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:is_location: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.IsLocation)); err != nil {
		return fmt.Errorf("%T.is_location (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:is_location: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_filter", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:ad_filter: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdFilter)); err != nil {
		return fmt.Errorf("%T.ad_filter (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:ad_filter: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_filter_status", thrift.BOOL, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:ad_filter_status: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdFilterStatus)); err != nil {
		return fmt.Errorf("%T.ad_filter_status (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:ad_filter_status: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("score_balance", thrift.BOOL, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:score_balance: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ScoreBalance)); err != nil {
		return fmt.Errorf("%T.score_balance (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:score_balance: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_type", thrift.I16, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:media_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.MediaType)); err != nil {
		return fmt.Errorf("%T.media_type (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:media_type: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_proportion", thrift.I16, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:act_proportion: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.ActProportion)); err != nil {
		return fmt.Errorf("%T.act_proportion (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:act_proportion: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("decimal_num", thrift.I16, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:decimal_num: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.DecimalNum)); err != nil {
		return fmt.Errorf("%T.decimal_num (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:decimal_num: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_mediashare_rate", thrift.I16, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:task_mediashare_rate: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.TaskMediashareRate)); err != nil {
		return fmt.Errorf("%T.task_mediashare_rate (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:task_mediashare_rate: %s", p, err)
	}
	return err
}

func (p *AowMediaInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowMediaInfo(%+v)", *p)
}

type AowExtraBonus struct {
	Id               int32  `thrift:"id,1" json:"id"`
	Name             string `thrift:"name,2" json:"name"`
	MediaTags        string `thrift:"media_tags,3" json:"media_tags"`
	StartTime        int32  `thrift:"start_time,4" json:"start_time"`
	EndTime          int32  `thrift:"end_time,5" json:"end_time"`
	BonusType        int32  `thrift:"bonus_type,6" json:"bonus_type"`
	TaskDescription  string `thrift:"task_description,7" json:"task_description"`
	BonusDescription string `thrift:"bonus_description,8" json:"bonus_description"`
	Bonus            int32  `thrift:"bonus,9" json:"bonus"`
	Url              string `thrift:"url,10" json:"url"`
	ActNum           int32  `thrift:"act_num,11" json:"act_num"`
	ReopenNum        int32  `thrift:"reopen_num,12" json:"reopen_num"`
}

func NewAowExtraBonus() *AowExtraBonus {
	return &AowExtraBonus{}
}

func (p *AowExtraBonus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowExtraBonus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AowExtraBonus) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AowExtraBonus) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaTags = v
	}
	return nil
}

func (p *AowExtraBonus) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *AowExtraBonus) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *AowExtraBonus) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.BonusType = v
	}
	return nil
}

func (p *AowExtraBonus) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TaskDescription = v
	}
	return nil
}

func (p *AowExtraBonus) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.BonusDescription = v
	}
	return nil
}

func (p *AowExtraBonus) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Bonus = v
	}
	return nil
}

func (p *AowExtraBonus) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *AowExtraBonus) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ActNum = v
	}
	return nil
}

func (p *AowExtraBonus) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ReopenNum = v
	}
	return nil
}

func (p *AowExtraBonus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowExtraBonus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowExtraBonus) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_tags", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:media_tags: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaTags)); err != nil {
		return fmt.Errorf("%T.media_tags (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:media_tags: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:start_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:start_time: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:end_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:end_time: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus_type", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:bonus_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BonusType)); err != nil {
		return fmt.Errorf("%T.bonus_type (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:bonus_type: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_description", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:task_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskDescription)); err != nil {
		return fmt.Errorf("%T.task_description (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:task_description: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus_description", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:bonus_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BonusDescription)); err != nil {
		return fmt.Errorf("%T.bonus_description (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:bonus_description: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:bonus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Bonus)); err != nil {
		return fmt.Errorf("%T.bonus (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:bonus: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:url: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_num", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:act_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActNum)); err != nil {
		return fmt.Errorf("%T.act_num (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:act_num: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reopen_num", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:reopen_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReopenNum)); err != nil {
		return fmt.Errorf("%T.reopen_num (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:reopen_num: %s", p, err)
	}
	return err
}

func (p *AowExtraBonus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowExtraBonus(%+v)", *p)
}

type AowLiuliangInn struct {
	Id               int32  `thrift:"id,1" json:"id"`
	Name             string `thrift:"name,2" json:"name"`
	MediaTagWhite    string `thrift:"media_tag_white,3" json:"media_tag_white"`
	MediaTagBlack    string `thrift:"media_tag_black,4" json:"media_tag_black"`
	Cids             string `thrift:"cids,5" json:"cids"`
	StartTime        int32  `thrift:"start_time,6" json:"start_time"`
	EndTime          int32  `thrift:"end_time,7" json:"end_time"`
	TaskType         int32  `thrift:"task_type,8" json:"task_type"`
	TaskCycle        int32  `thrift:"task_cycle,9" json:"task_cycle"`
	TaskDescription  string `thrift:"task_description,10" json:"task_description"`
	BonusDescription string `thrift:"bonus_description,11" json:"bonus_description"`
	Bonus            int32  `thrift:"bonus,12" json:"bonus"`
	ActNum           int32  `thrift:"act_num,13" json:"act_num"`
	ReopenNum        int32  `thrift:"reopen_num,14" json:"reopen_num"`
	CreateTime       int32  `thrift:"create_time,15" json:"create_time"`
}

func NewAowLiuliangInn() *AowLiuliangInn {
	return &AowLiuliangInn{}
}

func (p *AowLiuliangInn) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowLiuliangInn) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AowLiuliangInn) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AowLiuliangInn) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MediaTagWhite = v
	}
	return nil
}

func (p *AowLiuliangInn) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MediaTagBlack = v
	}
	return nil
}

func (p *AowLiuliangInn) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Cids = v
	}
	return nil
}

func (p *AowLiuliangInn) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *AowLiuliangInn) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *AowLiuliangInn) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.TaskType = v
	}
	return nil
}

func (p *AowLiuliangInn) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.TaskCycle = v
	}
	return nil
}

func (p *AowLiuliangInn) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TaskDescription = v
	}
	return nil
}

func (p *AowLiuliangInn) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.BonusDescription = v
	}
	return nil
}

func (p *AowLiuliangInn) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Bonus = v
	}
	return nil
}

func (p *AowLiuliangInn) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ActNum = v
	}
	return nil
}

func (p *AowLiuliangInn) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.ReopenNum = v
	}
	return nil
}

func (p *AowLiuliangInn) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *AowLiuliangInn) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowLiuliangInn"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowLiuliangInn) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_tag_white", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:media_tag_white: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaTagWhite)); err != nil {
		return fmt.Errorf("%T.media_tag_white (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:media_tag_white: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_tag_black", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:media_tag_black: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaTagBlack)); err != nil {
		return fmt.Errorf("%T.media_tag_black (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:media_tag_black: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cids", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:cids: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cids)); err != nil {
		return fmt.Errorf("%T.cids (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:cids: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:start_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:start_time: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:end_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:end_time: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_type", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:task_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskType)); err != nil {
		return fmt.Errorf("%T.task_type (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:task_type: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_cycle", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:task_cycle: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskCycle)); err != nil {
		return fmt.Errorf("%T.task_cycle (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:task_cycle: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_description", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:task_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskDescription)); err != nil {
		return fmt.Errorf("%T.task_description (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:task_description: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus_description", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:bonus_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BonusDescription)); err != nil {
		return fmt.Errorf("%T.bonus_description (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:bonus_description: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:bonus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Bonus)); err != nil {
		return fmt.Errorf("%T.bonus (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:bonus: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_num", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:act_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActNum)); err != nil {
		return fmt.Errorf("%T.act_num (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:act_num: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reopen_num", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:reopen_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReopenNum)); err != nil {
		return fmt.Errorf("%T.reopen_num (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:reopen_num: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:create_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:create_time: %s", p, err)
	}
	return err
}

func (p *AowLiuliangInn) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowLiuliangInn(%+v)", *p)
}
