// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package zeus_user_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

type UserRole int64

const (
	UserRole_UR_D3_ADMIN            UserRole = 1
	UserRole_UR_D3_ANALYST          UserRole = 2
	UserRole_UR_COMPANY_ADMIN       UserRole = 11
	UserRole_UR_COMPANY_ANALYST     UserRole = 12
	UserRole_UR_COMPANY_NORMAL_USER UserRole = 13
	UserRole_UR_DOMOB_ADVERTISER    UserRole = 21
	UserRole_UR_DOMOB_DESIGNER      UserRole = 22
)

func (p UserRole) String() string {
	switch p {
	case UserRole_UR_D3_ADMIN:
		return "UserRole_UR_D3_ADMIN"
	case UserRole_UR_D3_ANALYST:
		return "UserRole_UR_D3_ANALYST"
	case UserRole_UR_COMPANY_ADMIN:
		return "UserRole_UR_COMPANY_ADMIN"
	case UserRole_UR_COMPANY_ANALYST:
		return "UserRole_UR_COMPANY_ANALYST"
	case UserRole_UR_COMPANY_NORMAL_USER:
		return "UserRole_UR_COMPANY_NORMAL_USER"
	case UserRole_UR_DOMOB_ADVERTISER:
		return "UserRole_UR_DOMOB_ADVERTISER"
	case UserRole_UR_DOMOB_DESIGNER:
		return "UserRole_UR_DOMOB_DESIGNER"
	}
	return "<UNSET>"
}

func UserRoleFromString(s string) (UserRole, error) {
	switch s {
	case "UserRole_UR_D3_ADMIN":
		return UserRole_UR_D3_ADMIN, nil
	case "UserRole_UR_D3_ANALYST":
		return UserRole_UR_D3_ANALYST, nil
	case "UserRole_UR_COMPANY_ADMIN":
		return UserRole_UR_COMPANY_ADMIN, nil
	case "UserRole_UR_COMPANY_ANALYST":
		return UserRole_UR_COMPANY_ANALYST, nil
	case "UserRole_UR_COMPANY_NORMAL_USER":
		return UserRole_UR_COMPANY_NORMAL_USER, nil
	case "UserRole_UR_DOMOB_ADVERTISER":
		return UserRole_UR_DOMOB_ADVERTISER, nil
	case "UserRole_UR_DOMOB_DESIGNER":
		return UserRole_UR_DOMOB_DESIGNER, nil
	}
	return UserRole(math.MinInt32 - 1), fmt.Errorf("not a valid UserRole string")
}

type CompanyChargeType int64

const (
	CompanyChargeType_FREE          CompanyChargeType = 1
	CompanyChargeType_NORMAL_CHARGE CompanyChargeType = 2
)

func (p CompanyChargeType) String() string {
	switch p {
	case CompanyChargeType_FREE:
		return "CompanyChargeType_FREE"
	case CompanyChargeType_NORMAL_CHARGE:
		return "CompanyChargeType_NORMAL_CHARGE"
	}
	return "<UNSET>"
}

func CompanyChargeTypeFromString(s string) (CompanyChargeType, error) {
	switch s {
	case "CompanyChargeType_FREE":
		return CompanyChargeType_FREE, nil
	case "CompanyChargeType_NORMAL_CHARGE":
		return CompanyChargeType_NORMAL_CHARGE, nil
	}
	return CompanyChargeType(math.MinInt32 - 1), fmt.Errorf("not a valid CompanyChargeType string")
}

type CompanyType int64

const (
	CompanyType_AGENCY            CompanyType = 1
	CompanyType_SOFTWARE          CompanyType = 2
	CompanyType_ECOMMERCE         CompanyType = 3
	CompanyType_TRADITIONAL_BRAND CompanyType = 4
)

func (p CompanyType) String() string {
	switch p {
	case CompanyType_AGENCY:
		return "CompanyType_AGENCY"
	case CompanyType_SOFTWARE:
		return "CompanyType_SOFTWARE"
	case CompanyType_ECOMMERCE:
		return "CompanyType_ECOMMERCE"
	case CompanyType_TRADITIONAL_BRAND:
		return "CompanyType_TRADITIONAL_BRAND"
	}
	return "<UNSET>"
}

func CompanyTypeFromString(s string) (CompanyType, error) {
	switch s {
	case "CompanyType_AGENCY":
		return CompanyType_AGENCY, nil
	case "CompanyType_SOFTWARE":
		return CompanyType_SOFTWARE, nil
	case "CompanyType_ECOMMERCE":
		return CompanyType_ECOMMERCE, nil
	case "CompanyType_TRADITIONAL_BRAND":
		return CompanyType_TRADITIONAL_BRAND, nil
	}
	return CompanyType(math.MinInt32 - 1), fmt.Errorf("not a valid CompanyType string")
}

type CompanyRole int64

const (
	CompanyRole_AGENCY     CompanyRole = 1
	CompanyRole_ADVERTISER CompanyRole = 2
)

func (p CompanyRole) String() string {
	switch p {
	case CompanyRole_AGENCY:
		return "CompanyRole_AGENCY"
	case CompanyRole_ADVERTISER:
		return "CompanyRole_ADVERTISER"
	}
	return "<UNSET>"
}

func CompanyRoleFromString(s string) (CompanyRole, error) {
	switch s {
	case "CompanyRole_AGENCY":
		return CompanyRole_AGENCY, nil
	case "CompanyRole_ADVERTISER":
		return CompanyRole_ADVERTISER, nil
	}
	return CompanyRole(math.MinInt32 - 1), fmt.Errorf("not a valid CompanyRole string")
}

type ZeusUserStatus int64

const (
	ZeusUserStatus_ZUS_DELETED   ZeusUserStatus = 0
	ZeusUserStatus_ZUS_CONNECTED ZeusUserStatus = 1
)

func (p ZeusUserStatus) String() string {
	switch p {
	case ZeusUserStatus_ZUS_DELETED:
		return "ZeusUserStatus_ZUS_DELETED"
	case ZeusUserStatus_ZUS_CONNECTED:
		return "ZeusUserStatus_ZUS_CONNECTED"
	}
	return "<UNSET>"
}

func ZeusUserStatusFromString(s string) (ZeusUserStatus, error) {
	switch s {
	case "ZeusUserStatus_ZUS_DELETED":
		return ZeusUserStatus_ZUS_DELETED, nil
	case "ZeusUserStatus_ZUS_CONNECTED":
		return ZeusUserStatus_ZUS_CONNECTED, nil
	}
	return ZeusUserStatus(math.MinInt32 - 1), fmt.Errorf("not a valid ZeusUserStatus string")
}

type FbUserStatus int64

const (
	FbUserStatus_FUS_DELETED   FbUserStatus = 0
	FbUserStatus_FUS_CONNECTED FbUserStatus = 1
	FbUserStatus_FUS_EXPIERED  FbUserStatus = 2
)

func (p FbUserStatus) String() string {
	switch p {
	case FbUserStatus_FUS_DELETED:
		return "FbUserStatus_FUS_DELETED"
	case FbUserStatus_FUS_CONNECTED:
		return "FbUserStatus_FUS_CONNECTED"
	case FbUserStatus_FUS_EXPIERED:
		return "FbUserStatus_FUS_EXPIERED"
	}
	return "<UNSET>"
}

func FbUserStatusFromString(s string) (FbUserStatus, error) {
	switch s {
	case "FbUserStatus_FUS_DELETED":
		return FbUserStatus_FUS_DELETED, nil
	case "FbUserStatus_FUS_CONNECTED":
		return FbUserStatus_FUS_CONNECTED, nil
	case "FbUserStatus_FUS_EXPIERED":
		return FbUserStatus_FUS_EXPIERED, nil
	}
	return FbUserStatus(math.MinInt32 - 1), fmt.Errorf("not a valid FbUserStatus string")
}

type FbAccountStatus int64

const (
	FbAccountStatus_FACC_DELETED   FbAccountStatus = 0
	FbAccountStatus_FACC_CONNECTED FbAccountStatus = 1
	FbAccountStatus_FACC_CONCEAL   FbAccountStatus = 2
)

func (p FbAccountStatus) String() string {
	switch p {
	case FbAccountStatus_FACC_DELETED:
		return "FbAccountStatus_FACC_DELETED"
	case FbAccountStatus_FACC_CONNECTED:
		return "FbAccountStatus_FACC_CONNECTED"
	case FbAccountStatus_FACC_CONCEAL:
		return "FbAccountStatus_FACC_CONCEAL"
	}
	return "<UNSET>"
}

func FbAccountStatusFromString(s string) (FbAccountStatus, error) {
	switch s {
	case "FbAccountStatus_FACC_DELETED":
		return FbAccountStatus_FACC_DELETED, nil
	case "FbAccountStatus_FACC_CONNECTED":
		return FbAccountStatus_FACC_CONNECTED, nil
	case "FbAccountStatus_FACC_CONCEAL":
		return FbAccountStatus_FACC_CONCEAL, nil
	}
	return FbAccountStatus(math.MinInt32 - 1), fmt.Errorf("not a valid FbAccountStatus string")
}

type FbAccountRole int64

const (
	FbAccountRole_FAR_UNKNOWN             FbAccountRole = 0
	FbAccountRole_FAR_ADMIN               FbAccountRole = 1
	FbAccountRole_FAR_ANALYST             FbAccountRole = 2
	FbAccountRole_FAR_ADVERTISER          FbAccountRole = 3
	FbAccountRole_FAR_ANALYST_BUSINESS    FbAccountRole = 4
	FbAccountRole_FAR_ADVERTISER_BUSINESS FbAccountRole = 5
	FbAccountRole_FAR_ADMIN_BUSINESS      FbAccountRole = 6
)

func (p FbAccountRole) String() string {
	switch p {
	case FbAccountRole_FAR_UNKNOWN:
		return "FbAccountRole_FAR_UNKNOWN"
	case FbAccountRole_FAR_ADMIN:
		return "FbAccountRole_FAR_ADMIN"
	case FbAccountRole_FAR_ANALYST:
		return "FbAccountRole_FAR_ANALYST"
	case FbAccountRole_FAR_ADVERTISER:
		return "FbAccountRole_FAR_ADVERTISER"
	case FbAccountRole_FAR_ANALYST_BUSINESS:
		return "FbAccountRole_FAR_ANALYST_BUSINESS"
	case FbAccountRole_FAR_ADVERTISER_BUSINESS:
		return "FbAccountRole_FAR_ADVERTISER_BUSINESS"
	case FbAccountRole_FAR_ADMIN_BUSINESS:
		return "FbAccountRole_FAR_ADMIN_BUSINESS"
	}
	return "<UNSET>"
}

func FbAccountRoleFromString(s string) (FbAccountRole, error) {
	switch s {
	case "FbAccountRole_FAR_UNKNOWN":
		return FbAccountRole_FAR_UNKNOWN, nil
	case "FbAccountRole_FAR_ADMIN":
		return FbAccountRole_FAR_ADMIN, nil
	case "FbAccountRole_FAR_ANALYST":
		return FbAccountRole_FAR_ANALYST, nil
	case "FbAccountRole_FAR_ADVERTISER":
		return FbAccountRole_FAR_ADVERTISER, nil
	case "FbAccountRole_FAR_ANALYST_BUSINESS":
		return FbAccountRole_FAR_ANALYST_BUSINESS, nil
	case "FbAccountRole_FAR_ADVERTISER_BUSINESS":
		return FbAccountRole_FAR_ADVERTISER_BUSINESS, nil
	case "FbAccountRole_FAR_ADMIN_BUSINESS":
		return FbAccountRole_FAR_ADMIN_BUSINESS, nil
	}
	return FbAccountRole(math.MinInt32 - 1), fmt.Errorf("not a valid FbAccountRole string")
}

type ZeusUser struct {
	Id                 int64    `thrift:"id,1" json:"id"`
	Username           string   `thrift:"username,2" json:"username"`
	AuthKey            string   `thrift:"auth_key,3" json:"auth_key"`
	PasswordHash       string   `thrift:"password_hash,4" json:"password_hash"`
	PasswordResetToken string   `thrift:"password_reset_token,5" json:"password_reset_token"`
	Email              string   `thrift:"email,6" json:"email"`
	Role               UserRole `thrift:"role,7" json:"role"`
	CompanyId          int64    `thrift:"company_id,8" json:"company_id"`
}

func NewZeusUser() *ZeusUser {
	return &ZeusUser{
		Role: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ZeusUser) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *ZeusUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ZeusUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *ZeusUser) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AuthKey = v
	}
	return nil
}

func (p *ZeusUser) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PasswordHash = v
	}
	return nil
}

func (p *ZeusUser) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PasswordResetToken = v
	}
	return nil
}

func (p *ZeusUser) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Email = v
	}
	return nil
}

func (p *ZeusUser) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Role = UserRole(v)
	}
	return nil
}

func (p *ZeusUser) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.CompanyId = v
	}
	return nil
}

func (p *ZeusUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ZeusUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:username: %s", p, err)
	}
	return err
}

func (p *ZeusUser) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("auth_key", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:auth_key: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AuthKey)); err != nil {
		return fmt.Errorf("%T.auth_key (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:auth_key: %s", p, err)
	}
	return err
}

func (p *ZeusUser) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password_hash", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:password_hash: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PasswordHash)); err != nil {
		return fmt.Errorf("%T.password_hash (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:password_hash: %s", p, err)
	}
	return err
}

func (p *ZeusUser) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password_reset_token", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:password_reset_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PasswordResetToken)); err != nil {
		return fmt.Errorf("%T.password_reset_token (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:password_reset_token: %s", p, err)
	}
	return err
}

func (p *ZeusUser) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("email", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:email: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Email)); err != nil {
		return fmt.Errorf("%T.email (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:email: %s", p, err)
	}
	return err
}

func (p *ZeusUser) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetRole() {
		if err := oprot.WriteFieldBegin("role", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:role: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Role)); err != nil {
			return fmt.Errorf("%T.role (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:role: %s", p, err)
		}
	}
	return err
}

func (p *ZeusUser) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("company_id", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:company_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CompanyId)); err != nil {
		return fmt.Errorf("%T.company_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:company_id: %s", p, err)
	}
	return err
}

func (p *ZeusUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusUser(%+v)", *p)
}

type ZeusAccount struct {
	FbAccountId            int64           `thrift:"fb_account_id,1" json:"fb_account_id"`
	FbAccountName          string          `thrift:"fb_account_name,2" json:"fb_account_name"`
	Role                   FbAccountRole   `thrift:"role,3" json:"role"`
	TimezoneOffsetHoursUtc int16           `thrift:"timezone_offset_hours_utc,4" json:"timezone_offset_hours_utc"`
	Status                 FbAccountStatus `thrift:"status,5" json:"status"`
}

func NewZeusAccount() *ZeusAccount {
	return &ZeusAccount{
		Role: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ZeusAccount) IsSetRole() bool {
	return int64(p.Role) != math.MinInt32-1
}

func (p *ZeusAccount) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ZeusAccount) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I16 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusAccount) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.FbAccountId = v
	}
	return nil
}

func (p *ZeusAccount) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.FbAccountName = v
	}
	return nil
}

func (p *ZeusAccount) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Role = FbAccountRole(v)
	}
	return nil
}

func (p *ZeusAccount) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TimezoneOffsetHoursUtc = v
	}
	return nil
}

func (p *ZeusAccount) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Status = FbAccountStatus(v)
	}
	return nil
}

func (p *ZeusAccount) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusAccount"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusAccount) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_account_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:fb_account_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FbAccountId)); err != nil {
		return fmt.Errorf("%T.fb_account_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:fb_account_id: %s", p, err)
	}
	return err
}

func (p *ZeusAccount) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fb_account_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:fb_account_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FbAccountName)); err != nil {
		return fmt.Errorf("%T.fb_account_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:fb_account_name: %s", p, err)
	}
	return err
}

func (p *ZeusAccount) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetRole() {
		if err := oprot.WriteFieldBegin("role", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:role: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Role)); err != nil {
			return fmt.Errorf("%T.role (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:role: %s", p, err)
		}
	}
	return err
}

func (p *ZeusAccount) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timezone_offset_hours_utc", thrift.I16, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:timezone_offset_hours_utc: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.TimezoneOffsetHoursUtc)); err != nil {
		return fmt.Errorf("%T.timezone_offset_hours_utc (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:timezone_offset_hours_utc: %s", p, err)
	}
	return err
}

func (p *ZeusAccount) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:status: %s", p, err)
		}
	}
	return err
}

func (p *ZeusAccount) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusAccount(%+v)", *p)
}

type FbUser struct {
	Id                    int64        `thrift:"id,1" json:"id"`
	ZeusUserId            int64        `thrift:"zeus_user_id,2" json:"zeus_user_id"`
	Username              string       `thrift:"username,3" json:"username"`
	AccessToken           string       `thrift:"access_token,4" json:"access_token"`
	AccessTokenExpireTime int64        `thrift:"access_token_expire_time,5" json:"access_token_expire_time"`
	CreateTime            int64        `thrift:"create_time,6" json:"create_time"`
	UpdateTime            int64        `thrift:"update_time,7" json:"update_time"`
	Status                FbUserStatus `thrift:"status,8" json:"status"`
}

func NewFbUser() *FbUser {
	return &FbUser{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FbUser) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *FbUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FbUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *FbUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ZeusUserId = v
	}
	return nil
}

func (p *FbUser) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Username = v
	}
	return nil
}

func (p *FbUser) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AccessToken = v
	}
	return nil
}

func (p *FbUser) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AccessTokenExpireTime = v
	}
	return nil
}

func (p *FbUser) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *FbUser) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.UpdateTime = v
	}
	return nil
}

func (p *FbUser) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Status = FbUserStatus(v)
	}
	return nil
}

func (p *FbUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FbUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FbUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *FbUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_user_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:zeus_user_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusUserId)); err != nil {
		return fmt.Errorf("%T.zeus_user_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:zeus_user_id: %s", p, err)
	}
	return err
}

func (p *FbUser) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("username", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:username: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Username)); err != nil {
		return fmt.Errorf("%T.username (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:username: %s", p, err)
	}
	return err
}

func (p *FbUser) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:access_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessToken)); err != nil {
		return fmt.Errorf("%T.access_token (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:access_token: %s", p, err)
	}
	return err
}

func (p *FbUser) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token_expire_time", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:access_token_expire_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccessTokenExpireTime)); err != nil {
		return fmt.Errorf("%T.access_token_expire_time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:access_token_expire_time: %s", p, err)
	}
	return err
}

func (p *FbUser) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:create_time: %s", p, err)
	}
	return err
}

func (p *FbUser) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("update_time", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:update_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.update_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:update_time: %s", p, err)
	}
	return err
}

func (p *FbUser) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:status: %s", p, err)
		}
	}
	return err
}

func (p *FbUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FbUser(%+v)", *p)
}

type ZeusFbUser struct {
	Id                    int64          `thrift:"id,1" json:"id"`
	ZeusUserId            int64          `thrift:"zeus_user_id,2" json:"zeus_user_id"`
	UserName              string         `thrift:"user_name,3" json:"user_name"`
	AccessToken           string         `thrift:"access_token,4" json:"access_token"`
	AccessTokenExpireTime int64          `thrift:"access_token_expire_time,5" json:"access_token_expire_time"`
	AccountList           []*ZeusAccount `thrift:"account_list,6" json:"account_list"`
	Status                FbUserStatus   `thrift:"status,7" json:"status"`
}

func NewZeusFbUser() *ZeusFbUser {
	return &ZeusFbUser{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ZeusFbUser) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *ZeusFbUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusFbUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *ZeusFbUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ZeusUserId = v
	}
	return nil
}

func (p *ZeusFbUser) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UserName = v
	}
	return nil
}

func (p *ZeusFbUser) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AccessToken = v
	}
	return nil
}

func (p *ZeusFbUser) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AccessTokenExpireTime = v
	}
	return nil
}

func (p *ZeusFbUser) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccountList = make([]*ZeusAccount, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewZeusAccount()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.AccountList = append(p.AccountList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ZeusFbUser) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Status = FbUserStatus(v)
	}
	return nil
}

func (p *ZeusFbUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusFbUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusFbUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *ZeusFbUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("zeus_user_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:zeus_user_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ZeusUserId)); err != nil {
		return fmt.Errorf("%T.zeus_user_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:zeus_user_id: %s", p, err)
	}
	return err
}

func (p *ZeusFbUser) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:user_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserName)); err != nil {
		return fmt.Errorf("%T.user_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:user_name: %s", p, err)
	}
	return err
}

func (p *ZeusFbUser) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:access_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AccessToken)); err != nil {
		return fmt.Errorf("%T.access_token (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:access_token: %s", p, err)
	}
	return err
}

func (p *ZeusFbUser) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_token_expire_time", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:access_token_expire_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AccessTokenExpireTime)); err != nil {
		return fmt.Errorf("%T.access_token_expire_time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:access_token_expire_time: %s", p, err)
	}
	return err
}

func (p *ZeusFbUser) writeField6(oprot thrift.TProtocol) (err error) {
	if p.AccountList != nil {
		if err := oprot.WriteFieldBegin("account_list", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:account_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AccountList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccountList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:account_list: %s", p, err)
		}
	}
	return err
}

func (p *ZeusFbUser) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:status: %s", p, err)
		}
	}
	return err
}

func (p *ZeusFbUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusFbUser(%+v)", *p)
}

type ZeusMultiReturn struct {
	SuccessReturn map[int32]int64 `thrift:"success_return,1" json:"success_return"`
	FailedIndex   []int32         `thrift:"failed_index,2" json:"failed_index"`
	NoFailed      bool            `thrift:"no_failed,3" json:"no_failed"`
}

func NewZeusMultiReturn() *ZeusMultiReturn {
	return &ZeusMultiReturn{}
}

func (p *ZeusMultiReturn) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ZeusMultiReturn) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.SuccessReturn = make(map[int32]int64, size)
	for i := 0; i < size; i++ {
		var _key1 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1 = v
		}
		var _val2 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val2 = v
		}
		p.SuccessReturn[_key1] = _val2
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ZeusMultiReturn) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.FailedIndex = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.FailedIndex = append(p.FailedIndex, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ZeusMultiReturn) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NoFailed = v
	}
	return nil
}

func (p *ZeusMultiReturn) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ZeusMultiReturn"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ZeusMultiReturn) writeField1(oprot thrift.TProtocol) (err error) {
	if p.SuccessReturn != nil {
		if err := oprot.WriteFieldBegin("success_return", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:success_return: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I64, len(p.SuccessReturn)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.SuccessReturn {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:success_return: %s", p, err)
		}
	}
	return err
}

func (p *ZeusMultiReturn) writeField2(oprot thrift.TProtocol) (err error) {
	if p.FailedIndex != nil {
		if err := oprot.WriteFieldBegin("failed_index", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:failed_index: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.FailedIndex)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.FailedIndex {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:failed_index: %s", p, err)
		}
	}
	return err
}

func (p *ZeusMultiReturn) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("no_failed", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:no_failed: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.NoFailed)); err != nil {
		return fmt.Errorf("%T.no_failed (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:no_failed: %s", p, err)
	}
	return err
}

func (p *ZeusMultiReturn) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ZeusMultiReturn(%+v)", *p)
}
