// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package aow_stat_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type AowEventReport struct {
	EventType string `thrift:"event_type,1" json:"event_type"`
	EventTime int64  `thrift:"event_time,2" json:"event_time"`
	Searchid  int64  `thrift:"searchid,3" json:"searchid"`
	Expid     int32  `thrift:"expid,4" json:"expid"`
	Sv        string `thrift:"sv,5" json:"sv"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Ipb        string `thrift:"ipb,10" json:"ipb"`
	PubDeverid int32  `thrift:"pub_deverid,11" json:"pub_deverid"`
	PubMediaid int32  `thrift:"pub_mediaid,12" json:"pub_mediaid"`
	// unused field # 13
	// unused field # 14
	SponsorUid  int32  `thrift:"sponsor_uid,15" json:"sponsor_uid"`
	Planid      int32  `thrift:"planid,16" json:"planid"`
	Cid         int32  `thrift:"cid,17" json:"cid"`
	Appid       string `thrift:"appid,18" json:"appid"`
	Sr          int32  `thrift:"sr,19" json:"sr"`
	Action      int32  `thrift:"action,20" json:"action"`
	OfferType   int32  `thrift:"offer_type,21" json:"offer_type"`
	CostType    int32  `thrift:"cost_type,22" json:"cost_type"`
	DisplayType int32  `thrift:"display_type,23" json:"display_type"`
	Rank        int32  `thrift:"rank,24" json:"rank"`
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Dmac       string                `thrift:"dmac,30" json:"dmac"`
	Imei       string                `thrift:"imei,31" json:"imei"`
	AndroidId  string                `thrift:"android_id,32" json:"android_id"`
	EntryType  int32                 `thrift:"entry_type,33" json:"entry_type"`
	DeviceCode int32                 `thrift:"device_code,34" json:"device_code"`
	AccessCode common.AccessTypeCode `thrift:"access_code,35" json:"access_code"`
	RegionCode int32                 `thrift:"region_code,36" json:"region_code"`
	OsCode     int32                 `thrift:"os_code,37" json:"os_code"`
	Ip         string                `thrift:"ip,38" json:"ip"`
}

func NewAowEventReport() *AowEventReport {
	return &AowEventReport{
		AccessCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AowEventReport) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *AowEventReport) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I32 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowEventReport) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.EventType = v
	}
	return nil
}

func (p *AowEventReport) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EventTime = v
	}
	return nil
}

func (p *AowEventReport) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AowEventReport) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Expid = v
	}
	return nil
}

func (p *AowEventReport) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *AowEventReport) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *AowEventReport) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *AowEventReport) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *AowEventReport) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.SponsorUid = v
	}
	return nil
}

func (p *AowEventReport) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *AowEventReport) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *AowEventReport) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowEventReport) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *AowEventReport) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *AowEventReport) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *AowEventReport) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *AowEventReport) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.DisplayType = v
	}
	return nil
}

func (p *AowEventReport) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *AowEventReport) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *AowEventReport) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AowEventReport) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *AowEventReport) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.EntryType = v
	}
	return nil
}

func (p *AowEventReport) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *AowEventReport) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.AccessCode = common.AccessTypeCode(v)
	}
	return nil
}

func (p *AowEventReport) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *AowEventReport) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *AowEventReport) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *AowEventReport) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowEventReport"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowEventReport) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_type", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:event_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EventType)); err != nil {
		return fmt.Errorf("%T.event_type (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:event_type: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:event_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EventTime)); err != nil {
		return fmt.Errorf("%T.event_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:event_time: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:searchid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Expid)); err != nil {
		return fmt.Errorf("%T.expid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:expid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sv: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:ipb: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:pub_deverid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_uid", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:sponsor_uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorUid)); err != nil {
		return fmt.Errorf("%T.sponsor_uid (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:sponsor_uid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:planid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:cid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:appid: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:sr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:sr: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:action: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:offer_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:offer_type: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:cost_type: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_type", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:display_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.display_type (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:display_type: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:rank: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:dmac: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:imei: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:android_id: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entry_type", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:entry_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EntryType)); err != nil {
		return fmt.Errorf("%T.entry_type (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:entry_type: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:device_code: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:access_code: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:region_code: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:os_code: %s", p, err)
	}
	return err
}

func (p *AowEventReport) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:ip: %s", p, err)
	}
	return err
}

func (p *AowEventReport) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowEventReport(%+v)", *p)
}

type AowRawAct struct {
	Actid       int64  `thrift:"actid,1" json:"actid"`
	Appid       string `thrift:"appid,2" json:"appid"`
	Macmd5      string `thrift:"macmd5,3" json:"macmd5"`
	Odin1       string `thrift:"odin1,4" json:"odin1"`
	Uuid        string `thrift:"uuid,5" json:"uuid"`
	Oid         string `thrift:"oid,6" json:"oid"`
	ActTime     int32  `thrift:"act_time,7" json:"act_time"`
	Status      int32  `thrift:"status,8" json:"status"`
	ProcTime    int32  `thrift:"proc_time,9" json:"proc_time"`
	Hostid      string `thrift:"hostid,10" json:"hostid"`
	Dt          int32  `thrift:"dt,11" json:"dt"`
	Hr          int32  `thrift:"hr,12" json:"hr"`
	Dmac        string `thrift:"dmac,13" json:"dmac"`
	Source      string `thrift:"source,14" json:"source"`
	Imei        string `thrift:"imei,15" json:"imei"`
	AndroidId   string `thrift:"android_id,16" json:"android_id"`
	Idfa        string `thrift:"idfa,17" json:"idfa"`
	Platform    int32  `thrift:"platform,18" json:"platform"`
	Action      int32  `thrift:"action,19" json:"action"`
	Searchid    int64  `thrift:"searchid,20" json:"searchid"`
	Sr          int16  `thrift:"sr,21" json:"sr"`
	Cid         int32  `thrift:"cid,22" json:"cid"`
	Planid      int32  `thrift:"planid,23" json:"planid"`
	Uid         int32  `thrift:"uid,24" json:"uid"`
	Price       int64  `thrift:"price,25" json:"price"`
	Mediashare  int64  `thrift:"mediashare,26" json:"mediashare"`
	Point       int64  `thrift:"point,27" json:"point"`
	AccessCode  int32  `thrift:"access_code,28" json:"access_code"`
	DeviceCode  int32  `thrift:"device_code,29" json:"device_code"`
	Pubid       string `thrift:"pubid,30" json:"pubid"`
	PubMediaid  int32  `thrift:"pub_mediaid,31" json:"pub_mediaid"`
	PubDeverid  int32  `thrift:"pub_deverid,32" json:"pub_deverid"`
	Userid      string `thrift:"userid,33" json:"userid"`
	Ip          string `thrift:"ip,34" json:"ip"`
	RegionCode  int32  `thrift:"region_code,35" json:"region_code"`
	SpPrice     int64  `thrift:"sp_price,36" json:"sp_price"`
	Rank        int32  `thrift:"rank,37" json:"rank"`
	DisplayType int32  `thrift:"display_type,38" json:"display_type"`
	EntryType   int32  `thrift:"entry_type,39" json:"entry_type"`
	Sv          int32  `thrift:"sv,40" json:"sv"`
	Isspam      bool   `thrift:"isspam,41" json:"isspam"`
	SpamType    string `thrift:"spam_type,42" json:"spam_type"`
	OfferType   int32  `thrift:"offer_type,43" json:"offer_type"`
	CostType    int32  `thrift:"cost_type,44" json:"cost_type"`
	AdCharge    bool   `thrift:"ad_charge,45" json:"ad_charge"`
	MediaCharge bool   `thrift:"media_charge,46" json:"media_charge"`
	OsCode      int32  `thrift:"os_code,47" json:"os_code"`
	Idfv        string `thrift:"idfv,48" json:"idfv"`
	AntiStatus  int16  `thrift:"anti_status,49" json:"anti_status"`
	Duid        string `thrift:"duid,50" json:"duid"`
	Dmid        string `thrift:"dmid,51" json:"dmid"`
	Amac        string `thrift:"amac,52" json:"amac"`
	Amn         string `thrift:"amn,53" json:"amn"`
	Latitude    string `thrift:"latitude,54" json:"latitude"`
	Longitude   string `thrift:"longitude,55" json:"longitude"`
	Oifa        string `thrift:"oifa,56" json:"oifa"`
	Aifa        string `thrift:"aifa,57" json:"aifa"`
	Extinfo     string `thrift:"extinfo,58" json:"extinfo"`
	ClkTime     int32  `thrift:"clk_time,59" json:"clk_time"`
	Clkid       int32  `thrift:"clkid,60" json:"clkid"`
	ClkIsspam   bool   `thrift:"clk_isspam,61" json:"clk_isspam"`
	ClkSpamType string `thrift:"clk_spam_type,62" json:"clk_spam_type"`
	PackageName string `thrift:"package_name,63" json:"package_name"`
	Cis         string `thrift:"cis,64" json:"cis"`
}

func NewAowRawAct() *AowRawAct {
	return &AowRawAct{}
}

func (p *AowRawAct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I16 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I32 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I64 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I32 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.I32 {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.I32 {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I32 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.STRING {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.I16 {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.STRING {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.STRING {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.STRING {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.STRING {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.STRING {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.STRING {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.STRING {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.I32 {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I32 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.STRING {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.STRING {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.STRING {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowRawAct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Actid = v
	}
	return nil
}

func (p *AowRawAct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowRawAct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *AowRawAct) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *AowRawAct) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *AowRawAct) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *AowRawAct) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ActTime = v
	}
	return nil
}

func (p *AowRawAct) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AowRawAct) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ProcTime = v
	}
	return nil
}

func (p *AowRawAct) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Hostid = v
	}
	return nil
}

func (p *AowRawAct) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *AowRawAct) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *AowRawAct) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *AowRawAct) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *AowRawAct) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AowRawAct) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *AowRawAct) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *AowRawAct) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *AowRawAct) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *AowRawAct) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AowRawAct) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *AowRawAct) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *AowRawAct) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *AowRawAct) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AowRawAct) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *AowRawAct) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Mediashare = v
	}
	return nil
}

func (p *AowRawAct) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *AowRawAct) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.AccessCode = v
	}
	return nil
}

func (p *AowRawAct) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *AowRawAct) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *AowRawAct) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *AowRawAct) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *AowRawAct) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *AowRawAct) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *AowRawAct) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *AowRawAct) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *AowRawAct) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *AowRawAct) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.DisplayType = v
	}
	return nil
}

func (p *AowRawAct) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.EntryType = v
	}
	return nil
}

func (p *AowRawAct) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *AowRawAct) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *AowRawAct) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *AowRawAct) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *AowRawAct) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *AowRawAct) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *AowRawAct) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *AowRawAct) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *AowRawAct) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.Idfv = v
	}
	return nil
}

func (p *AowRawAct) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.AntiStatus = v
	}
	return nil
}

func (p *AowRawAct) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *AowRawAct) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Dmid = v
	}
	return nil
}

func (p *AowRawAct) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *AowRawAct) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.Amn = v
	}
	return nil
}

func (p *AowRawAct) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *AowRawAct) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *AowRawAct) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.Oifa = v
	}
	return nil
}

func (p *AowRawAct) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.Aifa = v
	}
	return nil
}

func (p *AowRawAct) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.Extinfo = v
	}
	return nil
}

func (p *AowRawAct) readField59(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 59: %s", err)
	} else {
		p.ClkTime = v
	}
	return nil
}

func (p *AowRawAct) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Clkid = v
	}
	return nil
}

func (p *AowRawAct) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.ClkIsspam = v
	}
	return nil
}

func (p *AowRawAct) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.ClkSpamType = v
	}
	return nil
}

func (p *AowRawAct) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AowRawAct) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.Cis = v
	}
	return nil
}

func (p *AowRawAct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowRawAct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowRawAct) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:actid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Actid)); err != nil {
		return fmt.Errorf("%T.actid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:actid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:macmd5: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:odin1: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:uuid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:oid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_time", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:act_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActTime)); err != nil {
		return fmt.Errorf("%T.act_time (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:act_time: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:status: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("proc_time", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:proc_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProcTime)); err != nil {
		return fmt.Errorf("%T.proc_time (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:proc_time: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostid", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:hostid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostid)); err != nil {
		return fmt.Errorf("%T.hostid (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:hostid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:dt: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:hr: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:dmac: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:source: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:imei: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:android_id: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:idfa: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:platform: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:action: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:searchid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I16, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:sr: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:sr: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:cid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:planid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:uid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:price: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare", thrift.I64, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mediashare)); err != nil {
		return fmt.Errorf("%T.mediashare (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:mediashare: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:point: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:access_code: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:device_code: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:pubid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:pub_deverid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:userid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:ip: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:region_code: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:sp_price: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:rank: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_type", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:display_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.display_type (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:display_type: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entry_type", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:entry_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EntryType)); err != nil {
		return fmt.Errorf("%T.entry_type (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:entry_type: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:sv: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:isspam: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:spam_type: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I32, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:offer_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:offer_type: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:cost_type: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:ad_charge: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:media_charge: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:os_code: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfv", thrift.STRING, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:idfv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfv)); err != nil {
		return fmt.Errorf("%T.idfv (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:idfv: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anti_status", thrift.I16, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:anti_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.AntiStatus)); err != nil {
		return fmt.Errorf("%T.anti_status (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:anti_status: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.STRING, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:duid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:duid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmid", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:dmid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmid)); err != nil {
		return fmt.Errorf("%T.dmid (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:dmid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:amac: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amn", thrift.STRING, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:amn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amn)); err != nil {
		return fmt.Errorf("%T.amn (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:amn: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.STRING, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:latitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:latitude: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.STRING, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:longitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:longitude: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oifa", thrift.STRING, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:oifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oifa)); err != nil {
		return fmt.Errorf("%T.oifa (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:oifa: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aifa", thrift.STRING, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:aifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Aifa)); err != nil {
		return fmt.Errorf("%T.aifa (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:aifa: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extinfo", thrift.STRING, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:extinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Extinfo)); err != nil {
		return fmt.Errorf("%T.extinfo (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:extinfo: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField59(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_time", thrift.I32, 59); err != nil {
		return fmt.Errorf("%T write field begin error 59:clk_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkTime)); err != nil {
		return fmt.Errorf("%T.clk_time (59) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 59:clk_time: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkid", thrift.I32, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:clkid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Clkid)); err != nil {
		return fmt.Errorf("%T.clkid (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:clkid: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_isspam", thrift.BOOL, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:clk_isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ClkIsspam)); err != nil {
		return fmt.Errorf("%T.clk_isspam (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:clk_isspam: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_spam_type", thrift.STRING, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:clk_spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClkSpamType)); err != nil {
		return fmt.Errorf("%T.clk_spam_type (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:clk_spam_type: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField63(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 63); err != nil {
		return fmt.Errorf("%T write field begin error 63:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (63) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 63:package_name: %s", p, err)
	}
	return err
}

func (p *AowRawAct) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cis", thrift.STRING, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:cis: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cis)); err != nil {
		return fmt.Errorf("%T.cis (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:cis: %s", p, err)
	}
	return err
}

func (p *AowRawAct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowRawAct(%+v)", *p)
}

type AowRawClick struct {
	Clkid       int64  `thrift:"clkid,1" json:"clkid"`
	Searchid    int64  `thrift:"searchid,2" json:"searchid"`
	Sr          int16  `thrift:"sr,3" json:"sr"`
	Cid         int32  `thrift:"cid,4" json:"cid"`
	Planid      int32  `thrift:"planid,5" json:"planid"`
	Uid         int32  `thrift:"uid,6" json:"uid"`
	Price       int64  `thrift:"price,7" json:"price"`
	Mediashare  int64  `thrift:"mediashare,8" json:"mediashare"`
	Point       int64  `thrift:"point,9" json:"point"`
	AccessCode  int32  `thrift:"access_code,10" json:"access_code"`
	DeviceCode  int32  `thrift:"device_code,11" json:"device_code"`
	Appid       string `thrift:"appid,12" json:"appid"`
	Pubid       string `thrift:"pubid,13" json:"pubid"`
	PubMediaid  int32  `thrift:"pub_mediaid,14" json:"pub_mediaid"`
	PubDeverid  int32  `thrift:"pub_deverid,15" json:"pub_deverid"`
	Macmd5      string `thrift:"macmd5,16" json:"macmd5"`
	Odin1       string `thrift:"odin1,17" json:"odin1"`
	Uuid        string `thrift:"uuid,18" json:"uuid"`
	Oid         string `thrift:"oid,19" json:"oid"`
	Dmac        string `thrift:"dmac,20" json:"dmac"`
	ClkTime     int32  `thrift:"clk_time,21" json:"clk_time"`
	Hostid      string `thrift:"hostid,22" json:"hostid"`
	Dt          int32  `thrift:"dt,23" json:"dt"`
	Hr          int32  `thrift:"hr,24" json:"hr"`
	Sendstatus  int32  `thrift:"sendstatus,25" json:"sendstatus"`
	Userid      string `thrift:"userid,26" json:"userid"`
	CallbackUrl string `thrift:"callback_url,27" json:"callback_url"`
	Ip          string `thrift:"ip,28" json:"ip"`
	RegionCode  int32  `thrift:"region_code,29" json:"region_code"`
	SpPrice     int64  `thrift:"sp_price,30" json:"sp_price"`
	Rank        int32  `thrift:"rank,31" json:"rank"`
	IsAct       int32  `thrift:"is_act,32" json:"is_act"`
	DisplayType int32  `thrift:"display_type,33" json:"display_type"`
	EntryType   int32  `thrift:"entry_type,34" json:"entry_type"`
	Sv          int32  `thrift:"sv,35" json:"sv"`
	Imei        string `thrift:"imei,36" json:"imei"`
	AndroidId   string `thrift:"android_id,37" json:"android_id"`
	Idfa        string `thrift:"idfa,38" json:"idfa"`
	Platform    int32  `thrift:"platform,39" json:"platform"`
	Action      int32  `thrift:"action,40" json:"action"`
	CostType    int32  `thrift:"cost_type,41" json:"cost_type"`
	OfferType   int32  `thrift:"offer_type,42" json:"offer_type"`
	Isspam      bool   `thrift:"isspam,43" json:"isspam"`
	SpamType    string `thrift:"spam_type,44" json:"spam_type"`
	AdCharge    bool   `thrift:"ad_charge,45" json:"ad_charge"`
	MediaCharge bool   `thrift:"media_charge,46" json:"media_charge"`
	OsCode      int32  `thrift:"os_code,47" json:"os_code"`
	Idfv        string `thrift:"idfv,48" json:"idfv"`
	Duid        string `thrift:"duid,49" json:"duid"`
	Dmid        string `thrift:"dmid,50" json:"dmid"`
	Amac        string `thrift:"amac,51" json:"amac"`
	Amn         string `thrift:"amn,52" json:"amn"`
	Latitude    string `thrift:"latitude,53" json:"latitude"`
	Longitude   string `thrift:"longitude,54" json:"longitude"`
	Oifa        string `thrift:"oifa,55" json:"oifa"`
	Aifa        string `thrift:"aifa,56" json:"aifa"`
	Extinfo     string `thrift:"extinfo,57" json:"extinfo"`
	AntiStatus  int16  `thrift:"anti_status,58" json:"anti_status"`
	PackageName string `thrift:"package_name,59" json:"package_name"`
	Source      int16  `thrift:"source,60" json:"source"`
}

func NewAowRawClick() *AowRawClick {
	return &AowRawClick{}
}

func (p *AowRawClick) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I32 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.STRING {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.STRING {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.STRING {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.STRING {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.STRING {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.STRING {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.STRING {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.STRING {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.I16 {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 59:
			if fieldTypeId == thrift.STRING {
				if err := p.readField59(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I16 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowRawClick) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Clkid = v
	}
	return nil
}

func (p *AowRawClick) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AowRawClick) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *AowRawClick) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *AowRawClick) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *AowRawClick) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AowRawClick) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *AowRawClick) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Mediashare = v
	}
	return nil
}

func (p *AowRawClick) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *AowRawClick) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AccessCode = v
	}
	return nil
}

func (p *AowRawClick) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *AowRawClick) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowRawClick) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *AowRawClick) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *AowRawClick) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *AowRawClick) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *AowRawClick) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Odin1 = v
	}
	return nil
}

func (p *AowRawClick) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *AowRawClick) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Oid = v
	}
	return nil
}

func (p *AowRawClick) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *AowRawClick) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ClkTime = v
	}
	return nil
}

func (p *AowRawClick) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Hostid = v
	}
	return nil
}

func (p *AowRawClick) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *AowRawClick) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *AowRawClick) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Sendstatus = v
	}
	return nil
}

func (p *AowRawClick) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *AowRawClick) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.CallbackUrl = v
	}
	return nil
}

func (p *AowRawClick) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *AowRawClick) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *AowRawClick) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *AowRawClick) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *AowRawClick) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.IsAct = v
	}
	return nil
}

func (p *AowRawClick) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.DisplayType = v
	}
	return nil
}

func (p *AowRawClick) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.EntryType = v
	}
	return nil
}

func (p *AowRawClick) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *AowRawClick) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AowRawClick) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *AowRawClick) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Idfa = v
	}
	return nil
}

func (p *AowRawClick) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *AowRawClick) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *AowRawClick) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *AowRawClick) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *AowRawClick) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *AowRawClick) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *AowRawClick) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *AowRawClick) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *AowRawClick) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *AowRawClick) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.Idfv = v
	}
	return nil
}

func (p *AowRawClick) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *AowRawClick) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.Dmid = v
	}
	return nil
}

func (p *AowRawClick) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *AowRawClick) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.Amn = v
	}
	return nil
}

func (p *AowRawClick) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *AowRawClick) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *AowRawClick) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.Oifa = v
	}
	return nil
}

func (p *AowRawClick) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.Aifa = v
	}
	return nil
}

func (p *AowRawClick) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.Extinfo = v
	}
	return nil
}

func (p *AowRawClick) readField58(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 58: %s", err)
	} else {
		p.AntiStatus = v
	}
	return nil
}

func (p *AowRawClick) readField59(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 59: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AowRawClick) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *AowRawClick) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowRawClick"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField59(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowRawClick) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clkid", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:clkid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clkid)); err != nil {
		return fmt.Errorf("%T.clkid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:clkid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sr: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sr: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:planid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:uid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:price: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mediashare)); err != nil {
		return fmt.Errorf("%T.mediashare (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:mediashare: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:point: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:access_code: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:device_code: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:appid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:pubid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:pub_deverid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:macmd5: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin1", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:odin1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin1)); err != nil {
		return fmt.Errorf("%T.odin1 (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:odin1: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:uuid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oid", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:oid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oid)); err != nil {
		return fmt.Errorf("%T.oid (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:oid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:dmac: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk_time", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:clk_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ClkTime)); err != nil {
		return fmt.Errorf("%T.clk_time (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:clk_time: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hostid", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:hostid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hostid)); err != nil {
		return fmt.Errorf("%T.hostid (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:hostid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:dt: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:hr: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sendstatus", thrift.I32, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:sendstatus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sendstatus)); err != nil {
		return fmt.Errorf("%T.sendstatus (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:sendstatus: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:userid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("callback_url", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:callback_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CallbackUrl)); err != nil {
		return fmt.Errorf("%T.callback_url (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:callback_url: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:ip: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:region_code: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:sp_price: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:rank: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_act", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:is_act: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsAct)); err != nil {
		return fmt.Errorf("%T.is_act (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:is_act: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_type", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:display_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.display_type (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:display_type: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entry_type", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:entry_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EntryType)); err != nil {
		return fmt.Errorf("%T.entry_type (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:entry_type: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:sv: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:imei: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:android_id: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:idfa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfa)); err != nil {
		return fmt.Errorf("%T.idfa (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:idfa: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:platform: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:action: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:cost_type: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:offer_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:offer_type: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:isspam: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:spam_type: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:ad_charge: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:media_charge: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:os_code: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfv", thrift.STRING, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:idfv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idfv)); err != nil {
		return fmt.Errorf("%T.idfv (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:idfv: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.STRING, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:duid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:duid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmid", thrift.STRING, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:dmid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmid)); err != nil {
		return fmt.Errorf("%T.dmid (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:dmid: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:amac: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amn", thrift.STRING, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:amn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amn)); err != nil {
		return fmt.Errorf("%T.amn (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:amn: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.STRING, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:latitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:latitude: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.STRING, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:longitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:longitude: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("oifa", thrift.STRING, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:oifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Oifa)); err != nil {
		return fmt.Errorf("%T.oifa (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:oifa: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("aifa", thrift.STRING, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:aifa: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Aifa)); err != nil {
		return fmt.Errorf("%T.aifa (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:aifa: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extinfo", thrift.STRING, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:extinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Extinfo)); err != nil {
		return fmt.Errorf("%T.extinfo (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:extinfo: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField58(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anti_status", thrift.I16, 58); err != nil {
		return fmt.Errorf("%T write field begin error 58:anti_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.AntiStatus)); err != nil {
		return fmt.Errorf("%T.anti_status (58) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 58:anti_status: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField59(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 59); err != nil {
		return fmt.Errorf("%T write field begin error 59:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (59) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 59:package_name: %s", p, err)
	}
	return err
}

func (p *AowRawClick) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.I16, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:source: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Source)); err != nil {
		return fmt.Errorf("%T.source (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:source: %s", p, err)
	}
	return err
}

func (p *AowRawClick) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowRawClick(%+v)", *p)
}

type AowRawDownload struct {
	Id         int64  `thrift:"id,1" json:"id"`
	Searchid   int64  `thrift:"searchid,2" json:"searchid"`
	Sr         int16  `thrift:"sr,3" json:"sr"`
	Cid        int32  `thrift:"cid,4" json:"cid"`
	Planid     int32  `thrift:"planid,5" json:"planid"`
	Uid        int32  `thrift:"uid,6" json:"uid"`
	Action     int32  `thrift:"action,7" json:"action"`
	Appid      string `thrift:"appid,8" json:"appid"`
	Pubid      string `thrift:"pubid,9" json:"pubid"`
	PubMediaid int32  `thrift:"pub_mediaid,10" json:"pub_mediaid"`
	PubDeverid int32  `thrift:"pub_deverid,11" json:"pub_deverid"`
	Price      int64  `thrift:"price,12" json:"price"`
	Mediashare int64  `thrift:"mediashare,13" json:"mediashare"`
	Point      int64  `thrift:"point,14" json:"point"`
	SpPrice    int64  `thrift:"sp_price,15" json:"sp_price"`
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	DownloadTime int32  `thrift:"download_time,20" json:"download_time"`
	Macmd5       string `thrift:"macmd5,21" json:"macmd5"`
	Dmac         string `thrift:"dmac,22" json:"dmac"`
	AccessCode   int32  `thrift:"access_code,23" json:"access_code"`
	DeviceCode   int32  `thrift:"device_code,24" json:"device_code"`
	Userid       string `thrift:"userid,25" json:"userid"`
	Ip           string `thrift:"ip,26" json:"ip"`
	Duid         string `thrift:"duid,27" json:"duid"`
	Dmid         string `thrift:"dmid,28" json:"dmid"`
	RegionCode   int32  `thrift:"region_code,29" json:"region_code"`
	OsCode       int32  `thrift:"os_code,30" json:"os_code"`
	Rank         int32  `thrift:"rank,31" json:"rank"`
	IsAct        int32  `thrift:"is_act,32" json:"is_act"`
	Sv           int32  `thrift:"sv,33" json:"sv"`
	Imei         string `thrift:"imei,34" json:"imei"`
	AndroidId    string `thrift:"android_id,35" json:"android_id"`
	Amac         string `thrift:"amac,36" json:"amac"`
	Amn          string `thrift:"amn,37" json:"amn"`
	Latitude     string `thrift:"latitude,38" json:"latitude"`
	Longitude    string `thrift:"longitude,39" json:"longitude"`
	Sendstatus   int32  `thrift:"sendstatus,40" json:"sendstatus"`
	CallbackUrl  string `thrift:"callback_url,41" json:"callback_url"`
	// unused field # 42
	// unused field # 43
	// unused field # 44
	DisplayType int32  `thrift:"display_type,45" json:"display_type"`
	EntryType   int32  `thrift:"entry_type,46" json:"entry_type"`
	CostType    int32  `thrift:"cost_type,47" json:"cost_type"`
	OfferType   int32  `thrift:"offer_type,48" json:"offer_type"`
	Isspam      bool   `thrift:"isspam,49" json:"isspam"`
	SpamType    string `thrift:"spam_type,50" json:"spam_type"`
	AntiStatus  int16  `thrift:"anti_status,51" json:"anti_status"`
	AdCharge    bool   `thrift:"ad_charge,52" json:"ad_charge"`
	MediaCharge bool   `thrift:"media_charge,53" json:"media_charge"`
	PackageName string `thrift:"package_name,54" json:"package_name"`
	Extinfo     string `thrift:"extinfo,55" json:"extinfo"`
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	Dt int32 `thrift:"dt,60" json:"dt"`
	Hr int32 `thrift:"hr,61" json:"hr"`
}

func NewAowRawDownload() *AowRawDownload {
	return &AowRawDownload{}
}

func (p *AowRawDownload) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I64 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I32 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I32 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.STRING {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.STRING {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.I32 {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.I32 {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.I32 {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 48:
			if fieldTypeId == thrift.I32 {
				if err := p.readField48(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 49:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField49(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.STRING {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I16 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.STRING {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.STRING {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I32 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowRawDownload) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AowRawDownload) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AowRawDownload) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sr = v
	}
	return nil
}

func (p *AowRawDownload) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *AowRawDownload) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *AowRawDownload) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AowRawDownload) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *AowRawDownload) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowRawDownload) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Pubid = v
	}
	return nil
}

func (p *AowRawDownload) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *AowRawDownload) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *AowRawDownload) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *AowRawDownload) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Mediashare = v
	}
	return nil
}

func (p *AowRawDownload) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *AowRawDownload) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *AowRawDownload) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.DownloadTime = v
	}
	return nil
}

func (p *AowRawDownload) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Macmd5 = v
	}
	return nil
}

func (p *AowRawDownload) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *AowRawDownload) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.AccessCode = v
	}
	return nil
}

func (p *AowRawDownload) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *AowRawDownload) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *AowRawDownload) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *AowRawDownload) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Duid = v
	}
	return nil
}

func (p *AowRawDownload) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.Dmid = v
	}
	return nil
}

func (p *AowRawDownload) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *AowRawDownload) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *AowRawDownload) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *AowRawDownload) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.IsAct = v
	}
	return nil
}

func (p *AowRawDownload) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *AowRawDownload) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AowRawDownload) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *AowRawDownload) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *AowRawDownload) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Amn = v
	}
	return nil
}

func (p *AowRawDownload) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *AowRawDownload) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *AowRawDownload) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Sendstatus = v
	}
	return nil
}

func (p *AowRawDownload) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.CallbackUrl = v
	}
	return nil
}

func (p *AowRawDownload) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.DisplayType = v
	}
	return nil
}

func (p *AowRawDownload) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.EntryType = v
	}
	return nil
}

func (p *AowRawDownload) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.CostType = v
	}
	return nil
}

func (p *AowRawDownload) readField48(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 48: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *AowRawDownload) readField49(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 49: %s", err)
	} else {
		p.Isspam = v
	}
	return nil
}

func (p *AowRawDownload) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *AowRawDownload) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.AntiStatus = v
	}
	return nil
}

func (p *AowRawDownload) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *AowRawDownload) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *AowRawDownload) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AowRawDownload) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.Extinfo = v
	}
	return nil
}

func (p *AowRawDownload) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *AowRawDownload) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *AowRawDownload) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowRawDownload"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := p.writeField48(oprot); err != nil {
		return err
	}
	if err := p.writeField49(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowRawDownload) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sr", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sr: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Sr)); err != nil {
		return fmt.Errorf("%T.sr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sr: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:planid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:uid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:action: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:appid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubid", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:pubid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pubid)); err != nil {
		return fmt.Errorf("%T.pubid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:pubid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:pub_deverid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:price: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mediashare)); err != nil {
		return fmt.Errorf("%T.mediashare (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:mediashare: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:point: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:sp_price: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("download_time", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:download_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DownloadTime)); err != nil {
		return fmt.Errorf("%T.download_time (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:download_time: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("macmd5", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:macmd5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Macmd5)); err != nil {
		return fmt.Errorf("%T.macmd5 (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:macmd5: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:dmac: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:access_code: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:device_code: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:userid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:ip: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duid", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:duid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Duid)); err != nil {
		return fmt.Errorf("%T.duid (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:duid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmid", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:dmid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmid)); err != nil {
		return fmt.Errorf("%T.dmid (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:dmid: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:region_code: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:os_code: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:rank: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_act", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:is_act: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IsAct)); err != nil {
		return fmt.Errorf("%T.is_act (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:is_act: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:sv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:sv: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:imei: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:android_id: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:amac: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amn", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:amn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amn)); err != nil {
		return fmt.Errorf("%T.amn (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:amn: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.STRING, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:latitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:latitude: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.STRING, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:longitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:longitude: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sendstatus", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:sendstatus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sendstatus)); err != nil {
		return fmt.Errorf("%T.sendstatus (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:sendstatus: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("callback_url", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:callback_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CallbackUrl)); err != nil {
		return fmt.Errorf("%T.callback_url (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:callback_url: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_type", thrift.I32, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:display_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.display_type (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:display_type: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("entry_type", thrift.I32, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:entry_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.EntryType)); err != nil {
		return fmt.Errorf("%T.entry_type (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:entry_type: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cost_type", thrift.I32, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.cost_type (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:cost_type: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField48(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I32, 48); err != nil {
		return fmt.Errorf("%T write field begin error 48:offer_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (48) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 48:offer_type: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField49(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isspam", thrift.BOOL, 49); err != nil {
		return fmt.Errorf("%T write field begin error 49:isspam: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Isspam)); err != nil {
		return fmt.Errorf("%T.isspam (49) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 49:isspam: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.STRING, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:spam_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:spam_type: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("anti_status", thrift.I16, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:anti_status: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.AntiStatus)); err != nil {
		return fmt.Errorf("%T.anti_status (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:anti_status: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:ad_charge: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:media_charge: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("package_name", thrift.STRING, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:package_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.package_name (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:package_name: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extinfo", thrift.STRING, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:extinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Extinfo)); err != nil {
		return fmt.Errorf("%T.extinfo (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:extinfo: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:dt: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) writeField61(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 61); err != nil {
		return fmt.Errorf("%T write field begin error 61:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (61) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 61:hr: %s", p, err)
	}
	return err
}

func (p *AowRawDownload) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowRawDownload(%+v)", *p)
}
