// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package aow_stat_types2

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type AowWallReport struct {
	EventType  string `thrift:"event_type,1" json:"event_type"`
	EventTime  int64  `thrift:"event_time,2" json:"event_time"`
	Dt         int32  `thrift:"dt,3" json:"dt"`
	Hr         int32  `thrift:"hr,4" json:"hr"`
	Ipb        string `thrift:"ipb,5" json:"ipb"`
	PubDeverid int32  `thrift:"pub_deverid,6" json:"pub_deverid"`
	PubMediaid int32  `thrift:"pub_mediaid,7" json:"pub_mediaid"`
	PubPkgName string `thrift:"pub_pkg_name,8" json:"pub_pkg_name"`
	PubUserid  string `thrift:"pub_userid,9" json:"pub_userid"`
}

func NewAowWallReport() *AowWallReport {
	return &AowWallReport{}
}

func (p *AowWallReport) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowWallReport) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.EventType = v
	}
	return nil
}

func (p *AowWallReport) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EventTime = v
	}
	return nil
}

func (p *AowWallReport) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *AowWallReport) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *AowWallReport) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *AowWallReport) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *AowWallReport) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *AowWallReport) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.PubPkgName = v
	}
	return nil
}

func (p *AowWallReport) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PubUserid = v
	}
	return nil
}

func (p *AowWallReport) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowWallReport"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowWallReport) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_type", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:event_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EventType)); err != nil {
		return fmt.Errorf("%T.event_type (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:event_type: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:event_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EventTime)); err != nil {
		return fmt.Errorf("%T.event_time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:event_time: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:dt: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:hr: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ipb: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:pub_deverid: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_pkg_name", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:pub_pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PubPkgName)); err != nil {
		return fmt.Errorf("%T.pub_pkg_name (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:pub_pkg_name: %s", p, err)
	}
	return err
}

func (p *AowWallReport) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_userid", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:pub_userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PubUserid)); err != nil {
		return fmt.Errorf("%T.pub_userid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:pub_userid: %s", p, err)
	}
	return err
}

func (p *AowWallReport) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowWallReport(%+v)", *p)
}

type AowAdEventReport struct {
	EventType  string `thrift:"event_type,1" json:"event_type"`
	Searchid   int64  `thrift:"searchid,2" json:"searchid"`
	EventTime  int64  `thrift:"event_time,3" json:"event_time"`
	ImpReqTime int32  `thrift:"imp_req_time,4" json:"imp_req_time"`
	Expid      int32  `thrift:"expid,5" json:"expid"`
	Dt         int32  `thrift:"dt,6" json:"dt"`
	Hr         int32  `thrift:"hr,7" json:"hr"`
	// unused field # 8
	// unused field # 9
	Ipb        string `thrift:"ipb,10" json:"ipb"`
	PubDeverid int32  `thrift:"pub_deverid,11" json:"pub_deverid"`
	PubMediaid int32  `thrift:"pub_mediaid,12" json:"pub_mediaid"`
	PubPkgName string `thrift:"pub_pkg_name,13" json:"pub_pkg_name"`
	PubUserid  string `thrift:"pub_userid,14" json:"pub_userid"`
	SponsorUid int32  `thrift:"sponsor_uid,15" json:"sponsor_uid"`
	Planid     int32  `thrift:"planid,16" json:"planid"`
	Cid        int32  `thrift:"cid,17" json:"cid"`
	Pkgid      int32  `thrift:"pkgid,18" json:"pkgid"`
	Appid      string `thrift:"appid,19" json:"appid"`
	Action     int32  `thrift:"action,20" json:"action"`
	SpPrice    int64  `thrift:"sp_price,21" json:"sp_price"`
	Price      int64  `thrift:"price,22" json:"price"`
	Mediashare int64  `thrift:"mediashare,23" json:"mediashare"`
	Point      int64  `thrift:"point,24" json:"point"`
	Rank       int32  `thrift:"rank,25" json:"rank"`
	AdPkgName  string `thrift:"ad_pkg_name,26" json:"ad_pkg_name"`
	// unused field # 27
	// unused field # 28
	// unused field # 29
	Imei        string                `thrift:"imei,30" json:"imei"`
	Imsi        string                `thrift:"imsi,31" json:"imsi"`
	Dmac        string                `thrift:"dmac,32" json:"dmac"`
	Ip          string                `thrift:"ip,33" json:"ip"`
	AndroidId   string                `thrift:"android_id,34" json:"android_id"`
	Amac        string                `thrift:"amac,35" json:"amac"`
	Amn         string                `thrift:"amn,36" json:"amn"`
	Sv          string                `thrift:"sv,37" json:"sv"`
	AccessCode  common.AccessTypeCode `thrift:"access_code,38" json:"access_code"`
	CarrierCode common.CarrierCode    `thrift:"carrier_code,39" json:"carrier_code"`
	RegionCode  int32                 `thrift:"region_code,40" json:"region_code"`
	OsCode      int32                 `thrift:"os_code,41" json:"os_code"`
	DeviceCode  int32                 `thrift:"device_code,42" json:"device_code"`
	Latitude    string                `thrift:"latitude,43" json:"latitude"`
	Longitude   string                `thrift:"longitude,44" json:"longitude"`
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	SpamType         int16 `thrift:"spam_type,50" json:"spam_type"`
	AdCharge         bool  `thrift:"ad_charge,51" json:"ad_charge"`
	MediaCharge      bool  `thrift:"media_charge,52" json:"media_charge"`
	SettlePrice      int64 `thrift:"settle_price,53" json:"settle_price"`
	SettleMediashare int64 `thrift:"settle_mediashare,54" json:"settle_mediashare"`
	SettleTime       int64 `thrift:"settle_time,55" json:"settle_time"`
	// unused field # 56
	Extinfo string `thrift:"extinfo,57" json:"extinfo"`
}

func NewAowAdEventReport() *AowAdEventReport {
	return &AowAdEventReport{
		AccessCode: math.MinInt32 - 1, // unset sentinal value

		CarrierCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AowAdEventReport) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *AowAdEventReport) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *AowAdEventReport) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.I32 {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I64 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I64 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I32 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.STRING {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.STRING {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.STRING {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.STRING {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.STRING {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.STRING {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I32 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.I32 {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.I32 {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.STRING {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I16 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I64 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I64 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.STRING {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowAdEventReport) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.EventType = v
	}
	return nil
}

func (p *AowAdEventReport) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Searchid = v
	}
	return nil
}

func (p *AowAdEventReport) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.EventTime = v
	}
	return nil
}

func (p *AowAdEventReport) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImpReqTime = v
	}
	return nil
}

func (p *AowAdEventReport) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Expid = v
	}
	return nil
}

func (p *AowAdEventReport) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Dt = v
	}
	return nil
}

func (p *AowAdEventReport) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Hr = v
	}
	return nil
}

func (p *AowAdEventReport) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *AowAdEventReport) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.PubDeverid = v
	}
	return nil
}

func (p *AowAdEventReport) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PubMediaid = v
	}
	return nil
}

func (p *AowAdEventReport) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.PubPkgName = v
	}
	return nil
}

func (p *AowAdEventReport) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.PubUserid = v
	}
	return nil
}

func (p *AowAdEventReport) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.SponsorUid = v
	}
	return nil
}

func (p *AowAdEventReport) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *AowAdEventReport) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *AowAdEventReport) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Pkgid = v
	}
	return nil
}

func (p *AowAdEventReport) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowAdEventReport) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *AowAdEventReport) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *AowAdEventReport) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *AowAdEventReport) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Mediashare = v
	}
	return nil
}

func (p *AowAdEventReport) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *AowAdEventReport) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *AowAdEventReport) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.AdPkgName = v
	}
	return nil
}

func (p *AowAdEventReport) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AowAdEventReport) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Imsi = v
	}
	return nil
}

func (p *AowAdEventReport) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Dmac = v
	}
	return nil
}

func (p *AowAdEventReport) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *AowAdEventReport) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.AndroidId = v
	}
	return nil
}

func (p *AowAdEventReport) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.Amac = v
	}
	return nil
}

func (p *AowAdEventReport) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Amn = v
	}
	return nil
}

func (p *AowAdEventReport) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *AowAdEventReport) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.AccessCode = common.AccessTypeCode(v)
	}
	return nil
}

func (p *AowAdEventReport) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.CarrierCode = common.CarrierCode(v)
	}
	return nil
}

func (p *AowAdEventReport) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.RegionCode = v
	}
	return nil
}

func (p *AowAdEventReport) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.OsCode = v
	}
	return nil
}

func (p *AowAdEventReport) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.DeviceCode = v
	}
	return nil
}

func (p *AowAdEventReport) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *AowAdEventReport) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *AowAdEventReport) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.SpamType = v
	}
	return nil
}

func (p *AowAdEventReport) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.AdCharge = v
	}
	return nil
}

func (p *AowAdEventReport) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.MediaCharge = v
	}
	return nil
}

func (p *AowAdEventReport) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.SettlePrice = v
	}
	return nil
}

func (p *AowAdEventReport) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.SettleMediashare = v
	}
	return nil
}

func (p *AowAdEventReport) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.SettleTime = v
	}
	return nil
}

func (p *AowAdEventReport) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.Extinfo = v
	}
	return nil
}

func (p *AowAdEventReport) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowAdEventReport"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowAdEventReport) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_type", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:event_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EventType)); err != nil {
		return fmt.Errorf("%T.event_type (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:event_type: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Searchid)); err != nil {
		return fmt.Errorf("%T.searchid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("event_time", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:event_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EventTime)); err != nil {
		return fmt.Errorf("%T.event_time (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:event_time: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp_req_time", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:imp_req_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImpReqTime)); err != nil {
		return fmt.Errorf("%T.imp_req_time (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:imp_req_time: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:expid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Expid)); err != nil {
		return fmt.Errorf("%T.expid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:expid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dt", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:dt: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dt)); err != nil {
		return fmt.Errorf("%T.dt (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:dt: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hr", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:hr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Hr)); err != nil {
		return fmt.Errorf("%T.hr (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:hr: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:ipb: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_deverid", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:pub_deverid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubDeverid)); err != nil {
		return fmt.Errorf("%T.pub_deverid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:pub_deverid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_mediaid", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:pub_mediaid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PubMediaid)); err != nil {
		return fmt.Errorf("%T.pub_mediaid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:pub_mediaid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_pkg_name", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:pub_pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PubPkgName)); err != nil {
		return fmt.Errorf("%T.pub_pkg_name (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:pub_pkg_name: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pub_userid", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:pub_userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PubUserid)); err != nil {
		return fmt.Errorf("%T.pub_userid (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:pub_userid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_uid", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:sponsor_uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorUid)); err != nil {
		return fmt.Errorf("%T.sponsor_uid (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:sponsor_uid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:planid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:cid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkgid", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:pkgid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pkgid)); err != nil {
		return fmt.Errorf("%T.pkgid (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:pkgid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:appid: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:action: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:sp_price: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:price: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediashare", thrift.I64, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Mediashare)); err != nil {
		return fmt.Errorf("%T.mediashare (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:mediashare: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:point: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:rank: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_pkg_name", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:ad_pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AdPkgName)); err != nil {
		return fmt.Errorf("%T.ad_pkg_name (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:ad_pkg_name: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:imei: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imsi", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:imsi: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imsi)); err != nil {
		return fmt.Errorf("%T.imsi (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:imsi: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dmac", thrift.STRING, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:dmac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Dmac)); err != nil {
		return fmt.Errorf("%T.dmac (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:dmac: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:ip: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_id", thrift.STRING, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:android_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AndroidId)); err != nil {
		return fmt.Errorf("%T.android_id (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:android_id: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField35(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amac", thrift.STRING, 35); err != nil {
		return fmt.Errorf("%T write field begin error 35:amac: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amac)); err != nil {
		return fmt.Errorf("%T.amac (35) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 35:amac: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amn", thrift.STRING, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:amn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Amn)); err != nil {
		return fmt.Errorf("%T.amn (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:amn: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.STRING, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:sv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:sv: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:access_code: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:carrier_code: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("region_code", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:region_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RegionCode)); err != nil {
		return fmt.Errorf("%T.region_code (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:region_code: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:os_code: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_code", thrift.I32, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:device_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceCode)); err != nil {
		return fmt.Errorf("%T.device_code (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:device_code: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.STRING, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:latitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:latitude: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:longitude: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:longitude: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("spam_type", thrift.I16, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:spam_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.SpamType)); err != nil {
		return fmt.Errorf("%T.spam_type (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:spam_type: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_charge", thrift.BOOL, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:ad_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AdCharge)); err != nil {
		return fmt.Errorf("%T.ad_charge (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:ad_charge: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_charge", thrift.BOOL, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:media_charge: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.MediaCharge)); err != nil {
		return fmt.Errorf("%T.media_charge (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:media_charge: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField53(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_price", thrift.I64, 53); err != nil {
		return fmt.Errorf("%T write field begin error 53:settle_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettlePrice)); err != nil {
		return fmt.Errorf("%T.settle_price (53) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 53:settle_price: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_mediashare", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:settle_mediashare: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleMediashare)); err != nil {
		return fmt.Errorf("%T.settle_mediashare (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:settle_mediashare: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("settle_time", thrift.I64, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:settle_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SettleTime)); err != nil {
		return fmt.Errorf("%T.settle_time (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:settle_time: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extinfo", thrift.STRING, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:extinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Extinfo)); err != nil {
		return fmt.Errorf("%T.extinfo (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:extinfo: %s", p, err)
	}
	return err
}

func (p *AowAdEventReport) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowAdEventReport(%+v)", *p)
}
