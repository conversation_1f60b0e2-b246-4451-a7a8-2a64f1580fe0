// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appinfo_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//需要查询的字段
//searchField目前支持的字段：
//   应用id，精确查询
//   应用名称，模糊查询
//   应用包名，模糊查询
//   APP备注，模糊查询
type SearchField int64

const (
	SearchField_APP_ID          SearchField = 0
	SearchField_APP_NAME        SearchField = 1
	SearchField_APP_PACKAGENAME SearchField = 2
	SearchField_APP_REMARK      SearchField = 3
)

func (p SearchField) String() string {
	switch p {
	case SearchField_APP_ID:
		return "SearchField_APP_ID"
	case SearchField_APP_NAME:
		return "SearchField_APP_NAME"
	case SearchField_APP_PACKAGENAME:
		return "SearchField_APP_PACKAGENAME"
	case SearchField_APP_REMARK:
		return "SearchField_APP_REMARK"
	}
	return "<UNSET>"
}

func SearchFieldFromString(s string) (SearchField, error) {
	switch s {
	case "SearchField_APP_ID":
		return SearchField_APP_ID, nil
	case "SearchField_APP_NAME":
		return SearchField_APP_NAME, nil
	case "SearchField_APP_PACKAGENAME":
		return SearchField_APP_PACKAGENAME, nil
	case "SearchField_APP_REMARK":
		return SearchField_APP_REMARK, nil
	}
	return SearchField(math.MinInt32 - 1), fmt.Errorf("not a valid SearchField string")
}

//激活事件类型
type ActType int64

const (
	ActType_AT_UNKNOWN           ActType = 0
	ActType_AT_APP               ActType = 2
	ActType_AT_ACTIVE            ActType = 3
	ActType_AT_PAYMENT           ActType = 4
	ActType_AT_OPEN              ActType = 5
	ActType_AT_REGISTER          ActType = 11
	ActType_AT_FIRST_LOGIN       ActType = 12
	ActType_AT_ROLE_CREATION     ActType = 13
	ActType_AT_LOGIN             ActType = 16
	ActType_AT_FIRST_PLAY        ActType = 17
	ActType_AT_LOGOUT            ActType = 18
	ActType_AT_DOWNLOAD_START    ActType = 21
	ActType_AT_DOWNLOAD_FINISH   ActType = 22
	ActType_AT_WAKE              ActType = 23
	ActType_AT_APP_INSTALL       ActType = 24
	ActType_AT_VIDEO_START       ActType = 25
	ActType_AT_VIDEO_FINISH      ActType = 26
	ActType_AT_LANDING_ARRIVAL   ActType = 27
	ActType_AT_LANDING_CLICK     ActType = 28
	ActType_AT_LANDING_REGISTER  ActType = 29
	ActType_AT_ORDER             ActType = 10
	ActType_AT_FIRST_WAKEUP      ActType = 30
	ActType_AT_PRODUCT_VIEW      ActType = 31
	ActType_AT_FIRST_WAKEUP_UV   ActType = 32
	ActType_AT_PAGE_INTERACTIONS ActType = 100
	ActType_AT_CUE_SUBMISSION    ActType = 101
	ActType_AT_ORDER_SUBMISSION  ActType = 102
)

func (p ActType) String() string {
	switch p {
	case ActType_AT_UNKNOWN:
		return "ActType_AT_UNKNOWN"
	case ActType_AT_APP:
		return "ActType_AT_APP"
	case ActType_AT_ACTIVE:
		return "ActType_AT_ACTIVE"
	case ActType_AT_PAYMENT:
		return "ActType_AT_PAYMENT"
	case ActType_AT_OPEN:
		return "ActType_AT_OPEN"
	case ActType_AT_REGISTER:
		return "ActType_AT_REGISTER"
	case ActType_AT_FIRST_LOGIN:
		return "ActType_AT_FIRST_LOGIN"
	case ActType_AT_ROLE_CREATION:
		return "ActType_AT_ROLE_CREATION"
	case ActType_AT_LOGIN:
		return "ActType_AT_LOGIN"
	case ActType_AT_FIRST_PLAY:
		return "ActType_AT_FIRST_PLAY"
	case ActType_AT_LOGOUT:
		return "ActType_AT_LOGOUT"
	case ActType_AT_DOWNLOAD_START:
		return "ActType_AT_DOWNLOAD_START"
	case ActType_AT_DOWNLOAD_FINISH:
		return "ActType_AT_DOWNLOAD_FINISH"
	case ActType_AT_WAKE:
		return "ActType_AT_WAKE"
	case ActType_AT_APP_INSTALL:
		return "ActType_AT_APP_INSTALL"
	case ActType_AT_VIDEO_START:
		return "ActType_AT_VIDEO_START"
	case ActType_AT_VIDEO_FINISH:
		return "ActType_AT_VIDEO_FINISH"
	case ActType_AT_LANDING_ARRIVAL:
		return "ActType_AT_LANDING_ARRIVAL"
	case ActType_AT_LANDING_CLICK:
		return "ActType_AT_LANDING_CLICK"
	case ActType_AT_LANDING_REGISTER:
		return "ActType_AT_LANDING_REGISTER"
	case ActType_AT_ORDER:
		return "ActType_AT_ORDER"
	case ActType_AT_FIRST_WAKEUP:
		return "ActType_AT_FIRST_WAKEUP"
	case ActType_AT_PRODUCT_VIEW:
		return "ActType_AT_PRODUCT_VIEW"
	case ActType_AT_FIRST_WAKEUP_UV:
		return "ActType_AT_FIRST_WAKEUP_UV"
	case ActType_AT_PAGE_INTERACTIONS:
		return "ActType_AT_PAGE_INTERACTIONS"
	case ActType_AT_CUE_SUBMISSION:
		return "ActType_AT_CUE_SUBMISSION"
	case ActType_AT_ORDER_SUBMISSION:
		return "ActType_AT_ORDER_SUBMISSION"
	}
	return "<UNSET>"
}

func ActTypeFromString(s string) (ActType, error) {
	switch s {
	case "ActType_AT_UNKNOWN":
		return ActType_AT_UNKNOWN, nil
	case "ActType_AT_APP":
		return ActType_AT_APP, nil
	case "ActType_AT_ACTIVE":
		return ActType_AT_ACTIVE, nil
	case "ActType_AT_PAYMENT":
		return ActType_AT_PAYMENT, nil
	case "ActType_AT_OPEN":
		return ActType_AT_OPEN, nil
	case "ActType_AT_REGISTER":
		return ActType_AT_REGISTER, nil
	case "ActType_AT_FIRST_LOGIN":
		return ActType_AT_FIRST_LOGIN, nil
	case "ActType_AT_ROLE_CREATION":
		return ActType_AT_ROLE_CREATION, nil
	case "ActType_AT_LOGIN":
		return ActType_AT_LOGIN, nil
	case "ActType_AT_FIRST_PLAY":
		return ActType_AT_FIRST_PLAY, nil
	case "ActType_AT_LOGOUT":
		return ActType_AT_LOGOUT, nil
	case "ActType_AT_DOWNLOAD_START":
		return ActType_AT_DOWNLOAD_START, nil
	case "ActType_AT_DOWNLOAD_FINISH":
		return ActType_AT_DOWNLOAD_FINISH, nil
	case "ActType_AT_WAKE":
		return ActType_AT_WAKE, nil
	case "ActType_AT_APP_INSTALL":
		return ActType_AT_APP_INSTALL, nil
	case "ActType_AT_VIDEO_START":
		return ActType_AT_VIDEO_START, nil
	case "ActType_AT_VIDEO_FINISH":
		return ActType_AT_VIDEO_FINISH, nil
	case "ActType_AT_LANDING_ARRIVAL":
		return ActType_AT_LANDING_ARRIVAL, nil
	case "ActType_AT_LANDING_CLICK":
		return ActType_AT_LANDING_CLICK, nil
	case "ActType_AT_LANDING_REGISTER":
		return ActType_AT_LANDING_REGISTER, nil
	case "ActType_AT_ORDER":
		return ActType_AT_ORDER, nil
	case "ActType_AT_FIRST_WAKEUP":
		return ActType_AT_FIRST_WAKEUP, nil
	case "ActType_AT_PRODUCT_VIEW":
		return ActType_AT_PRODUCT_VIEW, nil
	case "ActType_AT_FIRST_WAKEUP_UV":
		return ActType_AT_FIRST_WAKEUP_UV, nil
	case "ActType_AT_PAGE_INTERACTIONS":
		return ActType_AT_PAGE_INTERACTIONS, nil
	case "ActType_AT_CUE_SUBMISSION":
		return ActType_AT_CUE_SUBMISSION, nil
	case "ActType_AT_ORDER_SUBMISSION":
		return ActType_AT_ORDER_SUBMISSION, nil
	}
	return ActType(math.MinInt32 - 1), fmt.Errorf("not a valid ActType string")
}

type TrackingType int64

const (
	TrackingType_UNCHECK   TrackingType = 0
	TrackingType_MACMD5    TrackingType = 1
	TrackingType_MAC       TrackingType = 2
	TrackingType_UDID      TrackingType = 3
	TrackingType_IMEI      TrackingType = 4
	TrackingType_AndroidID TrackingType = 5
)

func (p TrackingType) String() string {
	switch p {
	case TrackingType_UNCHECK:
		return "TrackingType_UNCHECK"
	case TrackingType_MACMD5:
		return "TrackingType_MACMD5"
	case TrackingType_MAC:
		return "TrackingType_MAC"
	case TrackingType_UDID:
		return "TrackingType_UDID"
	case TrackingType_IMEI:
		return "TrackingType_IMEI"
	case TrackingType_AndroidID:
		return "TrackingType_AndroidID"
	}
	return "<UNSET>"
}

func TrackingTypeFromString(s string) (TrackingType, error) {
	switch s {
	case "TrackingType_UNCHECK":
		return TrackingType_UNCHECK, nil
	case "TrackingType_MACMD5":
		return TrackingType_MACMD5, nil
	case "TrackingType_MAC":
		return TrackingType_MAC, nil
	case "TrackingType_UDID":
		return TrackingType_UDID, nil
	case "TrackingType_IMEI":
		return TrackingType_IMEI, nil
	case "TrackingType_AndroidID":
		return TrackingType_AndroidID, nil
	}
	return TrackingType(math.MinInt32 - 1), fmt.Errorf("not a valid TrackingType string")
}

//app应用类型
type AppPlatform int64

const (
	AppPlatform_APP_PLATFORM_UNKNOWN AppPlatform = 0
	AppPlatform_APP_PLATFORM_ANDROID AppPlatform = 1
	AppPlatform_APP_PLATFORM_IOS     AppPlatform = 2
	AppPlatform_APP_PLATFORM_IOS_JB  AppPlatform = 5
	AppPlatform_APP_PLATFORM_LANDING AppPlatform = 6
)

func (p AppPlatform) String() string {
	switch p {
	case AppPlatform_APP_PLATFORM_UNKNOWN:
		return "AppPlatform_APP_PLATFORM_UNKNOWN"
	case AppPlatform_APP_PLATFORM_ANDROID:
		return "AppPlatform_APP_PLATFORM_ANDROID"
	case AppPlatform_APP_PLATFORM_IOS:
		return "AppPlatform_APP_PLATFORM_IOS"
	case AppPlatform_APP_PLATFORM_IOS_JB:
		return "AppPlatform_APP_PLATFORM_IOS_JB"
	case AppPlatform_APP_PLATFORM_LANDING:
		return "AppPlatform_APP_PLATFORM_LANDING"
	}
	return "<UNSET>"
}

func AppPlatformFromString(s string) (AppPlatform, error) {
	switch s {
	case "AppPlatform_APP_PLATFORM_UNKNOWN":
		return AppPlatform_APP_PLATFORM_UNKNOWN, nil
	case "AppPlatform_APP_PLATFORM_ANDROID":
		return AppPlatform_APP_PLATFORM_ANDROID, nil
	case "AppPlatform_APP_PLATFORM_IOS":
		return AppPlatform_APP_PLATFORM_IOS, nil
	case "AppPlatform_APP_PLATFORM_IOS_JB":
		return AppPlatform_APP_PLATFORM_IOS_JB, nil
	case "AppPlatform_APP_PLATFORM_LANDING":
		return AppPlatform_APP_PLATFORM_LANDING, nil
	}
	return AppPlatform(math.MinInt32 - 1), fmt.Errorf("not a valid AppPlatform string")
}

//激活反馈方式
type FeedbackType int64

const (
	FeedbackType_FBT_UNKNOWN     FeedbackType = 0
	FeedbackType_FBT_TRACKINGSDK FeedbackType = 1
	FeedbackType_FBT_SERVERAPI   FeedbackType = 2
	FeedbackType_FBT_MANUAL      FeedbackType = 3
	FeedbackType_FBT_UNCHECK     FeedbackType = 4
)

func (p FeedbackType) String() string {
	switch p {
	case FeedbackType_FBT_UNKNOWN:
		return "FeedbackType_FBT_UNKNOWN"
	case FeedbackType_FBT_TRACKINGSDK:
		return "FeedbackType_FBT_TRACKINGSDK"
	case FeedbackType_FBT_SERVERAPI:
		return "FeedbackType_FBT_SERVERAPI"
	case FeedbackType_FBT_MANUAL:
		return "FeedbackType_FBT_MANUAL"
	case FeedbackType_FBT_UNCHECK:
		return "FeedbackType_FBT_UNCHECK"
	}
	return "<UNSET>"
}

func FeedbackTypeFromString(s string) (FeedbackType, error) {
	switch s {
	case "FeedbackType_FBT_UNKNOWN":
		return FeedbackType_FBT_UNKNOWN, nil
	case "FeedbackType_FBT_TRACKINGSDK":
		return FeedbackType_FBT_TRACKINGSDK, nil
	case "FeedbackType_FBT_SERVERAPI":
		return FeedbackType_FBT_SERVERAPI, nil
	case "FeedbackType_FBT_MANUAL":
		return FeedbackType_FBT_MANUAL, nil
	case "FeedbackType_FBT_UNCHECK":
		return FeedbackType_FBT_UNCHECK, nil
	}
	return FeedbackType(math.MinInt32 - 1), fmt.Errorf("not a valid FeedbackType string")
}

//渠道类型
type AppChannelType int64

const (
	AppChannelType_ACT_UNKNOWN    AppChannelType = 0
	AppChannelType_ACT_AD         AppChannelType = 1
	AppChannelType_ACT_OFFERWALL  AppChannelType = 2
	AppChannelType_ACT_ADWALL     AppChannelType = 3
	AppChannelType_ACT_GAMECENTER AppChannelType = 4
	AppChannelType_ACT_RTB        AppChannelType = 5
	AppChannelType_ACT_DBM        AppChannelType = 6
)

func (p AppChannelType) String() string {
	switch p {
	case AppChannelType_ACT_UNKNOWN:
		return "AppChannelType_ACT_UNKNOWN"
	case AppChannelType_ACT_AD:
		return "AppChannelType_ACT_AD"
	case AppChannelType_ACT_OFFERWALL:
		return "AppChannelType_ACT_OFFERWALL"
	case AppChannelType_ACT_ADWALL:
		return "AppChannelType_ACT_ADWALL"
	case AppChannelType_ACT_GAMECENTER:
		return "AppChannelType_ACT_GAMECENTER"
	case AppChannelType_ACT_RTB:
		return "AppChannelType_ACT_RTB"
	case AppChannelType_ACT_DBM:
		return "AppChannelType_ACT_DBM"
	}
	return "<UNSET>"
}

func AppChannelTypeFromString(s string) (AppChannelType, error) {
	switch s {
	case "AppChannelType_ACT_UNKNOWN":
		return AppChannelType_ACT_UNKNOWN, nil
	case "AppChannelType_ACT_AD":
		return AppChannelType_ACT_AD, nil
	case "AppChannelType_ACT_OFFERWALL":
		return AppChannelType_ACT_OFFERWALL, nil
	case "AppChannelType_ACT_ADWALL":
		return AppChannelType_ACT_ADWALL, nil
	case "AppChannelType_ACT_GAMECENTER":
		return AppChannelType_ACT_GAMECENTER, nil
	case "AppChannelType_ACT_RTB":
		return AppChannelType_ACT_RTB, nil
	case "AppChannelType_ACT_DBM":
		return AppChannelType_ACT_DBM, nil
	}
	return AppChannelType(math.MinInt32 - 1), fmt.Errorf("not a valid AppChannelType string")
}

//物料类型
type AppMaterialType int64

const (
	AppMaterialType_AMT_UNKNOWN          AppMaterialType = 0
	AppMaterialType_AMT_ICON             AppMaterialType = 1
	AppMaterialType_AMT_IOS_SNAPSHOT     AppMaterialType = 2
	AppMaterialType_AMT_IOS_SNAPSHOT_HD  AppMaterialType = 3
	AppMaterialType_AMT_ANDROID_SNAPSHOT AppMaterialType = 4
	AppMaterialType_AMT_THUMBNAIL        AppMaterialType = 5
)

func (p AppMaterialType) String() string {
	switch p {
	case AppMaterialType_AMT_UNKNOWN:
		return "AppMaterialType_AMT_UNKNOWN"
	case AppMaterialType_AMT_ICON:
		return "AppMaterialType_AMT_ICON"
	case AppMaterialType_AMT_IOS_SNAPSHOT:
		return "AppMaterialType_AMT_IOS_SNAPSHOT"
	case AppMaterialType_AMT_IOS_SNAPSHOT_HD:
		return "AppMaterialType_AMT_IOS_SNAPSHOT_HD"
	case AppMaterialType_AMT_ANDROID_SNAPSHOT:
		return "AppMaterialType_AMT_ANDROID_SNAPSHOT"
	case AppMaterialType_AMT_THUMBNAIL:
		return "AppMaterialType_AMT_THUMBNAIL"
	}
	return "<UNSET>"
}

func AppMaterialTypeFromString(s string) (AppMaterialType, error) {
	switch s {
	case "AppMaterialType_AMT_UNKNOWN":
		return AppMaterialType_AMT_UNKNOWN, nil
	case "AppMaterialType_AMT_ICON":
		return AppMaterialType_AMT_ICON, nil
	case "AppMaterialType_AMT_IOS_SNAPSHOT":
		return AppMaterialType_AMT_IOS_SNAPSHOT, nil
	case "AppMaterialType_AMT_IOS_SNAPSHOT_HD":
		return AppMaterialType_AMT_IOS_SNAPSHOT_HD, nil
	case "AppMaterialType_AMT_ANDROID_SNAPSHOT":
		return AppMaterialType_AMT_ANDROID_SNAPSHOT, nil
	case "AppMaterialType_AMT_THUMBNAIL":
		return AppMaterialType_AMT_THUMBNAIL, nil
	}
	return AppMaterialType(math.MinInt32 - 1), fmt.Errorf("not a valid AppMaterialType string")
}

type IdInt common.IdInt

type TimeInt common.TimeInt

type RequestHeader *common.RequestHeader

type AdCategory common.AdCategory

type AvailableStatus common.StatusWhetherAvailable

type AppQueryInt common.QueryInt

type AppQueryResult *common.QueryResult

type QueryParam struct {
	SearchField SearchField `thrift:"searchField,1" json:"searchField"`
	SearchValue string      `thrift:"searchValue,2" json:"searchValue"`
}

func NewQueryParam() *QueryParam {
	return &QueryParam{
		SearchField: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *QueryParam) IsSetSearchField() bool {
	return int64(p.SearchField) != math.MinInt32-1
}

func (p *QueryParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchField = SearchField(v)
	}
	return nil
}

func (p *QueryParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchValue = v
	}
	return nil
}

func (p *QueryParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("QueryParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryParam) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetSearchField() {
		if err := oprot.WriteFieldBegin("searchField", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:searchField: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SearchField)); err != nil {
			return fmt.Errorf("%T.searchField (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:searchField: %s", p, err)
		}
	}
	return err
}

func (p *QueryParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchValue", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchValue: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SearchValue)); err != nil {
		return fmt.Errorf("%T.searchValue (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchValue: %s", p, err)
	}
	return err
}

func (p *QueryParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryParam(%+v)", *p)
}

type AppMaterial struct {
	Id     IdInt           `thrift:"id,1" json:"id"`
	AppId  IdInt           `thrift:"appId,2" json:"appId"`
	TypeA1 AppMaterialType `thrift:"type,3" json:"type"`
	Size   int32           `thrift:"size,4" json:"size"`
	Img    int32           `thrift:"img,5" json:"img"`
}

func NewAppMaterial() *AppMaterial {
	return &AppMaterial{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppMaterial) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AppMaterial) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppMaterial) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *AppMaterial) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = IdInt(v)
	}
	return nil
}

func (p *AppMaterial) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = AppMaterialType(v)
	}
	return nil
}

func (p *AppMaterial) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *AppMaterial) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Img = v
	}
	return nil
}

func (p *AppMaterial) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppMaterial"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppMaterial) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AppMaterial) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appId: %s", p, err)
	}
	return err
}

func (p *AppMaterial) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *AppMaterial) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:size: %s", p, err)
	}
	return err
}

func (p *AppMaterial) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:img: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Img)); err != nil {
		return fmt.Errorf("%T.img (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:img: %s", p, err)
	}
	return err
}

func (p *AppMaterial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppMaterial(%+v)", *p)
}

type App struct {
	Id IdInt `thrift:"id,1" json:"id"`
	// unused field # 2
	Platform    AppPlatform    `thrift:"platform,3" json:"platform"`
	Name        string         `thrift:"name,4" json:"name"`
	Remark      string         `thrift:"remark,5" json:"remark"`
	Category    AdCategory     `thrift:"category,6" json:"category"`
	PackageName string         `thrift:"packageName,7" json:"packageName"`
	Size        int32          `thrift:"size,8" json:"size"`
	Version     string         `thrift:"version,9" json:"version"`
	VersionCode int32          `thrift:"versionCode,10" json:"versionCode"`
	Logo        int32          `thrift:"logo,11" json:"logo"`
	Url         string         `thrift:"url,12" json:"url"`
	ItunesId    IdInt          `thrift:"itunesId,13" json:"itunesId"`
	Materials   []*AppMaterial `thrift:"materials,14" json:"materials"`
	PubTime     TimeInt        `thrift:"pubTime,15" json:"pubTime"`
	UserRating  int32          `thrift:"userRating,16" json:"userRating"`
	Price       string         `thrift:"price,17" json:"price"`
	Currency    string         `thrift:"currency,18" json:"currency"`
	UpdateTime  TimeInt        `thrift:"updateTime,19" json:"updateTime"`
	IndustryId  int32          `thrift:"industryId,20" json:"industryId"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime TimeInt `thrift:"createTime,30" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,31" json:"lastUpdate"`
}

func NewApp() *App {
	return &App{
		Platform: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *App) IsSetPlatform() bool {
	return int64(p.Platform) != math.MinInt32-1
}

func (p *App) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *App) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *App) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *App) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Platform = AppPlatform(v)
	}
	return nil
}

func (p *App) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *App) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *App) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Category = AdCategory(v)
	}
	return nil
}

func (p *App) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *App) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *App) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *App) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *App) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *App) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *App) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ItunesId = IdInt(v)
	}
	return nil
}

func (p *App) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Materials = make([]*AppMaterial, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewAppMaterial()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.Materials = append(p.Materials, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *App) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.PubTime = TimeInt(v)
	}
	return nil
}

func (p *App) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.UserRating = v
	}
	return nil
}

func (p *App) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *App) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Currency = v
	}
	return nil
}

func (p *App) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.UpdateTime = TimeInt(v)
	}
	return nil
}

func (p *App) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.IndustryId = v
	}
	return nil
}

func (p *App) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *App) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *App) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("App"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *App) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *App) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetPlatform() {
		if err := oprot.WriteFieldBegin("platform", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Platform)); err != nil {
			return fmt.Errorf("%T.platform (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:platform: %s", p, err)
		}
	}
	return err
}

func (p *App) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *App) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:remark: %s", p, err)
	}
	return err
}

func (p *App) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:category: %s", p, err)
	}
	return err
}

func (p *App) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:packageName: %s", p, err)
	}
	return err
}

func (p *App) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:size: %s", p, err)
	}
	return err
}

func (p *App) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:version: %s", p, err)
	}
	return err
}

func (p *App) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionCode", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:versionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.versionCode (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:versionCode: %s", p, err)
	}
	return err
}

func (p *App) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:logo: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:logo: %s", p, err)
	}
	return err
}

func (p *App) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:url: %s", p, err)
	}
	return err
}

func (p *App) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesId", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:itunesId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunesId (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:itunesId: %s", p, err)
	}
	return err
}

func (p *App) writeField14(oprot thrift.TProtocol) (err error) {
	if p.Materials != nil {
		if err := oprot.WriteFieldBegin("materials", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:materials: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Materials)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Materials {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:materials: %s", p, err)
		}
	}
	return err
}

func (p *App) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pubTime", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:pubTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PubTime)); err != nil {
		return fmt.Errorf("%T.pubTime (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:pubTime: %s", p, err)
	}
	return err
}

func (p *App) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userRating", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:userRating: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserRating)); err != nil {
		return fmt.Errorf("%T.userRating (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:userRating: %s", p, err)
	}
	return err
}

func (p *App) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:price: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Price)); err != nil {
		return fmt.Errorf("%T.price (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:price: %s", p, err)
	}
	return err
}

func (p *App) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("currency", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:currency: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Currency)); err != nil {
		return fmt.Errorf("%T.currency (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:currency: %s", p, err)
	}
	return err
}

func (p *App) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("updateTime", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:updateTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.updateTime (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:updateTime: %s", p, err)
	}
	return err
}

func (p *App) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("industryId", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:industryId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IndustryId)); err != nil {
		return fmt.Errorf("%T.industryId (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:industryId: %s", p, err)
	}
	return err
}

func (p *App) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *App) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastUpdate: %s", p, err)
	}
	return err
}

func (p *App) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("App(%+v)", *p)
}

type AppChannel struct {
	Id                    IdInt           `thrift:"id,1" json:"id"`
	AppId                 IdInt           `thrift:"appId,2" json:"appId"`
	TypeA1                AppChannelType  `thrift:"type,3" json:"type"`
	Name                  string          `thrift:"name,4" json:"name"`
	AppIdStr              string          `thrift:"appIdStr,5" json:"appIdStr"`
	Logo                  int32           `thrift:"logo,6" json:"logo"`
	Url                   string          `thrift:"url,7" json:"url"`
	ClickUrl              string          `thrift:"clickUrl,8" json:"clickUrl"`
	ClickSenderUrl        string          `thrift:"clickSenderUrl,9" json:"clickSenderUrl"`
	FeedbackType          FeedbackType    `thrift:"feedbackType,10" json:"feedbackType"`
	Status                AvailableStatus `thrift:"status,11" json:"status"`
	SnapshotImgs          string          `thrift:"snapshotImgs,12" json:"snapshotImgs"`
	CpaPrice              int32           `thrift:"cpaPrice,13" json:"cpaPrice"`
	CpaCostLimit          int32           `thrift:"cpaCostLimit,14" json:"cpaCostLimit"`
	TrackingType          TrackingType    `thrift:"trackingType,15" json:"trackingType"`
	AppKey                string          `thrift:"appKey,16" json:"appKey"`
	AppPrivateKey         string          `thrift:"appPrivateKey,17" json:"appPrivateKey"`
	Uid                   int32           `thrift:"uid,18" json:"uid"`
	TrackingDeviceIdTypes int64           `thrift:"trackingDeviceIdTypes,19" json:"trackingDeviceIdTypes"`
	ActType               ActType         `thrift:"actType,20" json:"actType"`
	ImpSenderUrl          string          `thrift:"impSenderUrl,21" json:"impSenderUrl"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	CreateTime TimeInt `thrift:"createTime,30" json:"createTime"`
	Lastupdate TimeInt `thrift:"lastupdate,31" json:"lastupdate"`
}

func NewAppChannel() *AppChannel {
	return &AppChannel{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		FeedbackType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		TrackingType: math.MinInt32 - 1, // unset sentinal value

		ActType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppChannel) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AppChannel) IsSetFeedbackType() bool {
	return int64(p.FeedbackType) != math.MinInt32-1
}

func (p *AppChannel) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AppChannel) IsSetTrackingType() bool {
	return int64(p.TrackingType) != math.MinInt32-1
}

func (p *AppChannel) IsSetActType() bool {
	return int64(p.ActType) != math.MinInt32-1
}

func (p *AppChannel) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I64 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.I64 {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I64 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppChannel) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *AppChannel) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = IdInt(v)
	}
	return nil
}

func (p *AppChannel) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = AppChannelType(v)
	}
	return nil
}

func (p *AppChannel) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AppChannel) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AppIdStr = v
	}
	return nil
}

func (p *AppChannel) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *AppChannel) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *AppChannel) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ClickUrl = v
	}
	return nil
}

func (p *AppChannel) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ClickSenderUrl = v
	}
	return nil
}

func (p *AppChannel) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.FeedbackType = FeedbackType(v)
	}
	return nil
}

func (p *AppChannel) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Status = AvailableStatus(v)
	}
	return nil
}

func (p *AppChannel) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SnapshotImgs = v
	}
	return nil
}

func (p *AppChannel) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.CpaPrice = v
	}
	return nil
}

func (p *AppChannel) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.CpaCostLimit = v
	}
	return nil
}

func (p *AppChannel) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.TrackingType = TrackingType(v)
	}
	return nil
}

func (p *AppChannel) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.AppKey = v
	}
	return nil
}

func (p *AppChannel) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.AppPrivateKey = v
	}
	return nil
}

func (p *AppChannel) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AppChannel) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.TrackingDeviceIdTypes = v
	}
	return nil
}

func (p *AppChannel) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ActType = ActType(v)
	}
	return nil
}

func (p *AppChannel) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ImpSenderUrl = v
	}
	return nil
}

func (p *AppChannel) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AppChannel) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.Lastupdate = TimeInt(v)
	}
	return nil
}

func (p *AppChannel) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppChannel"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppChannel) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appId: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *AppChannel) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appIdStr", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:appIdStr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppIdStr)); err != nil {
		return fmt.Errorf("%T.appIdStr (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:appIdStr: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:logo: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:logo: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:url: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickUrl", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:clickUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickUrl)); err != nil {
		return fmt.Errorf("%T.clickUrl (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:clickUrl: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickSenderUrl", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:clickSenderUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickSenderUrl)); err != nil {
		return fmt.Errorf("%T.clickSenderUrl (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:clickSenderUrl: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetFeedbackType() {
		if err := oprot.WriteFieldBegin("feedbackType", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:feedbackType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FeedbackType)); err != nil {
			return fmt.Errorf("%T.feedbackType (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:feedbackType: %s", p, err)
		}
	}
	return err
}

func (p *AppChannel) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:status: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("snapshotImgs", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:snapshotImgs: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SnapshotImgs)); err != nil {
		return fmt.Errorf("%T.snapshotImgs (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:snapshotImgs: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaPrice", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:cpaPrice: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CpaPrice)); err != nil {
		return fmt.Errorf("%T.cpaPrice (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:cpaPrice: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaCostLimit", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:cpaCostLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CpaCostLimit)); err != nil {
		return fmt.Errorf("%T.cpaCostLimit (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:cpaCostLimit: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetTrackingType() {
		if err := oprot.WriteFieldBegin("trackingType", thrift.I32, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:trackingType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TrackingType)); err != nil {
			return fmt.Errorf("%T.trackingType (15) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:trackingType: %s", p, err)
		}
	}
	return err
}

func (p *AppChannel) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appKey", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:appKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppKey)); err != nil {
		return fmt.Errorf("%T.appKey (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:appKey: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appPrivateKey", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:appPrivateKey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppPrivateKey)); err != nil {
		return fmt.Errorf("%T.appPrivateKey (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:appPrivateKey: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:uid: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("trackingDeviceIdTypes", thrift.I64, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:trackingDeviceIdTypes: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TrackingDeviceIdTypes)); err != nil {
		return fmt.Errorf("%T.trackingDeviceIdTypes (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:trackingDeviceIdTypes: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetActType() {
		if err := oprot.WriteFieldBegin("actType", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:actType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ActType)); err != nil {
			return fmt.Errorf("%T.actType (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:actType: %s", p, err)
		}
	}
	return err
}

func (p *AppChannel) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impSenderUrl", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:impSenderUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpSenderUrl)); err != nil {
		return fmt.Errorf("%T.impSenderUrl (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:impSenderUrl: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:createTime: %s", p, err)
	}
	return err
}

func (p *AppChannel) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastupdate", thrift.I64, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:lastupdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Lastupdate)); err != nil {
		return fmt.Errorf("%T.lastupdate (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:lastupdate: %s", p, err)
	}
	return err
}

func (p *AppChannel) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppChannel(%+v)", *p)
}

type AppInfoAppAndChannelResult struct {
	App     *App        `thrift:"app,1" json:"app"`
	Channel *AppChannel `thrift:"channel,2" json:"channel"`
}

func NewAppInfoAppAndChannelResult() *AppInfoAppAndChannelResult {
	return &AppInfoAppAndChannelResult{}
}

func (p *AppInfoAppAndChannelResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoAppAndChannelResult) readField1(iprot thrift.TProtocol) error {
	p.App = NewApp()
	if err := p.App.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.App)
	}
	return nil
}

func (p *AppInfoAppAndChannelResult) readField2(iprot thrift.TProtocol) error {
	p.Channel = NewAppChannel()
	if err := p.Channel.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Channel)
	}
	return nil
}

func (p *AppInfoAppAndChannelResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoAppAndChannelResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoAppAndChannelResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.App != nil {
		if err := oprot.WriteFieldBegin("app", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:app: %s", p, err)
		}
		if err := p.App.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.App)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:app: %s", p, err)
		}
	}
	return err
}

func (p *AppInfoAppAndChannelResult) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Channel != nil {
		if err := oprot.WriteFieldBegin("channel", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:channel: %s", p, err)
		}
		if err := p.Channel.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Channel)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:channel: %s", p, err)
		}
	}
	return err
}

func (p *AppInfoAppAndChannelResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoAppAndChannelResult(%+v)", *p)
}

type AppChannelParams struct {
	Uid       int32          `thrift:"uid,1" json:"uid"`
	Name      string         `thrift:"name,2" json:"name"`
	TypeA1    AppChannelType `thrift:"type,3" json:"type"`
	Platforms []AppPlatform  `thrift:"platforms,4" json:"platforms"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Offset int32 `thrift:"offset,10" json:"offset"`
	Limit  int32 `thrift:"limit,11" json:"limit"`
}

func NewAppChannelParams() *AppChannelParams {
	return &AppChannelParams{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppChannelParams) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AppChannelParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppChannelParams) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = v
	}
	return nil
}

func (p *AppChannelParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AppChannelParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = AppChannelType(v)
	}
	return nil
}

func (p *AppChannelParams) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Platforms = make([]AppPlatform, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 AppPlatform
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = AppPlatform(v)
		}
		p.Platforms = append(p.Platforms, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppChannelParams) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AppChannelParams) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AppChannelParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppChannelParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppChannelParams) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AppChannelParams) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *AppChannelParams) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:type: %s", p, err)
		}
	}
	return err
}

func (p *AppChannelParams) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Platforms != nil {
		if err := oprot.WriteFieldBegin("platforms", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:platforms: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Platforms)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Platforms {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:platforms: %s", p, err)
		}
	}
	return err
}

func (p *AppChannelParams) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:offset: %s", p, err)
	}
	return err
}

func (p *AppChannelParams) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:limit: %s", p, err)
	}
	return err
}

func (p *AppChannelParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppChannelParams(%+v)", *p)
}
