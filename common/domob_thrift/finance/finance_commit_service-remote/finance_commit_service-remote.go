// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  FinancialProfile updateFinancialProfile(RequestHeader header, FinancialProfile profile)")
	fmt.Fprintln(os.Stderr, "  FinancialAccount updateFinancialAccount(RequestHeader header, FinancialAccount account)")
	fmt.Fprintln(os.<PERSON>, "  TeamMember addTeamMember(RequestHeader header, UidInt uid, TeamMember member)")
	fmt.Fprintln(os.Stderr, "  TeamMember updateTeamMember(RequestHeader header, UidInt uid, TeamMember member, bool submit)")
	fmt.Fprintln(os.Stderr, "  TeamMember submitTeamMember(RequestHeader header, UidInt uid, IdInt memberId)")
	fmt.Fprintln(os.Stderr, "  TeamMember removeTeamMember(RequestHeader header, UidInt uid, IdInt memberId)")
	fmt.Fprintln(os.Stderr, "  FinancialAccount setBeneficiary(RequestHeader header, UidInt uid, Beneficiary beneficiary)")
	fmt.Fprintln(os.Stderr, "   updateTeamMemberSharing(RequestHeader header, UidInt uid,  sharingMap)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord submitPaymentRequest(RequestHeader header, UidInt uid, Amount amount, TimeInt time, WithdrawType type)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord submitPaymentRequestWithAutoConfirmation(RequestHeader header, UidInt uid, Amount amount, TimeInt time, WithdrawType type)")
	fmt.Fprintln(os.Stderr, "  AppropriationRecord performAppropriation(RequestHeader header, UidInt agentUid, UidInt sponsorUid, Amount amount)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := finance.NewFinanceCommitServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "updateFinancialProfile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateFinancialProfile requires 2 args")
			flag.Usage()
		}
		arg744 := flag.Arg(1)
		mbTrans745 := thrift.NewTMemoryBufferLen(len(arg744))
		defer mbTrans745.Close()
		_, err746 := mbTrans745.WriteString(arg744)
		if err746 != nil {
			Usage()
			return
		}
		factory747 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt748 := factory747.GetProtocol(mbTrans745)
		argvalue0 := finance.NewRequestHeader()
		err749 := argvalue0.Read(jsProt748)
		if err749 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg750 := flag.Arg(2)
		mbTrans751 := thrift.NewTMemoryBufferLen(len(arg750))
		defer mbTrans751.Close()
		_, err752 := mbTrans751.WriteString(arg750)
		if err752 != nil {
			Usage()
			return
		}
		factory753 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt754 := factory753.GetProtocol(mbTrans751)
		argvalue1 := finance.NewFinancialProfile()
		err755 := argvalue1.Read(jsProt754)
		if err755 != nil {
			Usage()
			return
		}
		value1 := finance.FinancialProfile(argvalue1)
		fmt.Print(client.UpdateFinancialProfile(value0, value1))
		fmt.Print("\n")
		break
	case "updateFinancialAccount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateFinancialAccount requires 2 args")
			flag.Usage()
		}
		arg756 := flag.Arg(1)
		mbTrans757 := thrift.NewTMemoryBufferLen(len(arg756))
		defer mbTrans757.Close()
		_, err758 := mbTrans757.WriteString(arg756)
		if err758 != nil {
			Usage()
			return
		}
		factory759 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt760 := factory759.GetProtocol(mbTrans757)
		argvalue0 := finance.NewRequestHeader()
		err761 := argvalue0.Read(jsProt760)
		if err761 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg762 := flag.Arg(2)
		mbTrans763 := thrift.NewTMemoryBufferLen(len(arg762))
		defer mbTrans763.Close()
		_, err764 := mbTrans763.WriteString(arg762)
		if err764 != nil {
			Usage()
			return
		}
		factory765 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt766 := factory765.GetProtocol(mbTrans763)
		argvalue1 := finance.NewFinancialAccount()
		err767 := argvalue1.Read(jsProt766)
		if err767 != nil {
			Usage()
			return
		}
		value1 := finance.FinancialAccount(argvalue1)
		fmt.Print(client.UpdateFinancialAccount(value0, value1))
		fmt.Print("\n")
		break
	case "addTeamMember":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddTeamMember requires 3 args")
			flag.Usage()
		}
		arg768 := flag.Arg(1)
		mbTrans769 := thrift.NewTMemoryBufferLen(len(arg768))
		defer mbTrans769.Close()
		_, err770 := mbTrans769.WriteString(arg768)
		if err770 != nil {
			Usage()
			return
		}
		factory771 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt772 := factory771.GetProtocol(mbTrans769)
		argvalue0 := finance.NewRequestHeader()
		err773 := argvalue0.Read(jsProt772)
		if err773 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err774 := (strconv.Atoi(flag.Arg(2)))
		if err774 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg775 := flag.Arg(3)
		mbTrans776 := thrift.NewTMemoryBufferLen(len(arg775))
		defer mbTrans776.Close()
		_, err777 := mbTrans776.WriteString(arg775)
		if err777 != nil {
			Usage()
			return
		}
		factory778 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt779 := factory778.GetProtocol(mbTrans776)
		argvalue2 := finance.NewTeamMember()
		err780 := argvalue2.Read(jsProt779)
		if err780 != nil {
			Usage()
			return
		}
		value2 := finance.TeamMember(argvalue2)
		fmt.Print(client.AddTeamMember(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateTeamMember":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "UpdateTeamMember requires 4 args")
			flag.Usage()
		}
		arg781 := flag.Arg(1)
		mbTrans782 := thrift.NewTMemoryBufferLen(len(arg781))
		defer mbTrans782.Close()
		_, err783 := mbTrans782.WriteString(arg781)
		if err783 != nil {
			Usage()
			return
		}
		factory784 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt785 := factory784.GetProtocol(mbTrans782)
		argvalue0 := finance.NewRequestHeader()
		err786 := argvalue0.Read(jsProt785)
		if err786 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err787 := (strconv.Atoi(flag.Arg(2)))
		if err787 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg788 := flag.Arg(3)
		mbTrans789 := thrift.NewTMemoryBufferLen(len(arg788))
		defer mbTrans789.Close()
		_, err790 := mbTrans789.WriteString(arg788)
		if err790 != nil {
			Usage()
			return
		}
		factory791 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt792 := factory791.GetProtocol(mbTrans789)
		argvalue2 := finance.NewTeamMember()
		err793 := argvalue2.Read(jsProt792)
		if err793 != nil {
			Usage()
			return
		}
		value2 := finance.TeamMember(argvalue2)
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.UpdateTeamMember(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "submitTeamMember":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SubmitTeamMember requires 3 args")
			flag.Usage()
		}
		arg795 := flag.Arg(1)
		mbTrans796 := thrift.NewTMemoryBufferLen(len(arg795))
		defer mbTrans796.Close()
		_, err797 := mbTrans796.WriteString(arg795)
		if err797 != nil {
			Usage()
			return
		}
		factory798 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt799 := factory798.GetProtocol(mbTrans796)
		argvalue0 := finance.NewRequestHeader()
		err800 := argvalue0.Read(jsProt799)
		if err800 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err801 := (strconv.Atoi(flag.Arg(2)))
		if err801 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err802 := (strconv.Atoi(flag.Arg(3)))
		if err802 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.IdInt(argvalue2)
		fmt.Print(client.SubmitTeamMember(value0, value1, value2))
		fmt.Print("\n")
		break
	case "removeTeamMember":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "RemoveTeamMember requires 3 args")
			flag.Usage()
		}
		arg803 := flag.Arg(1)
		mbTrans804 := thrift.NewTMemoryBufferLen(len(arg803))
		defer mbTrans804.Close()
		_, err805 := mbTrans804.WriteString(arg803)
		if err805 != nil {
			Usage()
			return
		}
		factory806 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt807 := factory806.GetProtocol(mbTrans804)
		argvalue0 := finance.NewRequestHeader()
		err808 := argvalue0.Read(jsProt807)
		if err808 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err809 := (strconv.Atoi(flag.Arg(2)))
		if err809 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err810 := (strconv.Atoi(flag.Arg(3)))
		if err810 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.IdInt(argvalue2)
		fmt.Print(client.RemoveTeamMember(value0, value1, value2))
		fmt.Print("\n")
		break
	case "setBeneficiary":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SetBeneficiary requires 3 args")
			flag.Usage()
		}
		arg811 := flag.Arg(1)
		mbTrans812 := thrift.NewTMemoryBufferLen(len(arg811))
		defer mbTrans812.Close()
		_, err813 := mbTrans812.WriteString(arg811)
		if err813 != nil {
			Usage()
			return
		}
		factory814 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt815 := factory814.GetProtocol(mbTrans812)
		argvalue0 := finance.NewRequestHeader()
		err816 := argvalue0.Read(jsProt815)
		if err816 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err817 := (strconv.Atoi(flag.Arg(2)))
		if err817 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg818 := flag.Arg(3)
		mbTrans819 := thrift.NewTMemoryBufferLen(len(arg818))
		defer mbTrans819.Close()
		_, err820 := mbTrans819.WriteString(arg818)
		if err820 != nil {
			Usage()
			return
		}
		factory821 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt822 := factory821.GetProtocol(mbTrans819)
		argvalue2 := finance.NewBeneficiary()
		err823 := argvalue2.Read(jsProt822)
		if err823 != nil {
			Usage()
			return
		}
		value2 := finance.Beneficiary(argvalue2)
		fmt.Print(client.SetBeneficiary(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateTeamMemberSharing":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateTeamMemberSharing requires 3 args")
			flag.Usage()
		}
		arg824 := flag.Arg(1)
		mbTrans825 := thrift.NewTMemoryBufferLen(len(arg824))
		defer mbTrans825.Close()
		_, err826 := mbTrans825.WriteString(arg824)
		if err826 != nil {
			Usage()
			return
		}
		factory827 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt828 := factory827.GetProtocol(mbTrans825)
		argvalue0 := finance.NewRequestHeader()
		err829 := argvalue0.Read(jsProt828)
		if err829 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err830 := (strconv.Atoi(flag.Arg(2)))
		if err830 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg831 := flag.Arg(3)
		mbTrans832 := thrift.NewTMemoryBufferLen(len(arg831))
		defer mbTrans832.Close()
		_, err833 := mbTrans832.WriteString(arg831)
		if err833 != nil {
			Usage()
			return
		}
		factory834 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt835 := factory834.GetProtocol(mbTrans832)
		containerStruct2 := finance.NewUpdateTeamMemberSharingArgs()
		err836 := containerStruct2.ReadField3(jsProt835)
		if err836 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.SharingMap
		value2 := argvalue2
		fmt.Print(client.UpdateTeamMemberSharing(value0, value1, value2))
		fmt.Print("\n")
		break
	case "submitPaymentRequest":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "SubmitPaymentRequest requires 5 args")
			flag.Usage()
		}
		arg837 := flag.Arg(1)
		mbTrans838 := thrift.NewTMemoryBufferLen(len(arg837))
		defer mbTrans838.Close()
		_, err839 := mbTrans838.WriteString(arg837)
		if err839 != nil {
			Usage()
			return
		}
		factory840 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt841 := factory840.GetProtocol(mbTrans838)
		argvalue0 := finance.NewRequestHeader()
		err842 := argvalue0.Read(jsProt841)
		if err842 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err843 := (strconv.Atoi(flag.Arg(2)))
		if err843 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err844 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err844 != nil {
			Usage()
			return
		}
		value2 := finance.Amount(argvalue2)
		argvalue3, err845 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err845 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := finance.WithdrawType(tmp4)
		value4 := finance.WithdrawType(argvalue4)
		fmt.Print(client.SubmitPaymentRequest(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "submitPaymentRequestWithAutoConfirmation":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "SubmitPaymentRequestWithAutoConfirmation requires 5 args")
			flag.Usage()
		}
		arg846 := flag.Arg(1)
		mbTrans847 := thrift.NewTMemoryBufferLen(len(arg846))
		defer mbTrans847.Close()
		_, err848 := mbTrans847.WriteString(arg846)
		if err848 != nil {
			Usage()
			return
		}
		factory849 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt850 := factory849.GetProtocol(mbTrans847)
		argvalue0 := finance.NewRequestHeader()
		err851 := argvalue0.Read(jsProt850)
		if err851 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err852 := (strconv.Atoi(flag.Arg(2)))
		if err852 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err853 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err853 != nil {
			Usage()
			return
		}
		value2 := finance.Amount(argvalue2)
		argvalue3, err854 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err854 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := finance.WithdrawType(tmp4)
		value4 := finance.WithdrawType(argvalue4)
		fmt.Print(client.SubmitPaymentRequestWithAutoConfirmation(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "performAppropriation":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "PerformAppropriation requires 4 args")
			flag.Usage()
		}
		arg855 := flag.Arg(1)
		mbTrans856 := thrift.NewTMemoryBufferLen(len(arg855))
		defer mbTrans856.Close()
		_, err857 := mbTrans856.WriteString(arg855)
		if err857 != nil {
			Usage()
			return
		}
		factory858 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt859 := factory858.GetProtocol(mbTrans856)
		argvalue0 := finance.NewRequestHeader()
		err860 := argvalue0.Read(jsProt859)
		if err860 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err861 := (strconv.Atoi(flag.Arg(2)))
		if err861 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err862 := (strconv.Atoi(flag.Arg(3)))
		if err862 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err863 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err863 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		fmt.Print(client.PerformAppropriation(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
