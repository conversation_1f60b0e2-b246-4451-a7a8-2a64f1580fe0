// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = finance_types.GoUnusedProtection__

type FinanceCommitService interface { //@Description("财务系统提交服务")
	//{{{

	// #2.1 登记财务信息
	// <p>财务信息是用户信息的扩展，对广告主和开发者来说是统一的</p>
	// @return 更新后的完整财务信息
	// @throws FinanceServiceException TODO
	//
	// Parameters:
	//  - Header
	//  - Profile: 提交的财务信息，不更新的字段 unset
	UpdateFinancialProfile(header *common.RequestHeader, profile *finance_types.FinancialProfile) (r *finance_types.FinancialProfile, fe *FinanceServiceException, err error)
	// #3.1 开发者更新财务信息
	// @since 1.2.0
	//
	// Parameters:
	//  - Header
	//  - Account: 用户财务信息
	UpdateFinancialAccount(header *common.RequestHeader, account *finance_types.FinancialAccount) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error)
	// #3.2 添加团队成员
	// @since 1.2.0
	// @return 返回添加的成员信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Member
	AddTeamMember(header *common.RequestHeader, uid UidInt, member *finance_types.TeamMember) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// #3.3 更新团队成员信息
	// @since 1.2.0
	// @return 返回更新后的成员信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Member
	//  - Submit
	UpdateTeamMember(header *common.RequestHeader, uid UidInt, member *finance_types.TeamMember, submit bool) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// #3.4 提交团队成员的验证信息
	// @since 1.2.0
	// @return 返回提交的验证信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - MemberId
	SubmitTeamMember(header *common.RequestHeader, uid UidInt, memberId IdInt) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// #3.5 删除团队成员
	// @since 1.2.0
	// @return 返回删除的成员信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - MemberId
	RemoveTeamMember(header *common.RequestHeader, uid UidInt, memberId IdInt) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// #3.6 设定收款人
	// @since 1.2.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Beneficiary
	SetBeneficiary(header *common.RequestHeader, uid UidInt, beneficiary *finance_types.Beneficiary) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error)
	// #3.7 调整团队成员的分成比例
	// @since 2.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - SharingMap
	UpdateTeamMemberSharing(header *common.RequestHeader, uid UidInt, sharingMap map[IdInt]common.PercentageInt) (r map[IdInt]common.PercentageInt, fe *FinanceServiceException, err error)
	// #4.3 发起新版提现
	// @param uid 提现开发者用户 id
	// @param amount 提现金额
	// @param time 操作时间
	// @param type 提现类型(手动/自动)
	// @return 发起后的提现记录
	// @since 2.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Amount
	//  - Time
	//  - TypeA1
	SubmitPaymentRequest(header *common.RequestHeader, uid UidInt, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// #4.4 发起新版提现, 同时自动通过（在当前无需审核的流程下）
	// @param uid 提现开发者用户 id
	// @param amount 提现金额
	// @param time 操作时间
	// @param type 提现类型(手动/自动)
	// @return 发起后的提现记录
	// @since 2.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Amount
	//  - Time
	//  - TypeA1
	SubmitPaymentRequestWithAutoConfirmation(header *common.RequestHeader, uid UidInt, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// #5.1 代理商为广告主划款
	// @param agentUid 代理商 uid
	// @param sponsorUid 广告主 uid
	// @param amount 划款金额, 必须为正
	// @since 2.4.0
	// @return 操作成功后产生的流水记录
	// @throws FinanceServiceException 若操作失败
	//
	// Parameters:
	//  - Header
	//  - AgentUid
	//  - SponsorUid
	//  - Amount
	PerformAppropriation(header *common.RequestHeader, agentUid UidInt, sponsorUid UidInt, amount Amount) (r *finance_types.AppropriationRecord, fe *FinanceServiceException, err error)
}

//@Description("财务系统提交服务")
//{{{
type FinanceCommitServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewFinanceCommitServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *FinanceCommitServiceClient {
	return &FinanceCommitServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewFinanceCommitServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *FinanceCommitServiceClient {
	return &FinanceCommitServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// #2.1 登记财务信息
// <p>财务信息是用户信息的扩展，对广告主和开发者来说是统一的</p>
// @return 更新后的完整财务信息
// @throws FinanceServiceException TODO
//
// Parameters:
//  - Header
//  - Profile: 提交的财务信息，不更新的字段 unset
func (p *FinanceCommitServiceClient) UpdateFinancialProfile(header *common.RequestHeader, profile *finance_types.FinancialProfile) (r *finance_types.FinancialProfile, fe *FinanceServiceException, err error) {
	if err = p.sendUpdateFinancialProfile(header, profile); err != nil {
		return
	}
	return p.recvUpdateFinancialProfile()
}

func (p *FinanceCommitServiceClient) sendUpdateFinancialProfile(header *common.RequestHeader, profile *finance_types.FinancialProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateFinancialProfile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args694 := NewUpdateFinancialProfileArgs()
	args694.Header = header
	args694.Profile = profile
	if err = args694.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvUpdateFinancialProfile() (value *finance_types.FinancialProfile, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error696 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error697 error
		error697, err = error696.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error697
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result695 := NewUpdateFinancialProfileResult()
	if err = result695.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result695.Success
	if result695.Fe != nil {
		fe = result695.Fe
	}
	return
}

// #3.1 开发者更新财务信息
// @since 1.2.0
//
// Parameters:
//  - Header
//  - Account: 用户财务信息
func (p *FinanceCommitServiceClient) UpdateFinancialAccount(header *common.RequestHeader, account *finance_types.FinancialAccount) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	if err = p.sendUpdateFinancialAccount(header, account); err != nil {
		return
	}
	return p.recvUpdateFinancialAccount()
}

func (p *FinanceCommitServiceClient) sendUpdateFinancialAccount(header *common.RequestHeader, account *finance_types.FinancialAccount) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateFinancialAccount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args698 := NewUpdateFinancialAccountArgs()
	args698.Header = header
	args698.Account = account
	if err = args698.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvUpdateFinancialAccount() (value *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error700 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error701 error
		error701, err = error700.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error701
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result699 := NewUpdateFinancialAccountResult()
	if err = result699.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result699.Success
	if result699.Fe != nil {
		fe = result699.Fe
	}
	return
}

// #3.2 添加团队成员
// @since 1.2.0
// @return 返回添加的成员信息
//
// Parameters:
//  - Header
//  - Uid
//  - Member
func (p *FinanceCommitServiceClient) AddTeamMember(header *common.RequestHeader, uid UidInt, member *finance_types.TeamMember) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendAddTeamMember(header, uid, member); err != nil {
		return
	}
	return p.recvAddTeamMember()
}

func (p *FinanceCommitServiceClient) sendAddTeamMember(header *common.RequestHeader, uid UidInt, member *finance_types.TeamMember) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addTeamMember", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args702 := NewAddTeamMemberArgs()
	args702.Header = header
	args702.Uid = uid
	args702.Member = member
	if err = args702.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvAddTeamMember() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error704 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error705 error
		error705, err = error704.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error705
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result703 := NewAddTeamMemberResult()
	if err = result703.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result703.Success
	if result703.Fe != nil {
		fe = result703.Fe
	}
	return
}

// #3.3 更新团队成员信息
// @since 1.2.0
// @return 返回更新后的成员信息
//
// Parameters:
//  - Header
//  - Uid
//  - Member
//  - Submit
func (p *FinanceCommitServiceClient) UpdateTeamMember(header *common.RequestHeader, uid UidInt, member *finance_types.TeamMember, submit bool) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendUpdateTeamMember(header, uid, member, submit); err != nil {
		return
	}
	return p.recvUpdateTeamMember()
}

func (p *FinanceCommitServiceClient) sendUpdateTeamMember(header *common.RequestHeader, uid UidInt, member *finance_types.TeamMember, submit bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateTeamMember", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args706 := NewUpdateTeamMemberArgs()
	args706.Header = header
	args706.Uid = uid
	args706.Member = member
	args706.Submit = submit
	if err = args706.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvUpdateTeamMember() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error708 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error709 error
		error709, err = error708.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error709
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result707 := NewUpdateTeamMemberResult()
	if err = result707.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result707.Success
	if result707.Fe != nil {
		fe = result707.Fe
	}
	return
}

// #3.4 提交团队成员的验证信息
// @since 1.2.0
// @return 返回提交的验证信息
//
// Parameters:
//  - Header
//  - Uid
//  - MemberId
func (p *FinanceCommitServiceClient) SubmitTeamMember(header *common.RequestHeader, uid UidInt, memberId IdInt) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendSubmitTeamMember(header, uid, memberId); err != nil {
		return
	}
	return p.recvSubmitTeamMember()
}

func (p *FinanceCommitServiceClient) sendSubmitTeamMember(header *common.RequestHeader, uid UidInt, memberId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitTeamMember", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args710 := NewSubmitTeamMemberArgs()
	args710.Header = header
	args710.Uid = uid
	args710.MemberId = memberId
	if err = args710.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvSubmitTeamMember() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error712 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error713 error
		error713, err = error712.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error713
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result711 := NewSubmitTeamMemberResult()
	if err = result711.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result711.Success
	if result711.Fe != nil {
		fe = result711.Fe
	}
	return
}

// #3.5 删除团队成员
// @since 1.2.0
// @return 返回删除的成员信息
//
// Parameters:
//  - Header
//  - Uid
//  - MemberId
func (p *FinanceCommitServiceClient) RemoveTeamMember(header *common.RequestHeader, uid UidInt, memberId IdInt) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendRemoveTeamMember(header, uid, memberId); err != nil {
		return
	}
	return p.recvRemoveTeamMember()
}

func (p *FinanceCommitServiceClient) sendRemoveTeamMember(header *common.RequestHeader, uid UidInt, memberId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("removeTeamMember", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args714 := NewRemoveTeamMemberArgs()
	args714.Header = header
	args714.Uid = uid
	args714.MemberId = memberId
	if err = args714.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvRemoveTeamMember() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error716 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error717 error
		error717, err = error716.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error717
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result715 := NewRemoveTeamMemberResult()
	if err = result715.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result715.Success
	if result715.Fe != nil {
		fe = result715.Fe
	}
	return
}

// #3.6 设定收款人
// @since 1.2.0
//
// Parameters:
//  - Header
//  - Uid
//  - Beneficiary
func (p *FinanceCommitServiceClient) SetBeneficiary(header *common.RequestHeader, uid UidInt, beneficiary *finance_types.Beneficiary) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	if err = p.sendSetBeneficiary(header, uid, beneficiary); err != nil {
		return
	}
	return p.recvSetBeneficiary()
}

func (p *FinanceCommitServiceClient) sendSetBeneficiary(header *common.RequestHeader, uid UidInt, beneficiary *finance_types.Beneficiary) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("setBeneficiary", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args718 := NewSetBeneficiaryArgs()
	args718.Header = header
	args718.Uid = uid
	args718.Beneficiary = beneficiary
	if err = args718.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvSetBeneficiary() (value *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error720 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error721 error
		error721, err = error720.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error721
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result719 := NewSetBeneficiaryResult()
	if err = result719.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result719.Success
	if result719.Fe != nil {
		fe = result719.Fe
	}
	return
}

// #3.7 调整团队成员的分成比例
// @since 2.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - SharingMap
func (p *FinanceCommitServiceClient) UpdateTeamMemberSharing(header *common.RequestHeader, uid UidInt, sharingMap map[IdInt]common.PercentageInt) (r map[IdInt]common.PercentageInt, fe *FinanceServiceException, err error) {
	if err = p.sendUpdateTeamMemberSharing(header, uid, sharingMap); err != nil {
		return
	}
	return p.recvUpdateTeamMemberSharing()
}

func (p *FinanceCommitServiceClient) sendUpdateTeamMemberSharing(header *common.RequestHeader, uid UidInt, sharingMap map[IdInt]common.PercentageInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateTeamMemberSharing", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args722 := NewUpdateTeamMemberSharingArgs()
	args722.Header = header
	args722.Uid = uid
	args722.SharingMap = sharingMap
	if err = args722.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvUpdateTeamMemberSharing() (value map[IdInt]common.PercentageInt, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error724 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error725 error
		error725, err = error724.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error725
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result723 := NewUpdateTeamMemberSharingResult()
	if err = result723.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result723.Success
	if result723.Fe != nil {
		fe = result723.Fe
	}
	return
}

// #4.3 发起新版提现
// @param uid 提现开发者用户 id
// @param amount 提现金额
// @param time 操作时间
// @param type 提现类型(手动/自动)
// @return 发起后的提现记录
// @since 2.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - Amount
//  - Time
//  - TypeA1
func (p *FinanceCommitServiceClient) SubmitPaymentRequest(header *common.RequestHeader, uid UidInt, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendSubmitPaymentRequest(header, uid, amount, time, type_a1); err != nil {
		return
	}
	return p.recvSubmitPaymentRequest()
}

func (p *FinanceCommitServiceClient) sendSubmitPaymentRequest(header *common.RequestHeader, uid UidInt, amount Amount, time TimeInt, type_a1 WithdrawType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitPaymentRequest", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args726 := NewSubmitPaymentRequestArgs()
	args726.Header = header
	args726.Uid = uid
	args726.Amount = amount
	args726.Time = time
	args726.TypeA1 = type_a1
	if err = args726.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvSubmitPaymentRequest() (value *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error728 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error729 error
		error729, err = error728.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error729
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result727 := NewSubmitPaymentRequestResult()
	if err = result727.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result727.Success
	if result727.Fe != nil {
		fe = result727.Fe
	}
	return
}

// #4.4 发起新版提现, 同时自动通过（在当前无需审核的流程下）
// @param uid 提现开发者用户 id
// @param amount 提现金额
// @param time 操作时间
// @param type 提现类型(手动/自动)
// @return 发起后的提现记录
// @since 2.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - Amount
//  - Time
//  - TypeA1
func (p *FinanceCommitServiceClient) SubmitPaymentRequestWithAutoConfirmation(header *common.RequestHeader, uid UidInt, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendSubmitPaymentRequestWithAutoConfirmation(header, uid, amount, time, type_a1); err != nil {
		return
	}
	return p.recvSubmitPaymentRequestWithAutoConfirmation()
}

func (p *FinanceCommitServiceClient) sendSubmitPaymentRequestWithAutoConfirmation(header *common.RequestHeader, uid UidInt, amount Amount, time TimeInt, type_a1 WithdrawType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitPaymentRequestWithAutoConfirmation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args730 := NewSubmitPaymentRequestWithAutoConfirmationArgs()
	args730.Header = header
	args730.Uid = uid
	args730.Amount = amount
	args730.Time = time
	args730.TypeA1 = type_a1
	if err = args730.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvSubmitPaymentRequestWithAutoConfirmation() (value *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error732 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error733 error
		error733, err = error732.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error733
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result731 := NewSubmitPaymentRequestWithAutoConfirmationResult()
	if err = result731.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result731.Success
	if result731.Fe != nil {
		fe = result731.Fe
	}
	return
}

// #5.1 代理商为广告主划款
// @param agentUid 代理商 uid
// @param sponsorUid 广告主 uid
// @param amount 划款金额, 必须为正
// @since 2.4.0
// @return 操作成功后产生的流水记录
// @throws FinanceServiceException 若操作失败
//
// Parameters:
//  - Header
//  - AgentUid
//  - SponsorUid
//  - Amount
func (p *FinanceCommitServiceClient) PerformAppropriation(header *common.RequestHeader, agentUid UidInt, sponsorUid UidInt, amount Amount) (r *finance_types.AppropriationRecord, fe *FinanceServiceException, err error) {
	if err = p.sendPerformAppropriation(header, agentUid, sponsorUid, amount); err != nil {
		return
	}
	return p.recvPerformAppropriation()
}

func (p *FinanceCommitServiceClient) sendPerformAppropriation(header *common.RequestHeader, agentUid UidInt, sponsorUid UidInt, amount Amount) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("performAppropriation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args734 := NewPerformAppropriationArgs()
	args734.Header = header
	args734.AgentUid = agentUid
	args734.SponsorUid = sponsorUid
	args734.Amount = amount
	if err = args734.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceCommitServiceClient) recvPerformAppropriation() (value *finance_types.AppropriationRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error736 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error737 error
		error737, err = error736.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error737
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result735 := NewPerformAppropriationResult()
	if err = result735.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result735.Success
	if result735.Fe != nil {
		fe = result735.Fe
	}
	return
}

type FinanceCommitServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      FinanceCommitService
}

func (p *FinanceCommitServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *FinanceCommitServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *FinanceCommitServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewFinanceCommitServiceProcessor(handler FinanceCommitService) *FinanceCommitServiceProcessor {

	self738 := &FinanceCommitServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self738.processorMap["updateFinancialProfile"] = &financeCommitServiceProcessorUpdateFinancialProfile{handler: handler}
	self738.processorMap["updateFinancialAccount"] = &financeCommitServiceProcessorUpdateFinancialAccount{handler: handler}
	self738.processorMap["addTeamMember"] = &financeCommitServiceProcessorAddTeamMember{handler: handler}
	self738.processorMap["updateTeamMember"] = &financeCommitServiceProcessorUpdateTeamMember{handler: handler}
	self738.processorMap["submitTeamMember"] = &financeCommitServiceProcessorSubmitTeamMember{handler: handler}
	self738.processorMap["removeTeamMember"] = &financeCommitServiceProcessorRemoveTeamMember{handler: handler}
	self738.processorMap["setBeneficiary"] = &financeCommitServiceProcessorSetBeneficiary{handler: handler}
	self738.processorMap["updateTeamMemberSharing"] = &financeCommitServiceProcessorUpdateTeamMemberSharing{handler: handler}
	self738.processorMap["submitPaymentRequest"] = &financeCommitServiceProcessorSubmitPaymentRequest{handler: handler}
	self738.processorMap["submitPaymentRequestWithAutoConfirmation"] = &financeCommitServiceProcessorSubmitPaymentRequestWithAutoConfirmation{handler: handler}
	self738.processorMap["performAppropriation"] = &financeCommitServiceProcessorPerformAppropriation{handler: handler}
	return self738
}

func (p *FinanceCommitServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x739 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x739.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x739

}

type financeCommitServiceProcessorUpdateFinancialProfile struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorUpdateFinancialProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateFinancialProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateFinancialProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateFinancialProfileResult()
	if result.Success, result.Fe, err = p.handler.UpdateFinancialProfile(args.Header, args.Profile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateFinancialProfile: "+err.Error())
		oprot.WriteMessageBegin("updateFinancialProfile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateFinancialProfile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorUpdateFinancialAccount struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorUpdateFinancialAccount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateFinancialAccountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateFinancialAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateFinancialAccountResult()
	if result.Success, result.Fe, err = p.handler.UpdateFinancialAccount(args.Header, args.Account); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateFinancialAccount: "+err.Error())
		oprot.WriteMessageBegin("updateFinancialAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateFinancialAccount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorAddTeamMember struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorAddTeamMember) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddTeamMemberArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddTeamMemberResult()
	if result.Success, result.Fe, err = p.handler.AddTeamMember(args.Header, args.Uid, args.Member); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addTeamMember: "+err.Error())
		oprot.WriteMessageBegin("addTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addTeamMember", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorUpdateTeamMember struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorUpdateTeamMember) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateTeamMemberArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateTeamMemberResult()
	if result.Success, result.Fe, err = p.handler.UpdateTeamMember(args.Header, args.Uid, args.Member, args.Submit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateTeamMember: "+err.Error())
		oprot.WriteMessageBegin("updateTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateTeamMember", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorSubmitTeamMember struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorSubmitTeamMember) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitTeamMemberArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitTeamMemberResult()
	if result.Success, result.Fe, err = p.handler.SubmitTeamMember(args.Header, args.Uid, args.MemberId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitTeamMember: "+err.Error())
		oprot.WriteMessageBegin("submitTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitTeamMember", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorRemoveTeamMember struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorRemoveTeamMember) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRemoveTeamMemberArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("removeTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRemoveTeamMemberResult()
	if result.Success, result.Fe, err = p.handler.RemoveTeamMember(args.Header, args.Uid, args.MemberId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing removeTeamMember: "+err.Error())
		oprot.WriteMessageBegin("removeTeamMember", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("removeTeamMember", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorSetBeneficiary struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorSetBeneficiary) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSetBeneficiaryArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("setBeneficiary", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSetBeneficiaryResult()
	if result.Success, result.Fe, err = p.handler.SetBeneficiary(args.Header, args.Uid, args.Beneficiary); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing setBeneficiary: "+err.Error())
		oprot.WriteMessageBegin("setBeneficiary", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("setBeneficiary", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorUpdateTeamMemberSharing struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorUpdateTeamMemberSharing) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateTeamMemberSharingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateTeamMemberSharing", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateTeamMemberSharingResult()
	if result.Success, result.Fe, err = p.handler.UpdateTeamMemberSharing(args.Header, args.Uid, args.SharingMap); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateTeamMemberSharing: "+err.Error())
		oprot.WriteMessageBegin("updateTeamMemberSharing", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateTeamMemberSharing", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorSubmitPaymentRequest struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorSubmitPaymentRequest) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitPaymentRequestArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitPaymentRequest", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitPaymentRequestResult()
	if result.Success, result.Fe, err = p.handler.SubmitPaymentRequest(args.Header, args.Uid, args.Amount, args.Time, args.TypeA1); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitPaymentRequest: "+err.Error())
		oprot.WriteMessageBegin("submitPaymentRequest", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitPaymentRequest", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorSubmitPaymentRequestWithAutoConfirmation struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorSubmitPaymentRequestWithAutoConfirmation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitPaymentRequestWithAutoConfirmationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitPaymentRequestWithAutoConfirmation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitPaymentRequestWithAutoConfirmationResult()
	if result.Success, result.Fe, err = p.handler.SubmitPaymentRequestWithAutoConfirmation(args.Header, args.Uid, args.Amount, args.Time, args.TypeA1); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitPaymentRequestWithAutoConfirmation: "+err.Error())
		oprot.WriteMessageBegin("submitPaymentRequestWithAutoConfirmation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitPaymentRequestWithAutoConfirmation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeCommitServiceProcessorPerformAppropriation struct {
	handler FinanceCommitService
}

func (p *financeCommitServiceProcessorPerformAppropriation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPerformAppropriationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("performAppropriation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPerformAppropriationResult()
	if result.Success, result.Fe, err = p.handler.PerformAppropriation(args.Header, args.AgentUid, args.SponsorUid, args.Amount); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing performAppropriation: "+err.Error())
		oprot.WriteMessageBegin("performAppropriation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("performAppropriation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type UpdateFinancialProfileArgs struct {
	Header  *common.RequestHeader           `thrift:"header,1" json:"header"`
	Profile *finance_types.FinancialProfile `thrift:"profile,2" json:"profile"`
}

func NewUpdateFinancialProfileArgs() *UpdateFinancialProfileArgs {
	return &UpdateFinancialProfileArgs{}
}

func (p *UpdateFinancialProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateFinancialProfileArgs) readField2(iprot thrift.TProtocol) error {
	p.Profile = finance_types.NewFinancialProfile()
	if err := p.Profile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Profile)
	}
	return nil
}

func (p *UpdateFinancialProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateFinancialProfile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialProfileArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Profile != nil {
		if err := oprot.WriteFieldBegin("profile", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:profile: %s", p, err)
		}
		if err := p.Profile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Profile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:profile: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFinancialProfileArgs(%+v)", *p)
}

type UpdateFinancialProfileResult struct {
	Success *finance_types.FinancialProfile `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException        `thrift:"fe,1" json:"fe"`
}

func NewUpdateFinancialProfileResult() *UpdateFinancialProfileResult {
	return &UpdateFinancialProfileResult{}
}

func (p *UpdateFinancialProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialProfileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewFinancialProfile()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *UpdateFinancialProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdateFinancialProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateFinancialProfile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFinancialProfileResult(%+v)", *p)
}

type UpdateFinancialAccountArgs struct {
	Header  *common.RequestHeader           `thrift:"header,1" json:"header"`
	Account *finance_types.FinancialAccount `thrift:"account,2" json:"account"`
}

func NewUpdateFinancialAccountArgs() *UpdateFinancialAccountArgs {
	return &UpdateFinancialAccountArgs{}
}

func (p *UpdateFinancialAccountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateFinancialAccountArgs) readField2(iprot thrift.TProtocol) error {
	p.Account = finance_types.NewFinancialAccount()
	if err := p.Account.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Account)
	}
	return nil
}

func (p *UpdateFinancialAccountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateFinancialAccount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Account != nil {
		if err := oprot.WriteFieldBegin("account", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:account: %s", p, err)
		}
		if err := p.Account.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Account)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:account: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFinancialAccountArgs(%+v)", *p)
}

type UpdateFinancialAccountResult struct {
	Success *finance_types.FinancialAccount `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException        `thrift:"fe,1" json:"fe"`
}

func NewUpdateFinancialAccountResult() *UpdateFinancialAccountResult {
	return &UpdateFinancialAccountResult{}
}

func (p *UpdateFinancialAccountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewFinancialAccount()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *UpdateFinancialAccountResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdateFinancialAccountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateFinancialAccount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFinancialAccountResult(%+v)", *p)
}

type AddTeamMemberArgs struct {
	Header *common.RequestHeader     `thrift:"header,1" json:"header"`
	Uid    UidInt                    `thrift:"uid,2" json:"uid"`
	Member *finance_types.TeamMember `thrift:"member,3" json:"member"`
}

func NewAddTeamMemberArgs() *AddTeamMemberArgs {
	return &AddTeamMemberArgs{}
}

func (p *AddTeamMemberArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTeamMemberArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AddTeamMemberArgs) readField3(iprot thrift.TProtocol) error {
	p.Member = finance_types.NewTeamMember()
	if err := p.Member.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Member)
	}
	return nil
}

func (p *AddTeamMemberArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTeamMember_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AddTeamMemberArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Member != nil {
		if err := oprot.WriteFieldBegin("member", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:member: %s", p, err)
		}
		if err := p.Member.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Member)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:member: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTeamMemberArgs(%+v)", *p)
}

type AddTeamMemberResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewAddTeamMemberResult() *AddTeamMemberResult {
	return &AddTeamMemberResult{}
}

func (p *AddTeamMemberResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddTeamMemberResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *AddTeamMemberResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTeamMember_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTeamMemberResult(%+v)", *p)
}

type UpdateTeamMemberArgs struct {
	Header *common.RequestHeader     `thrift:"header,1" json:"header"`
	Uid    UidInt                    `thrift:"uid,2" json:"uid"`
	Member *finance_types.TeamMember `thrift:"member,3" json:"member"`
	Submit bool                      `thrift:"submit,4" json:"submit"`
}

func NewUpdateTeamMemberArgs() *UpdateTeamMemberArgs {
	return &UpdateTeamMemberArgs{}
}

func (p *UpdateTeamMemberArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateTeamMemberArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *UpdateTeamMemberArgs) readField3(iprot thrift.TProtocol) error {
	p.Member = finance_types.NewTeamMember()
	if err := p.Member.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Member)
	}
	return nil
}

func (p *UpdateTeamMemberArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Submit = v
	}
	return nil
}

func (p *UpdateTeamMemberArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMember_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Member != nil {
		if err := oprot.WriteFieldBegin("member", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:member: %s", p, err)
		}
		if err := p.Member.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Member)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:member: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("submit", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:submit: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Submit)); err != nil {
		return fmt.Errorf("%T.submit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:submit: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberArgs(%+v)", *p)
}

type UpdateTeamMemberResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewUpdateTeamMemberResult() *UpdateTeamMemberResult {
	return &UpdateTeamMemberResult{}
}

func (p *UpdateTeamMemberResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *UpdateTeamMemberResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdateTeamMemberResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMember_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberResult(%+v)", *p)
}

type SubmitTeamMemberArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid      UidInt                `thrift:"uid,2" json:"uid"`
	MemberId IdInt                 `thrift:"memberId,3" json:"memberId"`
}

func NewSubmitTeamMemberArgs() *SubmitTeamMemberArgs {
	return &SubmitTeamMemberArgs{}
}

func (p *SubmitTeamMemberArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMemberArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitTeamMemberArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SubmitTeamMemberArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MemberId = IdInt(v)
	}
	return nil
}

func (p *SubmitTeamMemberArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitTeamMember_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMemberArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitTeamMemberArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SubmitTeamMemberArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memberId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:memberId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MemberId)); err != nil {
		return fmt.Errorf("%T.memberId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:memberId: %s", p, err)
	}
	return err
}

func (p *SubmitTeamMemberArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitTeamMemberArgs(%+v)", *p)
}

type SubmitTeamMemberResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewSubmitTeamMemberResult() *SubmitTeamMemberResult {
	return &SubmitTeamMemberResult{}
}

func (p *SubmitTeamMemberResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMemberResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SubmitTeamMemberResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SubmitTeamMemberResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitTeamMember_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMemberResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SubmitTeamMemberResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SubmitTeamMemberResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitTeamMemberResult(%+v)", *p)
}

type RemoveTeamMemberArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid      UidInt                `thrift:"uid,2" json:"uid"`
	MemberId IdInt                 `thrift:"memberId,3" json:"memberId"`
}

func NewRemoveTeamMemberArgs() *RemoveTeamMemberArgs {
	return &RemoveTeamMemberArgs{}
}

func (p *RemoveTeamMemberArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RemoveTeamMemberArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *RemoveTeamMemberArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MemberId = IdInt(v)
	}
	return nil
}

func (p *RemoveTeamMemberArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeTeamMember_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RemoveTeamMemberArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *RemoveTeamMemberArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memberId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:memberId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MemberId)); err != nil {
		return fmt.Errorf("%T.memberId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:memberId: %s", p, err)
	}
	return err
}

func (p *RemoveTeamMemberArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveTeamMemberArgs(%+v)", *p)
}

type RemoveTeamMemberResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewRemoveTeamMemberResult() *RemoveTeamMemberResult {
	return &RemoveTeamMemberResult{}
}

func (p *RemoveTeamMemberResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RemoveTeamMemberResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *RemoveTeamMemberResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeTeamMember_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RemoveTeamMemberResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *RemoveTeamMemberResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveTeamMemberResult(%+v)", *p)
}

type SetBeneficiaryArgs struct {
	Header      *common.RequestHeader      `thrift:"header,1" json:"header"`
	Uid         UidInt                     `thrift:"uid,2" json:"uid"`
	Beneficiary *finance_types.Beneficiary `thrift:"beneficiary,3" json:"beneficiary"`
}

func NewSetBeneficiaryArgs() *SetBeneficiaryArgs {
	return &SetBeneficiaryArgs{}
}

func (p *SetBeneficiaryArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SetBeneficiaryArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SetBeneficiaryArgs) readField3(iprot thrift.TProtocol) error {
	p.Beneficiary = finance_types.NewBeneficiary()
	if err := p.Beneficiary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Beneficiary)
	}
	return nil
}

func (p *SetBeneficiaryArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setBeneficiary_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SetBeneficiaryArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Beneficiary != nil {
		if err := oprot.WriteFieldBegin("beneficiary", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:beneficiary: %s", p, err)
		}
		if err := p.Beneficiary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Beneficiary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:beneficiary: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetBeneficiaryArgs(%+v)", *p)
}

type SetBeneficiaryResult struct {
	Success *finance_types.FinancialAccount `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException        `thrift:"fe,1" json:"fe"`
}

func NewSetBeneficiaryResult() *SetBeneficiaryResult {
	return &SetBeneficiaryResult{}
}

func (p *SetBeneficiaryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewFinancialAccount()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SetBeneficiaryResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SetBeneficiaryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setBeneficiary_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetBeneficiaryResult(%+v)", *p)
}

type UpdateTeamMemberSharingArgs struct {
	Header     *common.RequestHeader          `thrift:"header,1" json:"header"`
	Uid        UidInt                         `thrift:"uid,2" json:"uid"`
	SharingMap map[IdInt]common.PercentageInt `thrift:"sharingMap,3" json:"sharingMap"`
}

func NewUpdateTeamMemberSharingArgs() *UpdateTeamMemberSharingArgs {
	return &UpdateTeamMemberSharingArgs{}
}

func (p *UpdateTeamMemberSharingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateTeamMemberSharingArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *UpdateTeamMemberSharingArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.SharingMap = make(map[IdInt]common.PercentageInt, size)
	for i := 0; i < size; i++ {
		var _key740 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key740 = IdInt(v)
		}
		var _val741 common.PercentageInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val741 = common.PercentageInt(v)
		}
		p.SharingMap[_key740] = _val741
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMemberSharing_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberSharingArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SharingMap != nil {
		if err := oprot.WriteFieldBegin("sharingMap", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sharingMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.SharingMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.SharingMap {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sharingMap: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberSharingArgs(%+v)", *p)
}

type UpdateTeamMemberSharingResult struct {
	Success map[IdInt]common.PercentageInt `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException       `thrift:"fe,1" json:"fe"`
}

func NewUpdateTeamMemberSharingResult() *UpdateTeamMemberSharingResult {
	return &UpdateTeamMemberSharingResult{}
}

func (p *UpdateTeamMemberSharingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[IdInt]common.PercentageInt, size)
	for i := 0; i < size; i++ {
		var _key742 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key742 = IdInt(v)
		}
		var _val743 common.PercentageInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val743 = common.PercentageInt(v)
		}
		p.Success[_key742] = _val743
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdateTeamMemberSharingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMemberSharing_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberSharingResult(%+v)", *p)
}

type SubmitPaymentRequestArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    UidInt                `thrift:"uid,2" json:"uid"`
	Amount Amount                `thrift:"amount,3" json:"amount"`
	Time   TimeInt               `thrift:"time,4" json:"time"`
	TypeA1 WithdrawType          `thrift:"type,5" json:"type"`
}

func NewSubmitPaymentRequestArgs() *SubmitPaymentRequestArgs {
	return &SubmitPaymentRequestArgs{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SubmitPaymentRequestArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SubmitPaymentRequestArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitPaymentRequestArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = Amount(v)
	}
	return nil
}

func (p *SubmitPaymentRequestArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = WithdrawType(v)
	}
	return nil
}

func (p *SubmitPaymentRequestArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequest_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:time: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:type: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestArgs(%+v)", *p)
}

type SubmitPaymentRequestResult struct {
	Success *finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException     `thrift:"fe,1" json:"fe"`
}

func NewSubmitPaymentRequestResult() *SubmitPaymentRequestResult {
	return &SubmitPaymentRequestResult{}
}

func (p *SubmitPaymentRequestResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SubmitPaymentRequestResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SubmitPaymentRequestResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequest_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestResult(%+v)", *p)
}

type SubmitPaymentRequestWithAutoConfirmationArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    UidInt                `thrift:"uid,2" json:"uid"`
	Amount Amount                `thrift:"amount,3" json:"amount"`
	Time   TimeInt               `thrift:"time,4" json:"time"`
	TypeA1 WithdrawType          `thrift:"type,5" json:"type"`
}

func NewSubmitPaymentRequestWithAutoConfirmationArgs() *SubmitPaymentRequestWithAutoConfirmationArgs {
	return &SubmitPaymentRequestWithAutoConfirmationArgs{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = Amount(v)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = WithdrawType(v)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequestWithAutoConfirmation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:time: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:type: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestWithAutoConfirmationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestWithAutoConfirmationArgs(%+v)", *p)
}

type SubmitPaymentRequestWithAutoConfirmationResult struct {
	Success *finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException     `thrift:"fe,1" json:"fe"`
}

func NewSubmitPaymentRequestWithAutoConfirmationResult() *SubmitPaymentRequestWithAutoConfirmationResult {
	return &SubmitPaymentRequestWithAutoConfirmationResult{}
}

func (p *SubmitPaymentRequestWithAutoConfirmationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequestWithAutoConfirmation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestWithAutoConfirmationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestWithAutoConfirmationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestWithAutoConfirmationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestWithAutoConfirmationResult(%+v)", *p)
}

type PerformAppropriationArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid   UidInt                `thrift:"agentUid,2" json:"agentUid"`
	SponsorUid UidInt                `thrift:"sponsorUid,3" json:"sponsorUid"`
	Amount     Amount                `thrift:"amount,4" json:"amount"`
}

func NewPerformAppropriationArgs() *PerformAppropriationArgs {
	return &PerformAppropriationArgs{}
}

func (p *PerformAppropriationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PerformAppropriationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = UidInt(v)
	}
	return nil
}

func (p *PerformAppropriationArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorUid = UidInt(v)
	}
	return nil
}

func (p *PerformAppropriationArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Amount = Amount(v)
	}
	return nil
}

func (p *PerformAppropriationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("performAppropriation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PerformAppropriationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorUid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorUid)); err != nil {
		return fmt.Errorf("%T.sponsorUid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorUid: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:amount: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PerformAppropriationArgs(%+v)", *p)
}

type PerformAppropriationResult struct {
	Success *finance_types.AppropriationRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException           `thrift:"fe,1" json:"fe"`
}

func NewPerformAppropriationResult() *PerformAppropriationResult {
	return &PerformAppropriationResult{}
}

func (p *PerformAppropriationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewAppropriationRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PerformAppropriationResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *PerformAppropriationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("performAppropriation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PerformAppropriationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *PerformAppropriationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PerformAppropriationResult(%+v)", *p)
}
