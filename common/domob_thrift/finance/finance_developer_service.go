// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = finance_types.GoUnusedProtection__

type FinanceDeveloperService interface { //@Description("开发者相关财务服务")
	//@since 3.1.0
	//{{{

	// {{{ #10.1 查询新版提现记录, 新接口, 支持多渠道
	// @param header requestHeader
	// @param uid 指定查询的开发者用户 id, 对于内部调用, 允许为0, 取得所有用户的列表
	// @param cooperators 指定查询的 cooperator 集合, 若为空, 查询所有 cooperator
	// @param fromTime 开始时间, unix time
	// @param toTime 结束时间, unix time
	// @param statuses 指定查询的状态集合, 若为空, 查询所有状态
	// @param offset offset
	// @param limit limit, 最大 50, 对于内部调用, 允许为 0 表示取所有
	// @return 匹配的记录列表
	// @TODO 内部调用的验证方式暂未提供, 需要时, 可以考虑通过 header.requester 简单校验
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperators
	//  - FromTime
	//  - ToTime
	//  - Statuses
	//  - Offset
	//  - Limit
	ListPaymentRecordEx(header *common.RequestHeader, uid UidInt, cooperators []common.CooperateCompany, fromTime TimeInt, toTime TimeInt, statuses []OperationStatus, offset int32, limit int32) (r *finance_types.PaymentRecordListResult, fe *FinanceServiceException, err error)
	// {{{ #10.2 获取新版提现记录
	// @param header requestHeader
	// @param uid 指定查询的开发者用户 id
	// @param ids 提现记录 id 列表
	// @return 匹配的记录列表
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Ids
	GetPaymentRecordEx(header *common.RequestHeader, uid UidInt, ids []IdInt) (r map[IdInt]*finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// {{{ #10.3 发起新版提现, 新版, 支持多渠道
	// @param header request header
	// @param uid 提现开发者用户 id
	// @param cooperator 合作公司/渠道
	// @param amount 提现金额
	// @param time 操作时间
	// @param type 提现类型(手动/自动)
	// @return 发起后的提现记录
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	//  - Amount
	//  - Time
	//  - TypeA1
	SubmitPaymentRequestEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// {{{ #10.4 发起新版提现, 并同时自动审核通过(适用无审核流程的场景), 新版, 支持多渠道
	// @param header request header
	// @param uid 提现开发者用户 id
	// @param cooperator 合作公司/渠道
	// @param amount 提现金额
	// @param time 操作时间
	// @param type 提现类型(手动/自动)
	// @return 发起后的提现记录
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	//  - Amount
	//  - Time
	//  - TypeA1
	SubmitPaymentRequestExWithAutoConfirmation(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// {{{ #10.5 确认新版提现申请, 新版, 支持多渠道
	// @since 3.1.0
	// @param header request header
	// @param id 记录 id
	// @param uid 开发者 id, 用于校验
	// @param cooperator 合作公司/渠道, 用于校验
	// @param confirmedAmount 确认的提现金额
	// @param adminUid 操作管理员的 id
	// @param extInfo 其他附加信息
	// @return 操作后的提现申请记录
	//
	// Parameters:
	//  - Header
	//  - Id
	//  - Uid
	//  - Cooperator
	//  - ConfirmedAmount
	//  - AdminUid
	//  - ExtInfo
	ConfirmPaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, confirmedAmount Amount, adminUid UidInt, extInfo map[string]string) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// {{{ #10.6 拒绝新版提现申请, 新版, 支持多渠道
	// @since 3.1.0
	// @param header request header
	// @param id 记录 id
	// @param uid 开发者 id
	// @param cooperator 合作公司/渠道
	// @param requestedAmount 申请的金额
	// @param adminUid 操作管理员的 id
	// @param extInfo 其他附加信息, 建议将理由以 reason 为 key 写入
	// @return 操作后的提现申请记录
	//
	// Parameters:
	//  - Header
	//  - Id
	//  - Uid
	//  - Cooperator
	//  - RequestedAmount
	//  - AdminUid
	//  - ExtInfo
	DenyPaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, requestedAmount Amount, adminUid UidInt, extInfo map[string]string) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// {{{ #10.7 完成新版提现申请, 新版, 支持多渠道
	// @since 3.1.0
	// @param id 记录 id
	// @param uid 开发者 id, 用于校验
	// @param cooperator 合作公司/渠道, 用于校验
	// @param paidAmount 支付金额
	// @param paymentFee 支付手续费
	// @param adminUid 操作管理员的 id
	// @param extInfo 其他附加信息
	// @return 操作后的提现申请记录
	//
	// Parameters:
	//  - Header
	//  - Id
	//  - Uid
	//  - Cooperator
	//  - PaidAmount
	//  - PaymentFee
	//  - AdminUid
	//  - ExtInfo
	CompletePaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, paidAmount Amount, paymentFee Amount, adminUid UidInt, extInfo map[string]string) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// {{{ #11.1 获取某用户的团队成员信息, 新接口, 支持多渠道
	// @param header requestHeader
	// @param uid 用户 uid
	// @param cooperator 合作公司/渠道
	// @return 全部满足查询条件的团队成员
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	ListTeamMembersEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany) (r []*finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.2 后台团队成员列表, 新接口, 支持多渠道
	// @param header requestHeader
	// @param cooperator 合作公司/渠道
	// @param uid 查询的用户 id, 可以指定 0 获取全部, 后台通常是获取全部的
	// @param statuses 查询的审核状态列表, 可以指定空列表, 取全部状态
	// @param offset 分页 offset
	// @param limit 分页 limit, 最大 50, 可以指定 0 获取全部, 但不推荐使用
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - Cooperator
	//  - Uid
	//  - Statuses
	//  - Offset
	//  - Limit
	ListTeamMembersExForAdmin(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, statuses []CommonAuditStatus, offset QueryInt, limit QueryInt) (r *finance_types.TeamMemberListResult, fe *FinanceServiceException, err error)
	// {{{ #11.3 根据证件号码搜索团队成员, 新接口, 支持多渠道
	// @param header requestHeader
	// @param type 证件类型
	// @param identities 证件号码, 支持多个
	// @param cooperator 合作公司/渠道
	// @return 匹配的团队成员, 如果没有, 返回空集合
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - TypeA1
	//  - Identities
	//  - Cooperator
	ListTeamMembersExByIdentity(header *common.RequestHeader, type_a1 IdentityType, identities []string, cooperator common.CooperateCompany) (r []*finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.4 根据姓名搜索团队成员, 新接口, 支持多渠道
	// @param header requestHeader
	// @param cooperator 合作公司/渠道
	// @param name 团队成员姓名
	// @param exactMatching 是否严格匹配
	// @return 匹配的团队成员
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - Cooperator
	//  - Name
	//  - ExactMatching
	ListTeamMembersExByName(header *common.RequestHeader, cooperator common.CooperateCompany, name string, exactMatching bool) (r []*finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.5 根据 id 列表批量查询团队成员, 新接口, 支持多渠道
	// @param header requestHeader
	// @param memberIdList 指定的团队成员 id 列表
	// @return 匹配的团队成员
	// @since 3.1.0
	//
	// Parameters:
	//  - Header
	//  - MemberIdList
	GetTeamMembersExByIdList(header *common.RequestHeader, memberIdList []IdInt) (r map[IdInt]*finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.6 审核通过团队成员信息, 新接口, 支持多渠道
	// @since 3.1.0
	// @param header requestHeader
	// @param cooperator 合作公司/渠道, 作校验使用, 团队成员的 cooperator 应与之相符
	// @param uid 团队成员所属 uid, 作校验使用, 团队成员记录的 uid 应与之相符
	// @param memberId 团队成员 id
	// @param adminUid 管理员 uid
	// @param reason 操作说明
	//
	// Parameters:
	//  - Header
	//  - Cooperator
	//  - Uid
	//  - MemberId
	//  - AdminUid
	//  - Reason
	PassTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.7 审核拒绝团队成员信息, 新接口, 支持多渠道
	// @since 3.1.0
	// @param header requestHeader
	// @param cooperator 合作公司/渠道, 作校验使用, 团队成员的 cooperator 应与之相符
	// @param uid 团队成员所属 uid, 作校验使用, 团队成员记录的 uid 应与之相符
	// @param memberId 团队成员 id
	// @param adminUid 管理员 uid
	// @param reason 操作说明
	//
	// Parameters:
	//  - Header
	//  - Cooperator
	//  - Uid
	//  - MemberId
	//  - AdminUid
	//  - Reason
	RejectTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.8 封禁团队成员信息, 新接口, 支持多渠道
	// @since 3.1.0
	// @param header requestHeader
	// @param cooperator 合作公司/渠道, 作校验使用, 团队成员的 cooperator 应与之相符
	// @param uid 团队成员所属 uid, 作校验使用, 团队成员记录的 uid 应与之相符
	// @param memberId 团队成员 id
	// @param adminUid 管理员 uid
	// @param reason 操作说明
	//
	// Parameters:
	//  - Header
	//  - Cooperator
	//  - Uid
	//  - MemberId
	//  - AdminUid
	//  - Reason
	ForbidTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.9 添加团队成员, 新接口, 支持多渠道
	// @since 3.1.0
	// @param header request header
	// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
	// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
	// @param member 新建的团队成员
	// @return 返回添加的成员信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	//  - Member
	AddTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, member *finance_types.TeamMember) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.10 更新团队成员信息, 新接口, 支持多渠道
	// @since 3.1.0
	// @param header request header
	// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
	// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
	// @param member 更新的团队成员信息
	// @param submit 是否更新后同时提交审核
	// @return 返回更新后的成员信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	//  - Member
	//  - Submit
	UpdateTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, member *finance_types.TeamMember, submit bool) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.11 将团队成员信息提交审核, 批量, 新接口, 支持多渠道
	// @since 3.1.0
	// @param header request header
	// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
	// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
	// @param memberIdList 提交的团度成员 id 列表
	// @return 返回提交的验证信息
	// @exception FinanceServiceException 提交或处理有误 (提交的 id 必须全部合法, 否则整体异常, 不更新数据)
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	//  - MemberIdList
	SubmitTeamMembersEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, memberIdList []IdInt) (r map[IdInt]*finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.12 删除团队成员, 批量, 新接口, 支持多渠道
	// @since 3.1.0
	// @param header request header
	// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
	// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
	// @param memberId 欲删除的团队成员 id
	// @return 返回删除的成员信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	//  - MemberId
	RemoveTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, memberId IdInt) (r *finance_types.TeamMember, fe *FinanceServiceException, err error)
	// {{{ #11.13 调整团队成员的分成比例, 新接口, 支持多渠道
	// @param header request header
	// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
	// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
	// @param sharingMap 团队成员分成比例
	// @since 3.1.0
	// @exception FinanceServiceException 提交或处理有误 (提交的 id 必须全部合法, 否则整体异常, 不更新数据)
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cooperator
	//  - SharingMap
	UpdateTeamMemberSharingEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, sharingMap map[IdInt]common.PercentageInt) (r map[IdInt]common.PercentageInt, fe *FinanceServiceException, err error)
	// {{{ #11.14 设定收款人, 新接口, 支持多渠道
	// @param header request header
	// @param uid 用户uid, 作校验, 须与提交的 beneficiary 数据一致
	// @param beneficiary 收款人信息
	// @since 3.7.0
	// @exception FinanceServiceException 提交或处理有误 (提交的 id 必须全部合法, 否则整体异常, 不更新数据)
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Beneficiary
	SetBeneficiaryEx(header *common.RequestHeader, uid UidInt, beneficiary *finance_types.Beneficiary) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error)
	// {{{ #12.1 查询收入确认记录, 新版, 支持多渠道
	// @since 3.7.0
	// @param header requestHeader
	// @param uid 指定查询的开发者用户 id
	// @param fromTime 查询起始时间
	// @param toTime 查询终止时间
	// @param status 指定状态, 若填写空集, 则返回全部状态
	// @return 匹配的记录列表, 月份正序
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - FromTime
	//  - ToTime
	//  - Status
	ListIncomeConfirmationRecordEx(header *common.RequestHeader, uid UidInt, fromTime TimeInt, toTime TimeInt, status []OperationStatus) (r []*finance_types.IncomeConfirmationRecord, fe *FinanceServiceException, err error)
	// {{{ 开发者更新财务信息接口（银行账号和开户行），新接口
	// @since 1.0.0
	//
	// Parameters:
	//  - Header
	//  - Account: 用户财务信息
	UpdateFinancialAccountBankInfo(header *common.RequestHeader, account *finance_types.FinancialAccount) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error)
	// {{{更新待提现记录的银行账户信息（银行账号和开户行），新接口
	// @param ids 待提现记录的id集合
	// @param account 更新后的开发者财务信息
	// @since 1.0.0
	// @return 返回同步修改后的提现记录
	//
	// Parameters:
	//  - Header
	//  - Ids
	//  - Account
	UpdatePaymentRecordBankInfo(header *common.RequestHeader, ids []IdInt, account *finance_types.FinancialAccount) (r []*finance_types.PaymentRecord, fe *FinanceServiceException, err error)
	// {{{ 重置开发者财务信息，新接口
	//  @param uid 需要重置的开发者财务信息的开发者Uid
	//  @param cate 需要重置成的账户类型
	//  @param frozenAmount 需要冻结的金额
	//  @param name 名称，作为重置的验证参数（防止多次提交），个人为姓名，公司为公司名
	//  @param identity 证件号，作为重置的验证参数（防止多次提交），个人为身份证号，公司为空
	//  @since 1.0.0
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - Cate
	//  - FrozenAmount
	//  - Name
	//  - Identity
	ResetFinancialAccount(header *common.RequestHeader, uid UidInt, cate AccountCategory, frozenAmount Amount, name string, identity string) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error)
	// {{{ 标记开发者结算周期，可更改为按周或按月
	//  @param uid 可以按周结算的开发者uid
	//  @param type 结算类型，按周或按月
	//  @return 返回标记后的财务扩展信息
	//
	// Parameters:
	//  - Header
	//  - Uid
	//  - TypeA1
	MarkFinanceSettleType(header *common.RequestHeader, uid UidInt, type_a1 SettleType) (r *finance_types.FinancialProfile, fe *FinanceServiceException, err error)
}

//@Description("开发者相关财务服务")
//@since 3.1.0
//{{{
type FinanceDeveloperServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewFinanceDeveloperServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *FinanceDeveloperServiceClient {
	return &FinanceDeveloperServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewFinanceDeveloperServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *FinanceDeveloperServiceClient {
	return &FinanceDeveloperServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// {{{ #10.1 查询新版提现记录, 新接口, 支持多渠道
// @param header requestHeader
// @param uid 指定查询的开发者用户 id, 对于内部调用, 允许为0, 取得所有用户的列表
// @param cooperators 指定查询的 cooperator 集合, 若为空, 查询所有 cooperator
// @param fromTime 开始时间, unix time
// @param toTime 结束时间, unix time
// @param statuses 指定查询的状态集合, 若为空, 查询所有状态
// @param offset offset
// @param limit limit, 最大 50, 对于内部调用, 允许为 0 表示取所有
// @return 匹配的记录列表
// @TODO 内部调用的验证方式暂未提供, 需要时, 可以考虑通过 header.requester 简单校验
// @since 3.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperators
//  - FromTime
//  - ToTime
//  - Statuses
//  - Offset
//  - Limit
func (p *FinanceDeveloperServiceClient) ListPaymentRecordEx(header *common.RequestHeader, uid UidInt, cooperators []common.CooperateCompany, fromTime TimeInt, toTime TimeInt, statuses []OperationStatus, offset int32, limit int32) (r *finance_types.PaymentRecordListResult, fe *FinanceServiceException, err error) {
	if err = p.sendListPaymentRecordEx(header, uid, cooperators, fromTime, toTime, statuses, offset, limit); err != nil {
		return
	}
	return p.recvListPaymentRecordEx()
}

func (p *FinanceDeveloperServiceClient) sendListPaymentRecordEx(header *common.RequestHeader, uid UidInt, cooperators []common.CooperateCompany, fromTime TimeInt, toTime TimeInt, statuses []OperationStatus, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listPaymentRecordEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1825 := NewListPaymentRecordExArgs()
	args1825.Header = header
	args1825.Uid = uid
	args1825.Cooperators = cooperators
	args1825.FromTime = fromTime
	args1825.ToTime = toTime
	args1825.Statuses = statuses
	args1825.Offset = offset
	args1825.Limit = limit
	if err = args1825.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvListPaymentRecordEx() (value *finance_types.PaymentRecordListResult, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1827 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1828 error
		error1828, err = error1827.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1828
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1826 := NewListPaymentRecordExResult()
	if err = result1826.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1826.Success
	if result1826.Fe != nil {
		fe = result1826.Fe
	}
	return
}

// {{{ #10.2 获取新版提现记录
// @param header requestHeader
// @param uid 指定查询的开发者用户 id
// @param ids 提现记录 id 列表
// @return 匹配的记录列表
// @since 3.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - Ids
func (p *FinanceDeveloperServiceClient) GetPaymentRecordEx(header *common.RequestHeader, uid UidInt, ids []IdInt) (r map[IdInt]*finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendGetPaymentRecordEx(header, uid, ids); err != nil {
		return
	}
	return p.recvGetPaymentRecordEx()
}

func (p *FinanceDeveloperServiceClient) sendGetPaymentRecordEx(header *common.RequestHeader, uid UidInt, ids []IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPaymentRecordEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1829 := NewGetPaymentRecordExArgs()
	args1829.Header = header
	args1829.Uid = uid
	args1829.Ids = ids
	if err = args1829.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvGetPaymentRecordEx() (value map[IdInt]*finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1831 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1832 error
		error1832, err = error1831.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1832
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1830 := NewGetPaymentRecordExResult()
	if err = result1830.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1830.Success
	if result1830.Fe != nil {
		fe = result1830.Fe
	}
	return
}

// {{{ #10.3 发起新版提现, 新版, 支持多渠道
// @param header request header
// @param uid 提现开发者用户 id
// @param cooperator 合作公司/渠道
// @param amount 提现金额
// @param time 操作时间
// @param type 提现类型(手动/自动)
// @return 发起后的提现记录
// @since 3.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
//  - Amount
//  - Time
//  - TypeA1
func (p *FinanceDeveloperServiceClient) SubmitPaymentRequestEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendSubmitPaymentRequestEx(header, uid, cooperator, amount, time, type_a1); err != nil {
		return
	}
	return p.recvSubmitPaymentRequestEx()
}

func (p *FinanceDeveloperServiceClient) sendSubmitPaymentRequestEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, amount Amount, time TimeInt, type_a1 WithdrawType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitPaymentRequestEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1833 := NewSubmitPaymentRequestExArgs()
	args1833.Header = header
	args1833.Uid = uid
	args1833.Cooperator = cooperator
	args1833.Amount = amount
	args1833.Time = time
	args1833.TypeA1 = type_a1
	if err = args1833.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvSubmitPaymentRequestEx() (value *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1835 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1836 error
		error1836, err = error1835.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1836
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1834 := NewSubmitPaymentRequestExResult()
	if err = result1834.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1834.Success
	if result1834.Fe != nil {
		fe = result1834.Fe
	}
	return
}

// {{{ #10.4 发起新版提现, 并同时自动审核通过(适用无审核流程的场景), 新版, 支持多渠道
// @param header request header
// @param uid 提现开发者用户 id
// @param cooperator 合作公司/渠道
// @param amount 提现金额
// @param time 操作时间
// @param type 提现类型(手动/自动)
// @return 发起后的提现记录
// @since 3.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
//  - Amount
//  - Time
//  - TypeA1
func (p *FinanceDeveloperServiceClient) SubmitPaymentRequestExWithAutoConfirmation(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, amount Amount, time TimeInt, type_a1 WithdrawType) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendSubmitPaymentRequestExWithAutoConfirmation(header, uid, cooperator, amount, time, type_a1); err != nil {
		return
	}
	return p.recvSubmitPaymentRequestExWithAutoConfirmation()
}

func (p *FinanceDeveloperServiceClient) sendSubmitPaymentRequestExWithAutoConfirmation(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, amount Amount, time TimeInt, type_a1 WithdrawType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitPaymentRequestExWithAutoConfirmation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1837 := NewSubmitPaymentRequestExWithAutoConfirmationArgs()
	args1837.Header = header
	args1837.Uid = uid
	args1837.Cooperator = cooperator
	args1837.Amount = amount
	args1837.Time = time
	args1837.TypeA1 = type_a1
	if err = args1837.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvSubmitPaymentRequestExWithAutoConfirmation() (value *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1839 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1840 error
		error1840, err = error1839.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1840
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1838 := NewSubmitPaymentRequestExWithAutoConfirmationResult()
	if err = result1838.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1838.Success
	if result1838.Fe != nil {
		fe = result1838.Fe
	}
	return
}

// {{{ #10.5 确认新版提现申请, 新版, 支持多渠道
// @since 3.1.0
// @param header request header
// @param id 记录 id
// @param uid 开发者 id, 用于校验
// @param cooperator 合作公司/渠道, 用于校验
// @param confirmedAmount 确认的提现金额
// @param adminUid 操作管理员的 id
// @param extInfo 其他附加信息
// @return 操作后的提现申请记录
//
// Parameters:
//  - Header
//  - Id
//  - Uid
//  - Cooperator
//  - ConfirmedAmount
//  - AdminUid
//  - ExtInfo
func (p *FinanceDeveloperServiceClient) ConfirmPaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, confirmedAmount Amount, adminUid UidInt, extInfo map[string]string) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendConfirmPaymentRequestEx(header, id, uid, cooperator, confirmedAmount, adminUid, extInfo); err != nil {
		return
	}
	return p.recvConfirmPaymentRequestEx()
}

func (p *FinanceDeveloperServiceClient) sendConfirmPaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, confirmedAmount Amount, adminUid UidInt, extInfo map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("confirmPaymentRequestEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1841 := NewConfirmPaymentRequestExArgs()
	args1841.Header = header
	args1841.Id = id
	args1841.Uid = uid
	args1841.Cooperator = cooperator
	args1841.ConfirmedAmount = confirmedAmount
	args1841.AdminUid = adminUid
	args1841.ExtInfo = extInfo
	if err = args1841.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvConfirmPaymentRequestEx() (value *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1843 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1844 error
		error1844, err = error1843.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1844
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1842 := NewConfirmPaymentRequestExResult()
	if err = result1842.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1842.Success
	if result1842.Fe != nil {
		fe = result1842.Fe
	}
	return
}

// {{{ #10.6 拒绝新版提现申请, 新版, 支持多渠道
// @since 3.1.0
// @param header request header
// @param id 记录 id
// @param uid 开发者 id
// @param cooperator 合作公司/渠道
// @param requestedAmount 申请的金额
// @param adminUid 操作管理员的 id
// @param extInfo 其他附加信息, 建议将理由以 reason 为 key 写入
// @return 操作后的提现申请记录
//
// Parameters:
//  - Header
//  - Id
//  - Uid
//  - Cooperator
//  - RequestedAmount
//  - AdminUid
//  - ExtInfo
func (p *FinanceDeveloperServiceClient) DenyPaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, requestedAmount Amount, adminUid UidInt, extInfo map[string]string) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendDenyPaymentRequestEx(header, id, uid, cooperator, requestedAmount, adminUid, extInfo); err != nil {
		return
	}
	return p.recvDenyPaymentRequestEx()
}

func (p *FinanceDeveloperServiceClient) sendDenyPaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, requestedAmount Amount, adminUid UidInt, extInfo map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("denyPaymentRequestEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1845 := NewDenyPaymentRequestExArgs()
	args1845.Header = header
	args1845.Id = id
	args1845.Uid = uid
	args1845.Cooperator = cooperator
	args1845.RequestedAmount = requestedAmount
	args1845.AdminUid = adminUid
	args1845.ExtInfo = extInfo
	if err = args1845.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvDenyPaymentRequestEx() (value *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1847 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1848 error
		error1848, err = error1847.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1848
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1846 := NewDenyPaymentRequestExResult()
	if err = result1846.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1846.Success
	if result1846.Fe != nil {
		fe = result1846.Fe
	}
	return
}

// {{{ #10.7 完成新版提现申请, 新版, 支持多渠道
// @since 3.1.0
// @param id 记录 id
// @param uid 开发者 id, 用于校验
// @param cooperator 合作公司/渠道, 用于校验
// @param paidAmount 支付金额
// @param paymentFee 支付手续费
// @param adminUid 操作管理员的 id
// @param extInfo 其他附加信息
// @return 操作后的提现申请记录
//
// Parameters:
//  - Header
//  - Id
//  - Uid
//  - Cooperator
//  - PaidAmount
//  - PaymentFee
//  - AdminUid
//  - ExtInfo
func (p *FinanceDeveloperServiceClient) CompletePaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, paidAmount Amount, paymentFee Amount, adminUid UidInt, extInfo map[string]string) (r *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendCompletePaymentRequestEx(header, id, uid, cooperator, paidAmount, paymentFee, adminUid, extInfo); err != nil {
		return
	}
	return p.recvCompletePaymentRequestEx()
}

func (p *FinanceDeveloperServiceClient) sendCompletePaymentRequestEx(header *common.RequestHeader, id IdInt, uid UidInt, cooperator common.CooperateCompany, paidAmount Amount, paymentFee Amount, adminUid UidInt, extInfo map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("completePaymentRequestEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1849 := NewCompletePaymentRequestExArgs()
	args1849.Header = header
	args1849.Id = id
	args1849.Uid = uid
	args1849.Cooperator = cooperator
	args1849.PaidAmount = paidAmount
	args1849.PaymentFee = paymentFee
	args1849.AdminUid = adminUid
	args1849.ExtInfo = extInfo
	if err = args1849.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvCompletePaymentRequestEx() (value *finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1851 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1852 error
		error1852, err = error1851.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1852
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1850 := NewCompletePaymentRequestExResult()
	if err = result1850.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1850.Success
	if result1850.Fe != nil {
		fe = result1850.Fe
	}
	return
}

// {{{ #11.1 获取某用户的团队成员信息, 新接口, 支持多渠道
// @param header requestHeader
// @param uid 用户 uid
// @param cooperator 合作公司/渠道
// @return 全部满足查询条件的团队成员
// @since 3.1.0
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
func (p *FinanceDeveloperServiceClient) ListTeamMembersEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany) (r []*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendListTeamMembersEx(header, uid, cooperator); err != nil {
		return
	}
	return p.recvListTeamMembersEx()
}

func (p *FinanceDeveloperServiceClient) sendListTeamMembersEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listTeamMembersEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1853 := NewListTeamMembersExArgs()
	args1853.Header = header
	args1853.Uid = uid
	args1853.Cooperator = cooperator
	if err = args1853.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvListTeamMembersEx() (value []*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1855 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1856 error
		error1856, err = error1855.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1856
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1854 := NewListTeamMembersExResult()
	if err = result1854.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1854.Success
	if result1854.Fe != nil {
		fe = result1854.Fe
	}
	return
}

// {{{ #11.2 后台团队成员列表, 新接口, 支持多渠道
// @param header requestHeader
// @param cooperator 合作公司/渠道
// @param uid 查询的用户 id, 可以指定 0 获取全部, 后台通常是获取全部的
// @param statuses 查询的审核状态列表, 可以指定空列表, 取全部状态
// @param offset 分页 offset
// @param limit 分页 limit, 最大 50, 可以指定 0 获取全部, 但不推荐使用
// @since 3.1.0
//
// Parameters:
//  - Header
//  - Cooperator
//  - Uid
//  - Statuses
//  - Offset
//  - Limit
func (p *FinanceDeveloperServiceClient) ListTeamMembersExForAdmin(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, statuses []CommonAuditStatus, offset QueryInt, limit QueryInt) (r *finance_types.TeamMemberListResult, fe *FinanceServiceException, err error) {
	if err = p.sendListTeamMembersExForAdmin(header, cooperator, uid, statuses, offset, limit); err != nil {
		return
	}
	return p.recvListTeamMembersExForAdmin()
}

func (p *FinanceDeveloperServiceClient) sendListTeamMembersExForAdmin(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, statuses []CommonAuditStatus, offset QueryInt, limit QueryInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listTeamMembersExForAdmin", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1857 := NewListTeamMembersExForAdminArgs()
	args1857.Header = header
	args1857.Cooperator = cooperator
	args1857.Uid = uid
	args1857.Statuses = statuses
	args1857.Offset = offset
	args1857.Limit = limit
	if err = args1857.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvListTeamMembersExForAdmin() (value *finance_types.TeamMemberListResult, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1859 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1860 error
		error1860, err = error1859.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1860
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1858 := NewListTeamMembersExForAdminResult()
	if err = result1858.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1858.Success
	if result1858.Fe != nil {
		fe = result1858.Fe
	}
	return
}

// {{{ #11.3 根据证件号码搜索团队成员, 新接口, 支持多渠道
// @param header requestHeader
// @param type 证件类型
// @param identities 证件号码, 支持多个
// @param cooperator 合作公司/渠道
// @return 匹配的团队成员, 如果没有, 返回空集合
// @since 3.1.0
//
// Parameters:
//  - Header
//  - TypeA1
//  - Identities
//  - Cooperator
func (p *FinanceDeveloperServiceClient) ListTeamMembersExByIdentity(header *common.RequestHeader, type_a1 IdentityType, identities []string, cooperator common.CooperateCompany) (r []*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendListTeamMembersExByIdentity(header, type_a1, identities, cooperator); err != nil {
		return
	}
	return p.recvListTeamMembersExByIdentity()
}

func (p *FinanceDeveloperServiceClient) sendListTeamMembersExByIdentity(header *common.RequestHeader, type_a1 IdentityType, identities []string, cooperator common.CooperateCompany) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listTeamMembersExByIdentity", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1861 := NewListTeamMembersExByIdentityArgs()
	args1861.Header = header
	args1861.TypeA1 = type_a1
	args1861.Identities = identities
	args1861.Cooperator = cooperator
	if err = args1861.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvListTeamMembersExByIdentity() (value []*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1863 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1864 error
		error1864, err = error1863.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1864
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1862 := NewListTeamMembersExByIdentityResult()
	if err = result1862.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1862.Success
	if result1862.Fe != nil {
		fe = result1862.Fe
	}
	return
}

// {{{ #11.4 根据姓名搜索团队成员, 新接口, 支持多渠道
// @param header requestHeader
// @param cooperator 合作公司/渠道
// @param name 团队成员姓名
// @param exactMatching 是否严格匹配
// @return 匹配的团队成员
// @since 3.1.0
//
// Parameters:
//  - Header
//  - Cooperator
//  - Name
//  - ExactMatching
func (p *FinanceDeveloperServiceClient) ListTeamMembersExByName(header *common.RequestHeader, cooperator common.CooperateCompany, name string, exactMatching bool) (r []*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendListTeamMembersExByName(header, cooperator, name, exactMatching); err != nil {
		return
	}
	return p.recvListTeamMembersExByName()
}

func (p *FinanceDeveloperServiceClient) sendListTeamMembersExByName(header *common.RequestHeader, cooperator common.CooperateCompany, name string, exactMatching bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listTeamMembersExByName", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1865 := NewListTeamMembersExByNameArgs()
	args1865.Header = header
	args1865.Cooperator = cooperator
	args1865.Name = name
	args1865.ExactMatching = exactMatching
	if err = args1865.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvListTeamMembersExByName() (value []*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1867 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1868 error
		error1868, err = error1867.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1868
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1866 := NewListTeamMembersExByNameResult()
	if err = result1866.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1866.Success
	if result1866.Fe != nil {
		fe = result1866.Fe
	}
	return
}

// {{{ #11.5 根据 id 列表批量查询团队成员, 新接口, 支持多渠道
// @param header requestHeader
// @param memberIdList 指定的团队成员 id 列表
// @return 匹配的团队成员
// @since 3.1.0
//
// Parameters:
//  - Header
//  - MemberIdList
func (p *FinanceDeveloperServiceClient) GetTeamMembersExByIdList(header *common.RequestHeader, memberIdList []IdInt) (r map[IdInt]*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendGetTeamMembersExByIdList(header, memberIdList); err != nil {
		return
	}
	return p.recvGetTeamMembersExByIdList()
}

func (p *FinanceDeveloperServiceClient) sendGetTeamMembersExByIdList(header *common.RequestHeader, memberIdList []IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTeamMembersExByIdList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1869 := NewGetTeamMembersExByIdListArgs()
	args1869.Header = header
	args1869.MemberIdList = memberIdList
	if err = args1869.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvGetTeamMembersExByIdList() (value map[IdInt]*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1871 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1872 error
		error1872, err = error1871.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1872
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1870 := NewGetTeamMembersExByIdListResult()
	if err = result1870.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1870.Success
	if result1870.Fe != nil {
		fe = result1870.Fe
	}
	return
}

// {{{ #11.6 审核通过团队成员信息, 新接口, 支持多渠道
// @since 3.1.0
// @param header requestHeader
// @param cooperator 合作公司/渠道, 作校验使用, 团队成员的 cooperator 应与之相符
// @param uid 团队成员所属 uid, 作校验使用, 团队成员记录的 uid 应与之相符
// @param memberId 团队成员 id
// @param adminUid 管理员 uid
// @param reason 操作说明
//
// Parameters:
//  - Header
//  - Cooperator
//  - Uid
//  - MemberId
//  - AdminUid
//  - Reason
func (p *FinanceDeveloperServiceClient) PassTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendPassTeamMemberEx(header, cooperator, uid, memberId, adminUid, reason); err != nil {
		return
	}
	return p.recvPassTeamMemberEx()
}

func (p *FinanceDeveloperServiceClient) sendPassTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("passTeamMemberEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1873 := NewPassTeamMemberExArgs()
	args1873.Header = header
	args1873.Cooperator = cooperator
	args1873.Uid = uid
	args1873.MemberId = memberId
	args1873.AdminUid = adminUid
	args1873.Reason = reason
	if err = args1873.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvPassTeamMemberEx() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1875 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1876 error
		error1876, err = error1875.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1876
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1874 := NewPassTeamMemberExResult()
	if err = result1874.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1874.Success
	if result1874.Fe != nil {
		fe = result1874.Fe
	}
	return
}

// {{{ #11.7 审核拒绝团队成员信息, 新接口, 支持多渠道
// @since 3.1.0
// @param header requestHeader
// @param cooperator 合作公司/渠道, 作校验使用, 团队成员的 cooperator 应与之相符
// @param uid 团队成员所属 uid, 作校验使用, 团队成员记录的 uid 应与之相符
// @param memberId 团队成员 id
// @param adminUid 管理员 uid
// @param reason 操作说明
//
// Parameters:
//  - Header
//  - Cooperator
//  - Uid
//  - MemberId
//  - AdminUid
//  - Reason
func (p *FinanceDeveloperServiceClient) RejectTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendRejectTeamMemberEx(header, cooperator, uid, memberId, adminUid, reason); err != nil {
		return
	}
	return p.recvRejectTeamMemberEx()
}

func (p *FinanceDeveloperServiceClient) sendRejectTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("rejectTeamMemberEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1877 := NewRejectTeamMemberExArgs()
	args1877.Header = header
	args1877.Cooperator = cooperator
	args1877.Uid = uid
	args1877.MemberId = memberId
	args1877.AdminUid = adminUid
	args1877.Reason = reason
	if err = args1877.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvRejectTeamMemberEx() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1879 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1880 error
		error1880, err = error1879.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1880
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1878 := NewRejectTeamMemberExResult()
	if err = result1878.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1878.Success
	if result1878.Fe != nil {
		fe = result1878.Fe
	}
	return
}

// {{{ #11.8 封禁团队成员信息, 新接口, 支持多渠道
// @since 3.1.0
// @param header requestHeader
// @param cooperator 合作公司/渠道, 作校验使用, 团队成员的 cooperator 应与之相符
// @param uid 团队成员所属 uid, 作校验使用, 团队成员记录的 uid 应与之相符
// @param memberId 团队成员 id
// @param adminUid 管理员 uid
// @param reason 操作说明
//
// Parameters:
//  - Header
//  - Cooperator
//  - Uid
//  - MemberId
//  - AdminUid
//  - Reason
func (p *FinanceDeveloperServiceClient) ForbidTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendForbidTeamMemberEx(header, cooperator, uid, memberId, adminUid, reason); err != nil {
		return
	}
	return p.recvForbidTeamMemberEx()
}

func (p *FinanceDeveloperServiceClient) sendForbidTeamMemberEx(header *common.RequestHeader, cooperator common.CooperateCompany, uid UidInt, memberId IdInt, adminUid UidInt, reason string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("forbidTeamMemberEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1881 := NewForbidTeamMemberExArgs()
	args1881.Header = header
	args1881.Cooperator = cooperator
	args1881.Uid = uid
	args1881.MemberId = memberId
	args1881.AdminUid = adminUid
	args1881.Reason = reason
	if err = args1881.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvForbidTeamMemberEx() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1883 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1884 error
		error1884, err = error1883.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1884
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1882 := NewForbidTeamMemberExResult()
	if err = result1882.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1882.Success
	if result1882.Fe != nil {
		fe = result1882.Fe
	}
	return
}

// {{{ #11.9 添加团队成员, 新接口, 支持多渠道
// @since 3.1.0
// @param header request header
// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
// @param member 新建的团队成员
// @return 返回添加的成员信息
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
//  - Member
func (p *FinanceDeveloperServiceClient) AddTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, member *finance_types.TeamMember) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendAddTeamMemberEx(header, uid, cooperator, member); err != nil {
		return
	}
	return p.recvAddTeamMemberEx()
}

func (p *FinanceDeveloperServiceClient) sendAddTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, member *finance_types.TeamMember) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addTeamMemberEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1885 := NewAddTeamMemberExArgs()
	args1885.Header = header
	args1885.Uid = uid
	args1885.Cooperator = cooperator
	args1885.Member = member
	if err = args1885.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvAddTeamMemberEx() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1887 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1888 error
		error1888, err = error1887.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1888
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1886 := NewAddTeamMemberExResult()
	if err = result1886.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1886.Success
	if result1886.Fe != nil {
		fe = result1886.Fe
	}
	return
}

// {{{ #11.10 更新团队成员信息, 新接口, 支持多渠道
// @since 3.1.0
// @param header request header
// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
// @param member 更新的团队成员信息
// @param submit 是否更新后同时提交审核
// @return 返回更新后的成员信息
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
//  - Member
//  - Submit
func (p *FinanceDeveloperServiceClient) UpdateTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, member *finance_types.TeamMember, submit bool) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendUpdateTeamMemberEx(header, uid, cooperator, member, submit); err != nil {
		return
	}
	return p.recvUpdateTeamMemberEx()
}

func (p *FinanceDeveloperServiceClient) sendUpdateTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, member *finance_types.TeamMember, submit bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateTeamMemberEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1889 := NewUpdateTeamMemberExArgs()
	args1889.Header = header
	args1889.Uid = uid
	args1889.Cooperator = cooperator
	args1889.Member = member
	args1889.Submit = submit
	if err = args1889.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvUpdateTeamMemberEx() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1891 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1892 error
		error1892, err = error1891.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1892
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1890 := NewUpdateTeamMemberExResult()
	if err = result1890.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1890.Success
	if result1890.Fe != nil {
		fe = result1890.Fe
	}
	return
}

// {{{ #11.11 将团队成员信息提交审核, 批量, 新接口, 支持多渠道
// @since 3.1.0
// @param header request header
// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
// @param memberIdList 提交的团度成员 id 列表
// @return 返回提交的验证信息
// @exception FinanceServiceException 提交或处理有误 (提交的 id 必须全部合法, 否则整体异常, 不更新数据)
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
//  - MemberIdList
func (p *FinanceDeveloperServiceClient) SubmitTeamMembersEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, memberIdList []IdInt) (r map[IdInt]*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendSubmitTeamMembersEx(header, uid, cooperator, memberIdList); err != nil {
		return
	}
	return p.recvSubmitTeamMembersEx()
}

func (p *FinanceDeveloperServiceClient) sendSubmitTeamMembersEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, memberIdList []IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("submitTeamMembersEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1893 := NewSubmitTeamMembersExArgs()
	args1893.Header = header
	args1893.Uid = uid
	args1893.Cooperator = cooperator
	args1893.MemberIdList = memberIdList
	if err = args1893.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvSubmitTeamMembersEx() (value map[IdInt]*finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1895 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1896 error
		error1896, err = error1895.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1896
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1894 := NewSubmitTeamMembersExResult()
	if err = result1894.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1894.Success
	if result1894.Fe != nil {
		fe = result1894.Fe
	}
	return
}

// {{{ #11.12 删除团队成员, 批量, 新接口, 支持多渠道
// @since 3.1.0
// @param header request header
// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
// @param memberId 欲删除的团队成员 id
// @return 返回删除的成员信息
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
//  - MemberId
func (p *FinanceDeveloperServiceClient) RemoveTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, memberId IdInt) (r *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	if err = p.sendRemoveTeamMemberEx(header, uid, cooperator, memberId); err != nil {
		return
	}
	return p.recvRemoveTeamMemberEx()
}

func (p *FinanceDeveloperServiceClient) sendRemoveTeamMemberEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, memberId IdInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("removeTeamMemberEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1897 := NewRemoveTeamMemberExArgs()
	args1897.Header = header
	args1897.Uid = uid
	args1897.Cooperator = cooperator
	args1897.MemberId = memberId
	if err = args1897.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvRemoveTeamMemberEx() (value *finance_types.TeamMember, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1899 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1900 error
		error1900, err = error1899.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1900
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1898 := NewRemoveTeamMemberExResult()
	if err = result1898.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1898.Success
	if result1898.Fe != nil {
		fe = result1898.Fe
	}
	return
}

// {{{ #11.13 调整团队成员的分成比例, 新接口, 支持多渠道
// @param header request header
// @param uid 用户uid, 作校验, 须与提交的 member 数据一致
// @param cooperator 合作公司, 作校验, 须与提交的 member 数据一致
// @param sharingMap 团队成员分成比例
// @since 3.1.0
// @exception FinanceServiceException 提交或处理有误 (提交的 id 必须全部合法, 否则整体异常, 不更新数据)
//
// Parameters:
//  - Header
//  - Uid
//  - Cooperator
//  - SharingMap
func (p *FinanceDeveloperServiceClient) UpdateTeamMemberSharingEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, sharingMap map[IdInt]common.PercentageInt) (r map[IdInt]common.PercentageInt, fe *FinanceServiceException, err error) {
	if err = p.sendUpdateTeamMemberSharingEx(header, uid, cooperator, sharingMap); err != nil {
		return
	}
	return p.recvUpdateTeamMemberSharingEx()
}

func (p *FinanceDeveloperServiceClient) sendUpdateTeamMemberSharingEx(header *common.RequestHeader, uid UidInt, cooperator common.CooperateCompany, sharingMap map[IdInt]common.PercentageInt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateTeamMemberSharingEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1901 := NewUpdateTeamMemberSharingExArgs()
	args1901.Header = header
	args1901.Uid = uid
	args1901.Cooperator = cooperator
	args1901.SharingMap = sharingMap
	if err = args1901.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvUpdateTeamMemberSharingEx() (value map[IdInt]common.PercentageInt, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1903 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1904 error
		error1904, err = error1903.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1904
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1902 := NewUpdateTeamMemberSharingExResult()
	if err = result1902.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1902.Success
	if result1902.Fe != nil {
		fe = result1902.Fe
	}
	return
}

// {{{ #11.14 设定收款人, 新接口, 支持多渠道
// @param header request header
// @param uid 用户uid, 作校验, 须与提交的 beneficiary 数据一致
// @param beneficiary 收款人信息
// @since 3.7.0
// @exception FinanceServiceException 提交或处理有误 (提交的 id 必须全部合法, 否则整体异常, 不更新数据)
//
// Parameters:
//  - Header
//  - Uid
//  - Beneficiary
func (p *FinanceDeveloperServiceClient) SetBeneficiaryEx(header *common.RequestHeader, uid UidInt, beneficiary *finance_types.Beneficiary) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	if err = p.sendSetBeneficiaryEx(header, uid, beneficiary); err != nil {
		return
	}
	return p.recvSetBeneficiaryEx()
}

func (p *FinanceDeveloperServiceClient) sendSetBeneficiaryEx(header *common.RequestHeader, uid UidInt, beneficiary *finance_types.Beneficiary) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("setBeneficiaryEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1905 := NewSetBeneficiaryExArgs()
	args1905.Header = header
	args1905.Uid = uid
	args1905.Beneficiary = beneficiary
	if err = args1905.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvSetBeneficiaryEx() (value *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1907 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1908 error
		error1908, err = error1907.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1908
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1906 := NewSetBeneficiaryExResult()
	if err = result1906.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1906.Success
	if result1906.Fe != nil {
		fe = result1906.Fe
	}
	return
}

// {{{ #12.1 查询收入确认记录, 新版, 支持多渠道
// @since 3.7.0
// @param header requestHeader
// @param uid 指定查询的开发者用户 id
// @param fromTime 查询起始时间
// @param toTime 查询终止时间
// @param status 指定状态, 若填写空集, 则返回全部状态
// @return 匹配的记录列表, 月份正序
//
// Parameters:
//  - Header
//  - Uid
//  - FromTime
//  - ToTime
//  - Status
func (p *FinanceDeveloperServiceClient) ListIncomeConfirmationRecordEx(header *common.RequestHeader, uid UidInt, fromTime TimeInt, toTime TimeInt, status []OperationStatus) (r []*finance_types.IncomeConfirmationRecord, fe *FinanceServiceException, err error) {
	if err = p.sendListIncomeConfirmationRecordEx(header, uid, fromTime, toTime, status); err != nil {
		return
	}
	return p.recvListIncomeConfirmationRecordEx()
}

func (p *FinanceDeveloperServiceClient) sendListIncomeConfirmationRecordEx(header *common.RequestHeader, uid UidInt, fromTime TimeInt, toTime TimeInt, status []OperationStatus) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listIncomeConfirmationRecordEx", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1909 := NewListIncomeConfirmationRecordExArgs()
	args1909.Header = header
	args1909.Uid = uid
	args1909.FromTime = fromTime
	args1909.ToTime = toTime
	args1909.Status = status
	if err = args1909.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvListIncomeConfirmationRecordEx() (value []*finance_types.IncomeConfirmationRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1911 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1912 error
		error1912, err = error1911.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1912
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1910 := NewListIncomeConfirmationRecordExResult()
	if err = result1910.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1910.Success
	if result1910.Fe != nil {
		fe = result1910.Fe
	}
	return
}

// {{{ 开发者更新财务信息接口（银行账号和开户行），新接口
// @since 1.0.0
//
// Parameters:
//  - Header
//  - Account: 用户财务信息
func (p *FinanceDeveloperServiceClient) UpdateFinancialAccountBankInfo(header *common.RequestHeader, account *finance_types.FinancialAccount) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	if err = p.sendUpdateFinancialAccountBankInfo(header, account); err != nil {
		return
	}
	return p.recvUpdateFinancialAccountBankInfo()
}

func (p *FinanceDeveloperServiceClient) sendUpdateFinancialAccountBankInfo(header *common.RequestHeader, account *finance_types.FinancialAccount) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateFinancialAccountBankInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1913 := NewUpdateFinancialAccountBankInfoArgs()
	args1913.Header = header
	args1913.Account = account
	if err = args1913.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvUpdateFinancialAccountBankInfo() (value *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1915 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1916 error
		error1916, err = error1915.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1916
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1914 := NewUpdateFinancialAccountBankInfoResult()
	if err = result1914.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1914.Success
	if result1914.Fe != nil {
		fe = result1914.Fe
	}
	return
}

// {{{更新待提现记录的银行账户信息（银行账号和开户行），新接口
// @param ids 待提现记录的id集合
// @param account 更新后的开发者财务信息
// @since 1.0.0
// @return 返回同步修改后的提现记录
//
// Parameters:
//  - Header
//  - Ids
//  - Account
func (p *FinanceDeveloperServiceClient) UpdatePaymentRecordBankInfo(header *common.RequestHeader, ids []IdInt, account *finance_types.FinancialAccount) (r []*finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	if err = p.sendUpdatePaymentRecordBankInfo(header, ids, account); err != nil {
		return
	}
	return p.recvUpdatePaymentRecordBankInfo()
}

func (p *FinanceDeveloperServiceClient) sendUpdatePaymentRecordBankInfo(header *common.RequestHeader, ids []IdInt, account *finance_types.FinancialAccount) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updatePaymentRecordBankInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1917 := NewUpdatePaymentRecordBankInfoArgs()
	args1917.Header = header
	args1917.Ids = ids
	args1917.Account = account
	if err = args1917.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvUpdatePaymentRecordBankInfo() (value []*finance_types.PaymentRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1919 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1920 error
		error1920, err = error1919.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1920
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1918 := NewUpdatePaymentRecordBankInfoResult()
	if err = result1918.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1918.Success
	if result1918.Fe != nil {
		fe = result1918.Fe
	}
	return
}

// {{{ 重置开发者财务信息，新接口
//  @param uid 需要重置的开发者财务信息的开发者Uid
//  @param cate 需要重置成的账户类型
//  @param frozenAmount 需要冻结的金额
//  @param name 名称，作为重置的验证参数（防止多次提交），个人为姓名，公司为公司名
//  @param identity 证件号，作为重置的验证参数（防止多次提交），个人为身份证号，公司为空
//  @since 1.0.0
//
// Parameters:
//  - Header
//  - Uid
//  - Cate
//  - FrozenAmount
//  - Name
//  - Identity
func (p *FinanceDeveloperServiceClient) ResetFinancialAccount(header *common.RequestHeader, uid UidInt, cate AccountCategory, frozenAmount Amount, name string, identity string) (r *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	if err = p.sendResetFinancialAccount(header, uid, cate, frozenAmount, name, identity); err != nil {
		return
	}
	return p.recvResetFinancialAccount()
}

func (p *FinanceDeveloperServiceClient) sendResetFinancialAccount(header *common.RequestHeader, uid UidInt, cate AccountCategory, frozenAmount Amount, name string, identity string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("resetFinancialAccount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1921 := NewResetFinancialAccountArgs()
	args1921.Header = header
	args1921.Uid = uid
	args1921.Cate = cate
	args1921.FrozenAmount = frozenAmount
	args1921.Name = name
	args1921.Identity = identity
	if err = args1921.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvResetFinancialAccount() (value *finance_types.FinancialAccount, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1923 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1924 error
		error1924, err = error1923.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1924
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1922 := NewResetFinancialAccountResult()
	if err = result1922.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1922.Success
	if result1922.Fe != nil {
		fe = result1922.Fe
	}
	return
}

// {{{ 标记开发者结算周期，可更改为按周或按月
//  @param uid 可以按周结算的开发者uid
//  @param type 结算类型，按周或按月
//  @return 返回标记后的财务扩展信息
//
// Parameters:
//  - Header
//  - Uid
//  - TypeA1
func (p *FinanceDeveloperServiceClient) MarkFinanceSettleType(header *common.RequestHeader, uid UidInt, type_a1 SettleType) (r *finance_types.FinancialProfile, fe *FinanceServiceException, err error) {
	if err = p.sendMarkFinanceSettleType(header, uid, type_a1); err != nil {
		return
	}
	return p.recvMarkFinanceSettleType()
}

func (p *FinanceDeveloperServiceClient) sendMarkFinanceSettleType(header *common.RequestHeader, uid UidInt, type_a1 SettleType) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("markFinanceSettleType", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args1925 := NewMarkFinanceSettleTypeArgs()
	args1925.Header = header
	args1925.Uid = uid
	args1925.TypeA1 = type_a1
	if err = args1925.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceDeveloperServiceClient) recvMarkFinanceSettleType() (value *finance_types.FinancialProfile, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error1927 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error1928 error
		error1928, err = error1927.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error1928
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1926 := NewMarkFinanceSettleTypeResult()
	if err = result1926.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1926.Success
	if result1926.Fe != nil {
		fe = result1926.Fe
	}
	return
}

type FinanceDeveloperServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      FinanceDeveloperService
}

func (p *FinanceDeveloperServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *FinanceDeveloperServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *FinanceDeveloperServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewFinanceDeveloperServiceProcessor(handler FinanceDeveloperService) *FinanceDeveloperServiceProcessor {

	self1929 := &FinanceDeveloperServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self1929.processorMap["listPaymentRecordEx"] = &financeDeveloperServiceProcessorListPaymentRecordEx{handler: handler}
	self1929.processorMap["getPaymentRecordEx"] = &financeDeveloperServiceProcessorGetPaymentRecordEx{handler: handler}
	self1929.processorMap["submitPaymentRequestEx"] = &financeDeveloperServiceProcessorSubmitPaymentRequestEx{handler: handler}
	self1929.processorMap["submitPaymentRequestExWithAutoConfirmation"] = &financeDeveloperServiceProcessorSubmitPaymentRequestExWithAutoConfirmation{handler: handler}
	self1929.processorMap["confirmPaymentRequestEx"] = &financeDeveloperServiceProcessorConfirmPaymentRequestEx{handler: handler}
	self1929.processorMap["denyPaymentRequestEx"] = &financeDeveloperServiceProcessorDenyPaymentRequestEx{handler: handler}
	self1929.processorMap["completePaymentRequestEx"] = &financeDeveloperServiceProcessorCompletePaymentRequestEx{handler: handler}
	self1929.processorMap["listTeamMembersEx"] = &financeDeveloperServiceProcessorListTeamMembersEx{handler: handler}
	self1929.processorMap["listTeamMembersExForAdmin"] = &financeDeveloperServiceProcessorListTeamMembersExForAdmin{handler: handler}
	self1929.processorMap["listTeamMembersExByIdentity"] = &financeDeveloperServiceProcessorListTeamMembersExByIdentity{handler: handler}
	self1929.processorMap["listTeamMembersExByName"] = &financeDeveloperServiceProcessorListTeamMembersExByName{handler: handler}
	self1929.processorMap["getTeamMembersExByIdList"] = &financeDeveloperServiceProcessorGetTeamMembersExByIdList{handler: handler}
	self1929.processorMap["passTeamMemberEx"] = &financeDeveloperServiceProcessorPassTeamMemberEx{handler: handler}
	self1929.processorMap["rejectTeamMemberEx"] = &financeDeveloperServiceProcessorRejectTeamMemberEx{handler: handler}
	self1929.processorMap["forbidTeamMemberEx"] = &financeDeveloperServiceProcessorForbidTeamMemberEx{handler: handler}
	self1929.processorMap["addTeamMemberEx"] = &financeDeveloperServiceProcessorAddTeamMemberEx{handler: handler}
	self1929.processorMap["updateTeamMemberEx"] = &financeDeveloperServiceProcessorUpdateTeamMemberEx{handler: handler}
	self1929.processorMap["submitTeamMembersEx"] = &financeDeveloperServiceProcessorSubmitTeamMembersEx{handler: handler}
	self1929.processorMap["removeTeamMemberEx"] = &financeDeveloperServiceProcessorRemoveTeamMemberEx{handler: handler}
	self1929.processorMap["updateTeamMemberSharingEx"] = &financeDeveloperServiceProcessorUpdateTeamMemberSharingEx{handler: handler}
	self1929.processorMap["setBeneficiaryEx"] = &financeDeveloperServiceProcessorSetBeneficiaryEx{handler: handler}
	self1929.processorMap["listIncomeConfirmationRecordEx"] = &financeDeveloperServiceProcessorListIncomeConfirmationRecordEx{handler: handler}
	self1929.processorMap["updateFinancialAccountBankInfo"] = &financeDeveloperServiceProcessorUpdateFinancialAccountBankInfo{handler: handler}
	self1929.processorMap["updatePaymentRecordBankInfo"] = &financeDeveloperServiceProcessorUpdatePaymentRecordBankInfo{handler: handler}
	self1929.processorMap["resetFinancialAccount"] = &financeDeveloperServiceProcessorResetFinancialAccount{handler: handler}
	self1929.processorMap["markFinanceSettleType"] = &financeDeveloperServiceProcessorMarkFinanceSettleType{handler: handler}
	return self1929
}

func (p *FinanceDeveloperServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x1930 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x1930.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x1930

}

type financeDeveloperServiceProcessorListPaymentRecordEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorListPaymentRecordEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListPaymentRecordExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listPaymentRecordEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListPaymentRecordExResult()
	if result.Success, result.Fe, err = p.handler.ListPaymentRecordEx(args.Header, args.Uid, args.Cooperators, args.FromTime, args.ToTime, args.Statuses, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listPaymentRecordEx: "+err.Error())
		oprot.WriteMessageBegin("listPaymentRecordEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listPaymentRecordEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorGetPaymentRecordEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorGetPaymentRecordEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPaymentRecordExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPaymentRecordEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPaymentRecordExResult()
	if result.Success, result.Fe, err = p.handler.GetPaymentRecordEx(args.Header, args.Uid, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPaymentRecordEx: "+err.Error())
		oprot.WriteMessageBegin("getPaymentRecordEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPaymentRecordEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorSubmitPaymentRequestEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorSubmitPaymentRequestEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitPaymentRequestExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitPaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitPaymentRequestExResult()
	if result.Success, result.Fe, err = p.handler.SubmitPaymentRequestEx(args.Header, args.Uid, args.Cooperator, args.Amount, args.Time, args.TypeA1); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitPaymentRequestEx: "+err.Error())
		oprot.WriteMessageBegin("submitPaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitPaymentRequestEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorSubmitPaymentRequestExWithAutoConfirmation struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorSubmitPaymentRequestExWithAutoConfirmation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitPaymentRequestExWithAutoConfirmationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitPaymentRequestExWithAutoConfirmation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitPaymentRequestExWithAutoConfirmationResult()
	if result.Success, result.Fe, err = p.handler.SubmitPaymentRequestExWithAutoConfirmation(args.Header, args.Uid, args.Cooperator, args.Amount, args.Time, args.TypeA1); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitPaymentRequestExWithAutoConfirmation: "+err.Error())
		oprot.WriteMessageBegin("submitPaymentRequestExWithAutoConfirmation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitPaymentRequestExWithAutoConfirmation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorConfirmPaymentRequestEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorConfirmPaymentRequestEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConfirmPaymentRequestExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("confirmPaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConfirmPaymentRequestExResult()
	if result.Success, result.Fe, err = p.handler.ConfirmPaymentRequestEx(args.Header, args.Id, args.Uid, args.Cooperator, args.ConfirmedAmount, args.AdminUid, args.ExtInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing confirmPaymentRequestEx: "+err.Error())
		oprot.WriteMessageBegin("confirmPaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("confirmPaymentRequestEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorDenyPaymentRequestEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorDenyPaymentRequestEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDenyPaymentRequestExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("denyPaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDenyPaymentRequestExResult()
	if result.Success, result.Fe, err = p.handler.DenyPaymentRequestEx(args.Header, args.Id, args.Uid, args.Cooperator, args.RequestedAmount, args.AdminUid, args.ExtInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing denyPaymentRequestEx: "+err.Error())
		oprot.WriteMessageBegin("denyPaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("denyPaymentRequestEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorCompletePaymentRequestEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorCompletePaymentRequestEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCompletePaymentRequestExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("completePaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCompletePaymentRequestExResult()
	if result.Success, result.Fe, err = p.handler.CompletePaymentRequestEx(args.Header, args.Id, args.Uid, args.Cooperator, args.PaidAmount, args.PaymentFee, args.AdminUid, args.ExtInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing completePaymentRequestEx: "+err.Error())
		oprot.WriteMessageBegin("completePaymentRequestEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("completePaymentRequestEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorListTeamMembersEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorListTeamMembersEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListTeamMembersExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listTeamMembersEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListTeamMembersExResult()
	if result.Success, result.Fe, err = p.handler.ListTeamMembersEx(args.Header, args.Uid, args.Cooperator); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listTeamMembersEx: "+err.Error())
		oprot.WriteMessageBegin("listTeamMembersEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listTeamMembersEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorListTeamMembersExForAdmin struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorListTeamMembersExForAdmin) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListTeamMembersExForAdminArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listTeamMembersExForAdmin", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListTeamMembersExForAdminResult()
	if result.Success, result.Fe, err = p.handler.ListTeamMembersExForAdmin(args.Header, args.Cooperator, args.Uid, args.Statuses, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listTeamMembersExForAdmin: "+err.Error())
		oprot.WriteMessageBegin("listTeamMembersExForAdmin", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listTeamMembersExForAdmin", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorListTeamMembersExByIdentity struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorListTeamMembersExByIdentity) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListTeamMembersExByIdentityArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listTeamMembersExByIdentity", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListTeamMembersExByIdentityResult()
	if result.Success, result.Fe, err = p.handler.ListTeamMembersExByIdentity(args.Header, args.TypeA1, args.Identities, args.Cooperator); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listTeamMembersExByIdentity: "+err.Error())
		oprot.WriteMessageBegin("listTeamMembersExByIdentity", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listTeamMembersExByIdentity", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorListTeamMembersExByName struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorListTeamMembersExByName) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListTeamMembersExByNameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listTeamMembersExByName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListTeamMembersExByNameResult()
	if result.Success, result.Fe, err = p.handler.ListTeamMembersExByName(args.Header, args.Cooperator, args.Name, args.ExactMatching); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listTeamMembersExByName: "+err.Error())
		oprot.WriteMessageBegin("listTeamMembersExByName", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listTeamMembersExByName", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorGetTeamMembersExByIdList struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorGetTeamMembersExByIdList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTeamMembersExByIdListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTeamMembersExByIdList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTeamMembersExByIdListResult()
	if result.Success, result.Fe, err = p.handler.GetTeamMembersExByIdList(args.Header, args.MemberIdList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTeamMembersExByIdList: "+err.Error())
		oprot.WriteMessageBegin("getTeamMembersExByIdList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTeamMembersExByIdList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorPassTeamMemberEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorPassTeamMemberEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPassTeamMemberExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("passTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPassTeamMemberExResult()
	if result.Success, result.Fe, err = p.handler.PassTeamMemberEx(args.Header, args.Cooperator, args.Uid, args.MemberId, args.AdminUid, args.Reason); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing passTeamMemberEx: "+err.Error())
		oprot.WriteMessageBegin("passTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("passTeamMemberEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorRejectTeamMemberEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorRejectTeamMemberEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRejectTeamMemberExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("rejectTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRejectTeamMemberExResult()
	if result.Success, result.Fe, err = p.handler.RejectTeamMemberEx(args.Header, args.Cooperator, args.Uid, args.MemberId, args.AdminUid, args.Reason); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing rejectTeamMemberEx: "+err.Error())
		oprot.WriteMessageBegin("rejectTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("rejectTeamMemberEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorForbidTeamMemberEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorForbidTeamMemberEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewForbidTeamMemberExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("forbidTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewForbidTeamMemberExResult()
	if result.Success, result.Fe, err = p.handler.ForbidTeamMemberEx(args.Header, args.Cooperator, args.Uid, args.MemberId, args.AdminUid, args.Reason); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing forbidTeamMemberEx: "+err.Error())
		oprot.WriteMessageBegin("forbidTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("forbidTeamMemberEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorAddTeamMemberEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorAddTeamMemberEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddTeamMemberExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddTeamMemberExResult()
	if result.Success, result.Fe, err = p.handler.AddTeamMemberEx(args.Header, args.Uid, args.Cooperator, args.Member); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addTeamMemberEx: "+err.Error())
		oprot.WriteMessageBegin("addTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addTeamMemberEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorUpdateTeamMemberEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorUpdateTeamMemberEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateTeamMemberExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateTeamMemberExResult()
	if result.Success, result.Fe, err = p.handler.UpdateTeamMemberEx(args.Header, args.Uid, args.Cooperator, args.Member, args.Submit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateTeamMemberEx: "+err.Error())
		oprot.WriteMessageBegin("updateTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateTeamMemberEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorSubmitTeamMembersEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorSubmitTeamMembersEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSubmitTeamMembersExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("submitTeamMembersEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSubmitTeamMembersExResult()
	if result.Success, result.Fe, err = p.handler.SubmitTeamMembersEx(args.Header, args.Uid, args.Cooperator, args.MemberIdList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing submitTeamMembersEx: "+err.Error())
		oprot.WriteMessageBegin("submitTeamMembersEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("submitTeamMembersEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorRemoveTeamMemberEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorRemoveTeamMemberEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRemoveTeamMemberExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("removeTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRemoveTeamMemberExResult()
	if result.Success, result.Fe, err = p.handler.RemoveTeamMemberEx(args.Header, args.Uid, args.Cooperator, args.MemberId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing removeTeamMemberEx: "+err.Error())
		oprot.WriteMessageBegin("removeTeamMemberEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("removeTeamMemberEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorUpdateTeamMemberSharingEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorUpdateTeamMemberSharingEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateTeamMemberSharingExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateTeamMemberSharingEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateTeamMemberSharingExResult()
	if result.Success, result.Fe, err = p.handler.UpdateTeamMemberSharingEx(args.Header, args.Uid, args.Cooperator, args.SharingMap); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateTeamMemberSharingEx: "+err.Error())
		oprot.WriteMessageBegin("updateTeamMemberSharingEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateTeamMemberSharingEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorSetBeneficiaryEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorSetBeneficiaryEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSetBeneficiaryExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("setBeneficiaryEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSetBeneficiaryExResult()
	if result.Success, result.Fe, err = p.handler.SetBeneficiaryEx(args.Header, args.Uid, args.Beneficiary); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing setBeneficiaryEx: "+err.Error())
		oprot.WriteMessageBegin("setBeneficiaryEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("setBeneficiaryEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorListIncomeConfirmationRecordEx struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorListIncomeConfirmationRecordEx) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListIncomeConfirmationRecordExArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listIncomeConfirmationRecordEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListIncomeConfirmationRecordExResult()
	if result.Success, result.Fe, err = p.handler.ListIncomeConfirmationRecordEx(args.Header, args.Uid, args.FromTime, args.ToTime, args.Status); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listIncomeConfirmationRecordEx: "+err.Error())
		oprot.WriteMessageBegin("listIncomeConfirmationRecordEx", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listIncomeConfirmationRecordEx", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorUpdateFinancialAccountBankInfo struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorUpdateFinancialAccountBankInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateFinancialAccountBankInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateFinancialAccountBankInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateFinancialAccountBankInfoResult()
	if result.Success, result.Fe, err = p.handler.UpdateFinancialAccountBankInfo(args.Header, args.Account); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateFinancialAccountBankInfo: "+err.Error())
		oprot.WriteMessageBegin("updateFinancialAccountBankInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateFinancialAccountBankInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorUpdatePaymentRecordBankInfo struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorUpdatePaymentRecordBankInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdatePaymentRecordBankInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updatePaymentRecordBankInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdatePaymentRecordBankInfoResult()
	if result.Success, result.Fe, err = p.handler.UpdatePaymentRecordBankInfo(args.Header, args.Ids, args.Account); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updatePaymentRecordBankInfo: "+err.Error())
		oprot.WriteMessageBegin("updatePaymentRecordBankInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updatePaymentRecordBankInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorResetFinancialAccount struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorResetFinancialAccount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewResetFinancialAccountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("resetFinancialAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewResetFinancialAccountResult()
	if result.Success, result.Fe, err = p.handler.ResetFinancialAccount(args.Header, args.Uid, args.Cate, args.FrozenAmount, args.Name, args.Identity); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing resetFinancialAccount: "+err.Error())
		oprot.WriteMessageBegin("resetFinancialAccount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("resetFinancialAccount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeDeveloperServiceProcessorMarkFinanceSettleType struct {
	handler FinanceDeveloperService
}

func (p *financeDeveloperServiceProcessorMarkFinanceSettleType) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewMarkFinanceSettleTypeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("markFinanceSettleType", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewMarkFinanceSettleTypeResult()
	if result.Success, result.Fe, err = p.handler.MarkFinanceSettleType(args.Header, args.Uid, args.TypeA1); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing markFinanceSettleType: "+err.Error())
		oprot.WriteMessageBegin("markFinanceSettleType", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("markFinanceSettleType", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ListPaymentRecordExArgs struct {
	Header      *common.RequestHeader     `thrift:"header,1" json:"header"`
	Uid         UidInt                    `thrift:"uid,2" json:"uid"`
	Cooperators []common.CooperateCompany `thrift:"cooperators,3" json:"cooperators"`
	FromTime    TimeInt                   `thrift:"fromTime,4" json:"fromTime"`
	ToTime      TimeInt                   `thrift:"toTime,5" json:"toTime"`
	Statuses    []OperationStatus         `thrift:"statuses,6" json:"statuses"`
	Offset      int32                     `thrift:"offset,7" json:"offset"`
	Limit       int32                     `thrift:"limit,8" json:"limit"`
}

func NewListPaymentRecordExArgs() *ListPaymentRecordExArgs {
	return &ListPaymentRecordExArgs{}
}

func (p *ListPaymentRecordExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Cooperators = make([]common.CooperateCompany, 0, size)
	for i := 0; i < size; i++ {
		var _elem1931 common.CooperateCompany
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1931 = common.CooperateCompany(v)
		}
		p.Cooperators = append(p.Cooperators, _elem1931)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FromTime = TimeInt(v)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ToTime = TimeInt(v)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Statuses = make([]OperationStatus, 0, size)
	for i := 0; i < size; i++ {
		var _elem1932 OperationStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1932 = OperationStatus(v)
		}
		p.Statuses = append(p.Statuses, _elem1932)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListPaymentRecordExArgs) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListPaymentRecordExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPaymentRecordEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPaymentRecordExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListPaymentRecordExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ListPaymentRecordExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Cooperators != nil {
		if err := oprot.WriteFieldBegin("cooperators", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:cooperators: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Cooperators)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Cooperators {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:cooperators: %s", p, err)
		}
	}
	return err
}

func (p *ListPaymentRecordExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fromTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:fromTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FromTime)); err != nil {
		return fmt.Errorf("%T.fromTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:fromTime: %s", p, err)
	}
	return err
}

func (p *ListPaymentRecordExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("toTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:toTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ToTime)); err != nil {
		return fmt.Errorf("%T.toTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:toTime: %s", p, err)
	}
	return err
}

func (p *ListPaymentRecordExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Statuses != nil {
		if err := oprot.WriteFieldBegin("statuses", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:statuses: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Statuses)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Statuses {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:statuses: %s", p, err)
		}
	}
	return err
}

func (p *ListPaymentRecordExArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:offset: %s", p, err)
	}
	return err
}

func (p *ListPaymentRecordExArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:limit: %s", p, err)
	}
	return err
}

func (p *ListPaymentRecordExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPaymentRecordExArgs(%+v)", *p)
}

type ListPaymentRecordExResult struct {
	Success *finance_types.PaymentRecordListResult `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException               `thrift:"fe,1" json:"fe"`
}

func NewListPaymentRecordExResult() *ListPaymentRecordExResult {
	return &ListPaymentRecordExResult{}
}

func (p *ListPaymentRecordExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPaymentRecordExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecordListResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListPaymentRecordExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ListPaymentRecordExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPaymentRecordEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPaymentRecordExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListPaymentRecordExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ListPaymentRecordExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPaymentRecordExResult(%+v)", *p)
}

type GetPaymentRecordExArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    UidInt                `thrift:"uid,2" json:"uid"`
	Ids    []IdInt               `thrift:"ids,3" json:"ids"`
}

func NewGetPaymentRecordExArgs() *GetPaymentRecordExArgs {
	return &GetPaymentRecordExArgs{}
}

func (p *GetPaymentRecordExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPaymentRecordExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPaymentRecordExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *GetPaymentRecordExArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1933 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1933 = IdInt(v)
		}
		p.Ids = append(p.Ids, _elem1933)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPaymentRecordExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPaymentRecordEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPaymentRecordExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPaymentRecordExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *GetPaymentRecordExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetPaymentRecordExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPaymentRecordExArgs(%+v)", *p)
}

type GetPaymentRecordExResult struct {
	Success map[IdInt]*finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException               `thrift:"fe,1" json:"fe"`
}

func NewGetPaymentRecordExResult() *GetPaymentRecordExResult {
	return &GetPaymentRecordExResult{}
}

func (p *GetPaymentRecordExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPaymentRecordExResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[IdInt]*finance_types.PaymentRecord, size)
	for i := 0; i < size; i++ {
		var _key1934 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1934 = IdInt(v)
		}
		_val1935 := finance_types.NewPaymentRecord()
		if err := _val1935.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val1935)
		}
		p.Success[_key1934] = _val1935
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetPaymentRecordExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *GetPaymentRecordExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPaymentRecordEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPaymentRecordExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPaymentRecordExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *GetPaymentRecordExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPaymentRecordExResult(%+v)", *p)
}

type SubmitPaymentRequestExArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Uid        UidInt                  `thrift:"uid,2" json:"uid"`
	Cooperator common.CooperateCompany `thrift:"cooperator,3" json:"cooperator"`
	Amount     Amount                  `thrift:"amount,4" json:"amount"`
	Time       TimeInt                 `thrift:"time,5" json:"time"`
	TypeA1     WithdrawType            `thrift:"type,6" json:"type"`
}

func NewSubmitPaymentRequestExArgs() *SubmitPaymentRequestExArgs {
	return &SubmitPaymentRequestExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value

		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SubmitPaymentRequestExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *SubmitPaymentRequestExArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SubmitPaymentRequestExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Amount = Amount(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TypeA1 = WithdrawType(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequestEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:amount: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:time: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:type: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestExArgs(%+v)", *p)
}

type SubmitPaymentRequestExResult struct {
	Success *finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException     `thrift:"fe,1" json:"fe"`
}

func NewSubmitPaymentRequestExResult() *SubmitPaymentRequestExResult {
	return &SubmitPaymentRequestExResult{}
}

func (p *SubmitPaymentRequestExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SubmitPaymentRequestExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SubmitPaymentRequestExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequestEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestExResult(%+v)", *p)
}

type SubmitPaymentRequestExWithAutoConfirmationArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Uid        UidInt                  `thrift:"uid,2" json:"uid"`
	Cooperator common.CooperateCompany `thrift:"cooperator,3" json:"cooperator"`
	Amount     Amount                  `thrift:"amount,4" json:"amount"`
	Time       TimeInt                 `thrift:"time,5" json:"time"`
	TypeA1     WithdrawType            `thrift:"type,6" json:"type"`
}

func NewSubmitPaymentRequestExWithAutoConfirmationArgs() *SubmitPaymentRequestExWithAutoConfirmationArgs {
	return &SubmitPaymentRequestExWithAutoConfirmationArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value

		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Amount = Amount(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TypeA1 = WithdrawType(v)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequestExWithAutoConfirmation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:amount: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:time: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:type: %s", p, err)
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestExWithAutoConfirmationArgs(%+v)", *p)
}

type SubmitPaymentRequestExWithAutoConfirmationResult struct {
	Success *finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException     `thrift:"fe,1" json:"fe"`
}

func NewSubmitPaymentRequestExWithAutoConfirmationResult() *SubmitPaymentRequestExWithAutoConfirmationResult {
	return &SubmitPaymentRequestExWithAutoConfirmationResult{}
}

func (p *SubmitPaymentRequestExWithAutoConfirmationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitPaymentRequestExWithAutoConfirmation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitPaymentRequestExWithAutoConfirmationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SubmitPaymentRequestExWithAutoConfirmationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitPaymentRequestExWithAutoConfirmationResult(%+v)", *p)
}

type ConfirmPaymentRequestExArgs struct {
	Header          *common.RequestHeader   `thrift:"header,1" json:"header"`
	Id              IdInt                   `thrift:"id,2" json:"id"`
	Uid             UidInt                  `thrift:"uid,3" json:"uid"`
	Cooperator      common.CooperateCompany `thrift:"cooperator,4" json:"cooperator"`
	ConfirmedAmount Amount                  `thrift:"confirmedAmount,5" json:"confirmedAmount"`
	AdminUid        UidInt                  `thrift:"adminUid,6" json:"adminUid"`
	ExtInfo         map[string]string       `thrift:"extInfo,7" json:"extInfo"`
}

func NewConfirmPaymentRequestExArgs() *ConfirmPaymentRequestExArgs {
	return &ConfirmPaymentRequestExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ConfirmPaymentRequestExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *ConfirmPaymentRequestExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ConfirmedAmount = Amount(v)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AdminUid = UidInt(v)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) readField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1936 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1936 = v
		}
		var _val1937 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1937 = v
		}
		p.ExtInfo[_key1936] = _val1937
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("confirmPaymentRequestEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConfirmPaymentRequestExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ConfirmPaymentRequestExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *ConfirmPaymentRequestExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *ConfirmPaymentRequestExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cooperator: %s", p, err)
	}
	return err
}

func (p *ConfirmPaymentRequestExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("confirmedAmount", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:confirmedAmount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConfirmedAmount)); err != nil {
		return fmt.Errorf("%T.confirmedAmount (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:confirmedAmount: %s", p, err)
	}
	return err
}

func (p *ConfirmPaymentRequestExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminUid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:adminUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdminUid)); err != nil {
		return fmt.Errorf("%T.adminUid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:adminUid: %s", p, err)
	}
	return err
}

func (p *ConfirmPaymentRequestExArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *ConfirmPaymentRequestExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConfirmPaymentRequestExArgs(%+v)", *p)
}

type ConfirmPaymentRequestExResult struct {
	Success *finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException     `thrift:"fe,1" json:"fe"`
}

func NewConfirmPaymentRequestExResult() *ConfirmPaymentRequestExResult {
	return &ConfirmPaymentRequestExResult{}
}

func (p *ConfirmPaymentRequestExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConfirmPaymentRequestExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConfirmPaymentRequestExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ConfirmPaymentRequestExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("confirmPaymentRequestEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConfirmPaymentRequestExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConfirmPaymentRequestExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ConfirmPaymentRequestExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConfirmPaymentRequestExResult(%+v)", *p)
}

type DenyPaymentRequestExArgs struct {
	Header          *common.RequestHeader   `thrift:"header,1" json:"header"`
	Id              IdInt                   `thrift:"id,2" json:"id"`
	Uid             UidInt                  `thrift:"uid,3" json:"uid"`
	Cooperator      common.CooperateCompany `thrift:"cooperator,4" json:"cooperator"`
	RequestedAmount Amount                  `thrift:"requestedAmount,5" json:"requestedAmount"`
	AdminUid        UidInt                  `thrift:"adminUid,6" json:"adminUid"`
	ExtInfo         map[string]string       `thrift:"extInfo,7" json:"extInfo"`
}

func NewDenyPaymentRequestExArgs() *DenyPaymentRequestExArgs {
	return &DenyPaymentRequestExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DenyPaymentRequestExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *DenyPaymentRequestExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.RequestedAmount = Amount(v)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AdminUid = UidInt(v)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) readField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1938 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1938 = v
		}
		var _val1939 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1939 = v
		}
		p.ExtInfo[_key1938] = _val1939
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("denyPaymentRequestEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DenyPaymentRequestExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *DenyPaymentRequestExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *DenyPaymentRequestExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *DenyPaymentRequestExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cooperator: %s", p, err)
	}
	return err
}

func (p *DenyPaymentRequestExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requestedAmount", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:requestedAmount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RequestedAmount)); err != nil {
		return fmt.Errorf("%T.requestedAmount (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:requestedAmount: %s", p, err)
	}
	return err
}

func (p *DenyPaymentRequestExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminUid", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:adminUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdminUid)); err != nil {
		return fmt.Errorf("%T.adminUid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:adminUid: %s", p, err)
	}
	return err
}

func (p *DenyPaymentRequestExArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *DenyPaymentRequestExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DenyPaymentRequestExArgs(%+v)", *p)
}

type DenyPaymentRequestExResult struct {
	Success *finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException     `thrift:"fe,1" json:"fe"`
}

func NewDenyPaymentRequestExResult() *DenyPaymentRequestExResult {
	return &DenyPaymentRequestExResult{}
}

func (p *DenyPaymentRequestExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DenyPaymentRequestExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *DenyPaymentRequestExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *DenyPaymentRequestExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("denyPaymentRequestEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DenyPaymentRequestExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DenyPaymentRequestExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *DenyPaymentRequestExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DenyPaymentRequestExResult(%+v)", *p)
}

type CompletePaymentRequestExArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Id         IdInt                   `thrift:"id,2" json:"id"`
	Uid        UidInt                  `thrift:"uid,3" json:"uid"`
	Cooperator common.CooperateCompany `thrift:"cooperator,4" json:"cooperator"`
	PaidAmount Amount                  `thrift:"paidAmount,5" json:"paidAmount"`
	PaymentFee Amount                  `thrift:"paymentFee,6" json:"paymentFee"`
	AdminUid   UidInt                  `thrift:"adminUid,7" json:"adminUid"`
	ExtInfo    map[string]string       `thrift:"extInfo,8" json:"extInfo"`
}

func NewCompletePaymentRequestExArgs() *CompletePaymentRequestExArgs {
	return &CompletePaymentRequestExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CompletePaymentRequestExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *CompletePaymentRequestExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.MAP {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PaidAmount = Amount(v)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PaymentFee = Amount(v)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AdminUid = UidInt(v)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) readField8(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key1940 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1940 = v
		}
		var _val1941 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1941 = v
		}
		p.ExtInfo[_key1940] = _val1941
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("completePaymentRequestEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompletePaymentRequestExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CompletePaymentRequestExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *CompletePaymentRequestExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *CompletePaymentRequestExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cooperator: %s", p, err)
	}
	return err
}

func (p *CompletePaymentRequestExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paidAmount", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:paidAmount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PaidAmount)); err != nil {
		return fmt.Errorf("%T.paidAmount (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:paidAmount: %s", p, err)
	}
	return err
}

func (p *CompletePaymentRequestExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("paymentFee", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:paymentFee: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PaymentFee)); err != nil {
		return fmt.Errorf("%T.paymentFee (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:paymentFee: %s", p, err)
	}
	return err
}

func (p *CompletePaymentRequestExArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminUid", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:adminUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdminUid)); err != nil {
		return fmt.Errorf("%T.adminUid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:adminUid: %s", p, err)
	}
	return err
}

func (p *CompletePaymentRequestExArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *CompletePaymentRequestExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompletePaymentRequestExArgs(%+v)", *p)
}

type CompletePaymentRequestExResult struct {
	Success *finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException     `thrift:"fe,1" json:"fe"`
}

func NewCompletePaymentRequestExResult() *CompletePaymentRequestExResult {
	return &CompletePaymentRequestExResult{}
}

func (p *CompletePaymentRequestExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CompletePaymentRequestExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewPaymentRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CompletePaymentRequestExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *CompletePaymentRequestExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("completePaymentRequestEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CompletePaymentRequestExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CompletePaymentRequestExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *CompletePaymentRequestExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CompletePaymentRequestExResult(%+v)", *p)
}

type ListTeamMembersExArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Uid        UidInt                  `thrift:"uid,2" json:"uid"`
	Cooperator common.CooperateCompany `thrift:"cooperator,3" json:"cooperator"`
}

func NewListTeamMembersExArgs() *ListTeamMembersExArgs {
	return &ListTeamMembersExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListTeamMembersExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *ListTeamMembersExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListTeamMembersExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ListTeamMembersExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *ListTeamMembersExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExArgs(%+v)", *p)
}

type ListTeamMembersExResult struct {
	Success []*finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException    `thrift:"fe,1" json:"fe"`
}

func NewListTeamMembersExResult() *ListTeamMembersExResult {
	return &ListTeamMembersExResult{}
}

func (p *ListTeamMembersExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*finance_types.TeamMember, 0, size)
	for i := 0; i < size; i++ {
		_elem1942 := finance_types.NewTeamMember()
		if err := _elem1942.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1942)
		}
		p.Success = append(p.Success, _elem1942)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListTeamMembersExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ListTeamMembersExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExResult(%+v)", *p)
}

type ListTeamMembersExForAdminArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Cooperator common.CooperateCompany `thrift:"cooperator,2" json:"cooperator"`
	Uid        UidInt                  `thrift:"uid,3" json:"uid"`
	Statuses   []CommonAuditStatus     `thrift:"statuses,4" json:"statuses"`
	Offset     QueryInt                `thrift:"offset,5" json:"offset"`
	Limit      QueryInt                `thrift:"limit,6" json:"limit"`
}

func NewListTeamMembersExForAdminArgs() *ListTeamMembersExForAdminArgs {
	return &ListTeamMembersExForAdminArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListTeamMembersExForAdminArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *ListTeamMembersExForAdminArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Statuses = make([]CommonAuditStatus, 0, size)
	for i := 0; i < size; i++ {
		var _elem1943 CommonAuditStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1943 = CommonAuditStatus(v)
		}
		p.Statuses = append(p.Statuses, _elem1943)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = QueryInt(v)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = QueryInt(v)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersExForAdmin_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExForAdminArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExForAdminArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cooperator: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExForAdminArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExForAdminArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Statuses != nil {
		if err := oprot.WriteFieldBegin("statuses", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:statuses: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Statuses)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Statuses {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:statuses: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExForAdminArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExForAdminArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExForAdminArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExForAdminArgs(%+v)", *p)
}

type ListTeamMembersExForAdminResult struct {
	Success *finance_types.TeamMemberListResult `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException            `thrift:"fe,1" json:"fe"`
}

func NewListTeamMembersExForAdminResult() *ListTeamMembersExForAdminResult {
	return &ListTeamMembersExForAdminResult{}
}

func (p *ListTeamMembersExForAdminResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExForAdminResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMemberListResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListTeamMembersExForAdminResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ListTeamMembersExForAdminResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersExForAdmin_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExForAdminResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExForAdminResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExForAdminResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExForAdminResult(%+v)", *p)
}

type ListTeamMembersExByIdentityArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	TypeA1     IdentityType            `thrift:"type,2" json:"type"`
	Identities []string                `thrift:"identities,3" json:"identities"`
	Cooperator common.CooperateCompany `thrift:"cooperator,4" json:"cooperator"`
}

func NewListTeamMembersExByIdentityArgs() *ListTeamMembersExByIdentityArgs {
	return &ListTeamMembersExByIdentityArgs{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListTeamMembersExByIdentityArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *ListTeamMembersExByIdentityArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *ListTeamMembersExByIdentityArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = IdentityType(v)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Identities = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem1944 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1944 = v
		}
		p.Identities = append(p.Identities, _elem1944)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersExByIdentity_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExByIdentityArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:type: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExByIdentityArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Identities != nil {
		if err := oprot.WriteFieldBegin("identities", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:identities: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Identities)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Identities {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:identities: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExByIdentityArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cooperator: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExByIdentityArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExByIdentityArgs(%+v)", *p)
}

type ListTeamMembersExByIdentityResult struct {
	Success []*finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException    `thrift:"fe,1" json:"fe"`
}

func NewListTeamMembersExByIdentityResult() *ListTeamMembersExByIdentityResult {
	return &ListTeamMembersExByIdentityResult{}
}

func (p *ListTeamMembersExByIdentityResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*finance_types.TeamMember, 0, size)
	for i := 0; i < size; i++ {
		_elem1945 := finance_types.NewTeamMember()
		if err := _elem1945.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1945)
		}
		p.Success = append(p.Success, _elem1945)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersExByIdentity_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByIdentityResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExByIdentityResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExByIdentityResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExByIdentityResult(%+v)", *p)
}

type ListTeamMembersExByNameArgs struct {
	Header        *common.RequestHeader   `thrift:"header,1" json:"header"`
	Cooperator    common.CooperateCompany `thrift:"cooperator,2" json:"cooperator"`
	Name          string                  `thrift:"name,3" json:"name"`
	ExactMatching bool                    `thrift:"exactMatching,4" json:"exactMatching"`
}

func NewListTeamMembersExByNameArgs() *ListTeamMembersExByNameArgs {
	return &ListTeamMembersExByNameArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListTeamMembersExByNameArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *ListTeamMembersExByNameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByNameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListTeamMembersExByNameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *ListTeamMembersExByNameArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ListTeamMembersExByNameArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ExactMatching = v
	}
	return nil
}

func (p *ListTeamMembersExByNameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersExByName_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExByNameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cooperator: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExByNameArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExByNameArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exactMatching", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:exactMatching: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExactMatching)); err != nil {
		return fmt.Errorf("%T.exactMatching (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:exactMatching: %s", p, err)
	}
	return err
}

func (p *ListTeamMembersExByNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExByNameArgs(%+v)", *p)
}

type ListTeamMembersExByNameResult struct {
	Success []*finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException    `thrift:"fe,1" json:"fe"`
}

func NewListTeamMembersExByNameResult() *ListTeamMembersExByNameResult {
	return &ListTeamMembersExByNameResult{}
}

func (p *ListTeamMembersExByNameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByNameResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*finance_types.TeamMember, 0, size)
	for i := 0; i < size; i++ {
		_elem1946 := finance_types.NewTeamMember()
		if err := _elem1946.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1946)
		}
		p.Success = append(p.Success, _elem1946)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListTeamMembersExByNameResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ListTeamMembersExByNameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listTeamMembersExByName_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListTeamMembersExByNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExByNameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ListTeamMembersExByNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListTeamMembersExByNameResult(%+v)", *p)
}

type GetTeamMembersExByIdListArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	MemberIdList []IdInt               `thrift:"memberIdList,2" json:"memberIdList"`
}

func NewGetTeamMembersExByIdListArgs() *GetTeamMembersExByIdListArgs {
	return &GetTeamMembersExByIdListArgs{}
}

func (p *GetTeamMembersExByIdListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTeamMembersExByIdListArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetTeamMembersExByIdListArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MemberIdList = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1947 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1947 = IdInt(v)
		}
		p.MemberIdList = append(p.MemberIdList, _elem1947)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTeamMembersExByIdListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTeamMembersExByIdList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTeamMembersExByIdListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetTeamMembersExByIdListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.MemberIdList != nil {
		if err := oprot.WriteFieldBegin("memberIdList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:memberIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MemberIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MemberIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:memberIdList: %s", p, err)
		}
	}
	return err
}

func (p *GetTeamMembersExByIdListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTeamMembersExByIdListArgs(%+v)", *p)
}

type GetTeamMembersExByIdListResult struct {
	Success map[IdInt]*finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException            `thrift:"fe,1" json:"fe"`
}

func NewGetTeamMembersExByIdListResult() *GetTeamMembersExByIdListResult {
	return &GetTeamMembersExByIdListResult{}
}

func (p *GetTeamMembersExByIdListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTeamMembersExByIdListResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[IdInt]*finance_types.TeamMember, size)
	for i := 0; i < size; i++ {
		var _key1948 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1948 = IdInt(v)
		}
		_val1949 := finance_types.NewTeamMember()
		if err := _val1949.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val1949)
		}
		p.Success[_key1948] = _val1949
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetTeamMembersExByIdListResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *GetTeamMembersExByIdListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTeamMembersExByIdList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTeamMembersExByIdListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTeamMembersExByIdListResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *GetTeamMembersExByIdListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTeamMembersExByIdListResult(%+v)", *p)
}

type PassTeamMemberExArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Cooperator common.CooperateCompany `thrift:"cooperator,2" json:"cooperator"`
	Uid        UidInt                  `thrift:"uid,3" json:"uid"`
	MemberId   IdInt                   `thrift:"memberId,4" json:"memberId"`
	AdminUid   UidInt                  `thrift:"adminUid,5" json:"adminUid"`
	Reason     string                  `thrift:"reason,6" json:"reason"`
}

func NewPassTeamMemberExArgs() *PassTeamMemberExArgs {
	return &PassTeamMemberExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PassTeamMemberExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *PassTeamMemberExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PassTeamMemberExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PassTeamMemberExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *PassTeamMemberExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *PassTeamMemberExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MemberId = IdInt(v)
	}
	return nil
}

func (p *PassTeamMemberExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AdminUid = UidInt(v)
	}
	return nil
}

func (p *PassTeamMemberExArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Reason = v
	}
	return nil
}

func (p *PassTeamMemberExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("passTeamMemberEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PassTeamMemberExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PassTeamMemberExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cooperator: %s", p, err)
	}
	return err
}

func (p *PassTeamMemberExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *PassTeamMemberExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memberId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:memberId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MemberId)); err != nil {
		return fmt.Errorf("%T.memberId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:memberId: %s", p, err)
	}
	return err
}

func (p *PassTeamMemberExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminUid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:adminUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdminUid)); err != nil {
		return fmt.Errorf("%T.adminUid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:adminUid: %s", p, err)
	}
	return err
}

func (p *PassTeamMemberExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reason", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:reason: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Reason)); err != nil {
		return fmt.Errorf("%T.reason (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:reason: %s", p, err)
	}
	return err
}

func (p *PassTeamMemberExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassTeamMemberExArgs(%+v)", *p)
}

type PassTeamMemberExResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewPassTeamMemberExResult() *PassTeamMemberExResult {
	return &PassTeamMemberExResult{}
}

func (p *PassTeamMemberExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PassTeamMemberExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PassTeamMemberExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *PassTeamMemberExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("passTeamMemberEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PassTeamMemberExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PassTeamMemberExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *PassTeamMemberExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PassTeamMemberExResult(%+v)", *p)
}

type RejectTeamMemberExArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Cooperator common.CooperateCompany `thrift:"cooperator,2" json:"cooperator"`
	Uid        UidInt                  `thrift:"uid,3" json:"uid"`
	MemberId   IdInt                   `thrift:"memberId,4" json:"memberId"`
	AdminUid   UidInt                  `thrift:"adminUid,5" json:"adminUid"`
	Reason     string                  `thrift:"reason,6" json:"reason"`
}

func NewRejectTeamMemberExArgs() *RejectTeamMemberExArgs {
	return &RejectTeamMemberExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RejectTeamMemberExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *RejectTeamMemberExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RejectTeamMemberExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RejectTeamMemberExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *RejectTeamMemberExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *RejectTeamMemberExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MemberId = IdInt(v)
	}
	return nil
}

func (p *RejectTeamMemberExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AdminUid = UidInt(v)
	}
	return nil
}

func (p *RejectTeamMemberExArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Reason = v
	}
	return nil
}

func (p *RejectTeamMemberExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rejectTeamMemberEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RejectTeamMemberExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RejectTeamMemberExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cooperator: %s", p, err)
	}
	return err
}

func (p *RejectTeamMemberExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *RejectTeamMemberExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memberId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:memberId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MemberId)); err != nil {
		return fmt.Errorf("%T.memberId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:memberId: %s", p, err)
	}
	return err
}

func (p *RejectTeamMemberExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminUid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:adminUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdminUid)); err != nil {
		return fmt.Errorf("%T.adminUid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:adminUid: %s", p, err)
	}
	return err
}

func (p *RejectTeamMemberExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reason", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:reason: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Reason)); err != nil {
		return fmt.Errorf("%T.reason (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:reason: %s", p, err)
	}
	return err
}

func (p *RejectTeamMemberExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RejectTeamMemberExArgs(%+v)", *p)
}

type RejectTeamMemberExResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewRejectTeamMemberExResult() *RejectTeamMemberExResult {
	return &RejectTeamMemberExResult{}
}

func (p *RejectTeamMemberExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RejectTeamMemberExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RejectTeamMemberExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *RejectTeamMemberExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rejectTeamMemberEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RejectTeamMemberExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RejectTeamMemberExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *RejectTeamMemberExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RejectTeamMemberExResult(%+v)", *p)
}

type ForbidTeamMemberExArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Cooperator common.CooperateCompany `thrift:"cooperator,2" json:"cooperator"`
	Uid        UidInt                  `thrift:"uid,3" json:"uid"`
	MemberId   IdInt                   `thrift:"memberId,4" json:"memberId"`
	AdminUid   UidInt                  `thrift:"adminUid,5" json:"adminUid"`
	Reason     string                  `thrift:"reason,6" json:"reason"`
}

func NewForbidTeamMemberExArgs() *ForbidTeamMemberExArgs {
	return &ForbidTeamMemberExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ForbidTeamMemberExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *ForbidTeamMemberExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MemberId = IdInt(v)
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AdminUid = UidInt(v)
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Reason = v
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("forbidTeamMemberEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ForbidTeamMemberExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ForbidTeamMemberExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cooperator: %s", p, err)
	}
	return err
}

func (p *ForbidTeamMemberExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:uid: %s", p, err)
	}
	return err
}

func (p *ForbidTeamMemberExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memberId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:memberId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MemberId)); err != nil {
		return fmt.Errorf("%T.memberId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:memberId: %s", p, err)
	}
	return err
}

func (p *ForbidTeamMemberExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adminUid", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:adminUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdminUid)); err != nil {
		return fmt.Errorf("%T.adminUid (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:adminUid: %s", p, err)
	}
	return err
}

func (p *ForbidTeamMemberExArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reason", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:reason: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Reason)); err != nil {
		return fmt.Errorf("%T.reason (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:reason: %s", p, err)
	}
	return err
}

func (p *ForbidTeamMemberExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ForbidTeamMemberExArgs(%+v)", *p)
}

type ForbidTeamMemberExResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewForbidTeamMemberExResult() *ForbidTeamMemberExResult {
	return &ForbidTeamMemberExResult{}
}

func (p *ForbidTeamMemberExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ForbidTeamMemberExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ForbidTeamMemberExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ForbidTeamMemberExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("forbidTeamMemberEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ForbidTeamMemberExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ForbidTeamMemberExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ForbidTeamMemberExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ForbidTeamMemberExResult(%+v)", *p)
}

type AddTeamMemberExArgs struct {
	Header     *common.RequestHeader     `thrift:"header,1" json:"header"`
	Uid        UidInt                    `thrift:"uid,2" json:"uid"`
	Cooperator common.CooperateCompany   `thrift:"cooperator,3" json:"cooperator"`
	Member     *finance_types.TeamMember `thrift:"member,4" json:"member"`
}

func NewAddTeamMemberExArgs() *AddTeamMemberExArgs {
	return &AddTeamMemberExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddTeamMemberExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *AddTeamMemberExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTeamMemberExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AddTeamMemberExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *AddTeamMemberExArgs) readField4(iprot thrift.TProtocol) error {
	p.Member = finance_types.NewTeamMember()
	if err := p.Member.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Member)
	}
	return nil
}

func (p *AddTeamMemberExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTeamMemberEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AddTeamMemberExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *AddTeamMemberExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Member != nil {
		if err := oprot.WriteFieldBegin("member", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:member: %s", p, err)
		}
		if err := p.Member.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Member)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:member: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTeamMemberExArgs(%+v)", *p)
}

type AddTeamMemberExResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewAddTeamMemberExResult() *AddTeamMemberExResult {
	return &AddTeamMemberExResult{}
}

func (p *AddTeamMemberExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddTeamMemberExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *AddTeamMemberExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTeamMemberEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTeamMemberExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *AddTeamMemberExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTeamMemberExResult(%+v)", *p)
}

type UpdateTeamMemberExArgs struct {
	Header     *common.RequestHeader     `thrift:"header,1" json:"header"`
	Uid        UidInt                    `thrift:"uid,2" json:"uid"`
	Cooperator common.CooperateCompany   `thrift:"cooperator,3" json:"cooperator"`
	Member     *finance_types.TeamMember `thrift:"member,4" json:"member"`
	Submit     bool                      `thrift:"submit,5" json:"submit"`
}

func NewUpdateTeamMemberExArgs() *UpdateTeamMemberExArgs {
	return &UpdateTeamMemberExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateTeamMemberExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *UpdateTeamMemberExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateTeamMemberExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *UpdateTeamMemberExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *UpdateTeamMemberExArgs) readField4(iprot thrift.TProtocol) error {
	p.Member = finance_types.NewTeamMember()
	if err := p.Member.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Member)
	}
	return nil
}

func (p *UpdateTeamMemberExArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Submit = v
	}
	return nil
}

func (p *UpdateTeamMemberExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMemberEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Member != nil {
		if err := oprot.WriteFieldBegin("member", thrift.STRUCT, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:member: %s", p, err)
		}
		if err := p.Member.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Member)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:member: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("submit", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:submit: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Submit)); err != nil {
		return fmt.Errorf("%T.submit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:submit: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberExArgs(%+v)", *p)
}

type UpdateTeamMemberExResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewUpdateTeamMemberExResult() *UpdateTeamMemberExResult {
	return &UpdateTeamMemberExResult{}
}

func (p *UpdateTeamMemberExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *UpdateTeamMemberExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdateTeamMemberExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMemberEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberExResult(%+v)", *p)
}

type SubmitTeamMembersExArgs struct {
	Header       *common.RequestHeader   `thrift:"header,1" json:"header"`
	Uid          UidInt                  `thrift:"uid,2" json:"uid"`
	Cooperator   common.CooperateCompany `thrift:"cooperator,3" json:"cooperator"`
	MemberIdList []IdInt                 `thrift:"memberIdList,4" json:"memberIdList"`
}

func NewSubmitTeamMembersExArgs() *SubmitTeamMembersExArgs {
	return &SubmitTeamMembersExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SubmitTeamMembersExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *SubmitTeamMembersExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMembersExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SubmitTeamMembersExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SubmitTeamMembersExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *SubmitTeamMembersExArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MemberIdList = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1950 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1950 = IdInt(v)
		}
		p.MemberIdList = append(p.MemberIdList, _elem1950)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SubmitTeamMembersExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitTeamMembersEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMembersExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SubmitTeamMembersExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SubmitTeamMembersExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *SubmitTeamMembersExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.MemberIdList != nil {
		if err := oprot.WriteFieldBegin("memberIdList", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:memberIdList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MemberIdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MemberIdList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:memberIdList: %s", p, err)
		}
	}
	return err
}

func (p *SubmitTeamMembersExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitTeamMembersExArgs(%+v)", *p)
}

type SubmitTeamMembersExResult struct {
	Success map[IdInt]*finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException            `thrift:"fe,1" json:"fe"`
}

func NewSubmitTeamMembersExResult() *SubmitTeamMembersExResult {
	return &SubmitTeamMembersExResult{}
}

func (p *SubmitTeamMembersExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMembersExResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[IdInt]*finance_types.TeamMember, size)
	for i := 0; i < size; i++ {
		var _key1951 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1951 = IdInt(v)
		}
		_val1952 := finance_types.NewTeamMember()
		if err := _val1952.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val1952)
		}
		p.Success[_key1951] = _val1952
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *SubmitTeamMembersExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SubmitTeamMembersExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("submitTeamMembersEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SubmitTeamMembersExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SubmitTeamMembersExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SubmitTeamMembersExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SubmitTeamMembersExResult(%+v)", *p)
}

type RemoveTeamMemberExArgs struct {
	Header     *common.RequestHeader   `thrift:"header,1" json:"header"`
	Uid        UidInt                  `thrift:"uid,2" json:"uid"`
	Cooperator common.CooperateCompany `thrift:"cooperator,3" json:"cooperator"`
	MemberId   IdInt                   `thrift:"memberId,4" json:"memberId"`
}

func NewRemoveTeamMemberExArgs() *RemoveTeamMemberExArgs {
	return &RemoveTeamMemberExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RemoveTeamMemberExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *RemoveTeamMemberExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RemoveTeamMemberExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *RemoveTeamMemberExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *RemoveTeamMemberExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MemberId = IdInt(v)
	}
	return nil
}

func (p *RemoveTeamMemberExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeTeamMemberEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RemoveTeamMemberExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *RemoveTeamMemberExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *RemoveTeamMemberExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memberId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:memberId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MemberId)); err != nil {
		return fmt.Errorf("%T.memberId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:memberId: %s", p, err)
	}
	return err
}

func (p *RemoveTeamMemberExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveTeamMemberExArgs(%+v)", *p)
}

type RemoveTeamMemberExResult struct {
	Success *finance_types.TeamMember `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException  `thrift:"fe,1" json:"fe"`
}

func NewRemoveTeamMemberExResult() *RemoveTeamMemberExResult {
	return &RemoveTeamMemberExResult{}
}

func (p *RemoveTeamMemberExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewTeamMember()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RemoveTeamMemberExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *RemoveTeamMemberExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("removeTeamMemberEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RemoveTeamMemberExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RemoveTeamMemberExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *RemoveTeamMemberExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RemoveTeamMemberExResult(%+v)", *p)
}

type UpdateTeamMemberSharingExArgs struct {
	Header     *common.RequestHeader          `thrift:"header,1" json:"header"`
	Uid        UidInt                         `thrift:"uid,2" json:"uid"`
	Cooperator common.CooperateCompany        `thrift:"cooperator,3" json:"cooperator"`
	SharingMap map[IdInt]common.PercentageInt `thrift:"sharingMap,4" json:"sharingMap"`
}

func NewUpdateTeamMemberSharingExArgs() *UpdateTeamMemberSharingExArgs {
	return &UpdateTeamMemberSharingExArgs{
		Cooperator: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateTeamMemberSharingExArgs) IsSetCooperator() bool {
	return int64(p.Cooperator) != math.MinInt32-1
}

func (p *UpdateTeamMemberSharingExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cooperator = common.CooperateCompany(v)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExArgs) readField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.SharingMap = make(map[IdInt]common.PercentageInt, size)
	for i := 0; i < size; i++ {
		var _key1953 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1953 = IdInt(v)
		}
		var _val1954 common.PercentageInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1954 = common.PercentageInt(v)
		}
		p.SharingMap[_key1953] = _val1954
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMemberSharingEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberSharingExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cooperator", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cooperator: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cooperator)); err != nil {
		return fmt.Errorf("%T.cooperator (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cooperator: %s", p, err)
	}
	return err
}

func (p *UpdateTeamMemberSharingExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.SharingMap != nil {
		if err := oprot.WriteFieldBegin("sharingMap", thrift.MAP, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:sharingMap: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.SharingMap)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.SharingMap {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:sharingMap: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberSharingExArgs(%+v)", *p)
}

type UpdateTeamMemberSharingExResult struct {
	Success map[IdInt]common.PercentageInt `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException       `thrift:"fe,1" json:"fe"`
}

func NewUpdateTeamMemberSharingExResult() *UpdateTeamMemberSharingExResult {
	return &UpdateTeamMemberSharingExResult{}
}

func (p *UpdateTeamMemberSharingExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[IdInt]common.PercentageInt, size)
	for i := 0; i < size; i++ {
		var _key1955 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key1955 = IdInt(v)
		}
		var _val1956 common.PercentageInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1956 = common.PercentageInt(v)
		}
		p.Success[_key1955] = _val1956
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateTeamMemberSharingEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTeamMemberSharingExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTeamMemberSharingExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTeamMemberSharingExResult(%+v)", *p)
}

type SetBeneficiaryExArgs struct {
	Header      *common.RequestHeader      `thrift:"header,1" json:"header"`
	Uid         UidInt                     `thrift:"uid,2" json:"uid"`
	Beneficiary *finance_types.Beneficiary `thrift:"beneficiary,3" json:"beneficiary"`
}

func NewSetBeneficiaryExArgs() *SetBeneficiaryExArgs {
	return &SetBeneficiaryExArgs{}
}

func (p *SetBeneficiaryExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SetBeneficiaryExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *SetBeneficiaryExArgs) readField3(iprot thrift.TProtocol) error {
	p.Beneficiary = finance_types.NewBeneficiary()
	if err := p.Beneficiary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Beneficiary)
	}
	return nil
}

func (p *SetBeneficiaryExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setBeneficiaryEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *SetBeneficiaryExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Beneficiary != nil {
		if err := oprot.WriteFieldBegin("beneficiary", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:beneficiary: %s", p, err)
		}
		if err := p.Beneficiary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Beneficiary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:beneficiary: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetBeneficiaryExArgs(%+v)", *p)
}

type SetBeneficiaryExResult struct {
	Success *finance_types.FinancialAccount `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException        `thrift:"fe,1" json:"fe"`
}

func NewSetBeneficiaryExResult() *SetBeneficiaryExResult {
	return &SetBeneficiaryExResult{}
}

func (p *SetBeneficiaryExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryExResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewFinancialAccount()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SetBeneficiaryExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SetBeneficiaryExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setBeneficiaryEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetBeneficiaryExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SetBeneficiaryExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetBeneficiaryExResult(%+v)", *p)
}

type ListIncomeConfirmationRecordExArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid      UidInt                `thrift:"uid,2" json:"uid"`
	FromTime TimeInt               `thrift:"fromTime,3" json:"fromTime"`
	ToTime   TimeInt               `thrift:"toTime,4" json:"toTime"`
	Status   []OperationStatus     `thrift:"status,5" json:"status"`
}

func NewListIncomeConfirmationRecordExArgs() *ListIncomeConfirmationRecordExArgs {
	return &ListIncomeConfirmationRecordExArgs{}
}

func (p *ListIncomeConfirmationRecordExArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FromTime = TimeInt(v)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ToTime = TimeInt(v)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExArgs) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Status = make([]OperationStatus, 0, size)
	for i := 0; i < size; i++ {
		var _elem1957 OperationStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1957 = OperationStatus(v)
		}
		p.Status = append(p.Status, _elem1957)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listIncomeConfirmationRecordEx_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListIncomeConfirmationRecordExArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ListIncomeConfirmationRecordExArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fromTime", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:fromTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FromTime)); err != nil {
		return fmt.Errorf("%T.fromTime (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:fromTime: %s", p, err)
	}
	return err
}

func (p *ListIncomeConfirmationRecordExArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("toTime", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:toTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ToTime)); err != nil {
		return fmt.Errorf("%T.toTime (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:toTime: %s", p, err)
	}
	return err
}

func (p *ListIncomeConfirmationRecordExArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Status != nil {
		if err := oprot.WriteFieldBegin("status", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:status: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Status)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Status {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:status: %s", p, err)
		}
	}
	return err
}

func (p *ListIncomeConfirmationRecordExArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListIncomeConfirmationRecordExArgs(%+v)", *p)
}

type ListIncomeConfirmationRecordExResult struct {
	Success []*finance_types.IncomeConfirmationRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException                  `thrift:"fe,1" json:"fe"`
}

func NewListIncomeConfirmationRecordExResult() *ListIncomeConfirmationRecordExResult {
	return &ListIncomeConfirmationRecordExResult{}
}

func (p *ListIncomeConfirmationRecordExResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*finance_types.IncomeConfirmationRecord, 0, size)
	for i := 0; i < size; i++ {
		_elem1958 := finance_types.NewIncomeConfirmationRecord()
		if err := _elem1958.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1958)
		}
		p.Success = append(p.Success, _elem1958)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listIncomeConfirmationRecordEx_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListIncomeConfirmationRecordExResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListIncomeConfirmationRecordExResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ListIncomeConfirmationRecordExResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListIncomeConfirmationRecordExResult(%+v)", *p)
}

type UpdateFinancialAccountBankInfoArgs struct {
	Header  *common.RequestHeader           `thrift:"header,1" json:"header"`
	Account *finance_types.FinancialAccount `thrift:"account,2" json:"account"`
}

func NewUpdateFinancialAccountBankInfoArgs() *UpdateFinancialAccountBankInfoArgs {
	return &UpdateFinancialAccountBankInfoArgs{}
}

func (p *UpdateFinancialAccountBankInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.Account = finance_types.NewFinancialAccount()
	if err := p.Account.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Account)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateFinancialAccountBankInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountBankInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Account != nil {
		if err := oprot.WriteFieldBegin("account", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:account: %s", p, err)
		}
		if err := p.Account.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Account)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:account: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountBankInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFinancialAccountBankInfoArgs(%+v)", *p)
}

type UpdateFinancialAccountBankInfoResult struct {
	Success *finance_types.FinancialAccount `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException        `thrift:"fe,1" json:"fe"`
}

func NewUpdateFinancialAccountBankInfoResult() *UpdateFinancialAccountBankInfoResult {
	return &UpdateFinancialAccountBankInfoResult{}
}

func (p *UpdateFinancialAccountBankInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewFinancialAccount()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateFinancialAccountBankInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateFinancialAccountBankInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountBankInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdateFinancialAccountBankInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateFinancialAccountBankInfoResult(%+v)", *p)
}

type UpdatePaymentRecordBankInfoArgs struct {
	Header  *common.RequestHeader           `thrift:"header,1" json:"header"`
	Ids     []IdInt                         `thrift:"ids,2" json:"ids"`
	Account *finance_types.FinancialAccount `thrift:"account,3" json:"account"`
}

func NewUpdatePaymentRecordBankInfoArgs() *UpdatePaymentRecordBankInfoArgs {
	return &UpdatePaymentRecordBankInfoArgs{}
}

func (p *UpdatePaymentRecordBankInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1959 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1959 = IdInt(v)
		}
		p.Ids = append(p.Ids, _elem1959)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoArgs) readField3(iprot thrift.TProtocol) error {
	p.Account = finance_types.NewFinancialAccount()
	if err := p.Account.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Account)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updatePaymentRecordBankInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdatePaymentRecordBankInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *UpdatePaymentRecordBankInfoArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Account != nil {
		if err := oprot.WriteFieldBegin("account", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:account: %s", p, err)
		}
		if err := p.Account.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Account)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:account: %s", p, err)
		}
	}
	return err
}

func (p *UpdatePaymentRecordBankInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePaymentRecordBankInfoArgs(%+v)", *p)
}

type UpdatePaymentRecordBankInfoResult struct {
	Success []*finance_types.PaymentRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException       `thrift:"fe,1" json:"fe"`
}

func NewUpdatePaymentRecordBankInfoResult() *UpdatePaymentRecordBankInfoResult {
	return &UpdatePaymentRecordBankInfoResult{}
}

func (p *UpdatePaymentRecordBankInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*finance_types.PaymentRecord, 0, size)
	for i := 0; i < size; i++ {
		_elem1960 := finance_types.NewPaymentRecord()
		if err := _elem1960.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1960)
		}
		p.Success = append(p.Success, _elem1960)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updatePaymentRecordBankInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdatePaymentRecordBankInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdatePaymentRecordBankInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *UpdatePaymentRecordBankInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdatePaymentRecordBankInfoResult(%+v)", *p)
}

type ResetFinancialAccountArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid          UidInt                `thrift:"uid,2" json:"uid"`
	Cate         AccountCategory       `thrift:"cate,3" json:"cate"`
	FrozenAmount Amount                `thrift:"frozenAmount,4" json:"frozenAmount"`
	Name         string                `thrift:"name,5" json:"name"`
	Identity     string                `thrift:"identity,6" json:"identity"`
}

func NewResetFinancialAccountArgs() *ResetFinancialAccountArgs {
	return &ResetFinancialAccountArgs{
		Cate: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ResetFinancialAccountArgs) IsSetCate() bool {
	return int64(p.Cate) != math.MinInt32-1
}

func (p *ResetFinancialAccountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetFinancialAccountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ResetFinancialAccountArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *ResetFinancialAccountArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cate = AccountCategory(v)
	}
	return nil
}

func (p *ResetFinancialAccountArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FrozenAmount = Amount(v)
	}
	return nil
}

func (p *ResetFinancialAccountArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *ResetFinancialAccountArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Identity = v
	}
	return nil
}

func (p *ResetFinancialAccountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetFinancialAccount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetFinancialAccountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ResetFinancialAccountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *ResetFinancialAccountArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cate", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cate)); err != nil {
		return fmt.Errorf("%T.cate (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cate: %s", p, err)
	}
	return err
}

func (p *ResetFinancialAccountArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("frozenAmount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:frozenAmount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.FrozenAmount)); err != nil {
		return fmt.Errorf("%T.frozenAmount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:frozenAmount: %s", p, err)
	}
	return err
}

func (p *ResetFinancialAccountArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *ResetFinancialAccountArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("identity", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:identity: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Identity)); err != nil {
		return fmt.Errorf("%T.identity (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:identity: %s", p, err)
	}
	return err
}

func (p *ResetFinancialAccountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetFinancialAccountArgs(%+v)", *p)
}

type ResetFinancialAccountResult struct {
	Success *finance_types.FinancialAccount `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException        `thrift:"fe,1" json:"fe"`
}

func NewResetFinancialAccountResult() *ResetFinancialAccountResult {
	return &ResetFinancialAccountResult{}
}

func (p *ResetFinancialAccountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResetFinancialAccountResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewFinancialAccount()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ResetFinancialAccountResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *ResetFinancialAccountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resetFinancialAccount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResetFinancialAccountResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ResetFinancialAccountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *ResetFinancialAccountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResetFinancialAccountResult(%+v)", *p)
}

type MarkFinanceSettleTypeArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Uid    UidInt                `thrift:"uid,2" json:"uid"`
	TypeA1 SettleType            `thrift:"type,3" json:"type"`
}

func NewMarkFinanceSettleTypeArgs() *MarkFinanceSettleTypeArgs {
	return &MarkFinanceSettleTypeArgs{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MarkFinanceSettleTypeArgs) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *MarkFinanceSettleTypeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MarkFinanceSettleTypeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *MarkFinanceSettleTypeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *MarkFinanceSettleTypeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = SettleType(v)
	}
	return nil
}

func (p *MarkFinanceSettleTypeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("markFinanceSettleType_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MarkFinanceSettleTypeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *MarkFinanceSettleTypeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *MarkFinanceSettleTypeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:type: %s", p, err)
	}
	return err
}

func (p *MarkFinanceSettleTypeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MarkFinanceSettleTypeArgs(%+v)", *p)
}

type MarkFinanceSettleTypeResult struct {
	Success *finance_types.FinancialProfile `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException        `thrift:"fe,1" json:"fe"`
}

func NewMarkFinanceSettleTypeResult() *MarkFinanceSettleTypeResult {
	return &MarkFinanceSettleTypeResult{}
}

func (p *MarkFinanceSettleTypeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MarkFinanceSettleTypeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewFinancialProfile()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *MarkFinanceSettleTypeResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *MarkFinanceSettleTypeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("markFinanceSettleType_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MarkFinanceSettleTypeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *MarkFinanceSettleTypeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *MarkFinanceSettleTypeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MarkFinanceSettleTypeResult(%+v)", *p)
}
