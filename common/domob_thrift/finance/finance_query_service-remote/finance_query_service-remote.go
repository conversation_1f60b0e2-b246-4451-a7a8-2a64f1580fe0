// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "   getFinancialStatus(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  FinancialSummary getFinancialSummary(RequestHeader header, UidInt uid)")
	fmt.Fprintln(os.<PERSON>, "  FinancialProfile getFinancialProfile(RequestHeader header, UidInt uid)")
	fmt.Fprintln(os.Stderr, "   listConsumeRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime)")
	fmt.Fprintln(os.Stderr, "  RechargeRecordResult listRechargeRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  operationStatus, bool invoiceNotSentOnly, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getRechargeRecord(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "  AdInvoiceRecordResult listAdInvoiceRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  operationStatus, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getAdInvoiceRecord(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "   listIncomeRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime)")
	fmt.Fprintln(os.Stderr, "  WithdrawRecordResult listWithdrawRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime, WithdrawType type,  operationStatus, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getWithdrawRecord(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "  MediaInvoiceRecordResult listMediaInvoiceRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  operationStatus, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getMediaInvoiceRecord(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult listAdUsers(RequestHeader header, bool available, bool ascending, UidInt fromUid, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "  QueryResult listMediaUsers(RequestHeader header, bool available, bool ascending, UidInt fromUid, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "  SysAccountSummary getSystemAccountSummary(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "   listAwardRecord(RequestHeader header, UidInt uid, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "  FinancialSummary getFinancialSummaryNew(RequestHeader header, UidInt uid)")
	fmt.Fprintln(os.Stderr, "  QueryResult listAccountIdByRole(RequestHeader header, UserRole role, bool available, QueryInt offset, QueryInt limit, bool ascending)")
	fmt.Fprintln(os.Stderr, "   getSummariesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getFinancialAccountByUidList(RequestHeader header,  uidList)")
	fmt.Fprintln(os.Stderr, "   listTeamMembers(RequestHeader header, UidInt uid)")
	fmt.Fprintln(os.Stderr, "  TeamMember getTeamMemberByIdentity(RequestHeader header, IdentityType type, string identity)")
	fmt.Fprintln(os.Stderr, "   getTeamMembersByName(RequestHeader header, string name)")
	fmt.Fprintln(os.Stderr, "   getTeamMembersByIdList(RequestHeader header,  memberIdList)")
	fmt.Fprintln(os.Stderr, "   listIncomeShare(RequestHeader header, IdInt withdrawRecordId)")
	fmt.Fprintln(os.Stderr, "   listIncomeHistory(RequestHeader header,  memberIdList, i32 fromMonth, i32 toMonth)")
	fmt.Fprintln(os.Stderr, "   getAdPlanBudgetStatus(RequestHeader header, UidInt uid,  pids, TimeInt effectiveFrom, TimeInt effectiveTo)")
	fmt.Fprintln(os.Stderr, "   getAdStrategyBudgetStatus(RequestHeader header, UidInt uid, IdInt pid,  sids, TimeInt effectiveFrom, TimeInt effectiveTo)")
	fmt.Fprintln(os.Stderr, "   listPaymentRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  statuses)")
	fmt.Fprintln(os.Stderr, "   getPaymentRecord(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "   listIncomeConfirmationRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  status)")
	fmt.Fprintln(os.Stderr, "   listAwardRecordByTime(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime)")
	fmt.Fprintln(os.Stderr, "   listIncomeConfirmationRecordNew(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  status)")
	fmt.Fprintln(os.Stderr, "   getIncomeConfirmationRecordList(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  status)")
	fmt.Fprintln(os.Stderr, "   listPaymentRecordNew(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  statuses)")
	fmt.Fprintln(os.Stderr, "   listAppropriationRecord(RequestHeader header,  uids, TimeInt fromTime, TimeInt toTime)")
	fmt.Fprintln(os.Stderr, "   listAdPlanBudgetHistory(RequestHeader header,  uids, TimeInt dateStart)")
	fmt.Fprintln(os.Stderr, "   listAgentAppropriationRecord(RequestHeader header, UidInt agentUid, TimeInt fromTime, TimeInt toTime)")
	fmt.Fprintln(os.Stderr, "   getAllAdUsers(RequestHeader header, UidInt fromUid, QueryInt limit)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := finance.NewFinanceQueryServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getFinancialStatus":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinancialStatus requires 2 args")
			flag.Usage()
		}
		arg228 := flag.Arg(1)
		mbTrans229 := thrift.NewTMemoryBufferLen(len(arg228))
		defer mbTrans229.Close()
		_, err230 := mbTrans229.WriteString(arg228)
		if err230 != nil {
			Usage()
			return
		}
		factory231 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt232 := factory231.GetProtocol(mbTrans229)
		argvalue0 := finance.NewRequestHeader()
		err233 := argvalue0.Read(jsProt232)
		if err233 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg234 := flag.Arg(2)
		mbTrans235 := thrift.NewTMemoryBufferLen(len(arg234))
		defer mbTrans235.Close()
		_, err236 := mbTrans235.WriteString(arg234)
		if err236 != nil {
			Usage()
			return
		}
		factory237 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt238 := factory237.GetProtocol(mbTrans235)
		containerStruct1 := finance.NewGetFinancialStatusArgs()
		err239 := containerStruct1.ReadField2(jsProt238)
		if err239 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetFinancialStatus(value0, value1))
		fmt.Print("\n")
		break
	case "getFinancialSummary":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinancialSummary requires 2 args")
			flag.Usage()
		}
		arg240 := flag.Arg(1)
		mbTrans241 := thrift.NewTMemoryBufferLen(len(arg240))
		defer mbTrans241.Close()
		_, err242 := mbTrans241.WriteString(arg240)
		if err242 != nil {
			Usage()
			return
		}
		factory243 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt244 := factory243.GetProtocol(mbTrans241)
		argvalue0 := finance.NewRequestHeader()
		err245 := argvalue0.Read(jsProt244)
		if err245 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err246 := (strconv.Atoi(flag.Arg(2)))
		if err246 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		fmt.Print(client.GetFinancialSummary(value0, value1))
		fmt.Print("\n")
		break
	case "getFinancialProfile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinancialProfile requires 2 args")
			flag.Usage()
		}
		arg247 := flag.Arg(1)
		mbTrans248 := thrift.NewTMemoryBufferLen(len(arg247))
		defer mbTrans248.Close()
		_, err249 := mbTrans248.WriteString(arg247)
		if err249 != nil {
			Usage()
			return
		}
		factory250 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt251 := factory250.GetProtocol(mbTrans248)
		argvalue0 := finance.NewRequestHeader()
		err252 := argvalue0.Read(jsProt251)
		if err252 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err253 := (strconv.Atoi(flag.Arg(2)))
		if err253 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		fmt.Print(client.GetFinancialProfile(value0, value1))
		fmt.Print("\n")
		break
	case "listConsumeRecord":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListConsumeRecord requires 4 args")
			flag.Usage()
		}
		arg254 := flag.Arg(1)
		mbTrans255 := thrift.NewTMemoryBufferLen(len(arg254))
		defer mbTrans255.Close()
		_, err256 := mbTrans255.WriteString(arg254)
		if err256 != nil {
			Usage()
			return
		}
		factory257 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt258 := factory257.GetProtocol(mbTrans255)
		argvalue0 := finance.NewRequestHeader()
		err259 := argvalue0.Read(jsProt258)
		if err259 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err260 := (strconv.Atoi(flag.Arg(2)))
		if err260 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err261 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err261 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err262 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err262 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		fmt.Print(client.ListConsumeRecord(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listRechargeRecord":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "ListRechargeRecord requires 8 args")
			flag.Usage()
		}
		arg263 := flag.Arg(1)
		mbTrans264 := thrift.NewTMemoryBufferLen(len(arg263))
		defer mbTrans264.Close()
		_, err265 := mbTrans264.WriteString(arg263)
		if err265 != nil {
			Usage()
			return
		}
		factory266 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt267 := factory266.GetProtocol(mbTrans264)
		argvalue0 := finance.NewRequestHeader()
		err268 := argvalue0.Read(jsProt267)
		if err268 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err269 := (strconv.Atoi(flag.Arg(2)))
		if err269 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err270 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err270 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err271 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err271 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg272 := flag.Arg(5)
		mbTrans273 := thrift.NewTMemoryBufferLen(len(arg272))
		defer mbTrans273.Close()
		_, err274 := mbTrans273.WriteString(arg272)
		if err274 != nil {
			Usage()
			return
		}
		factory275 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt276 := factory275.GetProtocol(mbTrans273)
		containerStruct4 := finance.NewListRechargeRecordArgs()
		err277 := containerStruct4.ReadField5(jsProt276)
		if err277 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.OperationStatus
		value4 := argvalue4
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		tmp6, err279 := (strconv.Atoi(flag.Arg(7)))
		if err279 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := finance.QueryInt(argvalue6)
		tmp7, err280 := (strconv.Atoi(flag.Arg(8)))
		if err280 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := finance.QueryInt(argvalue7)
		fmt.Print(client.ListRechargeRecord(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getRechargeRecord":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetRechargeRecord requires 3 args")
			flag.Usage()
		}
		arg281 := flag.Arg(1)
		mbTrans282 := thrift.NewTMemoryBufferLen(len(arg281))
		defer mbTrans282.Close()
		_, err283 := mbTrans282.WriteString(arg281)
		if err283 != nil {
			Usage()
			return
		}
		factory284 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt285 := factory284.GetProtocol(mbTrans282)
		argvalue0 := finance.NewRequestHeader()
		err286 := argvalue0.Read(jsProt285)
		if err286 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err287 := (strconv.Atoi(flag.Arg(2)))
		if err287 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg288 := flag.Arg(3)
		mbTrans289 := thrift.NewTMemoryBufferLen(len(arg288))
		defer mbTrans289.Close()
		_, err290 := mbTrans289.WriteString(arg288)
		if err290 != nil {
			Usage()
			return
		}
		factory291 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt292 := factory291.GetProtocol(mbTrans289)
		containerStruct2 := finance.NewGetRechargeRecordArgs()
		err293 := containerStruct2.ReadField3(jsProt292)
		if err293 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetRechargeRecord(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAdInvoiceRecord":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListAdInvoiceRecord requires 7 args")
			flag.Usage()
		}
		arg294 := flag.Arg(1)
		mbTrans295 := thrift.NewTMemoryBufferLen(len(arg294))
		defer mbTrans295.Close()
		_, err296 := mbTrans295.WriteString(arg294)
		if err296 != nil {
			Usage()
			return
		}
		factory297 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt298 := factory297.GetProtocol(mbTrans295)
		argvalue0 := finance.NewRequestHeader()
		err299 := argvalue0.Read(jsProt298)
		if err299 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err300 := (strconv.Atoi(flag.Arg(2)))
		if err300 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err301 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err301 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err302 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err302 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg303 := flag.Arg(5)
		mbTrans304 := thrift.NewTMemoryBufferLen(len(arg303))
		defer mbTrans304.Close()
		_, err305 := mbTrans304.WriteString(arg303)
		if err305 != nil {
			Usage()
			return
		}
		factory306 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt307 := factory306.GetProtocol(mbTrans304)
		containerStruct4 := finance.NewListAdInvoiceRecordArgs()
		err308 := containerStruct4.ReadField5(jsProt307)
		if err308 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.OperationStatus
		value4 := argvalue4
		tmp5, err309 := (strconv.Atoi(flag.Arg(6)))
		if err309 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.QueryInt(argvalue5)
		tmp6, err310 := (strconv.Atoi(flag.Arg(7)))
		if err310 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := finance.QueryInt(argvalue6)
		fmt.Print(client.ListAdInvoiceRecord(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getAdInvoiceRecord":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAdInvoiceRecord requires 3 args")
			flag.Usage()
		}
		arg311 := flag.Arg(1)
		mbTrans312 := thrift.NewTMemoryBufferLen(len(arg311))
		defer mbTrans312.Close()
		_, err313 := mbTrans312.WriteString(arg311)
		if err313 != nil {
			Usage()
			return
		}
		factory314 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt315 := factory314.GetProtocol(mbTrans312)
		argvalue0 := finance.NewRequestHeader()
		err316 := argvalue0.Read(jsProt315)
		if err316 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err317 := (strconv.Atoi(flag.Arg(2)))
		if err317 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg318 := flag.Arg(3)
		mbTrans319 := thrift.NewTMemoryBufferLen(len(arg318))
		defer mbTrans319.Close()
		_, err320 := mbTrans319.WriteString(arg318)
		if err320 != nil {
			Usage()
			return
		}
		factory321 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt322 := factory321.GetProtocol(mbTrans319)
		containerStruct2 := finance.NewGetAdInvoiceRecordArgs()
		err323 := containerStruct2.ReadField3(jsProt322)
		if err323 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetAdInvoiceRecord(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listIncomeRecord":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListIncomeRecord requires 4 args")
			flag.Usage()
		}
		arg324 := flag.Arg(1)
		mbTrans325 := thrift.NewTMemoryBufferLen(len(arg324))
		defer mbTrans325.Close()
		_, err326 := mbTrans325.WriteString(arg324)
		if err326 != nil {
			Usage()
			return
		}
		factory327 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt328 := factory327.GetProtocol(mbTrans325)
		argvalue0 := finance.NewRequestHeader()
		err329 := argvalue0.Read(jsProt328)
		if err329 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err330 := (strconv.Atoi(flag.Arg(2)))
		if err330 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err331 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err331 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err332 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err332 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		fmt.Print(client.ListIncomeRecord(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listWithdrawRecord":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "ListWithdrawRecord requires 8 args")
			flag.Usage()
		}
		arg333 := flag.Arg(1)
		mbTrans334 := thrift.NewTMemoryBufferLen(len(arg333))
		defer mbTrans334.Close()
		_, err335 := mbTrans334.WriteString(arg333)
		if err335 != nil {
			Usage()
			return
		}
		factory336 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt337 := factory336.GetProtocol(mbTrans334)
		argvalue0 := finance.NewRequestHeader()
		err338 := argvalue0.Read(jsProt337)
		if err338 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err339 := (strconv.Atoi(flag.Arg(2)))
		if err339 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err340 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err340 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err341 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err341 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		tmp4, err := (strconv.Atoi(flag.Arg(5)))
		if err != nil {
			Usage()
			return
		}
		argvalue4 := finance.WithdrawType(tmp4)
		value4 := finance.WithdrawType(argvalue4)
		arg342 := flag.Arg(6)
		mbTrans343 := thrift.NewTMemoryBufferLen(len(arg342))
		defer mbTrans343.Close()
		_, err344 := mbTrans343.WriteString(arg342)
		if err344 != nil {
			Usage()
			return
		}
		factory345 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt346 := factory345.GetProtocol(mbTrans343)
		containerStruct5 := finance.NewListWithdrawRecordArgs()
		err347 := containerStruct5.ReadField6(jsProt346)
		if err347 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.OperationStatus
		value5 := argvalue5
		tmp6, err348 := (strconv.Atoi(flag.Arg(7)))
		if err348 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := finance.QueryInt(argvalue6)
		tmp7, err349 := (strconv.Atoi(flag.Arg(8)))
		if err349 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := finance.QueryInt(argvalue7)
		fmt.Print(client.ListWithdrawRecord(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getWithdrawRecord":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetWithdrawRecord requires 3 args")
			flag.Usage()
		}
		arg350 := flag.Arg(1)
		mbTrans351 := thrift.NewTMemoryBufferLen(len(arg350))
		defer mbTrans351.Close()
		_, err352 := mbTrans351.WriteString(arg350)
		if err352 != nil {
			Usage()
			return
		}
		factory353 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt354 := factory353.GetProtocol(mbTrans351)
		argvalue0 := finance.NewRequestHeader()
		err355 := argvalue0.Read(jsProt354)
		if err355 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err356 := (strconv.Atoi(flag.Arg(2)))
		if err356 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg357 := flag.Arg(3)
		mbTrans358 := thrift.NewTMemoryBufferLen(len(arg357))
		defer mbTrans358.Close()
		_, err359 := mbTrans358.WriteString(arg357)
		if err359 != nil {
			Usage()
			return
		}
		factory360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt361 := factory360.GetProtocol(mbTrans358)
		containerStruct2 := finance.NewGetWithdrawRecordArgs()
		err362 := containerStruct2.ReadField3(jsProt361)
		if err362 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetWithdrawRecord(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listMediaInvoiceRecord":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ListMediaInvoiceRecord requires 7 args")
			flag.Usage()
		}
		arg363 := flag.Arg(1)
		mbTrans364 := thrift.NewTMemoryBufferLen(len(arg363))
		defer mbTrans364.Close()
		_, err365 := mbTrans364.WriteString(arg363)
		if err365 != nil {
			Usage()
			return
		}
		factory366 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt367 := factory366.GetProtocol(mbTrans364)
		argvalue0 := finance.NewRequestHeader()
		err368 := argvalue0.Read(jsProt367)
		if err368 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err369 := (strconv.Atoi(flag.Arg(2)))
		if err369 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err370 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err370 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err371 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err371 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg372 := flag.Arg(5)
		mbTrans373 := thrift.NewTMemoryBufferLen(len(arg372))
		defer mbTrans373.Close()
		_, err374 := mbTrans373.WriteString(arg372)
		if err374 != nil {
			Usage()
			return
		}
		factory375 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt376 := factory375.GetProtocol(mbTrans373)
		containerStruct4 := finance.NewListMediaInvoiceRecordArgs()
		err377 := containerStruct4.ReadField5(jsProt376)
		if err377 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.OperationStatus
		value4 := argvalue4
		tmp5, err378 := (strconv.Atoi(flag.Arg(6)))
		if err378 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.QueryInt(argvalue5)
		tmp6, err379 := (strconv.Atoi(flag.Arg(7)))
		if err379 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := finance.QueryInt(argvalue6)
		fmt.Print(client.ListMediaInvoiceRecord(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "getMediaInvoiceRecord":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetMediaInvoiceRecord requires 3 args")
			flag.Usage()
		}
		arg380 := flag.Arg(1)
		mbTrans381 := thrift.NewTMemoryBufferLen(len(arg380))
		defer mbTrans381.Close()
		_, err382 := mbTrans381.WriteString(arg380)
		if err382 != nil {
			Usage()
			return
		}
		factory383 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt384 := factory383.GetProtocol(mbTrans381)
		argvalue0 := finance.NewRequestHeader()
		err385 := argvalue0.Read(jsProt384)
		if err385 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err386 := (strconv.Atoi(flag.Arg(2)))
		if err386 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg387 := flag.Arg(3)
		mbTrans388 := thrift.NewTMemoryBufferLen(len(arg387))
		defer mbTrans388.Close()
		_, err389 := mbTrans388.WriteString(arg387)
		if err389 != nil {
			Usage()
			return
		}
		factory390 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt391 := factory390.GetProtocol(mbTrans388)
		containerStruct2 := finance.NewGetMediaInvoiceRecordArgs()
		err392 := containerStruct2.ReadField3(jsProt391)
		if err392 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetMediaInvoiceRecord(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAdUsers":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListAdUsers requires 5 args")
			flag.Usage()
		}
		arg393 := flag.Arg(1)
		mbTrans394 := thrift.NewTMemoryBufferLen(len(arg393))
		defer mbTrans394.Close()
		_, err395 := mbTrans394.WriteString(arg393)
		if err395 != nil {
			Usage()
			return
		}
		factory396 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt397 := factory396.GetProtocol(mbTrans394)
		argvalue0 := finance.NewRequestHeader()
		err398 := argvalue0.Read(jsProt397)
		if err398 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err401 := (strconv.Atoi(flag.Arg(4)))
		if err401 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.UidInt(argvalue3)
		tmp4, err402 := (strconv.Atoi(flag.Arg(5)))
		if err402 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.QueryInt(argvalue4)
		fmt.Print(client.ListAdUsers(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listMediaUsers":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListMediaUsers requires 5 args")
			flag.Usage()
		}
		arg403 := flag.Arg(1)
		mbTrans404 := thrift.NewTMemoryBufferLen(len(arg403))
		defer mbTrans404.Close()
		_, err405 := mbTrans404.WriteString(arg403)
		if err405 != nil {
			Usage()
			return
		}
		factory406 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt407 := factory406.GetProtocol(mbTrans404)
		argvalue0 := finance.NewRequestHeader()
		err408 := argvalue0.Read(jsProt407)
		if err408 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2) == "true"
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err411 := (strconv.Atoi(flag.Arg(4)))
		if err411 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.UidInt(argvalue3)
		tmp4, err412 := (strconv.Atoi(flag.Arg(5)))
		if err412 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.QueryInt(argvalue4)
		fmt.Print(client.ListMediaUsers(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getSystemAccountSummary":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetSystemAccountSummary requires 1 args")
			flag.Usage()
		}
		arg413 := flag.Arg(1)
		mbTrans414 := thrift.NewTMemoryBufferLen(len(arg413))
		defer mbTrans414.Close()
		_, err415 := mbTrans414.WriteString(arg413)
		if err415 != nil {
			Usage()
			return
		}
		factory416 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt417 := factory416.GetProtocol(mbTrans414)
		argvalue0 := finance.NewRequestHeader()
		err418 := argvalue0.Read(jsProt417)
		if err418 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		fmt.Print(client.GetSystemAccountSummary(value0))
		fmt.Print("\n")
		break
	case "listAwardRecord":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListAwardRecord requires 3 args")
			flag.Usage()
		}
		arg419 := flag.Arg(1)
		mbTrans420 := thrift.NewTMemoryBufferLen(len(arg419))
		defer mbTrans420.Close()
		_, err421 := mbTrans420.WriteString(arg419)
		if err421 != nil {
			Usage()
			return
		}
		factory422 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt423 := factory422.GetProtocol(mbTrans420)
		argvalue0 := finance.NewRequestHeader()
		err424 := argvalue0.Read(jsProt423)
		if err424 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err425 := (strconv.Atoi(flag.Arg(2)))
		if err425 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err426 := (strconv.Atoi(flag.Arg(3)))
		if err426 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.QueryInt(argvalue2)
		fmt.Print(client.ListAwardRecord(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getFinancialSummaryNew":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinancialSummaryNew requires 2 args")
			flag.Usage()
		}
		arg427 := flag.Arg(1)
		mbTrans428 := thrift.NewTMemoryBufferLen(len(arg427))
		defer mbTrans428.Close()
		_, err429 := mbTrans428.WriteString(arg427)
		if err429 != nil {
			Usage()
			return
		}
		factory430 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt431 := factory430.GetProtocol(mbTrans428)
		argvalue0 := finance.NewRequestHeader()
		err432 := argvalue0.Read(jsProt431)
		if err432 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err433 := (strconv.Atoi(flag.Arg(2)))
		if err433 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		fmt.Print(client.GetFinancialSummaryNew(value0, value1))
		fmt.Print("\n")
		break
	case "listAccountIdByRole":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListAccountIdByRole requires 6 args")
			flag.Usage()
		}
		arg434 := flag.Arg(1)
		mbTrans435 := thrift.NewTMemoryBufferLen(len(arg434))
		defer mbTrans435.Close()
		_, err436 := mbTrans435.WriteString(arg434)
		if err436 != nil {
			Usage()
			return
		}
		factory437 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt438 := factory437.GetProtocol(mbTrans435)
		argvalue0 := finance.NewRequestHeader()
		err439 := argvalue0.Read(jsProt438)
		if err439 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.UserRole(tmp1)
		value1 := finance.UserRole(argvalue1)
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		tmp3, err441 := (strconv.Atoi(flag.Arg(4)))
		if err441 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.QueryInt(argvalue3)
		tmp4, err442 := (strconv.Atoi(flag.Arg(5)))
		if err442 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.QueryInt(argvalue4)
		argvalue5 := flag.Arg(6) == "true"
		value5 := argvalue5
		fmt.Print(client.ListAccountIdByRole(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getSummariesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSummariesByIds requires 2 args")
			flag.Usage()
		}
		arg444 := flag.Arg(1)
		mbTrans445 := thrift.NewTMemoryBufferLen(len(arg444))
		defer mbTrans445.Close()
		_, err446 := mbTrans445.WriteString(arg444)
		if err446 != nil {
			Usage()
			return
		}
		factory447 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt448 := factory447.GetProtocol(mbTrans445)
		argvalue0 := finance.NewRequestHeader()
		err449 := argvalue0.Read(jsProt448)
		if err449 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg450 := flag.Arg(2)
		mbTrans451 := thrift.NewTMemoryBufferLen(len(arg450))
		defer mbTrans451.Close()
		_, err452 := mbTrans451.WriteString(arg450)
		if err452 != nil {
			Usage()
			return
		}
		factory453 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt454 := factory453.GetProtocol(mbTrans451)
		containerStruct1 := finance.NewGetSummariesByIdsArgs()
		err455 := containerStruct1.ReadField2(jsProt454)
		if err455 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSummariesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getFinancialAccountByUidList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinancialAccountByUidList requires 2 args")
			flag.Usage()
		}
		arg456 := flag.Arg(1)
		mbTrans457 := thrift.NewTMemoryBufferLen(len(arg456))
		defer mbTrans457.Close()
		_, err458 := mbTrans457.WriteString(arg456)
		if err458 != nil {
			Usage()
			return
		}
		factory459 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt460 := factory459.GetProtocol(mbTrans457)
		argvalue0 := finance.NewRequestHeader()
		err461 := argvalue0.Read(jsProt460)
		if err461 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg462 := flag.Arg(2)
		mbTrans463 := thrift.NewTMemoryBufferLen(len(arg462))
		defer mbTrans463.Close()
		_, err464 := mbTrans463.WriteString(arg462)
		if err464 != nil {
			Usage()
			return
		}
		factory465 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt466 := factory465.GetProtocol(mbTrans463)
		containerStruct1 := finance.NewGetFinancialAccountByUidListArgs()
		err467 := containerStruct1.ReadField2(jsProt466)
		if err467 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.UidList
		value1 := argvalue1
		fmt.Print(client.GetFinancialAccountByUidList(value0, value1))
		fmt.Print("\n")
		break
	case "listTeamMembers":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListTeamMembers requires 2 args")
			flag.Usage()
		}
		arg468 := flag.Arg(1)
		mbTrans469 := thrift.NewTMemoryBufferLen(len(arg468))
		defer mbTrans469.Close()
		_, err470 := mbTrans469.WriteString(arg468)
		if err470 != nil {
			Usage()
			return
		}
		factory471 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt472 := factory471.GetProtocol(mbTrans469)
		argvalue0 := finance.NewRequestHeader()
		err473 := argvalue0.Read(jsProt472)
		if err473 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err474 := (strconv.Atoi(flag.Arg(2)))
		if err474 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		fmt.Print(client.ListTeamMembers(value0, value1))
		fmt.Print("\n")
		break
	case "getTeamMemberByIdentity":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetTeamMemberByIdentity requires 3 args")
			flag.Usage()
		}
		arg475 := flag.Arg(1)
		mbTrans476 := thrift.NewTMemoryBufferLen(len(arg475))
		defer mbTrans476.Close()
		_, err477 := mbTrans476.WriteString(arg475)
		if err477 != nil {
			Usage()
			return
		}
		factory478 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt479 := factory478.GetProtocol(mbTrans476)
		argvalue0 := finance.NewRequestHeader()
		err480 := argvalue0.Read(jsProt479)
		if err480 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.IdentityType(tmp1)
		value1 := finance.IdentityType(argvalue1)
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.GetTeamMemberByIdentity(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getTeamMembersByName":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTeamMembersByName requires 2 args")
			flag.Usage()
		}
		arg482 := flag.Arg(1)
		mbTrans483 := thrift.NewTMemoryBufferLen(len(arg482))
		defer mbTrans483.Close()
		_, err484 := mbTrans483.WriteString(arg482)
		if err484 != nil {
			Usage()
			return
		}
		factory485 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt486 := factory485.GetProtocol(mbTrans483)
		argvalue0 := finance.NewRequestHeader()
		err487 := argvalue0.Read(jsProt486)
		if err487 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetTeamMembersByName(value0, value1))
		fmt.Print("\n")
		break
	case "getTeamMembersByIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTeamMembersByIdList requires 2 args")
			flag.Usage()
		}
		arg489 := flag.Arg(1)
		mbTrans490 := thrift.NewTMemoryBufferLen(len(arg489))
		defer mbTrans490.Close()
		_, err491 := mbTrans490.WriteString(arg489)
		if err491 != nil {
			Usage()
			return
		}
		factory492 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt493 := factory492.GetProtocol(mbTrans490)
		argvalue0 := finance.NewRequestHeader()
		err494 := argvalue0.Read(jsProt493)
		if err494 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg495 := flag.Arg(2)
		mbTrans496 := thrift.NewTMemoryBufferLen(len(arg495))
		defer mbTrans496.Close()
		_, err497 := mbTrans496.WriteString(arg495)
		if err497 != nil {
			Usage()
			return
		}
		factory498 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt499 := factory498.GetProtocol(mbTrans496)
		containerStruct1 := finance.NewGetTeamMembersByIdListArgs()
		err500 := containerStruct1.ReadField2(jsProt499)
		if err500 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.MemberIdList
		value1 := argvalue1
		fmt.Print(client.GetTeamMembersByIdList(value0, value1))
		fmt.Print("\n")
		break
	case "listIncomeShare":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ListIncomeShare requires 2 args")
			flag.Usage()
		}
		arg501 := flag.Arg(1)
		mbTrans502 := thrift.NewTMemoryBufferLen(len(arg501))
		defer mbTrans502.Close()
		_, err503 := mbTrans502.WriteString(arg501)
		if err503 != nil {
			Usage()
			return
		}
		factory504 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt505 := factory504.GetProtocol(mbTrans502)
		argvalue0 := finance.NewRequestHeader()
		err506 := argvalue0.Read(jsProt505)
		if err506 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err507 := (strconv.Atoi(flag.Arg(2)))
		if err507 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		fmt.Print(client.ListIncomeShare(value0, value1))
		fmt.Print("\n")
		break
	case "listIncomeHistory":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListIncomeHistory requires 4 args")
			flag.Usage()
		}
		arg508 := flag.Arg(1)
		mbTrans509 := thrift.NewTMemoryBufferLen(len(arg508))
		defer mbTrans509.Close()
		_, err510 := mbTrans509.WriteString(arg508)
		if err510 != nil {
			Usage()
			return
		}
		factory511 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt512 := factory511.GetProtocol(mbTrans509)
		argvalue0 := finance.NewRequestHeader()
		err513 := argvalue0.Read(jsProt512)
		if err513 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg514 := flag.Arg(2)
		mbTrans515 := thrift.NewTMemoryBufferLen(len(arg514))
		defer mbTrans515.Close()
		_, err516 := mbTrans515.WriteString(arg514)
		if err516 != nil {
			Usage()
			return
		}
		factory517 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt518 := factory517.GetProtocol(mbTrans515)
		containerStruct1 := finance.NewListIncomeHistoryArgs()
		err519 := containerStruct1.ReadField2(jsProt518)
		if err519 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.MemberIdList
		value1 := argvalue1
		tmp2, err520 := (strconv.Atoi(flag.Arg(3)))
		if err520 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err521 := (strconv.Atoi(flag.Arg(4)))
		if err521 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.ListIncomeHistory(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAdPlanBudgetStatus":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetAdPlanBudgetStatus requires 5 args")
			flag.Usage()
		}
		arg522 := flag.Arg(1)
		mbTrans523 := thrift.NewTMemoryBufferLen(len(arg522))
		defer mbTrans523.Close()
		_, err524 := mbTrans523.WriteString(arg522)
		if err524 != nil {
			Usage()
			return
		}
		factory525 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt526 := factory525.GetProtocol(mbTrans523)
		argvalue0 := finance.NewRequestHeader()
		err527 := argvalue0.Read(jsProt526)
		if err527 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err528 := (strconv.Atoi(flag.Arg(2)))
		if err528 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg529 := flag.Arg(3)
		mbTrans530 := thrift.NewTMemoryBufferLen(len(arg529))
		defer mbTrans530.Close()
		_, err531 := mbTrans530.WriteString(arg529)
		if err531 != nil {
			Usage()
			return
		}
		factory532 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt533 := factory532.GetProtocol(mbTrans530)
		containerStruct2 := finance.NewGetAdPlanBudgetStatusArgs()
		err534 := containerStruct2.ReadField3(jsProt533)
		if err534 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Pids
		value2 := argvalue2
		argvalue3, err535 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err535 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		argvalue4, err536 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err536 != nil {
			Usage()
			return
		}
		value4 := finance.TimeInt(argvalue4)
		fmt.Print(client.GetAdPlanBudgetStatus(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getAdStrategyBudgetStatus":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetAdStrategyBudgetStatus requires 6 args")
			flag.Usage()
		}
		arg537 := flag.Arg(1)
		mbTrans538 := thrift.NewTMemoryBufferLen(len(arg537))
		defer mbTrans538.Close()
		_, err539 := mbTrans538.WriteString(arg537)
		if err539 != nil {
			Usage()
			return
		}
		factory540 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt541 := factory540.GetProtocol(mbTrans538)
		argvalue0 := finance.NewRequestHeader()
		err542 := argvalue0.Read(jsProt541)
		if err542 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err543 := (strconv.Atoi(flag.Arg(2)))
		if err543 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err544 := (strconv.Atoi(flag.Arg(3)))
		if err544 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.IdInt(argvalue2)
		arg545 := flag.Arg(4)
		mbTrans546 := thrift.NewTMemoryBufferLen(len(arg545))
		defer mbTrans546.Close()
		_, err547 := mbTrans546.WriteString(arg545)
		if err547 != nil {
			Usage()
			return
		}
		factory548 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt549 := factory548.GetProtocol(mbTrans546)
		containerStruct3 := finance.NewGetAdStrategyBudgetStatusArgs()
		err550 := containerStruct3.ReadField4(jsProt549)
		if err550 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Sids
		value3 := argvalue3
		argvalue4, err551 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err551 != nil {
			Usage()
			return
		}
		value4 := finance.TimeInt(argvalue4)
		argvalue5, err552 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err552 != nil {
			Usage()
			return
		}
		value5 := finance.TimeInt(argvalue5)
		fmt.Print(client.GetAdStrategyBudgetStatus(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listPaymentRecord":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListPaymentRecord requires 5 args")
			flag.Usage()
		}
		arg553 := flag.Arg(1)
		mbTrans554 := thrift.NewTMemoryBufferLen(len(arg553))
		defer mbTrans554.Close()
		_, err555 := mbTrans554.WriteString(arg553)
		if err555 != nil {
			Usage()
			return
		}
		factory556 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt557 := factory556.GetProtocol(mbTrans554)
		argvalue0 := finance.NewRequestHeader()
		err558 := argvalue0.Read(jsProt557)
		if err558 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err559 := (strconv.Atoi(flag.Arg(2)))
		if err559 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err560 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err560 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err561 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err561 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg562 := flag.Arg(5)
		mbTrans563 := thrift.NewTMemoryBufferLen(len(arg562))
		defer mbTrans563.Close()
		_, err564 := mbTrans563.WriteString(arg562)
		if err564 != nil {
			Usage()
			return
		}
		factory565 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt566 := factory565.GetProtocol(mbTrans563)
		containerStruct4 := finance.NewListPaymentRecordArgs()
		err567 := containerStruct4.ReadField5(jsProt566)
		if err567 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Statuses
		value4 := argvalue4
		fmt.Print(client.ListPaymentRecord(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getPaymentRecord":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetPaymentRecord requires 3 args")
			flag.Usage()
		}
		arg568 := flag.Arg(1)
		mbTrans569 := thrift.NewTMemoryBufferLen(len(arg568))
		defer mbTrans569.Close()
		_, err570 := mbTrans569.WriteString(arg568)
		if err570 != nil {
			Usage()
			return
		}
		factory571 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt572 := factory571.GetProtocol(mbTrans569)
		argvalue0 := finance.NewRequestHeader()
		err573 := argvalue0.Read(jsProt572)
		if err573 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err574 := (strconv.Atoi(flag.Arg(2)))
		if err574 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg575 := flag.Arg(3)
		mbTrans576 := thrift.NewTMemoryBufferLen(len(arg575))
		defer mbTrans576.Close()
		_, err577 := mbTrans576.WriteString(arg575)
		if err577 != nil {
			Usage()
			return
		}
		factory578 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt579 := factory578.GetProtocol(mbTrans576)
		containerStruct2 := finance.NewGetPaymentRecordArgs()
		err580 := containerStruct2.ReadField3(jsProt579)
		if err580 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetPaymentRecord(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listIncomeConfirmationRecord":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListIncomeConfirmationRecord requires 5 args")
			flag.Usage()
		}
		arg581 := flag.Arg(1)
		mbTrans582 := thrift.NewTMemoryBufferLen(len(arg581))
		defer mbTrans582.Close()
		_, err583 := mbTrans582.WriteString(arg581)
		if err583 != nil {
			Usage()
			return
		}
		factory584 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt585 := factory584.GetProtocol(mbTrans582)
		argvalue0 := finance.NewRequestHeader()
		err586 := argvalue0.Read(jsProt585)
		if err586 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err587 := (strconv.Atoi(flag.Arg(2)))
		if err587 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err588 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err588 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err589 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err589 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg590 := flag.Arg(5)
		mbTrans591 := thrift.NewTMemoryBufferLen(len(arg590))
		defer mbTrans591.Close()
		_, err592 := mbTrans591.WriteString(arg590)
		if err592 != nil {
			Usage()
			return
		}
		factory593 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt594 := factory593.GetProtocol(mbTrans591)
		containerStruct4 := finance.NewListIncomeConfirmationRecordArgs()
		err595 := containerStruct4.ReadField5(jsProt594)
		if err595 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Status
		value4 := argvalue4
		fmt.Print(client.ListIncomeConfirmationRecord(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listAwardRecordByTime":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAwardRecordByTime requires 4 args")
			flag.Usage()
		}
		arg596 := flag.Arg(1)
		mbTrans597 := thrift.NewTMemoryBufferLen(len(arg596))
		defer mbTrans597.Close()
		_, err598 := mbTrans597.WriteString(arg596)
		if err598 != nil {
			Usage()
			return
		}
		factory599 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt600 := factory599.GetProtocol(mbTrans597)
		argvalue0 := finance.NewRequestHeader()
		err601 := argvalue0.Read(jsProt600)
		if err601 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err602 := (strconv.Atoi(flag.Arg(2)))
		if err602 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err603 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err603 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err604 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err604 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		fmt.Print(client.ListAwardRecordByTime(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listIncomeConfirmationRecordNew":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListIncomeConfirmationRecordNew requires 5 args")
			flag.Usage()
		}
		arg605 := flag.Arg(1)
		mbTrans606 := thrift.NewTMemoryBufferLen(len(arg605))
		defer mbTrans606.Close()
		_, err607 := mbTrans606.WriteString(arg605)
		if err607 != nil {
			Usage()
			return
		}
		factory608 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt609 := factory608.GetProtocol(mbTrans606)
		argvalue0 := finance.NewRequestHeader()
		err610 := argvalue0.Read(jsProt609)
		if err610 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err611 := (strconv.Atoi(flag.Arg(2)))
		if err611 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err612 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err612 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err613 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err613 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg614 := flag.Arg(5)
		mbTrans615 := thrift.NewTMemoryBufferLen(len(arg614))
		defer mbTrans615.Close()
		_, err616 := mbTrans615.WriteString(arg614)
		if err616 != nil {
			Usage()
			return
		}
		factory617 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt618 := factory617.GetProtocol(mbTrans615)
		containerStruct4 := finance.NewListIncomeConfirmationRecordNewArgs()
		err619 := containerStruct4.ReadField5(jsProt618)
		if err619 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Status
		value4 := argvalue4
		fmt.Print(client.ListIncomeConfirmationRecordNew(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getIncomeConfirmationRecordList":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "GetIncomeConfirmationRecordList requires 5 args")
			flag.Usage()
		}
		arg620 := flag.Arg(1)
		mbTrans621 := thrift.NewTMemoryBufferLen(len(arg620))
		defer mbTrans621.Close()
		_, err622 := mbTrans621.WriteString(arg620)
		if err622 != nil {
			Usage()
			return
		}
		factory623 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt624 := factory623.GetProtocol(mbTrans621)
		argvalue0 := finance.NewRequestHeader()
		err625 := argvalue0.Read(jsProt624)
		if err625 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err626 := (strconv.Atoi(flag.Arg(2)))
		if err626 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err627 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err627 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err628 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err628 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg629 := flag.Arg(5)
		mbTrans630 := thrift.NewTMemoryBufferLen(len(arg629))
		defer mbTrans630.Close()
		_, err631 := mbTrans630.WriteString(arg629)
		if err631 != nil {
			Usage()
			return
		}
		factory632 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt633 := factory632.GetProtocol(mbTrans630)
		containerStruct4 := finance.NewGetIncomeConfirmationRecordListArgs()
		err634 := containerStruct4.ReadField5(jsProt633)
		if err634 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Status
		value4 := argvalue4
		fmt.Print(client.GetIncomeConfirmationRecordList(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listPaymentRecordNew":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListPaymentRecordNew requires 5 args")
			flag.Usage()
		}
		arg635 := flag.Arg(1)
		mbTrans636 := thrift.NewTMemoryBufferLen(len(arg635))
		defer mbTrans636.Close()
		_, err637 := mbTrans636.WriteString(arg635)
		if err637 != nil {
			Usage()
			return
		}
		factory638 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt639 := factory638.GetProtocol(mbTrans636)
		argvalue0 := finance.NewRequestHeader()
		err640 := argvalue0.Read(jsProt639)
		if err640 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err641 := (strconv.Atoi(flag.Arg(2)))
		if err641 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err642 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err642 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err643 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err643 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg644 := flag.Arg(5)
		mbTrans645 := thrift.NewTMemoryBufferLen(len(arg644))
		defer mbTrans645.Close()
		_, err646 := mbTrans645.WriteString(arg644)
		if err646 != nil {
			Usage()
			return
		}
		factory647 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt648 := factory647.GetProtocol(mbTrans645)
		containerStruct4 := finance.NewListPaymentRecordNewArgs()
		err649 := containerStruct4.ReadField5(jsProt648)
		if err649 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Statuses
		value4 := argvalue4
		fmt.Print(client.ListPaymentRecordNew(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listAppropriationRecord":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAppropriationRecord requires 4 args")
			flag.Usage()
		}
		arg650 := flag.Arg(1)
		mbTrans651 := thrift.NewTMemoryBufferLen(len(arg650))
		defer mbTrans651.Close()
		_, err652 := mbTrans651.WriteString(arg650)
		if err652 != nil {
			Usage()
			return
		}
		factory653 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt654 := factory653.GetProtocol(mbTrans651)
		argvalue0 := finance.NewRequestHeader()
		err655 := argvalue0.Read(jsProt654)
		if err655 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg656 := flag.Arg(2)
		mbTrans657 := thrift.NewTMemoryBufferLen(len(arg656))
		defer mbTrans657.Close()
		_, err658 := mbTrans657.WriteString(arg656)
		if err658 != nil {
			Usage()
			return
		}
		factory659 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt660 := factory659.GetProtocol(mbTrans657)
		containerStruct1 := finance.NewListAppropriationRecordArgs()
		err661 := containerStruct1.ReadField2(jsProt660)
		if err661 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2, err662 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err662 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err663 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err663 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		fmt.Print(client.ListAppropriationRecord(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listAdPlanBudgetHistory":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListAdPlanBudgetHistory requires 3 args")
			flag.Usage()
		}
		arg664 := flag.Arg(1)
		mbTrans665 := thrift.NewTMemoryBufferLen(len(arg664))
		defer mbTrans665.Close()
		_, err666 := mbTrans665.WriteString(arg664)
		if err666 != nil {
			Usage()
			return
		}
		factory667 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt668 := factory667.GetProtocol(mbTrans665)
		argvalue0 := finance.NewRequestHeader()
		err669 := argvalue0.Read(jsProt668)
		if err669 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg670 := flag.Arg(2)
		mbTrans671 := thrift.NewTMemoryBufferLen(len(arg670))
		defer mbTrans671.Close()
		_, err672 := mbTrans671.WriteString(arg670)
		if err672 != nil {
			Usage()
			return
		}
		factory673 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt674 := factory673.GetProtocol(mbTrans671)
		containerStruct1 := finance.NewListAdPlanBudgetHistoryArgs()
		err675 := containerStruct1.ReadField2(jsProt674)
		if err675 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2, err676 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err676 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		fmt.Print(client.ListAdPlanBudgetHistory(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listAgentAppropriationRecord":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListAgentAppropriationRecord requires 4 args")
			flag.Usage()
		}
		arg677 := flag.Arg(1)
		mbTrans678 := thrift.NewTMemoryBufferLen(len(arg677))
		defer mbTrans678.Close()
		_, err679 := mbTrans678.WriteString(arg677)
		if err679 != nil {
			Usage()
			return
		}
		factory680 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt681 := factory680.GetProtocol(mbTrans678)
		argvalue0 := finance.NewRequestHeader()
		err682 := argvalue0.Read(jsProt681)
		if err682 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err683 := (strconv.Atoi(flag.Arg(2)))
		if err683 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err684 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err684 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err685 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err685 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		fmt.Print(client.ListAgentAppropriationRecord(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAllAdUsers":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAllAdUsers requires 3 args")
			flag.Usage()
		}
		arg686 := flag.Arg(1)
		mbTrans687 := thrift.NewTMemoryBufferLen(len(arg686))
		defer mbTrans687.Close()
		_, err688 := mbTrans687.WriteString(arg686)
		if err688 != nil {
			Usage()
			return
		}
		factory689 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt690 := factory689.GetProtocol(mbTrans687)
		argvalue0 := finance.NewRequestHeader()
		err691 := argvalue0.Read(jsProt690)
		if err691 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err692 := (strconv.Atoi(flag.Arg(2)))
		if err692 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err693 := (strconv.Atoi(flag.Arg(3)))
		if err693 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.QueryInt(argvalue2)
		fmt.Print(client.GetAllAdUsers(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
