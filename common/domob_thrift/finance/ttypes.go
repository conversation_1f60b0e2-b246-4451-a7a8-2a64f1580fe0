// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = finance_types.GoUnusedProtection__
var GoUnusedProtection__ int

//内部错误信息
type InternalErrorNo int64

const (
	InternalErrorNo_IE_NO_AD_ACCOUNT               InternalErrorNo = 20001
	InternalErrorNo_IE_AD_ACCOUNT_NOT_ACTIVATED    InternalErrorNo = 20002
	InternalErrorNo_IE_AD_ACCOUNT_BANNED           InternalErrorNo = 20003
	InternalErrorNo_IE_NO_MEDIA_ACCOUNT            InternalErrorNo = 20011
	InternalErrorNo_IE_MEDIA_ACCOUNT_NOT_ACTIVATED InternalErrorNo = 20012
	InternalErrorNo_IE_MEDIA_ACCOUNT_BANNED        InternalErrorNo = 20013
	InternalErrorNo_IE_RECORD_NOT_EXISTS           InternalErrorNo = 20021
	InternalErrorNo_IE_RECORD_BAD_STATUS           InternalErrorNo = 20022
	InternalErrorNo_IE_RECORD_ALREADY_PROCESSED    InternalErrorNo = 20023
	InternalErrorNo_IE_AMOUNT_TOO_SMALL            InternalErrorNo = 20031
	InternalErrorNo_IE_AMOUNT_TOO_LARGE            InternalErrorNo = 20032
	InternalErrorNo_IE_AMOUNT_NOT_ENOUGH           InternalErrorNo = 20033
	InternalErrorNo_IE_DAILY_BUDGET_OVER           InternalErrorNo = 20034
	InternalErrorNo_IE_PREV_WITHDRAW_IN_PROCESS    InternalErrorNo = 20035
	InternalErrorNo_IE_KEY_ID_INVALID              InternalErrorNo = 20040
	InternalErrorNo_IE_BAD_SETTLE_STATUS           InternalErrorNo = 20041
)

func (p InternalErrorNo) String() string {
	switch p {
	case InternalErrorNo_IE_NO_AD_ACCOUNT:
		return "InternalErrorNo_IE_NO_AD_ACCOUNT"
	case InternalErrorNo_IE_AD_ACCOUNT_NOT_ACTIVATED:
		return "InternalErrorNo_IE_AD_ACCOUNT_NOT_ACTIVATED"
	case InternalErrorNo_IE_AD_ACCOUNT_BANNED:
		return "InternalErrorNo_IE_AD_ACCOUNT_BANNED"
	case InternalErrorNo_IE_NO_MEDIA_ACCOUNT:
		return "InternalErrorNo_IE_NO_MEDIA_ACCOUNT"
	case InternalErrorNo_IE_MEDIA_ACCOUNT_NOT_ACTIVATED:
		return "InternalErrorNo_IE_MEDIA_ACCOUNT_NOT_ACTIVATED"
	case InternalErrorNo_IE_MEDIA_ACCOUNT_BANNED:
		return "InternalErrorNo_IE_MEDIA_ACCOUNT_BANNED"
	case InternalErrorNo_IE_RECORD_NOT_EXISTS:
		return "InternalErrorNo_IE_RECORD_NOT_EXISTS"
	case InternalErrorNo_IE_RECORD_BAD_STATUS:
		return "InternalErrorNo_IE_RECORD_BAD_STATUS"
	case InternalErrorNo_IE_RECORD_ALREADY_PROCESSED:
		return "InternalErrorNo_IE_RECORD_ALREADY_PROCESSED"
	case InternalErrorNo_IE_AMOUNT_TOO_SMALL:
		return "InternalErrorNo_IE_AMOUNT_TOO_SMALL"
	case InternalErrorNo_IE_AMOUNT_TOO_LARGE:
		return "InternalErrorNo_IE_AMOUNT_TOO_LARGE"
	case InternalErrorNo_IE_AMOUNT_NOT_ENOUGH:
		return "InternalErrorNo_IE_AMOUNT_NOT_ENOUGH"
	case InternalErrorNo_IE_DAILY_BUDGET_OVER:
		return "InternalErrorNo_IE_DAILY_BUDGET_OVER"
	case InternalErrorNo_IE_PREV_WITHDRAW_IN_PROCESS:
		return "InternalErrorNo_IE_PREV_WITHDRAW_IN_PROCESS"
	case InternalErrorNo_IE_KEY_ID_INVALID:
		return "InternalErrorNo_IE_KEY_ID_INVALID"
	case InternalErrorNo_IE_BAD_SETTLE_STATUS:
		return "InternalErrorNo_IE_BAD_SETTLE_STATUS"
	}
	return "<UNSET>"
}

func InternalErrorNoFromString(s string) (InternalErrorNo, error) {
	switch s {
	case "InternalErrorNo_IE_NO_AD_ACCOUNT":
		return InternalErrorNo_IE_NO_AD_ACCOUNT, nil
	case "InternalErrorNo_IE_AD_ACCOUNT_NOT_ACTIVATED":
		return InternalErrorNo_IE_AD_ACCOUNT_NOT_ACTIVATED, nil
	case "InternalErrorNo_IE_AD_ACCOUNT_BANNED":
		return InternalErrorNo_IE_AD_ACCOUNT_BANNED, nil
	case "InternalErrorNo_IE_NO_MEDIA_ACCOUNT":
		return InternalErrorNo_IE_NO_MEDIA_ACCOUNT, nil
	case "InternalErrorNo_IE_MEDIA_ACCOUNT_NOT_ACTIVATED":
		return InternalErrorNo_IE_MEDIA_ACCOUNT_NOT_ACTIVATED, nil
	case "InternalErrorNo_IE_MEDIA_ACCOUNT_BANNED":
		return InternalErrorNo_IE_MEDIA_ACCOUNT_BANNED, nil
	case "InternalErrorNo_IE_RECORD_NOT_EXISTS":
		return InternalErrorNo_IE_RECORD_NOT_EXISTS, nil
	case "InternalErrorNo_IE_RECORD_BAD_STATUS":
		return InternalErrorNo_IE_RECORD_BAD_STATUS, nil
	case "InternalErrorNo_IE_RECORD_ALREADY_PROCESSED":
		return InternalErrorNo_IE_RECORD_ALREADY_PROCESSED, nil
	case "InternalErrorNo_IE_AMOUNT_TOO_SMALL":
		return InternalErrorNo_IE_AMOUNT_TOO_SMALL, nil
	case "InternalErrorNo_IE_AMOUNT_TOO_LARGE":
		return InternalErrorNo_IE_AMOUNT_TOO_LARGE, nil
	case "InternalErrorNo_IE_AMOUNT_NOT_ENOUGH":
		return InternalErrorNo_IE_AMOUNT_NOT_ENOUGH, nil
	case "InternalErrorNo_IE_DAILY_BUDGET_OVER":
		return InternalErrorNo_IE_DAILY_BUDGET_OVER, nil
	case "InternalErrorNo_IE_PREV_WITHDRAW_IN_PROCESS":
		return InternalErrorNo_IE_PREV_WITHDRAW_IN_PROCESS, nil
	case "InternalErrorNo_IE_KEY_ID_INVALID":
		return InternalErrorNo_IE_KEY_ID_INVALID, nil
	case "InternalErrorNo_IE_BAD_SETTLE_STATUS":
		return InternalErrorNo_IE_BAD_SETTLE_STATUS, nil
	}
	return InternalErrorNo(math.MinInt32 - 1), fmt.Errorf("not a valid InternalErrorNo string")
}

//错误信息
type FinanceErrorNo int64

const (
	FinanceErrorNo_ERROR_CLIENT_UNKNOWN_ERROR        FinanceErrorNo = 38300
	FinanceErrorNo_ERROR_CLIENT_UNAUTHORIZED         FinanceErrorNo = 38301
	FinanceErrorNo_ERROR_CLIENT_INVALID_PARAMETER    FinanceErrorNo = 38302
	FinanceErrorNo_ERROR_CLIENT_NO_SUCH_ACCOUNT      FinanceErrorNo = 38303
	FinanceErrorNo_ERROR_CLIENT_USER_UNPRIVILEGED    FinanceErrorNo = 38304
	FinanceErrorNo_ERROR_CLIENT_INVALID_AMMOUNT      FinanceErrorNo = 38305
	FinanceErrorNo_ERROR_CLIENT_INVALID_ADMIN_UID    FinanceErrorNo = 38306
	FinanceErrorNo_ERROR_CLIENT_NO_SUCH_RECORD       FinanceErrorNo = 38307
	FinanceErrorNo_ERROR_CLIENT_RECORD_CLOSED        FinanceErrorNo = 38308
	FinanceErrorNo_ERROR_CLIENT_RECORD_BAD_STATUS    FinanceErrorNo = 38309
	FinanceErrorNo_ERROR_CLIENT_BAD_RECHARGE_COMMIT  FinanceErrorNo = 38311
	FinanceErrorNo_ERROR_CLIENT_BAD_COUPON_CODE      FinanceErrorNo = 38312
	FinanceErrorNo_ERROR_CLIENT_COUPON_INVALID       FinanceErrorNo = 38313
	FinanceErrorNo_ERROR_CLIENT_BAD_WITHDRAW_COMMIT  FinanceErrorNo = 38321
	FinanceErrorNo_ERROR_CLIENT_WAITING_PREV_PROCESS FinanceErrorNo = 38322
	FinanceErrorNo_ERROR_CLIENT_BALANCE_NOT_ENOUGH   FinanceErrorNo = 38330
	FinanceErrorNo_ERROR_SERVER_UNKNOWN_ERROR        FinanceErrorNo = 38400
	FinanceErrorNo_ERROR_SERVER_DB_ERROR             FinanceErrorNo = 38401
	FinanceErrorNo_ERROR_SERVER_UNKNOWN_FATAL        FinanceErrorNo = 38500
)

func (p FinanceErrorNo) String() string {
	switch p {
	case FinanceErrorNo_ERROR_CLIENT_UNKNOWN_ERROR:
		return "FinanceErrorNo_ERROR_CLIENT_UNKNOWN_ERROR"
	case FinanceErrorNo_ERROR_CLIENT_UNAUTHORIZED:
		return "FinanceErrorNo_ERROR_CLIENT_UNAUTHORIZED"
	case FinanceErrorNo_ERROR_CLIENT_INVALID_PARAMETER:
		return "FinanceErrorNo_ERROR_CLIENT_INVALID_PARAMETER"
	case FinanceErrorNo_ERROR_CLIENT_NO_SUCH_ACCOUNT:
		return "FinanceErrorNo_ERROR_CLIENT_NO_SUCH_ACCOUNT"
	case FinanceErrorNo_ERROR_CLIENT_USER_UNPRIVILEGED:
		return "FinanceErrorNo_ERROR_CLIENT_USER_UNPRIVILEGED"
	case FinanceErrorNo_ERROR_CLIENT_INVALID_AMMOUNT:
		return "FinanceErrorNo_ERROR_CLIENT_INVALID_AMMOUNT"
	case FinanceErrorNo_ERROR_CLIENT_INVALID_ADMIN_UID:
		return "FinanceErrorNo_ERROR_CLIENT_INVALID_ADMIN_UID"
	case FinanceErrorNo_ERROR_CLIENT_NO_SUCH_RECORD:
		return "FinanceErrorNo_ERROR_CLIENT_NO_SUCH_RECORD"
	case FinanceErrorNo_ERROR_CLIENT_RECORD_CLOSED:
		return "FinanceErrorNo_ERROR_CLIENT_RECORD_CLOSED"
	case FinanceErrorNo_ERROR_CLIENT_RECORD_BAD_STATUS:
		return "FinanceErrorNo_ERROR_CLIENT_RECORD_BAD_STATUS"
	case FinanceErrorNo_ERROR_CLIENT_BAD_RECHARGE_COMMIT:
		return "FinanceErrorNo_ERROR_CLIENT_BAD_RECHARGE_COMMIT"
	case FinanceErrorNo_ERROR_CLIENT_BAD_COUPON_CODE:
		return "FinanceErrorNo_ERROR_CLIENT_BAD_COUPON_CODE"
	case FinanceErrorNo_ERROR_CLIENT_COUPON_INVALID:
		return "FinanceErrorNo_ERROR_CLIENT_COUPON_INVALID"
	case FinanceErrorNo_ERROR_CLIENT_BAD_WITHDRAW_COMMIT:
		return "FinanceErrorNo_ERROR_CLIENT_BAD_WITHDRAW_COMMIT"
	case FinanceErrorNo_ERROR_CLIENT_WAITING_PREV_PROCESS:
		return "FinanceErrorNo_ERROR_CLIENT_WAITING_PREV_PROCESS"
	case FinanceErrorNo_ERROR_CLIENT_BALANCE_NOT_ENOUGH:
		return "FinanceErrorNo_ERROR_CLIENT_BALANCE_NOT_ENOUGH"
	case FinanceErrorNo_ERROR_SERVER_UNKNOWN_ERROR:
		return "FinanceErrorNo_ERROR_SERVER_UNKNOWN_ERROR"
	case FinanceErrorNo_ERROR_SERVER_DB_ERROR:
		return "FinanceErrorNo_ERROR_SERVER_DB_ERROR"
	case FinanceErrorNo_ERROR_SERVER_UNKNOWN_FATAL:
		return "FinanceErrorNo_ERROR_SERVER_UNKNOWN_FATAL"
	}
	return "<UNSET>"
}

func FinanceErrorNoFromString(s string) (FinanceErrorNo, error) {
	switch s {
	case "FinanceErrorNo_ERROR_CLIENT_UNKNOWN_ERROR":
		return FinanceErrorNo_ERROR_CLIENT_UNKNOWN_ERROR, nil
	case "FinanceErrorNo_ERROR_CLIENT_UNAUTHORIZED":
		return FinanceErrorNo_ERROR_CLIENT_UNAUTHORIZED, nil
	case "FinanceErrorNo_ERROR_CLIENT_INVALID_PARAMETER":
		return FinanceErrorNo_ERROR_CLIENT_INVALID_PARAMETER, nil
	case "FinanceErrorNo_ERROR_CLIENT_NO_SUCH_ACCOUNT":
		return FinanceErrorNo_ERROR_CLIENT_NO_SUCH_ACCOUNT, nil
	case "FinanceErrorNo_ERROR_CLIENT_USER_UNPRIVILEGED":
		return FinanceErrorNo_ERROR_CLIENT_USER_UNPRIVILEGED, nil
	case "FinanceErrorNo_ERROR_CLIENT_INVALID_AMMOUNT":
		return FinanceErrorNo_ERROR_CLIENT_INVALID_AMMOUNT, nil
	case "FinanceErrorNo_ERROR_CLIENT_INVALID_ADMIN_UID":
		return FinanceErrorNo_ERROR_CLIENT_INVALID_ADMIN_UID, nil
	case "FinanceErrorNo_ERROR_CLIENT_NO_SUCH_RECORD":
		return FinanceErrorNo_ERROR_CLIENT_NO_SUCH_RECORD, nil
	case "FinanceErrorNo_ERROR_CLIENT_RECORD_CLOSED":
		return FinanceErrorNo_ERROR_CLIENT_RECORD_CLOSED, nil
	case "FinanceErrorNo_ERROR_CLIENT_RECORD_BAD_STATUS":
		return FinanceErrorNo_ERROR_CLIENT_RECORD_BAD_STATUS, nil
	case "FinanceErrorNo_ERROR_CLIENT_BAD_RECHARGE_COMMIT":
		return FinanceErrorNo_ERROR_CLIENT_BAD_RECHARGE_COMMIT, nil
	case "FinanceErrorNo_ERROR_CLIENT_BAD_COUPON_CODE":
		return FinanceErrorNo_ERROR_CLIENT_BAD_COUPON_CODE, nil
	case "FinanceErrorNo_ERROR_CLIENT_COUPON_INVALID":
		return FinanceErrorNo_ERROR_CLIENT_COUPON_INVALID, nil
	case "FinanceErrorNo_ERROR_CLIENT_BAD_WITHDRAW_COMMIT":
		return FinanceErrorNo_ERROR_CLIENT_BAD_WITHDRAW_COMMIT, nil
	case "FinanceErrorNo_ERROR_CLIENT_WAITING_PREV_PROCESS":
		return FinanceErrorNo_ERROR_CLIENT_WAITING_PREV_PROCESS, nil
	case "FinanceErrorNo_ERROR_CLIENT_BALANCE_NOT_ENOUGH":
		return FinanceErrorNo_ERROR_CLIENT_BALANCE_NOT_ENOUGH, nil
	case "FinanceErrorNo_ERROR_SERVER_UNKNOWN_ERROR":
		return FinanceErrorNo_ERROR_SERVER_UNKNOWN_ERROR, nil
	case "FinanceErrorNo_ERROR_SERVER_DB_ERROR":
		return FinanceErrorNo_ERROR_SERVER_DB_ERROR, nil
	case "FinanceErrorNo_ERROR_SERVER_UNKNOWN_FATAL":
		return FinanceErrorNo_ERROR_SERVER_UNKNOWN_FATAL, nil
	}
	return FinanceErrorNo(math.MinInt32 - 1), fmt.Errorf("not a valid FinanceErrorNo string")
}

type NumAffected int64

type IdInt common.IdInt

type UidInt common.UidInt

type TimeInt common.TimeInt

type QueryInt common.QueryInt

type RequestHeader *common.RequestHeader

type QueryResult *common.QueryResult

type Amount common.Amount

type AccountStatus common.AccountStatus

type AccountCategory common.AccountCategory

type PaymentType common.PaymentType

type UserRole common.UserRole

type IdentityType common.IdentityType

type CommonAuditStatus common.CommonAuditStatus

type OperationStatus finance_types.OperationStatus

type ResourceStatus finance_types.ResourceStatus

type WithdrawType finance_types.WithdrawType

type FinancialStatus *finance_types.FinancialStatus

type FinancialSummary *finance_types.FinancialSummary

type FinancialProfile *finance_types.FinancialProfile

type ConsumeRecord *finance_types.ConsumeRecord

type IncomeRecord *finance_types.IncomeRecord

type AwardRecord *finance_types.AwardRecord

type CouponRecord *finance_types.CouponRecord

type RechargeCommit *finance_types.RechargeCommit

type RechargeRecord *finance_types.RechargeRecord

type RechargeRecordResult *finance_types.RechargeRecordResult

type WithdrawCommit *finance_types.WithdrawCommit

type WithdrawRecord *finance_types.WithdrawRecord

type WithdrawRecordResult *finance_types.WithdrawRecordResult

type AdInvoiceRecord *finance_types.AdInvoiceRecord

type AdInvoiceRecordResult *finance_types.AdInvoiceRecordResult

type MediaInvoiceRecord *finance_types.MediaInvoiceRecord

type MediaInvoiceRecordResult *finance_types.MediaInvoiceRecordResult

type SettleRecord *finance_types.SettleRecord

type BatchSettleRecord *finance_types.BatchSettleRecord

type SysAccountSummary *finance_types.SysAccountSummary

type MediaProfile *finance_types.MediaProfile

type MediaProfileResult *finance_types.MediaProfileResult

type FinancialAccount *finance_types.FinancialAccount

type TeamMember *finance_types.TeamMember

type IncomeShare *finance_types.IncomeShare

type IncomeHistory *finance_types.IncomeHistory

type Beneficiary *finance_types.Beneficiary

type TeamMemberListResult *finance_types.TeamMemberListResult

type UserConsumeRecordResult *finance_types.UserConsumeRecordResult

type SettleType finance_types.SettleType

type Settlement *finance_types.Settlement

type FinanceServiceException struct {
	Errno   FinanceErrorNo `thrift:"errno,1" json:"errno"`
	Message string         `thrift:"message,2" json:"message"`
}

func NewFinanceServiceException() *FinanceServiceException {
	return &FinanceServiceException{
		Errno: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FinanceServiceException) IsSetErrno() bool {
	return int64(p.Errno) != math.MinInt32-1
}

func (p *FinanceServiceException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinanceServiceException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Errno = FinanceErrorNo(v)
	}
	return nil
}

func (p *FinanceServiceException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *FinanceServiceException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FinanceServiceException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinanceServiceException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetErrno() {
		if err := oprot.WriteFieldBegin("errno", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:errno: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Errno)); err != nil {
			return fmt.Errorf("%T.errno (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:errno: %s", p, err)
		}
	}
	return err
}

func (p *FinanceServiceException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *FinanceServiceException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinanceServiceException(%+v)", *p)
}
