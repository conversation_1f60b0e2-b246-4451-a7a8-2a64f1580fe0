// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  RechargeRecord offlineRecharge(RequestHeader header, RechargeCommit rechargeCommit, Amount chargedAmount, Amount fee, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.<PERSON>, "  RechargeRecord rechargeRevoke(RequestHeader header, IdInt id, RechargeCommit rechargeCommit, Amount chargeRevokeAmount, Amount fee, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  AdInvoiceRecord passAdInvoiceRequest(RequestHeader header, IdInt id, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "  AdInvoiceRecord rejectAdInvoiceRequest(RequestHeader header, IdInt id, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  AdInvoiceRecord completeAdInvoiceRequest(RequestHeader header, IdInt id, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "  FinancialProfile updateAwardRate(RequestHeader header, FinancialProfile profile, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "  FinancialProfile updateMediaShareRate(RequestHeader header, FinancialProfile profile, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "  FinancialProfile updateCredit(RequestHeader header, FinancialProfile profile, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "  FinancialProfile updateUserFinancialProfile(RequestHeader header, UidInt adminUid, FinancialProfile profile)")
	fmt.Fprintln(os.Stderr, "  MediaProfile updateSpecificMediaShareRate(RequestHeader header, UidInt uid, IdInt mediaId, PercentageInt shareRate)")
	fmt.Fprintln(os.Stderr, "  MediaProfileResult listMediaProfile(RequestHeader header, UidInt uid, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getMediaProfileByIdList(RequestHeader header,  idList)")
	fmt.Fprintln(os.Stderr, "  TeamMemberListResult listTeamMembers(RequestHeader header, UidInt uid, CommonAuditStatus status, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "  TeamMember passTeamMember(RequestHeader header, IdInt memberId, UidInt adminUid, string reason)")
	fmt.Fprintln(os.Stderr, "  TeamMember rejectTeamMember(RequestHeader header, IdInt memberId, UidInt adminUid, string reason)")
	fmt.Fprintln(os.Stderr, "  TeamMember forbidTeamMember(RequestHeader header, IdInt memberId, UidInt adminUid, string reason)")
	fmt.Fprintln(os.Stderr, "  TeamMemberListResult listTeamMembersByName(RequestHeader header, string name, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "  UserConsumeRecordResult getConsumeRecords(RequestHeader header,  uids, TimeInt fromTime, TimeInt toTime, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "   getFinancialProfilesByUidList(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "   getFinancialSummariesByUidList(RequestHeader header,  uids, bool requiresDetail)")
	fmt.Fprintln(os.Stderr, "   getAdPlanBudgetStatusByUidList(RequestHeader header,  uids, TimeInt updatedSince)")
	fmt.Fprintln(os.Stderr, "   getAdPlanBudgetStatusByPlanIdList(RequestHeader header,  pids, TimeInt updatedSince)")
	fmt.Fprintln(os.Stderr, "   listConsumeBrief(RequestHeader header,  uids, TimeInt fromDate, TimeInt toDate)")
	fmt.Fprintln(os.Stderr, "   listIncomeBrief(RequestHeader header,  uids, TimeInt fromDate, TimeInt toDate)")
	fmt.Fprintln(os.Stderr, "   listIncomeConfirmationRequest(RequestHeader header, TimeInt month,  uids,  status)")
	fmt.Fprintln(os.Stderr, "  IncomeConfirmationRecord confirmIncomeConfirmationRequest(RequestHeader header, IdInt id, UidInt uid, Amount confirmedAmount, Amount lossAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  IncomeConfirmationRecord denyIncomeConfirmationRequest(RequestHeader header, IdInt id, UidInt uid, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  IncomeConfirmationRecord confirmIncomeAmount(RequestHeader header, IdInt id, UidInt uid, Amount confirmedAmount, Amount lossAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "   listIncomeConfirmationWeeklyRequest(RequestHeader header, TimeInt month, TimeInt week,  uids,  status)")
	fmt.Fprintln(os.Stderr, "   listSystemCostRequest(RequestHeader header, TimeInt month,  uids)")
	fmt.Fprintln(os.Stderr, "  Settlement updateSettlement(RequestHeader header, Settlement settlement)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord confirmPaymentRequest(RequestHeader header, IdInt id, UidInt uid, Amount confirmedAmount, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord denyPaymentRequest(RequestHeader header, IdInt id, UidInt uid, Amount requestedAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord completePaymentRequest(RequestHeader header, IdInt id, UidInt uid, Amount paidAmount, Amount paymentFee, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "   listPaymentRecord(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  statuses)")
	fmt.Fprintln(os.Stderr, "   getPaymentRecord(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord submitPrePaidPaymentRequest(RequestHeader header, UidInt uid, Amount amount, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord subtractPaymentRequest(RequestHeader header, IdInt id, UidInt uid, Amount paymentAmount, Amount frozenAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord launchPaymentRequest(RequestHeader header, UidInt uid, Amount amount, IdInt entityId, string entityName, TimeInt time, WithdrawType type)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord completePaymentRequestNew(RequestHeader header, IdInt id, UidInt uid, Amount paidAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord denyPaymentRequestNew(RequestHeader header, IdInt id, UidInt uid, Amount requestedAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  FinancialAccount changeFinancialAccount(RequestHeader header, UidInt uid, AccountCategory cate, string name, string identity, string bank, string account)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord submitNoPaymentRequest(RequestHeader header, IdInt id, UidInt uid, Amount nopayAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  AppropriationRecord refundAppropriation(RequestHeader header, UidInt agentUid, UidInt sponsorUid, Amount amount, UidInt adminUid)")
	fmt.Fprintln(os.Stderr, "   getAdPlanBudgetSummary(RequestHeader header,  pidList)")
	fmt.Fprintln(os.Stderr, "   getAdStrategyBudgetSummary(RequestHeader header,  sidList)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := finance.NewFinanceAdminServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "offlineRecharge":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "OfflineRecharge requires 6 args")
			flag.Usage()
		}
		arg1217 := flag.Arg(1)
		mbTrans1218 := thrift.NewTMemoryBufferLen(len(arg1217))
		defer mbTrans1218.Close()
		_, err1219 := mbTrans1218.WriteString(arg1217)
		if err1219 != nil {
			Usage()
			return
		}
		factory1220 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1221 := factory1220.GetProtocol(mbTrans1218)
		argvalue0 := finance.NewRequestHeader()
		err1222 := argvalue0.Read(jsProt1221)
		if err1222 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1223 := flag.Arg(2)
		mbTrans1224 := thrift.NewTMemoryBufferLen(len(arg1223))
		defer mbTrans1224.Close()
		_, err1225 := mbTrans1224.WriteString(arg1223)
		if err1225 != nil {
			Usage()
			return
		}
		factory1226 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1227 := factory1226.GetProtocol(mbTrans1224)
		argvalue1 := finance.NewRechargeCommit()
		err1228 := argvalue1.Read(jsProt1227)
		if err1228 != nil {
			Usage()
			return
		}
		value1 := finance.RechargeCommit(argvalue1)
		argvalue2, err1229 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1229 != nil {
			Usage()
			return
		}
		value2 := finance.Amount(argvalue2)
		argvalue3, err1230 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1230 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		tmp4, err1231 := (strconv.Atoi(flag.Arg(5)))
		if err1231 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		arg1232 := flag.Arg(6)
		mbTrans1233 := thrift.NewTMemoryBufferLen(len(arg1232))
		defer mbTrans1233.Close()
		_, err1234 := mbTrans1233.WriteString(arg1232)
		if err1234 != nil {
			Usage()
			return
		}
		factory1235 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1236 := factory1235.GetProtocol(mbTrans1233)
		containerStruct5 := finance.NewOfflineRechargeArgs()
		err1237 := containerStruct5.ReadField6(jsProt1236)
		if err1237 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.ExtInfo
		value5 := argvalue5
		fmt.Print(client.OfflineRecharge(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "rechargeRevoke":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "RechargeRevoke requires 7 args")
			flag.Usage()
		}
		arg1238 := flag.Arg(1)
		mbTrans1239 := thrift.NewTMemoryBufferLen(len(arg1238))
		defer mbTrans1239.Close()
		_, err1240 := mbTrans1239.WriteString(arg1238)
		if err1240 != nil {
			Usage()
			return
		}
		factory1241 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1242 := factory1241.GetProtocol(mbTrans1239)
		argvalue0 := finance.NewRequestHeader()
		err1243 := argvalue0.Read(jsProt1242)
		if err1243 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1244 := (strconv.Atoi(flag.Arg(2)))
		if err1244 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		arg1245 := flag.Arg(3)
		mbTrans1246 := thrift.NewTMemoryBufferLen(len(arg1245))
		defer mbTrans1246.Close()
		_, err1247 := mbTrans1246.WriteString(arg1245)
		if err1247 != nil {
			Usage()
			return
		}
		factory1248 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1249 := factory1248.GetProtocol(mbTrans1246)
		argvalue2 := finance.NewRechargeCommit()
		err1250 := argvalue2.Read(jsProt1249)
		if err1250 != nil {
			Usage()
			return
		}
		value2 := finance.RechargeCommit(argvalue2)
		argvalue3, err1251 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1251 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4, err1252 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1252 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		tmp5, err1253 := (strconv.Atoi(flag.Arg(6)))
		if err1253 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.UidInt(argvalue5)
		arg1254 := flag.Arg(7)
		mbTrans1255 := thrift.NewTMemoryBufferLen(len(arg1254))
		defer mbTrans1255.Close()
		_, err1256 := mbTrans1255.WriteString(arg1254)
		if err1256 != nil {
			Usage()
			return
		}
		factory1257 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1258 := factory1257.GetProtocol(mbTrans1255)
		containerStruct6 := finance.NewRechargeRevokeArgs()
		err1259 := containerStruct6.ReadField7(jsProt1258)
		if err1259 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.ExtInfo
		value6 := argvalue6
		fmt.Print(client.RechargeRevoke(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "passAdInvoiceRequest":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PassAdInvoiceRequest requires 3 args")
			flag.Usage()
		}
		arg1260 := flag.Arg(1)
		mbTrans1261 := thrift.NewTMemoryBufferLen(len(arg1260))
		defer mbTrans1261.Close()
		_, err1262 := mbTrans1261.WriteString(arg1260)
		if err1262 != nil {
			Usage()
			return
		}
		factory1263 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1264 := factory1263.GetProtocol(mbTrans1261)
		argvalue0 := finance.NewRequestHeader()
		err1265 := argvalue0.Read(jsProt1264)
		if err1265 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1266 := (strconv.Atoi(flag.Arg(2)))
		if err1266 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1267 := (strconv.Atoi(flag.Arg(3)))
		if err1267 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		fmt.Print(client.PassAdInvoiceRequest(value0, value1, value2))
		fmt.Print("\n")
		break
	case "rejectAdInvoiceRequest":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "RejectAdInvoiceRequest requires 4 args")
			flag.Usage()
		}
		arg1268 := flag.Arg(1)
		mbTrans1269 := thrift.NewTMemoryBufferLen(len(arg1268))
		defer mbTrans1269.Close()
		_, err1270 := mbTrans1269.WriteString(arg1268)
		if err1270 != nil {
			Usage()
			return
		}
		factory1271 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1272 := factory1271.GetProtocol(mbTrans1269)
		argvalue0 := finance.NewRequestHeader()
		err1273 := argvalue0.Read(jsProt1272)
		if err1273 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1274 := (strconv.Atoi(flag.Arg(2)))
		if err1274 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1275 := (strconv.Atoi(flag.Arg(3)))
		if err1275 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		arg1276 := flag.Arg(4)
		mbTrans1277 := thrift.NewTMemoryBufferLen(len(arg1276))
		defer mbTrans1277.Close()
		_, err1278 := mbTrans1277.WriteString(arg1276)
		if err1278 != nil {
			Usage()
			return
		}
		factory1279 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1280 := factory1279.GetProtocol(mbTrans1277)
		containerStruct3 := finance.NewRejectAdInvoiceRequestArgs()
		err1281 := containerStruct3.ReadField4(jsProt1280)
		if err1281 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.ExtInfo
		value3 := argvalue3
		fmt.Print(client.RejectAdInvoiceRequest(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "completeAdInvoiceRequest":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "CompleteAdInvoiceRequest requires 3 args")
			flag.Usage()
		}
		arg1282 := flag.Arg(1)
		mbTrans1283 := thrift.NewTMemoryBufferLen(len(arg1282))
		defer mbTrans1283.Close()
		_, err1284 := mbTrans1283.WriteString(arg1282)
		if err1284 != nil {
			Usage()
			return
		}
		factory1285 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1286 := factory1285.GetProtocol(mbTrans1283)
		argvalue0 := finance.NewRequestHeader()
		err1287 := argvalue0.Read(jsProt1286)
		if err1287 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1288 := (strconv.Atoi(flag.Arg(2)))
		if err1288 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1289 := (strconv.Atoi(flag.Arg(3)))
		if err1289 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		fmt.Print(client.CompleteAdInvoiceRequest(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateAwardRate":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateAwardRate requires 3 args")
			flag.Usage()
		}
		arg1290 := flag.Arg(1)
		mbTrans1291 := thrift.NewTMemoryBufferLen(len(arg1290))
		defer mbTrans1291.Close()
		_, err1292 := mbTrans1291.WriteString(arg1290)
		if err1292 != nil {
			Usage()
			return
		}
		factory1293 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1294 := factory1293.GetProtocol(mbTrans1291)
		argvalue0 := finance.NewRequestHeader()
		err1295 := argvalue0.Read(jsProt1294)
		if err1295 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1296 := flag.Arg(2)
		mbTrans1297 := thrift.NewTMemoryBufferLen(len(arg1296))
		defer mbTrans1297.Close()
		_, err1298 := mbTrans1297.WriteString(arg1296)
		if err1298 != nil {
			Usage()
			return
		}
		factory1299 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1300 := factory1299.GetProtocol(mbTrans1297)
		argvalue1 := finance.NewFinancialProfile()
		err1301 := argvalue1.Read(jsProt1300)
		if err1301 != nil {
			Usage()
			return
		}
		value1 := finance.FinancialProfile(argvalue1)
		tmp2, err1302 := (strconv.Atoi(flag.Arg(3)))
		if err1302 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		fmt.Print(client.UpdateAwardRate(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateMediaShareRate":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateMediaShareRate requires 3 args")
			flag.Usage()
		}
		arg1303 := flag.Arg(1)
		mbTrans1304 := thrift.NewTMemoryBufferLen(len(arg1303))
		defer mbTrans1304.Close()
		_, err1305 := mbTrans1304.WriteString(arg1303)
		if err1305 != nil {
			Usage()
			return
		}
		factory1306 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1307 := factory1306.GetProtocol(mbTrans1304)
		argvalue0 := finance.NewRequestHeader()
		err1308 := argvalue0.Read(jsProt1307)
		if err1308 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1309 := flag.Arg(2)
		mbTrans1310 := thrift.NewTMemoryBufferLen(len(arg1309))
		defer mbTrans1310.Close()
		_, err1311 := mbTrans1310.WriteString(arg1309)
		if err1311 != nil {
			Usage()
			return
		}
		factory1312 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1313 := factory1312.GetProtocol(mbTrans1310)
		argvalue1 := finance.NewFinancialProfile()
		err1314 := argvalue1.Read(jsProt1313)
		if err1314 != nil {
			Usage()
			return
		}
		value1 := finance.FinancialProfile(argvalue1)
		tmp2, err1315 := (strconv.Atoi(flag.Arg(3)))
		if err1315 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		fmt.Print(client.UpdateMediaShareRate(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateCredit":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateCredit requires 3 args")
			flag.Usage()
		}
		arg1316 := flag.Arg(1)
		mbTrans1317 := thrift.NewTMemoryBufferLen(len(arg1316))
		defer mbTrans1317.Close()
		_, err1318 := mbTrans1317.WriteString(arg1316)
		if err1318 != nil {
			Usage()
			return
		}
		factory1319 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1320 := factory1319.GetProtocol(mbTrans1317)
		argvalue0 := finance.NewRequestHeader()
		err1321 := argvalue0.Read(jsProt1320)
		if err1321 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1322 := flag.Arg(2)
		mbTrans1323 := thrift.NewTMemoryBufferLen(len(arg1322))
		defer mbTrans1323.Close()
		_, err1324 := mbTrans1323.WriteString(arg1322)
		if err1324 != nil {
			Usage()
			return
		}
		factory1325 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1326 := factory1325.GetProtocol(mbTrans1323)
		argvalue1 := finance.NewFinancialProfile()
		err1327 := argvalue1.Read(jsProt1326)
		if err1327 != nil {
			Usage()
			return
		}
		value1 := finance.FinancialProfile(argvalue1)
		tmp2, err1328 := (strconv.Atoi(flag.Arg(3)))
		if err1328 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		fmt.Print(client.UpdateCredit(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateUserFinancialProfile":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateUserFinancialProfile requires 3 args")
			flag.Usage()
		}
		arg1329 := flag.Arg(1)
		mbTrans1330 := thrift.NewTMemoryBufferLen(len(arg1329))
		defer mbTrans1330.Close()
		_, err1331 := mbTrans1330.WriteString(arg1329)
		if err1331 != nil {
			Usage()
			return
		}
		factory1332 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1333 := factory1332.GetProtocol(mbTrans1330)
		argvalue0 := finance.NewRequestHeader()
		err1334 := argvalue0.Read(jsProt1333)
		if err1334 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1335 := (strconv.Atoi(flag.Arg(2)))
		if err1335 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg1336 := flag.Arg(3)
		mbTrans1337 := thrift.NewTMemoryBufferLen(len(arg1336))
		defer mbTrans1337.Close()
		_, err1338 := mbTrans1337.WriteString(arg1336)
		if err1338 != nil {
			Usage()
			return
		}
		factory1339 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1340 := factory1339.GetProtocol(mbTrans1337)
		argvalue2 := finance.NewFinancialProfile()
		err1341 := argvalue2.Read(jsProt1340)
		if err1341 != nil {
			Usage()
			return
		}
		value2 := finance.FinancialProfile(argvalue2)
		fmt.Print(client.UpdateUserFinancialProfile(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateSpecificMediaShareRate":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "UpdateSpecificMediaShareRate requires 4 args")
			flag.Usage()
		}
		arg1342 := flag.Arg(1)
		mbTrans1343 := thrift.NewTMemoryBufferLen(len(arg1342))
		defer mbTrans1343.Close()
		_, err1344 := mbTrans1343.WriteString(arg1342)
		if err1344 != nil {
			Usage()
			return
		}
		factory1345 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1346 := factory1345.GetProtocol(mbTrans1343)
		argvalue0 := finance.NewRequestHeader()
		err1347 := argvalue0.Read(jsProt1346)
		if err1347 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1348 := (strconv.Atoi(flag.Arg(2)))
		if err1348 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err1349 := (strconv.Atoi(flag.Arg(3)))
		if err1349 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.IdInt(argvalue2)
		tmp3, err1350 := (strconv.Atoi(flag.Arg(4)))
		if err1350 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.PercentageInt(argvalue3)
		fmt.Print(client.UpdateSpecificMediaShareRate(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listMediaProfile":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListMediaProfile requires 4 args")
			flag.Usage()
		}
		arg1351 := flag.Arg(1)
		mbTrans1352 := thrift.NewTMemoryBufferLen(len(arg1351))
		defer mbTrans1352.Close()
		_, err1353 := mbTrans1352.WriteString(arg1351)
		if err1353 != nil {
			Usage()
			return
		}
		factory1354 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1355 := factory1354.GetProtocol(mbTrans1352)
		argvalue0 := finance.NewRequestHeader()
		err1356 := argvalue0.Read(jsProt1355)
		if err1356 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1357 := (strconv.Atoi(flag.Arg(2)))
		if err1357 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err1358 := (strconv.Atoi(flag.Arg(3)))
		if err1358 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.QueryInt(argvalue2)
		tmp3, err1359 := (strconv.Atoi(flag.Arg(4)))
		if err1359 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.QueryInt(argvalue3)
		fmt.Print(client.ListMediaProfile(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getMediaProfileByIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMediaProfileByIdList requires 2 args")
			flag.Usage()
		}
		arg1360 := flag.Arg(1)
		mbTrans1361 := thrift.NewTMemoryBufferLen(len(arg1360))
		defer mbTrans1361.Close()
		_, err1362 := mbTrans1361.WriteString(arg1360)
		if err1362 != nil {
			Usage()
			return
		}
		factory1363 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1364 := factory1363.GetProtocol(mbTrans1361)
		argvalue0 := finance.NewRequestHeader()
		err1365 := argvalue0.Read(jsProt1364)
		if err1365 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1366 := flag.Arg(2)
		mbTrans1367 := thrift.NewTMemoryBufferLen(len(arg1366))
		defer mbTrans1367.Close()
		_, err1368 := mbTrans1367.WriteString(arg1366)
		if err1368 != nil {
			Usage()
			return
		}
		factory1369 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1370 := factory1369.GetProtocol(mbTrans1367)
		containerStruct1 := finance.NewGetMediaProfileByIdListArgs()
		err1371 := containerStruct1.ReadField2(jsProt1370)
		if err1371 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.IdList
		value1 := argvalue1
		fmt.Print(client.GetMediaProfileByIdList(value0, value1))
		fmt.Print("\n")
		break
	case "listTeamMembers":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListTeamMembers requires 5 args")
			flag.Usage()
		}
		arg1372 := flag.Arg(1)
		mbTrans1373 := thrift.NewTMemoryBufferLen(len(arg1372))
		defer mbTrans1373.Close()
		_, err1374 := mbTrans1373.WriteString(arg1372)
		if err1374 != nil {
			Usage()
			return
		}
		factory1375 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1376 := factory1375.GetProtocol(mbTrans1373)
		argvalue0 := finance.NewRequestHeader()
		err1377 := argvalue0.Read(jsProt1376)
		if err1377 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1378 := (strconv.Atoi(flag.Arg(2)))
		if err1378 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CommonAuditStatus(tmp2)
		value2 := finance.CommonAuditStatus(argvalue2)
		tmp3, err1379 := (strconv.Atoi(flag.Arg(4)))
		if err1379 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.QueryInt(argvalue3)
		tmp4, err1380 := (strconv.Atoi(flag.Arg(5)))
		if err1380 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.QueryInt(argvalue4)
		fmt.Print(client.ListTeamMembers(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "passTeamMember":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "PassTeamMember requires 4 args")
			flag.Usage()
		}
		arg1381 := flag.Arg(1)
		mbTrans1382 := thrift.NewTMemoryBufferLen(len(arg1381))
		defer mbTrans1382.Close()
		_, err1383 := mbTrans1382.WriteString(arg1381)
		if err1383 != nil {
			Usage()
			return
		}
		factory1384 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1385 := factory1384.GetProtocol(mbTrans1382)
		argvalue0 := finance.NewRequestHeader()
		err1386 := argvalue0.Read(jsProt1385)
		if err1386 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1387 := (strconv.Atoi(flag.Arg(2)))
		if err1387 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1388 := (strconv.Atoi(flag.Arg(3)))
		if err1388 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.PassTeamMember(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "rejectTeamMember":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "RejectTeamMember requires 4 args")
			flag.Usage()
		}
		arg1390 := flag.Arg(1)
		mbTrans1391 := thrift.NewTMemoryBufferLen(len(arg1390))
		defer mbTrans1391.Close()
		_, err1392 := mbTrans1391.WriteString(arg1390)
		if err1392 != nil {
			Usage()
			return
		}
		factory1393 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1394 := factory1393.GetProtocol(mbTrans1391)
		argvalue0 := finance.NewRequestHeader()
		err1395 := argvalue0.Read(jsProt1394)
		if err1395 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1396 := (strconv.Atoi(flag.Arg(2)))
		if err1396 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1397 := (strconv.Atoi(flag.Arg(3)))
		if err1397 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.RejectTeamMember(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "forbidTeamMember":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ForbidTeamMember requires 4 args")
			flag.Usage()
		}
		arg1399 := flag.Arg(1)
		mbTrans1400 := thrift.NewTMemoryBufferLen(len(arg1399))
		defer mbTrans1400.Close()
		_, err1401 := mbTrans1400.WriteString(arg1399)
		if err1401 != nil {
			Usage()
			return
		}
		factory1402 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1403 := factory1402.GetProtocol(mbTrans1400)
		argvalue0 := finance.NewRequestHeader()
		err1404 := argvalue0.Read(jsProt1403)
		if err1404 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1405 := (strconv.Atoi(flag.Arg(2)))
		if err1405 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1406 := (strconv.Atoi(flag.Arg(3)))
		if err1406 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		fmt.Print(client.ForbidTeamMember(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listTeamMembersByName":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListTeamMembersByName requires 4 args")
			flag.Usage()
		}
		arg1408 := flag.Arg(1)
		mbTrans1409 := thrift.NewTMemoryBufferLen(len(arg1408))
		defer mbTrans1409.Close()
		_, err1410 := mbTrans1409.WriteString(arg1408)
		if err1410 != nil {
			Usage()
			return
		}
		factory1411 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1412 := factory1411.GetProtocol(mbTrans1409)
		argvalue0 := finance.NewRequestHeader()
		err1413 := argvalue0.Read(jsProt1412)
		if err1413 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		tmp2, err1415 := (strconv.Atoi(flag.Arg(3)))
		if err1415 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.QueryInt(argvalue2)
		tmp3, err1416 := (strconv.Atoi(flag.Arg(4)))
		if err1416 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.QueryInt(argvalue3)
		fmt.Print(client.ListTeamMembersByName(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getConsumeRecords":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "GetConsumeRecords requires 6 args")
			flag.Usage()
		}
		arg1417 := flag.Arg(1)
		mbTrans1418 := thrift.NewTMemoryBufferLen(len(arg1417))
		defer mbTrans1418.Close()
		_, err1419 := mbTrans1418.WriteString(arg1417)
		if err1419 != nil {
			Usage()
			return
		}
		factory1420 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1421 := factory1420.GetProtocol(mbTrans1418)
		argvalue0 := finance.NewRequestHeader()
		err1422 := argvalue0.Read(jsProt1421)
		if err1422 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1423 := flag.Arg(2)
		mbTrans1424 := thrift.NewTMemoryBufferLen(len(arg1423))
		defer mbTrans1424.Close()
		_, err1425 := mbTrans1424.WriteString(arg1423)
		if err1425 != nil {
			Usage()
			return
		}
		factory1426 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1427 := factory1426.GetProtocol(mbTrans1424)
		containerStruct1 := finance.NewGetConsumeRecordsArgs()
		err1428 := containerStruct1.ReadField2(jsProt1427)
		if err1428 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2, err1429 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1429 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err1430 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1430 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		tmp4, err1431 := (strconv.Atoi(flag.Arg(5)))
		if err1431 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.QueryInt(argvalue4)
		tmp5, err1432 := (strconv.Atoi(flag.Arg(6)))
		if err1432 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.QueryInt(argvalue5)
		fmt.Print(client.GetConsumeRecords(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "getFinancialProfilesByUidList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetFinancialProfilesByUidList requires 2 args")
			flag.Usage()
		}
		arg1433 := flag.Arg(1)
		mbTrans1434 := thrift.NewTMemoryBufferLen(len(arg1433))
		defer mbTrans1434.Close()
		_, err1435 := mbTrans1434.WriteString(arg1433)
		if err1435 != nil {
			Usage()
			return
		}
		factory1436 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1437 := factory1436.GetProtocol(mbTrans1434)
		argvalue0 := finance.NewRequestHeader()
		err1438 := argvalue0.Read(jsProt1437)
		if err1438 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1439 := flag.Arg(2)
		mbTrans1440 := thrift.NewTMemoryBufferLen(len(arg1439))
		defer mbTrans1440.Close()
		_, err1441 := mbTrans1440.WriteString(arg1439)
		if err1441 != nil {
			Usage()
			return
		}
		factory1442 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1443 := factory1442.GetProtocol(mbTrans1440)
		containerStruct1 := finance.NewGetFinancialProfilesByUidListArgs()
		err1444 := containerStruct1.ReadField2(jsProt1443)
		if err1444 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetFinancialProfilesByUidList(value0, value1))
		fmt.Print("\n")
		break
	case "getFinancialSummariesByUidList":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetFinancialSummariesByUidList requires 3 args")
			flag.Usage()
		}
		arg1445 := flag.Arg(1)
		mbTrans1446 := thrift.NewTMemoryBufferLen(len(arg1445))
		defer mbTrans1446.Close()
		_, err1447 := mbTrans1446.WriteString(arg1445)
		if err1447 != nil {
			Usage()
			return
		}
		factory1448 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1449 := factory1448.GetProtocol(mbTrans1446)
		argvalue0 := finance.NewRequestHeader()
		err1450 := argvalue0.Read(jsProt1449)
		if err1450 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1451 := flag.Arg(2)
		mbTrans1452 := thrift.NewTMemoryBufferLen(len(arg1451))
		defer mbTrans1452.Close()
		_, err1453 := mbTrans1452.WriteString(arg1451)
		if err1453 != nil {
			Usage()
			return
		}
		factory1454 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1455 := factory1454.GetProtocol(mbTrans1452)
		containerStruct1 := finance.NewGetFinancialSummariesByUidListArgs()
		err1456 := containerStruct1.ReadField2(jsProt1455)
		if err1456 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.GetFinancialSummariesByUidList(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdPlanBudgetStatusByUidList":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAdPlanBudgetStatusByUidList requires 3 args")
			flag.Usage()
		}
		arg1458 := flag.Arg(1)
		mbTrans1459 := thrift.NewTMemoryBufferLen(len(arg1458))
		defer mbTrans1459.Close()
		_, err1460 := mbTrans1459.WriteString(arg1458)
		if err1460 != nil {
			Usage()
			return
		}
		factory1461 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1462 := factory1461.GetProtocol(mbTrans1459)
		argvalue0 := finance.NewRequestHeader()
		err1463 := argvalue0.Read(jsProt1462)
		if err1463 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1464 := flag.Arg(2)
		mbTrans1465 := thrift.NewTMemoryBufferLen(len(arg1464))
		defer mbTrans1465.Close()
		_, err1466 := mbTrans1465.WriteString(arg1464)
		if err1466 != nil {
			Usage()
			return
		}
		factory1467 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1468 := factory1467.GetProtocol(mbTrans1465)
		containerStruct1 := finance.NewGetAdPlanBudgetStatusByUidListArgs()
		err1469 := containerStruct1.ReadField2(jsProt1468)
		if err1469 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2, err1470 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1470 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		fmt.Print(client.GetAdPlanBudgetStatusByUidList(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdPlanBudgetStatusByPlanIdList":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetAdPlanBudgetStatusByPlanIdList requires 3 args")
			flag.Usage()
		}
		arg1471 := flag.Arg(1)
		mbTrans1472 := thrift.NewTMemoryBufferLen(len(arg1471))
		defer mbTrans1472.Close()
		_, err1473 := mbTrans1472.WriteString(arg1471)
		if err1473 != nil {
			Usage()
			return
		}
		factory1474 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1475 := factory1474.GetProtocol(mbTrans1472)
		argvalue0 := finance.NewRequestHeader()
		err1476 := argvalue0.Read(jsProt1475)
		if err1476 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1477 := flag.Arg(2)
		mbTrans1478 := thrift.NewTMemoryBufferLen(len(arg1477))
		defer mbTrans1478.Close()
		_, err1479 := mbTrans1478.WriteString(arg1477)
		if err1479 != nil {
			Usage()
			return
		}
		factory1480 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1481 := factory1480.GetProtocol(mbTrans1478)
		containerStruct1 := finance.NewGetAdPlanBudgetStatusByPlanIdListArgs()
		err1482 := containerStruct1.ReadField2(jsProt1481)
		if err1482 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Pids
		value1 := argvalue1
		argvalue2, err1483 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1483 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		fmt.Print(client.GetAdPlanBudgetStatusByPlanIdList(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listConsumeBrief":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListConsumeBrief requires 4 args")
			flag.Usage()
		}
		arg1484 := flag.Arg(1)
		mbTrans1485 := thrift.NewTMemoryBufferLen(len(arg1484))
		defer mbTrans1485.Close()
		_, err1486 := mbTrans1485.WriteString(arg1484)
		if err1486 != nil {
			Usage()
			return
		}
		factory1487 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1488 := factory1487.GetProtocol(mbTrans1485)
		argvalue0 := finance.NewRequestHeader()
		err1489 := argvalue0.Read(jsProt1488)
		if err1489 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1490 := flag.Arg(2)
		mbTrans1491 := thrift.NewTMemoryBufferLen(len(arg1490))
		defer mbTrans1491.Close()
		_, err1492 := mbTrans1491.WriteString(arg1490)
		if err1492 != nil {
			Usage()
			return
		}
		factory1493 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1494 := factory1493.GetProtocol(mbTrans1491)
		containerStruct1 := finance.NewListConsumeBriefArgs()
		err1495 := containerStruct1.ReadField2(jsProt1494)
		if err1495 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2, err1496 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1496 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err1497 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1497 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		fmt.Print(client.ListConsumeBrief(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listIncomeBrief":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListIncomeBrief requires 4 args")
			flag.Usage()
		}
		arg1498 := flag.Arg(1)
		mbTrans1499 := thrift.NewTMemoryBufferLen(len(arg1498))
		defer mbTrans1499.Close()
		_, err1500 := mbTrans1499.WriteString(arg1498)
		if err1500 != nil {
			Usage()
			return
		}
		factory1501 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1502 := factory1501.GetProtocol(mbTrans1499)
		argvalue0 := finance.NewRequestHeader()
		err1503 := argvalue0.Read(jsProt1502)
		if err1503 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1504 := flag.Arg(2)
		mbTrans1505 := thrift.NewTMemoryBufferLen(len(arg1504))
		defer mbTrans1505.Close()
		_, err1506 := mbTrans1505.WriteString(arg1504)
		if err1506 != nil {
			Usage()
			return
		}
		factory1507 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1508 := factory1507.GetProtocol(mbTrans1505)
		containerStruct1 := finance.NewListIncomeBriefArgs()
		err1509 := containerStruct1.ReadField2(jsProt1508)
		if err1509 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		argvalue2, err1510 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1510 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err1511 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1511 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		fmt.Print(client.ListIncomeBrief(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listIncomeConfirmationRequest":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListIncomeConfirmationRequest requires 4 args")
			flag.Usage()
		}
		arg1512 := flag.Arg(1)
		mbTrans1513 := thrift.NewTMemoryBufferLen(len(arg1512))
		defer mbTrans1513.Close()
		_, err1514 := mbTrans1513.WriteString(arg1512)
		if err1514 != nil {
			Usage()
			return
		}
		factory1515 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1516 := factory1515.GetProtocol(mbTrans1513)
		argvalue0 := finance.NewRequestHeader()
		err1517 := argvalue0.Read(jsProt1516)
		if err1517 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1, err1518 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1518 != nil {
			Usage()
			return
		}
		value1 := finance.TimeInt(argvalue1)
		arg1519 := flag.Arg(3)
		mbTrans1520 := thrift.NewTMemoryBufferLen(len(arg1519))
		defer mbTrans1520.Close()
		_, err1521 := mbTrans1520.WriteString(arg1519)
		if err1521 != nil {
			Usage()
			return
		}
		factory1522 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1523 := factory1522.GetProtocol(mbTrans1520)
		containerStruct2 := finance.NewListIncomeConfirmationRequestArgs()
		err1524 := containerStruct2.ReadField3(jsProt1523)
		if err1524 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Uids
		value2 := argvalue2
		arg1525 := flag.Arg(4)
		mbTrans1526 := thrift.NewTMemoryBufferLen(len(arg1525))
		defer mbTrans1526.Close()
		_, err1527 := mbTrans1526.WriteString(arg1525)
		if err1527 != nil {
			Usage()
			return
		}
		factory1528 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1529 := factory1528.GetProtocol(mbTrans1526)
		containerStruct3 := finance.NewListIncomeConfirmationRequestArgs()
		err1530 := containerStruct3.ReadField4(jsProt1529)
		if err1530 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Status
		value3 := argvalue3
		fmt.Print(client.ListIncomeConfirmationRequest(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "confirmIncomeConfirmationRequest":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ConfirmIncomeConfirmationRequest requires 7 args")
			flag.Usage()
		}
		arg1531 := flag.Arg(1)
		mbTrans1532 := thrift.NewTMemoryBufferLen(len(arg1531))
		defer mbTrans1532.Close()
		_, err1533 := mbTrans1532.WriteString(arg1531)
		if err1533 != nil {
			Usage()
			return
		}
		factory1534 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1535 := factory1534.GetProtocol(mbTrans1532)
		argvalue0 := finance.NewRequestHeader()
		err1536 := argvalue0.Read(jsProt1535)
		if err1536 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1537 := (strconv.Atoi(flag.Arg(2)))
		if err1537 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1538 := (strconv.Atoi(flag.Arg(3)))
		if err1538 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1539 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1539 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4, err1540 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1540 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		tmp5, err1541 := (strconv.Atoi(flag.Arg(6)))
		if err1541 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.UidInt(argvalue5)
		arg1542 := flag.Arg(7)
		mbTrans1543 := thrift.NewTMemoryBufferLen(len(arg1542))
		defer mbTrans1543.Close()
		_, err1544 := mbTrans1543.WriteString(arg1542)
		if err1544 != nil {
			Usage()
			return
		}
		factory1545 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1546 := factory1545.GetProtocol(mbTrans1543)
		containerStruct6 := finance.NewConfirmIncomeConfirmationRequestArgs()
		err1547 := containerStruct6.ReadField7(jsProt1546)
		if err1547 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.ExtInfo
		value6 := argvalue6
		fmt.Print(client.ConfirmIncomeConfirmationRequest(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "denyIncomeConfirmationRequest":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DenyIncomeConfirmationRequest requires 5 args")
			flag.Usage()
		}
		arg1548 := flag.Arg(1)
		mbTrans1549 := thrift.NewTMemoryBufferLen(len(arg1548))
		defer mbTrans1549.Close()
		_, err1550 := mbTrans1549.WriteString(arg1548)
		if err1550 != nil {
			Usage()
			return
		}
		factory1551 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1552 := factory1551.GetProtocol(mbTrans1549)
		argvalue0 := finance.NewRequestHeader()
		err1553 := argvalue0.Read(jsProt1552)
		if err1553 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1554 := (strconv.Atoi(flag.Arg(2)))
		if err1554 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1555 := (strconv.Atoi(flag.Arg(3)))
		if err1555 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		tmp3, err1556 := (strconv.Atoi(flag.Arg(4)))
		if err1556 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.UidInt(argvalue3)
		arg1557 := flag.Arg(5)
		mbTrans1558 := thrift.NewTMemoryBufferLen(len(arg1557))
		defer mbTrans1558.Close()
		_, err1559 := mbTrans1558.WriteString(arg1557)
		if err1559 != nil {
			Usage()
			return
		}
		factory1560 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1561 := factory1560.GetProtocol(mbTrans1558)
		containerStruct4 := finance.NewDenyIncomeConfirmationRequestArgs()
		err1562 := containerStruct4.ReadField5(jsProt1561)
		if err1562 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.ExtInfo
		value4 := argvalue4
		fmt.Print(client.DenyIncomeConfirmationRequest(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "confirmIncomeAmount":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ConfirmIncomeAmount requires 7 args")
			flag.Usage()
		}
		arg1563 := flag.Arg(1)
		mbTrans1564 := thrift.NewTMemoryBufferLen(len(arg1563))
		defer mbTrans1564.Close()
		_, err1565 := mbTrans1564.WriteString(arg1563)
		if err1565 != nil {
			Usage()
			return
		}
		factory1566 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1567 := factory1566.GetProtocol(mbTrans1564)
		argvalue0 := finance.NewRequestHeader()
		err1568 := argvalue0.Read(jsProt1567)
		if err1568 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1569 := (strconv.Atoi(flag.Arg(2)))
		if err1569 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1570 := (strconv.Atoi(flag.Arg(3)))
		if err1570 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1571 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1571 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4, err1572 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1572 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		tmp5, err1573 := (strconv.Atoi(flag.Arg(6)))
		if err1573 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.UidInt(argvalue5)
		arg1574 := flag.Arg(7)
		mbTrans1575 := thrift.NewTMemoryBufferLen(len(arg1574))
		defer mbTrans1575.Close()
		_, err1576 := mbTrans1575.WriteString(arg1574)
		if err1576 != nil {
			Usage()
			return
		}
		factory1577 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1578 := factory1577.GetProtocol(mbTrans1575)
		containerStruct6 := finance.NewConfirmIncomeAmountArgs()
		err1579 := containerStruct6.ReadField7(jsProt1578)
		if err1579 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.ExtInfo
		value6 := argvalue6
		fmt.Print(client.ConfirmIncomeAmount(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listIncomeConfirmationWeeklyRequest":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListIncomeConfirmationWeeklyRequest requires 5 args")
			flag.Usage()
		}
		arg1580 := flag.Arg(1)
		mbTrans1581 := thrift.NewTMemoryBufferLen(len(arg1580))
		defer mbTrans1581.Close()
		_, err1582 := mbTrans1581.WriteString(arg1580)
		if err1582 != nil {
			Usage()
			return
		}
		factory1583 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1584 := factory1583.GetProtocol(mbTrans1581)
		argvalue0 := finance.NewRequestHeader()
		err1585 := argvalue0.Read(jsProt1584)
		if err1585 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1, err1586 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1586 != nil {
			Usage()
			return
		}
		value1 := finance.TimeInt(argvalue1)
		argvalue2, err1587 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1587 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		arg1588 := flag.Arg(4)
		mbTrans1589 := thrift.NewTMemoryBufferLen(len(arg1588))
		defer mbTrans1589.Close()
		_, err1590 := mbTrans1589.WriteString(arg1588)
		if err1590 != nil {
			Usage()
			return
		}
		factory1591 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1592 := factory1591.GetProtocol(mbTrans1589)
		containerStruct3 := finance.NewListIncomeConfirmationWeeklyRequestArgs()
		err1593 := containerStruct3.ReadField4(jsProt1592)
		if err1593 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Uids
		value3 := argvalue3
		arg1594 := flag.Arg(5)
		mbTrans1595 := thrift.NewTMemoryBufferLen(len(arg1594))
		defer mbTrans1595.Close()
		_, err1596 := mbTrans1595.WriteString(arg1594)
		if err1596 != nil {
			Usage()
			return
		}
		factory1597 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1598 := factory1597.GetProtocol(mbTrans1595)
		containerStruct4 := finance.NewListIncomeConfirmationWeeklyRequestArgs()
		err1599 := containerStruct4.ReadField5(jsProt1598)
		if err1599 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Status
		value4 := argvalue4
		fmt.Print(client.ListIncomeConfirmationWeeklyRequest(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "listSystemCostRequest":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListSystemCostRequest requires 3 args")
			flag.Usage()
		}
		arg1600 := flag.Arg(1)
		mbTrans1601 := thrift.NewTMemoryBufferLen(len(arg1600))
		defer mbTrans1601.Close()
		_, err1602 := mbTrans1601.WriteString(arg1600)
		if err1602 != nil {
			Usage()
			return
		}
		factory1603 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1604 := factory1603.GetProtocol(mbTrans1601)
		argvalue0 := finance.NewRequestHeader()
		err1605 := argvalue0.Read(jsProt1604)
		if err1605 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1, err1606 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err1606 != nil {
			Usage()
			return
		}
		value1 := finance.TimeInt(argvalue1)
		arg1607 := flag.Arg(3)
		mbTrans1608 := thrift.NewTMemoryBufferLen(len(arg1607))
		defer mbTrans1608.Close()
		_, err1609 := mbTrans1608.WriteString(arg1607)
		if err1609 != nil {
			Usage()
			return
		}
		factory1610 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1611 := factory1610.GetProtocol(mbTrans1608)
		containerStruct2 := finance.NewListSystemCostRequestArgs()
		err1612 := containerStruct2.ReadField3(jsProt1611)
		if err1612 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Uids
		value2 := argvalue2
		fmt.Print(client.ListSystemCostRequest(value0, value1, value2))
		fmt.Print("\n")
		break
	case "updateSettlement":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateSettlement requires 2 args")
			flag.Usage()
		}
		arg1613 := flag.Arg(1)
		mbTrans1614 := thrift.NewTMemoryBufferLen(len(arg1613))
		defer mbTrans1614.Close()
		_, err1615 := mbTrans1614.WriteString(arg1613)
		if err1615 != nil {
			Usage()
			return
		}
		factory1616 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1617 := factory1616.GetProtocol(mbTrans1614)
		argvalue0 := finance.NewRequestHeader()
		err1618 := argvalue0.Read(jsProt1617)
		if err1618 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1619 := flag.Arg(2)
		mbTrans1620 := thrift.NewTMemoryBufferLen(len(arg1619))
		defer mbTrans1620.Close()
		_, err1621 := mbTrans1620.WriteString(arg1619)
		if err1621 != nil {
			Usage()
			return
		}
		factory1622 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1623 := factory1622.GetProtocol(mbTrans1620)
		argvalue1 := finance.NewSettlement()
		err1624 := argvalue1.Read(jsProt1623)
		if err1624 != nil {
			Usage()
			return
		}
		value1 := finance.Settlement(argvalue1)
		fmt.Print(client.UpdateSettlement(value0, value1))
		fmt.Print("\n")
		break
	case "confirmPaymentRequest":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ConfirmPaymentRequest requires 5 args")
			flag.Usage()
		}
		arg1625 := flag.Arg(1)
		mbTrans1626 := thrift.NewTMemoryBufferLen(len(arg1625))
		defer mbTrans1626.Close()
		_, err1627 := mbTrans1626.WriteString(arg1625)
		if err1627 != nil {
			Usage()
			return
		}
		factory1628 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1629 := factory1628.GetProtocol(mbTrans1626)
		argvalue0 := finance.NewRequestHeader()
		err1630 := argvalue0.Read(jsProt1629)
		if err1630 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1631 := (strconv.Atoi(flag.Arg(2)))
		if err1631 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1632 := (strconv.Atoi(flag.Arg(3)))
		if err1632 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1633 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1633 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		tmp4, err1634 := (strconv.Atoi(flag.Arg(5)))
		if err1634 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		fmt.Print(client.ConfirmPaymentRequest(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "denyPaymentRequest":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "DenyPaymentRequest requires 6 args")
			flag.Usage()
		}
		arg1635 := flag.Arg(1)
		mbTrans1636 := thrift.NewTMemoryBufferLen(len(arg1635))
		defer mbTrans1636.Close()
		_, err1637 := mbTrans1636.WriteString(arg1635)
		if err1637 != nil {
			Usage()
			return
		}
		factory1638 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1639 := factory1638.GetProtocol(mbTrans1636)
		argvalue0 := finance.NewRequestHeader()
		err1640 := argvalue0.Read(jsProt1639)
		if err1640 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1641 := (strconv.Atoi(flag.Arg(2)))
		if err1641 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1642 := (strconv.Atoi(flag.Arg(3)))
		if err1642 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1643 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1643 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		tmp4, err1644 := (strconv.Atoi(flag.Arg(5)))
		if err1644 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		arg1645 := flag.Arg(6)
		mbTrans1646 := thrift.NewTMemoryBufferLen(len(arg1645))
		defer mbTrans1646.Close()
		_, err1647 := mbTrans1646.WriteString(arg1645)
		if err1647 != nil {
			Usage()
			return
		}
		factory1648 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1649 := factory1648.GetProtocol(mbTrans1646)
		containerStruct5 := finance.NewDenyPaymentRequestArgs()
		err1650 := containerStruct5.ReadField6(jsProt1649)
		if err1650 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.ExtInfo
		value5 := argvalue5
		fmt.Print(client.DenyPaymentRequest(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "completePaymentRequest":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "CompletePaymentRequest requires 7 args")
			flag.Usage()
		}
		arg1651 := flag.Arg(1)
		mbTrans1652 := thrift.NewTMemoryBufferLen(len(arg1651))
		defer mbTrans1652.Close()
		_, err1653 := mbTrans1652.WriteString(arg1651)
		if err1653 != nil {
			Usage()
			return
		}
		factory1654 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1655 := factory1654.GetProtocol(mbTrans1652)
		argvalue0 := finance.NewRequestHeader()
		err1656 := argvalue0.Read(jsProt1655)
		if err1656 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1657 := (strconv.Atoi(flag.Arg(2)))
		if err1657 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1658 := (strconv.Atoi(flag.Arg(3)))
		if err1658 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1659 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1659 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4, err1660 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1660 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		tmp5, err1661 := (strconv.Atoi(flag.Arg(6)))
		if err1661 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.UidInt(argvalue5)
		arg1662 := flag.Arg(7)
		mbTrans1663 := thrift.NewTMemoryBufferLen(len(arg1662))
		defer mbTrans1663.Close()
		_, err1664 := mbTrans1663.WriteString(arg1662)
		if err1664 != nil {
			Usage()
			return
		}
		factory1665 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1666 := factory1665.GetProtocol(mbTrans1663)
		containerStruct6 := finance.NewCompletePaymentRequestArgs()
		err1667 := containerStruct6.ReadField7(jsProt1666)
		if err1667 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.ExtInfo
		value6 := argvalue6
		fmt.Print(client.CompletePaymentRequest(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "listPaymentRecord":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListPaymentRecord requires 5 args")
			flag.Usage()
		}
		arg1668 := flag.Arg(1)
		mbTrans1669 := thrift.NewTMemoryBufferLen(len(arg1668))
		defer mbTrans1669.Close()
		_, err1670 := mbTrans1669.WriteString(arg1668)
		if err1670 != nil {
			Usage()
			return
		}
		factory1671 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1672 := factory1671.GetProtocol(mbTrans1669)
		argvalue0 := finance.NewRequestHeader()
		err1673 := argvalue0.Read(jsProt1672)
		if err1673 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1674 := (strconv.Atoi(flag.Arg(2)))
		if err1674 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err1675 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1675 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err1676 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1676 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg1677 := flag.Arg(5)
		mbTrans1678 := thrift.NewTMemoryBufferLen(len(arg1677))
		defer mbTrans1678.Close()
		_, err1679 := mbTrans1678.WriteString(arg1677)
		if err1679 != nil {
			Usage()
			return
		}
		factory1680 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1681 := factory1680.GetProtocol(mbTrans1678)
		containerStruct4 := finance.NewListPaymentRecordArgs()
		err1682 := containerStruct4.ReadField5(jsProt1681)
		if err1682 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Statuses
		value4 := argvalue4
		fmt.Print(client.ListPaymentRecord(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getPaymentRecord":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPaymentRecord requires 2 args")
			flag.Usage()
		}
		arg1683 := flag.Arg(1)
		mbTrans1684 := thrift.NewTMemoryBufferLen(len(arg1683))
		defer mbTrans1684.Close()
		_, err1685 := mbTrans1684.WriteString(arg1683)
		if err1685 != nil {
			Usage()
			return
		}
		factory1686 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1687 := factory1686.GetProtocol(mbTrans1684)
		argvalue0 := finance.NewRequestHeader()
		err1688 := argvalue0.Read(jsProt1687)
		if err1688 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1689 := flag.Arg(2)
		mbTrans1690 := thrift.NewTMemoryBufferLen(len(arg1689))
		defer mbTrans1690.Close()
		_, err1691 := mbTrans1690.WriteString(arg1689)
		if err1691 != nil {
			Usage()
			return
		}
		factory1692 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1693 := factory1692.GetProtocol(mbTrans1690)
		containerStruct1 := finance.NewGetPaymentRecordArgs()
		err1694 := containerStruct1.ReadField2(jsProt1693)
		if err1694 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPaymentRecord(value0, value1))
		fmt.Print("\n")
		break
	case "submitPrePaidPaymentRequest":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SubmitPrePaidPaymentRequest requires 4 args")
			flag.Usage()
		}
		arg1695 := flag.Arg(1)
		mbTrans1696 := thrift.NewTMemoryBufferLen(len(arg1695))
		defer mbTrans1696.Close()
		_, err1697 := mbTrans1696.WriteString(arg1695)
		if err1697 != nil {
			Usage()
			return
		}
		factory1698 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1699 := factory1698.GetProtocol(mbTrans1696)
		argvalue0 := finance.NewRequestHeader()
		err1700 := argvalue0.Read(jsProt1699)
		if err1700 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1701 := (strconv.Atoi(flag.Arg(2)))
		if err1701 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err1702 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1702 != nil {
			Usage()
			return
		}
		value2 := finance.Amount(argvalue2)
		tmp3, err1703 := (strconv.Atoi(flag.Arg(4)))
		if err1703 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.UidInt(argvalue3)
		fmt.Print(client.SubmitPrePaidPaymentRequest(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "subtractPaymentRequest":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "SubtractPaymentRequest requires 7 args")
			flag.Usage()
		}
		arg1704 := flag.Arg(1)
		mbTrans1705 := thrift.NewTMemoryBufferLen(len(arg1704))
		defer mbTrans1705.Close()
		_, err1706 := mbTrans1705.WriteString(arg1704)
		if err1706 != nil {
			Usage()
			return
		}
		factory1707 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1708 := factory1707.GetProtocol(mbTrans1705)
		argvalue0 := finance.NewRequestHeader()
		err1709 := argvalue0.Read(jsProt1708)
		if err1709 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1710 := (strconv.Atoi(flag.Arg(2)))
		if err1710 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1711 := (strconv.Atoi(flag.Arg(3)))
		if err1711 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1712 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1712 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4, err1713 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1713 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		tmp5, err1714 := (strconv.Atoi(flag.Arg(6)))
		if err1714 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.UidInt(argvalue5)
		arg1715 := flag.Arg(7)
		mbTrans1716 := thrift.NewTMemoryBufferLen(len(arg1715))
		defer mbTrans1716.Close()
		_, err1717 := mbTrans1716.WriteString(arg1715)
		if err1717 != nil {
			Usage()
			return
		}
		factory1718 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1719 := factory1718.GetProtocol(mbTrans1716)
		containerStruct6 := finance.NewSubtractPaymentRequestArgs()
		err1720 := containerStruct6.ReadField7(jsProt1719)
		if err1720 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.ExtInfo
		value6 := argvalue6
		fmt.Print(client.SubtractPaymentRequest(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "launchPaymentRequest":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "LaunchPaymentRequest requires 7 args")
			flag.Usage()
		}
		arg1721 := flag.Arg(1)
		mbTrans1722 := thrift.NewTMemoryBufferLen(len(arg1721))
		defer mbTrans1722.Close()
		_, err1723 := mbTrans1722.WriteString(arg1721)
		if err1723 != nil {
			Usage()
			return
		}
		factory1724 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1725 := factory1724.GetProtocol(mbTrans1722)
		argvalue0 := finance.NewRequestHeader()
		err1726 := argvalue0.Read(jsProt1725)
		if err1726 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1727 := (strconv.Atoi(flag.Arg(2)))
		if err1727 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err1728 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err1728 != nil {
			Usage()
			return
		}
		value2 := finance.Amount(argvalue2)
		tmp3, err1729 := (strconv.Atoi(flag.Arg(4)))
		if err1729 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.IdInt(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5, err1731 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err1731 != nil {
			Usage()
			return
		}
		value5 := finance.TimeInt(argvalue5)
		tmp6, err := (strconv.Atoi(flag.Arg(7)))
		if err != nil {
			Usage()
			return
		}
		argvalue6 := finance.WithdrawType(tmp6)
		value6 := finance.WithdrawType(argvalue6)
		fmt.Print(client.LaunchPaymentRequest(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "completePaymentRequestNew":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "CompletePaymentRequestNew requires 6 args")
			flag.Usage()
		}
		arg1732 := flag.Arg(1)
		mbTrans1733 := thrift.NewTMemoryBufferLen(len(arg1732))
		defer mbTrans1733.Close()
		_, err1734 := mbTrans1733.WriteString(arg1732)
		if err1734 != nil {
			Usage()
			return
		}
		factory1735 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1736 := factory1735.GetProtocol(mbTrans1733)
		argvalue0 := finance.NewRequestHeader()
		err1737 := argvalue0.Read(jsProt1736)
		if err1737 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1738 := (strconv.Atoi(flag.Arg(2)))
		if err1738 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1739 := (strconv.Atoi(flag.Arg(3)))
		if err1739 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1740 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1740 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		tmp4, err1741 := (strconv.Atoi(flag.Arg(5)))
		if err1741 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		arg1742 := flag.Arg(6)
		mbTrans1743 := thrift.NewTMemoryBufferLen(len(arg1742))
		defer mbTrans1743.Close()
		_, err1744 := mbTrans1743.WriteString(arg1742)
		if err1744 != nil {
			Usage()
			return
		}
		factory1745 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1746 := factory1745.GetProtocol(mbTrans1743)
		containerStruct5 := finance.NewCompletePaymentRequestNewArgs()
		err1747 := containerStruct5.ReadField6(jsProt1746)
		if err1747 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.ExtInfo
		value5 := argvalue5
		fmt.Print(client.CompletePaymentRequestNew(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "denyPaymentRequestNew":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "DenyPaymentRequestNew requires 6 args")
			flag.Usage()
		}
		arg1748 := flag.Arg(1)
		mbTrans1749 := thrift.NewTMemoryBufferLen(len(arg1748))
		defer mbTrans1749.Close()
		_, err1750 := mbTrans1749.WriteString(arg1748)
		if err1750 != nil {
			Usage()
			return
		}
		factory1751 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1752 := factory1751.GetProtocol(mbTrans1749)
		argvalue0 := finance.NewRequestHeader()
		err1753 := argvalue0.Read(jsProt1752)
		if err1753 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1754 := (strconv.Atoi(flag.Arg(2)))
		if err1754 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1755 := (strconv.Atoi(flag.Arg(3)))
		if err1755 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1756 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1756 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		tmp4, err1757 := (strconv.Atoi(flag.Arg(5)))
		if err1757 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		arg1758 := flag.Arg(6)
		mbTrans1759 := thrift.NewTMemoryBufferLen(len(arg1758))
		defer mbTrans1759.Close()
		_, err1760 := mbTrans1759.WriteString(arg1758)
		if err1760 != nil {
			Usage()
			return
		}
		factory1761 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1762 := factory1761.GetProtocol(mbTrans1759)
		containerStruct5 := finance.NewDenyPaymentRequestNewArgs()
		err1763 := containerStruct5.ReadField6(jsProt1762)
		if err1763 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.ExtInfo
		value5 := argvalue5
		fmt.Print(client.DenyPaymentRequestNew(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "changeFinancialAccount":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ChangeFinancialAccount requires 7 args")
			flag.Usage()
		}
		arg1764 := flag.Arg(1)
		mbTrans1765 := thrift.NewTMemoryBufferLen(len(arg1764))
		defer mbTrans1765.Close()
		_, err1766 := mbTrans1765.WriteString(arg1764)
		if err1766 != nil {
			Usage()
			return
		}
		factory1767 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1768 := factory1767.GetProtocol(mbTrans1765)
		argvalue0 := finance.NewRequestHeader()
		err1769 := argvalue0.Read(jsProt1768)
		if err1769 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1770 := (strconv.Atoi(flag.Arg(2)))
		if err1770 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.AccountCategory(tmp2)
		value2 := finance.AccountCategory(argvalue2)
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		fmt.Print(client.ChangeFinancialAccount(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "submitNoPaymentRequest":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "SubmitNoPaymentRequest requires 6 args")
			flag.Usage()
		}
		arg1775 := flag.Arg(1)
		mbTrans1776 := thrift.NewTMemoryBufferLen(len(arg1775))
		defer mbTrans1776.Close()
		_, err1777 := mbTrans1776.WriteString(arg1775)
		if err1777 != nil {
			Usage()
			return
		}
		factory1778 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1779 := factory1778.GetProtocol(mbTrans1776)
		argvalue0 := finance.NewRequestHeader()
		err1780 := argvalue0.Read(jsProt1779)
		if err1780 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1781 := (strconv.Atoi(flag.Arg(2)))
		if err1781 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err1782 := (strconv.Atoi(flag.Arg(3)))
		if err1782 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1783 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1783 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		tmp4, err1784 := (strconv.Atoi(flag.Arg(5)))
		if err1784 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		arg1785 := flag.Arg(6)
		mbTrans1786 := thrift.NewTMemoryBufferLen(len(arg1785))
		defer mbTrans1786.Close()
		_, err1787 := mbTrans1786.WriteString(arg1785)
		if err1787 != nil {
			Usage()
			return
		}
		factory1788 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1789 := factory1788.GetProtocol(mbTrans1786)
		containerStruct5 := finance.NewSubmitNoPaymentRequestArgs()
		err1790 := containerStruct5.ReadField6(jsProt1789)
		if err1790 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.ExtInfo
		value5 := argvalue5
		fmt.Print(client.SubmitNoPaymentRequest(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "refundAppropriation":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "RefundAppropriation requires 5 args")
			flag.Usage()
		}
		arg1791 := flag.Arg(1)
		mbTrans1792 := thrift.NewTMemoryBufferLen(len(arg1791))
		defer mbTrans1792.Close()
		_, err1793 := mbTrans1792.WriteString(arg1791)
		if err1793 != nil {
			Usage()
			return
		}
		factory1794 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1795 := factory1794.GetProtocol(mbTrans1792)
		argvalue0 := finance.NewRequestHeader()
		err1796 := argvalue0.Read(jsProt1795)
		if err1796 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1797 := (strconv.Atoi(flag.Arg(2)))
		if err1797 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err1798 := (strconv.Atoi(flag.Arg(3)))
		if err1798 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		argvalue3, err1799 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1799 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		tmp4, err1800 := (strconv.Atoi(flag.Arg(5)))
		if err1800 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		fmt.Print(client.RefundAppropriation(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getAdPlanBudgetSummary":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdPlanBudgetSummary requires 2 args")
			flag.Usage()
		}
		arg1801 := flag.Arg(1)
		mbTrans1802 := thrift.NewTMemoryBufferLen(len(arg1801))
		defer mbTrans1802.Close()
		_, err1803 := mbTrans1802.WriteString(arg1801)
		if err1803 != nil {
			Usage()
			return
		}
		factory1804 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1805 := factory1804.GetProtocol(mbTrans1802)
		argvalue0 := finance.NewRequestHeader()
		err1806 := argvalue0.Read(jsProt1805)
		if err1806 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1807 := flag.Arg(2)
		mbTrans1808 := thrift.NewTMemoryBufferLen(len(arg1807))
		defer mbTrans1808.Close()
		_, err1809 := mbTrans1808.WriteString(arg1807)
		if err1809 != nil {
			Usage()
			return
		}
		factory1810 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1811 := factory1810.GetProtocol(mbTrans1808)
		containerStruct1 := finance.NewGetAdPlanBudgetSummaryArgs()
		err1812 := containerStruct1.ReadField2(jsProt1811)
		if err1812 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.PidList
		value1 := argvalue1
		fmt.Print(client.GetAdPlanBudgetSummary(value0, value1))
		fmt.Print("\n")
		break
	case "getAdStrategyBudgetSummary":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdStrategyBudgetSummary requires 2 args")
			flag.Usage()
		}
		arg1813 := flag.Arg(1)
		mbTrans1814 := thrift.NewTMemoryBufferLen(len(arg1813))
		defer mbTrans1814.Close()
		_, err1815 := mbTrans1814.WriteString(arg1813)
		if err1815 != nil {
			Usage()
			return
		}
		factory1816 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1817 := factory1816.GetProtocol(mbTrans1814)
		argvalue0 := finance.NewRequestHeader()
		err1818 := argvalue0.Read(jsProt1817)
		if err1818 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg1819 := flag.Arg(2)
		mbTrans1820 := thrift.NewTMemoryBufferLen(len(arg1819))
		defer mbTrans1820.Close()
		_, err1821 := mbTrans1820.WriteString(arg1819)
		if err1821 != nil {
			Usage()
			return
		}
		factory1822 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1823 := factory1822.GetProtocol(mbTrans1820)
		containerStruct1 := finance.NewGetAdStrategyBudgetSummaryArgs()
		err1824 := containerStruct1.ReadField2(jsProt1823)
		if err1824 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.SidList
		value1 := argvalue1
		fmt.Print(client.GetAdStrategyBudgetSummary(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
