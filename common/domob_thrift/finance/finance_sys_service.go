// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/finance_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = finance_types.GoUnusedProtection__

type FinanceSysService interface { //@Description("财务系统内部服务，仅针对财务系统内部的子模块开放")
	//{{{

	// #3.1 预结算
	// TODO
	// <li>由于是预结算，记录的 isPreSettle 必须为 true</li>
	// @return 成功后的记录数据
	// @throws FinanceServiceException TODO
	//
	// Parameters:
	//  - Header
	//  - Record: 预结算的记录
	PreSettle(header *common.RequestHeader, record *finance_types.SettleRecord) (r *finance_types.SettleRecord, fe *FinanceServiceException, err error)
	// #3.2 开始结算
	// 计费模块由此通知财务系统开始当日结算
	// @return void
	// @throws FinanceServiceException TODO
	//
	// Parameters:
	//  - Header
	//  - Time: 结算周期的开始时间，取结算周期当日零点
	//  - RecordCount: 预计的CPC结算记录数
	//  - CpmBatchRecordCount: 预计的CPM批量结算记录数
	StartSettlement(header *common.RequestHeader, time TimeInt, recordCount int32, cpmBatchRecordCount int32) (fe *FinanceServiceException, err error)
	// #3.3 结算
	// TODO
	// @return 成功后的记录数据
	// @throws FinanceServiceException TODO
	//
	// Parameters:
	//  - Header
	//  - Time: 结算周期的开始时间，取结算周期当日零点
	//  - Record: 结算的记录
	Settle(header *common.RequestHeader, time TimeInt, record *finance_types.SettleRecord) (r *finance_types.SettleRecord, fe *FinanceServiceException, err error)
	// #3.4 结束结算
	// 计费模块由此通知财务系统结束当日结算
	// @return void
	// @throws FinanceServiceException TODO
	//
	// Parameters:
	//  - Header
	//  - Time: 结算周期的开始时间，取结算周期当日零点
	//  - RecordCount: 实际的CPC结算记录数
	//  - CpmBatchRecordCount: 实际的CPM批量结算记录数
	FinishSettlement(header *common.RequestHeader, time TimeInt, recordCount int32, cpmBatchRecordCount int32) (fe *FinanceServiceException, err error)
	// #3.6 批量预结算
	// TODO
	// <li>由于是预结算，记录的 isPreSettle 必须为 true</li>
	// @return 成功后的记录数据
	// @throws FinanceServiceException TODO
	//
	// Parameters:
	//  - Header
	//  - Record: 批量预结算的记录
	BatchPreSettle(header *common.RequestHeader, record *finance_types.BatchSettleRecord) (r *finance_types.BatchSettleRecord, fe *FinanceServiceException, err error)
	// #3.7 批量结算
	// TODO
	// @return 成功后的记录数据
	// @throws FinanceServiceException TODO
	//
	// Parameters:
	//  - Header
	//  - Time: 结算周期的开始时间，取结算周期当日零点
	//  - Record: 批量结算的记录
	BatchSettle(header *common.RequestHeader, time TimeInt, record *finance_types.BatchSettleRecord) (r *finance_types.BatchSettleRecord, fe *FinanceServiceException, err error)
}

//@Description("财务系统内部服务，仅针对财务系统内部的子模块开放")
//{{{
type FinanceSysServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewFinanceSysServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *FinanceSysServiceClient {
	return &FinanceSysServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewFinanceSysServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *FinanceSysServiceClient {
	return &FinanceSysServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// #3.1 预结算
// TODO
// <li>由于是预结算，记录的 isPreSettle 必须为 true</li>
// @return 成功后的记录数据
// @throws FinanceServiceException TODO
//
// Parameters:
//  - Header
//  - Record: 预结算的记录
func (p *FinanceSysServiceClient) PreSettle(header *common.RequestHeader, record *finance_types.SettleRecord) (r *finance_types.SettleRecord, fe *FinanceServiceException, err error) {
	if err = p.sendPreSettle(header, record); err != nil {
		return
	}
	return p.recvPreSettle()
}

func (p *FinanceSysServiceClient) sendPreSettle(header *common.RequestHeader, record *finance_types.SettleRecord) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("preSettle", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args864 := NewPreSettleArgs()
	args864.Header = header
	args864.Record = record
	if err = args864.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceSysServiceClient) recvPreSettle() (value *finance_types.SettleRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error866 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error867 error
		error867, err = error866.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error867
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result865 := NewPreSettleResult()
	if err = result865.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result865.Success
	if result865.Fe != nil {
		fe = result865.Fe
	}
	return
}

// #3.2 开始结算
// 计费模块由此通知财务系统开始当日结算
// @return void
// @throws FinanceServiceException TODO
//
// Parameters:
//  - Header
//  - Time: 结算周期的开始时间，取结算周期当日零点
//  - RecordCount: 预计的CPC结算记录数
//  - CpmBatchRecordCount: 预计的CPM批量结算记录数
func (p *FinanceSysServiceClient) StartSettlement(header *common.RequestHeader, time TimeInt, recordCount int32, cpmBatchRecordCount int32) (fe *FinanceServiceException, err error) {
	if err = p.sendStartSettlement(header, time, recordCount, cpmBatchRecordCount); err != nil {
		return
	}
	return p.recvStartSettlement()
}

func (p *FinanceSysServiceClient) sendStartSettlement(header *common.RequestHeader, time TimeInt, recordCount int32, cpmBatchRecordCount int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("startSettlement", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args868 := NewStartSettlementArgs()
	args868.Header = header
	args868.Time = time
	args868.RecordCount = recordCount
	args868.CpmBatchRecordCount = cpmBatchRecordCount
	if err = args868.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceSysServiceClient) recvStartSettlement() (fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error870 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error871 error
		error871, err = error870.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error871
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result869 := NewStartSettlementResult()
	if err = result869.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result869.Fe != nil {
		fe = result869.Fe
	}
	return
}

// #3.3 结算
// TODO
// @return 成功后的记录数据
// @throws FinanceServiceException TODO
//
// Parameters:
//  - Header
//  - Time: 结算周期的开始时间，取结算周期当日零点
//  - Record: 结算的记录
func (p *FinanceSysServiceClient) Settle(header *common.RequestHeader, time TimeInt, record *finance_types.SettleRecord) (r *finance_types.SettleRecord, fe *FinanceServiceException, err error) {
	if err = p.sendSettle(header, time, record); err != nil {
		return
	}
	return p.recvSettle()
}

func (p *FinanceSysServiceClient) sendSettle(header *common.RequestHeader, time TimeInt, record *finance_types.SettleRecord) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("settle", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args872 := NewSettleArgs()
	args872.Header = header
	args872.Time = time
	args872.Record = record
	if err = args872.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceSysServiceClient) recvSettle() (value *finance_types.SettleRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error874 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error875 error
		error875, err = error874.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error875
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result873 := NewSettleResult()
	if err = result873.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result873.Success
	if result873.Fe != nil {
		fe = result873.Fe
	}
	return
}

// #3.4 结束结算
// 计费模块由此通知财务系统结束当日结算
// @return void
// @throws FinanceServiceException TODO
//
// Parameters:
//  - Header
//  - Time: 结算周期的开始时间，取结算周期当日零点
//  - RecordCount: 实际的CPC结算记录数
//  - CpmBatchRecordCount: 实际的CPM批量结算记录数
func (p *FinanceSysServiceClient) FinishSettlement(header *common.RequestHeader, time TimeInt, recordCount int32, cpmBatchRecordCount int32) (fe *FinanceServiceException, err error) {
	if err = p.sendFinishSettlement(header, time, recordCount, cpmBatchRecordCount); err != nil {
		return
	}
	return p.recvFinishSettlement()
}

func (p *FinanceSysServiceClient) sendFinishSettlement(header *common.RequestHeader, time TimeInt, recordCount int32, cpmBatchRecordCount int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("finishSettlement", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args876 := NewFinishSettlementArgs()
	args876.Header = header
	args876.Time = time
	args876.RecordCount = recordCount
	args876.CpmBatchRecordCount = cpmBatchRecordCount
	if err = args876.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceSysServiceClient) recvFinishSettlement() (fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error878 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error879 error
		error879, err = error878.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error879
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result877 := NewFinishSettlementResult()
	if err = result877.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result877.Fe != nil {
		fe = result877.Fe
	}
	return
}

// #3.6 批量预结算
// TODO
// <li>由于是预结算，记录的 isPreSettle 必须为 true</li>
// @return 成功后的记录数据
// @throws FinanceServiceException TODO
//
// Parameters:
//  - Header
//  - Record: 批量预结算的记录
func (p *FinanceSysServiceClient) BatchPreSettle(header *common.RequestHeader, record *finance_types.BatchSettleRecord) (r *finance_types.BatchSettleRecord, fe *FinanceServiceException, err error) {
	if err = p.sendBatchPreSettle(header, record); err != nil {
		return
	}
	return p.recvBatchPreSettle()
}

func (p *FinanceSysServiceClient) sendBatchPreSettle(header *common.RequestHeader, record *finance_types.BatchSettleRecord) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("batchPreSettle", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args880 := NewBatchPreSettleArgs()
	args880.Header = header
	args880.Record = record
	if err = args880.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceSysServiceClient) recvBatchPreSettle() (value *finance_types.BatchSettleRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error882 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error883 error
		error883, err = error882.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error883
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result881 := NewBatchPreSettleResult()
	if err = result881.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result881.Success
	if result881.Fe != nil {
		fe = result881.Fe
	}
	return
}

// #3.7 批量结算
// TODO
// @return 成功后的记录数据
// @throws FinanceServiceException TODO
//
// Parameters:
//  - Header
//  - Time: 结算周期的开始时间，取结算周期当日零点
//  - Record: 批量结算的记录
func (p *FinanceSysServiceClient) BatchSettle(header *common.RequestHeader, time TimeInt, record *finance_types.BatchSettleRecord) (r *finance_types.BatchSettleRecord, fe *FinanceServiceException, err error) {
	if err = p.sendBatchSettle(header, time, record); err != nil {
		return
	}
	return p.recvBatchSettle()
}

func (p *FinanceSysServiceClient) sendBatchSettle(header *common.RequestHeader, time TimeInt, record *finance_types.BatchSettleRecord) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("batchSettle", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args884 := NewBatchSettleArgs()
	args884.Header = header
	args884.Time = time
	args884.Record = record
	if err = args884.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FinanceSysServiceClient) recvBatchSettle() (value *finance_types.BatchSettleRecord, fe *FinanceServiceException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error886 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error887 error
		error887, err = error886.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error887
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result885 := NewBatchSettleResult()
	if err = result885.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result885.Success
	if result885.Fe != nil {
		fe = result885.Fe
	}
	return
}

type FinanceSysServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      FinanceSysService
}

func (p *FinanceSysServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *FinanceSysServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *FinanceSysServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewFinanceSysServiceProcessor(handler FinanceSysService) *FinanceSysServiceProcessor {

	self888 := &FinanceSysServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self888.processorMap["preSettle"] = &financeSysServiceProcessorPreSettle{handler: handler}
	self888.processorMap["startSettlement"] = &financeSysServiceProcessorStartSettlement{handler: handler}
	self888.processorMap["settle"] = &financeSysServiceProcessorSettle{handler: handler}
	self888.processorMap["finishSettlement"] = &financeSysServiceProcessorFinishSettlement{handler: handler}
	self888.processorMap["batchPreSettle"] = &financeSysServiceProcessorBatchPreSettle{handler: handler}
	self888.processorMap["batchSettle"] = &financeSysServiceProcessorBatchSettle{handler: handler}
	return self888
}

func (p *FinanceSysServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x889 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x889.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x889

}

type financeSysServiceProcessorPreSettle struct {
	handler FinanceSysService
}

func (p *financeSysServiceProcessorPreSettle) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPreSettleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("preSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPreSettleResult()
	if result.Success, result.Fe, err = p.handler.PreSettle(args.Header, args.Record); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing preSettle: "+err.Error())
		oprot.WriteMessageBegin("preSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("preSettle", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeSysServiceProcessorStartSettlement struct {
	handler FinanceSysService
}

func (p *financeSysServiceProcessorStartSettlement) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewStartSettlementArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("startSettlement", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewStartSettlementResult()
	if result.Fe, err = p.handler.StartSettlement(args.Header, args.Time, args.RecordCount, args.CpmBatchRecordCount); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing startSettlement: "+err.Error())
		oprot.WriteMessageBegin("startSettlement", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("startSettlement", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeSysServiceProcessorSettle struct {
	handler FinanceSysService
}

func (p *financeSysServiceProcessorSettle) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSettleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("settle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSettleResult()
	if result.Success, result.Fe, err = p.handler.Settle(args.Header, args.Time, args.Record); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing settle: "+err.Error())
		oprot.WriteMessageBegin("settle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("settle", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeSysServiceProcessorFinishSettlement struct {
	handler FinanceSysService
}

func (p *financeSysServiceProcessorFinishSettlement) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewFinishSettlementArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("finishSettlement", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewFinishSettlementResult()
	if result.Fe, err = p.handler.FinishSettlement(args.Header, args.Time, args.RecordCount, args.CpmBatchRecordCount); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing finishSettlement: "+err.Error())
		oprot.WriteMessageBegin("finishSettlement", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("finishSettlement", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeSysServiceProcessorBatchPreSettle struct {
	handler FinanceSysService
}

func (p *financeSysServiceProcessorBatchPreSettle) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewBatchPreSettleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("batchPreSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewBatchPreSettleResult()
	if result.Success, result.Fe, err = p.handler.BatchPreSettle(args.Header, args.Record); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchPreSettle: "+err.Error())
		oprot.WriteMessageBegin("batchPreSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("batchPreSettle", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type financeSysServiceProcessorBatchSettle struct {
	handler FinanceSysService
}

func (p *financeSysServiceProcessorBatchSettle) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewBatchSettleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("batchSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewBatchSettleResult()
	if result.Success, result.Fe, err = p.handler.BatchSettle(args.Header, args.Time, args.Record); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchSettle: "+err.Error())
		oprot.WriteMessageBegin("batchSettle", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("batchSettle", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type PreSettleArgs struct {
	Header *common.RequestHeader       `thrift:"header,1" json:"header"`
	Record *finance_types.SettleRecord `thrift:"record,2" json:"record"`
}

func NewPreSettleArgs() *PreSettleArgs {
	return &PreSettleArgs{}
}

func (p *PreSettleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PreSettleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PreSettleArgs) readField2(iprot thrift.TProtocol) error {
	p.Record = finance_types.NewSettleRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *PreSettleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("preSettle_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PreSettleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PreSettleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:record: %s", p, err)
		}
	}
	return err
}

func (p *PreSettleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PreSettleArgs(%+v)", *p)
}

type PreSettleResult struct {
	Success *finance_types.SettleRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException    `thrift:"fe,1" json:"fe"`
}

func NewPreSettleResult() *PreSettleResult {
	return &PreSettleResult{}
}

func (p *PreSettleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PreSettleResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewSettleRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PreSettleResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *PreSettleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("preSettle_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PreSettleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PreSettleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *PreSettleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PreSettleResult(%+v)", *p)
}

type StartSettlementArgs struct {
	Header              *common.RequestHeader `thrift:"header,1" json:"header"`
	Time                TimeInt               `thrift:"time,2" json:"time"`
	RecordCount         int32                 `thrift:"recordCount,3" json:"recordCount"`
	CpmBatchRecordCount int32                 `thrift:"cpmBatchRecordCount,4" json:"cpmBatchRecordCount"`
}

func NewStartSettlementArgs() *StartSettlementArgs {
	return &StartSettlementArgs{}
}

func (p *StartSettlementArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StartSettlementArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *StartSettlementArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *StartSettlementArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RecordCount = v
	}
	return nil
}

func (p *StartSettlementArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CpmBatchRecordCount = v
	}
	return nil
}

func (p *StartSettlementArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("startSettlement_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StartSettlementArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *StartSettlementArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:time: %s", p, err)
	}
	return err
}

func (p *StartSettlementArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("recordCount", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:recordCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RecordCount)); err != nil {
		return fmt.Errorf("%T.recordCount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:recordCount: %s", p, err)
	}
	return err
}

func (p *StartSettlementArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpmBatchRecordCount", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cpmBatchRecordCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CpmBatchRecordCount)); err != nil {
		return fmt.Errorf("%T.cpmBatchRecordCount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cpmBatchRecordCount: %s", p, err)
	}
	return err
}

func (p *StartSettlementArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StartSettlementArgs(%+v)", *p)
}

type StartSettlementResult struct {
	Fe *FinanceServiceException `thrift:"fe,1" json:"fe"`
}

func NewStartSettlementResult() *StartSettlementResult {
	return &StartSettlementResult{}
}

func (p *StartSettlementResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StartSettlementResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *StartSettlementResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("startSettlement_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StartSettlementResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *StartSettlementResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StartSettlementResult(%+v)", *p)
}

type SettleArgs struct {
	Header *common.RequestHeader       `thrift:"header,1" json:"header"`
	Time   TimeInt                     `thrift:"time,2" json:"time"`
	Record *finance_types.SettleRecord `thrift:"record,3" json:"record"`
}

func NewSettleArgs() *SettleArgs {
	return &SettleArgs{}
}

func (p *SettleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SettleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SettleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *SettleArgs) readField3(iprot thrift.TProtocol) error {
	p.Record = finance_types.NewSettleRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *SettleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("settle_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SettleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SettleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:time: %s", p, err)
	}
	return err
}

func (p *SettleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:record: %s", p, err)
		}
	}
	return err
}

func (p *SettleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SettleArgs(%+v)", *p)
}

type SettleResult struct {
	Success *finance_types.SettleRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException    `thrift:"fe,1" json:"fe"`
}

func NewSettleResult() *SettleResult {
	return &SettleResult{}
}

func (p *SettleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SettleResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewSettleRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SettleResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *SettleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("settle_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SettleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SettleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *SettleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SettleResult(%+v)", *p)
}

type FinishSettlementArgs struct {
	Header              *common.RequestHeader `thrift:"header,1" json:"header"`
	Time                TimeInt               `thrift:"time,2" json:"time"`
	RecordCount         int32                 `thrift:"recordCount,3" json:"recordCount"`
	CpmBatchRecordCount int32                 `thrift:"cpmBatchRecordCount,4" json:"cpmBatchRecordCount"`
}

func NewFinishSettlementArgs() *FinishSettlementArgs {
	return &FinishSettlementArgs{}
}

func (p *FinishSettlementArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinishSettlementArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *FinishSettlementArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *FinishSettlementArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.RecordCount = v
	}
	return nil
}

func (p *FinishSettlementArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CpmBatchRecordCount = v
	}
	return nil
}

func (p *FinishSettlementArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("finishSettlement_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinishSettlementArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *FinishSettlementArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:time: %s", p, err)
	}
	return err
}

func (p *FinishSettlementArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("recordCount", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:recordCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RecordCount)); err != nil {
		return fmt.Errorf("%T.recordCount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:recordCount: %s", p, err)
	}
	return err
}

func (p *FinishSettlementArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpmBatchRecordCount", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cpmBatchRecordCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CpmBatchRecordCount)); err != nil {
		return fmt.Errorf("%T.cpmBatchRecordCount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cpmBatchRecordCount: %s", p, err)
	}
	return err
}

func (p *FinishSettlementArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinishSettlementArgs(%+v)", *p)
}

type FinishSettlementResult struct {
	Fe *FinanceServiceException `thrift:"fe,1" json:"fe"`
}

func NewFinishSettlementResult() *FinishSettlementResult {
	return &FinishSettlementResult{}
}

func (p *FinishSettlementResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FinishSettlementResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *FinishSettlementResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("finishSettlement_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FinishSettlementResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *FinishSettlementResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FinishSettlementResult(%+v)", *p)
}

type BatchPreSettleArgs struct {
	Header *common.RequestHeader            `thrift:"header,1" json:"header"`
	Record *finance_types.BatchSettleRecord `thrift:"record,2" json:"record"`
}

func NewBatchPreSettleArgs() *BatchPreSettleArgs {
	return &BatchPreSettleArgs{}
}

func (p *BatchPreSettleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BatchPreSettleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *BatchPreSettleArgs) readField2(iprot thrift.TProtocol) error {
	p.Record = finance_types.NewBatchSettleRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *BatchPreSettleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("batchPreSettle_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BatchPreSettleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *BatchPreSettleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:record: %s", p, err)
		}
	}
	return err
}

func (p *BatchPreSettleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchPreSettleArgs(%+v)", *p)
}

type BatchPreSettleResult struct {
	Success *finance_types.BatchSettleRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException         `thrift:"fe,1" json:"fe"`
}

func NewBatchPreSettleResult() *BatchPreSettleResult {
	return &BatchPreSettleResult{}
}

func (p *BatchPreSettleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BatchPreSettleResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewBatchSettleRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *BatchPreSettleResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *BatchPreSettleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("batchPreSettle_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BatchPreSettleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *BatchPreSettleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *BatchPreSettleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchPreSettleResult(%+v)", *p)
}

type BatchSettleArgs struct {
	Header *common.RequestHeader            `thrift:"header,1" json:"header"`
	Time   TimeInt                          `thrift:"time,2" json:"time"`
	Record *finance_types.BatchSettleRecord `thrift:"record,3" json:"record"`
}

func NewBatchSettleArgs() *BatchSettleArgs {
	return &BatchSettleArgs{}
}

func (p *BatchSettleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BatchSettleArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *BatchSettleArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Time = TimeInt(v)
	}
	return nil
}

func (p *BatchSettleArgs) readField3(iprot thrift.TProtocol) error {
	p.Record = finance_types.NewBatchSettleRecord()
	if err := p.Record.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Record)
	}
	return nil
}

func (p *BatchSettleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("batchSettle_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BatchSettleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *BatchSettleArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:time: %s", p, err)
	}
	return err
}

func (p *BatchSettleArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Record != nil {
		if err := oprot.WriteFieldBegin("record", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:record: %s", p, err)
		}
		if err := p.Record.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Record)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:record: %s", p, err)
		}
	}
	return err
}

func (p *BatchSettleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchSettleArgs(%+v)", *p)
}

type BatchSettleResult struct {
	Success *finance_types.BatchSettleRecord `thrift:"success,0" json:"success"`
	Fe      *FinanceServiceException         `thrift:"fe,1" json:"fe"`
}

func NewBatchSettleResult() *BatchSettleResult {
	return &BatchSettleResult{}
}

func (p *BatchSettleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BatchSettleResult) readField0(iprot thrift.TProtocol) error {
	p.Success = finance_types.NewBatchSettleRecord()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *BatchSettleResult) readField1(iprot thrift.TProtocol) error {
	p.Fe = NewFinanceServiceException()
	if err := p.Fe.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fe)
	}
	return nil
}

func (p *BatchSettleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("batchSettle_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fe != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BatchSettleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *BatchSettleResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fe != nil {
		if err := oprot.WriteFieldBegin("fe", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fe: %s", p, err)
		}
		if err := p.Fe.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fe)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fe: %s", p, err)
		}
	}
	return err
}

func (p *BatchSettleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchSettleResult(%+v)", *p)
}
