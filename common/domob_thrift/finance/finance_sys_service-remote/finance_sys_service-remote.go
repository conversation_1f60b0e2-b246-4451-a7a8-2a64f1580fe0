// Autogenerated by <PERSON>hrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  SettleRecord preSettle(RequestHeader header, SettleRecord record)")
	fmt.Fprintln(os.Stderr, "  void startSettlement(RequestHeader header, TimeInt time, i32 recordCount, i32 cpmBatchRecordCount)")
	fmt.Fprintln(os.<PERSON>, "  SettleRecord settle(RequestHeader header, TimeInt time, SettleRecord record)")
	fmt.Fprintln(os.Stderr, "  void finishSettlement(RequestHeader header, TimeInt time, i32 recordCount, i32 cpmBatchRecordCount)")
	fmt.Fprintln(os.Stderr, "  BatchSettleRecord batchPreSettle(RequestHeader header, BatchSettleRecord record)")
	fmt.Fprintln(os.Stderr, "  BatchSettleRecord batchSettle(RequestHeader header, TimeInt time, BatchSettleRecord record)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := finance.NewFinanceSysServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "preSettle":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PreSettle requires 2 args")
			flag.Usage()
		}
		arg890 := flag.Arg(1)
		mbTrans891 := thrift.NewTMemoryBufferLen(len(arg890))
		defer mbTrans891.Close()
		_, err892 := mbTrans891.WriteString(arg890)
		if err892 != nil {
			Usage()
			return
		}
		factory893 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt894 := factory893.GetProtocol(mbTrans891)
		argvalue0 := finance.NewRequestHeader()
		err895 := argvalue0.Read(jsProt894)
		if err895 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg896 := flag.Arg(2)
		mbTrans897 := thrift.NewTMemoryBufferLen(len(arg896))
		defer mbTrans897.Close()
		_, err898 := mbTrans897.WriteString(arg896)
		if err898 != nil {
			Usage()
			return
		}
		factory899 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt900 := factory899.GetProtocol(mbTrans897)
		argvalue1 := finance.NewSettleRecord()
		err901 := argvalue1.Read(jsProt900)
		if err901 != nil {
			Usage()
			return
		}
		value1 := finance.SettleRecord(argvalue1)
		fmt.Print(client.PreSettle(value0, value1))
		fmt.Print("\n")
		break
	case "startSettlement":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "StartSettlement requires 4 args")
			flag.Usage()
		}
		arg902 := flag.Arg(1)
		mbTrans903 := thrift.NewTMemoryBufferLen(len(arg902))
		defer mbTrans903.Close()
		_, err904 := mbTrans903.WriteString(arg902)
		if err904 != nil {
			Usage()
			return
		}
		factory905 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt906 := factory905.GetProtocol(mbTrans903)
		argvalue0 := finance.NewRequestHeader()
		err907 := argvalue0.Read(jsProt906)
		if err907 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1, err908 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err908 != nil {
			Usage()
			return
		}
		value1 := finance.TimeInt(argvalue1)
		tmp2, err909 := (strconv.Atoi(flag.Arg(3)))
		if err909 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err910 := (strconv.Atoi(flag.Arg(4)))
		if err910 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.StartSettlement(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "settle":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "Settle requires 3 args")
			flag.Usage()
		}
		arg911 := flag.Arg(1)
		mbTrans912 := thrift.NewTMemoryBufferLen(len(arg911))
		defer mbTrans912.Close()
		_, err913 := mbTrans912.WriteString(arg911)
		if err913 != nil {
			Usage()
			return
		}
		factory914 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt915 := factory914.GetProtocol(mbTrans912)
		argvalue0 := finance.NewRequestHeader()
		err916 := argvalue0.Read(jsProt915)
		if err916 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1, err917 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err917 != nil {
			Usage()
			return
		}
		value1 := finance.TimeInt(argvalue1)
		arg918 := flag.Arg(3)
		mbTrans919 := thrift.NewTMemoryBufferLen(len(arg918))
		defer mbTrans919.Close()
		_, err920 := mbTrans919.WriteString(arg918)
		if err920 != nil {
			Usage()
			return
		}
		factory921 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt922 := factory921.GetProtocol(mbTrans919)
		argvalue2 := finance.NewSettleRecord()
		err923 := argvalue2.Read(jsProt922)
		if err923 != nil {
			Usage()
			return
		}
		value2 := finance.SettleRecord(argvalue2)
		fmt.Print(client.Settle(value0, value1, value2))
		fmt.Print("\n")
		break
	case "finishSettlement":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "FinishSettlement requires 4 args")
			flag.Usage()
		}
		arg924 := flag.Arg(1)
		mbTrans925 := thrift.NewTMemoryBufferLen(len(arg924))
		defer mbTrans925.Close()
		_, err926 := mbTrans925.WriteString(arg924)
		if err926 != nil {
			Usage()
			return
		}
		factory927 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt928 := factory927.GetProtocol(mbTrans925)
		argvalue0 := finance.NewRequestHeader()
		err929 := argvalue0.Read(jsProt928)
		if err929 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1, err930 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err930 != nil {
			Usage()
			return
		}
		value1 := finance.TimeInt(argvalue1)
		tmp2, err931 := (strconv.Atoi(flag.Arg(3)))
		if err931 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err932 := (strconv.Atoi(flag.Arg(4)))
		if err932 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.FinishSettlement(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "batchPreSettle":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "BatchPreSettle requires 2 args")
			flag.Usage()
		}
		arg933 := flag.Arg(1)
		mbTrans934 := thrift.NewTMemoryBufferLen(len(arg933))
		defer mbTrans934.Close()
		_, err935 := mbTrans934.WriteString(arg933)
		if err935 != nil {
			Usage()
			return
		}
		factory936 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt937 := factory936.GetProtocol(mbTrans934)
		argvalue0 := finance.NewRequestHeader()
		err938 := argvalue0.Read(jsProt937)
		if err938 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg939 := flag.Arg(2)
		mbTrans940 := thrift.NewTMemoryBufferLen(len(arg939))
		defer mbTrans940.Close()
		_, err941 := mbTrans940.WriteString(arg939)
		if err941 != nil {
			Usage()
			return
		}
		factory942 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt943 := factory942.GetProtocol(mbTrans940)
		argvalue1 := finance.NewBatchSettleRecord()
		err944 := argvalue1.Read(jsProt943)
		if err944 != nil {
			Usage()
			return
		}
		value1 := finance.BatchSettleRecord(argvalue1)
		fmt.Print(client.BatchPreSettle(value0, value1))
		fmt.Print("\n")
		break
	case "batchSettle":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "BatchSettle requires 3 args")
			flag.Usage()
		}
		arg945 := flag.Arg(1)
		mbTrans946 := thrift.NewTMemoryBufferLen(len(arg945))
		defer mbTrans946.Close()
		_, err947 := mbTrans946.WriteString(arg945)
		if err947 != nil {
			Usage()
			return
		}
		factory948 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt949 := factory948.GetProtocol(mbTrans946)
		argvalue0 := finance.NewRequestHeader()
		err950 := argvalue0.Read(jsProt949)
		if err950 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		argvalue1, err951 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err951 != nil {
			Usage()
			return
		}
		value1 := finance.TimeInt(argvalue1)
		arg952 := flag.Arg(3)
		mbTrans953 := thrift.NewTMemoryBufferLen(len(arg952))
		defer mbTrans953.Close()
		_, err954 := mbTrans953.WriteString(arg952)
		if err954 != nil {
			Usage()
			return
		}
		factory955 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt956 := factory955.GetProtocol(mbTrans953)
		argvalue2 := finance.NewBatchSettleRecord()
		err957 := argvalue2.Read(jsProt956)
		if err957 != nil {
			Usage()
			return
		}
		value2 := finance.BatchSettleRecord(argvalue2)
		fmt.Print(client.BatchSettle(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
