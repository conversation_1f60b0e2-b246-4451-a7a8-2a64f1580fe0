// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>r, "  PaymentRecordListResult listPaymentRecordEx(RequestHeader header, UidInt uid,  cooperators, TimeInt fromTime, TimeInt toTime,  statuses, i32 offset, i32 limit)")
	fmt.Fprintln(os.<PERSON><PERSON>, "   getPaymentRecordEx(RequestHeader header, UidInt uid,  ids)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord submitPaymentRequestEx(RequestHeader header, UidInt uid, CooperateCompany cooperator, Amount amount, TimeInt time, WithdrawType type)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord submitPaymentRequestExWithAutoConfirmation(RequestHeader header, UidInt uid, CooperateCompany cooperator, Amount amount, TimeInt time, WithdrawType type)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord confirmPaymentRequestEx(RequestHeader header, IdInt id, UidInt uid, CooperateCompany cooperator, Amount confirmedAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord denyPaymentRequestEx(RequestHeader header, IdInt id, UidInt uid, CooperateCompany cooperator, Amount requestedAmount, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "  PaymentRecord completePaymentRequestEx(RequestHeader header, IdInt id, UidInt uid, CooperateCompany cooperator, Amount paidAmount, Amount paymentFee, UidInt adminUid,  extInfo)")
	fmt.Fprintln(os.Stderr, "   listTeamMembersEx(RequestHeader header, UidInt uid, CooperateCompany cooperator)")
	fmt.Fprintln(os.Stderr, "  TeamMemberListResult listTeamMembersExForAdmin(RequestHeader header, CooperateCompany cooperator, UidInt uid,  statuses, QueryInt offset, QueryInt limit)")
	fmt.Fprintln(os.Stderr, "   listTeamMembersExByIdentity(RequestHeader header, IdentityType type,  identities, CooperateCompany cooperator)")
	fmt.Fprintln(os.Stderr, "   listTeamMembersExByName(RequestHeader header, CooperateCompany cooperator, string name, bool exactMatching)")
	fmt.Fprintln(os.Stderr, "   getTeamMembersExByIdList(RequestHeader header,  memberIdList)")
	fmt.Fprintln(os.Stderr, "  TeamMember passTeamMemberEx(RequestHeader header, CooperateCompany cooperator, UidInt uid, IdInt memberId, UidInt adminUid, string reason)")
	fmt.Fprintln(os.Stderr, "  TeamMember rejectTeamMemberEx(RequestHeader header, CooperateCompany cooperator, UidInt uid, IdInt memberId, UidInt adminUid, string reason)")
	fmt.Fprintln(os.Stderr, "  TeamMember forbidTeamMemberEx(RequestHeader header, CooperateCompany cooperator, UidInt uid, IdInt memberId, UidInt adminUid, string reason)")
	fmt.Fprintln(os.Stderr, "  TeamMember addTeamMemberEx(RequestHeader header, UidInt uid, CooperateCompany cooperator, TeamMember member)")
	fmt.Fprintln(os.Stderr, "  TeamMember updateTeamMemberEx(RequestHeader header, UidInt uid, CooperateCompany cooperator, TeamMember member, bool submit)")
	fmt.Fprintln(os.Stderr, "   submitTeamMembersEx(RequestHeader header, UidInt uid, CooperateCompany cooperator,  memberIdList)")
	fmt.Fprintln(os.Stderr, "  TeamMember removeTeamMemberEx(RequestHeader header, UidInt uid, CooperateCompany cooperator, IdInt memberId)")
	fmt.Fprintln(os.Stderr, "   updateTeamMemberSharingEx(RequestHeader header, UidInt uid, CooperateCompany cooperator,  sharingMap)")
	fmt.Fprintln(os.Stderr, "  FinancialAccount setBeneficiaryEx(RequestHeader header, UidInt uid, Beneficiary beneficiary)")
	fmt.Fprintln(os.Stderr, "   listIncomeConfirmationRecordEx(RequestHeader header, UidInt uid, TimeInt fromTime, TimeInt toTime,  status)")
	fmt.Fprintln(os.Stderr, "  FinancialAccount updateFinancialAccountBankInfo(RequestHeader header, FinancialAccount account)")
	fmt.Fprintln(os.Stderr, "   updatePaymentRecordBankInfo(RequestHeader header,  ids, FinancialAccount account)")
	fmt.Fprintln(os.Stderr, "  FinancialAccount resetFinancialAccount(RequestHeader header, UidInt uid, AccountCategory cate, Amount frozenAmount, string name, string identity)")
	fmt.Fprintln(os.Stderr, "  FinancialProfile markFinanceSettleType(RequestHeader header, UidInt uid, SettleType type)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := finance.NewFinanceDeveloperServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "listPaymentRecordEx":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "ListPaymentRecordEx requires 8 args")
			flag.Usage()
		}
		arg1961 := flag.Arg(1)
		mbTrans1962 := thrift.NewTMemoryBufferLen(len(arg1961))
		defer mbTrans1962.Close()
		_, err1963 := mbTrans1962.WriteString(arg1961)
		if err1963 != nil {
			Usage()
			return
		}
		factory1964 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1965 := factory1964.GetProtocol(mbTrans1962)
		argvalue0 := finance.NewRequestHeader()
		err1966 := argvalue0.Read(jsProt1965)
		if err1966 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1967 := (strconv.Atoi(flag.Arg(2)))
		if err1967 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg1968 := flag.Arg(3)
		mbTrans1969 := thrift.NewTMemoryBufferLen(len(arg1968))
		defer mbTrans1969.Close()
		_, err1970 := mbTrans1969.WriteString(arg1968)
		if err1970 != nil {
			Usage()
			return
		}
		factory1971 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1972 := factory1971.GetProtocol(mbTrans1969)
		containerStruct2 := finance.NewListPaymentRecordExArgs()
		err1973 := containerStruct2.ReadField3(jsProt1972)
		if err1973 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Cooperators
		value2 := argvalue2
		argvalue3, err1974 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err1974 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		argvalue4, err1975 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err1975 != nil {
			Usage()
			return
		}
		value4 := finance.TimeInt(argvalue4)
		arg1976 := flag.Arg(6)
		mbTrans1977 := thrift.NewTMemoryBufferLen(len(arg1976))
		defer mbTrans1977.Close()
		_, err1978 := mbTrans1977.WriteString(arg1976)
		if err1978 != nil {
			Usage()
			return
		}
		factory1979 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1980 := factory1979.GetProtocol(mbTrans1977)
		containerStruct5 := finance.NewListPaymentRecordExArgs()
		err1981 := containerStruct5.ReadField6(jsProt1980)
		if err1981 != nil {
			Usage()
			return
		}
		argvalue5 := containerStruct5.Statuses
		value5 := argvalue5
		tmp6, err1982 := (strconv.Atoi(flag.Arg(7)))
		if err1982 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		tmp7, err1983 := (strconv.Atoi(flag.Arg(8)))
		if err1983 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := argvalue7
		fmt.Print(client.ListPaymentRecordEx(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "getPaymentRecordEx":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetPaymentRecordEx requires 3 args")
			flag.Usage()
		}
		arg1984 := flag.Arg(1)
		mbTrans1985 := thrift.NewTMemoryBufferLen(len(arg1984))
		defer mbTrans1985.Close()
		_, err1986 := mbTrans1985.WriteString(arg1984)
		if err1986 != nil {
			Usage()
			return
		}
		factory1987 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1988 := factory1987.GetProtocol(mbTrans1985)
		argvalue0 := finance.NewRequestHeader()
		err1989 := argvalue0.Read(jsProt1988)
		if err1989 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err1990 := (strconv.Atoi(flag.Arg(2)))
		if err1990 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg1991 := flag.Arg(3)
		mbTrans1992 := thrift.NewTMemoryBufferLen(len(arg1991))
		defer mbTrans1992.Close()
		_, err1993 := mbTrans1992.WriteString(arg1991)
		if err1993 != nil {
			Usage()
			return
		}
		factory1994 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1995 := factory1994.GetProtocol(mbTrans1992)
		containerStruct2 := finance.NewGetPaymentRecordExArgs()
		err1996 := containerStruct2.ReadField3(jsProt1995)
		if err1996 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.GetPaymentRecordEx(value0, value1, value2))
		fmt.Print("\n")
		break
	case "submitPaymentRequestEx":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "SubmitPaymentRequestEx requires 6 args")
			flag.Usage()
		}
		arg1997 := flag.Arg(1)
		mbTrans1998 := thrift.NewTMemoryBufferLen(len(arg1997))
		defer mbTrans1998.Close()
		_, err1999 := mbTrans1998.WriteString(arg1997)
		if err1999 != nil {
			Usage()
			return
		}
		factory2000 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2001 := factory2000.GetProtocol(mbTrans1998)
		argvalue0 := finance.NewRequestHeader()
		err2002 := argvalue0.Read(jsProt2001)
		if err2002 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2003 := (strconv.Atoi(flag.Arg(2)))
		if err2003 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		argvalue3, err2004 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err2004 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4, err2005 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err2005 != nil {
			Usage()
			return
		}
		value4 := finance.TimeInt(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := finance.WithdrawType(tmp5)
		value5 := finance.WithdrawType(argvalue5)
		fmt.Print(client.SubmitPaymentRequestEx(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "submitPaymentRequestExWithAutoConfirmation":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "SubmitPaymentRequestExWithAutoConfirmation requires 6 args")
			flag.Usage()
		}
		arg2006 := flag.Arg(1)
		mbTrans2007 := thrift.NewTMemoryBufferLen(len(arg2006))
		defer mbTrans2007.Close()
		_, err2008 := mbTrans2007.WriteString(arg2006)
		if err2008 != nil {
			Usage()
			return
		}
		factory2009 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2010 := factory2009.GetProtocol(mbTrans2007)
		argvalue0 := finance.NewRequestHeader()
		err2011 := argvalue0.Read(jsProt2010)
		if err2011 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2012 := (strconv.Atoi(flag.Arg(2)))
		if err2012 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		argvalue3, err2013 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err2013 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4, err2014 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err2014 != nil {
			Usage()
			return
		}
		value4 := finance.TimeInt(argvalue4)
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := finance.WithdrawType(tmp5)
		value5 := finance.WithdrawType(argvalue5)
		fmt.Print(client.SubmitPaymentRequestExWithAutoConfirmation(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "confirmPaymentRequestEx":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "ConfirmPaymentRequestEx requires 7 args")
			flag.Usage()
		}
		arg2015 := flag.Arg(1)
		mbTrans2016 := thrift.NewTMemoryBufferLen(len(arg2015))
		defer mbTrans2016.Close()
		_, err2017 := mbTrans2016.WriteString(arg2015)
		if err2017 != nil {
			Usage()
			return
		}
		factory2018 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2019 := factory2018.GetProtocol(mbTrans2016)
		argvalue0 := finance.NewRequestHeader()
		err2020 := argvalue0.Read(jsProt2019)
		if err2020 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2021 := (strconv.Atoi(flag.Arg(2)))
		if err2021 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err2022 := (strconv.Atoi(flag.Arg(3)))
		if err2022 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := finance.CooperateCompany(tmp3)
		value3 := finance.CooperateCompany(argvalue3)
		argvalue4, err2023 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err2023 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		tmp5, err2024 := (strconv.Atoi(flag.Arg(6)))
		if err2024 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.UidInt(argvalue5)
		arg2025 := flag.Arg(7)
		mbTrans2026 := thrift.NewTMemoryBufferLen(len(arg2025))
		defer mbTrans2026.Close()
		_, err2027 := mbTrans2026.WriteString(arg2025)
		if err2027 != nil {
			Usage()
			return
		}
		factory2028 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2029 := factory2028.GetProtocol(mbTrans2026)
		containerStruct6 := finance.NewConfirmPaymentRequestExArgs()
		err2030 := containerStruct6.ReadField7(jsProt2029)
		if err2030 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.ExtInfo
		value6 := argvalue6
		fmt.Print(client.ConfirmPaymentRequestEx(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "denyPaymentRequestEx":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "DenyPaymentRequestEx requires 7 args")
			flag.Usage()
		}
		arg2031 := flag.Arg(1)
		mbTrans2032 := thrift.NewTMemoryBufferLen(len(arg2031))
		defer mbTrans2032.Close()
		_, err2033 := mbTrans2032.WriteString(arg2031)
		if err2033 != nil {
			Usage()
			return
		}
		factory2034 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2035 := factory2034.GetProtocol(mbTrans2032)
		argvalue0 := finance.NewRequestHeader()
		err2036 := argvalue0.Read(jsProt2035)
		if err2036 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2037 := (strconv.Atoi(flag.Arg(2)))
		if err2037 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err2038 := (strconv.Atoi(flag.Arg(3)))
		if err2038 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := finance.CooperateCompany(tmp3)
		value3 := finance.CooperateCompany(argvalue3)
		argvalue4, err2039 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err2039 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		tmp5, err2040 := (strconv.Atoi(flag.Arg(6)))
		if err2040 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.UidInt(argvalue5)
		arg2041 := flag.Arg(7)
		mbTrans2042 := thrift.NewTMemoryBufferLen(len(arg2041))
		defer mbTrans2042.Close()
		_, err2043 := mbTrans2042.WriteString(arg2041)
		if err2043 != nil {
			Usage()
			return
		}
		factory2044 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2045 := factory2044.GetProtocol(mbTrans2042)
		containerStruct6 := finance.NewDenyPaymentRequestExArgs()
		err2046 := containerStruct6.ReadField7(jsProt2045)
		if err2046 != nil {
			Usage()
			return
		}
		argvalue6 := containerStruct6.ExtInfo
		value6 := argvalue6
		fmt.Print(client.DenyPaymentRequestEx(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "completePaymentRequestEx":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "CompletePaymentRequestEx requires 8 args")
			flag.Usage()
		}
		arg2047 := flag.Arg(1)
		mbTrans2048 := thrift.NewTMemoryBufferLen(len(arg2047))
		defer mbTrans2048.Close()
		_, err2049 := mbTrans2048.WriteString(arg2047)
		if err2049 != nil {
			Usage()
			return
		}
		factory2050 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2051 := factory2050.GetProtocol(mbTrans2048)
		argvalue0 := finance.NewRequestHeader()
		err2052 := argvalue0.Read(jsProt2051)
		if err2052 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2053 := (strconv.Atoi(flag.Arg(2)))
		if err2053 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.IdInt(argvalue1)
		tmp2, err2054 := (strconv.Atoi(flag.Arg(3)))
		if err2054 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := finance.CooperateCompany(tmp3)
		value3 := finance.CooperateCompany(argvalue3)
		argvalue4, err2055 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err2055 != nil {
			Usage()
			return
		}
		value4 := finance.Amount(argvalue4)
		argvalue5, err2056 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err2056 != nil {
			Usage()
			return
		}
		value5 := finance.Amount(argvalue5)
		tmp6, err2057 := (strconv.Atoi(flag.Arg(7)))
		if err2057 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := finance.UidInt(argvalue6)
		arg2058 := flag.Arg(8)
		mbTrans2059 := thrift.NewTMemoryBufferLen(len(arg2058))
		defer mbTrans2059.Close()
		_, err2060 := mbTrans2059.WriteString(arg2058)
		if err2060 != nil {
			Usage()
			return
		}
		factory2061 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2062 := factory2061.GetProtocol(mbTrans2059)
		containerStruct7 := finance.NewCompletePaymentRequestExArgs()
		err2063 := containerStruct7.ReadField8(jsProt2062)
		if err2063 != nil {
			Usage()
			return
		}
		argvalue7 := containerStruct7.ExtInfo
		value7 := argvalue7
		fmt.Print(client.CompletePaymentRequestEx(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "listTeamMembersEx":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListTeamMembersEx requires 3 args")
			flag.Usage()
		}
		arg2064 := flag.Arg(1)
		mbTrans2065 := thrift.NewTMemoryBufferLen(len(arg2064))
		defer mbTrans2065.Close()
		_, err2066 := mbTrans2065.WriteString(arg2064)
		if err2066 != nil {
			Usage()
			return
		}
		factory2067 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2068 := factory2067.GetProtocol(mbTrans2065)
		argvalue0 := finance.NewRequestHeader()
		err2069 := argvalue0.Read(jsProt2068)
		if err2069 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2070 := (strconv.Atoi(flag.Arg(2)))
		if err2070 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		fmt.Print(client.ListTeamMembersEx(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listTeamMembersExForAdmin":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ListTeamMembersExForAdmin requires 6 args")
			flag.Usage()
		}
		arg2071 := flag.Arg(1)
		mbTrans2072 := thrift.NewTMemoryBufferLen(len(arg2071))
		defer mbTrans2072.Close()
		_, err2073 := mbTrans2072.WriteString(arg2071)
		if err2073 != nil {
			Usage()
			return
		}
		factory2074 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2075 := factory2074.GetProtocol(mbTrans2072)
		argvalue0 := finance.NewRequestHeader()
		err2076 := argvalue0.Read(jsProt2075)
		if err2076 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.CooperateCompany(tmp1)
		value1 := finance.CooperateCompany(argvalue1)
		tmp2, err2077 := (strconv.Atoi(flag.Arg(3)))
		if err2077 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		arg2078 := flag.Arg(4)
		mbTrans2079 := thrift.NewTMemoryBufferLen(len(arg2078))
		defer mbTrans2079.Close()
		_, err2080 := mbTrans2079.WriteString(arg2078)
		if err2080 != nil {
			Usage()
			return
		}
		factory2081 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2082 := factory2081.GetProtocol(mbTrans2079)
		containerStruct3 := finance.NewListTeamMembersExForAdminArgs()
		err2083 := containerStruct3.ReadField4(jsProt2082)
		if err2083 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Statuses
		value3 := argvalue3
		tmp4, err2084 := (strconv.Atoi(flag.Arg(5)))
		if err2084 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.QueryInt(argvalue4)
		tmp5, err2085 := (strconv.Atoi(flag.Arg(6)))
		if err2085 != nil {
			Usage()
			return
		}
		argvalue5 := int32(tmp5)
		value5 := finance.QueryInt(argvalue5)
		fmt.Print(client.ListTeamMembersExForAdmin(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "listTeamMembersExByIdentity":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListTeamMembersExByIdentity requires 4 args")
			flag.Usage()
		}
		arg2086 := flag.Arg(1)
		mbTrans2087 := thrift.NewTMemoryBufferLen(len(arg2086))
		defer mbTrans2087.Close()
		_, err2088 := mbTrans2087.WriteString(arg2086)
		if err2088 != nil {
			Usage()
			return
		}
		factory2089 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2090 := factory2089.GetProtocol(mbTrans2087)
		argvalue0 := finance.NewRequestHeader()
		err2091 := argvalue0.Read(jsProt2090)
		if err2091 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.IdentityType(tmp1)
		value1 := finance.IdentityType(argvalue1)
		arg2092 := flag.Arg(3)
		mbTrans2093 := thrift.NewTMemoryBufferLen(len(arg2092))
		defer mbTrans2093.Close()
		_, err2094 := mbTrans2093.WriteString(arg2092)
		if err2094 != nil {
			Usage()
			return
		}
		factory2095 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2096 := factory2095.GetProtocol(mbTrans2093)
		containerStruct2 := finance.NewListTeamMembersExByIdentityArgs()
		err2097 := containerStruct2.ReadField3(jsProt2096)
		if err2097 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Identities
		value2 := argvalue2
		tmp3, err := (strconv.Atoi(flag.Arg(4)))
		if err != nil {
			Usage()
			return
		}
		argvalue3 := finance.CooperateCompany(tmp3)
		value3 := finance.CooperateCompany(argvalue3)
		fmt.Print(client.ListTeamMembersExByIdentity(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "listTeamMembersExByName":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ListTeamMembersExByName requires 4 args")
			flag.Usage()
		}
		arg2098 := flag.Arg(1)
		mbTrans2099 := thrift.NewTMemoryBufferLen(len(arg2098))
		defer mbTrans2099.Close()
		_, err2100 := mbTrans2099.WriteString(arg2098)
		if err2100 != nil {
			Usage()
			return
		}
		factory2101 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2102 := factory2101.GetProtocol(mbTrans2099)
		argvalue0 := finance.NewRequestHeader()
		err2103 := argvalue0.Read(jsProt2102)
		if err2103 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.CooperateCompany(tmp1)
		value1 := finance.CooperateCompany(argvalue1)
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		argvalue3 := flag.Arg(4) == "true"
		value3 := argvalue3
		fmt.Print(client.ListTeamMembersExByName(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getTeamMembersExByIdList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTeamMembersExByIdList requires 2 args")
			flag.Usage()
		}
		arg2106 := flag.Arg(1)
		mbTrans2107 := thrift.NewTMemoryBufferLen(len(arg2106))
		defer mbTrans2107.Close()
		_, err2108 := mbTrans2107.WriteString(arg2106)
		if err2108 != nil {
			Usage()
			return
		}
		factory2109 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2110 := factory2109.GetProtocol(mbTrans2107)
		argvalue0 := finance.NewRequestHeader()
		err2111 := argvalue0.Read(jsProt2110)
		if err2111 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg2112 := flag.Arg(2)
		mbTrans2113 := thrift.NewTMemoryBufferLen(len(arg2112))
		defer mbTrans2113.Close()
		_, err2114 := mbTrans2113.WriteString(arg2112)
		if err2114 != nil {
			Usage()
			return
		}
		factory2115 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2116 := factory2115.GetProtocol(mbTrans2113)
		containerStruct1 := finance.NewGetTeamMembersExByIdListArgs()
		err2117 := containerStruct1.ReadField2(jsProt2116)
		if err2117 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.MemberIdList
		value1 := argvalue1
		fmt.Print(client.GetTeamMembersExByIdList(value0, value1))
		fmt.Print("\n")
		break
	case "passTeamMemberEx":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "PassTeamMemberEx requires 6 args")
			flag.Usage()
		}
		arg2118 := flag.Arg(1)
		mbTrans2119 := thrift.NewTMemoryBufferLen(len(arg2118))
		defer mbTrans2119.Close()
		_, err2120 := mbTrans2119.WriteString(arg2118)
		if err2120 != nil {
			Usage()
			return
		}
		factory2121 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2122 := factory2121.GetProtocol(mbTrans2119)
		argvalue0 := finance.NewRequestHeader()
		err2123 := argvalue0.Read(jsProt2122)
		if err2123 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.CooperateCompany(tmp1)
		value1 := finance.CooperateCompany(argvalue1)
		tmp2, err2124 := (strconv.Atoi(flag.Arg(3)))
		if err2124 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		tmp3, err2125 := (strconv.Atoi(flag.Arg(4)))
		if err2125 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.IdInt(argvalue3)
		tmp4, err2126 := (strconv.Atoi(flag.Arg(5)))
		if err2126 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.PassTeamMemberEx(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "rejectTeamMemberEx":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "RejectTeamMemberEx requires 6 args")
			flag.Usage()
		}
		arg2128 := flag.Arg(1)
		mbTrans2129 := thrift.NewTMemoryBufferLen(len(arg2128))
		defer mbTrans2129.Close()
		_, err2130 := mbTrans2129.WriteString(arg2128)
		if err2130 != nil {
			Usage()
			return
		}
		factory2131 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2132 := factory2131.GetProtocol(mbTrans2129)
		argvalue0 := finance.NewRequestHeader()
		err2133 := argvalue0.Read(jsProt2132)
		if err2133 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.CooperateCompany(tmp1)
		value1 := finance.CooperateCompany(argvalue1)
		tmp2, err2134 := (strconv.Atoi(flag.Arg(3)))
		if err2134 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		tmp3, err2135 := (strconv.Atoi(flag.Arg(4)))
		if err2135 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.IdInt(argvalue3)
		tmp4, err2136 := (strconv.Atoi(flag.Arg(5)))
		if err2136 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.RejectTeamMemberEx(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "forbidTeamMemberEx":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ForbidTeamMemberEx requires 6 args")
			flag.Usage()
		}
		arg2138 := flag.Arg(1)
		mbTrans2139 := thrift.NewTMemoryBufferLen(len(arg2138))
		defer mbTrans2139.Close()
		_, err2140 := mbTrans2139.WriteString(arg2138)
		if err2140 != nil {
			Usage()
			return
		}
		factory2141 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2142 := factory2141.GetProtocol(mbTrans2139)
		argvalue0 := finance.NewRequestHeader()
		err2143 := argvalue0.Read(jsProt2142)
		if err2143 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err := (strconv.Atoi(flag.Arg(2)))
		if err != nil {
			Usage()
			return
		}
		argvalue1 := finance.CooperateCompany(tmp1)
		value1 := finance.CooperateCompany(argvalue1)
		tmp2, err2144 := (strconv.Atoi(flag.Arg(3)))
		if err2144 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := finance.UidInt(argvalue2)
		tmp3, err2145 := (strconv.Atoi(flag.Arg(4)))
		if err2145 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.IdInt(argvalue3)
		tmp4, err2146 := (strconv.Atoi(flag.Arg(5)))
		if err2146 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := finance.UidInt(argvalue4)
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.ForbidTeamMemberEx(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "addTeamMemberEx":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "AddTeamMemberEx requires 4 args")
			flag.Usage()
		}
		arg2148 := flag.Arg(1)
		mbTrans2149 := thrift.NewTMemoryBufferLen(len(arg2148))
		defer mbTrans2149.Close()
		_, err2150 := mbTrans2149.WriteString(arg2148)
		if err2150 != nil {
			Usage()
			return
		}
		factory2151 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2152 := factory2151.GetProtocol(mbTrans2149)
		argvalue0 := finance.NewRequestHeader()
		err2153 := argvalue0.Read(jsProt2152)
		if err2153 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2154 := (strconv.Atoi(flag.Arg(2)))
		if err2154 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		arg2155 := flag.Arg(4)
		mbTrans2156 := thrift.NewTMemoryBufferLen(len(arg2155))
		defer mbTrans2156.Close()
		_, err2157 := mbTrans2156.WriteString(arg2155)
		if err2157 != nil {
			Usage()
			return
		}
		factory2158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2159 := factory2158.GetProtocol(mbTrans2156)
		argvalue3 := finance.NewTeamMember()
		err2160 := argvalue3.Read(jsProt2159)
		if err2160 != nil {
			Usage()
			return
		}
		value3 := finance.TeamMember(argvalue3)
		fmt.Print(client.AddTeamMemberEx(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updateTeamMemberEx":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "UpdateTeamMemberEx requires 5 args")
			flag.Usage()
		}
		arg2161 := flag.Arg(1)
		mbTrans2162 := thrift.NewTMemoryBufferLen(len(arg2161))
		defer mbTrans2162.Close()
		_, err2163 := mbTrans2162.WriteString(arg2161)
		if err2163 != nil {
			Usage()
			return
		}
		factory2164 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2165 := factory2164.GetProtocol(mbTrans2162)
		argvalue0 := finance.NewRequestHeader()
		err2166 := argvalue0.Read(jsProt2165)
		if err2166 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2167 := (strconv.Atoi(flag.Arg(2)))
		if err2167 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		arg2168 := flag.Arg(4)
		mbTrans2169 := thrift.NewTMemoryBufferLen(len(arg2168))
		defer mbTrans2169.Close()
		_, err2170 := mbTrans2169.WriteString(arg2168)
		if err2170 != nil {
			Usage()
			return
		}
		factory2171 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2172 := factory2171.GetProtocol(mbTrans2169)
		argvalue3 := finance.NewTeamMember()
		err2173 := argvalue3.Read(jsProt2172)
		if err2173 != nil {
			Usage()
			return
		}
		value3 := finance.TeamMember(argvalue3)
		argvalue4 := flag.Arg(5) == "true"
		value4 := argvalue4
		fmt.Print(client.UpdateTeamMemberEx(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "submitTeamMembersEx":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SubmitTeamMembersEx requires 4 args")
			flag.Usage()
		}
		arg2175 := flag.Arg(1)
		mbTrans2176 := thrift.NewTMemoryBufferLen(len(arg2175))
		defer mbTrans2176.Close()
		_, err2177 := mbTrans2176.WriteString(arg2175)
		if err2177 != nil {
			Usage()
			return
		}
		factory2178 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2179 := factory2178.GetProtocol(mbTrans2176)
		argvalue0 := finance.NewRequestHeader()
		err2180 := argvalue0.Read(jsProt2179)
		if err2180 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2181 := (strconv.Atoi(flag.Arg(2)))
		if err2181 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		arg2182 := flag.Arg(4)
		mbTrans2183 := thrift.NewTMemoryBufferLen(len(arg2182))
		defer mbTrans2183.Close()
		_, err2184 := mbTrans2183.WriteString(arg2182)
		if err2184 != nil {
			Usage()
			return
		}
		factory2185 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2186 := factory2185.GetProtocol(mbTrans2183)
		containerStruct3 := finance.NewSubmitTeamMembersExArgs()
		err2187 := containerStruct3.ReadField4(jsProt2186)
		if err2187 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.MemberIdList
		value3 := argvalue3
		fmt.Print(client.SubmitTeamMembersEx(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "removeTeamMemberEx":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "RemoveTeamMemberEx requires 4 args")
			flag.Usage()
		}
		arg2188 := flag.Arg(1)
		mbTrans2189 := thrift.NewTMemoryBufferLen(len(arg2188))
		defer mbTrans2189.Close()
		_, err2190 := mbTrans2189.WriteString(arg2188)
		if err2190 != nil {
			Usage()
			return
		}
		factory2191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2192 := factory2191.GetProtocol(mbTrans2189)
		argvalue0 := finance.NewRequestHeader()
		err2193 := argvalue0.Read(jsProt2192)
		if err2193 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2194 := (strconv.Atoi(flag.Arg(2)))
		if err2194 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		tmp3, err2195 := (strconv.Atoi(flag.Arg(4)))
		if err2195 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := finance.IdInt(argvalue3)
		fmt.Print(client.RemoveTeamMemberEx(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updateTeamMemberSharingEx":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "UpdateTeamMemberSharingEx requires 4 args")
			flag.Usage()
		}
		arg2196 := flag.Arg(1)
		mbTrans2197 := thrift.NewTMemoryBufferLen(len(arg2196))
		defer mbTrans2197.Close()
		_, err2198 := mbTrans2197.WriteString(arg2196)
		if err2198 != nil {
			Usage()
			return
		}
		factory2199 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2200 := factory2199.GetProtocol(mbTrans2197)
		argvalue0 := finance.NewRequestHeader()
		err2201 := argvalue0.Read(jsProt2200)
		if err2201 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2202 := (strconv.Atoi(flag.Arg(2)))
		if err2202 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.CooperateCompany(tmp2)
		value2 := finance.CooperateCompany(argvalue2)
		arg2203 := flag.Arg(4)
		mbTrans2204 := thrift.NewTMemoryBufferLen(len(arg2203))
		defer mbTrans2204.Close()
		_, err2205 := mbTrans2204.WriteString(arg2203)
		if err2205 != nil {
			Usage()
			return
		}
		factory2206 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2207 := factory2206.GetProtocol(mbTrans2204)
		containerStruct3 := finance.NewUpdateTeamMemberSharingExArgs()
		err2208 := containerStruct3.ReadField4(jsProt2207)
		if err2208 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.SharingMap
		value3 := argvalue3
		fmt.Print(client.UpdateTeamMemberSharingEx(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "setBeneficiaryEx":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SetBeneficiaryEx requires 3 args")
			flag.Usage()
		}
		arg2209 := flag.Arg(1)
		mbTrans2210 := thrift.NewTMemoryBufferLen(len(arg2209))
		defer mbTrans2210.Close()
		_, err2211 := mbTrans2210.WriteString(arg2209)
		if err2211 != nil {
			Usage()
			return
		}
		factory2212 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2213 := factory2212.GetProtocol(mbTrans2210)
		argvalue0 := finance.NewRequestHeader()
		err2214 := argvalue0.Read(jsProt2213)
		if err2214 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2215 := (strconv.Atoi(flag.Arg(2)))
		if err2215 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		arg2216 := flag.Arg(3)
		mbTrans2217 := thrift.NewTMemoryBufferLen(len(arg2216))
		defer mbTrans2217.Close()
		_, err2218 := mbTrans2217.WriteString(arg2216)
		if err2218 != nil {
			Usage()
			return
		}
		factory2219 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2220 := factory2219.GetProtocol(mbTrans2217)
		argvalue2 := finance.NewBeneficiary()
		err2221 := argvalue2.Read(jsProt2220)
		if err2221 != nil {
			Usage()
			return
		}
		value2 := finance.Beneficiary(argvalue2)
		fmt.Print(client.SetBeneficiaryEx(value0, value1, value2))
		fmt.Print("\n")
		break
	case "listIncomeConfirmationRecordEx":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ListIncomeConfirmationRecordEx requires 5 args")
			flag.Usage()
		}
		arg2222 := flag.Arg(1)
		mbTrans2223 := thrift.NewTMemoryBufferLen(len(arg2222))
		defer mbTrans2223.Close()
		_, err2224 := mbTrans2223.WriteString(arg2222)
		if err2224 != nil {
			Usage()
			return
		}
		factory2225 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2226 := factory2225.GetProtocol(mbTrans2223)
		argvalue0 := finance.NewRequestHeader()
		err2227 := argvalue0.Read(jsProt2226)
		if err2227 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2228 := (strconv.Atoi(flag.Arg(2)))
		if err2228 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		argvalue2, err2229 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err2229 != nil {
			Usage()
			return
		}
		value2 := finance.TimeInt(argvalue2)
		argvalue3, err2230 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err2230 != nil {
			Usage()
			return
		}
		value3 := finance.TimeInt(argvalue3)
		arg2231 := flag.Arg(5)
		mbTrans2232 := thrift.NewTMemoryBufferLen(len(arg2231))
		defer mbTrans2232.Close()
		_, err2233 := mbTrans2232.WriteString(arg2231)
		if err2233 != nil {
			Usage()
			return
		}
		factory2234 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2235 := factory2234.GetProtocol(mbTrans2232)
		containerStruct4 := finance.NewListIncomeConfirmationRecordExArgs()
		err2236 := containerStruct4.ReadField5(jsProt2235)
		if err2236 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Status
		value4 := argvalue4
		fmt.Print(client.ListIncomeConfirmationRecordEx(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "updateFinancialAccountBankInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UpdateFinancialAccountBankInfo requires 2 args")
			flag.Usage()
		}
		arg2237 := flag.Arg(1)
		mbTrans2238 := thrift.NewTMemoryBufferLen(len(arg2237))
		defer mbTrans2238.Close()
		_, err2239 := mbTrans2238.WriteString(arg2237)
		if err2239 != nil {
			Usage()
			return
		}
		factory2240 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2241 := factory2240.GetProtocol(mbTrans2238)
		argvalue0 := finance.NewRequestHeader()
		err2242 := argvalue0.Read(jsProt2241)
		if err2242 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg2243 := flag.Arg(2)
		mbTrans2244 := thrift.NewTMemoryBufferLen(len(arg2243))
		defer mbTrans2244.Close()
		_, err2245 := mbTrans2244.WriteString(arg2243)
		if err2245 != nil {
			Usage()
			return
		}
		factory2246 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2247 := factory2246.GetProtocol(mbTrans2244)
		argvalue1 := finance.NewFinancialAccount()
		err2248 := argvalue1.Read(jsProt2247)
		if err2248 != nil {
			Usage()
			return
		}
		value1 := finance.FinancialAccount(argvalue1)
		fmt.Print(client.UpdateFinancialAccountBankInfo(value0, value1))
		fmt.Print("\n")
		break
	case "updatePaymentRecordBankInfo":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdatePaymentRecordBankInfo requires 3 args")
			flag.Usage()
		}
		arg2249 := flag.Arg(1)
		mbTrans2250 := thrift.NewTMemoryBufferLen(len(arg2249))
		defer mbTrans2250.Close()
		_, err2251 := mbTrans2250.WriteString(arg2249)
		if err2251 != nil {
			Usage()
			return
		}
		factory2252 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2253 := factory2252.GetProtocol(mbTrans2250)
		argvalue0 := finance.NewRequestHeader()
		err2254 := argvalue0.Read(jsProt2253)
		if err2254 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		arg2255 := flag.Arg(2)
		mbTrans2256 := thrift.NewTMemoryBufferLen(len(arg2255))
		defer mbTrans2256.Close()
		_, err2257 := mbTrans2256.WriteString(arg2255)
		if err2257 != nil {
			Usage()
			return
		}
		factory2258 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2259 := factory2258.GetProtocol(mbTrans2256)
		containerStruct1 := finance.NewUpdatePaymentRecordBankInfoArgs()
		err2260 := containerStruct1.ReadField2(jsProt2259)
		if err2260 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		arg2261 := flag.Arg(3)
		mbTrans2262 := thrift.NewTMemoryBufferLen(len(arg2261))
		defer mbTrans2262.Close()
		_, err2263 := mbTrans2262.WriteString(arg2261)
		if err2263 != nil {
			Usage()
			return
		}
		factory2264 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2265 := factory2264.GetProtocol(mbTrans2262)
		argvalue2 := finance.NewFinancialAccount()
		err2266 := argvalue2.Read(jsProt2265)
		if err2266 != nil {
			Usage()
			return
		}
		value2 := finance.FinancialAccount(argvalue2)
		fmt.Print(client.UpdatePaymentRecordBankInfo(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resetFinancialAccount":
		if flag.NArg()-1 != 6 {
			fmt.Fprintln(os.Stderr, "ResetFinancialAccount requires 6 args")
			flag.Usage()
		}
		arg2267 := flag.Arg(1)
		mbTrans2268 := thrift.NewTMemoryBufferLen(len(arg2267))
		defer mbTrans2268.Close()
		_, err2269 := mbTrans2268.WriteString(arg2267)
		if err2269 != nil {
			Usage()
			return
		}
		factory2270 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2271 := factory2270.GetProtocol(mbTrans2268)
		argvalue0 := finance.NewRequestHeader()
		err2272 := argvalue0.Read(jsProt2271)
		if err2272 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2273 := (strconv.Atoi(flag.Arg(2)))
		if err2273 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.AccountCategory(tmp2)
		value2 := finance.AccountCategory(argvalue2)
		argvalue3, err2274 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err2274 != nil {
			Usage()
			return
		}
		value3 := finance.Amount(argvalue3)
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		argvalue5 := flag.Arg(6)
		value5 := argvalue5
		fmt.Print(client.ResetFinancialAccount(value0, value1, value2, value3, value4, value5))
		fmt.Print("\n")
		break
	case "markFinanceSettleType":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "MarkFinanceSettleType requires 3 args")
			flag.Usage()
		}
		arg2277 := flag.Arg(1)
		mbTrans2278 := thrift.NewTMemoryBufferLen(len(arg2277))
		defer mbTrans2278.Close()
		_, err2279 := mbTrans2278.WriteString(arg2277)
		if err2279 != nil {
			Usage()
			return
		}
		factory2280 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt2281 := factory2280.GetProtocol(mbTrans2278)
		argvalue0 := finance.NewRequestHeader()
		err2282 := argvalue0.Read(jsProt2281)
		if err2282 != nil {
			Usage()
			return
		}
		value0 := finance.RequestHeader(argvalue0)
		tmp1, err2283 := (strconv.Atoi(flag.Arg(2)))
		if err2283 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := finance.UidInt(argvalue1)
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := finance.SettleType(tmp2)
		value2 := finance.SettleType(argvalue2)
		fmt.Print(client.MarkFinanceSettleType(value0, value1, value2))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
