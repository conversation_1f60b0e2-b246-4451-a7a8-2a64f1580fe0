// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appserver_ios

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appserver_ios_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = appserver_ios_types.GoUnusedProtection__

type DasIosApiService interface {
	dm303.DomobService

	// 获取游戏列表，同时获得推广墙和游戏中心的游戏
	// 新游戏，网络游戏需要使用
	//
	// Parameters:
	//  - Header
	//  - Criteria
	ListApp(header *common.RequestHeader, criteria *ListCriteria) (r *appserver_ios_types.IosGameResult, ie *IosException, err error)
	// 根据关键字搜索游戏
	//
	// Parameters:
	//  - Header
	//  - Word
	//  - Criteria
	SearchApp(header *common.RequestHeader, word string, criteria *ListCriteria) (r *appserver_ios_types.IosGameResult, ie *IosException, err error)
	// 根据id获取游戏，获取游戏详情时使用
	//
	// Parameters:
	//  - Header
	//  - Id
	GetAppById(header *common.RequestHeader, id int32) (r *appserver_ios_types.IosDasGame, ie *IosException, err error)
	// Parameters:
	//  - Header
	//  - Id
	//  - AppwallProt
	GetWallGame(header *common.RequestHeader, id int32, appwall_prot AppWallProt) (r *appserver_ios_types.IosWallGame, ie *IosException, err error)
	// Parameters:
	//  - Header
	//  - Criteria
	ListGift(header *common.RequestHeader, criteria *ListGiftCriteria) (r *appserver_ios_types.IosGiftResult, ie *IosException, err error)
}

type DasIosApiServiceClient struct {
	*dm303.DomobServiceClient
}

func NewDasIosApiServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DasIosApiServiceClient {
	return &DasIosApiServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewDasIosApiServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DasIosApiServiceClient {
	return &DasIosApiServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 获取游戏列表，同时获得推广墙和游戏中心的游戏
// 新游戏，网络游戏需要使用
//
// Parameters:
//  - Header
//  - Criteria
func (p *DasIosApiServiceClient) ListApp(header *common.RequestHeader, criteria *ListCriteria) (r *appserver_ios_types.IosGameResult, ie *IosException, err error) {
	if err = p.sendListApp(header, criteria); err != nil {
		return
	}
	return p.recvListApp()
}

func (p *DasIosApiServiceClient) sendListApp(header *common.RequestHeader, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listApp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewListAppArgs()
	args4.Header = header
	args4.Criteria = criteria
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DasIosApiServiceClient) recvListApp() (value *appserver_ios_types.IosGameResult, ie *IosException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewListAppResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Ie != nil {
		ie = result5.Ie
	}
	return
}

// 根据关键字搜索游戏
//
// Parameters:
//  - Header
//  - Word
//  - Criteria
func (p *DasIosApiServiceClient) SearchApp(header *common.RequestHeader, word string, criteria *ListCriteria) (r *appserver_ios_types.IosGameResult, ie *IosException, err error) {
	if err = p.sendSearchApp(header, word, criteria); err != nil {
		return
	}
	return p.recvSearchApp()
}

func (p *DasIosApiServiceClient) sendSearchApp(header *common.RequestHeader, word string, criteria *ListCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchApp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewSearchAppArgs()
	args8.Header = header
	args8.Word = word
	args8.Criteria = criteria
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DasIosApiServiceClient) recvSearchApp() (value *appserver_ios_types.IosGameResult, ie *IosException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewSearchAppResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Ie != nil {
		ie = result9.Ie
	}
	return
}

// 根据id获取游戏，获取游戏详情时使用
//
// Parameters:
//  - Header
//  - Id
func (p *DasIosApiServiceClient) GetAppById(header *common.RequestHeader, id int32) (r *appserver_ios_types.IosDasGame, ie *IosException, err error) {
	if err = p.sendGetAppById(header, id); err != nil {
		return
	}
	return p.recvGetAppById()
}

func (p *DasIosApiServiceClient) sendGetAppById(header *common.RequestHeader, id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetAppByIdArgs()
	args12.Header = header
	args12.Id = id
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DasIosApiServiceClient) recvGetAppById() (value *appserver_ios_types.IosDasGame, ie *IosException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetAppByIdResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Ie != nil {
		ie = result13.Ie
	}
	return
}

// Parameters:
//  - Header
//  - Id
//  - AppwallProt
func (p *DasIosApiServiceClient) GetWallGame(header *common.RequestHeader, id int32, appwall_prot AppWallProt) (r *appserver_ios_types.IosWallGame, ie *IosException, err error) {
	if err = p.sendGetWallGame(header, id, appwall_prot); err != nil {
		return
	}
	return p.recvGetWallGame()
}

func (p *DasIosApiServiceClient) sendGetWallGame(header *common.RequestHeader, id int32, appwall_prot AppWallProt) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getWallGame", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetWallGameArgs()
	args16.Header = header
	args16.Id = id
	args16.AppwallProt = appwall_prot
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DasIosApiServiceClient) recvGetWallGame() (value *appserver_ios_types.IosWallGame, ie *IosException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetWallGameResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Ie != nil {
		ie = result17.Ie
	}
	return
}

// Parameters:
//  - Header
//  - Criteria
func (p *DasIosApiServiceClient) ListGift(header *common.RequestHeader, criteria *ListGiftCriteria) (r *appserver_ios_types.IosGiftResult, ie *IosException, err error) {
	if err = p.sendListGift(header, criteria); err != nil {
		return
	}
	return p.recvListGift()
}

func (p *DasIosApiServiceClient) sendListGift(header *common.RequestHeader, criteria *ListGiftCriteria) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listGift", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewListGiftArgs()
	args20.Header = header
	args20.Criteria = criteria
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DasIosApiServiceClient) recvListGift() (value *appserver_ios_types.IosGiftResult, ie *IosException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewListGiftResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Ie != nil {
		ie = result21.Ie
	}
	return
}

type DasIosApiServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewDasIosApiServiceProcessor(handler DasIosApiService) *DasIosApiServiceProcessor {
	self24 := &DasIosApiServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self24.AddToProcessorMap("listApp", &dasIosApiServiceProcessorListApp{handler: handler})
	self24.AddToProcessorMap("searchApp", &dasIosApiServiceProcessorSearchApp{handler: handler})
	self24.AddToProcessorMap("getAppById", &dasIosApiServiceProcessorGetAppById{handler: handler})
	self24.AddToProcessorMap("getWallGame", &dasIosApiServiceProcessorGetWallGame{handler: handler})
	self24.AddToProcessorMap("listGift", &dasIosApiServiceProcessorListGift{handler: handler})
	return self24
}

type dasIosApiServiceProcessorListApp struct {
	handler DasIosApiService
}

func (p *dasIosApiServiceProcessorListApp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListAppArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListAppResult()
	if result.Success, result.Ie, err = p.handler.ListApp(args.Header, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listApp: "+err.Error())
		oprot.WriteMessageBegin("listApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listApp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dasIosApiServiceProcessorSearchApp struct {
	handler DasIosApiService
}

func (p *dasIosApiServiceProcessorSearchApp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchAppArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchAppResult()
	if result.Success, result.Ie, err = p.handler.SearchApp(args.Header, args.Word, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchApp: "+err.Error())
		oprot.WriteMessageBegin("searchApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchApp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dasIosApiServiceProcessorGetAppById struct {
	handler DasIosApiService
}

func (p *dasIosApiServiceProcessorGetAppById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppByIdResult()
	if result.Success, result.Ie, err = p.handler.GetAppById(args.Header, args.Id); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppById: "+err.Error())
		oprot.WriteMessageBegin("getAppById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dasIosApiServiceProcessorGetWallGame struct {
	handler DasIosApiService
}

func (p *dasIosApiServiceProcessorGetWallGame) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetWallGameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getWallGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetWallGameResult()
	if result.Success, result.Ie, err = p.handler.GetWallGame(args.Header, args.Id, args.AppwallProt); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getWallGame: "+err.Error())
		oprot.WriteMessageBegin("getWallGame", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getWallGame", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dasIosApiServiceProcessorListGift struct {
	handler DasIosApiService
}

func (p *dasIosApiServiceProcessorListGift) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListGiftArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listGift", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListGiftResult()
	if result.Success, result.Ie, err = p.handler.ListGift(args.Header, args.Criteria); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listGift: "+err.Error())
		oprot.WriteMessageBegin("listGift", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listGift", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type ListAppArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Criteria *ListCriteria         `thrift:"criteria,2" json:"criteria"`
}

func NewListAppArgs() *ListAppArgs {
	return &ListAppArgs{}
}

func (p *ListAppArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListAppArgs) readField2(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *ListAppArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listApp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListAppArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:criteria: %s", p, err)
		}
	}
	return err
}

func (p *ListAppArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppArgs(%+v)", *p)
}

type ListAppResult struct {
	Success *appserver_ios_types.IosGameResult `thrift:"success,0" json:"success"`
	Ie      *IosException                      `thrift:"ie,1" json:"ie"`
}

func NewListAppResult() *ListAppResult {
	return &ListAppResult{}
}

func (p *ListAppResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListAppResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_ios_types.NewIosGameResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListAppResult) readField1(iprot thrift.TProtocol) error {
	p.Ie = NewIosException()
	if err := p.Ie.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ie)
	}
	return nil
}

func (p *ListAppResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listApp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ie != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListAppResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListAppResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ie != nil {
		if err := oprot.WriteFieldBegin("ie", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ie: %s", p, err)
		}
		if err := p.Ie.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ie)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ie: %s", p, err)
		}
	}
	return err
}

func (p *ListAppResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListAppResult(%+v)", *p)
}

type SearchAppArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Word     string                `thrift:"word,2" json:"word"`
	Criteria *ListCriteria         `thrift:"criteria,3" json:"criteria"`
}

func NewSearchAppArgs() *SearchAppArgs {
	return &SearchAppArgs{}
}

func (p *SearchAppArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchAppArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchAppArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Word = v
	}
	return nil
}

func (p *SearchAppArgs) readField3(iprot thrift.TProtocol) error {
	p.Criteria = NewListCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *SearchAppArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchApp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchAppArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchAppArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("word", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:word: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Word)); err != nil {
		return fmt.Errorf("%T.word (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:word: %s", p, err)
	}
	return err
}

func (p *SearchAppArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:criteria: %s", p, err)
		}
	}
	return err
}

func (p *SearchAppArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchAppArgs(%+v)", *p)
}

type SearchAppResult struct {
	Success *appserver_ios_types.IosGameResult `thrift:"success,0" json:"success"`
	Ie      *IosException                      `thrift:"ie,1" json:"ie"`
}

func NewSearchAppResult() *SearchAppResult {
	return &SearchAppResult{}
}

func (p *SearchAppResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchAppResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_ios_types.NewIosGameResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchAppResult) readField1(iprot thrift.TProtocol) error {
	p.Ie = NewIosException()
	if err := p.Ie.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ie)
	}
	return nil
}

func (p *SearchAppResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchApp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ie != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchAppResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchAppResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ie != nil {
		if err := oprot.WriteFieldBegin("ie", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ie: %s", p, err)
		}
		if err := p.Ie.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ie)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ie: %s", p, err)
		}
	}
	return err
}

func (p *SearchAppResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchAppResult(%+v)", *p)
}

type GetAppByIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Id     int32                 `thrift:"id,2" json:"id"`
}

func NewGetAppByIdArgs() *GetAppByIdArgs {
	return &GetAppByIdArgs{}
}

func (p *GetAppByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppByIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *GetAppByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *GetAppByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppByIdArgs(%+v)", *p)
}

type GetAppByIdResult struct {
	Success *appserver_ios_types.IosDasGame `thrift:"success,0" json:"success"`
	Ie      *IosException                   `thrift:"ie,1" json:"ie"`
}

func NewGetAppByIdResult() *GetAppByIdResult {
	return &GetAppByIdResult{}
}

func (p *GetAppByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_ios_types.NewIosDasGame()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Ie = NewIosException()
	if err := p.Ie.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ie)
	}
	return nil
}

func (p *GetAppByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ie != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ie != nil {
		if err := oprot.WriteFieldBegin("ie", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ie: %s", p, err)
		}
		if err := p.Ie.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ie)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ie: %s", p, err)
		}
	}
	return err
}

func (p *GetAppByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppByIdResult(%+v)", *p)
}

type GetWallGameArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	Id          int32                 `thrift:"id,2" json:"id"`
	AppwallProt AppWallProt           `thrift:"appwall_prot,3" json:"appwall_prot"`
}

func NewGetWallGameArgs() *GetWallGameArgs {
	return &GetWallGameArgs{}
}

func (p *GetWallGameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetWallGameArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *GetWallGameArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key25 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key25 = v
		}
		var _val26 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val26 = v
		}
		p.AppwallProt[_key25] = _val26
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetWallGameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getWallGame_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *GetWallGameArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetWallGameArgs(%+v)", *p)
}

type GetWallGameResult struct {
	Success *appserver_ios_types.IosWallGame `thrift:"success,0" json:"success"`
	Ie      *IosException                    `thrift:"ie,1" json:"ie"`
}

func NewGetWallGameResult() *GetWallGameResult {
	return &GetWallGameResult{}
}

func (p *GetWallGameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_ios_types.NewIosWallGame()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetWallGameResult) readField1(iprot thrift.TProtocol) error {
	p.Ie = NewIosException()
	if err := p.Ie.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ie)
	}
	return nil
}

func (p *GetWallGameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getWallGame_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ie != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetWallGameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ie != nil {
		if err := oprot.WriteFieldBegin("ie", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ie: %s", p, err)
		}
		if err := p.Ie.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ie)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ie: %s", p, err)
		}
	}
	return err
}

func (p *GetWallGameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetWallGameResult(%+v)", *p)
}

type ListGiftArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Criteria *ListGiftCriteria     `thrift:"criteria,2" json:"criteria"`
}

func NewListGiftArgs() *ListGiftArgs {
	return &ListGiftArgs{}
}

func (p *ListGiftArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGiftArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListGiftArgs) readField2(iprot thrift.TProtocol) error {
	p.Criteria = NewListGiftCriteria()
	if err := p.Criteria.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Criteria)
	}
	return nil
}

func (p *ListGiftArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listGift_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGiftArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Criteria != nil {
		if err := oprot.WriteFieldBegin("criteria", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:criteria: %s", p, err)
		}
		if err := p.Criteria.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Criteria)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:criteria: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGiftArgs(%+v)", *p)
}

type ListGiftResult struct {
	Success *appserver_ios_types.IosGiftResult `thrift:"success,0" json:"success"`
	Ie      *IosException                      `thrift:"ie,1" json:"ie"`
}

func NewListGiftResult() *ListGiftResult {
	return &ListGiftResult{}
}

func (p *ListGiftResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGiftResult) readField0(iprot thrift.TProtocol) error {
	p.Success = appserver_ios_types.NewIosGiftResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ListGiftResult) readField1(iprot thrift.TProtocol) error {
	p.Ie = NewIosException()
	if err := p.Ie.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Ie)
	}
	return nil
}

func (p *ListGiftResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listGift_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Ie != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGiftResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Ie != nil {
		if err := oprot.WriteFieldBegin("ie", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ie: %s", p, err)
		}
		if err := p.Ie.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Ie)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ie: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGiftResult(%+v)", *p)
}
