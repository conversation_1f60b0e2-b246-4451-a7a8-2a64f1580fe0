// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package appserver_ios

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appserver_ios_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = appserver_ios_types.GoUnusedProtection__
var GoUnusedProtection__ int

type ListBy int64

const (
	ListBy_LIST_BY_SELECTED    ListBy = 1
	ListBy_LIST_BY_UPDATE_TIME ListBy = 2
	ListBy_LIST_BY_ONLINEGAME  ListBy = 3
	ListBy_LIST_BY_FREE        ListBy = 4
	ListBy_LIST_BY_GUESS       ListBy = 5
)

func (p ListBy) String() string {
	switch p {
	case ListBy_LIST_BY_SELECTED:
		return "ListBy_LIST_BY_SELECTED"
	case ListBy_LIST_BY_UPDATE_TIME:
		return "ListBy_LIST_BY_UPDATE_TIME"
	case ListBy_LIST_BY_ONLINEGAME:
		return "ListBy_LIST_BY_ONLINEGAME"
	case ListBy_LIST_BY_FREE:
		return "ListBy_LIST_BY_FREE"
	case ListBy_LIST_BY_GUESS:
		return "ListBy_LIST_BY_GUESS"
	}
	return "<UNSET>"
}

func ListByFromString(s string) (ListBy, error) {
	switch s {
	case "ListBy_LIST_BY_SELECTED":
		return ListBy_LIST_BY_SELECTED, nil
	case "ListBy_LIST_BY_UPDATE_TIME":
		return ListBy_LIST_BY_UPDATE_TIME, nil
	case "ListBy_LIST_BY_ONLINEGAME":
		return ListBy_LIST_BY_ONLINEGAME, nil
	case "ListBy_LIST_BY_FREE":
		return ListBy_LIST_BY_FREE, nil
	case "ListBy_LIST_BY_GUESS":
		return ListBy_LIST_BY_GUESS, nil
	}
	return ListBy(math.MinInt32 - 1), fmt.Errorf("not a valid ListBy string")
}

type RequestHeader *common.RequestHeader

type IosGame *appserver_ios_types.IosGame

type IosGameResult *appserver_ios_types.IosGameResult

type IosDasGame *appserver_ios_types.IosDasGame

type IosWallGame *appserver_ios_types.IosWallGame

type IosGift *appserver_ios_types.IosGift

type IosGiftResult *appserver_ios_types.IosGiftResult

type IosCategory appserver_ios_types.IosCategory

type AppWallProt map[string]string

type IosException struct {
	Message string `thrift:"message,1" json:"message"`
}

func NewIosException() *IosException {
	return &IosException{}
}

func (p *IosException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *IosException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *IosException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("IosException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *IosException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:message: %s", p, err)
	}
	return err
}

func (p *IosException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("IosException(%+v)", *p)
}

type ListCriteria struct {
	Offset      int32       `thrift:"offset,1" json:"offset"`
	Limit       int32       `thrift:"limit,2" json:"limit"`
	Listby      ListBy      `thrift:"listby,3" json:"listby"`
	Category    IosCategory `thrift:"category,4" json:"category"`
	AppwallProt AppWallProt `thrift:"appwall_prot,5" json:"appwall_prot"`
	Channel     string      `thrift:"channel,6" json:"channel"`
}

func NewListCriteria() *ListCriteria {
	return &ListCriteria{
		Listby: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ListCriteria) IsSetListby() bool {
	return int64(p.Listby) != math.MinInt32-1
}

func (p *ListCriteria) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *ListCriteria) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListCriteria) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListCriteria) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListCriteria) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Listby = ListBy(v)
	}
	return nil
}

func (p *ListCriteria) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Category = IosCategory(v)
	}
	return nil
}

func (p *ListCriteria) readField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.AppwallProt[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ListCriteria) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *ListCriteria) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ListCriteria"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListCriteria) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:offset: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:limit: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetListby() {
		if err := oprot.WriteFieldBegin("listby", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:listby: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Listby)); err != nil {
			return fmt.Errorf("%T.listby (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:listby: %s", p, err)
		}
	}
	return err
}

func (p *ListCriteria) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:category: %s", p, err)
	}
	return err
}

func (p *ListCriteria) writeField5(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *ListCriteria) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:channel: %s", p, err)
	}
	return err
}

func (p *ListCriteria) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListCriteria(%+v)", *p)
}

type ListGiftCriteria struct {
	Offset      int32       `thrift:"offset,1" json:"offset"`
	Limit       int32       `thrift:"limit,2" json:"limit"`
	AppwallProt AppWallProt `thrift:"appwall_prot,3" json:"appwall_prot"`
	UserId      int32       `thrift:"user_id,4" json:"user_id"`
	GameId      int32       `thrift:"game_id,5" json:"game_id"`
	Vendor      string      `thrift:"vendor,6" json:"vendor"`
	IsAll       bool        `thrift:"is_all,7" json:"is_all"`
}

func NewListGiftCriteria() *ListGiftCriteria {
	return &ListGiftCriteria{}
}

func (p *ListGiftCriteria) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListGiftCriteria) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *ListGiftCriteria) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *ListGiftCriteria) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AppwallProt = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key2 = v
		}
		var _val3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val3 = v
		}
		p.AppwallProt[_key2] = _val3
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ListGiftCriteria) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UserId = v
	}
	return nil
}

func (p *ListGiftCriteria) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.GameId = v
	}
	return nil
}

func (p *ListGiftCriteria) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Vendor = v
	}
	return nil
}

func (p *ListGiftCriteria) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.IsAll = v
	}
	return nil
}

func (p *ListGiftCriteria) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ListGiftCriteria"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListGiftCriteria) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:offset: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:limit: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AppwallProt != nil {
		if err := oprot.WriteFieldBegin("appwall_prot", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appwall_prot: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AppwallProt)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AppwallProt {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appwall_prot: %s", p, err)
		}
	}
	return err
}

func (p *ListGiftCriteria) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:user_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UserId)); err != nil {
		return fmt.Errorf("%T.user_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:user_id: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("game_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:game_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GameId)); err != nil {
		return fmt.Errorf("%T.game_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:game_id: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("vendor", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:vendor: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Vendor)); err != nil {
		return fmt.Errorf("%T.vendor (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:vendor: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_all", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:is_all: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsAll)); err != nil {
		return fmt.Errorf("%T.is_all (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:is_all: %s", p, err)
	}
	return err
}

func (p *ListGiftCriteria) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListGiftCriteria(%+v)", *p)
}
