// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package tag_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__

const ConfidenceLevel_0 = 0
const ConfidenceLevel_25 = 25
const ConfidenceLevel_50 = 50
const ConfidenceLevel_75 = 75
const ConfidenceLevel_100 = 100

func init() {
}
