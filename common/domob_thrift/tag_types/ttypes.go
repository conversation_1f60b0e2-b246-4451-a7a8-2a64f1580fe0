// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package tag_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type IdInt common.IdInt

type RequestHeader *common.RequestHeader

type DemoConceptIdInt common.IdInt

type DemoTagIdInt common.IdInt

type MediaTagIdInt common.IdInt

type DemoTagGroupIdInt common.IdInt

type ImpReasonIdInt common.IdInt

type Amount common.Amount

type DemoConcept struct {
	ConceptId DemoConceptIdInt `thrift:"conceptId,1" json:"conceptId"`
	TagIds    []DemoTagIdInt   `thrift:"tagIds,2" json:"tagIds"`
	Relation  int32            `thrift:"relation,3" json:"relation"`
	Name      string           `thrift:"name,4" json:"name"`
	Desc      string           `thrift:"desc,5" json:"desc"`
}

func NewDemoConcept() *DemoConcept {
	return &DemoConcept{}
}

func (p *DemoConcept) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DemoConcept) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ConceptId = DemoConceptIdInt(v)
	}
	return nil
}

func (p *DemoConcept) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TagIds = make([]DemoTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 DemoTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = DemoTagIdInt(v)
		}
		p.TagIds = append(p.TagIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DemoConcept) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Relation = v
	}
	return nil
}

func (p *DemoConcept) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DemoConcept) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *DemoConcept) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DemoConcept"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DemoConcept) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("conceptId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:conceptId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ConceptId)); err != nil {
		return fmt.Errorf("%T.conceptId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:conceptId: %s", p, err)
	}
	return err
}

func (p *DemoConcept) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TagIds != nil {
		if err := oprot.WriteFieldBegin("tagIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:tagIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.TagIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TagIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:tagIds: %s", p, err)
		}
	}
	return err
}

func (p *DemoConcept) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("relation", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:relation: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Relation)); err != nil {
		return fmt.Errorf("%T.relation (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:relation: %s", p, err)
	}
	return err
}

func (p *DemoConcept) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *DemoConcept) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:desc: %s", p, err)
	}
	return err
}

func (p *DemoConcept) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DemoConcept(%+v)", *p)
}

type DemoTagGroup struct {
	TagGroupId DemoTagGroupIdInt `thrift:"tagGroupId,1" json:"tagGroupId"`
	TagIds     []DemoTagIdInt    `thrift:"tagIds,2" json:"tagIds"`
	Name       string            `thrift:"name,3" json:"name"`
	Desc       string            `thrift:"desc,4" json:"desc"`
}

func NewDemoTagGroup() *DemoTagGroup {
	return &DemoTagGroup{}
}

func (p *DemoTagGroup) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DemoTagGroup) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagGroupId = DemoTagGroupIdInt(v)
	}
	return nil
}

func (p *DemoTagGroup) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TagIds = make([]DemoTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem1 DemoTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem1 = DemoTagIdInt(v)
		}
		p.TagIds = append(p.TagIds, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DemoTagGroup) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DemoTagGroup) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *DemoTagGroup) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DemoTagGroup"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DemoTagGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagGroupId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tagGroupId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TagGroupId)); err != nil {
		return fmt.Errorf("%T.tagGroupId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tagGroupId: %s", p, err)
	}
	return err
}

func (p *DemoTagGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if p.TagIds != nil {
		if err := oprot.WriteFieldBegin("tagIds", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:tagIds: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.TagIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TagIds {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:tagIds: %s", p, err)
		}
	}
	return err
}

func (p *DemoTagGroup) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *DemoTagGroup) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:desc: %s", p, err)
	}
	return err
}

func (p *DemoTagGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DemoTagGroup(%+v)", *p)
}

type DemoTag struct {
	TagId      DemoTagIdInt      `thrift:"tagId,1" json:"tagId"`
	TagGroupId DemoTagGroupIdInt `thrift:"tagGroupId,2" json:"tagGroupId"`
	Name       string            `thrift:"name,3" json:"name"`
	Desc       string            `thrift:"desc,4" json:"desc"`
}

func NewDemoTag() *DemoTag {
	return &DemoTag{}
}

func (p *DemoTag) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DemoTag) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagId = DemoTagIdInt(v)
	}
	return nil
}

func (p *DemoTag) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TagGroupId = DemoTagGroupIdInt(v)
	}
	return nil
}

func (p *DemoTag) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DemoTag) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *DemoTag) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DemoTag"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DemoTag) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tagId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TagId)); err != nil {
		return fmt.Errorf("%T.tagId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tagId: %s", p, err)
	}
	return err
}

func (p *DemoTag) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagGroupId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tagGroupId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TagGroupId)); err != nil {
		return fmt.Errorf("%T.tagGroupId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tagGroupId: %s", p, err)
	}
	return err
}

func (p *DemoTag) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *DemoTag) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:desc: %s", p, err)
	}
	return err
}

func (p *DemoTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DemoTag(%+v)", *p)
}

type DemoTagging struct {
	TagId      DemoTagIdInt `thrift:"tagId,1" json:"tagId"`
	Confidence int32        `thrift:"confidence,2" json:"confidence"`
}

func NewDemoTagging() *DemoTagging {
	return &DemoTagging{}
}

func (p *DemoTagging) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DemoTagging) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagId = DemoTagIdInt(v)
	}
	return nil
}

func (p *DemoTagging) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Confidence = v
	}
	return nil
}

func (p *DemoTagging) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DemoTagging"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DemoTagging) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tagId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TagId)); err != nil {
		return fmt.Errorf("%T.tagId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tagId: %s", p, err)
	}
	return err
}

func (p *DemoTagging) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("confidence", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:confidence: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Confidence)); err != nil {
		return fmt.Errorf("%T.confidence (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:confidence: %s", p, err)
	}
	return err
}

func (p *DemoTagging) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DemoTagging(%+v)", *p)
}

type ImpDecision struct {
	ConceptOrTagId IdInt          `thrift:"conceptOrTagId,1" json:"conceptOrTagId"`
	Reason         ImpReasonIdInt `thrift:"reason,2" json:"reason"`
	Confidence     int32          `thrift:"confidence,3" json:"confidence"`
}

func NewImpDecision() *ImpDecision {
	return &ImpDecision{}
}

func (p *ImpDecision) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ImpDecision) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ConceptOrTagId = IdInt(v)
	}
	return nil
}

func (p *ImpDecision) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Reason = ImpReasonIdInt(v)
	}
	return nil
}

func (p *ImpDecision) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Confidence = v
	}
	return nil
}

func (p *ImpDecision) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ImpDecision"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ImpDecision) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("conceptOrTagId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:conceptOrTagId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ConceptOrTagId)); err != nil {
		return fmt.Errorf("%T.conceptOrTagId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:conceptOrTagId: %s", p, err)
	}
	return err
}

func (p *ImpDecision) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reason", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:reason: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Reason)); err != nil {
		return fmt.Errorf("%T.reason (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:reason: %s", p, err)
	}
	return err
}

func (p *ImpDecision) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("confidence", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:confidence: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Confidence)); err != nil {
		return fmt.Errorf("%T.confidence (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:confidence: %s", p, err)
	}
	return err
}

func (p *ImpDecision) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ImpDecision(%+v)", *p)
}

type MediaTag struct {
	TagId MediaTagIdInt `thrift:"tagId,1" json:"tagId"`
	// unused field # 2
	Name string `thrift:"name,3" json:"name"`
	Desc string `thrift:"desc,4" json:"desc"`
}

func NewMediaTag() *MediaTag {
	return &MediaTag{}
}

func (p *MediaTag) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaTag) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagId = MediaTagIdInt(v)
	}
	return nil
}

func (p *MediaTag) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *MediaTag) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *MediaTag) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaTag"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaTag) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tagId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TagId)); err != nil {
		return fmt.Errorf("%T.tagId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tagId: %s", p, err)
	}
	return err
}

func (p *MediaTag) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *MediaTag) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:desc: %s", p, err)
	}
	return err
}

func (p *MediaTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaTag(%+v)", *p)
}

type MediaTagging struct {
	TagId      MediaTagIdInt `thrift:"tagId,1" json:"tagId"`
	Confidence int32         `thrift:"confidence,2" json:"confidence"`
}

func NewMediaTagging() *MediaTagging {
	return &MediaTagging{}
}

func (p *MediaTagging) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MediaTagging) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagId = MediaTagIdInt(v)
	}
	return nil
}

func (p *MediaTagging) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Confidence = v
	}
	return nil
}

func (p *MediaTagging) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MediaTagging"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MediaTagging) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagId", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tagId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TagId)); err != nil {
		return fmt.Errorf("%T.tagId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tagId: %s", p, err)
	}
	return err
}

func (p *MediaTagging) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("confidence", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:confidence: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Confidence)); err != nil {
		return fmt.Errorf("%T.confidence (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:confidence: %s", p, err)
	}
	return err
}

func (p *MediaTagging) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MediaTagging(%+v)", *p)
}
