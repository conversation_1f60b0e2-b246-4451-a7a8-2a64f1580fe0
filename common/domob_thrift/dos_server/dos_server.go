// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package dos_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dos_types"
	"rtb_model_server/common/domob_thrift/enums"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dos_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__

type DosServer interface { //DOS 服务接口定义

	// 添加外采计划
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Plan: 计划结构体
	AddPlan(header *common.RequestHeader, plan *dos_types.DOSPlan) (r int32, e *DosServerException, err error)
	// 编辑外采计划
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Plan: 计划结构体
	EditPlan(header *common.RequestHeader, plan *dos_types.DOSPlan) (e *DosServerException, err error)
	// 批量获取推广活动列表
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 推广活动的编号列表 list, 最大允许一次请求100个活动 *
	GetPlansByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dos_types.DOSPlan, e *DosServerException, err error)
	// 根据dspCampaignId获取DOS计划ID列表
	// 提供参数控制是否排除掉已经删除的plan,符合条件的id全部返回。
	// 按pid升序排序
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - DspCampaignId: DSP活动编号
	//  - ExcludeDeleted: 是否排除掉已删除的
	ListPlanIdsByDspCampaignId(header *common.RequestHeader, dspCampaignId int32, excludeDeleted bool) (r []int32, e *DosServerException, err error)
	// 检索计划信息，根据检索的参数返回结果
	// 返回计划ID列表
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Params: 查询参数
	//  - Order: 排序条件 *
	//  - Offset: 查询的偏移量 *
	//  - Limit: 查询数量限制,最大支持100个 *
	QueryPlanByParam(header *common.RequestHeader, params *dos_types.QueryParam, order *dos_types.OrderParam, offset int32, limit int32) (r *common.QueryResult, e *DosServerException, err error)
	// 根据计划ids批量暂停计划
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 计划编号list
	PausePlansByIds(header *common.RequestHeader, ids []int32) (e *DosServerException, err error)
	// 根据计划ids批量回复运行计划
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 计划编号list
	ResumePlansByIds(header *common.RequestHeader, ids []int32) (e *DosServerException, err error)
	// 新建策略
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Strategy: 策略结构体
	AddStrategy(header *common.RequestHeader, strategy *dos_types.DOSStrategy) (r int32, e *DosServerException, err error)
	// 编辑策略
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Strategy: 策略结构体
	EditStrategy(header *common.RequestHeader, strategy *dos_types.DOSStrategy) (e *DosServerException, err error)
	// 检索策略，根据检索的参数返回符合条件的策略ID
	// 返回策略ID列表
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Params: 查询参数
	//  - Order: 排序条件 *
	//  - Offset: 查询的偏移量 *
	//  - Limit: 查询数量限制,最大支持100个 *
	QueryStrategyByParam(header *common.RequestHeader, params *dos_types.StrategyQueryParam, order *dos_types.StrategyOrderParam, offset int32, limit int32) (r *common.QueryResult, e *DosServerException, err error)
	// 根据策略ID批量获得策略
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 策略的编号列表 list, 最大允许一次请求100个策略 *
	GetStrategiesByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dos_types.DOSStrategy, e *DosServerException, err error)
	// 新建创意
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Creative: 创意结构体
	AddCreative(header *common.RequestHeader, creative *dos_types.DOSCreative) (r int32, e *DosServerException, err error)
	// 编辑创意
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Creative: 创意结构体
	EditCreative(header *common.RequestHeader, creative *dos_types.DOSCreative) (e *DosServerException, err error)
	// 检索创意，根据检索的参数返回符合条件的创意ID
	// 返回创意ID列表
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Params: 查询参数
	//  - Order: 排序条件 *
	//  - Offset: 查询的偏移量 *
	//  - Limit: 查询数量限制,最大支持100个 *
	QueryCreativeByParam(header *common.RequestHeader, params *dos_types.CreativeQueryParam, order *dos_types.CreativeOrderParam, offset int32, limit int32) (r *common.QueryResult, e *DosServerException, err error)
	// 根据创意ID批量获得创意
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 创意的编号列表 list, 最大允许一次请求100个创意 *
	//  - WithCache: 是否需要开启 cache *
	GetCreativesByIds(header *common.RequestHeader, ids []int32, withCache bool) (r map[int32]*dos_types.DOSCreative, e *DosServerException, err error)
	// 新建第三方绑定
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Third: 创意结构体
	AddThirdParty(header *common.RequestHeader, third *dos_types.DOSThirdParty) (r int32, e *DosServerException, err error)
	// 查询第三方token
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Token: 查询token结构体
	GetThirdPartyToken(header *common.RequestHeader, token *dos_types.QueryThirdPartyToken) (r *dos_types.DOSQueryThirdPartyTokenResult, e *DosServerException, err error)
	// 新建第三方数据绑定关联
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Info: 第三方数据绑定信息
	AddThirdBandingInfo(header *common.RequestHeader, info *dos_types.DOSThirdBandingInfo) (e *DosServerException, err error)
	// 查询头条子账户对应top account id
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - SubAccountId: 第三方数据绑定信息
	QueryTopAccountId(header *common.RequestHeader, SubAccountId int64) (r int64, e *DosServerException, err error)
}

//DOS 服务接口定义
type DosServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDosServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DosServerClient {
	return &DosServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDosServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DosServerClient {
	return &DosServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 添加外采计划
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Plan: 计划结构体
func (p *DosServerClient) AddPlan(header *common.RequestHeader, plan *dos_types.DOSPlan) (r int32, e *DosServerException, err error) {
	if err = p.sendAddPlan(header, plan); err != nil {
		return
	}
	return p.recvAddPlan()
}

func (p *DosServerClient) sendAddPlan(header *common.RequestHeader, plan *dos_types.DOSPlan) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddPlanArgs()
	args0.Header = header
	args0.Plan = plan
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvAddPlan() (value int32, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddPlanResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// 编辑外采计划
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Plan: 计划结构体
func (p *DosServerClient) EditPlan(header *common.RequestHeader, plan *dos_types.DOSPlan) (e *DosServerException, err error) {
	if err = p.sendEditPlan(header, plan); err != nil {
		return
	}
	return p.recvEditPlan()
}

func (p *DosServerClient) sendEditPlan(header *common.RequestHeader, plan *dos_types.DOSPlan) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editPlan", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewEditPlanArgs()
	args4.Header = header
	args4.Plan = plan
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvEditPlan() (e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewEditPlanResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result5.E != nil {
		e = result5.E
	}
	return
}

// 批量获取推广活动列表
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 推广活动的编号列表 list, 最大允许一次请求100个活动 *
func (p *DosServerClient) GetPlansByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dos_types.DOSPlan, e *DosServerException, err error) {
	if err = p.sendGetPlansByIds(header, ids); err != nil {
		return
	}
	return p.recvGetPlansByIds()
}

func (p *DosServerClient) sendGetPlansByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getPlansByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetPlansByIdsArgs()
	args8.Header = header
	args8.Ids = ids
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvGetPlansByIds() (value map[int32]*dos_types.DOSPlan, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetPlansByIdsResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.E != nil {
		e = result9.E
	}
	return
}

// 根据dspCampaignId获取DOS计划ID列表
// 提供参数控制是否排除掉已经删除的plan,符合条件的id全部返回。
// 按pid升序排序
//
// Parameters:
//  - Header: 请求消息头结构体
//  - DspCampaignId: DSP活动编号
//  - ExcludeDeleted: 是否排除掉已删除的
func (p *DosServerClient) ListPlanIdsByDspCampaignId(header *common.RequestHeader, dspCampaignId int32, excludeDeleted bool) (r []int32, e *DosServerException, err error) {
	if err = p.sendListPlanIdsByDspCampaignId(header, dspCampaignId, excludeDeleted); err != nil {
		return
	}
	return p.recvListPlanIdsByDspCampaignId()
}

func (p *DosServerClient) sendListPlanIdsByDspCampaignId(header *common.RequestHeader, dspCampaignId int32, excludeDeleted bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("listPlanIdsByDspCampaignId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewListPlanIdsByDspCampaignIdArgs()
	args12.Header = header
	args12.DspCampaignId = dspCampaignId
	args12.ExcludeDeleted = excludeDeleted
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvListPlanIdsByDspCampaignId() (value []int32, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewListPlanIdsByDspCampaignIdResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.E != nil {
		e = result13.E
	}
	return
}

// 检索计划信息，根据检索的参数返回结果
// 返回计划ID列表
//
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Params: 查询参数
//  - Order: 排序条件 *
//  - Offset: 查询的偏移量 *
//  - Limit: 查询数量限制,最大支持100个 *
func (p *DosServerClient) QueryPlanByParam(header *common.RequestHeader, params *dos_types.QueryParam, order *dos_types.OrderParam, offset int32, limit int32) (r *common.QueryResult, e *DosServerException, err error) {
	if err = p.sendQueryPlanByParam(header, params, order, offset, limit); err != nil {
		return
	}
	return p.recvQueryPlanByParam()
}

func (p *DosServerClient) sendQueryPlanByParam(header *common.RequestHeader, params *dos_types.QueryParam, order *dos_types.OrderParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryPlanByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewQueryPlanByParamArgs()
	args16.Header = header
	args16.Params = params
	args16.Order = order
	args16.Offset = offset
	args16.Limit = limit
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvQueryPlanByParam() (value *common.QueryResult, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewQueryPlanByParamResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.E != nil {
		e = result17.E
	}
	return
}

// 根据计划ids批量暂停计划
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 计划编号list
func (p *DosServerClient) PausePlansByIds(header *common.RequestHeader, ids []int32) (e *DosServerException, err error) {
	if err = p.sendPausePlansByIds(header, ids); err != nil {
		return
	}
	return p.recvPausePlansByIds()
}

func (p *DosServerClient) sendPausePlansByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("pausePlansByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewPausePlansByIdsArgs()
	args20.Header = header
	args20.Ids = ids
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvPausePlansByIds() (e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewPausePlansByIdsResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result21.E != nil {
		e = result21.E
	}
	return
}

// 根据计划ids批量回复运行计划
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 计划编号list
func (p *DosServerClient) ResumePlansByIds(header *common.RequestHeader, ids []int32) (e *DosServerException, err error) {
	if err = p.sendResumePlansByIds(header, ids); err != nil {
		return
	}
	return p.recvResumePlansByIds()
}

func (p *DosServerClient) sendResumePlansByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("resumePlansByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewResumePlansByIdsArgs()
	args24.Header = header
	args24.Ids = ids
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvResumePlansByIds() (e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewResumePlansByIdsResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result25.E != nil {
		e = result25.E
	}
	return
}

// 新建策略
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Strategy: 策略结构体
func (p *DosServerClient) AddStrategy(header *common.RequestHeader, strategy *dos_types.DOSStrategy) (r int32, e *DosServerException, err error) {
	if err = p.sendAddStrategy(header, strategy); err != nil {
		return
	}
	return p.recvAddStrategy()
}

func (p *DosServerClient) sendAddStrategy(header *common.RequestHeader, strategy *dos_types.DOSStrategy) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addStrategy", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewAddStrategyArgs()
	args28.Header = header
	args28.Strategy = strategy
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvAddStrategy() (value int32, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewAddStrategyResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.E != nil {
		e = result29.E
	}
	return
}

// 编辑策略
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Strategy: 策略结构体
func (p *DosServerClient) EditStrategy(header *common.RequestHeader, strategy *dos_types.DOSStrategy) (e *DosServerException, err error) {
	if err = p.sendEditStrategy(header, strategy); err != nil {
		return
	}
	return p.recvEditStrategy()
}

func (p *DosServerClient) sendEditStrategy(header *common.RequestHeader, strategy *dos_types.DOSStrategy) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editStrategy", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewEditStrategyArgs()
	args32.Header = header
	args32.Strategy = strategy
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvEditStrategy() (e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewEditStrategyResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result33.E != nil {
		e = result33.E
	}
	return
}

// 检索策略，根据检索的参数返回符合条件的策略ID
// 返回策略ID列表
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Params: 查询参数
//  - Order: 排序条件 *
//  - Offset: 查询的偏移量 *
//  - Limit: 查询数量限制,最大支持100个 *
func (p *DosServerClient) QueryStrategyByParam(header *common.RequestHeader, params *dos_types.StrategyQueryParam, order *dos_types.StrategyOrderParam, offset int32, limit int32) (r *common.QueryResult, e *DosServerException, err error) {
	if err = p.sendQueryStrategyByParam(header, params, order, offset, limit); err != nil {
		return
	}
	return p.recvQueryStrategyByParam()
}

func (p *DosServerClient) sendQueryStrategyByParam(header *common.RequestHeader, params *dos_types.StrategyQueryParam, order *dos_types.StrategyOrderParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryStrategyByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewQueryStrategyByParamArgs()
	args36.Header = header
	args36.Params = params
	args36.Order = order
	args36.Offset = offset
	args36.Limit = limit
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvQueryStrategyByParam() (value *common.QueryResult, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewQueryStrategyByParamResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.E != nil {
		e = result37.E
	}
	return
}

// 根据策略ID批量获得策略
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 策略的编号列表 list, 最大允许一次请求100个策略 *
func (p *DosServerClient) GetStrategiesByIds(header *common.RequestHeader, ids []int32) (r map[int32]*dos_types.DOSStrategy, e *DosServerException, err error) {
	if err = p.sendGetStrategiesByIds(header, ids); err != nil {
		return
	}
	return p.recvGetStrategiesByIds()
}

func (p *DosServerClient) sendGetStrategiesByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getStrategiesByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewGetStrategiesByIdsArgs()
	args40.Header = header
	args40.Ids = ids
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvGetStrategiesByIds() (value map[int32]*dos_types.DOSStrategy, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewGetStrategiesByIdsResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.E != nil {
		e = result41.E
	}
	return
}

// 新建创意
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Creative: 创意结构体
func (p *DosServerClient) AddCreative(header *common.RequestHeader, creative *dos_types.DOSCreative) (r int32, e *DosServerException, err error) {
	if err = p.sendAddCreative(header, creative); err != nil {
		return
	}
	return p.recvAddCreative()
}

func (p *DosServerClient) sendAddCreative(header *common.RequestHeader, creative *dos_types.DOSCreative) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addCreative", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args44 := NewAddCreativeArgs()
	args44.Header = header
	args44.Creative = creative
	if err = args44.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvAddCreative() (value int32, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error46 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error47 error
		error47, err = error46.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error47
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result45 := NewAddCreativeResult()
	if err = result45.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result45.Success
	if result45.E != nil {
		e = result45.E
	}
	return
}

// 编辑创意
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Creative: 创意结构体
func (p *DosServerClient) EditCreative(header *common.RequestHeader, creative *dos_types.DOSCreative) (e *DosServerException, err error) {
	if err = p.sendEditCreative(header, creative); err != nil {
		return
	}
	return p.recvEditCreative()
}

func (p *DosServerClient) sendEditCreative(header *common.RequestHeader, creative *dos_types.DOSCreative) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editCreative", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args48 := NewEditCreativeArgs()
	args48.Header = header
	args48.Creative = creative
	if err = args48.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvEditCreative() (e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error50 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error51 error
		error51, err = error50.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error51
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result49 := NewEditCreativeResult()
	if err = result49.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result49.E != nil {
		e = result49.E
	}
	return
}

// 检索创意，根据检索的参数返回符合条件的创意ID
// 返回创意ID列表
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Params: 查询参数
//  - Order: 排序条件 *
//  - Offset: 查询的偏移量 *
//  - Limit: 查询数量限制,最大支持100个 *
func (p *DosServerClient) QueryCreativeByParam(header *common.RequestHeader, params *dos_types.CreativeQueryParam, order *dos_types.CreativeOrderParam, offset int32, limit int32) (r *common.QueryResult, e *DosServerException, err error) {
	if err = p.sendQueryCreativeByParam(header, params, order, offset, limit); err != nil {
		return
	}
	return p.recvQueryCreativeByParam()
}

func (p *DosServerClient) sendQueryCreativeByParam(header *common.RequestHeader, params *dos_types.CreativeQueryParam, order *dos_types.CreativeOrderParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryCreativeByParam", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args52 := NewQueryCreativeByParamArgs()
	args52.Header = header
	args52.Params = params
	args52.Order = order
	args52.Offset = offset
	args52.Limit = limit
	if err = args52.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvQueryCreativeByParam() (value *common.QueryResult, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error54 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error55 error
		error55, err = error54.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error55
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result53 := NewQueryCreativeByParamResult()
	if err = result53.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result53.Success
	if result53.E != nil {
		e = result53.E
	}
	return
}

// 根据创意ID批量获得创意
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 创意的编号列表 list, 最大允许一次请求100个创意 *
//  - WithCache: 是否需要开启 cache *
func (p *DosServerClient) GetCreativesByIds(header *common.RequestHeader, ids []int32, withCache bool) (r map[int32]*dos_types.DOSCreative, e *DosServerException, err error) {
	if err = p.sendGetCreativesByIds(header, ids, withCache); err != nil {
		return
	}
	return p.recvGetCreativesByIds()
}

func (p *DosServerClient) sendGetCreativesByIds(header *common.RequestHeader, ids []int32, withCache bool) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCreativesByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args56 := NewGetCreativesByIdsArgs()
	args56.Header = header
	args56.Ids = ids
	args56.WithCache = withCache
	if err = args56.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvGetCreativesByIds() (value map[int32]*dos_types.DOSCreative, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error58 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error59 error
		error59, err = error58.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error59
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result57 := NewGetCreativesByIdsResult()
	if err = result57.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result57.Success
	if result57.E != nil {
		e = result57.E
	}
	return
}

// 新建第三方绑定
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Third: 创意结构体
func (p *DosServerClient) AddThirdParty(header *common.RequestHeader, third *dos_types.DOSThirdParty) (r int32, e *DosServerException, err error) {
	if err = p.sendAddThirdParty(header, third); err != nil {
		return
	}
	return p.recvAddThirdParty()
}

func (p *DosServerClient) sendAddThirdParty(header *common.RequestHeader, third *dos_types.DOSThirdParty) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addThirdParty", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args60 := NewAddThirdPartyArgs()
	args60.Header = header
	args60.Third = third
	if err = args60.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvAddThirdParty() (value int32, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error62 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error63 error
		error63, err = error62.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error63
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result61 := NewAddThirdPartyResult()
	if err = result61.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result61.Success
	if result61.E != nil {
		e = result61.E
	}
	return
}

// 查询第三方token
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Token: 查询token结构体
func (p *DosServerClient) GetThirdPartyToken(header *common.RequestHeader, token *dos_types.QueryThirdPartyToken) (r *dos_types.DOSQueryThirdPartyTokenResult, e *DosServerException, err error) {
	if err = p.sendGetThirdPartyToken(header, token); err != nil {
		return
	}
	return p.recvGetThirdPartyToken()
}

func (p *DosServerClient) sendGetThirdPartyToken(header *common.RequestHeader, token *dos_types.QueryThirdPartyToken) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getThirdPartyToken", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args64 := NewGetThirdPartyTokenArgs()
	args64.Header = header
	args64.Token = token
	if err = args64.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvGetThirdPartyToken() (value *dos_types.DOSQueryThirdPartyTokenResult, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error66 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error67 error
		error67, err = error66.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error67
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result65 := NewGetThirdPartyTokenResult()
	if err = result65.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result65.Success
	if result65.E != nil {
		e = result65.E
	}
	return
}

// 新建第三方数据绑定关联
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Info: 第三方数据绑定信息
func (p *DosServerClient) AddThirdBandingInfo(header *common.RequestHeader, info *dos_types.DOSThirdBandingInfo) (e *DosServerException, err error) {
	if err = p.sendAddThirdBandingInfo(header, info); err != nil {
		return
	}
	return p.recvAddThirdBandingInfo()
}

func (p *DosServerClient) sendAddThirdBandingInfo(header *common.RequestHeader, info *dos_types.DOSThirdBandingInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addThirdBandingInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args68 := NewAddThirdBandingInfoArgs()
	args68.Header = header
	args68.Info = info
	if err = args68.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvAddThirdBandingInfo() (e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error70 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error71 error
		error71, err = error70.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error71
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result69 := NewAddThirdBandingInfoResult()
	if err = result69.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result69.E != nil {
		e = result69.E
	}
	return
}

// 查询头条子账户对应top account id
//
// Parameters:
//  - Header: 请求消息头结构体
//  - SubAccountId: 第三方数据绑定信息
func (p *DosServerClient) QueryTopAccountId(header *common.RequestHeader, SubAccountId int64) (r int64, e *DosServerException, err error) {
	if err = p.sendQueryTopAccountId(header, SubAccountId); err != nil {
		return
	}
	return p.recvQueryTopAccountId()
}

func (p *DosServerClient) sendQueryTopAccountId(header *common.RequestHeader, SubAccountId int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryTopAccountId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args72 := NewQueryTopAccountIdArgs()
	args72.Header = header
	args72.SubAccountId = SubAccountId
	if err = args72.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DosServerClient) recvQueryTopAccountId() (value int64, e *DosServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error74 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error75 error
		error75, err = error74.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error75
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result73 := NewQueryTopAccountIdResult()
	if err = result73.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result73.Success
	if result73.E != nil {
		e = result73.E
	}
	return
}

type DosServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DosServer
}

func (p *DosServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DosServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DosServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDosServerProcessor(handler DosServer) *DosServerProcessor {

	self76 := &DosServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self76.processorMap["addPlan"] = &dosServerProcessorAddPlan{handler: handler}
	self76.processorMap["editPlan"] = &dosServerProcessorEditPlan{handler: handler}
	self76.processorMap["getPlansByIds"] = &dosServerProcessorGetPlansByIds{handler: handler}
	self76.processorMap["listPlanIdsByDspCampaignId"] = &dosServerProcessorListPlanIdsByDspCampaignId{handler: handler}
	self76.processorMap["queryPlanByParam"] = &dosServerProcessorQueryPlanByParam{handler: handler}
	self76.processorMap["pausePlansByIds"] = &dosServerProcessorPausePlansByIds{handler: handler}
	self76.processorMap["resumePlansByIds"] = &dosServerProcessorResumePlansByIds{handler: handler}
	self76.processorMap["addStrategy"] = &dosServerProcessorAddStrategy{handler: handler}
	self76.processorMap["editStrategy"] = &dosServerProcessorEditStrategy{handler: handler}
	self76.processorMap["queryStrategyByParam"] = &dosServerProcessorQueryStrategyByParam{handler: handler}
	self76.processorMap["getStrategiesByIds"] = &dosServerProcessorGetStrategiesByIds{handler: handler}
	self76.processorMap["addCreative"] = &dosServerProcessorAddCreative{handler: handler}
	self76.processorMap["editCreative"] = &dosServerProcessorEditCreative{handler: handler}
	self76.processorMap["queryCreativeByParam"] = &dosServerProcessorQueryCreativeByParam{handler: handler}
	self76.processorMap["getCreativesByIds"] = &dosServerProcessorGetCreativesByIds{handler: handler}
	self76.processorMap["addThirdParty"] = &dosServerProcessorAddThirdParty{handler: handler}
	self76.processorMap["getThirdPartyToken"] = &dosServerProcessorGetThirdPartyToken{handler: handler}
	self76.processorMap["addThirdBandingInfo"] = &dosServerProcessorAddThirdBandingInfo{handler: handler}
	self76.processorMap["queryTopAccountId"] = &dosServerProcessorQueryTopAccountId{handler: handler}
	return self76
}

func (p *DosServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x77 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x77.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x77

}

type dosServerProcessorAddPlan struct {
	handler DosServer
}

func (p *dosServerProcessorAddPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddPlanResult()
	if result.Success, result.E, err = p.handler.AddPlan(args.Header, args.Plan); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addPlan: "+err.Error())
		oprot.WriteMessageBegin("addPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorEditPlan struct {
	handler DosServer
}

func (p *dosServerProcessorEditPlan) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditPlanArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditPlanResult()
	if result.E, err = p.handler.EditPlan(args.Header, args.Plan); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editPlan: "+err.Error())
		oprot.WriteMessageBegin("editPlan", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editPlan", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorGetPlansByIds struct {
	handler DosServer
}

func (p *dosServerProcessorGetPlansByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetPlansByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getPlansByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetPlansByIdsResult()
	if result.Success, result.E, err = p.handler.GetPlansByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getPlansByIds: "+err.Error())
		oprot.WriteMessageBegin("getPlansByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getPlansByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorListPlanIdsByDspCampaignId struct {
	handler DosServer
}

func (p *dosServerProcessorListPlanIdsByDspCampaignId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewListPlanIdsByDspCampaignIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("listPlanIdsByDspCampaignId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewListPlanIdsByDspCampaignIdResult()
	if result.Success, result.E, err = p.handler.ListPlanIdsByDspCampaignId(args.Header, args.DspCampaignId, args.ExcludeDeleted); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing listPlanIdsByDspCampaignId: "+err.Error())
		oprot.WriteMessageBegin("listPlanIdsByDspCampaignId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("listPlanIdsByDspCampaignId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorQueryPlanByParam struct {
	handler DosServer
}

func (p *dosServerProcessorQueryPlanByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryPlanByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryPlanByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryPlanByParamResult()
	if result.Success, result.E, err = p.handler.QueryPlanByParam(args.Header, args.Params, args.Order, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryPlanByParam: "+err.Error())
		oprot.WriteMessageBegin("queryPlanByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryPlanByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorPausePlansByIds struct {
	handler DosServer
}

func (p *dosServerProcessorPausePlansByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPausePlansByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("pausePlansByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPausePlansByIdsResult()
	if result.E, err = p.handler.PausePlansByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing pausePlansByIds: "+err.Error())
		oprot.WriteMessageBegin("pausePlansByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("pausePlansByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorResumePlansByIds struct {
	handler DosServer
}

func (p *dosServerProcessorResumePlansByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewResumePlansByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("resumePlansByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewResumePlansByIdsResult()
	if result.E, err = p.handler.ResumePlansByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing resumePlansByIds: "+err.Error())
		oprot.WriteMessageBegin("resumePlansByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("resumePlansByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorAddStrategy struct {
	handler DosServer
}

func (p *dosServerProcessorAddStrategy) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddStrategyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addStrategy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddStrategyResult()
	if result.Success, result.E, err = p.handler.AddStrategy(args.Header, args.Strategy); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addStrategy: "+err.Error())
		oprot.WriteMessageBegin("addStrategy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addStrategy", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorEditStrategy struct {
	handler DosServer
}

func (p *dosServerProcessorEditStrategy) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditStrategyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editStrategy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditStrategyResult()
	if result.E, err = p.handler.EditStrategy(args.Header, args.Strategy); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editStrategy: "+err.Error())
		oprot.WriteMessageBegin("editStrategy", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editStrategy", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorQueryStrategyByParam struct {
	handler DosServer
}

func (p *dosServerProcessorQueryStrategyByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryStrategyByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryStrategyByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryStrategyByParamResult()
	if result.Success, result.E, err = p.handler.QueryStrategyByParam(args.Header, args.Params, args.Order, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryStrategyByParam: "+err.Error())
		oprot.WriteMessageBegin("queryStrategyByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryStrategyByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorGetStrategiesByIds struct {
	handler DosServer
}

func (p *dosServerProcessorGetStrategiesByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetStrategiesByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getStrategiesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetStrategiesByIdsResult()
	if result.Success, result.E, err = p.handler.GetStrategiesByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getStrategiesByIds: "+err.Error())
		oprot.WriteMessageBegin("getStrategiesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getStrategiesByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorAddCreative struct {
	handler DosServer
}

func (p *dosServerProcessorAddCreative) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCreativeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCreativeResult()
	if result.Success, result.E, err = p.handler.AddCreative(args.Header, args.Creative); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addCreative: "+err.Error())
		oprot.WriteMessageBegin("addCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addCreative", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorEditCreative struct {
	handler DosServer
}

func (p *dosServerProcessorEditCreative) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditCreativeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditCreativeResult()
	if result.E, err = p.handler.EditCreative(args.Header, args.Creative); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editCreative: "+err.Error())
		oprot.WriteMessageBegin("editCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editCreative", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorQueryCreativeByParam struct {
	handler DosServer
}

func (p *dosServerProcessorQueryCreativeByParam) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryCreativeByParamArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryCreativeByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryCreativeByParamResult()
	if result.Success, result.E, err = p.handler.QueryCreativeByParam(args.Header, args.Params, args.Order, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryCreativeByParam: "+err.Error())
		oprot.WriteMessageBegin("queryCreativeByParam", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryCreativeByParam", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorGetCreativesByIds struct {
	handler DosServer
}

func (p *dosServerProcessorGetCreativesByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCreativesByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCreativesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCreativesByIdsResult()
	if result.Success, result.E, err = p.handler.GetCreativesByIds(args.Header, args.Ids, args.WithCache); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCreativesByIds: "+err.Error())
		oprot.WriteMessageBegin("getCreativesByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCreativesByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorAddThirdParty struct {
	handler DosServer
}

func (p *dosServerProcessorAddThirdParty) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddThirdPartyArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addThirdParty", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddThirdPartyResult()
	if result.Success, result.E, err = p.handler.AddThirdParty(args.Header, args.Third); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addThirdParty: "+err.Error())
		oprot.WriteMessageBegin("addThirdParty", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addThirdParty", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorGetThirdPartyToken struct {
	handler DosServer
}

func (p *dosServerProcessorGetThirdPartyToken) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetThirdPartyTokenArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getThirdPartyToken", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetThirdPartyTokenResult()
	if result.Success, result.E, err = p.handler.GetThirdPartyToken(args.Header, args.Token); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getThirdPartyToken: "+err.Error())
		oprot.WriteMessageBegin("getThirdPartyToken", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getThirdPartyToken", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorAddThirdBandingInfo struct {
	handler DosServer
}

func (p *dosServerProcessorAddThirdBandingInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddThirdBandingInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addThirdBandingInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddThirdBandingInfoResult()
	if result.E, err = p.handler.AddThirdBandingInfo(args.Header, args.Info); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addThirdBandingInfo: "+err.Error())
		oprot.WriteMessageBegin("addThirdBandingInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addThirdBandingInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dosServerProcessorQueryTopAccountId struct {
	handler DosServer
}

func (p *dosServerProcessorQueryTopAccountId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryTopAccountIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryTopAccountId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryTopAccountIdResult()
	if result.Success, result.E, err = p.handler.QueryTopAccountId(args.Header, args.SubAccountId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryTopAccountId: "+err.Error())
		oprot.WriteMessageBegin("queryTopAccountId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryTopAccountId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Plan   *dos_types.DOSPlan    `thrift:"plan,2" json:"plan"`
}

func NewAddPlanArgs() *AddPlanArgs {
	return &AddPlanArgs{}
}

func (p *AddPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddPlanArgs) readField2(iprot thrift.TProtocol) error {
	p.Plan = dos_types.NewDOSPlan()
	if err := p.Plan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Plan)
	}
	return nil
}

func (p *AddPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Plan != nil {
		if err := oprot.WriteFieldBegin("plan", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:plan: %s", p, err)
		}
		if err := p.Plan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Plan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:plan: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPlanArgs(%+v)", *p)
}

type AddPlanResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewAddPlanResult() *AddPlanResult {
	return &AddPlanResult{}
}

func (p *AddPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddPlanResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddPlanResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddPlanResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddPlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddPlanResult(%+v)", *p)
}

type EditPlanArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Plan   *dos_types.DOSPlan    `thrift:"plan,2" json:"plan"`
}

func NewEditPlanArgs() *EditPlanArgs {
	return &EditPlanArgs{}
}

func (p *EditPlanArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPlanArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditPlanArgs) readField2(iprot thrift.TProtocol) error {
	p.Plan = dos_types.NewDOSPlan()
	if err := p.Plan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Plan)
	}
	return nil
}

func (p *EditPlanArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPlan_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPlanArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditPlanArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Plan != nil {
		if err := oprot.WriteFieldBegin("plan", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:plan: %s", p, err)
		}
		if err := p.Plan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Plan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:plan: %s", p, err)
		}
	}
	return err
}

func (p *EditPlanArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPlanArgs(%+v)", *p)
}

type EditPlanResult struct {
	E *DosServerException `thrift:"e,1" json:"e"`
}

func NewEditPlanResult() *EditPlanResult {
	return &EditPlanResult{}
}

func (p *EditPlanResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditPlanResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditPlanResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editPlan_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditPlanResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditPlanResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditPlanResult(%+v)", *p)
}

type GetPlansByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetPlansByIdsArgs() *GetPlansByIdsArgs {
	return &GetPlansByIdsArgs{}
}

func (p *GetPlansByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetPlansByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem78 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem78 = v
		}
		p.Ids = append(p.Ids, _elem78)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetPlansByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlansByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetPlansByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetPlansByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlansByIdsArgs(%+v)", *p)
}

type GetPlansByIdsResult struct {
	Success map[int32]*dos_types.DOSPlan `thrift:"success,0" json:"success"`
	E       *DosServerException          `thrift:"e,1" json:"e"`
}

func NewGetPlansByIdsResult() *GetPlansByIdsResult {
	return &GetPlansByIdsResult{}
}

func (p *GetPlansByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dos_types.DOSPlan, size)
	for i := 0; i < size; i++ {
		var _key79 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key79 = v
		}
		_val80 := dos_types.NewDOSPlan()
		if err := _val80.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val80)
		}
		p.Success[_key79] = _val80
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetPlansByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetPlansByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getPlansByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetPlansByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetPlansByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetPlansByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetPlansByIdsResult(%+v)", *p)
}

type ListPlanIdsByDspCampaignIdArgs struct {
	Header         *common.RequestHeader `thrift:"header,1" json:"header"`
	DspCampaignId  int32                 `thrift:"dspCampaignId,2" json:"dspCampaignId"`
	ExcludeDeleted bool                  `thrift:"excludeDeleted,3" json:"excludeDeleted"`
}

func NewListPlanIdsByDspCampaignIdArgs() *ListPlanIdsByDspCampaignIdArgs {
	return &ListPlanIdsByDspCampaignIdArgs{}
}

func (p *ListPlanIdsByDspCampaignIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DspCampaignId = v
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExcludeDeleted = v
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPlanIdsByDspCampaignId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ListPlanIdsByDspCampaignIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dspCampaignId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dspCampaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspCampaignId)); err != nil {
		return fmt.Errorf("%T.dspCampaignId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dspCampaignId: %s", p, err)
	}
	return err
}

func (p *ListPlanIdsByDspCampaignIdArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("excludeDeleted", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:excludeDeleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExcludeDeleted)); err != nil {
		return fmt.Errorf("%T.excludeDeleted (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:excludeDeleted: %s", p, err)
	}
	return err
}

func (p *ListPlanIdsByDspCampaignIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPlanIdsByDspCampaignIdArgs(%+v)", *p)
}

type ListPlanIdsByDspCampaignIdResult struct {
	Success []int32             `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewListPlanIdsByDspCampaignIdResult() *ListPlanIdsByDspCampaignIdResult {
	return &ListPlanIdsByDspCampaignIdResult{}
}

func (p *ListPlanIdsByDspCampaignIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem81 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem81 = v
		}
		p.Success = append(p.Success, _elem81)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("listPlanIdsByDspCampaignId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ListPlanIdsByDspCampaignIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ListPlanIdsByDspCampaignIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ListPlanIdsByDspCampaignIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ListPlanIdsByDspCampaignIdResult(%+v)", *p)
}

type QueryPlanByParamArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Params *dos_types.QueryParam `thrift:"params,2" json:"params"`
	Order  *dos_types.OrderParam `thrift:"order,3" json:"order"`
	Offset int32                 `thrift:"offset,4" json:"offset"`
	Limit  int32                 `thrift:"limit,5" json:"limit"`
}

func NewQueryPlanByParamArgs() *QueryPlanByParamArgs {
	return &QueryPlanByParamArgs{}
}

func (p *QueryPlanByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryPlanByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryPlanByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = dos_types.NewQueryParam()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *QueryPlanByParamArgs) readField3(iprot thrift.TProtocol) error {
	p.Order = dos_types.NewOrderParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryPlanByParamArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryPlanByParamArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryPlanByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryPlanByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryPlanByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryPlanByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *QueryPlanByParamArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryPlanByParamArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *QueryPlanByParamArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *QueryPlanByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryPlanByParamArgs(%+v)", *p)
}

type QueryPlanByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewQueryPlanByParamResult() *QueryPlanByParamResult {
	return &QueryPlanByParamResult{}
}

func (p *QueryPlanByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryPlanByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryPlanByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryPlanByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryPlanByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryPlanByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryPlanByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryPlanByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryPlanByParamResult(%+v)", *p)
}

type PausePlansByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewPausePlansByIdsArgs() *PausePlansByIdsArgs {
	return &PausePlansByIdsArgs{}
}

func (p *PausePlansByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PausePlansByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PausePlansByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem82 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem82 = v
		}
		p.Ids = append(p.Ids, _elem82)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PausePlansByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("pausePlansByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PausePlansByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PausePlansByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *PausePlansByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PausePlansByIdsArgs(%+v)", *p)
}

type PausePlansByIdsResult struct {
	E *DosServerException `thrift:"e,1" json:"e"`
}

func NewPausePlansByIdsResult() *PausePlansByIdsResult {
	return &PausePlansByIdsResult{}
}

func (p *PausePlansByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PausePlansByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *PausePlansByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("pausePlansByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PausePlansByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *PausePlansByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PausePlansByIdsResult(%+v)", *p)
}

type ResumePlansByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewResumePlansByIdsArgs() *ResumePlansByIdsArgs {
	return &ResumePlansByIdsArgs{}
}

func (p *ResumePlansByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResumePlansByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ResumePlansByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem83 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem83 = v
		}
		p.Ids = append(p.Ids, _elem83)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ResumePlansByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resumePlansByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResumePlansByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ResumePlansByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *ResumePlansByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResumePlansByIdsArgs(%+v)", *p)
}

type ResumePlansByIdsResult struct {
	E *DosServerException `thrift:"e,1" json:"e"`
}

func NewResumePlansByIdsResult() *ResumePlansByIdsResult {
	return &ResumePlansByIdsResult{}
}

func (p *ResumePlansByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResumePlansByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ResumePlansByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("resumePlansByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResumePlansByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ResumePlansByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResumePlansByIdsResult(%+v)", *p)
}

type AddStrategyArgs struct {
	Header   *common.RequestHeader  `thrift:"header,1" json:"header"`
	Strategy *dos_types.DOSStrategy `thrift:"strategy,2" json:"strategy"`
}

func NewAddStrategyArgs() *AddStrategyArgs {
	return &AddStrategyArgs{}
}

func (p *AddStrategyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddStrategyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddStrategyArgs) readField2(iprot thrift.TProtocol) error {
	p.Strategy = dos_types.NewDOSStrategy()
	if err := p.Strategy.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Strategy)
	}
	return nil
}

func (p *AddStrategyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addStrategy_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddStrategyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddStrategyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Strategy != nil {
		if err := oprot.WriteFieldBegin("strategy", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:strategy: %s", p, err)
		}
		if err := p.Strategy.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Strategy)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:strategy: %s", p, err)
		}
	}
	return err
}

func (p *AddStrategyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddStrategyArgs(%+v)", *p)
}

type AddStrategyResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewAddStrategyResult() *AddStrategyResult {
	return &AddStrategyResult{}
}

func (p *AddStrategyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddStrategyResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddStrategyResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddStrategyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addStrategy_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddStrategyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddStrategyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddStrategyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddStrategyResult(%+v)", *p)
}

type EditStrategyArgs struct {
	Header   *common.RequestHeader  `thrift:"header,1" json:"header"`
	Strategy *dos_types.DOSStrategy `thrift:"strategy,2" json:"strategy"`
}

func NewEditStrategyArgs() *EditStrategyArgs {
	return &EditStrategyArgs{}
}

func (p *EditStrategyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditStrategyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditStrategyArgs) readField2(iprot thrift.TProtocol) error {
	p.Strategy = dos_types.NewDOSStrategy()
	if err := p.Strategy.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Strategy)
	}
	return nil
}

func (p *EditStrategyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editStrategy_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditStrategyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditStrategyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Strategy != nil {
		if err := oprot.WriteFieldBegin("strategy", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:strategy: %s", p, err)
		}
		if err := p.Strategy.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Strategy)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:strategy: %s", p, err)
		}
	}
	return err
}

func (p *EditStrategyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditStrategyArgs(%+v)", *p)
}

type EditStrategyResult struct {
	E *DosServerException `thrift:"e,1" json:"e"`
}

func NewEditStrategyResult() *EditStrategyResult {
	return &EditStrategyResult{}
}

func (p *EditStrategyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditStrategyResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditStrategyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editStrategy_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditStrategyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditStrategyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditStrategyResult(%+v)", *p)
}

type QueryStrategyByParamArgs struct {
	Header *common.RequestHeader         `thrift:"header,1" json:"header"`
	Params *dos_types.StrategyQueryParam `thrift:"params,2" json:"params"`
	Order  *dos_types.StrategyOrderParam `thrift:"order,3" json:"order"`
	Offset int32                         `thrift:"offset,4" json:"offset"`
	Limit  int32                         `thrift:"limit,5" json:"limit"`
}

func NewQueryStrategyByParamArgs() *QueryStrategyByParamArgs {
	return &QueryStrategyByParamArgs{}
}

func (p *QueryStrategyByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStrategyByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryStrategyByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = dos_types.NewStrategyQueryParam()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *QueryStrategyByParamArgs) readField3(iprot thrift.TProtocol) error {
	p.Order = dos_types.NewStrategyOrderParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryStrategyByParamArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryStrategyByParamArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryStrategyByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStrategyByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStrategyByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryStrategyByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *QueryStrategyByParamArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryStrategyByParamArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *QueryStrategyByParamArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *QueryStrategyByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStrategyByParamArgs(%+v)", *p)
}

type QueryStrategyByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewQueryStrategyByParamResult() *QueryStrategyByParamResult {
	return &QueryStrategyByParamResult{}
}

func (p *QueryStrategyByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryStrategyByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryStrategyByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryStrategyByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryStrategyByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryStrategyByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryStrategyByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryStrategyByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryStrategyByParamResult(%+v)", *p)
}

type GetStrategiesByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetStrategiesByIdsArgs() *GetStrategiesByIdsArgs {
	return &GetStrategiesByIdsArgs{}
}

func (p *GetStrategiesByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetStrategiesByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetStrategiesByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem84 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem84 = v
		}
		p.Ids = append(p.Ids, _elem84)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetStrategiesByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getStrategiesByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetStrategiesByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetStrategiesByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetStrategiesByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetStrategiesByIdsArgs(%+v)", *p)
}

type GetStrategiesByIdsResult struct {
	Success map[int32]*dos_types.DOSStrategy `thrift:"success,0" json:"success"`
	E       *DosServerException              `thrift:"e,1" json:"e"`
}

func NewGetStrategiesByIdsResult() *GetStrategiesByIdsResult {
	return &GetStrategiesByIdsResult{}
}

func (p *GetStrategiesByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetStrategiesByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dos_types.DOSStrategy, size)
	for i := 0; i < size; i++ {
		var _key85 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key85 = v
		}
		_val86 := dos_types.NewDOSStrategy()
		if err := _val86.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val86)
		}
		p.Success[_key85] = _val86
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetStrategiesByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetStrategiesByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getStrategiesByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetStrategiesByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetStrategiesByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetStrategiesByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetStrategiesByIdsResult(%+v)", *p)
}

type AddCreativeArgs struct {
	Header   *common.RequestHeader  `thrift:"header,1" json:"header"`
	Creative *dos_types.DOSCreative `thrift:"creative,2" json:"creative"`
}

func NewAddCreativeArgs() *AddCreativeArgs {
	return &AddCreativeArgs{}
}

func (p *AddCreativeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddCreativeArgs) readField2(iprot thrift.TProtocol) error {
	p.Creative = dos_types.NewDOSCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *AddCreativeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCreative_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddCreativeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:creative: %s", p, err)
		}
	}
	return err
}

func (p *AddCreativeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCreativeArgs(%+v)", *p)
}

type AddCreativeResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewAddCreativeResult() *AddCreativeResult {
	return &AddCreativeResult{}
}

func (p *AddCreativeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddCreativeResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddCreativeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addCreative_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCreativeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddCreativeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddCreativeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCreativeResult(%+v)", *p)
}

type EditCreativeArgs struct {
	Header   *common.RequestHeader  `thrift:"header,1" json:"header"`
	Creative *dos_types.DOSCreative `thrift:"creative,2" json:"creative"`
}

func NewEditCreativeArgs() *EditCreativeArgs {
	return &EditCreativeArgs{}
}

func (p *EditCreativeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCreativeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditCreativeArgs) readField2(iprot thrift.TProtocol) error {
	p.Creative = dos_types.NewDOSCreative()
	if err := p.Creative.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Creative)
	}
	return nil
}

func (p *EditCreativeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCreative_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCreativeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditCreativeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Creative != nil {
		if err := oprot.WriteFieldBegin("creative", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:creative: %s", p, err)
		}
		if err := p.Creative.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Creative)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:creative: %s", p, err)
		}
	}
	return err
}

func (p *EditCreativeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCreativeArgs(%+v)", *p)
}

type EditCreativeResult struct {
	E *DosServerException `thrift:"e,1" json:"e"`
}

func NewEditCreativeResult() *EditCreativeResult {
	return &EditCreativeResult{}
}

func (p *EditCreativeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditCreativeResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditCreativeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editCreative_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditCreativeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditCreativeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditCreativeResult(%+v)", *p)
}

type QueryCreativeByParamArgs struct {
	Header *common.RequestHeader         `thrift:"header,1" json:"header"`
	Params *dos_types.CreativeQueryParam `thrift:"params,2" json:"params"`
	Order  *dos_types.CreativeOrderParam `thrift:"order,3" json:"order"`
	Offset int32                         `thrift:"offset,4" json:"offset"`
	Limit  int32                         `thrift:"limit,5" json:"limit"`
}

func NewQueryCreativeByParamArgs() *QueryCreativeByParamArgs {
	return &QueryCreativeByParamArgs{}
}

func (p *QueryCreativeByParamArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryCreativeByParamArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryCreativeByParamArgs) readField2(iprot thrift.TProtocol) error {
	p.Params = dos_types.NewCreativeQueryParam()
	if err := p.Params.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Params)
	}
	return nil
}

func (p *QueryCreativeByParamArgs) readField3(iprot thrift.TProtocol) error {
	p.Order = dos_types.NewCreativeOrderParam()
	if err := p.Order.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Order)
	}
	return nil
}

func (p *QueryCreativeByParamArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryCreativeByParamArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryCreativeByParamArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryCreativeByParam_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryCreativeByParamArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryCreativeByParamArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:params: %s", p, err)
		}
		if err := p.Params.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Params)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:params: %s", p, err)
		}
	}
	return err
}

func (p *QueryCreativeByParamArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Order != nil {
		if err := oprot.WriteFieldBegin("order", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:order: %s", p, err)
		}
		if err := p.Order.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Order)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:order: %s", p, err)
		}
	}
	return err
}

func (p *QueryCreativeByParamArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:offset: %s", p, err)
	}
	return err
}

func (p *QueryCreativeByParamArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:limit: %s", p, err)
	}
	return err
}

func (p *QueryCreativeByParamArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryCreativeByParamArgs(%+v)", *p)
}

type QueryCreativeByParamResult struct {
	Success *common.QueryResult `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewQueryCreativeByParamResult() *QueryCreativeByParamResult {
	return &QueryCreativeByParamResult{}
}

func (p *QueryCreativeByParamResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryCreativeByParamResult) readField0(iprot thrift.TProtocol) error {
	p.Success = common.NewQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryCreativeByParamResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryCreativeByParamResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryCreativeByParam_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryCreativeByParamResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryCreativeByParamResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryCreativeByParamResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryCreativeByParamResult(%+v)", *p)
}

type GetCreativesByIdsArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids       []int32               `thrift:"ids,2" json:"ids"`
	WithCache bool                  `thrift:"withCache,3" json:"withCache"`
}

func NewGetCreativesByIdsArgs() *GetCreativesByIdsArgs {
	return &GetCreativesByIdsArgs{}
}

func (p *GetCreativesByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetCreativesByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem87 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem87 = v
		}
		p.Ids = append(p.Ids, _elem87)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetCreativesByIdsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.WithCache = v
	}
	return nil
}

func (p *GetCreativesByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCreativesByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesByIdsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("withCache", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:withCache: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithCache)); err != nil {
		return fmt.Errorf("%T.withCache (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:withCache: %s", p, err)
	}
	return err
}

func (p *GetCreativesByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCreativesByIdsArgs(%+v)", *p)
}

type GetCreativesByIdsResult struct {
	Success map[int32]*dos_types.DOSCreative `thrift:"success,0" json:"success"`
	E       *DosServerException              `thrift:"e,1" json:"e"`
}

func NewGetCreativesByIdsResult() *GetCreativesByIdsResult {
	return &GetCreativesByIdsResult{}
}

func (p *GetCreativesByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*dos_types.DOSCreative, size)
	for i := 0; i < size; i++ {
		var _key88 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key88 = v
		}
		_val89 := dos_types.NewDOSCreative()
		if err := _val89.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val89)
		}
		p.Success[_key88] = _val89
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetCreativesByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetCreativesByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCreativesByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCreativesByIdsResult(%+v)", *p)
}

type AddThirdPartyArgs struct {
	Header *common.RequestHeader    `thrift:"header,1" json:"header"`
	Third  *dos_types.DOSThirdParty `thrift:"third,2" json:"third"`
}

func NewAddThirdPartyArgs() *AddThirdPartyArgs {
	return &AddThirdPartyArgs{}
}

func (p *AddThirdPartyArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddThirdPartyArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddThirdPartyArgs) readField2(iprot thrift.TProtocol) error {
	p.Third = dos_types.NewDOSThirdParty()
	if err := p.Third.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Third)
	}
	return nil
}

func (p *AddThirdPartyArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addThirdParty_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddThirdPartyArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddThirdPartyArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Third != nil {
		if err := oprot.WriteFieldBegin("third", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:third: %s", p, err)
		}
		if err := p.Third.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Third)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:third: %s", p, err)
		}
	}
	return err
}

func (p *AddThirdPartyArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddThirdPartyArgs(%+v)", *p)
}

type AddThirdPartyResult struct {
	Success int32               `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewAddThirdPartyResult() *AddThirdPartyResult {
	return &AddThirdPartyResult{}
}

func (p *AddThirdPartyResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddThirdPartyResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddThirdPartyResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddThirdPartyResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addThirdParty_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddThirdPartyResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddThirdPartyResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddThirdPartyResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddThirdPartyResult(%+v)", *p)
}

type GetThirdPartyTokenArgs struct {
	Header *common.RequestHeader           `thrift:"header,1" json:"header"`
	Token  *dos_types.QueryThirdPartyToken `thrift:"token,2" json:"token"`
}

func NewGetThirdPartyTokenArgs() *GetThirdPartyTokenArgs {
	return &GetThirdPartyTokenArgs{}
}

func (p *GetThirdPartyTokenArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetThirdPartyTokenArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetThirdPartyTokenArgs) readField2(iprot thrift.TProtocol) error {
	p.Token = dos_types.NewQueryThirdPartyToken()
	if err := p.Token.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Token)
	}
	return nil
}

func (p *GetThirdPartyTokenArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getThirdPartyToken_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetThirdPartyTokenArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetThirdPartyTokenArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Token != nil {
		if err := oprot.WriteFieldBegin("token", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:token: %s", p, err)
		}
		if err := p.Token.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Token)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:token: %s", p, err)
		}
	}
	return err
}

func (p *GetThirdPartyTokenArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetThirdPartyTokenArgs(%+v)", *p)
}

type GetThirdPartyTokenResult struct {
	Success *dos_types.DOSQueryThirdPartyTokenResult `thrift:"success,0" json:"success"`
	E       *DosServerException                      `thrift:"e,1" json:"e"`
}

func NewGetThirdPartyTokenResult() *GetThirdPartyTokenResult {
	return &GetThirdPartyTokenResult{}
}

func (p *GetThirdPartyTokenResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetThirdPartyTokenResult) readField0(iprot thrift.TProtocol) error {
	p.Success = dos_types.NewDOSQueryThirdPartyTokenResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetThirdPartyTokenResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetThirdPartyTokenResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getThirdPartyToken_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetThirdPartyTokenResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetThirdPartyTokenResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetThirdPartyTokenResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetThirdPartyTokenResult(%+v)", *p)
}

type AddThirdBandingInfoArgs struct {
	Header *common.RequestHeader          `thrift:"header,1" json:"header"`
	Info   *dos_types.DOSThirdBandingInfo `thrift:"info,2" json:"info"`
}

func NewAddThirdBandingInfoArgs() *AddThirdBandingInfoArgs {
	return &AddThirdBandingInfoArgs{}
}

func (p *AddThirdBandingInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddThirdBandingInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddThirdBandingInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.Info = dos_types.NewDOSThirdBandingInfo()
	if err := p.Info.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Info)
	}
	return nil
}

func (p *AddThirdBandingInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addThirdBandingInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddThirdBandingInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddThirdBandingInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Info != nil {
		if err := oprot.WriteFieldBegin("info", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:info: %s", p, err)
		}
		if err := p.Info.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Info)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:info: %s", p, err)
		}
	}
	return err
}

func (p *AddThirdBandingInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddThirdBandingInfoArgs(%+v)", *p)
}

type AddThirdBandingInfoResult struct {
	E *DosServerException `thrift:"e,1" json:"e"`
}

func NewAddThirdBandingInfoResult() *AddThirdBandingInfoResult {
	return &AddThirdBandingInfoResult{}
}

func (p *AddThirdBandingInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddThirdBandingInfoResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddThirdBandingInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addThirdBandingInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddThirdBandingInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddThirdBandingInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddThirdBandingInfoResult(%+v)", *p)
}

type QueryTopAccountIdArgs struct {
	Header       *common.RequestHeader `thrift:"header,1" json:"header"`
	SubAccountId int64                 `thrift:"SubAccountId,2" json:"SubAccountId"`
}

func NewQueryTopAccountIdArgs() *QueryTopAccountIdArgs {
	return &QueryTopAccountIdArgs{}
}

func (p *QueryTopAccountIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryTopAccountIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryTopAccountIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SubAccountId = v
	}
	return nil
}

func (p *QueryTopAccountIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryTopAccountId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryTopAccountIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryTopAccountIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("SubAccountId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:SubAccountId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SubAccountId)); err != nil {
		return fmt.Errorf("%T.SubAccountId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:SubAccountId: %s", p, err)
	}
	return err
}

func (p *QueryTopAccountIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryTopAccountIdArgs(%+v)", *p)
}

type QueryTopAccountIdResult struct {
	Success int64               `thrift:"success,0" json:"success"`
	E       *DosServerException `thrift:"e,1" json:"e"`
}

func NewQueryTopAccountIdResult() *QueryTopAccountIdResult {
	return &QueryTopAccountIdResult{}
}

func (p *QueryTopAccountIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryTopAccountIdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *QueryTopAccountIdResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewDosServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryTopAccountIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryTopAccountId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryTopAccountIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *QueryTopAccountIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryTopAccountIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryTopAccountIdResult(%+v)", *p)
}
