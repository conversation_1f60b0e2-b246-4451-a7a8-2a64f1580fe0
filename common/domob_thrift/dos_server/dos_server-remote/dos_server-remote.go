// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dos_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addPlan(RequestHeader header, DOSPlan plan)")
	fmt.Fprintln(os.Stderr, "  void editPlan(RequestHeader header, DOSPlan plan)")
	fmt.Fprintln(os.<PERSON>, "   getPlansByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   listPlanIdsByDspCampaignId(RequestHeader header, i32 dspCampaignId, bool excludeDeleted)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryPlanByParam(RequestHeader header, QueryParam params, OrderParam order, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  void pausePlansByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumePlansByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addStrategy(RequestHeader header, DOSStrategy strategy)")
	fmt.Fprintln(os.Stderr, "  void editStrategy(RequestHeader header, DOSStrategy strategy)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryStrategyByParam(RequestHeader header, StrategyQueryParam params, StrategyOrderParam order, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCreative(RequestHeader header, DOSCreative creative)")
	fmt.Fprintln(os.Stderr, "  void editCreative(RequestHeader header, DOSCreative creative)")
	fmt.Fprintln(os.Stderr, "  QueryResult queryCreativeByParam(RequestHeader header, CreativeQueryParam params, CreativeOrderParam order, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getCreativesByIds(RequestHeader header,  ids, bool withCache)")
	fmt.Fprintln(os.Stderr, "  i32 addThirdParty(RequestHeader header, DOSThirdParty third)")
	fmt.Fprintln(os.Stderr, "  DOSQueryThirdPartyTokenResult getThirdPartyToken(RequestHeader header, QueryThirdPartyToken token)")
	fmt.Fprintln(os.Stderr, "  void addThirdBandingInfo(RequestHeader header, DOSThirdBandingInfo info)")
	fmt.Fprintln(os.Stderr, "  i64 queryTopAccountId(RequestHeader header, i64 SubAccountId)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dos_server.NewDosServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPlan requires 2 args")
			flag.Usage()
		}
		arg90 := flag.Arg(1)
		mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
		defer mbTrans91.Close()
		_, err92 := mbTrans91.WriteString(arg90)
		if err92 != nil {
			Usage()
			return
		}
		factory93 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt94 := factory93.GetProtocol(mbTrans91)
		argvalue0 := dos_server.NewRequestHeader()
		err95 := argvalue0.Read(jsProt94)
		if err95 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg96 := flag.Arg(2)
		mbTrans97 := thrift.NewTMemoryBufferLen(len(arg96))
		defer mbTrans97.Close()
		_, err98 := mbTrans97.WriteString(arg96)
		if err98 != nil {
			Usage()
			return
		}
		factory99 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt100 := factory99.GetProtocol(mbTrans97)
		argvalue1 := dos_server.NewDOSPlan()
		err101 := argvalue1.Read(jsProt100)
		if err101 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPlan(value0, value1))
		fmt.Print("\n")
		break
	case "editPlan":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPlan requires 2 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := dos_server.NewRequestHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg108 := flag.Arg(2)
		mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
		defer mbTrans109.Close()
		_, err110 := mbTrans109.WriteString(arg108)
		if err110 != nil {
			Usage()
			return
		}
		factory111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt112 := factory111.GetProtocol(mbTrans109)
		argvalue1 := dos_server.NewDOSPlan()
		err113 := argvalue1.Read(jsProt112)
		if err113 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditPlan(value0, value1))
		fmt.Print("\n")
		break
	case "getPlansByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPlansByIds requires 2 args")
			flag.Usage()
		}
		arg114 := flag.Arg(1)
		mbTrans115 := thrift.NewTMemoryBufferLen(len(arg114))
		defer mbTrans115.Close()
		_, err116 := mbTrans115.WriteString(arg114)
		if err116 != nil {
			Usage()
			return
		}
		factory117 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt118 := factory117.GetProtocol(mbTrans115)
		argvalue0 := dos_server.NewRequestHeader()
		err119 := argvalue0.Read(jsProt118)
		if err119 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg120 := flag.Arg(2)
		mbTrans121 := thrift.NewTMemoryBufferLen(len(arg120))
		defer mbTrans121.Close()
		_, err122 := mbTrans121.WriteString(arg120)
		if err122 != nil {
			Usage()
			return
		}
		factory123 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt124 := factory123.GetProtocol(mbTrans121)
		containerStruct1 := dos_server.NewGetPlansByIdsArgs()
		err125 := containerStruct1.ReadField2(jsProt124)
		if err125 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPlansByIds(value0, value1))
		fmt.Print("\n")
		break
	case "listPlanIdsByDspCampaignId":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ListPlanIdsByDspCampaignId requires 3 args")
			flag.Usage()
		}
		arg126 := flag.Arg(1)
		mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
		defer mbTrans127.Close()
		_, err128 := mbTrans127.WriteString(arg126)
		if err128 != nil {
			Usage()
			return
		}
		factory129 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt130 := factory129.GetProtocol(mbTrans127)
		argvalue0 := dos_server.NewRequestHeader()
		err131 := argvalue0.Read(jsProt130)
		if err131 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err132 := (strconv.Atoi(flag.Arg(2)))
		if err132 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.ListPlanIdsByDspCampaignId(value0, value1, value2))
		fmt.Print("\n")
		break
	case "queryPlanByParam":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryPlanByParam requires 5 args")
			flag.Usage()
		}
		arg134 := flag.Arg(1)
		mbTrans135 := thrift.NewTMemoryBufferLen(len(arg134))
		defer mbTrans135.Close()
		_, err136 := mbTrans135.WriteString(arg134)
		if err136 != nil {
			Usage()
			return
		}
		factory137 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt138 := factory137.GetProtocol(mbTrans135)
		argvalue0 := dos_server.NewRequestHeader()
		err139 := argvalue0.Read(jsProt138)
		if err139 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg140 := flag.Arg(2)
		mbTrans141 := thrift.NewTMemoryBufferLen(len(arg140))
		defer mbTrans141.Close()
		_, err142 := mbTrans141.WriteString(arg140)
		if err142 != nil {
			Usage()
			return
		}
		factory143 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt144 := factory143.GetProtocol(mbTrans141)
		argvalue1 := dos_server.NewQueryParam()
		err145 := argvalue1.Read(jsProt144)
		if err145 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg146 := flag.Arg(3)
		mbTrans147 := thrift.NewTMemoryBufferLen(len(arg146))
		defer mbTrans147.Close()
		_, err148 := mbTrans147.WriteString(arg146)
		if err148 != nil {
			Usage()
			return
		}
		factory149 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt150 := factory149.GetProtocol(mbTrans147)
		argvalue2 := dos_server.NewOrderParam()
		err151 := argvalue2.Read(jsProt150)
		if err151 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err152 := (strconv.Atoi(flag.Arg(4)))
		if err152 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err153 := (strconv.Atoi(flag.Arg(5)))
		if err153 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryPlanByParam(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "pausePlansByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PausePlansByIds requires 2 args")
			flag.Usage()
		}
		arg154 := flag.Arg(1)
		mbTrans155 := thrift.NewTMemoryBufferLen(len(arg154))
		defer mbTrans155.Close()
		_, err156 := mbTrans155.WriteString(arg154)
		if err156 != nil {
			Usage()
			return
		}
		factory157 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt158 := factory157.GetProtocol(mbTrans155)
		argvalue0 := dos_server.NewRequestHeader()
		err159 := argvalue0.Read(jsProt158)
		if err159 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg160 := flag.Arg(2)
		mbTrans161 := thrift.NewTMemoryBufferLen(len(arg160))
		defer mbTrans161.Close()
		_, err162 := mbTrans161.WriteString(arg160)
		if err162 != nil {
			Usage()
			return
		}
		factory163 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt164 := factory163.GetProtocol(mbTrans161)
		containerStruct1 := dos_server.NewPausePlansByIdsArgs()
		err165 := containerStruct1.ReadField2(jsProt164)
		if err165 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PausePlansByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumePlansByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumePlansByIds requires 2 args")
			flag.Usage()
		}
		arg166 := flag.Arg(1)
		mbTrans167 := thrift.NewTMemoryBufferLen(len(arg166))
		defer mbTrans167.Close()
		_, err168 := mbTrans167.WriteString(arg166)
		if err168 != nil {
			Usage()
			return
		}
		factory169 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt170 := factory169.GetProtocol(mbTrans167)
		argvalue0 := dos_server.NewRequestHeader()
		err171 := argvalue0.Read(jsProt170)
		if err171 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg172 := flag.Arg(2)
		mbTrans173 := thrift.NewTMemoryBufferLen(len(arg172))
		defer mbTrans173.Close()
		_, err174 := mbTrans173.WriteString(arg172)
		if err174 != nil {
			Usage()
			return
		}
		factory175 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt176 := factory175.GetProtocol(mbTrans173)
		containerStruct1 := dos_server.NewResumePlansByIdsArgs()
		err177 := containerStruct1.ReadField2(jsProt176)
		if err177 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumePlansByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddStrategy requires 2 args")
			flag.Usage()
		}
		arg178 := flag.Arg(1)
		mbTrans179 := thrift.NewTMemoryBufferLen(len(arg178))
		defer mbTrans179.Close()
		_, err180 := mbTrans179.WriteString(arg178)
		if err180 != nil {
			Usage()
			return
		}
		factory181 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt182 := factory181.GetProtocol(mbTrans179)
		argvalue0 := dos_server.NewRequestHeader()
		err183 := argvalue0.Read(jsProt182)
		if err183 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg184 := flag.Arg(2)
		mbTrans185 := thrift.NewTMemoryBufferLen(len(arg184))
		defer mbTrans185.Close()
		_, err186 := mbTrans185.WriteString(arg184)
		if err186 != nil {
			Usage()
			return
		}
		factory187 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt188 := factory187.GetProtocol(mbTrans185)
		argvalue1 := dos_server.NewDOSStrategy()
		err189 := argvalue1.Read(jsProt188)
		if err189 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "editStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditStrategy requires 2 args")
			flag.Usage()
		}
		arg190 := flag.Arg(1)
		mbTrans191 := thrift.NewTMemoryBufferLen(len(arg190))
		defer mbTrans191.Close()
		_, err192 := mbTrans191.WriteString(arg190)
		if err192 != nil {
			Usage()
			return
		}
		factory193 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt194 := factory193.GetProtocol(mbTrans191)
		argvalue0 := dos_server.NewRequestHeader()
		err195 := argvalue0.Read(jsProt194)
		if err195 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg196 := flag.Arg(2)
		mbTrans197 := thrift.NewTMemoryBufferLen(len(arg196))
		defer mbTrans197.Close()
		_, err198 := mbTrans197.WriteString(arg196)
		if err198 != nil {
			Usage()
			return
		}
		factory199 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt200 := factory199.GetProtocol(mbTrans197)
		argvalue1 := dos_server.NewDOSStrategy()
		err201 := argvalue1.Read(jsProt200)
		if err201 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "queryStrategyByParam":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryStrategyByParam requires 5 args")
			flag.Usage()
		}
		arg202 := flag.Arg(1)
		mbTrans203 := thrift.NewTMemoryBufferLen(len(arg202))
		defer mbTrans203.Close()
		_, err204 := mbTrans203.WriteString(arg202)
		if err204 != nil {
			Usage()
			return
		}
		factory205 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt206 := factory205.GetProtocol(mbTrans203)
		argvalue0 := dos_server.NewRequestHeader()
		err207 := argvalue0.Read(jsProt206)
		if err207 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg208 := flag.Arg(2)
		mbTrans209 := thrift.NewTMemoryBufferLen(len(arg208))
		defer mbTrans209.Close()
		_, err210 := mbTrans209.WriteString(arg208)
		if err210 != nil {
			Usage()
			return
		}
		factory211 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt212 := factory211.GetProtocol(mbTrans209)
		argvalue1 := dos_server.NewStrategyQueryParam()
		err213 := argvalue1.Read(jsProt212)
		if err213 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg214 := flag.Arg(3)
		mbTrans215 := thrift.NewTMemoryBufferLen(len(arg214))
		defer mbTrans215.Close()
		_, err216 := mbTrans215.WriteString(arg214)
		if err216 != nil {
			Usage()
			return
		}
		factory217 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt218 := factory217.GetProtocol(mbTrans215)
		argvalue2 := dos_server.NewStrategyOrderParam()
		err219 := argvalue2.Read(jsProt218)
		if err219 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err220 := (strconv.Atoi(flag.Arg(4)))
		if err220 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err221 := (strconv.Atoi(flag.Arg(5)))
		if err221 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryStrategyByParam(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg222 := flag.Arg(1)
		mbTrans223 := thrift.NewTMemoryBufferLen(len(arg222))
		defer mbTrans223.Close()
		_, err224 := mbTrans223.WriteString(arg222)
		if err224 != nil {
			Usage()
			return
		}
		factory225 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt226 := factory225.GetProtocol(mbTrans223)
		argvalue0 := dos_server.NewRequestHeader()
		err227 := argvalue0.Read(jsProt226)
		if err227 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg228 := flag.Arg(2)
		mbTrans229 := thrift.NewTMemoryBufferLen(len(arg228))
		defer mbTrans229.Close()
		_, err230 := mbTrans229.WriteString(arg228)
		if err230 != nil {
			Usage()
			return
		}
		factory231 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt232 := factory231.GetProtocol(mbTrans229)
		containerStruct1 := dos_server.NewGetStrategiesByIdsArgs()
		err233 := containerStruct1.ReadField2(jsProt232)
		if err233 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreative requires 2 args")
			flag.Usage()
		}
		arg234 := flag.Arg(1)
		mbTrans235 := thrift.NewTMemoryBufferLen(len(arg234))
		defer mbTrans235.Close()
		_, err236 := mbTrans235.WriteString(arg234)
		if err236 != nil {
			Usage()
			return
		}
		factory237 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt238 := factory237.GetProtocol(mbTrans235)
		argvalue0 := dos_server.NewRequestHeader()
		err239 := argvalue0.Read(jsProt238)
		if err239 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg240 := flag.Arg(2)
		mbTrans241 := thrift.NewTMemoryBufferLen(len(arg240))
		defer mbTrans241.Close()
		_, err242 := mbTrans241.WriteString(arg240)
		if err242 != nil {
			Usage()
			return
		}
		factory243 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt244 := factory243.GetProtocol(mbTrans241)
		argvalue1 := dos_server.NewDOSCreative()
		err245 := argvalue1.Read(jsProt244)
		if err245 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCreative(value0, value1))
		fmt.Print("\n")
		break
	case "editCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCreative requires 2 args")
			flag.Usage()
		}
		arg246 := flag.Arg(1)
		mbTrans247 := thrift.NewTMemoryBufferLen(len(arg246))
		defer mbTrans247.Close()
		_, err248 := mbTrans247.WriteString(arg246)
		if err248 != nil {
			Usage()
			return
		}
		factory249 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt250 := factory249.GetProtocol(mbTrans247)
		argvalue0 := dos_server.NewRequestHeader()
		err251 := argvalue0.Read(jsProt250)
		if err251 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg252 := flag.Arg(2)
		mbTrans253 := thrift.NewTMemoryBufferLen(len(arg252))
		defer mbTrans253.Close()
		_, err254 := mbTrans253.WriteString(arg252)
		if err254 != nil {
			Usage()
			return
		}
		factory255 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt256 := factory255.GetProtocol(mbTrans253)
		argvalue1 := dos_server.NewDOSCreative()
		err257 := argvalue1.Read(jsProt256)
		if err257 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCreative(value0, value1))
		fmt.Print("\n")
		break
	case "queryCreativeByParam":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "QueryCreativeByParam requires 5 args")
			flag.Usage()
		}
		arg258 := flag.Arg(1)
		mbTrans259 := thrift.NewTMemoryBufferLen(len(arg258))
		defer mbTrans259.Close()
		_, err260 := mbTrans259.WriteString(arg258)
		if err260 != nil {
			Usage()
			return
		}
		factory261 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt262 := factory261.GetProtocol(mbTrans259)
		argvalue0 := dos_server.NewRequestHeader()
		err263 := argvalue0.Read(jsProt262)
		if err263 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg264 := flag.Arg(2)
		mbTrans265 := thrift.NewTMemoryBufferLen(len(arg264))
		defer mbTrans265.Close()
		_, err266 := mbTrans265.WriteString(arg264)
		if err266 != nil {
			Usage()
			return
		}
		factory267 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt268 := factory267.GetProtocol(mbTrans265)
		argvalue1 := dos_server.NewCreativeQueryParam()
		err269 := argvalue1.Read(jsProt268)
		if err269 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg270 := flag.Arg(3)
		mbTrans271 := thrift.NewTMemoryBufferLen(len(arg270))
		defer mbTrans271.Close()
		_, err272 := mbTrans271.WriteString(arg270)
		if err272 != nil {
			Usage()
			return
		}
		factory273 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt274 := factory273.GetProtocol(mbTrans271)
		argvalue2 := dos_server.NewCreativeOrderParam()
		err275 := argvalue2.Read(jsProt274)
		if err275 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		tmp3, err276 := (strconv.Atoi(flag.Arg(4)))
		if err276 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		tmp4, err277 := (strconv.Atoi(flag.Arg(5)))
		if err277 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.QueryCreativeByParam(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "getCreativesByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetCreativesByIds requires 3 args")
			flag.Usage()
		}
		arg278 := flag.Arg(1)
		mbTrans279 := thrift.NewTMemoryBufferLen(len(arg278))
		defer mbTrans279.Close()
		_, err280 := mbTrans279.WriteString(arg278)
		if err280 != nil {
			Usage()
			return
		}
		factory281 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt282 := factory281.GetProtocol(mbTrans279)
		argvalue0 := dos_server.NewRequestHeader()
		err283 := argvalue0.Read(jsProt282)
		if err283 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg284 := flag.Arg(2)
		mbTrans285 := thrift.NewTMemoryBufferLen(len(arg284))
		defer mbTrans285.Close()
		_, err286 := mbTrans285.WriteString(arg284)
		if err286 != nil {
			Usage()
			return
		}
		factory287 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt288 := factory287.GetProtocol(mbTrans285)
		containerStruct1 := dos_server.NewGetCreativesByIdsArgs()
		err289 := containerStruct1.ReadField2(jsProt288)
		if err289 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		argvalue2 := flag.Arg(3) == "true"
		value2 := argvalue2
		fmt.Print(client.GetCreativesByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addThirdParty":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddThirdParty requires 2 args")
			flag.Usage()
		}
		arg291 := flag.Arg(1)
		mbTrans292 := thrift.NewTMemoryBufferLen(len(arg291))
		defer mbTrans292.Close()
		_, err293 := mbTrans292.WriteString(arg291)
		if err293 != nil {
			Usage()
			return
		}
		factory294 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt295 := factory294.GetProtocol(mbTrans292)
		argvalue0 := dos_server.NewRequestHeader()
		err296 := argvalue0.Read(jsProt295)
		if err296 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg297 := flag.Arg(2)
		mbTrans298 := thrift.NewTMemoryBufferLen(len(arg297))
		defer mbTrans298.Close()
		_, err299 := mbTrans298.WriteString(arg297)
		if err299 != nil {
			Usage()
			return
		}
		factory300 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt301 := factory300.GetProtocol(mbTrans298)
		argvalue1 := dos_server.NewDOSThirdParty()
		err302 := argvalue1.Read(jsProt301)
		if err302 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddThirdParty(value0, value1))
		fmt.Print("\n")
		break
	case "getThirdPartyToken":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetThirdPartyToken requires 2 args")
			flag.Usage()
		}
		arg303 := flag.Arg(1)
		mbTrans304 := thrift.NewTMemoryBufferLen(len(arg303))
		defer mbTrans304.Close()
		_, err305 := mbTrans304.WriteString(arg303)
		if err305 != nil {
			Usage()
			return
		}
		factory306 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt307 := factory306.GetProtocol(mbTrans304)
		argvalue0 := dos_server.NewRequestHeader()
		err308 := argvalue0.Read(jsProt307)
		if err308 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg309 := flag.Arg(2)
		mbTrans310 := thrift.NewTMemoryBufferLen(len(arg309))
		defer mbTrans310.Close()
		_, err311 := mbTrans310.WriteString(arg309)
		if err311 != nil {
			Usage()
			return
		}
		factory312 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt313 := factory312.GetProtocol(mbTrans310)
		argvalue1 := dos_server.NewQueryThirdPartyToken()
		err314 := argvalue1.Read(jsProt313)
		if err314 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.GetThirdPartyToken(value0, value1))
		fmt.Print("\n")
		break
	case "addThirdBandingInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddThirdBandingInfo requires 2 args")
			flag.Usage()
		}
		arg315 := flag.Arg(1)
		mbTrans316 := thrift.NewTMemoryBufferLen(len(arg315))
		defer mbTrans316.Close()
		_, err317 := mbTrans316.WriteString(arg315)
		if err317 != nil {
			Usage()
			return
		}
		factory318 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt319 := factory318.GetProtocol(mbTrans316)
		argvalue0 := dos_server.NewRequestHeader()
		err320 := argvalue0.Read(jsProt319)
		if err320 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg321 := flag.Arg(2)
		mbTrans322 := thrift.NewTMemoryBufferLen(len(arg321))
		defer mbTrans322.Close()
		_, err323 := mbTrans322.WriteString(arg321)
		if err323 != nil {
			Usage()
			return
		}
		factory324 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt325 := factory324.GetProtocol(mbTrans322)
		argvalue1 := dos_server.NewDOSThirdBandingInfo()
		err326 := argvalue1.Read(jsProt325)
		if err326 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddThirdBandingInfo(value0, value1))
		fmt.Print("\n")
		break
	case "queryTopAccountId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "QueryTopAccountId requires 2 args")
			flag.Usage()
		}
		arg327 := flag.Arg(1)
		mbTrans328 := thrift.NewTMemoryBufferLen(len(arg327))
		defer mbTrans328.Close()
		_, err329 := mbTrans328.WriteString(arg327)
		if err329 != nil {
			Usage()
			return
		}
		factory330 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt331 := factory330.GetProtocol(mbTrans328)
		argvalue0 := dos_server.NewRequestHeader()
		err332 := argvalue0.Read(jsProt331)
		if err332 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1, err333 := (strconv.ParseInt(flag.Arg(2), 10, 64))
		if err333 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.QueryTopAccountId(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
