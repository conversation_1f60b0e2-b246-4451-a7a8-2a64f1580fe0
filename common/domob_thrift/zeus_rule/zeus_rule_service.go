// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package zeus_rule

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/zeus_common"
	"rtb_model_server/common/domob_thrift/zeus_rule_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = zeus_common.GoUnusedProtection__
var _ = zeus_rule_types.GoUnusedProtection__

type ZeusRuleService interface {
	dm303.DomobService

	// 向Matrix添加规则
	//
	// Parameters:
	//  - Rule
	AddRule(rule *zeus_rule_types.Rule) (r bool, err error)
	// 从Matrix中删除指定Rule
	//
	// Parameters:
	//  - Rule
	DeleteRule(rule *zeus_rule_types.Rule) (r bool, err error)
}

type ZeusRuleServiceClient struct {
	*dm303.DomobServiceClient
}

func NewZeusRuleServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *ZeusRuleServiceClient {
	return &ZeusRuleServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewZeusRuleServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *ZeusRuleServiceClient {
	return &ZeusRuleServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 向Matrix添加规则
//
// Parameters:
//  - Rule
func (p *ZeusRuleServiceClient) AddRule(rule *zeus_rule_types.Rule) (r bool, err error) {
	if err = p.sendAddRule(rule); err != nil {
		return
	}
	return p.recvAddRule()
}

func (p *ZeusRuleServiceClient) sendAddRule(rule *zeus_rule_types.Rule) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addRule", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddRuleArgs()
	args0.Rule = rule
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ZeusRuleServiceClient) recvAddRule() (value bool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddRuleResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	return
}

// 从Matrix中删除指定Rule
//
// Parameters:
//  - Rule
func (p *ZeusRuleServiceClient) DeleteRule(rule *zeus_rule_types.Rule) (r bool, err error) {
	if err = p.sendDeleteRule(rule); err != nil {
		return
	}
	return p.recvDeleteRule()
}

func (p *ZeusRuleServiceClient) sendDeleteRule(rule *zeus_rule_types.Rule) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("deleteRule", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewDeleteRuleArgs()
	args4.Rule = rule
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *ZeusRuleServiceClient) recvDeleteRule() (value bool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewDeleteRuleResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	return
}

type ZeusRuleServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewZeusRuleServiceProcessor(handler ZeusRuleService) *ZeusRuleServiceProcessor {
	self8 := &ZeusRuleServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self8.AddToProcessorMap("addRule", &zeusRuleServiceProcessorAddRule{handler: handler})
	self8.AddToProcessorMap("deleteRule", &zeusRuleServiceProcessorDeleteRule{handler: handler})
	return self8
}

type zeusRuleServiceProcessorAddRule struct {
	handler ZeusRuleService
}

func (p *zeusRuleServiceProcessorAddRule) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddRuleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addRule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddRuleResult()
	if result.Success, err = p.handler.AddRule(args.Rule); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addRule: "+err.Error())
		oprot.WriteMessageBegin("addRule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addRule", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type zeusRuleServiceProcessorDeleteRule struct {
	handler ZeusRuleService
}

func (p *zeusRuleServiceProcessorDeleteRule) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDeleteRuleArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("deleteRule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDeleteRuleResult()
	if result.Success, err = p.handler.DeleteRule(args.Rule); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing deleteRule: "+err.Error())
		oprot.WriteMessageBegin("deleteRule", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("deleteRule", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddRuleArgs struct {
	Rule *zeus_rule_types.Rule `thrift:"rule,1" json:"rule"`
}

func NewAddRuleArgs() *AddRuleArgs {
	return &AddRuleArgs{}
}

func (p *AddRuleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddRuleArgs) readField1(iprot thrift.TProtocol) error {
	p.Rule = zeus_rule_types.NewRule()
	if err := p.Rule.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rule)
	}
	return nil
}

func (p *AddRuleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addRule_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddRuleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rule != nil {
		if err := oprot.WriteFieldBegin("rule", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rule: %s", p, err)
		}
		if err := p.Rule.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rule)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rule: %s", p, err)
		}
	}
	return err
}

func (p *AddRuleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddRuleArgs(%+v)", *p)
}

type AddRuleResult struct {
	Success bool `thrift:"success,0" json:"success"`
}

func NewAddRuleResult() *AddRuleResult {
	return &AddRuleResult{}
}

func (p *AddRuleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddRuleResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddRuleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addRule_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddRuleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddRuleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddRuleResult(%+v)", *p)
}

type DeleteRuleArgs struct {
	Rule *zeus_rule_types.Rule `thrift:"rule,1" json:"rule"`
}

func NewDeleteRuleArgs() *DeleteRuleArgs {
	return &DeleteRuleArgs{}
}

func (p *DeleteRuleArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteRuleArgs) readField1(iprot thrift.TProtocol) error {
	p.Rule = zeus_rule_types.NewRule()
	if err := p.Rule.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rule)
	}
	return nil
}

func (p *DeleteRuleArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteRule_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteRuleArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rule != nil {
		if err := oprot.WriteFieldBegin("rule", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rule: %s", p, err)
		}
		if err := p.Rule.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rule)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rule: %s", p, err)
		}
	}
	return err
}

func (p *DeleteRuleArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteRuleArgs(%+v)", *p)
}

type DeleteRuleResult struct {
	Success bool `thrift:"success,0" json:"success"`
}

func NewDeleteRuleResult() *DeleteRuleResult {
	return &DeleteRuleResult{}
}

func (p *DeleteRuleResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DeleteRuleResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *DeleteRuleResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("deleteRule_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DeleteRuleResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *DeleteRuleResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DeleteRuleResult(%+v)", *p)
}
