// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"sos_userserver"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  CommonResponse sendPhoneSign(RequestHeader header, PhoneSighRequest request)")
	fmt.Fprintln(os.Stderr, "  CommonResponse validatePhoneSign(RequestHeader header, SignValidateRequest request)")
	fmt.Fprintln(os.Stderr, "  CommonResponse passwordReset(RequestHeader header, PasswordResetRequest request)")
	fmt.Fprintln(os.Stderr, "  UserResponse userRegister(RequestHeader header, RegisterRequest request)")
	fmt.Fprintln(os.Stderr, "  UserResponse userLogin(RequestHeader header, LoginRequest request)")
	fmt.Fprintln(os.Stderr, "  CommonResponse passwordUpdate(RequestHeader header, PasswordUpdateRequest request)")
	fmt.Fprintln(os.Stderr, "  CommonResponse userInvited(RequestHeader hader, InvitedRequest request)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := sos_userserver.NewUserServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "sendPhoneSign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SendPhoneSign requires 2 args")
			flag.Usage()
		}
		arg30 := flag.Arg(1)
		mbTrans31 := thrift.NewTMemoryBufferLen(len(arg30))
		defer mbTrans31.Close()
		_, err32 := mbTrans31.WriteString(arg30)
		if err32 != nil {
			Usage()
			return
		}
		factory33 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt34 := factory33.GetProtocol(mbTrans31)
		argvalue0 := sos_userserver.NewRequestHeader()
		err35 := argvalue0.Read(jsProt34)
		if err35 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg36 := flag.Arg(2)
		mbTrans37 := thrift.NewTMemoryBufferLen(len(arg36))
		defer mbTrans37.Close()
		_, err38 := mbTrans37.WriteString(arg36)
		if err38 != nil {
			Usage()
			return
		}
		factory39 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt40 := factory39.GetProtocol(mbTrans37)
		argvalue1 := sos_userserver.NewPhoneSighRequest()
		err41 := argvalue1.Read(jsProt40)
		if err41 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SendPhoneSign(value0, value1))
		fmt.Print("\n")
		break
	case "validatePhoneSign":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ValidatePhoneSign requires 2 args")
			flag.Usage()
		}
		arg42 := flag.Arg(1)
		mbTrans43 := thrift.NewTMemoryBufferLen(len(arg42))
		defer mbTrans43.Close()
		_, err44 := mbTrans43.WriteString(arg42)
		if err44 != nil {
			Usage()
			return
		}
		factory45 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt46 := factory45.GetProtocol(mbTrans43)
		argvalue0 := sos_userserver.NewRequestHeader()
		err47 := argvalue0.Read(jsProt46)
		if err47 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg48 := flag.Arg(2)
		mbTrans49 := thrift.NewTMemoryBufferLen(len(arg48))
		defer mbTrans49.Close()
		_, err50 := mbTrans49.WriteString(arg48)
		if err50 != nil {
			Usage()
			return
		}
		factory51 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt52 := factory51.GetProtocol(mbTrans49)
		argvalue1 := sos_userserver.NewSignValidateRequest()
		err53 := argvalue1.Read(jsProt52)
		if err53 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.ValidatePhoneSign(value0, value1))
		fmt.Print("\n")
		break
	case "passwordReset":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PasswordReset requires 2 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := sos_userserver.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg60 := flag.Arg(2)
		mbTrans61 := thrift.NewTMemoryBufferLen(len(arg60))
		defer mbTrans61.Close()
		_, err62 := mbTrans61.WriteString(arg60)
		if err62 != nil {
			Usage()
			return
		}
		factory63 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt64 := factory63.GetProtocol(mbTrans61)
		argvalue1 := sos_userserver.NewPasswordResetRequest()
		err65 := argvalue1.Read(jsProt64)
		if err65 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.PasswordReset(value0, value1))
		fmt.Print("\n")
		break
	case "userRegister":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UserRegister requires 2 args")
			flag.Usage()
		}
		arg66 := flag.Arg(1)
		mbTrans67 := thrift.NewTMemoryBufferLen(len(arg66))
		defer mbTrans67.Close()
		_, err68 := mbTrans67.WriteString(arg66)
		if err68 != nil {
			Usage()
			return
		}
		factory69 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt70 := factory69.GetProtocol(mbTrans67)
		argvalue0 := sos_userserver.NewRequestHeader()
		err71 := argvalue0.Read(jsProt70)
		if err71 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg72 := flag.Arg(2)
		mbTrans73 := thrift.NewTMemoryBufferLen(len(arg72))
		defer mbTrans73.Close()
		_, err74 := mbTrans73.WriteString(arg72)
		if err74 != nil {
			Usage()
			return
		}
		factory75 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt76 := factory75.GetProtocol(mbTrans73)
		argvalue1 := sos_userserver.NewRegisterRequest()
		err77 := argvalue1.Read(jsProt76)
		if err77 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UserRegister(value0, value1))
		fmt.Print("\n")
		break
	case "userLogin":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UserLogin requires 2 args")
			flag.Usage()
		}
		arg78 := flag.Arg(1)
		mbTrans79 := thrift.NewTMemoryBufferLen(len(arg78))
		defer mbTrans79.Close()
		_, err80 := mbTrans79.WriteString(arg78)
		if err80 != nil {
			Usage()
			return
		}
		factory81 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt82 := factory81.GetProtocol(mbTrans79)
		argvalue0 := sos_userserver.NewRequestHeader()
		err83 := argvalue0.Read(jsProt82)
		if err83 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg84 := flag.Arg(2)
		mbTrans85 := thrift.NewTMemoryBufferLen(len(arg84))
		defer mbTrans85.Close()
		_, err86 := mbTrans85.WriteString(arg84)
		if err86 != nil {
			Usage()
			return
		}
		factory87 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt88 := factory87.GetProtocol(mbTrans85)
		argvalue1 := sos_userserver.NewLoginRequest()
		err89 := argvalue1.Read(jsProt88)
		if err89 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UserLogin(value0, value1))
		fmt.Print("\n")
		break
	case "passwordUpdate":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PasswordUpdate requires 2 args")
			flag.Usage()
		}
		arg90 := flag.Arg(1)
		mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
		defer mbTrans91.Close()
		_, err92 := mbTrans91.WriteString(arg90)
		if err92 != nil {
			Usage()
			return
		}
		factory93 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt94 := factory93.GetProtocol(mbTrans91)
		argvalue0 := sos_userserver.NewRequestHeader()
		err95 := argvalue0.Read(jsProt94)
		if err95 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg96 := flag.Arg(2)
		mbTrans97 := thrift.NewTMemoryBufferLen(len(arg96))
		defer mbTrans97.Close()
		_, err98 := mbTrans97.WriteString(arg96)
		if err98 != nil {
			Usage()
			return
		}
		factory99 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt100 := factory99.GetProtocol(mbTrans97)
		argvalue1 := sos_userserver.NewPasswordUpdateRequest()
		err101 := argvalue1.Read(jsProt100)
		if err101 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.PasswordUpdate(value0, value1))
		fmt.Print("\n")
		break
	case "userInvited":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "UserInvited requires 2 args")
			flag.Usage()
		}
		arg102 := flag.Arg(1)
		mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
		defer mbTrans103.Close()
		_, err104 := mbTrans103.WriteString(arg102)
		if err104 != nil {
			Usage()
			return
		}
		factory105 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt106 := factory105.GetProtocol(mbTrans103)
		argvalue0 := sos_userserver.NewRequestHeader()
		err107 := argvalue0.Read(jsProt106)
		if err107 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg108 := flag.Arg(2)
		mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
		defer mbTrans109.Close()
		_, err110 := mbTrans109.WriteString(arg108)
		if err110 != nil {
			Usage()
			return
		}
		factory111 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt112 := factory111.GetProtocol(mbTrans109)
		argvalue1 := sos_userserver.NewInvitedRequest()
		err113 := argvalue1.Read(jsProt112)
		if err113 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.UserInvited(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
