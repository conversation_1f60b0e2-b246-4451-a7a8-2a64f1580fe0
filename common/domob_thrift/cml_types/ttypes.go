// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package cml_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//需要查询的字段
//searchField目前支持的字段：
//   素材id，  模糊查询
//   APP名称， 模糊查询
//   素材名称，模糊查询
type SearchField int64

const (
	SearchField_SF_MATERIAL_ID   SearchField = 0
	SearchField_SF_MATERIAL_NAME SearchField = 1
	SearchField_SF_APP_NAME      SearchField = 2
)

func (p SearchField) String() string {
	switch p {
	case SearchField_SF_MATERIAL_ID:
		return "SearchField_SF_MATERIAL_ID"
	case SearchField_SF_MATERIAL_NAME:
		return "SearchField_SF_MATERIAL_NAME"
	case SearchField_SF_APP_NAME:
		return "SearchField_SF_APP_NAME"
	}
	return "<UNSET>"
}

func SearchFieldFromString(s string) (SearchField, error) {
	switch s {
	case "SearchField_SF_MATERIAL_ID":
		return SearchField_SF_MATERIAL_ID, nil
	case "SearchField_SF_MATERIAL_NAME":
		return SearchField_SF_MATERIAL_NAME, nil
	case "SearchField_SF_APP_NAME":
		return SearchField_SF_APP_NAME, nil
	}
	return SearchField(math.MinInt32 - 1), fmt.Errorf("not a valid SearchField string")
}

//素材来源 *
type MaterialSourceType int64

const (
	MaterialSourceType_MST_UNKNOWN          MaterialSourceType = 0
	MaterialSourceType_MST_NETWORK_MATERIAL MaterialSourceType = 1
	MaterialSourceType_MST_SCREENSHOT       MaterialSourceType = 2
	MaterialSourceType_MST_TOOL_MAKING      MaterialSourceType = 3
	MaterialSourceType_MST_CUSTOMER_PROVIDE MaterialSourceType = 4
	MaterialSourceType_MST_PROGRAMMED       MaterialSourceType = 5
	MaterialSourceType_MST_DIRECT_DELIVERY  MaterialSourceType = 6
	MaterialSourceType_MST_FREE_ISLAND      MaterialSourceType = 7
)

func (p MaterialSourceType) String() string {
	switch p {
	case MaterialSourceType_MST_UNKNOWN:
		return "MaterialSourceType_MST_UNKNOWN"
	case MaterialSourceType_MST_NETWORK_MATERIAL:
		return "MaterialSourceType_MST_NETWORK_MATERIAL"
	case MaterialSourceType_MST_SCREENSHOT:
		return "MaterialSourceType_MST_SCREENSHOT"
	case MaterialSourceType_MST_TOOL_MAKING:
		return "MaterialSourceType_MST_TOOL_MAKING"
	case MaterialSourceType_MST_CUSTOMER_PROVIDE:
		return "MaterialSourceType_MST_CUSTOMER_PROVIDE"
	case MaterialSourceType_MST_PROGRAMMED:
		return "MaterialSourceType_MST_PROGRAMMED"
	case MaterialSourceType_MST_DIRECT_DELIVERY:
		return "MaterialSourceType_MST_DIRECT_DELIVERY"
	case MaterialSourceType_MST_FREE_ISLAND:
		return "MaterialSourceType_MST_FREE_ISLAND"
	}
	return "<UNSET>"
}

func MaterialSourceTypeFromString(s string) (MaterialSourceType, error) {
	switch s {
	case "MaterialSourceType_MST_UNKNOWN":
		return MaterialSourceType_MST_UNKNOWN, nil
	case "MaterialSourceType_MST_NETWORK_MATERIAL":
		return MaterialSourceType_MST_NETWORK_MATERIAL, nil
	case "MaterialSourceType_MST_SCREENSHOT":
		return MaterialSourceType_MST_SCREENSHOT, nil
	case "MaterialSourceType_MST_TOOL_MAKING":
		return MaterialSourceType_MST_TOOL_MAKING, nil
	case "MaterialSourceType_MST_CUSTOMER_PROVIDE":
		return MaterialSourceType_MST_CUSTOMER_PROVIDE, nil
	case "MaterialSourceType_MST_PROGRAMMED":
		return MaterialSourceType_MST_PROGRAMMED, nil
	case "MaterialSourceType_MST_DIRECT_DELIVERY":
		return MaterialSourceType_MST_DIRECT_DELIVERY, nil
	case "MaterialSourceType_MST_FREE_ISLAND":
		return MaterialSourceType_MST_FREE_ISLAND, nil
	}
	return MaterialSourceType(math.MinInt32 - 1), fmt.Errorf("not a valid MaterialSourceType string")
}

//素材类型
type MaterialType int64

const (
	MaterialType_MT_UNKNOWN     MaterialType = 0
	MaterialType_MT_IMAGE       MaterialType = 1
	MaterialType_MT_GIF         MaterialType = 2
	MaterialType_MT_LOGO        MaterialType = 3
	MaterialType_MT_VIDEO       MaterialType = 10
	MaterialType_MT_TITLE       MaterialType = 20
	MaterialType_MT_TEXT        MaterialType = 21
	MaterialType_MT_BUTTON_TEXT MaterialType = 22
	MaterialType_MT_POPUP_TEXT  MaterialType = 23
	MaterialType_MT_TAG_TEXT    MaterialType = 24
	MaterialType_MT_INFO_SOURCE MaterialType = 25
	MaterialType_MT_USERNAME    MaterialType = 26
	MaterialType_MT_SUBTITLE    MaterialType = 27
)

func (p MaterialType) String() string {
	switch p {
	case MaterialType_MT_UNKNOWN:
		return "MaterialType_MT_UNKNOWN"
	case MaterialType_MT_IMAGE:
		return "MaterialType_MT_IMAGE"
	case MaterialType_MT_GIF:
		return "MaterialType_MT_GIF"
	case MaterialType_MT_LOGO:
		return "MaterialType_MT_LOGO"
	case MaterialType_MT_VIDEO:
		return "MaterialType_MT_VIDEO"
	case MaterialType_MT_TITLE:
		return "MaterialType_MT_TITLE"
	case MaterialType_MT_TEXT:
		return "MaterialType_MT_TEXT"
	case MaterialType_MT_BUTTON_TEXT:
		return "MaterialType_MT_BUTTON_TEXT"
	case MaterialType_MT_POPUP_TEXT:
		return "MaterialType_MT_POPUP_TEXT"
	case MaterialType_MT_TAG_TEXT:
		return "MaterialType_MT_TAG_TEXT"
	case MaterialType_MT_INFO_SOURCE:
		return "MaterialType_MT_INFO_SOURCE"
	case MaterialType_MT_USERNAME:
		return "MaterialType_MT_USERNAME"
	case MaterialType_MT_SUBTITLE:
		return "MaterialType_MT_SUBTITLE"
	}
	return "<UNSET>"
}

func MaterialTypeFromString(s string) (MaterialType, error) {
	switch s {
	case "MaterialType_MT_UNKNOWN":
		return MaterialType_MT_UNKNOWN, nil
	case "MaterialType_MT_IMAGE":
		return MaterialType_MT_IMAGE, nil
	case "MaterialType_MT_GIF":
		return MaterialType_MT_GIF, nil
	case "MaterialType_MT_LOGO":
		return MaterialType_MT_LOGO, nil
	case "MaterialType_MT_VIDEO":
		return MaterialType_MT_VIDEO, nil
	case "MaterialType_MT_TITLE":
		return MaterialType_MT_TITLE, nil
	case "MaterialType_MT_TEXT":
		return MaterialType_MT_TEXT, nil
	case "MaterialType_MT_BUTTON_TEXT":
		return MaterialType_MT_BUTTON_TEXT, nil
	case "MaterialType_MT_POPUP_TEXT":
		return MaterialType_MT_POPUP_TEXT, nil
	case "MaterialType_MT_TAG_TEXT":
		return MaterialType_MT_TAG_TEXT, nil
	case "MaterialType_MT_INFO_SOURCE":
		return MaterialType_MT_INFO_SOURCE, nil
	case "MaterialType_MT_USERNAME":
		return MaterialType_MT_USERNAME, nil
	case "MaterialType_MT_SUBTITLE":
		return MaterialType_MT_SUBTITLE, nil
	}
	return MaterialType(math.MinInt32 - 1), fmt.Errorf("not a valid MaterialType string")
}

//创意点击跳转地址类型
type CreativeClickType int64

const (
	CreativeClickType_CCT_UNKNOWN           CreativeClickType = 0
	CreativeClickType_CCT_DOWNLOAD          CreativeClickType = 1
	CreativeClickType_CCT_LANDINGPAGE       CreativeClickType = 2
	CreativeClickType_CCT_ARTICLE_PROMOTION CreativeClickType = 3
)

func (p CreativeClickType) String() string {
	switch p {
	case CreativeClickType_CCT_UNKNOWN:
		return "CreativeClickType_CCT_UNKNOWN"
	case CreativeClickType_CCT_DOWNLOAD:
		return "CreativeClickType_CCT_DOWNLOAD"
	case CreativeClickType_CCT_LANDINGPAGE:
		return "CreativeClickType_CCT_LANDINGPAGE"
	case CreativeClickType_CCT_ARTICLE_PROMOTION:
		return "CreativeClickType_CCT_ARTICLE_PROMOTION"
	}
	return "<UNSET>"
}

func CreativeClickTypeFromString(s string) (CreativeClickType, error) {
	switch s {
	case "CreativeClickType_CCT_UNKNOWN":
		return CreativeClickType_CCT_UNKNOWN, nil
	case "CreativeClickType_CCT_DOWNLOAD":
		return CreativeClickType_CCT_DOWNLOAD, nil
	case "CreativeClickType_CCT_LANDINGPAGE":
		return CreativeClickType_CCT_LANDINGPAGE, nil
	case "CreativeClickType_CCT_ARTICLE_PROMOTION":
		return CreativeClickType_CCT_ARTICLE_PROMOTION, nil
	}
	return CreativeClickType(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeClickType string")
}

//创意来源 *
type CreativeSourceType int64

const (
	CreativeSourceType_CST_UNKNOWN         CreativeSourceType = 0
	CreativeSourceType_CST_RTB             CreativeSourceType = 1
	CreativeSourceType_CST_DIRECT_DELIVERY CreativeSourceType = 2
)

func (p CreativeSourceType) String() string {
	switch p {
	case CreativeSourceType_CST_UNKNOWN:
		return "CreativeSourceType_CST_UNKNOWN"
	case CreativeSourceType_CST_RTB:
		return "CreativeSourceType_CST_RTB"
	case CreativeSourceType_CST_DIRECT_DELIVERY:
		return "CreativeSourceType_CST_DIRECT_DELIVERY"
	}
	return "<UNSET>"
}

func CreativeSourceTypeFromString(s string) (CreativeSourceType, error) {
	switch s {
	case "CreativeSourceType_CST_UNKNOWN":
		return CreativeSourceType_CST_UNKNOWN, nil
	case "CreativeSourceType_CST_RTB":
		return CreativeSourceType_CST_RTB, nil
	case "CreativeSourceType_CST_DIRECT_DELIVERY":
		return CreativeSourceType_CST_DIRECT_DELIVERY, nil
	}
	return CreativeSourceType(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeSourceType string")
}

//创意创建渠道
type CreativeCreateChannel int64

const (
	CreativeCreateChannel_CCC_UNKNOWN        CreativeCreateChannel = 0
	CreativeCreateChannel_CCC_COMMON_CREATE  CreativeCreateChannel = 1
	CreativeCreateChannel_CCC_PROGRAM_CREATE CreativeCreateChannel = 2
	CreativeCreateChannel_CCC_UPLOAD         CreativeCreateChannel = 3
)

func (p CreativeCreateChannel) String() string {
	switch p {
	case CreativeCreateChannel_CCC_UNKNOWN:
		return "CreativeCreateChannel_CCC_UNKNOWN"
	case CreativeCreateChannel_CCC_COMMON_CREATE:
		return "CreativeCreateChannel_CCC_COMMON_CREATE"
	case CreativeCreateChannel_CCC_PROGRAM_CREATE:
		return "CreativeCreateChannel_CCC_PROGRAM_CREATE"
	case CreativeCreateChannel_CCC_UPLOAD:
		return "CreativeCreateChannel_CCC_UPLOAD"
	}
	return "<UNSET>"
}

func CreativeCreateChannelFromString(s string) (CreativeCreateChannel, error) {
	switch s {
	case "CreativeCreateChannel_CCC_UNKNOWN":
		return CreativeCreateChannel_CCC_UNKNOWN, nil
	case "CreativeCreateChannel_CCC_COMMON_CREATE":
		return CreativeCreateChannel_CCC_COMMON_CREATE, nil
	case "CreativeCreateChannel_CCC_PROGRAM_CREATE":
		return CreativeCreateChannel_CCC_PROGRAM_CREATE, nil
	case "CreativeCreateChannel_CCC_UPLOAD":
		return CreativeCreateChannel_CCC_UPLOAD, nil
	}
	return CreativeCreateChannel(math.MinInt32 - 1), fmt.Errorf("not a valid CreativeCreateChannel string")
}

//相似创意字段
type CategorizeCreativeFiled int64

const (
	CategorizeCreativeFiled_CCF_UNKNOWN CategorizeCreativeFiled = 0
	CategorizeCreativeFiled_CCF_MEDIA   CategorizeCreativeFiled = 1
	CategorizeCreativeFiled_CCF_DESC    CategorizeCreativeFiled = 2
)

func (p CategorizeCreativeFiled) String() string {
	switch p {
	case CategorizeCreativeFiled_CCF_UNKNOWN:
		return "CategorizeCreativeFiled_CCF_UNKNOWN"
	case CategorizeCreativeFiled_CCF_MEDIA:
		return "CategorizeCreativeFiled_CCF_MEDIA"
	case CategorizeCreativeFiled_CCF_DESC:
		return "CategorizeCreativeFiled_CCF_DESC"
	}
	return "<UNSET>"
}

func CategorizeCreativeFiledFromString(s string) (CategorizeCreativeFiled, error) {
	switch s {
	case "CategorizeCreativeFiled_CCF_UNKNOWN":
		return CategorizeCreativeFiled_CCF_UNKNOWN, nil
	case "CategorizeCreativeFiled_CCF_MEDIA":
		return CategorizeCreativeFiled_CCF_MEDIA, nil
	case "CategorizeCreativeFiled_CCF_DESC":
		return CategorizeCreativeFiled_CCF_DESC, nil
	}
	return CategorizeCreativeFiled(math.MinInt32 - 1), fmt.Errorf("not a valid CategorizeCreativeFiled string")
}

type Material struct {
	Id          int32              `thrift:"id,1" json:"id"`
	Name        string             `thrift:"name,2" json:"name"`
	UgcId       int32              `thrift:"ugcId,3" json:"ugcId"`
	EncodeId    string             `thrift:"encodeId,4" json:"encodeId"`
	AppId       int32              `thrift:"appId,5" json:"appId"`
	Size        int32              `thrift:"size,6" json:"size"`
	TypeA1      MaterialType       `thrift:"type,7" json:"type"`
	SourceType  MaterialSourceType `thrift:"sourceType,8" json:"sourceType"`
	Uploader    int32              `thrift:"uploader,9" json:"uploader"`
	Author      int32              `thrift:"author,10" json:"author"`
	Ext         string             `thrift:"ext,11" json:"ext"`
	RelationId  int32              `thrift:"relationId,12" json:"relationId"`
	Thumb       string             `thrift:"thumb,13" json:"thumb"`
	Fingerprint string             `thrift:"fingerprint,14" json:"fingerprint"`
	Desc        string             `thrift:"desc,15" json:"desc"`
	Source      string             `thrift:"source,16" json:"source"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewMaterial() *Material {
	return &Material{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		SourceType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Material) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *Material) IsSetSourceType() bool {
	return int64(p.SourceType) != math.MinInt32-1
}

func (p *Material) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Material) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Material) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Material) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.UgcId = v
	}
	return nil
}

func (p *Material) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.EncodeId = v
	}
	return nil
}

func (p *Material) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *Material) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *Material) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TypeA1 = MaterialType(v)
	}
	return nil
}

func (p *Material) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SourceType = MaterialSourceType(v)
	}
	return nil
}

func (p *Material) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Uploader = v
	}
	return nil
}

func (p *Material) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Author = v
	}
	return nil
}

func (p *Material) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Ext = v
	}
	return nil
}

func (p *Material) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.RelationId = v
	}
	return nil
}

func (p *Material) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Thumb = v
	}
	return nil
}

func (p *Material) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Fingerprint = v
	}
	return nil
}

func (p *Material) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *Material) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Source = v
	}
	return nil
}

func (p *Material) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Material) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Material) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Material) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Material"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Material) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Material) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Material) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ugcId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ugcId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UgcId)); err != nil {
		return fmt.Errorf("%T.ugcId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ugcId: %s", p, err)
	}
	return err
}

func (p *Material) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("encodeId", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:encodeId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.EncodeId)); err != nil {
		return fmt.Errorf("%T.encodeId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:encodeId: %s", p, err)
	}
	return err
}

func (p *Material) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:appId: %s", p, err)
	}
	return err
}

func (p *Material) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:size: %s", p, err)
	}
	return err
}

func (p *Material) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:type: %s", p, err)
		}
	}
	return err
}

func (p *Material) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetSourceType() {
		if err := oprot.WriteFieldBegin("sourceType", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:sourceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SourceType)); err != nil {
			return fmt.Errorf("%T.sourceType (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:sourceType: %s", p, err)
		}
	}
	return err
}

func (p *Material) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uploader", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:uploader: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uploader)); err != nil {
		return fmt.Errorf("%T.uploader (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:uploader: %s", p, err)
	}
	return err
}

func (p *Material) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:author: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Author)); err != nil {
		return fmt.Errorf("%T.author (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:author: %s", p, err)
	}
	return err
}

func (p *Material) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ext", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:ext: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ext)); err != nil {
		return fmt.Errorf("%T.ext (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:ext: %s", p, err)
	}
	return err
}

func (p *Material) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("relationId", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:relationId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RelationId)); err != nil {
		return fmt.Errorf("%T.relationId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:relationId: %s", p, err)
	}
	return err
}

func (p *Material) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("thumb", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:thumb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Thumb)); err != nil {
		return fmt.Errorf("%T.thumb (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:thumb: %s", p, err)
	}
	return err
}

func (p *Material) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fingerprint", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:fingerprint: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Fingerprint)); err != nil {
		return fmt.Errorf("%T.fingerprint (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:fingerprint: %s", p, err)
	}
	return err
}

func (p *Material) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:desc: %s", p, err)
	}
	return err
}

func (p *Material) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("source", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:source: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Source)); err != nil {
		return fmt.Errorf("%T.source (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:source: %s", p, err)
	}
	return err
}

func (p *Material) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *Material) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *Material) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Material) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Material(%+v)", *p)
}

type MaterialParams struct {
	Terms      map[SearchField]string `thrift:"terms,1" json:"terms"`
	SourceType MaterialSourceType     `thrift:"sourceType,2" json:"sourceType"`
	Author     int32                  `thrift:"author,3" json:"author"`
	Uploader   int32                  `thrift:"uploader,4" json:"uploader"`
	AppId      int32                  `thrift:"appId,5" json:"appId"`
	Size       int32                  `thrift:"size,6" json:"size"`
	TypeA1     MaterialType           `thrift:"type,7" json:"type"`
	Offset     int32                  `thrift:"offset,8" json:"offset"`
	Limit      int32                  `thrift:"limit,9" json:"limit"`
}

func NewMaterialParams() *MaterialParams {
	return &MaterialParams{
		SourceType: math.MinInt32 - 1, // unset sentinal value

		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MaterialParams) IsSetSourceType() bool {
	return int64(p.SourceType) != math.MinInt32-1
}

func (p *MaterialParams) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *MaterialParams) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.MAP {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MaterialParams) readField1(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Terms = make(map[SearchField]string, size)
	for i := 0; i < size; i++ {
		var _key0 SearchField
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = SearchField(v)
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.Terms[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *MaterialParams) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SourceType = MaterialSourceType(v)
	}
	return nil
}

func (p *MaterialParams) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Author = v
	}
	return nil
}

func (p *MaterialParams) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Uploader = v
	}
	return nil
}

func (p *MaterialParams) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *MaterialParams) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *MaterialParams) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.TypeA1 = MaterialType(v)
	}
	return nil
}

func (p *MaterialParams) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *MaterialParams) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *MaterialParams) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MaterialParams"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MaterialParams) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Terms != nil {
		if err := oprot.WriteFieldBegin("terms", thrift.MAP, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:terms: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRING, len(p.Terms)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Terms {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:terms: %s", p, err)
		}
	}
	return err
}

func (p *MaterialParams) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSourceType() {
		if err := oprot.WriteFieldBegin("sourceType", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:sourceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SourceType)); err != nil {
			return fmt.Errorf("%T.sourceType (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:sourceType: %s", p, err)
		}
	}
	return err
}

func (p *MaterialParams) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:author: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Author)); err != nil {
		return fmt.Errorf("%T.author (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:author: %s", p, err)
	}
	return err
}

func (p *MaterialParams) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uploader", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:uploader: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uploader)); err != nil {
		return fmt.Errorf("%T.uploader (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:uploader: %s", p, err)
	}
	return err
}

func (p *MaterialParams) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:appId: %s", p, err)
	}
	return err
}

func (p *MaterialParams) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:size: %s", p, err)
	}
	return err
}

func (p *MaterialParams) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:type: %s", p, err)
		}
	}
	return err
}

func (p *MaterialParams) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:offset: %s", p, err)
	}
	return err
}

func (p *MaterialParams) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:limit: %s", p, err)
	}
	return err
}

func (p *MaterialParams) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MaterialParams(%+v)", *p)
}

type CreativeContainer struct {
	Title       string   `thrift:"title,1" json:"title"`
	Urls        []string `thrift:"urls,2" json:"urls"`
	DescText    string   `thrift:"descText,3" json:"descText"`
	UserName    string   `thrift:"userName,4" json:"userName"`
	AvatarUrl   string   `thrift:"avatarUrl,5" json:"avatarUrl"`
	PopUpText   string   `thrift:"popUpText,6" json:"popUpText"`
	ButtonText  string   `thrift:"buttonText,7" json:"buttonText"`
	TagText     string   `thrift:"tagText,8" json:"tagText"`
	InfoSource  string   `thrift:"infoSource,9" json:"infoSource"`
	AdMatchType int64    `thrift:"adMatchType,10" json:"adMatchType"`
	SubTitle    string   `thrift:"subTitle,11" json:"subTitle"`
}

func NewCreativeContainer() *CreativeContainer {
	return &CreativeContainer{}
}

func (p *CreativeContainer) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CreativeContainer) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *CreativeContainer) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Urls = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.Urls = append(p.Urls, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CreativeContainer) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DescText = v
	}
	return nil
}

func (p *CreativeContainer) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UserName = v
	}
	return nil
}

func (p *CreativeContainer) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AvatarUrl = v
	}
	return nil
}

func (p *CreativeContainer) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.PopUpText = v
	}
	return nil
}

func (p *CreativeContainer) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ButtonText = v
	}
	return nil
}

func (p *CreativeContainer) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.TagText = v
	}
	return nil
}

func (p *CreativeContainer) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.InfoSource = v
	}
	return nil
}

func (p *CreativeContainer) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *CreativeContainer) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SubTitle = v
	}
	return nil
}

func (p *CreativeContainer) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CreativeContainer"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CreativeContainer) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:title: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Urls != nil {
		if err := oprot.WriteFieldBegin("urls", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:urls: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Urls)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Urls {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:urls: %s", p, err)
		}
	}
	return err
}

func (p *CreativeContainer) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("descText", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:descText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DescText)); err != nil {
		return fmt.Errorf("%T.descText (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:descText: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userName", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:userName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserName)); err != nil {
		return fmt.Errorf("%T.userName (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:userName: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("avatarUrl", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:avatarUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AvatarUrl)); err != nil {
		return fmt.Errorf("%T.avatarUrl (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:avatarUrl: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("popUpText", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:popUpText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PopUpText)); err != nil {
		return fmt.Errorf("%T.popUpText (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:popUpText: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("buttonText", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:buttonText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ButtonText)); err != nil {
		return fmt.Errorf("%T.buttonText (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:buttonText: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tagText", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:tagText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagText)); err != nil {
		return fmt.Errorf("%T.tagText (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:tagText: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("infoSource", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:infoSource: %s", p, err)
	}
	if err := oprot.WriteString(string(p.InfoSource)); err != nil {
		return fmt.Errorf("%T.infoSource (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:infoSource: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adMatchType", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:adMatchType: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.adMatchType (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:adMatchType: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("subTitle", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:subTitle: %s", p, err)
	}
	if err := oprot.WriteString(string(p.SubTitle)); err != nil {
		return fmt.Errorf("%T.subTitle (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:subTitle: %s", p, err)
	}
	return err
}

func (p *CreativeContainer) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CreativeContainer(%+v)", *p)
}

type Creative struct {
	Id            int32                 `thrift:"id,1" json:"id"`
	Name          string                `thrift:"name,2" json:"name"`
	SourceType    CreativeSourceType    `thrift:"sourceType,3" json:"sourceType"`
	CreateChannel CreativeCreateChannel `thrift:"createChannel,4" json:"createChannel"`
	Fingerprint   string                `thrift:"fingerprint,5" json:"fingerprint"`
	AppId         int32                 `thrift:"appId,6" json:"appId"`
	Author        int32                 `thrift:"author,7" json:"author"`
	RelationId    int32                 `thrift:"relationId,8" json:"relationId"`
	ClickUrl      string                `thrift:"clickUrl,9" json:"clickUrl"`
	ClickType     CreativeClickType     `thrift:"clickType,10" json:"clickType"`
	Containers    []*CreativeContainer  `thrift:"containers,11" json:"containers"`
	Media         int32                 `thrift:"media,12" json:"media"`
	InventoryType int32                 `thrift:"inventoryType,13" json:"inventoryType"`
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	Status     int32 `thrift:"status,97" json:"status"`
	CreateTime int64 `thrift:"createTime,98" json:"createTime"`
	LastUpdate int64 `thrift:"lastUpdate,99" json:"lastUpdate"`
}

func NewCreative() *Creative {
	return &Creative{
		SourceType: math.MinInt32 - 1, // unset sentinal value

		CreateChannel: math.MinInt32 - 1, // unset sentinal value

		ClickType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Creative) IsSetSourceType() bool {
	return int64(p.SourceType) != math.MinInt32-1
}

func (p *Creative) IsSetCreateChannel() bool {
	return int64(p.CreateChannel) != math.MinInt32-1
}

func (p *Creative) IsSetClickType() bool {
	return int64(p.ClickType) != math.MinInt32-1
}

func (p *Creative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 97:
			if fieldTypeId == thrift.I32 {
				if err := p.readField97(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 98:
			if fieldTypeId == thrift.I64 {
				if err := p.readField98(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 99:
			if fieldTypeId == thrift.I64 {
				if err := p.readField99(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Creative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Creative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Creative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SourceType = CreativeSourceType(v)
	}
	return nil
}

func (p *Creative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CreateChannel = CreativeCreateChannel(v)
	}
	return nil
}

func (p *Creative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Fingerprint = v
	}
	return nil
}

func (p *Creative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *Creative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Author = v
	}
	return nil
}

func (p *Creative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.RelationId = v
	}
	return nil
}

func (p *Creative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ClickUrl = v
	}
	return nil
}

func (p *Creative) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ClickType = CreativeClickType(v)
	}
	return nil
}

func (p *Creative) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Containers = make([]*CreativeContainer, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewCreativeContainer()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Containers = append(p.Containers, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Creative) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Media = v
	}
	return nil
}

func (p *Creative) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.InventoryType = v
	}
	return nil
}

func (p *Creative) readField97(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 97: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *Creative) readField98(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 98: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *Creative) readField99(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 99: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *Creative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Creative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField97(oprot); err != nil {
		return err
	}
	if err := p.writeField98(oprot); err != nil {
		return err
	}
	if err := p.writeField99(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Creative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Creative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *Creative) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSourceType() {
		if err := oprot.WriteFieldBegin("sourceType", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sourceType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SourceType)); err != nil {
			return fmt.Errorf("%T.sourceType (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sourceType: %s", p, err)
		}
	}
	return err
}

func (p *Creative) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetCreateChannel() {
		if err := oprot.WriteFieldBegin("createChannel", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:createChannel: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CreateChannel)); err != nil {
			return fmt.Errorf("%T.createChannel (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:createChannel: %s", p, err)
		}
	}
	return err
}

func (p *Creative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fingerprint", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:fingerprint: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Fingerprint)); err != nil {
		return fmt.Errorf("%T.fingerprint (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:fingerprint: %s", p, err)
	}
	return err
}

func (p *Creative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:appId: %s", p, err)
	}
	return err
}

func (p *Creative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("author", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:author: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Author)); err != nil {
		return fmt.Errorf("%T.author (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:author: %s", p, err)
	}
	return err
}

func (p *Creative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("relationId", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:relationId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RelationId)); err != nil {
		return fmt.Errorf("%T.relationId (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:relationId: %s", p, err)
	}
	return err
}

func (p *Creative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickUrl", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:clickUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickUrl)); err != nil {
		return fmt.Errorf("%T.clickUrl (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:clickUrl: %s", p, err)
	}
	return err
}

func (p *Creative) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetClickType() {
		if err := oprot.WriteFieldBegin("clickType", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:clickType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ClickType)); err != nil {
			return fmt.Errorf("%T.clickType (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:clickType: %s", p, err)
		}
	}
	return err
}

func (p *Creative) writeField11(oprot thrift.TProtocol) (err error) {
	if p.Containers != nil {
		if err := oprot.WriteFieldBegin("containers", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:containers: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Containers)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Containers {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:containers: %s", p, err)
		}
	}
	return err
}

func (p *Creative) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:media: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Media)); err != nil {
		return fmt.Errorf("%T.media (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:media: %s", p, err)
	}
	return err
}

func (p *Creative) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("inventoryType", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:inventoryType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.InventoryType)); err != nil {
		return fmt.Errorf("%T.inventoryType (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:inventoryType: %s", p, err)
	}
	return err
}

func (p *Creative) writeField97(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 97); err != nil {
		return fmt.Errorf("%T write field begin error 97:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (97) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 97:status: %s", p, err)
	}
	return err
}

func (p *Creative) writeField98(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 98); err != nil {
		return fmt.Errorf("%T write field begin error 98:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (98) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 98:createTime: %s", p, err)
	}
	return err
}

func (p *Creative) writeField99(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 99); err != nil {
		return fmt.Errorf("%T write field begin error 99:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (99) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 99:lastUpdate: %s", p, err)
	}
	return err
}

func (p *Creative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Creative(%+v)", *p)
}
