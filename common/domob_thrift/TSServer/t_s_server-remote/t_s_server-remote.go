// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"TSServer"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "   getCurrentStatus()")
	fmt.Fprintln(os.Stderr, "  bool addApp(RequestHeader header, AppInfoRequestData requestData)")
	fmt.Fprintln(os.Stderr, "  AppInfoResponse getAppInfo(RequestHeader header,  statusList)")
	fmt.Fprintln(os.Stderr, "  AppInfoResponse getAppInfoById(RequestHeader header,  requestData)")
	fmt.Fprintln(os.Stderr, "  AppInfoUpdateStatusResponse updateAppInfoStatus(RequestHeader header, i32 status,  requestData)")
	fmt.Fprintln(os.Stderr, "  AppInfoUpdateStatusResponse retryCount(RequestHeader header,  requestData)")
	fmt.Fprintln(os.Stderr, "  bool addChannelInfo(RequestHeader header, AddChannelInfoData requestData)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := TSServer.NewTSServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getCurrentStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCurrentStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCurrentStatus())
		fmt.Print("\n")
		break
	case "addApp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddApp requires 2 args")
			flag.Usage()
		}
		arg40 := flag.Arg(1)
		mbTrans41 := thrift.NewTMemoryBufferLen(len(arg40))
		defer mbTrans41.Close()
		_, err42 := mbTrans41.WriteString(arg40)
		if err42 != nil {
			Usage()
			return
		}
		factory43 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt44 := factory43.GetProtocol(mbTrans41)
		argvalue0 := TSServer.NewRequestHeader()
		err45 := argvalue0.Read(jsProt44)
		if err45 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg46 := flag.Arg(2)
		mbTrans47 := thrift.NewTMemoryBufferLen(len(arg46))
		defer mbTrans47.Close()
		_, err48 := mbTrans47.WriteString(arg46)
		if err48 != nil {
			Usage()
			return
		}
		factory49 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt50 := factory49.GetProtocol(mbTrans47)
		argvalue1 := TSServer.NewAppInfoRequestData()
		err51 := argvalue1.Read(jsProt50)
		if err51 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddApp(value0, value1))
		fmt.Print("\n")
		break
	case "getAppInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppInfo requires 2 args")
			flag.Usage()
		}
		arg52 := flag.Arg(1)
		mbTrans53 := thrift.NewTMemoryBufferLen(len(arg52))
		defer mbTrans53.Close()
		_, err54 := mbTrans53.WriteString(arg52)
		if err54 != nil {
			Usage()
			return
		}
		factory55 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt56 := factory55.GetProtocol(mbTrans53)
		argvalue0 := TSServer.NewRequestHeader()
		err57 := argvalue0.Read(jsProt56)
		if err57 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg58 := flag.Arg(2)
		mbTrans59 := thrift.NewTMemoryBufferLen(len(arg58))
		defer mbTrans59.Close()
		_, err60 := mbTrans59.WriteString(arg58)
		if err60 != nil {
			Usage()
			return
		}
		factory61 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt62 := factory61.GetProtocol(mbTrans59)
		containerStruct1 := TSServer.NewGetAppInfoArgs()
		err63 := containerStruct1.ReadField2(jsProt62)
		if err63 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.StatusList
		value1 := argvalue1
		fmt.Print(client.GetAppInfo(value0, value1))
		fmt.Print("\n")
		break
	case "getAppInfoById":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppInfoById requires 2 args")
			flag.Usage()
		}
		arg64 := flag.Arg(1)
		mbTrans65 := thrift.NewTMemoryBufferLen(len(arg64))
		defer mbTrans65.Close()
		_, err66 := mbTrans65.WriteString(arg64)
		if err66 != nil {
			Usage()
			return
		}
		factory67 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt68 := factory67.GetProtocol(mbTrans65)
		argvalue0 := TSServer.NewRequestHeader()
		err69 := argvalue0.Read(jsProt68)
		if err69 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg70 := flag.Arg(2)
		mbTrans71 := thrift.NewTMemoryBufferLen(len(arg70))
		defer mbTrans71.Close()
		_, err72 := mbTrans71.WriteString(arg70)
		if err72 != nil {
			Usage()
			return
		}
		factory73 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt74 := factory73.GetProtocol(mbTrans71)
		containerStruct1 := TSServer.NewGetAppInfoByIdArgs()
		err75 := containerStruct1.ReadField2(jsProt74)
		if err75 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.RequestData
		value1 := argvalue1
		fmt.Print(client.GetAppInfoById(value0, value1))
		fmt.Print("\n")
		break
	case "updateAppInfoStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateAppInfoStatus requires 3 args")
			flag.Usage()
		}
		arg76 := flag.Arg(1)
		mbTrans77 := thrift.NewTMemoryBufferLen(len(arg76))
		defer mbTrans77.Close()
		_, err78 := mbTrans77.WriteString(arg76)
		if err78 != nil {
			Usage()
			return
		}
		factory79 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt80 := factory79.GetProtocol(mbTrans77)
		argvalue0 := TSServer.NewRequestHeader()
		err81 := argvalue0.Read(jsProt80)
		if err81 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err82 := (strconv.Atoi(flag.Arg(2)))
		if err82 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg83 := flag.Arg(3)
		mbTrans84 := thrift.NewTMemoryBufferLen(len(arg83))
		defer mbTrans84.Close()
		_, err85 := mbTrans84.WriteString(arg83)
		if err85 != nil {
			Usage()
			return
		}
		factory86 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt87 := factory86.GetProtocol(mbTrans84)
		containerStruct2 := TSServer.NewUpdateAppInfoStatusArgs()
		err88 := containerStruct2.ReadField3(jsProt87)
		if err88 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.RequestData
		value2 := argvalue2
		fmt.Print(client.UpdateAppInfoStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "retryCount":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "RetryCount requires 2 args")
			flag.Usage()
		}
		arg89 := flag.Arg(1)
		mbTrans90 := thrift.NewTMemoryBufferLen(len(arg89))
		defer mbTrans90.Close()
		_, err91 := mbTrans90.WriteString(arg89)
		if err91 != nil {
			Usage()
			return
		}
		factory92 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt93 := factory92.GetProtocol(mbTrans90)
		argvalue0 := TSServer.NewRequestHeader()
		err94 := argvalue0.Read(jsProt93)
		if err94 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg95 := flag.Arg(2)
		mbTrans96 := thrift.NewTMemoryBufferLen(len(arg95))
		defer mbTrans96.Close()
		_, err97 := mbTrans96.WriteString(arg95)
		if err97 != nil {
			Usage()
			return
		}
		factory98 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt99 := factory98.GetProtocol(mbTrans96)
		containerStruct1 := TSServer.NewRetryCountArgs()
		err100 := containerStruct1.ReadField2(jsProt99)
		if err100 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.RequestData
		value1 := argvalue1
		fmt.Print(client.RetryCount(value0, value1))
		fmt.Print("\n")
		break
	case "addChannelInfo":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddChannelInfo requires 2 args")
			flag.Usage()
		}
		arg101 := flag.Arg(1)
		mbTrans102 := thrift.NewTMemoryBufferLen(len(arg101))
		defer mbTrans102.Close()
		_, err103 := mbTrans102.WriteString(arg101)
		if err103 != nil {
			Usage()
			return
		}
		factory104 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt105 := factory104.GetProtocol(mbTrans102)
		argvalue0 := TSServer.NewRequestHeader()
		err106 := argvalue0.Read(jsProt105)
		if err106 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg107 := flag.Arg(2)
		mbTrans108 := thrift.NewTMemoryBufferLen(len(arg107))
		defer mbTrans108.Close()
		_, err109 := mbTrans108.WriteString(arg107)
		if err109 != nil {
			Usage()
			return
		}
		factory110 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt111 := factory110.GetProtocol(mbTrans108)
		argvalue1 := TSServer.NewAddChannelInfoData()
		err112 := argvalue1.Read(jsProt111)
		if err112 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddChannelInfo(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
