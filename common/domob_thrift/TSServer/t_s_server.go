// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package TSServer

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

type TSServer interface { //用户服务接口

	// 获取当前TSServer运行状态
	// @exec_time 返回程序执行的微秒时间
	GetCurrentStatus() (r map[string]map[string]string, err error)
	// 添加app接口
	//
	// Parameters:
	//  - Header
	//  - RequestData: 在本接口，添加app信息时，三个参数都是必须有值的
	AddApp(header *RequestHeader, requestData *AppInfoRequestData) (r bool, err error)
	// 获取app相关信息
	//
	// Parameters:
	//  - Header
	//  - StatusList: 任务状态列表
	GetAppInfo(header *RequestHeader, statusList []int32) (r *AppInfoResponse, me *AppInfoException, err error)
	// 通过APP ID列表请求APP相关信息
	// APP ID包括itunesId, bundleId
	//
	// Parameters:
	//  - Header
	//  - RequestData: 请求数据
	GetAppInfoById(header *RequestHeader, requestData []*AppInfoRequestData) (r *AppInfoResponse, me *AppInfoException, err error)
	// 任务状态更改
	//
	// Parameters:
	//  - Header
	//  - Status: 更新的目标状态
	//  - RequestData: 待处理的数据
	UpdateAppInfoStatus(header *RequestHeader, status int32, requestData []*AppInfoRequestData) (r *AppInfoUpdateStatusResponse, me *AppInfoException, err error)
	// 错误重试+1
	//
	// Parameters:
	//  - Header
	//  - RequestData: 请求数据
	RetryCount(header *RequestHeader, requestData []*AppInfoRequestData) (r *AppInfoUpdateStatusResponse, me *AppInfoException, err error)
	// 添加原始渠道信息
	//
	// Parameters:
	//  - Header
	//  - RequestData: 添加的数据
	AddChannelInfo(header *RequestHeader, requestData *AddChannelInfoData) (r bool, me *AppInfoException, err error)
}

//用户服务接口
type TSServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewTSServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *TSServerClient {
	return &TSServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewTSServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *TSServerClient {
	return &TSServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 获取当前TSServer运行状态
// @exec_time 返回程序执行的微秒时间
func (p *TSServerClient) GetCurrentStatus() (r map[string]map[string]string, err error) {
	if err = p.sendGetCurrentStatus(); err != nil {
		return
	}
	return p.recvGetCurrentStatus()
}

func (p *TSServerClient) sendGetCurrentStatus() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCurrentStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args2 := NewGetCurrentStatusArgs()
	if err = args2.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TSServerClient) recvGetCurrentStatus() (value map[string]map[string]string, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error4 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error5 error
		error5, err = error4.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error5
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result3 := NewGetCurrentStatusResult()
	if err = result3.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result3.Success
	return
}

// 添加app接口
//
// Parameters:
//  - Header
//  - RequestData: 在本接口，添加app信息时，三个参数都是必须有值的
func (p *TSServerClient) AddApp(header *RequestHeader, requestData *AppInfoRequestData) (r bool, err error) {
	if err = p.sendAddApp(header, requestData); err != nil {
		return
	}
	return p.recvAddApp()
}

func (p *TSServerClient) sendAddApp(header *RequestHeader, requestData *AppInfoRequestData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addApp", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args6 := NewAddAppArgs()
	args6.Header = header
	args6.RequestData = requestData
	if err = args6.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TSServerClient) recvAddApp() (value bool, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error8 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error9 error
		error9, err = error8.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error9
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result7 := NewAddAppResult()
	if err = result7.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result7.Success
	return
}

// 获取app相关信息
//
// Parameters:
//  - Header
//  - StatusList: 任务状态列表
func (p *TSServerClient) GetAppInfo(header *RequestHeader, statusList []int32) (r *AppInfoResponse, me *AppInfoException, err error) {
	if err = p.sendGetAppInfo(header, statusList); err != nil {
		return
	}
	return p.recvGetAppInfo()
}

func (p *TSServerClient) sendGetAppInfo(header *RequestHeader, statusList []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args10 := NewGetAppInfoArgs()
	args10.Header = header
	args10.StatusList = statusList
	if err = args10.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TSServerClient) recvGetAppInfo() (value *AppInfoResponse, me *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error12 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error13 error
		error13, err = error12.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error13
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result11 := NewGetAppInfoResult()
	if err = result11.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result11.Success
	if result11.Me != nil {
		me = result11.Me
	}
	return
}

// 通过APP ID列表请求APP相关信息
// APP ID包括itunesId, bundleId
//
// Parameters:
//  - Header
//  - RequestData: 请求数据
func (p *TSServerClient) GetAppInfoById(header *RequestHeader, requestData []*AppInfoRequestData) (r *AppInfoResponse, me *AppInfoException, err error) {
	if err = p.sendGetAppInfoById(header, requestData); err != nil {
		return
	}
	return p.recvGetAppInfoById()
}

func (p *TSServerClient) sendGetAppInfoById(header *RequestHeader, requestData []*AppInfoRequestData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAppInfoById", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args14 := NewGetAppInfoByIdArgs()
	args14.Header = header
	args14.RequestData = requestData
	if err = args14.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TSServerClient) recvGetAppInfoById() (value *AppInfoResponse, me *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error16 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error17 error
		error17, err = error16.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error17
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result15 := NewGetAppInfoByIdResult()
	if err = result15.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result15.Success
	if result15.Me != nil {
		me = result15.Me
	}
	return
}

// 任务状态更改
//
// Parameters:
//  - Header
//  - Status: 更新的目标状态
//  - RequestData: 待处理的数据
func (p *TSServerClient) UpdateAppInfoStatus(header *RequestHeader, status int32, requestData []*AppInfoRequestData) (r *AppInfoUpdateStatusResponse, me *AppInfoException, err error) {
	if err = p.sendUpdateAppInfoStatus(header, status, requestData); err != nil {
		return
	}
	return p.recvUpdateAppInfoStatus()
}

func (p *TSServerClient) sendUpdateAppInfoStatus(header *RequestHeader, status int32, requestData []*AppInfoRequestData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateAppInfoStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args18 := NewUpdateAppInfoStatusArgs()
	args18.Header = header
	args18.Status = status
	args18.RequestData = requestData
	if err = args18.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TSServerClient) recvUpdateAppInfoStatus() (value *AppInfoUpdateStatusResponse, me *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error20 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error21 error
		error21, err = error20.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error21
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result19 := NewUpdateAppInfoStatusResult()
	if err = result19.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result19.Success
	if result19.Me != nil {
		me = result19.Me
	}
	return
}

// 错误重试+1
//
// Parameters:
//  - Header
//  - RequestData: 请求数据
func (p *TSServerClient) RetryCount(header *RequestHeader, requestData []*AppInfoRequestData) (r *AppInfoUpdateStatusResponse, me *AppInfoException, err error) {
	if err = p.sendRetryCount(header, requestData); err != nil {
		return
	}
	return p.recvRetryCount()
}

func (p *TSServerClient) sendRetryCount(header *RequestHeader, requestData []*AppInfoRequestData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("retryCount", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args22 := NewRetryCountArgs()
	args22.Header = header
	args22.RequestData = requestData
	if err = args22.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TSServerClient) recvRetryCount() (value *AppInfoUpdateStatusResponse, me *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error24 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error25 error
		error25, err = error24.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error25
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result23 := NewRetryCountResult()
	if err = result23.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result23.Success
	if result23.Me != nil {
		me = result23.Me
	}
	return
}

// 添加原始渠道信息
//
// Parameters:
//  - Header
//  - RequestData: 添加的数据
func (p *TSServerClient) AddChannelInfo(header *RequestHeader, requestData *AddChannelInfoData) (r bool, me *AppInfoException, err error) {
	if err = p.sendAddChannelInfo(header, requestData); err != nil {
		return
	}
	return p.recvAddChannelInfo()
}

func (p *TSServerClient) sendAddChannelInfo(header *RequestHeader, requestData *AddChannelInfoData) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addChannelInfo", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args26 := NewAddChannelInfoArgs()
	args26.Header = header
	args26.RequestData = requestData
	if err = args26.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *TSServerClient) recvAddChannelInfo() (value bool, me *AppInfoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error28 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error29 error
		error29, err = error28.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error29
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result27 := NewAddChannelInfoResult()
	if err = result27.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result27.Success
	if result27.Me != nil {
		me = result27.Me
	}
	return
}

type TSServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      TSServer
}

func (p *TSServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *TSServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *TSServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewTSServerProcessor(handler TSServer) *TSServerProcessor {

	self30 := &TSServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self30.processorMap["getCurrentStatus"] = &tSServerProcessorGetCurrentStatus{handler: handler}
	self30.processorMap["addApp"] = &tSServerProcessorAddApp{handler: handler}
	self30.processorMap["getAppInfo"] = &tSServerProcessorGetAppInfo{handler: handler}
	self30.processorMap["getAppInfoById"] = &tSServerProcessorGetAppInfoById{handler: handler}
	self30.processorMap["updateAppInfoStatus"] = &tSServerProcessorUpdateAppInfoStatus{handler: handler}
	self30.processorMap["retryCount"] = &tSServerProcessorRetryCount{handler: handler}
	self30.processorMap["addChannelInfo"] = &tSServerProcessorAddChannelInfo{handler: handler}
	return self30
}

func (p *TSServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x31 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x31.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x31

}

type tSServerProcessorGetCurrentStatus struct {
	handler TSServer
}

func (p *tSServerProcessorGetCurrentStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCurrentStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCurrentStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCurrentStatusResult()
	if result.Success, err = p.handler.GetCurrentStatus(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCurrentStatus: "+err.Error())
		oprot.WriteMessageBegin("getCurrentStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCurrentStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type tSServerProcessorAddApp struct {
	handler TSServer
}

func (p *tSServerProcessorAddApp) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAppArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAppResult()
	if result.Success, err = p.handler.AddApp(args.Header, args.RequestData); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addApp: "+err.Error())
		oprot.WriteMessageBegin("addApp", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addApp", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type tSServerProcessorGetAppInfo struct {
	handler TSServer
}

func (p *tSServerProcessorGetAppInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppInfoResult()
	if result.Success, result.Me, err = p.handler.GetAppInfo(args.Header, args.StatusList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppInfo: "+err.Error())
		oprot.WriteMessageBegin("getAppInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type tSServerProcessorGetAppInfoById struct {
	handler TSServer
}

func (p *tSServerProcessorGetAppInfoById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAppInfoByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAppInfoById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAppInfoByIdResult()
	if result.Success, result.Me, err = p.handler.GetAppInfoById(args.Header, args.RequestData); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAppInfoById: "+err.Error())
		oprot.WriteMessageBegin("getAppInfoById", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAppInfoById", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type tSServerProcessorUpdateAppInfoStatus struct {
	handler TSServer
}

func (p *tSServerProcessorUpdateAppInfoStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateAppInfoStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateAppInfoStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateAppInfoStatusResult()
	if result.Success, result.Me, err = p.handler.UpdateAppInfoStatus(args.Header, args.Status, args.RequestData); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateAppInfoStatus: "+err.Error())
		oprot.WriteMessageBegin("updateAppInfoStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateAppInfoStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type tSServerProcessorRetryCount struct {
	handler TSServer
}

func (p *tSServerProcessorRetryCount) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRetryCountArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("retryCount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRetryCountResult()
	if result.Success, result.Me, err = p.handler.RetryCount(args.Header, args.RequestData); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing retryCount: "+err.Error())
		oprot.WriteMessageBegin("retryCount", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("retryCount", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type tSServerProcessorAddChannelInfo struct {
	handler TSServer
}

func (p *tSServerProcessorAddChannelInfo) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddChannelInfoArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addChannelInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddChannelInfoResult()
	if result.Success, result.Me, err = p.handler.AddChannelInfo(args.Header, args.RequestData); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addChannelInfo: "+err.Error())
		oprot.WriteMessageBegin("addChannelInfo", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addChannelInfo", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetCurrentStatusArgs struct {
}

func NewGetCurrentStatusArgs() *GetCurrentStatusArgs {
	return &GetCurrentStatusArgs{}
}

func (p *GetCurrentStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCurrentStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCurrentStatusArgs(%+v)", *p)
}

type GetCurrentStatusResult struct {
	Success map[string]map[string]string `thrift:"success,0" json:"success"`
}

func NewGetCurrentStatusResult() *GetCurrentStatusResult {
	return &GetCurrentStatusResult{}
}

func (p *GetCurrentStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string]map[string]string, size)
	for i := 0; i < size; i++ {
		var _key32 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key32 = v
		}
		_, _, size, err := iprot.ReadMapBegin()
		if err != nil {
			return fmt.Errorf("error reading map begin: %s")
		}
		_val33 := make(map[string]string, size)
		for i := 0; i < size; i++ {
			var _key34 string
			if v, err := iprot.ReadString(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_key34 = v
			}
			var _val35 string
			if v, err := iprot.ReadString(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_val35 = v
			}
			_val33[_key34] = _val35
		}
		if err := iprot.ReadMapEnd(); err != nil {
			return fmt.Errorf("error reading map end: %s", err)
		}
		p.Success[_key32] = _val33
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetCurrentStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCurrentStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCurrentStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.MAP, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(v)); err != nil {
				return fmt.Errorf("error writing map begin: %s")
			}
			for k, v := range v {
				if err := oprot.WriteString(string(k)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
				if err := oprot.WriteString(string(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteMapEnd(); err != nil {
				return fmt.Errorf("error writing map end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCurrentStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCurrentStatusResult(%+v)", *p)
}

type AddAppArgs struct {
	Header      *RequestHeader      `thrift:"header,1" json:"header"`
	RequestData *AppInfoRequestData `thrift:"requestData,2" json:"requestData"`
}

func NewAddAppArgs() *AddAppArgs {
	return &AddAppArgs{}
}

func (p *AddAppArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAppArgs) readField2(iprot thrift.TProtocol) error {
	p.RequestData = NewAppInfoRequestData()
	if err := p.RequestData.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestData)
	}
	return nil
}

func (p *AddAppArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addApp_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAppArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RequestData != nil {
		if err := oprot.WriteFieldBegin("requestData", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:requestData: %s", p, err)
		}
		if err := p.RequestData.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestData)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:requestData: %s", p, err)
		}
	}
	return err
}

func (p *AddAppArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppArgs(%+v)", *p)
}

type AddAppResult struct {
	Success bool `thrift:"success,0" json:"success"`
}

func NewAddAppResult() *AddAppResult {
	return &AddAppResult{}
}

func (p *AddAppResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddAppResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addApp_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddAppResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppResult(%+v)", *p)
}

type GetAppInfoArgs struct {
	Header     *RequestHeader `thrift:"header,1" json:"header"`
	StatusList []int32        `thrift:"statusList,2" json:"statusList"`
}

func NewGetAppInfoArgs() *GetAppInfoArgs {
	return &GetAppInfoArgs{}
}

func (p *GetAppInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppInfoArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StatusList = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem36 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem36 = v
		}
		p.StatusList = append(p.StatusList, _elem36)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.StatusList != nil {
		if err := oprot.WriteFieldBegin("statusList", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:statusList: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.StatusList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StatusList {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:statusList: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppInfoArgs(%+v)", *p)
}

type GetAppInfoResult struct {
	Success *AppInfoResponse  `thrift:"success,0" json:"success"`
	Me      *AppInfoException `thrift:"me,1" json:"me"`
}

func NewGetAppInfoResult() *GetAppInfoResult {
	return &GetAppInfoResult{}
}

func (p *GetAppInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAppInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Me = NewAppInfoException()
	if err := p.Me.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Me)
	}
	return nil
}

func (p *GetAppInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Me != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Me != nil {
		if err := oprot.WriteFieldBegin("me", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:me: %s", p, err)
		}
		if err := p.Me.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Me)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:me: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppInfoResult(%+v)", *p)
}

type GetAppInfoByIdArgs struct {
	Header      *RequestHeader        `thrift:"header,1" json:"header"`
	RequestData []*AppInfoRequestData `thrift:"requestData,2" json:"requestData"`
}

func NewGetAppInfoByIdArgs() *GetAppInfoByIdArgs {
	return &GetAppInfoByIdArgs{}
}

func (p *GetAppInfoByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoByIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAppInfoByIdArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RequestData = make([]*AppInfoRequestData, 0, size)
	for i := 0; i < size; i++ {
		_elem37 := NewAppInfoRequestData()
		if err := _elem37.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem37)
		}
		p.RequestData = append(p.RequestData, _elem37)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAppInfoByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppInfoById_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoByIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RequestData != nil {
		if err := oprot.WriteFieldBegin("requestData", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:requestData: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RequestData)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RequestData {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:requestData: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppInfoByIdArgs(%+v)", *p)
}

type GetAppInfoByIdResult struct {
	Success *AppInfoResponse  `thrift:"success,0" json:"success"`
	Me      *AppInfoException `thrift:"me,1" json:"me"`
}

func NewGetAppInfoByIdResult() *GetAppInfoByIdResult {
	return &GetAppInfoByIdResult{}
}

func (p *GetAppInfoByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAppInfoResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetAppInfoByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Me = NewAppInfoException()
	if err := p.Me.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Me)
	}
	return nil
}

func (p *GetAppInfoByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAppInfoById_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Me != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAppInfoByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Me != nil {
		if err := oprot.WriteFieldBegin("me", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:me: %s", p, err)
		}
		if err := p.Me.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Me)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:me: %s", p, err)
		}
	}
	return err
}

func (p *GetAppInfoByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAppInfoByIdResult(%+v)", *p)
}

type UpdateAppInfoStatusArgs struct {
	Header      *RequestHeader        `thrift:"header,1" json:"header"`
	Status      int32                 `thrift:"status,2" json:"status"`
	RequestData []*AppInfoRequestData `thrift:"requestData,3" json:"requestData"`
}

func NewUpdateAppInfoStatusArgs() *UpdateAppInfoStatusArgs {
	return &UpdateAppInfoStatusArgs{}
}

func (p *UpdateAppInfoStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppInfoStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateAppInfoStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *UpdateAppInfoStatusArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RequestData = make([]*AppInfoRequestData, 0, size)
	for i := 0; i < size; i++ {
		_elem38 := NewAppInfoRequestData()
		if err := _elem38.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem38)
		}
		p.RequestData = append(p.RequestData, _elem38)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *UpdateAppInfoStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateAppInfoStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppInfoStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAppInfoStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:status: %s", p, err)
	}
	return err
}

func (p *UpdateAppInfoStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.RequestData != nil {
		if err := oprot.WriteFieldBegin("requestData", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:requestData: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RequestData)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RequestData {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:requestData: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAppInfoStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAppInfoStatusArgs(%+v)", *p)
}

type UpdateAppInfoStatusResult struct {
	Success *AppInfoUpdateStatusResponse `thrift:"success,0" json:"success"`
	Me      *AppInfoException            `thrift:"me,1" json:"me"`
}

func NewUpdateAppInfoStatusResult() *UpdateAppInfoStatusResult {
	return &UpdateAppInfoStatusResult{}
}

func (p *UpdateAppInfoStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppInfoStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAppInfoUpdateStatusResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *UpdateAppInfoStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Me = NewAppInfoException()
	if err := p.Me.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Me)
	}
	return nil
}

func (p *UpdateAppInfoStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateAppInfoStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Me != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateAppInfoStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAppInfoStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Me != nil {
		if err := oprot.WriteFieldBegin("me", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:me: %s", p, err)
		}
		if err := p.Me.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Me)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:me: %s", p, err)
		}
	}
	return err
}

func (p *UpdateAppInfoStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateAppInfoStatusResult(%+v)", *p)
}

type RetryCountArgs struct {
	Header      *RequestHeader        `thrift:"header,1" json:"header"`
	RequestData []*AppInfoRequestData `thrift:"requestData,2" json:"requestData"`
}

func NewRetryCountArgs() *RetryCountArgs {
	return &RetryCountArgs{}
}

func (p *RetryCountArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetryCountArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RetryCountArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RequestData = make([]*AppInfoRequestData, 0, size)
	for i := 0; i < size; i++ {
		_elem39 := NewAppInfoRequestData()
		if err := _elem39.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem39)
		}
		p.RequestData = append(p.RequestData, _elem39)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RetryCountArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("retryCount_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetryCountArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RetryCountArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RequestData != nil {
		if err := oprot.WriteFieldBegin("requestData", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:requestData: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RequestData)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RequestData {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:requestData: %s", p, err)
		}
	}
	return err
}

func (p *RetryCountArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetryCountArgs(%+v)", *p)
}

type RetryCountResult struct {
	Success *AppInfoUpdateStatusResponse `thrift:"success,0" json:"success"`
	Me      *AppInfoException            `thrift:"me,1" json:"me"`
}

func NewRetryCountResult() *RetryCountResult {
	return &RetryCountResult{}
}

func (p *RetryCountResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetryCountResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAppInfoUpdateStatusResponse()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RetryCountResult) readField1(iprot thrift.TProtocol) error {
	p.Me = NewAppInfoException()
	if err := p.Me.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Me)
	}
	return nil
}

func (p *RetryCountResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("retryCount_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Me != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetryCountResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RetryCountResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Me != nil {
		if err := oprot.WriteFieldBegin("me", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:me: %s", p, err)
		}
		if err := p.Me.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Me)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:me: %s", p, err)
		}
	}
	return err
}

func (p *RetryCountResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetryCountResult(%+v)", *p)
}

type AddChannelInfoArgs struct {
	Header      *RequestHeader      `thrift:"header,1" json:"header"`
	RequestData *AddChannelInfoData `thrift:"requestData,2" json:"requestData"`
}

func NewAddChannelInfoArgs() *AddChannelInfoArgs {
	return &AddChannelInfoArgs{}
}

func (p *AddChannelInfoArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddChannelInfoArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddChannelInfoArgs) readField2(iprot thrift.TProtocol) error {
	p.RequestData = NewAddChannelInfoData()
	if err := p.RequestData.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.RequestData)
	}
	return nil
}

func (p *AddChannelInfoArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addChannelInfo_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddChannelInfoArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddChannelInfoArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RequestData != nil {
		if err := oprot.WriteFieldBegin("requestData", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:requestData: %s", p, err)
		}
		if err := p.RequestData.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.RequestData)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:requestData: %s", p, err)
		}
	}
	return err
}

func (p *AddChannelInfoArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddChannelInfoArgs(%+v)", *p)
}

type AddChannelInfoResult struct {
	Success bool              `thrift:"success,0" json:"success"`
	Me      *AppInfoException `thrift:"me,1" json:"me"`
}

func NewAddChannelInfoResult() *AddChannelInfoResult {
	return &AddChannelInfoResult{}
}

func (p *AddChannelInfoResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddChannelInfoResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddChannelInfoResult) readField1(iprot thrift.TProtocol) error {
	p.Me = NewAppInfoException()
	if err := p.Me.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Me)
	}
	return nil
}

func (p *AddChannelInfoResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addChannelInfo_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Me != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddChannelInfoResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.BOOL, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Me != nil {
		if err := oprot.WriteFieldBegin("me", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:me: %s", p, err)
		}
		if err := p.Me.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Me)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:me: %s", p, err)
		}
	}
	return err
}

func (p *AddChannelInfoResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddChannelInfoResult(%+v)", *p)
}
