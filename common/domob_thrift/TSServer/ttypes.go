// Autogenerated by Thr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package TSServer

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

//APP任务状态
type TaskStatus int64

const (
	TaskStatus_WAITING TaskStatus = 5001
	TaskStatus_WORKING TaskStatus = 5002
	TaskStatus_SUCCESS TaskStatus = 5003
	TaskStatus_FAIL    TaskStatus = 5004
)

func (p TaskStatus) String() string {
	switch p {
	case TaskStatus_WAITING:
		return "TaskStatus_WAITING"
	case TaskStatus_WORKING:
		return "TaskStatus_WORKING"
	case TaskStatus_SUCCESS:
		return "TaskStatus_SUCCESS"
	case TaskStatus_FAIL:
		return "TaskStatus_FAIL"
	}
	return "<UNSET>"
}

func TaskStatusFromString(s string) (TaskStatus, error) {
	switch s {
	case "TaskStatus_WAITING":
		return TaskStatus_WAITING, nil
	case "TaskStatus_WORKING":
		return TaskStatus_WORKING, nil
	case "TaskStatus_SUCCESS":
		return TaskStatus_SUCCESS, nil
	case "TaskStatus_FAIL":
		return TaskStatus_FAIL, nil
	}
	return TaskStatus(math.MinInt32 - 1), fmt.Errorf("not a valid TaskStatus string")
}

//AppInfoException中可能出现的异常
type AppInfoServiceCode int64

const (
	AppInfoServiceCode_REQ_PARAMS_ERROR AppInfoServiceCode = 1
	AppInfoServiceCode_SERVER_ERROR     AppInfoServiceCode = 2
)

func (p AppInfoServiceCode) String() string {
	switch p {
	case AppInfoServiceCode_REQ_PARAMS_ERROR:
		return "AppInfoServiceCode_REQ_PARAMS_ERROR"
	case AppInfoServiceCode_SERVER_ERROR:
		return "AppInfoServiceCode_SERVER_ERROR"
	}
	return "<UNSET>"
}

func AppInfoServiceCodeFromString(s string) (AppInfoServiceCode, error) {
	switch s {
	case "AppInfoServiceCode_REQ_PARAMS_ERROR":
		return AppInfoServiceCode_REQ_PARAMS_ERROR, nil
	case "AppInfoServiceCode_SERVER_ERROR":
		return AppInfoServiceCode_SERVER_ERROR, nil
	}
	return AppInfoServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid AppInfoServiceCode string")
}

type RequestHeader struct {
	Requester string `thrift:"requester,1" json:"requester"`
	SearchId  int64  `thrift:"searchId,2" json:"searchId"`
}

func NewRequestHeader() *RequestHeader {
	return &RequestHeader{}
}

func (p *RequestHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Requester = v
	}
	return nil
}

func (p *RequestHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *RequestHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RequestHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("requester", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:requester: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Requester)); err != nil {
		return fmt.Errorf("%T.requester (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:requester: %s", p, err)
	}
	return err
}

func (p *RequestHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:searchId: %s", p, err)
	}
	return err
}

func (p *RequestHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RequestHeader(%+v)", *p)
}

type AppInfoException struct {
	Code    AppInfoServiceCode `thrift:"code,1" json:"code"`
	Message string             `thrift:"message,2" json:"message"`
}

func NewAppInfoException() *AppInfoException {
	return &AppInfoException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AppInfoException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *AppInfoException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = AppInfoServiceCode(v)
	}
	return nil
}

func (p *AppInfoException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *AppInfoException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *AppInfoException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *AppInfoException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoException(%+v)", *p)
}

type AppInfoRequestData struct {
	BundleId  string `thrift:"bundleId,1" json:"bundleId"`
	ItunesId  string `thrift:"itunesId,2" json:"itunesId"`
	ItunesUrl string `thrift:"itunesUrl,3" json:"itunesUrl"`
}

func NewAppInfoRequestData() *AppInfoRequestData {
	return &AppInfoRequestData{}
}

func (p *AppInfoRequestData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoRequestData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BundleId = v
	}
	return nil
}

func (p *AppInfoRequestData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ItunesId = v
	}
	return nil
}

func (p *AppInfoRequestData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ItunesUrl = v
	}
	return nil
}

func (p *AppInfoRequestData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoRequestData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoRequestData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bundleId", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bundleId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BundleId)); err != nil {
		return fmt.Errorf("%T.bundleId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bundleId: %s", p, err)
	}
	return err
}

func (p *AppInfoRequestData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:itunesId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunesId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:itunesId: %s", p, err)
	}
	return err
}

func (p *AppInfoRequestData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesUrl", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:itunesUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesUrl)); err != nil {
		return fmt.Errorf("%T.itunesUrl (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:itunesUrl: %s", p, err)
	}
	return err
}

func (p *AppInfoRequestData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoRequestData(%+v)", *p)
}

type AppInfoResponseData struct {
	BundleId        string `thrift:"bundleId,1" json:"bundleId"`
	ItunesId        string `thrift:"itunesId,2" json:"itunesId"`
	ItunesUrl       string `thrift:"itunesUrl,3" json:"itunesUrl"`
	AppVersion      string `thrift:"appVersion,4" json:"appVersion"`
	UrlSchemes      string `thrift:"urlSchemes,5" json:"urlSchemes"`
	ExecutableFile  string `thrift:"executableFile,6" json:"executableFile"`
	LogoUrl         string `thrift:"logoUrl,7" json:"logoUrl"`
	AppSize         string `thrift:"appSize,8" json:"appSize"`
	ApplicationType string `thrift:"applicationType,9" json:"applicationType"`
	ArtistName      string `thrift:"artistName,10" json:"artistName"`
	Description     string `thrift:"description,11" json:"description"`
	RetryCount      int32  `thrift:"retryCount,12" json:"retryCount"`
	UpdateTime      int32  `thrift:"updateTime,13" json:"updateTime"`
}

func NewAppInfoResponseData() *AppInfoResponseData {
	return &AppInfoResponseData{}
}

func (p *AppInfoResponseData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoResponseData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BundleId = v
	}
	return nil
}

func (p *AppInfoResponseData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ItunesId = v
	}
	return nil
}

func (p *AppInfoResponseData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ItunesUrl = v
	}
	return nil
}

func (p *AppInfoResponseData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AppVersion = v
	}
	return nil
}

func (p *AppInfoResponseData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UrlSchemes = v
	}
	return nil
}

func (p *AppInfoResponseData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ExecutableFile = v
	}
	return nil
}

func (p *AppInfoResponseData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.LogoUrl = v
	}
	return nil
}

func (p *AppInfoResponseData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AppSize = v
	}
	return nil
}

func (p *AppInfoResponseData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ApplicationType = v
	}
	return nil
}

func (p *AppInfoResponseData) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ArtistName = v
	}
	return nil
}

func (p *AppInfoResponseData) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *AppInfoResponseData) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.RetryCount = v
	}
	return nil
}

func (p *AppInfoResponseData) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.UpdateTime = v
	}
	return nil
}

func (p *AppInfoResponseData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoResponseData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoResponseData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bundleId", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bundleId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BundleId)); err != nil {
		return fmt.Errorf("%T.bundleId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bundleId: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:itunesId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunesId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:itunesId: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesUrl", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:itunesUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesUrl)); err != nil {
		return fmt.Errorf("%T.itunesUrl (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:itunesUrl: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appVersion", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:appVersion: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppVersion)); err != nil {
		return fmt.Errorf("%T.appVersion (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:appVersion: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("urlSchemes", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:urlSchemes: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UrlSchemes)); err != nil {
		return fmt.Errorf("%T.urlSchemes (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:urlSchemes: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("executableFile", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:executableFile: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExecutableFile)); err != nil {
		return fmt.Errorf("%T.executableFile (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:executableFile: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logoUrl", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:logoUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.LogoUrl)); err != nil {
		return fmt.Errorf("%T.logoUrl (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:logoUrl: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appSize", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:appSize: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppSize)); err != nil {
		return fmt.Errorf("%T.appSize (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:appSize: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("applicationType", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:applicationType: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ApplicationType)); err != nil {
		return fmt.Errorf("%T.applicationType (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:applicationType: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("artistName", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:artistName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ArtistName)); err != nil {
		return fmt.Errorf("%T.artistName (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:artistName: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:description: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("retryCount", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:retryCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.RetryCount)); err != nil {
		return fmt.Errorf("%T.retryCount (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:retryCount: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("updateTime", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:updateTime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UpdateTime)); err != nil {
		return fmt.Errorf("%T.updateTime (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:updateTime: %s", p, err)
	}
	return err
}

func (p *AppInfoResponseData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoResponseData(%+v)", *p)
}

type AppInfoResponse struct {
	SearchId    int64                  `thrift:"searchId,1" json:"searchId"`
	AppInfoData []*AppInfoResponseData `thrift:"appInfoData,2" json:"appInfoData"`
}

func NewAppInfoResponse() *AppInfoResponse {
	return &AppInfoResponse{}
}

func (p *AppInfoResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *AppInfoResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AppInfoData = make([]*AppInfoResponseData, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewAppInfoResponseData()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.AppInfoData = append(p.AppInfoData, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppInfoResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *AppInfoResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppInfoData != nil {
		if err := oprot.WriteFieldBegin("appInfoData", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:appInfoData: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AppInfoData)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AppInfoData {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:appInfoData: %s", p, err)
		}
	}
	return err
}

func (p *AppInfoResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoResponse(%+v)", *p)
}

type ResponseStatusInfo struct {
	BundleId     string `thrift:"bundleId,1" json:"bundleId"`
	ItunesId     string `thrift:"itunesId,2" json:"itunesId"`
	ItunesUrl    string `thrift:"itunesUrl,3" json:"itunesUrl"`
	UpdateStatus bool   `thrift:"updateStatus,4" json:"updateStatus"`
}

func NewResponseStatusInfo() *ResponseStatusInfo {
	return &ResponseStatusInfo{}
}

func (p *ResponseStatusInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ResponseStatusInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BundleId = v
	}
	return nil
}

func (p *ResponseStatusInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ItunesId = v
	}
	return nil
}

func (p *ResponseStatusInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ItunesUrl = v
	}
	return nil
}

func (p *ResponseStatusInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UpdateStatus = v
	}
	return nil
}

func (p *ResponseStatusInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("ResponseStatusInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ResponseStatusInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bundleId", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bundleId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BundleId)); err != nil {
		return fmt.Errorf("%T.bundleId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bundleId: %s", p, err)
	}
	return err
}

func (p *ResponseStatusInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesId", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:itunesId: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesId)); err != nil {
		return fmt.Errorf("%T.itunesId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:itunesId: %s", p, err)
	}
	return err
}

func (p *ResponseStatusInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("itunesUrl", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:itunesUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ItunesUrl)); err != nil {
		return fmt.Errorf("%T.itunesUrl (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:itunesUrl: %s", p, err)
	}
	return err
}

func (p *ResponseStatusInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("updateStatus", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:updateStatus: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UpdateStatus)); err != nil {
		return fmt.Errorf("%T.updateStatus (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:updateStatus: %s", p, err)
	}
	return err
}

func (p *ResponseStatusInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ResponseStatusInfo(%+v)", *p)
}

type AppInfoUpdateStatusResponse struct {
	SearchId           int64                 `thrift:"searchId,1" json:"searchId"`
	ResponseStatusInfo []*ResponseStatusInfo `thrift:"responseStatusInfo,2" json:"responseStatusInfo"`
}

func NewAppInfoUpdateStatusResponse() *AppInfoUpdateStatusResponse {
	return &AppInfoUpdateStatusResponse{}
}

func (p *AppInfoUpdateStatusResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppInfoUpdateStatusResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *AppInfoUpdateStatusResponse) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResponseStatusInfo = make([]*ResponseStatusInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewResponseStatusInfo()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.ResponseStatusInfo = append(p.ResponseStatusInfo, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AppInfoUpdateStatusResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppInfoUpdateStatusResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppInfoUpdateStatusResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("searchId", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:searchId: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.searchId (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:searchId: %s", p, err)
	}
	return err
}

func (p *AppInfoUpdateStatusResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if p.ResponseStatusInfo != nil {
		if err := oprot.WriteFieldBegin("responseStatusInfo", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:responseStatusInfo: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResponseStatusInfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResponseStatusInfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:responseStatusInfo: %s", p, err)
		}
	}
	return err
}

func (p *AppInfoUpdateStatusResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppInfoUpdateStatusResponse(%+v)", *p)
}

type AddChannelInfoData struct {
	Pkg             string `thrift:"pkg,1" json:"pkg"`
	Platform        int32  `thrift:"platform,2" json:"platform"`
	ChannelName     string `thrift:"channel_name,3" json:"channel_name"`
	ClickUrl        string `thrift:"click_url,4" json:"click_url"`
	ClickSenderUrl  string `thrift:"click_sender_url,5" json:"click_sender_url"`
	Appkey          string `thrift:"appkey,6" json:"appkey"`
	AppkeyType      int32  `thrift:"appkey_type,7" json:"appkey_type"`
	ActPostbackType int32  `thrift:"act_postback_type,8" json:"act_postback_type"`
	PutChannelType  string `thrift:"put_channel_type,9" json:"put_channel_type"`
}

func NewAddChannelInfoData() *AddChannelInfoData {
	return &AddChannelInfoData{}
}

func (p *AddChannelInfoData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddChannelInfoData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pkg = v
	}
	return nil
}

func (p *AddChannelInfoData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Platform = v
	}
	return nil
}

func (p *AddChannelInfoData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ChannelName = v
	}
	return nil
}

func (p *AddChannelInfoData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ClickUrl = v
	}
	return nil
}

func (p *AddChannelInfoData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ClickSenderUrl = v
	}
	return nil
}

func (p *AddChannelInfoData) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Appkey = v
	}
	return nil
}

func (p *AddChannelInfoData) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AppkeyType = v
	}
	return nil
}

func (p *AddChannelInfoData) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ActPostbackType = v
	}
	return nil
}

func (p *AddChannelInfoData) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.PutChannelType = v
	}
	return nil
}

func (p *AddChannelInfoData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AddChannelInfoData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddChannelInfoData) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pkg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pkg)); err != nil {
		return fmt.Errorf("%T.pkg (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pkg: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("platform", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Platform)); err != nil {
		return fmt.Errorf("%T.platform (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:platform: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:channel_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ChannelName)); err != nil {
		return fmt.Errorf("%T.channel_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:channel_name: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_url", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:click_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickUrl)); err != nil {
		return fmt.Errorf("%T.click_url (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:click_url: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("click_sender_url", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:click_sender_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickSenderUrl)); err != nil {
		return fmt.Errorf("%T.click_sender_url (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:click_sender_url: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appkey", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:appkey: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appkey)); err != nil {
		return fmt.Errorf("%T.appkey (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:appkey: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appkey_type", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:appkey_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppkeyType)); err != nil {
		return fmt.Errorf("%T.appkey_type (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:appkey_type: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_postback_type", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:act_postback_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActPostbackType)); err != nil {
		return fmt.Errorf("%T.act_postback_type (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:act_postback_type: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("put_channel_type", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:put_channel_type: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PutChannelType)); err != nil {
		return fmt.Errorf("%T.put_channel_type (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:put_channel_type: %s", p, err)
	}
	return err
}

func (p *AddChannelInfoData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddChannelInfoData(%+v)", *p)
}
