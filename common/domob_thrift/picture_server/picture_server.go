// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package picture_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = programmatic_creative_types.GoUnusedProtection__

type PictureServer interface {
	dm303.DomobService

	// Parameters:
	//  - Header
	//  - Request
	AddAppPictureMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - Request
	//  - TemplateFilter
	AddTemplatePictureMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, template_filter *programmatic_creative_types.TemplateFilter) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - Request
	//  - Text
	AddAppTextPicture(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, text string) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - Request
	//  - Content
	AddAppPSDPicture(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, content []byte) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - Request
	//  - ProcessInfoList
	//  - ImageList
	BatchProcessImage(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, process_info_list []*programmatic_creative_types.PCProcessImageInfo, image_list []*programmatic_creative_types.PCImage) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - SrcImage
	//  - MattingPrecision
	//  - FeatherEdgeSize
	//  - RegionInfo
	ProcessImageMatting(header *common.RequestHeader, src_image *programmatic_creative_types.PCImage, matting_precision int32, feather_edge_size int32, region_info []*programmatic_creative_types.PCImageRegionInfo) (r *programmatic_creative_types.PCImage, rae *programmatic_creative_types.PCSException, err error)
	// 提取主颜色特征，返回值是包含颜色的列表
	// 使用算法MMCQ
	//
	// Parameters:
	//  - Header
	//  - SourceImage
	//  - NumColor
	ExtractColorFeature(header *common.RequestHeader, source_image *programmatic_creative_types.PCImage, num_color int32) (r []*programmatic_creative_types.PCImageColor, rae *programmatic_creative_types.PCSException, err error)
	// 计算图片的指纹信息，方便进行图片相似性检索
	// 使用算法D-Hash
	// 参数source_image中可选ugc_url 或者binary_content_bytes 传递图片信息
	// binary_content_bytes直接根据图片二进制信息进行计算
	//
	// additional_request包含参数:
	// hash_type: 不提供的话为dhash,
	// hash_size: 计算Hash的值，默认为8
	//
	// Parameters:
	//  - Header
	//  - SourceImage
	//  - AdditionalRequest
	GetImageFingerPrint(header *common.RequestHeader, source_image *programmatic_creative_types.PCImage, additional_request map[string]string) (r string, rae *programmatic_creative_types.PCSException, err error)
	// 得到两个图片指纹信息的距离
	// 一般认为距离为6以内为相同图片
	//
	// Parameters:
	//  - Header
	//  - ImgFingerprint1
	//  - ImgFingerprint2
	GetImageDhashDistance(header *common.RequestHeader, img_fingerprint1 string, img_fingerprint2 string) (r int32, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - Images
	CategorizeSimilarImage(header *common.RequestHeader, images []*programmatic_creative_types.PCImage) (r map[string][]int32, rae *programmatic_creative_types.PCSException, err error)
	// 分组相同创意
	// 对于富媒体创意来说，图片和视频都是由至少1个图像指纹构造而成
	//
	// Parameters:
	//  - Header
	//  - CreativeList
	//  - CategorizeType
	//  - AdditionalRequest
	CategorizeSimilarCreativeFingerPrint(header *common.RequestHeader, creative_list []*programmatic_creative_types.CreativeInfo, categorize_type programmatic_creative_types.PCCategorizeSimilarCreativeType, additional_request map[string]string) (r map[string][]int32, rae *programmatic_creative_types.PCSException, err error)
}

type PictureServerClient struct {
	*dm303.DomobServiceClient
}

func NewPictureServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *PictureServerClient {
	return &PictureServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewPictureServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *PictureServerClient {
	return &PictureServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Header
//  - Request
func (p *PictureServerClient) AddAppPictureMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddAppPictureMaterial(header, request); err != nil {
		return
	}
	return p.recvAddAppPictureMaterial()
}

func (p *PictureServerClient) sendAddAppPictureMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addAppPictureMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewAddAppPictureMaterialArgs()
	args0.Header = header
	args0.Request = request
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvAddAppPictureMaterial() (value programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewAddAppPictureMaterialResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Rae != nil {
		rae = result1.Rae
	}
	return
}

// Parameters:
//  - Header
//  - Request
//  - TemplateFilter
func (p *PictureServerClient) AddTemplatePictureMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, template_filter *programmatic_creative_types.TemplateFilter) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddTemplatePictureMaterial(header, request, template_filter); err != nil {
		return
	}
	return p.recvAddTemplatePictureMaterial()
}

func (p *PictureServerClient) sendAddTemplatePictureMaterial(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, template_filter *programmatic_creative_types.TemplateFilter) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addTemplatePictureMaterial", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewAddTemplatePictureMaterialArgs()
	args4.Header = header
	args4.Request = request
	args4.TemplateFilter = template_filter
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvAddTemplatePictureMaterial() (value programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewAddTemplatePictureMaterialResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Rae != nil {
		rae = result5.Rae
	}
	return
}

// Parameters:
//  - Header
//  - Request
//  - Text
func (p *PictureServerClient) AddAppTextPicture(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, text string) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddAppTextPicture(header, request, text); err != nil {
		return
	}
	return p.recvAddAppTextPicture()
}

func (p *PictureServerClient) sendAddAppTextPicture(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, text string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addAppTextPicture", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewAddAppTextPictureArgs()
	args8.Header = header
	args8.Request = request
	args8.Text = text
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvAddAppTextPicture() (value programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewAddAppTextPictureResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Rae != nil {
		rae = result9.Rae
	}
	return
}

// Parameters:
//  - Header
//  - Request
//  - Content
func (p *PictureServerClient) AddAppPSDPicture(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, content []byte) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAddAppPSDPicture(header, request, content); err != nil {
		return
	}
	return p.recvAddAppPSDPicture()
}

func (p *PictureServerClient) sendAddAppPSDPicture(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, content []byte) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addAppPSDPicture", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewAddAppPSDPictureArgs()
	args12.Header = header
	args12.Request = request
	args12.Content = content
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvAddAppPSDPicture() (value programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewAddAppPSDPictureResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.Rae != nil {
		rae = result13.Rae
	}
	return
}

// Parameters:
//  - Header
//  - Request
//  - ProcessInfoList
//  - ImageList
func (p *PictureServerClient) BatchProcessImage(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, process_info_list []*programmatic_creative_types.PCProcessImageInfo, image_list []*programmatic_creative_types.PCImage) (r programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendBatchProcessImage(header, request, process_info_list, image_list); err != nil {
		return
	}
	return p.recvBatchProcessImage()
}

func (p *PictureServerClient) sendBatchProcessImage(header *common.RequestHeader, request *programmatic_creative_types.AppMaterialRequest, process_info_list []*programmatic_creative_types.PCProcessImageInfo, image_list []*programmatic_creative_types.PCImage) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("batchProcessImage", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewBatchProcessImageArgs()
	args16.Header = header
	args16.Request = request
	args16.ProcessInfoList = process_info_list
	args16.ImageList = image_list
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvBatchProcessImage() (value programmatic_creative_types.PCSStatus, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewBatchProcessImageResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.Rae != nil {
		rae = result17.Rae
	}
	return
}

// Parameters:
//  - Header
//  - SrcImage
//  - MattingPrecision
//  - FeatherEdgeSize
//  - RegionInfo
func (p *PictureServerClient) ProcessImageMatting(header *common.RequestHeader, src_image *programmatic_creative_types.PCImage, matting_precision int32, feather_edge_size int32, region_info []*programmatic_creative_types.PCImageRegionInfo) (r *programmatic_creative_types.PCImage, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendProcessImageMatting(header, src_image, matting_precision, feather_edge_size, region_info); err != nil {
		return
	}
	return p.recvProcessImageMatting()
}

func (p *PictureServerClient) sendProcessImageMatting(header *common.RequestHeader, src_image *programmatic_creative_types.PCImage, matting_precision int32, feather_edge_size int32, region_info []*programmatic_creative_types.PCImageRegionInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("processImageMatting", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewProcessImageMattingArgs()
	args20.Header = header
	args20.SrcImage = src_image
	args20.MattingPrecision = matting_precision
	args20.FeatherEdgeSize = feather_edge_size
	args20.RegionInfo = region_info
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvProcessImageMatting() (value *programmatic_creative_types.PCImage, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewProcessImageMattingResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.Rae != nil {
		rae = result21.Rae
	}
	return
}

// 提取主颜色特征，返回值是包含颜色的列表
// 使用算法MMCQ
//
// Parameters:
//  - Header
//  - SourceImage
//  - NumColor
func (p *PictureServerClient) ExtractColorFeature(header *common.RequestHeader, source_image *programmatic_creative_types.PCImage, num_color int32) (r []*programmatic_creative_types.PCImageColor, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendExtractColorFeature(header, source_image, num_color); err != nil {
		return
	}
	return p.recvExtractColorFeature()
}

func (p *PictureServerClient) sendExtractColorFeature(header *common.RequestHeader, source_image *programmatic_creative_types.PCImage, num_color int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("extractColorFeature", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args24 := NewExtractColorFeatureArgs()
	args24.Header = header
	args24.SourceImage = source_image
	args24.NumColor = num_color
	if err = args24.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvExtractColorFeature() (value []*programmatic_creative_types.PCImageColor, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error26 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error27 error
		error27, err = error26.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error27
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result25 := NewExtractColorFeatureResult()
	if err = result25.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result25.Success
	if result25.Rae != nil {
		rae = result25.Rae
	}
	return
}

// 计算图片的指纹信息，方便进行图片相似性检索
// 使用算法D-Hash
// 参数source_image中可选ugc_url 或者binary_content_bytes 传递图片信息
// binary_content_bytes直接根据图片二进制信息进行计算
//
// additional_request包含参数:
// hash_type: 不提供的话为dhash,
// hash_size: 计算Hash的值，默认为8
//
// Parameters:
//  - Header
//  - SourceImage
//  - AdditionalRequest
func (p *PictureServerClient) GetImageFingerPrint(header *common.RequestHeader, source_image *programmatic_creative_types.PCImage, additional_request map[string]string) (r string, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetImageFingerPrint(header, source_image, additional_request); err != nil {
		return
	}
	return p.recvGetImageFingerPrint()
}

func (p *PictureServerClient) sendGetImageFingerPrint(header *common.RequestHeader, source_image *programmatic_creative_types.PCImage, additional_request map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getImageFingerPrint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args28 := NewGetImageFingerPrintArgs()
	args28.Header = header
	args28.SourceImage = source_image
	args28.AdditionalRequest = additional_request
	if err = args28.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvGetImageFingerPrint() (value string, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error30 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error31 error
		error31, err = error30.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error31
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result29 := NewGetImageFingerPrintResult()
	if err = result29.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result29.Success
	if result29.Rae != nil {
		rae = result29.Rae
	}
	return
}

// 得到两个图片指纹信息的距离
// 一般认为距离为6以内为相同图片
//
// Parameters:
//  - Header
//  - ImgFingerprint1
//  - ImgFingerprint2
func (p *PictureServerClient) GetImageDhashDistance(header *common.RequestHeader, img_fingerprint1 string, img_fingerprint2 string) (r int32, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetImageDhashDistance(header, img_fingerprint1, img_fingerprint2); err != nil {
		return
	}
	return p.recvGetImageDhashDistance()
}

func (p *PictureServerClient) sendGetImageDhashDistance(header *common.RequestHeader, img_fingerprint1 string, img_fingerprint2 string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getImageDhashDistance", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args32 := NewGetImageDhashDistanceArgs()
	args32.Header = header
	args32.ImgFingerprint1 = img_fingerprint1
	args32.ImgFingerprint2 = img_fingerprint2
	if err = args32.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvGetImageDhashDistance() (value int32, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error34 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error35 error
		error35, err = error34.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error35
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result33 := NewGetImageDhashDistanceResult()
	if err = result33.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result33.Success
	if result33.Rae != nil {
		rae = result33.Rae
	}
	return
}

// Parameters:
//  - Header
//  - Images
func (p *PictureServerClient) CategorizeSimilarImage(header *common.RequestHeader, images []*programmatic_creative_types.PCImage) (r map[string][]int32, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendCategorizeSimilarImage(header, images); err != nil {
		return
	}
	return p.recvCategorizeSimilarImage()
}

func (p *PictureServerClient) sendCategorizeSimilarImage(header *common.RequestHeader, images []*programmatic_creative_types.PCImage) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("categorizeSimilarImage", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args36 := NewCategorizeSimilarImageArgs()
	args36.Header = header
	args36.Images = images
	if err = args36.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvCategorizeSimilarImage() (value map[string][]int32, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error38 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error39 error
		error39, err = error38.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error39
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result37 := NewCategorizeSimilarImageResult()
	if err = result37.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result37.Success
	if result37.Rae != nil {
		rae = result37.Rae
	}
	return
}

// 分组相同创意
// 对于富媒体创意来说，图片和视频都是由至少1个图像指纹构造而成
//
// Parameters:
//  - Header
//  - CreativeList
//  - CategorizeType
//  - AdditionalRequest
func (p *PictureServerClient) CategorizeSimilarCreativeFingerPrint(header *common.RequestHeader, creative_list []*programmatic_creative_types.CreativeInfo, categorize_type programmatic_creative_types.PCCategorizeSimilarCreativeType, additional_request map[string]string) (r map[string][]int32, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendCategorizeSimilarCreativeFingerPrint(header, creative_list, categorize_type, additional_request); err != nil {
		return
	}
	return p.recvCategorizeSimilarCreativeFingerPrint()
}

func (p *PictureServerClient) sendCategorizeSimilarCreativeFingerPrint(header *common.RequestHeader, creative_list []*programmatic_creative_types.CreativeInfo, categorize_type programmatic_creative_types.PCCategorizeSimilarCreativeType, additional_request map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("categorizeSimilarCreativeFingerPrint", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args40 := NewCategorizeSimilarCreativeFingerPrintArgs()
	args40.Header = header
	args40.CreativeList = creative_list
	args40.CategorizeType = categorize_type
	args40.AdditionalRequest = additional_request
	if err = args40.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *PictureServerClient) recvCategorizeSimilarCreativeFingerPrint() (value map[string][]int32, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error42 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error43 error
		error43, err = error42.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error43
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result41 := NewCategorizeSimilarCreativeFingerPrintResult()
	if err = result41.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result41.Success
	if result41.Rae != nil {
		rae = result41.Rae
	}
	return
}

type PictureServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewPictureServerProcessor(handler PictureServer) *PictureServerProcessor {
	self44 := &PictureServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self44.AddToProcessorMap("addAppPictureMaterial", &pictureServerProcessorAddAppPictureMaterial{handler: handler})
	self44.AddToProcessorMap("addTemplatePictureMaterial", &pictureServerProcessorAddTemplatePictureMaterial{handler: handler})
	self44.AddToProcessorMap("addAppTextPicture", &pictureServerProcessorAddAppTextPicture{handler: handler})
	self44.AddToProcessorMap("addAppPSDPicture", &pictureServerProcessorAddAppPSDPicture{handler: handler})
	self44.AddToProcessorMap("batchProcessImage", &pictureServerProcessorBatchProcessImage{handler: handler})
	self44.AddToProcessorMap("processImageMatting", &pictureServerProcessorProcessImageMatting{handler: handler})
	self44.AddToProcessorMap("extractColorFeature", &pictureServerProcessorExtractColorFeature{handler: handler})
	self44.AddToProcessorMap("getImageFingerPrint", &pictureServerProcessorGetImageFingerPrint{handler: handler})
	self44.AddToProcessorMap("getImageDhashDistance", &pictureServerProcessorGetImageDhashDistance{handler: handler})
	self44.AddToProcessorMap("categorizeSimilarImage", &pictureServerProcessorCategorizeSimilarImage{handler: handler})
	self44.AddToProcessorMap("categorizeSimilarCreativeFingerPrint", &pictureServerProcessorCategorizeSimilarCreativeFingerPrint{handler: handler})
	return self44
}

type pictureServerProcessorAddAppPictureMaterial struct {
	handler PictureServer
}

func (p *pictureServerProcessorAddAppPictureMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAppPictureMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addAppPictureMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAppPictureMaterialResult()
	if result.Success, result.Rae, err = p.handler.AddAppPictureMaterial(args.Header, args.Request); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addAppPictureMaterial: "+err.Error())
		oprot.WriteMessageBegin("addAppPictureMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addAppPictureMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorAddTemplatePictureMaterial struct {
	handler PictureServer
}

func (p *pictureServerProcessorAddTemplatePictureMaterial) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddTemplatePictureMaterialArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addTemplatePictureMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddTemplatePictureMaterialResult()
	if result.Success, result.Rae, err = p.handler.AddTemplatePictureMaterial(args.Header, args.Request, args.TemplateFilter); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addTemplatePictureMaterial: "+err.Error())
		oprot.WriteMessageBegin("addTemplatePictureMaterial", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addTemplatePictureMaterial", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorAddAppTextPicture struct {
	handler PictureServer
}

func (p *pictureServerProcessorAddAppTextPicture) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAppTextPictureArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addAppTextPicture", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAppTextPictureResult()
	if result.Success, result.Rae, err = p.handler.AddAppTextPicture(args.Header, args.Request, args.Text); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addAppTextPicture: "+err.Error())
		oprot.WriteMessageBegin("addAppTextPicture", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addAppTextPicture", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorAddAppPSDPicture struct {
	handler PictureServer
}

func (p *pictureServerProcessorAddAppPSDPicture) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddAppPSDPictureArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addAppPSDPicture", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddAppPSDPictureResult()
	if result.Success, result.Rae, err = p.handler.AddAppPSDPicture(args.Header, args.Request, args.Content); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addAppPSDPicture: "+err.Error())
		oprot.WriteMessageBegin("addAppPSDPicture", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addAppPSDPicture", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorBatchProcessImage struct {
	handler PictureServer
}

func (p *pictureServerProcessorBatchProcessImage) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewBatchProcessImageArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("batchProcessImage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewBatchProcessImageResult()
	if result.Success, result.Rae, err = p.handler.BatchProcessImage(args.Header, args.Request, args.ProcessInfoList, args.ImageList); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing batchProcessImage: "+err.Error())
		oprot.WriteMessageBegin("batchProcessImage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("batchProcessImage", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorProcessImageMatting struct {
	handler PictureServer
}

func (p *pictureServerProcessorProcessImageMatting) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewProcessImageMattingArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("processImageMatting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewProcessImageMattingResult()
	if result.Success, result.Rae, err = p.handler.ProcessImageMatting(args.Header, args.SrcImage, args.MattingPrecision, args.FeatherEdgeSize, args.RegionInfo); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing processImageMatting: "+err.Error())
		oprot.WriteMessageBegin("processImageMatting", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("processImageMatting", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorExtractColorFeature struct {
	handler PictureServer
}

func (p *pictureServerProcessorExtractColorFeature) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExtractColorFeatureArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("extractColorFeature", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExtractColorFeatureResult()
	if result.Success, result.Rae, err = p.handler.ExtractColorFeature(args.Header, args.SourceImage, args.NumColor); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing extractColorFeature: "+err.Error())
		oprot.WriteMessageBegin("extractColorFeature", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("extractColorFeature", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorGetImageFingerPrint struct {
	handler PictureServer
}

func (p *pictureServerProcessorGetImageFingerPrint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetImageFingerPrintArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getImageFingerPrint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetImageFingerPrintResult()
	if result.Success, result.Rae, err = p.handler.GetImageFingerPrint(args.Header, args.SourceImage, args.AdditionalRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getImageFingerPrint: "+err.Error())
		oprot.WriteMessageBegin("getImageFingerPrint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getImageFingerPrint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorGetImageDhashDistance struct {
	handler PictureServer
}

func (p *pictureServerProcessorGetImageDhashDistance) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetImageDhashDistanceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getImageDhashDistance", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetImageDhashDistanceResult()
	if result.Success, result.Rae, err = p.handler.GetImageDhashDistance(args.Header, args.ImgFingerprint1, args.ImgFingerprint2); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getImageDhashDistance: "+err.Error())
		oprot.WriteMessageBegin("getImageDhashDistance", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getImageDhashDistance", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorCategorizeSimilarImage struct {
	handler PictureServer
}

func (p *pictureServerProcessorCategorizeSimilarImage) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCategorizeSimilarImageArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("categorizeSimilarImage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCategorizeSimilarImageResult()
	if result.Success, result.Rae, err = p.handler.CategorizeSimilarImage(args.Header, args.Images); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing categorizeSimilarImage: "+err.Error())
		oprot.WriteMessageBegin("categorizeSimilarImage", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("categorizeSimilarImage", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type pictureServerProcessorCategorizeSimilarCreativeFingerPrint struct {
	handler PictureServer
}

func (p *pictureServerProcessorCategorizeSimilarCreativeFingerPrint) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCategorizeSimilarCreativeFingerPrintArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("categorizeSimilarCreativeFingerPrint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCategorizeSimilarCreativeFingerPrintResult()
	if result.Success, result.Rae, err = p.handler.CategorizeSimilarCreativeFingerPrint(args.Header, args.CreativeList, args.CategorizeType, args.AdditionalRequest); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing categorizeSimilarCreativeFingerPrint: "+err.Error())
		oprot.WriteMessageBegin("categorizeSimilarCreativeFingerPrint", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("categorizeSimilarCreativeFingerPrint", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddAppPictureMaterialArgs struct {
	Header  *common.RequestHeader                           `thrift:"header,1" json:"header"`
	Request *programmatic_creative_types.AppMaterialRequest `thrift:"request,2" json:"request"`
}

func NewAddAppPictureMaterialArgs() *AddAppPictureMaterialArgs {
	return &AddAppPictureMaterialArgs{}
}

func (p *AddAppPictureMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppPictureMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAppPictureMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *AddAppPictureMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppPictureMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppPictureMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPictureMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPictureMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppPictureMaterialArgs(%+v)", *p)
}

type AddAppPictureMaterialResult struct {
	Success programmatic_creative_types.PCSStatus     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddAppPictureMaterialResult() *AddAppPictureMaterialResult {
	return &AddAppPictureMaterialResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddAppPictureMaterialResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddAppPictureMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppPictureMaterialResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = programmatic_creative_types.PCSStatus(v)
	}
	return nil
}

func (p *AddAppPictureMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddAppPictureMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppPictureMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppPictureMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPictureMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPictureMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppPictureMaterialResult(%+v)", *p)
}

type AddTemplatePictureMaterialArgs struct {
	Header         *common.RequestHeader                           `thrift:"header,1" json:"header"`
	Request        *programmatic_creative_types.AppMaterialRequest `thrift:"request,2" json:"request"`
	TemplateFilter *programmatic_creative_types.TemplateFilter     `thrift:"template_filter,3" json:"template_filter"`
}

func NewAddTemplatePictureMaterialArgs() *AddTemplatePictureMaterialArgs {
	return &AddTemplatePictureMaterialArgs{}
}

func (p *AddTemplatePictureMaterialArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTemplatePictureMaterialArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddTemplatePictureMaterialArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *AddTemplatePictureMaterialArgs) readField3(iprot thrift.TProtocol) error {
	p.TemplateFilter = programmatic_creative_types.NewTemplateFilter()
	if err := p.TemplateFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TemplateFilter)
	}
	return nil
}

func (p *AddTemplatePictureMaterialArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTemplatePictureMaterial_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTemplatePictureMaterialArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplatePictureMaterialArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplatePictureMaterialArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.TemplateFilter != nil {
		if err := oprot.WriteFieldBegin("template_filter", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:template_filter: %s", p, err)
		}
		if err := p.TemplateFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TemplateFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:template_filter: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplatePictureMaterialArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTemplatePictureMaterialArgs(%+v)", *p)
}

type AddTemplatePictureMaterialResult struct {
	Success programmatic_creative_types.PCSStatus     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddTemplatePictureMaterialResult() *AddTemplatePictureMaterialResult {
	return &AddTemplatePictureMaterialResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddTemplatePictureMaterialResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddTemplatePictureMaterialResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddTemplatePictureMaterialResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = programmatic_creative_types.PCSStatus(v)
	}
	return nil
}

func (p *AddTemplatePictureMaterialResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddTemplatePictureMaterialResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addTemplatePictureMaterial_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddTemplatePictureMaterialResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplatePictureMaterialResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddTemplatePictureMaterialResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddTemplatePictureMaterialResult(%+v)", *p)
}

type AddAppTextPictureArgs struct {
	Header  *common.RequestHeader                           `thrift:"header,1" json:"header"`
	Request *programmatic_creative_types.AppMaterialRequest `thrift:"request,2" json:"request"`
	Text    string                                          `thrift:"text,3" json:"text"`
}

func NewAddAppTextPictureArgs() *AddAppTextPictureArgs {
	return &AddAppTextPictureArgs{}
}

func (p *AddAppTextPictureArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppTextPictureArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAppTextPictureArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *AddAppTextPictureArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *AddAppTextPictureArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppTextPicture_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppTextPictureArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAppTextPictureArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *AddAppTextPictureArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:text: %s", p, err)
	}
	return err
}

func (p *AddAppTextPictureArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppTextPictureArgs(%+v)", *p)
}

type AddAppTextPictureResult struct {
	Success programmatic_creative_types.PCSStatus     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddAppTextPictureResult() *AddAppTextPictureResult {
	return &AddAppTextPictureResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddAppTextPictureResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddAppTextPictureResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppTextPictureResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = programmatic_creative_types.PCSStatus(v)
	}
	return nil
}

func (p *AddAppTextPictureResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddAppTextPictureResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppTextPicture_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppTextPictureResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddAppTextPictureResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddAppTextPictureResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppTextPictureResult(%+v)", *p)
}

type AddAppPSDPictureArgs struct {
	Header  *common.RequestHeader                           `thrift:"header,1" json:"header"`
	Request *programmatic_creative_types.AppMaterialRequest `thrift:"request,2" json:"request"`
	Content []byte                                          `thrift:"content,3" json:"content"`
}

func NewAddAppPSDPictureArgs() *AddAppPSDPictureArgs {
	return &AddAppPSDPictureArgs{}
}

func (p *AddAppPSDPictureArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppPSDPictureArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddAppPSDPictureArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *AddAppPSDPictureArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBinary(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Content = v
	}
	return nil
}

func (p *AddAppPSDPictureArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppPSDPicture_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppPSDPictureArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPSDPictureArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPSDPictureArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Content != nil {
		if err := oprot.WriteFieldBegin("content", thrift.STRING, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:content: %s", p, err)
		}
		if err := oprot.WriteBinary(p.Content); err != nil {
			return fmt.Errorf("%T.content (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:content: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPSDPictureArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppPSDPictureArgs(%+v)", *p)
}

type AddAppPSDPictureResult struct {
	Success programmatic_creative_types.PCSStatus     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewAddAppPSDPictureResult() *AddAppPSDPictureResult {
	return &AddAppPSDPictureResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddAppPSDPictureResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddAppPSDPictureResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddAppPSDPictureResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = programmatic_creative_types.PCSStatus(v)
	}
	return nil
}

func (p *AddAppPSDPictureResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AddAppPSDPictureResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addAppPSDPicture_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddAppPSDPictureResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPSDPictureResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AddAppPSDPictureResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddAppPSDPictureResult(%+v)", *p)
}

type BatchProcessImageArgs struct {
	Header          *common.RequestHeader                             `thrift:"header,1" json:"header"`
	Request         *programmatic_creative_types.AppMaterialRequest   `thrift:"request,2" json:"request"`
	ProcessInfoList []*programmatic_creative_types.PCProcessImageInfo `thrift:"process_info_list,3" json:"process_info_list"`
	ImageList       []*programmatic_creative_types.PCImage            `thrift:"image_list,4" json:"image_list"`
}

func NewBatchProcessImageArgs() *BatchProcessImageArgs {
	return &BatchProcessImageArgs{}
}

func (p *BatchProcessImageArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BatchProcessImageArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *BatchProcessImageArgs) readField2(iprot thrift.TProtocol) error {
	p.Request = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.Request.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Request)
	}
	return nil
}

func (p *BatchProcessImageArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ProcessInfoList = make([]*programmatic_creative_types.PCProcessImageInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem45 := programmatic_creative_types.NewPCProcessImageInfo()
		if err := _elem45.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem45)
		}
		p.ProcessInfoList = append(p.ProcessInfoList, _elem45)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *BatchProcessImageArgs) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImageList = make([]*programmatic_creative_types.PCImage, 0, size)
	for i := 0; i < size; i++ {
		_elem46 := programmatic_creative_types.NewPCImage()
		if err := _elem46.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem46)
		}
		p.ImageList = append(p.ImageList, _elem46)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *BatchProcessImageArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("batchProcessImage_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BatchProcessImageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *BatchProcessImageArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Request != nil {
		if err := oprot.WriteFieldBegin("request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:request: %s", p, err)
		}
		if err := p.Request.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Request)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:request: %s", p, err)
		}
	}
	return err
}

func (p *BatchProcessImageArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.ProcessInfoList != nil {
		if err := oprot.WriteFieldBegin("process_info_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:process_info_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ProcessInfoList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ProcessInfoList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:process_info_list: %s", p, err)
		}
	}
	return err
}

func (p *BatchProcessImageArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ImageList != nil {
		if err := oprot.WriteFieldBegin("image_list", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:image_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImageList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImageList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:image_list: %s", p, err)
		}
	}
	return err
}

func (p *BatchProcessImageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchProcessImageArgs(%+v)", *p)
}

type BatchProcessImageResult struct {
	Success programmatic_creative_types.PCSStatus     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewBatchProcessImageResult() *BatchProcessImageResult {
	return &BatchProcessImageResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BatchProcessImageResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *BatchProcessImageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BatchProcessImageResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = programmatic_creative_types.PCSStatus(v)
	}
	return nil
}

func (p *BatchProcessImageResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *BatchProcessImageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("batchProcessImage_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BatchProcessImageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *BatchProcessImageResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *BatchProcessImageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BatchProcessImageResult(%+v)", *p)
}

type ProcessImageMattingArgs struct {
	Header           *common.RequestHeader                            `thrift:"header,1" json:"header"`
	SrcImage         *programmatic_creative_types.PCImage             `thrift:"src_image,2" json:"src_image"`
	MattingPrecision int32                                            `thrift:"matting_precision,3" json:"matting_precision"`
	FeatherEdgeSize  int32                                            `thrift:"feather_edge_size,4" json:"feather_edge_size"`
	RegionInfo       []*programmatic_creative_types.PCImageRegionInfo `thrift:"region_info,5" json:"region_info"`
}

func NewProcessImageMattingArgs() *ProcessImageMattingArgs {
	return &ProcessImageMattingArgs{}
}

func (p *ProcessImageMattingArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessImageMattingArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ProcessImageMattingArgs) readField2(iprot thrift.TProtocol) error {
	p.SrcImage = programmatic_creative_types.NewPCImage()
	if err := p.SrcImage.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SrcImage)
	}
	return nil
}

func (p *ProcessImageMattingArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.MattingPrecision = v
	}
	return nil
}

func (p *ProcessImageMattingArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FeatherEdgeSize = v
	}
	return nil
}

func (p *ProcessImageMattingArgs) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RegionInfo = make([]*programmatic_creative_types.PCImageRegionInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem47 := programmatic_creative_types.NewPCImageRegionInfo()
		if err := _elem47.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem47)
		}
		p.RegionInfo = append(p.RegionInfo, _elem47)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ProcessImageMattingArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processImageMatting_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessImageMattingArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ProcessImageMattingArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SrcImage != nil {
		if err := oprot.WriteFieldBegin("src_image", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:src_image: %s", p, err)
		}
		if err := p.SrcImage.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SrcImage)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:src_image: %s", p, err)
		}
	}
	return err
}

func (p *ProcessImageMattingArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("matting_precision", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:matting_precision: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MattingPrecision)); err != nil {
		return fmt.Errorf("%T.matting_precision (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:matting_precision: %s", p, err)
	}
	return err
}

func (p *ProcessImageMattingArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("feather_edge_size", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:feather_edge_size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FeatherEdgeSize)); err != nil {
		return fmt.Errorf("%T.feather_edge_size (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:feather_edge_size: %s", p, err)
	}
	return err
}

func (p *ProcessImageMattingArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.RegionInfo != nil {
		if err := oprot.WriteFieldBegin("region_info", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:region_info: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RegionInfo)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RegionInfo {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:region_info: %s", p, err)
		}
	}
	return err
}

func (p *ProcessImageMattingArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessImageMattingArgs(%+v)", *p)
}

type ProcessImageMattingResult struct {
	Success *programmatic_creative_types.PCImage      `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewProcessImageMattingResult() *ProcessImageMattingResult {
	return &ProcessImageMattingResult{}
}

func (p *ProcessImageMattingResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ProcessImageMattingResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCImage()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ProcessImageMattingResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *ProcessImageMattingResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("processImageMatting_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ProcessImageMattingResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ProcessImageMattingResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *ProcessImageMattingResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ProcessImageMattingResult(%+v)", *p)
}

type ExtractColorFeatureArgs struct {
	Header      *common.RequestHeader                `thrift:"header,1" json:"header"`
	SourceImage *programmatic_creative_types.PCImage `thrift:"source_image,2" json:"source_image"`
	NumColor    int32                                `thrift:"num_color,3" json:"num_color"`
}

func NewExtractColorFeatureArgs() *ExtractColorFeatureArgs {
	return &ExtractColorFeatureArgs{}
}

func (p *ExtractColorFeatureArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExtractColorFeatureArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExtractColorFeatureArgs) readField2(iprot thrift.TProtocol) error {
	p.SourceImage = programmatic_creative_types.NewPCImage()
	if err := p.SourceImage.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SourceImage)
	}
	return nil
}

func (p *ExtractColorFeatureArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NumColor = v
	}
	return nil
}

func (p *ExtractColorFeatureArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("extractColorFeature_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExtractColorFeatureArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExtractColorFeatureArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SourceImage != nil {
		if err := oprot.WriteFieldBegin("source_image", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:source_image: %s", p, err)
		}
		if err := p.SourceImage.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SourceImage)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:source_image: %s", p, err)
		}
	}
	return err
}

func (p *ExtractColorFeatureArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("num_color", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:num_color: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NumColor)); err != nil {
		return fmt.Errorf("%T.num_color (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:num_color: %s", p, err)
	}
	return err
}

func (p *ExtractColorFeatureArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtractColorFeatureArgs(%+v)", *p)
}

type ExtractColorFeatureResult struct {
	Success []*programmatic_creative_types.PCImageColor `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException   `thrift:"rae,1" json:"rae"`
}

func NewExtractColorFeatureResult() *ExtractColorFeatureResult {
	return &ExtractColorFeatureResult{}
}

func (p *ExtractColorFeatureResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExtractColorFeatureResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*programmatic_creative_types.PCImageColor, 0, size)
	for i := 0; i < size; i++ {
		_elem48 := programmatic_creative_types.NewPCImageColor()
		if err := _elem48.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem48)
		}
		p.Success = append(p.Success, _elem48)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *ExtractColorFeatureResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *ExtractColorFeatureResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("extractColorFeature_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExtractColorFeatureResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ExtractColorFeatureResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *ExtractColorFeatureResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExtractColorFeatureResult(%+v)", *p)
}

type GetImageFingerPrintArgs struct {
	Header            *common.RequestHeader                `thrift:"header,1" json:"header"`
	SourceImage       *programmatic_creative_types.PCImage `thrift:"source_image,2" json:"source_image"`
	AdditionalRequest map[string]string                    `thrift:"additional_request,3" json:"additional_request"`
}

func NewGetImageFingerPrintArgs() *GetImageFingerPrintArgs {
	return &GetImageFingerPrintArgs{}
}

func (p *GetImageFingerPrintArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.MAP {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetImageFingerPrintArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetImageFingerPrintArgs) readField2(iprot thrift.TProtocol) error {
	p.SourceImage = programmatic_creative_types.NewPCImage()
	if err := p.SourceImage.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SourceImage)
	}
	return nil
}

func (p *GetImageFingerPrintArgs) readField3(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AdditionalRequest = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key49 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key49 = v
		}
		var _val50 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val50 = v
		}
		p.AdditionalRequest[_key49] = _val50
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetImageFingerPrintArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getImageFingerPrint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetImageFingerPrintArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetImageFingerPrintArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SourceImage != nil {
		if err := oprot.WriteFieldBegin("source_image", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:source_image: %s", p, err)
		}
		if err := p.SourceImage.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SourceImage)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:source_image: %s", p, err)
		}
	}
	return err
}

func (p *GetImageFingerPrintArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.AdditionalRequest != nil {
		if err := oprot.WriteFieldBegin("additional_request", thrift.MAP, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:additional_request: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AdditionalRequest)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AdditionalRequest {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:additional_request: %s", p, err)
		}
	}
	return err
}

func (p *GetImageFingerPrintArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetImageFingerPrintArgs(%+v)", *p)
}

type GetImageFingerPrintResult struct {
	Success string                                    `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewGetImageFingerPrintResult() *GetImageFingerPrintResult {
	return &GetImageFingerPrintResult{}
}

func (p *GetImageFingerPrintResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetImageFingerPrintResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetImageFingerPrintResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetImageFingerPrintResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getImageFingerPrint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetImageFingerPrintResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetImageFingerPrintResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetImageFingerPrintResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetImageFingerPrintResult(%+v)", *p)
}

type GetImageDhashDistanceArgs struct {
	Header          *common.RequestHeader `thrift:"header,1" json:"header"`
	ImgFingerprint1 string                `thrift:"img_fingerprint1,2" json:"img_fingerprint1"`
	ImgFingerprint2 string                `thrift:"img_fingerprint2,3" json:"img_fingerprint2"`
}

func NewGetImageDhashDistanceArgs() *GetImageDhashDistanceArgs {
	return &GetImageDhashDistanceArgs{}
}

func (p *GetImageDhashDistanceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetImageDhashDistanceArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetImageDhashDistanceArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ImgFingerprint1 = v
	}
	return nil
}

func (p *GetImageDhashDistanceArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ImgFingerprint2 = v
	}
	return nil
}

func (p *GetImageDhashDistanceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getImageDhashDistance_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetImageDhashDistanceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetImageDhashDistanceArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img_fingerprint1", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:img_fingerprint1: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgFingerprint1)); err != nil {
		return fmt.Errorf("%T.img_fingerprint1 (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:img_fingerprint1: %s", p, err)
	}
	return err
}

func (p *GetImageDhashDistanceArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("img_fingerprint2", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:img_fingerprint2: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImgFingerprint2)); err != nil {
		return fmt.Errorf("%T.img_fingerprint2 (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:img_fingerprint2: %s", p, err)
	}
	return err
}

func (p *GetImageDhashDistanceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetImageDhashDistanceArgs(%+v)", *p)
}

type GetImageDhashDistanceResult struct {
	Success int32                                     `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewGetImageDhashDistanceResult() *GetImageDhashDistanceResult {
	return &GetImageDhashDistanceResult{}
}

func (p *GetImageDhashDistanceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetImageDhashDistanceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *GetImageDhashDistanceResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetImageDhashDistanceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getImageDhashDistance_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetImageDhashDistanceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *GetImageDhashDistanceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetImageDhashDistanceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetImageDhashDistanceResult(%+v)", *p)
}

type CategorizeSimilarImageArgs struct {
	Header *common.RequestHeader                  `thrift:"header,1" json:"header"`
	Images []*programmatic_creative_types.PCImage `thrift:"images,2" json:"images"`
}

func NewCategorizeSimilarImageArgs() *CategorizeSimilarImageArgs {
	return &CategorizeSimilarImageArgs{}
}

func (p *CategorizeSimilarImageArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarImageArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CategorizeSimilarImageArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Images = make([]*programmatic_creative_types.PCImage, 0, size)
	for i := 0; i < size; i++ {
		_elem51 := programmatic_creative_types.NewPCImage()
		if err := _elem51.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem51)
		}
		p.Images = append(p.Images, _elem51)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CategorizeSimilarImageArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("categorizeSimilarImage_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarImageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarImageArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Images != nil {
		if err := oprot.WriteFieldBegin("images", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:images: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Images)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Images {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:images: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarImageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CategorizeSimilarImageArgs(%+v)", *p)
}

type CategorizeSimilarImageResult struct {
	Success map[string][]int32                        `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewCategorizeSimilarImageResult() *CategorizeSimilarImageResult {
	return &CategorizeSimilarImageResult{}
}

func (p *CategorizeSimilarImageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarImageResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string][]int32, size)
	for i := 0; i < size; i++ {
		var _key52 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key52 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val53 := make([]int32, 0, size)
		for i := 0; i < size; i++ {
			var _elem54 int32
			if v, err := iprot.ReadI32(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_elem54 = v
			}
			_val53 = append(_val53, _elem54)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key52] = _val53
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CategorizeSimilarImageResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *CategorizeSimilarImageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("categorizeSimilarImage_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarImageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteI32(int32(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarImageResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarImageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CategorizeSimilarImageResult(%+v)", *p)
}

type CategorizeSimilarCreativeFingerPrintArgs struct {
	Header            *common.RequestHeader                                       `thrift:"header,1" json:"header"`
	CreativeList      []*programmatic_creative_types.CreativeInfo                 `thrift:"creative_list,2" json:"creative_list"`
	CategorizeType    programmatic_creative_types.PCCategorizeSimilarCreativeType `thrift:"categorize_type,3" json:"categorize_type"`
	AdditionalRequest map[string]string                                           `thrift:"additional_request,4" json:"additional_request"`
}

func NewCategorizeSimilarCreativeFingerPrintArgs() *CategorizeSimilarCreativeFingerPrintArgs {
	return &CategorizeSimilarCreativeFingerPrintArgs{
		CategorizeType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) IsSetCategorizeType() bool {
	return int64(p.CategorizeType) != math.MinInt32-1
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeList = make([]*programmatic_creative_types.CreativeInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem55 := programmatic_creative_types.NewCreativeInfo()
		if err := _elem55.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem55)
		}
		p.CreativeList = append(p.CreativeList, _elem55)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CategorizeType = programmatic_creative_types.PCCategorizeSimilarCreativeType(v)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) readField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.AdditionalRequest = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key56 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key56 = v
		}
		var _val57 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val57 = v
		}
		p.AdditionalRequest[_key56] = _val57
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("categorizeSimilarCreativeFingerPrint_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.CreativeList != nil {
		if err := oprot.WriteFieldBegin("creative_list", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:creative_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreativeList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:creative_list: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCategorizeType() {
		if err := oprot.WriteFieldBegin("categorize_type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:categorize_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CategorizeType)); err != nil {
			return fmt.Errorf("%T.categorize_type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:categorize_type: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.AdditionalRequest != nil {
		if err := oprot.WriteFieldBegin("additional_request", thrift.MAP, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:additional_request: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.AdditionalRequest)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.AdditionalRequest {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:additional_request: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarCreativeFingerPrintArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CategorizeSimilarCreativeFingerPrintArgs(%+v)", *p)
}

type CategorizeSimilarCreativeFingerPrintResult struct {
	Success map[string][]int32                        `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException `thrift:"rae,1" json:"rae"`
}

func NewCategorizeSimilarCreativeFingerPrintResult() *CategorizeSimilarCreativeFingerPrintResult {
	return &CategorizeSimilarCreativeFingerPrintResult{}
}

func (p *CategorizeSimilarCreativeFingerPrintResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string][]int32, size)
	for i := 0; i < size; i++ {
		var _key58 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key58 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val59 := make([]int32, 0, size)
		for i := 0; i < size; i++ {
			var _elem60 int32
			if v, err := iprot.ReadI32(); err != nil {
				return fmt.Errorf("error reading field 0: %s", err)
			} else {
				_elem60 = v
			}
			_val59 = append(_val59, _elem60)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key58] = _val59
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("categorizeSimilarCreativeFingerPrint_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CategorizeSimilarCreativeFingerPrintResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.I32, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := oprot.WriteI32(int32(v)); err != nil {
					return fmt.Errorf("%T. (0) field write error: %s", p, err)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarCreativeFingerPrintResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *CategorizeSimilarCreativeFingerPrintResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CategorizeSimilarCreativeFingerPrintResult(%+v)", *p)
}
