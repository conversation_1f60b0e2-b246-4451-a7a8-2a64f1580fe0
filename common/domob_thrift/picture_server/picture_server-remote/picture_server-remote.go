// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"picture_server"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  PCSStatus addAppPictureMaterial(RequestHeader header, AppMaterialRequest request)")
	fmt.Fprintln(os.Stderr, "  PCSStatus addTemplatePictureMaterial(RequestHeader header, AppMaterialRequest request, TemplateFilter template_filter)")
	fmt.Fprintln(os.Stderr, "  PCSStatus addAppTextPicture(RequestHeader header, AppMaterialRequest request, string text)")
	fmt.Fprintln(os.Stderr, "  PCSStatus addAppPSDPicture(RequestHeader header, AppMaterialRequest request, string content)")
	fmt.Fprintln(os.Stderr, "  PCSStatus batchProcessImage(RequestHeader header, AppMaterialRequest request,  process_info_list,  image_list)")
	fmt.Fprintln(os.Stderr, "  PCImage processImageMatting(RequestHeader header, PCImage src_image, i32 matting_precision, i32 feather_edge_size,  region_info)")
	fmt.Fprintln(os.Stderr, "   extractColorFeature(RequestHeader header, PCImage source_image, i32 num_color)")
	fmt.Fprintln(os.Stderr, "  string getImageFingerPrint(RequestHeader header, PCImage source_image,  additional_request)")
	fmt.Fprintln(os.Stderr, "  i32 getImageDhashDistance(RequestHeader header, string img_fingerprint1, string img_fingerprint2)")
	fmt.Fprintln(os.Stderr, "   categorizeSimilarImage(RequestHeader header,  images)")
	fmt.Fprintln(os.Stderr, "   categorizeSimilarCreativeFingerPrint(RequestHeader header,  creative_list, PCCategorizeSimilarCreativeType categorize_type,  additional_request)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := picture_server.NewPictureServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addAppPictureMaterial":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAppPictureMaterial requires 2 args")
			flag.Usage()
		}
		arg61 := flag.Arg(1)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue0 := picture_server.NewRequestHeader()
		err66 := argvalue0.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg67 := flag.Arg(2)
		mbTrans68 := thrift.NewTMemoryBufferLen(len(arg67))
		defer mbTrans68.Close()
		_, err69 := mbTrans68.WriteString(arg67)
		if err69 != nil {
			Usage()
			return
		}
		factory70 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt71 := factory70.GetProtocol(mbTrans68)
		argvalue1 := picture_server.NewAppMaterialRequest()
		err72 := argvalue1.Read(jsProt71)
		if err72 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAppPictureMaterial(value0, value1))
		fmt.Print("\n")
		break
	case "addTemplatePictureMaterial":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddTemplatePictureMaterial requires 3 args")
			flag.Usage()
		}
		arg73 := flag.Arg(1)
		mbTrans74 := thrift.NewTMemoryBufferLen(len(arg73))
		defer mbTrans74.Close()
		_, err75 := mbTrans74.WriteString(arg73)
		if err75 != nil {
			Usage()
			return
		}
		factory76 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt77 := factory76.GetProtocol(mbTrans74)
		argvalue0 := picture_server.NewRequestHeader()
		err78 := argvalue0.Read(jsProt77)
		if err78 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg79 := flag.Arg(2)
		mbTrans80 := thrift.NewTMemoryBufferLen(len(arg79))
		defer mbTrans80.Close()
		_, err81 := mbTrans80.WriteString(arg79)
		if err81 != nil {
			Usage()
			return
		}
		factory82 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt83 := factory82.GetProtocol(mbTrans80)
		argvalue1 := picture_server.NewAppMaterialRequest()
		err84 := argvalue1.Read(jsProt83)
		if err84 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg85 := flag.Arg(3)
		mbTrans86 := thrift.NewTMemoryBufferLen(len(arg85))
		defer mbTrans86.Close()
		_, err87 := mbTrans86.WriteString(arg85)
		if err87 != nil {
			Usage()
			return
		}
		factory88 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt89 := factory88.GetProtocol(mbTrans86)
		argvalue2 := picture_server.NewTemplateFilter()
		err90 := argvalue2.Read(jsProt89)
		if err90 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddTemplatePictureMaterial(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAppTextPicture":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAppTextPicture requires 3 args")
			flag.Usage()
		}
		arg91 := flag.Arg(1)
		mbTrans92 := thrift.NewTMemoryBufferLen(len(arg91))
		defer mbTrans92.Close()
		_, err93 := mbTrans92.WriteString(arg91)
		if err93 != nil {
			Usage()
			return
		}
		factory94 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt95 := factory94.GetProtocol(mbTrans92)
		argvalue0 := picture_server.NewRequestHeader()
		err96 := argvalue0.Read(jsProt95)
		if err96 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg97 := flag.Arg(2)
		mbTrans98 := thrift.NewTMemoryBufferLen(len(arg97))
		defer mbTrans98.Close()
		_, err99 := mbTrans98.WriteString(arg97)
		if err99 != nil {
			Usage()
			return
		}
		factory100 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt101 := factory100.GetProtocol(mbTrans98)
		argvalue1 := picture_server.NewAppMaterialRequest()
		err102 := argvalue1.Read(jsProt101)
		if err102 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.AddAppTextPicture(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAppPSDPicture":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAppPSDPicture requires 3 args")
			flag.Usage()
		}
		arg104 := flag.Arg(1)
		mbTrans105 := thrift.NewTMemoryBufferLen(len(arg104))
		defer mbTrans105.Close()
		_, err106 := mbTrans105.WriteString(arg104)
		if err106 != nil {
			Usage()
			return
		}
		factory107 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt108 := factory107.GetProtocol(mbTrans105)
		argvalue0 := picture_server.NewRequestHeader()
		err109 := argvalue0.Read(jsProt108)
		if err109 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg110 := flag.Arg(2)
		mbTrans111 := thrift.NewTMemoryBufferLen(len(arg110))
		defer mbTrans111.Close()
		_, err112 := mbTrans111.WriteString(arg110)
		if err112 != nil {
			Usage()
			return
		}
		factory113 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt114 := factory113.GetProtocol(mbTrans111)
		argvalue1 := picture_server.NewAppMaterialRequest()
		err115 := argvalue1.Read(jsProt114)
		if err115 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.AddAppPSDPicture(value0, value1, value2))
		fmt.Print("\n")
		break
	case "batchProcessImage":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "BatchProcessImage requires 4 args")
			flag.Usage()
		}
		arg117 := flag.Arg(1)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		argvalue0 := picture_server.NewRequestHeader()
		err122 := argvalue0.Read(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg123 := flag.Arg(2)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue1 := picture_server.NewAppMaterialRequest()
		err128 := argvalue1.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg129 := flag.Arg(3)
		mbTrans130 := thrift.NewTMemoryBufferLen(len(arg129))
		defer mbTrans130.Close()
		_, err131 := mbTrans130.WriteString(arg129)
		if err131 != nil {
			Usage()
			return
		}
		factory132 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt133 := factory132.GetProtocol(mbTrans130)
		containerStruct2 := picture_server.NewBatchProcessImageArgs()
		err134 := containerStruct2.ReadField3(jsProt133)
		if err134 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ProcessInfoList
		value2 := argvalue2
		arg135 := flag.Arg(4)
		mbTrans136 := thrift.NewTMemoryBufferLen(len(arg135))
		defer mbTrans136.Close()
		_, err137 := mbTrans136.WriteString(arg135)
		if err137 != nil {
			Usage()
			return
		}
		factory138 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt139 := factory138.GetProtocol(mbTrans136)
		containerStruct3 := picture_server.NewBatchProcessImageArgs()
		err140 := containerStruct3.ReadField4(jsProt139)
		if err140 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.ImageList
		value3 := argvalue3
		fmt.Print(client.BatchProcessImage(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "processImageMatting":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ProcessImageMatting requires 5 args")
			flag.Usage()
		}
		arg141 := flag.Arg(1)
		mbTrans142 := thrift.NewTMemoryBufferLen(len(arg141))
		defer mbTrans142.Close()
		_, err143 := mbTrans142.WriteString(arg141)
		if err143 != nil {
			Usage()
			return
		}
		factory144 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt145 := factory144.GetProtocol(mbTrans142)
		argvalue0 := picture_server.NewRequestHeader()
		err146 := argvalue0.Read(jsProt145)
		if err146 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg147 := flag.Arg(2)
		mbTrans148 := thrift.NewTMemoryBufferLen(len(arg147))
		defer mbTrans148.Close()
		_, err149 := mbTrans148.WriteString(arg147)
		if err149 != nil {
			Usage()
			return
		}
		factory150 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt151 := factory150.GetProtocol(mbTrans148)
		argvalue1 := picture_server.NewPCImage()
		err152 := argvalue1.Read(jsProt151)
		if err152 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err153 := (strconv.Atoi(flag.Arg(3)))
		if err153 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err154 := (strconv.Atoi(flag.Arg(4)))
		if err154 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg155 := flag.Arg(5)
		mbTrans156 := thrift.NewTMemoryBufferLen(len(arg155))
		defer mbTrans156.Close()
		_, err157 := mbTrans156.WriteString(arg155)
		if err157 != nil {
			Usage()
			return
		}
		factory158 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt159 := factory158.GetProtocol(mbTrans156)
		containerStruct4 := picture_server.NewProcessImageMattingArgs()
		err160 := containerStruct4.ReadField5(jsProt159)
		if err160 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.RegionInfo
		value4 := argvalue4
		fmt.Print(client.ProcessImageMatting(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "extractColorFeature":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ExtractColorFeature requires 3 args")
			flag.Usage()
		}
		arg161 := flag.Arg(1)
		mbTrans162 := thrift.NewTMemoryBufferLen(len(arg161))
		defer mbTrans162.Close()
		_, err163 := mbTrans162.WriteString(arg161)
		if err163 != nil {
			Usage()
			return
		}
		factory164 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt165 := factory164.GetProtocol(mbTrans162)
		argvalue0 := picture_server.NewRequestHeader()
		err166 := argvalue0.Read(jsProt165)
		if err166 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg167 := flag.Arg(2)
		mbTrans168 := thrift.NewTMemoryBufferLen(len(arg167))
		defer mbTrans168.Close()
		_, err169 := mbTrans168.WriteString(arg167)
		if err169 != nil {
			Usage()
			return
		}
		factory170 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt171 := factory170.GetProtocol(mbTrans168)
		argvalue1 := picture_server.NewPCImage()
		err172 := argvalue1.Read(jsProt171)
		if err172 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err173 := (strconv.Atoi(flag.Arg(3)))
		if err173 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.ExtractColorFeature(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getImageFingerPrint":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetImageFingerPrint requires 3 args")
			flag.Usage()
		}
		arg174 := flag.Arg(1)
		mbTrans175 := thrift.NewTMemoryBufferLen(len(arg174))
		defer mbTrans175.Close()
		_, err176 := mbTrans175.WriteString(arg174)
		if err176 != nil {
			Usage()
			return
		}
		factory177 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt178 := factory177.GetProtocol(mbTrans175)
		argvalue0 := picture_server.NewRequestHeader()
		err179 := argvalue0.Read(jsProt178)
		if err179 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg180 := flag.Arg(2)
		mbTrans181 := thrift.NewTMemoryBufferLen(len(arg180))
		defer mbTrans181.Close()
		_, err182 := mbTrans181.WriteString(arg180)
		if err182 != nil {
			Usage()
			return
		}
		factory183 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt184 := factory183.GetProtocol(mbTrans181)
		argvalue1 := picture_server.NewPCImage()
		err185 := argvalue1.Read(jsProt184)
		if err185 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		arg186 := flag.Arg(3)
		mbTrans187 := thrift.NewTMemoryBufferLen(len(arg186))
		defer mbTrans187.Close()
		_, err188 := mbTrans187.WriteString(arg186)
		if err188 != nil {
			Usage()
			return
		}
		factory189 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt190 := factory189.GetProtocol(mbTrans187)
		containerStruct2 := picture_server.NewGetImageFingerPrintArgs()
		err191 := containerStruct2.ReadField3(jsProt190)
		if err191 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.AdditionalRequest
		value2 := argvalue2
		fmt.Print(client.GetImageFingerPrint(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getImageDhashDistance":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "GetImageDhashDistance requires 3 args")
			flag.Usage()
		}
		arg192 := flag.Arg(1)
		mbTrans193 := thrift.NewTMemoryBufferLen(len(arg192))
		defer mbTrans193.Close()
		_, err194 := mbTrans193.WriteString(arg192)
		if err194 != nil {
			Usage()
			return
		}
		factory195 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt196 := factory195.GetProtocol(mbTrans193)
		argvalue0 := picture_server.NewRequestHeader()
		err197 := argvalue0.Read(jsProt196)
		if err197 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.GetImageDhashDistance(value0, value1, value2))
		fmt.Print("\n")
		break
	case "categorizeSimilarImage":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "CategorizeSimilarImage requires 2 args")
			flag.Usage()
		}
		arg200 := flag.Arg(1)
		mbTrans201 := thrift.NewTMemoryBufferLen(len(arg200))
		defer mbTrans201.Close()
		_, err202 := mbTrans201.WriteString(arg200)
		if err202 != nil {
			Usage()
			return
		}
		factory203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt204 := factory203.GetProtocol(mbTrans201)
		argvalue0 := picture_server.NewRequestHeader()
		err205 := argvalue0.Read(jsProt204)
		if err205 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg206 := flag.Arg(2)
		mbTrans207 := thrift.NewTMemoryBufferLen(len(arg206))
		defer mbTrans207.Close()
		_, err208 := mbTrans207.WriteString(arg206)
		if err208 != nil {
			Usage()
			return
		}
		factory209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt210 := factory209.GetProtocol(mbTrans207)
		containerStruct1 := picture_server.NewCategorizeSimilarImageArgs()
		err211 := containerStruct1.ReadField2(jsProt210)
		if err211 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Images
		value1 := argvalue1
		fmt.Print(client.CategorizeSimilarImage(value0, value1))
		fmt.Print("\n")
		break
	case "categorizeSimilarCreativeFingerPrint":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "CategorizeSimilarCreativeFingerPrint requires 4 args")
			flag.Usage()
		}
		arg212 := flag.Arg(1)
		mbTrans213 := thrift.NewTMemoryBufferLen(len(arg212))
		defer mbTrans213.Close()
		_, err214 := mbTrans213.WriteString(arg212)
		if err214 != nil {
			Usage()
			return
		}
		factory215 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt216 := factory215.GetProtocol(mbTrans213)
		argvalue0 := picture_server.NewRequestHeader()
		err217 := argvalue0.Read(jsProt216)
		if err217 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg218 := flag.Arg(2)
		mbTrans219 := thrift.NewTMemoryBufferLen(len(arg218))
		defer mbTrans219.Close()
		_, err220 := mbTrans219.WriteString(arg218)
		if err220 != nil {
			Usage()
			return
		}
		factory221 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt222 := factory221.GetProtocol(mbTrans219)
		containerStruct1 := picture_server.NewCategorizeSimilarCreativeFingerPrintArgs()
		err223 := containerStruct1.ReadField2(jsProt222)
		if err223 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CreativeList
		value1 := argvalue1
		tmp2, err := (strconv.Atoi(flag.Arg(3)))
		if err != nil {
			Usage()
			return
		}
		argvalue2 := picture_server.PCCategorizeSimilarCreativeType(tmp2)
		value2 := argvalue2
		arg224 := flag.Arg(4)
		mbTrans225 := thrift.NewTMemoryBufferLen(len(arg224))
		defer mbTrans225.Close()
		_, err226 := mbTrans225.WriteString(arg224)
		if err226 != nil {
			Usage()
			return
		}
		factory227 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt228 := factory227.GetProtocol(mbTrans225)
		containerStruct3 := picture_server.NewCategorizeSimilarCreativeFingerPrintArgs()
		err229 := containerStruct3.ReadField4(jsProt228)
		if err229 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.AdditionalRequest
		value3 := argvalue3
		fmt.Print(client.CategorizeSimilarCreativeFingerPrint(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err234 := (strconv.Atoi(flag.Arg(1)))
		if err234 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
