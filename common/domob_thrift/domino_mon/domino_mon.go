// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package domino_mon

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/domino_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = domino_types.GoUnusedProtection__

type DominoMon interface { //任务监控相关
	//

	// 获取报警接收人联系信息
	//
	//
	// Parameters:
	//  - Header
	//  - Name
	GetContact(header *common.RequestHeader, name string) (r *domino_types.Contact, de *domino_types.DominoException, err error)
	// 获取报警接收群组信息
	//
	//
	// Parameters:
	//  - Header
	//  - Name
	GetContactGroup(header *common.RequestHeader, name string) (r *domino_types.ContactGroup, de *domino_types.DominoException, err error)
	// 获取联系人群组名列表
	//
	//
	// Parameters:
	//  - Header
	GetContactGroupNameList(header *common.RequestHeader) (r []string, de *domino_types.DominoException, err error)
	// 获取失败或超时的Job列表
	// 返回结构为dict：
	// {'fail':[], 'cmd':[], 'sched':[]}
	// 分别代表失败的任务、执行超时的任务、调度超时的任务
	//
	//
	// Parameters:
	//  - Header
	//  - Seclimit: 时间限制，只取seclimit秒之前的Job
	GetWarningJobList(header *common.RequestHeader, seclimit int32) (r map[string][]*domino_types.Job, de *domino_types.DominoException, err error)
	// 获取丢失的Job列表
	// 返回结构为dict, key为planId, value为丢失的Job的list
	//
	//
	// Parameters:
	//  - Header
	//  - Seclimit: 时间限制，只取seclimit秒之前的Job
	GetMissingJobList(header *common.RequestHeader, seclimit int32) (r map[IdInt][]*domino_types.Job, de *domino_types.DominoException, err error)
	// 依据提供的status
	// 返回该status的job的list
	//
	// Parameters:
	//  - Header
	//  - Status
	GetJobListByStatus(header *common.RequestHeader, status int32) (r []*domino_types.Job, de *domino_types.DominoException, err error)
}

//任务监控相关
//
type DominoMonClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewDominoMonClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DominoMonClient {
	return &DominoMonClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewDominoMonClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DominoMonClient {
	return &DominoMonClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 获取报警接收人联系信息
//
//
// Parameters:
//  - Header
//  - Name
func (p *DominoMonClient) GetContact(header *common.RequestHeader, name string) (r *domino_types.Contact, de *domino_types.DominoException, err error) {
	if err = p.sendGetContact(header, name); err != nil {
		return
	}
	return p.recvGetContact()
}

func (p *DominoMonClient) sendGetContact(header *common.RequestHeader, name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getContact", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetContactArgs()
	args0.Header = header
	args0.Name = name
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoMonClient) recvGetContact() (value *domino_types.Contact, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetContactResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.De != nil {
		de = result1.De
	}
	return
}

// 获取报警接收群组信息
//
//
// Parameters:
//  - Header
//  - Name
func (p *DominoMonClient) GetContactGroup(header *common.RequestHeader, name string) (r *domino_types.ContactGroup, de *domino_types.DominoException, err error) {
	if err = p.sendGetContactGroup(header, name); err != nil {
		return
	}
	return p.recvGetContactGroup()
}

func (p *DominoMonClient) sendGetContactGroup(header *common.RequestHeader, name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getContactGroup", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetContactGroupArgs()
	args4.Header = header
	args4.Name = name
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoMonClient) recvGetContactGroup() (value *domino_types.ContactGroup, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetContactGroupResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.De != nil {
		de = result5.De
	}
	return
}

// 获取联系人群组名列表
//
//
// Parameters:
//  - Header
func (p *DominoMonClient) GetContactGroupNameList(header *common.RequestHeader) (r []string, de *domino_types.DominoException, err error) {
	if err = p.sendGetContactGroupNameList(header); err != nil {
		return
	}
	return p.recvGetContactGroupNameList()
}

func (p *DominoMonClient) sendGetContactGroupNameList(header *common.RequestHeader) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getContactGroupNameList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewGetContactGroupNameListArgs()
	args8.Header = header
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoMonClient) recvGetContactGroupNameList() (value []string, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewGetContactGroupNameListResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.De != nil {
		de = result9.De
	}
	return
}

// 获取失败或超时的Job列表
// 返回结构为dict：
// {'fail':[], 'cmd':[], 'sched':[]}
// 分别代表失败的任务、执行超时的任务、调度超时的任务
//
//
// Parameters:
//  - Header
//  - Seclimit: 时间限制，只取seclimit秒之前的Job
func (p *DominoMonClient) GetWarningJobList(header *common.RequestHeader, seclimit int32) (r map[string][]*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetWarningJobList(header, seclimit); err != nil {
		return
	}
	return p.recvGetWarningJobList()
}

func (p *DominoMonClient) sendGetWarningJobList(header *common.RequestHeader, seclimit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getWarningJobList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args12 := NewGetWarningJobListArgs()
	args12.Header = header
	args12.Seclimit = seclimit
	if err = args12.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoMonClient) recvGetWarningJobList() (value map[string][]*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error14 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error15 error
		error15, err = error14.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error15
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result13 := NewGetWarningJobListResult()
	if err = result13.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result13.Success
	if result13.De != nil {
		de = result13.De
	}
	return
}

// 获取丢失的Job列表
// 返回结构为dict, key为planId, value为丢失的Job的list
//
//
// Parameters:
//  - Header
//  - Seclimit: 时间限制，只取seclimit秒之前的Job
func (p *DominoMonClient) GetMissingJobList(header *common.RequestHeader, seclimit int32) (r map[IdInt][]*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetMissingJobList(header, seclimit); err != nil {
		return
	}
	return p.recvGetMissingJobList()
}

func (p *DominoMonClient) sendGetMissingJobList(header *common.RequestHeader, seclimit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getMissingJobList", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args16 := NewGetMissingJobListArgs()
	args16.Header = header
	args16.Seclimit = seclimit
	if err = args16.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoMonClient) recvGetMissingJobList() (value map[IdInt][]*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error18 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error19 error
		error19, err = error18.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error19
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result17 := NewGetMissingJobListResult()
	if err = result17.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result17.Success
	if result17.De != nil {
		de = result17.De
	}
	return
}

// 依据提供的status
// 返回该status的job的list
//
// Parameters:
//  - Header
//  - Status
func (p *DominoMonClient) GetJobListByStatus(header *common.RequestHeader, status int32) (r []*domino_types.Job, de *domino_types.DominoException, err error) {
	if err = p.sendGetJobListByStatus(header, status); err != nil {
		return
	}
	return p.recvGetJobListByStatus()
}

func (p *DominoMonClient) sendGetJobListByStatus(header *common.RequestHeader, status int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getJobListByStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args20 := NewGetJobListByStatusArgs()
	args20.Header = header
	args20.Status = status
	if err = args20.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *DominoMonClient) recvGetJobListByStatus() (value []*domino_types.Job, de *domino_types.DominoException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error22 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error23 error
		error23, err = error22.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error23
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result21 := NewGetJobListByStatusResult()
	if err = result21.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result21.Success
	if result21.De != nil {
		de = result21.De
	}
	return
}

type DominoMonProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      DominoMon
}

func (p *DominoMonProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *DominoMonProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *DominoMonProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewDominoMonProcessor(handler DominoMon) *DominoMonProcessor {

	self24 := &DominoMonProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self24.processorMap["getContact"] = &dominoMonProcessorGetContact{handler: handler}
	self24.processorMap["getContactGroup"] = &dominoMonProcessorGetContactGroup{handler: handler}
	self24.processorMap["getContactGroupNameList"] = &dominoMonProcessorGetContactGroupNameList{handler: handler}
	self24.processorMap["getWarningJobList"] = &dominoMonProcessorGetWarningJobList{handler: handler}
	self24.processorMap["getMissingJobList"] = &dominoMonProcessorGetMissingJobList{handler: handler}
	self24.processorMap["getJobListByStatus"] = &dominoMonProcessorGetJobListByStatus{handler: handler}
	return self24
}

func (p *DominoMonProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x25 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x25.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x25

}

type dominoMonProcessorGetContact struct {
	handler DominoMon
}

func (p *dominoMonProcessorGetContact) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetContactArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getContact", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetContactResult()
	if result.Success, result.De, err = p.handler.GetContact(args.Header, args.Name); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getContact: "+err.Error())
		oprot.WriteMessageBegin("getContact", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getContact", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoMonProcessorGetContactGroup struct {
	handler DominoMon
}

func (p *dominoMonProcessorGetContactGroup) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetContactGroupArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getContactGroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetContactGroupResult()
	if result.Success, result.De, err = p.handler.GetContactGroup(args.Header, args.Name); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getContactGroup: "+err.Error())
		oprot.WriteMessageBegin("getContactGroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getContactGroup", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoMonProcessorGetContactGroupNameList struct {
	handler DominoMon
}

func (p *dominoMonProcessorGetContactGroupNameList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetContactGroupNameListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getContactGroupNameList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetContactGroupNameListResult()
	if result.Success, result.De, err = p.handler.GetContactGroupNameList(args.Header); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getContactGroupNameList: "+err.Error())
		oprot.WriteMessageBegin("getContactGroupNameList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getContactGroupNameList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoMonProcessorGetWarningJobList struct {
	handler DominoMon
}

func (p *dominoMonProcessorGetWarningJobList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetWarningJobListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getWarningJobList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetWarningJobListResult()
	if result.Success, result.De, err = p.handler.GetWarningJobList(args.Header, args.Seclimit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getWarningJobList: "+err.Error())
		oprot.WriteMessageBegin("getWarningJobList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getWarningJobList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoMonProcessorGetMissingJobList struct {
	handler DominoMon
}

func (p *dominoMonProcessorGetMissingJobList) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMissingJobListArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getMissingJobList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMissingJobListResult()
	if result.Success, result.De, err = p.handler.GetMissingJobList(args.Header, args.Seclimit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getMissingJobList: "+err.Error())
		oprot.WriteMessageBegin("getMissingJobList", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getMissingJobList", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type dominoMonProcessorGetJobListByStatus struct {
	handler DominoMon
}

func (p *dominoMonProcessorGetJobListByStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetJobListByStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getJobListByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetJobListByStatusResult()
	if result.Success, result.De, err = p.handler.GetJobListByStatus(args.Header, args.Status); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getJobListByStatus: "+err.Error())
		oprot.WriteMessageBegin("getJobListByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getJobListByStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetContactArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Name   string                `thrift:"name,2" json:"name"`
}

func NewGetContactArgs() *GetContactArgs {
	return &GetContactArgs{}
}

func (p *GetContactArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetContactArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetContactArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *GetContactArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getContact_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetContactArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetContactArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *GetContactArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContactArgs(%+v)", *p)
}

type GetContactResult struct {
	Success *domino_types.Contact         `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetContactResult() *GetContactResult {
	return &GetContactResult{}
}

func (p *GetContactResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetContactResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewContact()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetContactResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetContactResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getContact_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetContactResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetContactResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetContactResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContactResult(%+v)", *p)
}

type GetContactGroupArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Name   string                `thrift:"name,2" json:"name"`
}

func NewGetContactGroupArgs() *GetContactGroupArgs {
	return &GetContactGroupArgs{}
}

func (p *GetContactGroupArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetContactGroupArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *GetContactGroupArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getContactGroup_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetContactGroupArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *GetContactGroupArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContactGroupArgs(%+v)", *p)
}

type GetContactGroupResult struct {
	Success *domino_types.ContactGroup    `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetContactGroupResult() *GetContactGroupResult {
	return &GetContactGroupResult{}
}

func (p *GetContactGroupResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupResult) readField0(iprot thrift.TProtocol) error {
	p.Success = domino_types.NewContactGroup()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetContactGroupResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetContactGroupResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getContactGroup_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetContactGroupResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetContactGroupResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContactGroupResult(%+v)", *p)
}

type GetContactGroupNameListArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
}

func NewGetContactGroupNameListArgs() *GetContactGroupNameListArgs {
	return &GetContactGroupNameListArgs{}
}

func (p *GetContactGroupNameListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupNameListArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetContactGroupNameListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getContactGroupNameList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupNameListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetContactGroupNameListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContactGroupNameListArgs(%+v)", *p)
}

type GetContactGroupNameListResult struct {
	Success []string                      `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetContactGroupNameListResult() *GetContactGroupNameListResult {
	return &GetContactGroupNameListResult{}
}

func (p *GetContactGroupNameListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupNameListResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem26 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem26 = v
		}
		p.Success = append(p.Success, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetContactGroupNameListResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetContactGroupNameListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getContactGroupNameList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetContactGroupNameListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetContactGroupNameListResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetContactGroupNameListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetContactGroupNameListResult(%+v)", *p)
}

type GetWarningJobListArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Seclimit int32                 `thrift:"seclimit,2" json:"seclimit"`
}

func NewGetWarningJobListArgs() *GetWarningJobListArgs {
	return &GetWarningJobListArgs{}
}

func (p *GetWarningJobListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetWarningJobListArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetWarningJobListArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Seclimit = v
	}
	return nil
}

func (p *GetWarningJobListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getWarningJobList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetWarningJobListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetWarningJobListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seclimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:seclimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Seclimit)); err != nil {
		return fmt.Errorf("%T.seclimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:seclimit: %s", p, err)
	}
	return err
}

func (p *GetWarningJobListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetWarningJobListArgs(%+v)", *p)
}

type GetWarningJobListResult struct {
	Success map[string][]*domino_types.Job `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException  `thrift:"de,1" json:"de"`
}

func NewGetWarningJobListResult() *GetWarningJobListResult {
	return &GetWarningJobListResult{}
}

func (p *GetWarningJobListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetWarningJobListResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[string][]*domino_types.Job, size)
	for i := 0; i < size; i++ {
		var _key27 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key27 = v
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val28 := make([]*domino_types.Job, 0, size)
		for i := 0; i < size; i++ {
			_elem29 := domino_types.NewJob()
			if err := _elem29.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem29)
			}
			_val28 = append(_val28, _elem29)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key27] = _val28
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetWarningJobListResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetWarningJobListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getWarningJobList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetWarningJobListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetWarningJobListResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetWarningJobListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetWarningJobListResult(%+v)", *p)
}

type GetMissingJobListArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	Seclimit int32                 `thrift:"seclimit,2" json:"seclimit"`
}

func NewGetMissingJobListArgs() *GetMissingJobListArgs {
	return &GetMissingJobListArgs{}
}

func (p *GetMissingJobListArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMissingJobListArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetMissingJobListArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Seclimit = v
	}
	return nil
}

func (p *GetMissingJobListArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMissingJobList_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMissingJobListArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetMissingJobListArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("seclimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:seclimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Seclimit)); err != nil {
		return fmt.Errorf("%T.seclimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:seclimit: %s", p, err)
	}
	return err
}

func (p *GetMissingJobListArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMissingJobListArgs(%+v)", *p)
}

type GetMissingJobListResult struct {
	Success map[IdInt][]*domino_types.Job `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetMissingJobListResult() *GetMissingJobListResult {
	return &GetMissingJobListResult{}
}

func (p *GetMissingJobListResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMissingJobListResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[IdInt][]*domino_types.Job, size)
	for i := 0; i < size; i++ {
		var _key30 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key30 = IdInt(v)
		}
		_, size, err := iprot.ReadListBegin()
		if err != nil {
			return fmt.Errorf("error reading list being: %s", err)
		}
		_val31 := make([]*domino_types.Job, 0, size)
		for i := 0; i < size; i++ {
			_elem32 := domino_types.NewJob()
			if err := _elem32.Read(iprot); err != nil {
				return fmt.Errorf("%T error reading struct: %s", _elem32)
			}
			_val31 = append(_val31, _elem32)
		}
		if err := iprot.ReadListEnd(); err != nil {
			return fmt.Errorf("error reading list end: %s", err)
		}
		p.Success[_key30] = _val31
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetMissingJobListResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetMissingJobListResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getMissingJobList_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMissingJobListResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.LIST, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteListBegin(thrift.STRUCT, len(v)); err != nil {
				return fmt.Errorf("error writing list begin: %s")
			}
			for _, v := range v {
				if err := v.Write(oprot); err != nil {
					return fmt.Errorf("%T error writing struct: %s", v)
				}
			}
			if err := oprot.WriteListEnd(); err != nil {
				return fmt.Errorf("error writing list end: %s")
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMissingJobListResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetMissingJobListResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMissingJobListResult(%+v)", *p)
}

type GetJobListByStatusArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Status int32                 `thrift:"status,2" json:"status"`
}

func NewGetJobListByStatusArgs() *GetJobListByStatusArgs {
	return &GetJobListByStatusArgs{}
}

func (p *GetJobListByStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetJobListByStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *GetJobListByStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobListByStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:status: %s", p, err)
	}
	return err
}

func (p *GetJobListByStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobListByStatusArgs(%+v)", *p)
}

type GetJobListByStatusResult struct {
	Success []*domino_types.Job           `thrift:"success,0" json:"success"`
	De      *domino_types.DominoException `thrift:"de,1" json:"de"`
}

func NewGetJobListByStatusResult() *GetJobListByStatusResult {
	return &GetJobListByStatusResult{}
}

func (p *GetJobListByStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*domino_types.Job, 0, size)
	for i := 0; i < size; i++ {
		_elem33 := domino_types.NewJob()
		if err := _elem33.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem33)
		}
		p.Success = append(p.Success, _elem33)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetJobListByStatusResult) readField1(iprot thrift.TProtocol) error {
	p.De = domino_types.NewDominoException()
	if err := p.De.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.De)
	}
	return nil
}

func (p *GetJobListByStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getJobListByStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.De != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetJobListByStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.De != nil {
		if err := oprot.WriteFieldBegin("de", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:de: %s", p, err)
		}
		if err := p.De.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.De)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:de: %s", p, err)
		}
	}
	return err
}

func (p *GetJobListByStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetJobListByStatusResult(%+v)", *p)
}
