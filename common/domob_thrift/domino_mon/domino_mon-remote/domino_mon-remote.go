// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"domino_mon"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  Contact getContact(RequestHeader header, string name)")
	fmt.Fprintln(os.Stderr, "  ContactGroup getContactGroup(RequestHeader header, string name)")
	fmt.Fprintln(os.<PERSON>, "   getContactGroupNameList(RequestHeader header)")
	fmt.Fprintln(os.St<PERSON>r, "   getWarningJobList(RequestHeader header, i32 seclimit)")
	fmt.Fprintln(os.Stderr, "   getMissingJobList(RequestHeader header, i32 seclimit)")
	fmt.Fprintln(os.Stderr, "   getJobListByStatus(RequestHeader header, i32 status)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := domino_mon.NewDominoMonClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "getContact":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetContact requires 2 args")
			flag.Usage()
		}
		arg34 := flag.Arg(1)
		mbTrans35 := thrift.NewTMemoryBufferLen(len(arg34))
		defer mbTrans35.Close()
		_, err36 := mbTrans35.WriteString(arg34)
		if err36 != nil {
			Usage()
			return
		}
		factory37 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt38 := factory37.GetProtocol(mbTrans35)
		argvalue0 := domino_mon.NewRequestHeader()
		err39 := argvalue0.Read(jsProt38)
		if err39 != nil {
			Usage()
			return
		}
		value0 := domino_mon.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetContact(value0, value1))
		fmt.Print("\n")
		break
	case "getContactGroup":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetContactGroup requires 2 args")
			flag.Usage()
		}
		arg41 := flag.Arg(1)
		mbTrans42 := thrift.NewTMemoryBufferLen(len(arg41))
		defer mbTrans42.Close()
		_, err43 := mbTrans42.WriteString(arg41)
		if err43 != nil {
			Usage()
			return
		}
		factory44 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt45 := factory44.GetProtocol(mbTrans42)
		argvalue0 := domino_mon.NewRequestHeader()
		err46 := argvalue0.Read(jsProt45)
		if err46 != nil {
			Usage()
			return
		}
		value0 := domino_mon.RequestHeader(argvalue0)
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.GetContactGroup(value0, value1))
		fmt.Print("\n")
		break
	case "getContactGroupNameList":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetContactGroupNameList requires 1 args")
			flag.Usage()
		}
		arg48 := flag.Arg(1)
		mbTrans49 := thrift.NewTMemoryBufferLen(len(arg48))
		defer mbTrans49.Close()
		_, err50 := mbTrans49.WriteString(arg48)
		if err50 != nil {
			Usage()
			return
		}
		factory51 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt52 := factory51.GetProtocol(mbTrans49)
		argvalue0 := domino_mon.NewRequestHeader()
		err53 := argvalue0.Read(jsProt52)
		if err53 != nil {
			Usage()
			return
		}
		value0 := domino_mon.RequestHeader(argvalue0)
		fmt.Print(client.GetContactGroupNameList(value0))
		fmt.Print("\n")
		break
	case "getWarningJobList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetWarningJobList requires 2 args")
			flag.Usage()
		}
		arg54 := flag.Arg(1)
		mbTrans55 := thrift.NewTMemoryBufferLen(len(arg54))
		defer mbTrans55.Close()
		_, err56 := mbTrans55.WriteString(arg54)
		if err56 != nil {
			Usage()
			return
		}
		factory57 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt58 := factory57.GetProtocol(mbTrans55)
		argvalue0 := domino_mon.NewRequestHeader()
		err59 := argvalue0.Read(jsProt58)
		if err59 != nil {
			Usage()
			return
		}
		value0 := domino_mon.RequestHeader(argvalue0)
		tmp1, err60 := (strconv.Atoi(flag.Arg(2)))
		if err60 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetWarningJobList(value0, value1))
		fmt.Print("\n")
		break
	case "getMissingJobList":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMissingJobList requires 2 args")
			flag.Usage()
		}
		arg61 := flag.Arg(1)
		mbTrans62 := thrift.NewTMemoryBufferLen(len(arg61))
		defer mbTrans62.Close()
		_, err63 := mbTrans62.WriteString(arg61)
		if err63 != nil {
			Usage()
			return
		}
		factory64 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt65 := factory64.GetProtocol(mbTrans62)
		argvalue0 := domino_mon.NewRequestHeader()
		err66 := argvalue0.Read(jsProt65)
		if err66 != nil {
			Usage()
			return
		}
		value0 := domino_mon.RequestHeader(argvalue0)
		tmp1, err67 := (strconv.Atoi(flag.Arg(2)))
		if err67 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetMissingJobList(value0, value1))
		fmt.Print("\n")
		break
	case "getJobListByStatus":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetJobListByStatus requires 2 args")
			flag.Usage()
		}
		arg68 := flag.Arg(1)
		mbTrans69 := thrift.NewTMemoryBufferLen(len(arg68))
		defer mbTrans69.Close()
		_, err70 := mbTrans69.WriteString(arg68)
		if err70 != nil {
			Usage()
			return
		}
		factory71 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt72 := factory71.GetProtocol(mbTrans69)
		argvalue0 := domino_mon.NewRequestHeader()
		err73 := argvalue0.Read(jsProt72)
		if err73 != nil {
			Usage()
			return
		}
		value0 := domino_mon.RequestHeader(argvalue0)
		tmp1, err74 := (strconv.Atoi(flag.Arg(2)))
		if err74 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetJobListByStatus(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
