// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package domino_mon

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/domino_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = domino_types.GoUnusedProtection__
var GoUnusedProtection__ int

type RequestHeader *common.RequestHeader

type IdInt common.IdInt

type Job *domino_types.Job

type Contact *domino_types.Contact

type ContactGroup *domino_types.ContactGroup
