// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"fea_manager"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.<PERSON>derr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  DataSourceVersion check_data_source_version(i64 version)")
	fmt.Fprintln(os.Stderr, "   get_all_data_source()")
	fmt.Fprintln(os.<PERSON>derr, "  DataSource get_data_source_by_name(string name)")
	fmt.Fprintln(os.<PERSON>, "  DataSource get_data_source_by_id(i64 data_source_id)")
	fmt.Fprintln(os.Stderr, "  i64 add_data_source(DataSource data_source)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_data_source(DataSource data_source)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode del_data_source(string data_name)")
	fmt.Fprintln(os.Stderr, "  CrowdProfile get_crowd_profile(i64 crowd_id)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode add_crowd_profile(CrowdProfile crowd_profile)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_crowd_profile(CrowdProfile crowd_profile)")
	fmt.Fprintln(os.Stderr, "   get_all_crowd()")
	fmt.Fprintln(os.Stderr, "  Crowd get_crowd(i64 crowd_id)")
	fmt.Fprintln(os.Stderr, "  CrowdProfile add_crowd(Crowd crowd)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_crowd(Crowd crowd)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode del_crowd(i64 crowd_id)")
	fmt.Fprintln(os.Stderr, "  i64 add_crowd_task(CrowdTask crowd_task)")
	fmt.Fprintln(os.Stderr, "   add_crowd_task_group( crowd_task_group)")
	fmt.Fprintln(os.Stderr, "  CrowdTask get_committed_crowd_task(string data_name)")
	fmt.Fprintln(os.Stderr, "   get_multi_committed_crowd_task(string data_name, i32 limit)")
	fmt.Fprintln(os.Stderr, "  CrowdTask get_crowd_task(i64 task_id)")
	fmt.Fprintln(os.Stderr, "   get_all_crowd_task()")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_crowd_task(CrowdTask crowd_task)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_task_status(CrowdTaskProfile task_profile)")
	fmt.Fprintln(os.Stderr, "  CrowdProfile get_groupcrowd_profile(i64 crowd_id)")
	fmt.Fprintln(os.Stderr, "  Crowd get_committed_crowd()")
	fmt.Fprintln(os.Stderr, "   get_multi_committed_crowd(i32 limit)")
	fmt.Fprintln(os.Stderr, "  Crowd get_committed_loadedcrowd()")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_crowd_status(CrowdProfile crowd_profile)")
	fmt.Fprintln(os.Stderr, "  CrowdProfile compose_crowd(CrowdGroup crowd_group)")
	fmt.Fprintln(os.Stderr, "  CrowdGroup get_compose_crowd(i64 compose_crowd_id)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_compose_crowd(CrowdGroup crowd_group)")
	fmt.Fprintln(os.Stderr, "  CrowdGroup get_committed_crowdgroup()")
	fmt.Fprintln(os.Stderr, "   get_multi_committed_crowdgroup(i32 limit)")
	fmt.Fprintln(os.Stderr, "  FeaManagerErrorCode update_crowdgroup_status(CrowdProfile crowdgroup_profile)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := fea_manager.NewFeaManagerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "check_data_source_version":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "CheckDataSourceVersion requires 1 args")
			flag.Usage()
		}
		argvalue0, err162 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err162 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.CheckDataSourceVersion(value0))
		fmt.Print("\n")
		break
	case "get_all_data_source":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetAllDataSource requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetAllDataSource())
		fmt.Print("\n")
		break
	case "get_data_source_by_name":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetDataSourceByName requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetDataSourceByName(value0))
		fmt.Print("\n")
		break
	case "get_data_source_by_id":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetDataSourceById requires 1 args")
			flag.Usage()
		}
		argvalue0, err164 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err164 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetDataSourceById(value0))
		fmt.Print("\n")
		break
	case "add_data_source":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddDataSource requires 1 args")
			flag.Usage()
		}
		arg165 := flag.Arg(1)
		mbTrans166 := thrift.NewTMemoryBufferLen(len(arg165))
		defer mbTrans166.Close()
		_, err167 := mbTrans166.WriteString(arg165)
		if err167 != nil {
			Usage()
			return
		}
		factory168 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt169 := factory168.GetProtocol(mbTrans166)
		argvalue0 := fea_manager.NewDataSource()
		err170 := argvalue0.Read(jsProt169)
		if err170 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddDataSource(value0))
		fmt.Print("\n")
		break
	case "update_data_source":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateDataSource requires 1 args")
			flag.Usage()
		}
		arg171 := flag.Arg(1)
		mbTrans172 := thrift.NewTMemoryBufferLen(len(arg171))
		defer mbTrans172.Close()
		_, err173 := mbTrans172.WriteString(arg171)
		if err173 != nil {
			Usage()
			return
		}
		factory174 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt175 := factory174.GetProtocol(mbTrans172)
		argvalue0 := fea_manager.NewDataSource()
		err176 := argvalue0.Read(jsProt175)
		if err176 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateDataSource(value0))
		fmt.Print("\n")
		break
	case "del_data_source":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DelDataSource requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.DelDataSource(value0))
		fmt.Print("\n")
		break
	case "get_crowd_profile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCrowdProfile requires 1 args")
			flag.Usage()
		}
		argvalue0, err178 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err178 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetCrowdProfile(value0))
		fmt.Print("\n")
		break
	case "add_crowd_profile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddCrowdProfile requires 1 args")
			flag.Usage()
		}
		arg179 := flag.Arg(1)
		mbTrans180 := thrift.NewTMemoryBufferLen(len(arg179))
		defer mbTrans180.Close()
		_, err181 := mbTrans180.WriteString(arg179)
		if err181 != nil {
			Usage()
			return
		}
		factory182 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt183 := factory182.GetProtocol(mbTrans180)
		argvalue0 := fea_manager.NewCrowdProfile()
		err184 := argvalue0.Read(jsProt183)
		if err184 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddCrowdProfile(value0))
		fmt.Print("\n")
		break
	case "update_crowd_profile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateCrowdProfile requires 1 args")
			flag.Usage()
		}
		arg185 := flag.Arg(1)
		mbTrans186 := thrift.NewTMemoryBufferLen(len(arg185))
		defer mbTrans186.Close()
		_, err187 := mbTrans186.WriteString(arg185)
		if err187 != nil {
			Usage()
			return
		}
		factory188 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt189 := factory188.GetProtocol(mbTrans186)
		argvalue0 := fea_manager.NewCrowdProfile()
		err190 := argvalue0.Read(jsProt189)
		if err190 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateCrowdProfile(value0))
		fmt.Print("\n")
		break
	case "get_all_crowd":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetAllCrowd requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetAllCrowd())
		fmt.Print("\n")
		break
	case "get_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCrowd requires 1 args")
			flag.Usage()
		}
		argvalue0, err191 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err191 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetCrowd(value0))
		fmt.Print("\n")
		break
	case "add_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddCrowd requires 1 args")
			flag.Usage()
		}
		arg192 := flag.Arg(1)
		mbTrans193 := thrift.NewTMemoryBufferLen(len(arg192))
		defer mbTrans193.Close()
		_, err194 := mbTrans193.WriteString(arg192)
		if err194 != nil {
			Usage()
			return
		}
		factory195 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt196 := factory195.GetProtocol(mbTrans193)
		argvalue0 := fea_manager.NewCrowd()
		err197 := argvalue0.Read(jsProt196)
		if err197 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddCrowd(value0))
		fmt.Print("\n")
		break
	case "update_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateCrowd requires 1 args")
			flag.Usage()
		}
		arg198 := flag.Arg(1)
		mbTrans199 := thrift.NewTMemoryBufferLen(len(arg198))
		defer mbTrans199.Close()
		_, err200 := mbTrans199.WriteString(arg198)
		if err200 != nil {
			Usage()
			return
		}
		factory201 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt202 := factory201.GetProtocol(mbTrans199)
		argvalue0 := fea_manager.NewCrowd()
		err203 := argvalue0.Read(jsProt202)
		if err203 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateCrowd(value0))
		fmt.Print("\n")
		break
	case "del_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "DelCrowd requires 1 args")
			flag.Usage()
		}
		argvalue0, err204 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err204 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.DelCrowd(value0))
		fmt.Print("\n")
		break
	case "add_crowd_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddCrowdTask requires 1 args")
			flag.Usage()
		}
		arg205 := flag.Arg(1)
		mbTrans206 := thrift.NewTMemoryBufferLen(len(arg205))
		defer mbTrans206.Close()
		_, err207 := mbTrans206.WriteString(arg205)
		if err207 != nil {
			Usage()
			return
		}
		factory208 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt209 := factory208.GetProtocol(mbTrans206)
		argvalue0 := fea_manager.NewCrowdTask()
		err210 := argvalue0.Read(jsProt209)
		if err210 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.AddCrowdTask(value0))
		fmt.Print("\n")
		break
	case "add_crowd_task_group":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "AddCrowdTaskGroup requires 1 args")
			flag.Usage()
		}
		arg211 := flag.Arg(1)
		mbTrans212 := thrift.NewTMemoryBufferLen(len(arg211))
		defer mbTrans212.Close()
		_, err213 := mbTrans212.WriteString(arg211)
		if err213 != nil {
			Usage()
			return
		}
		factory214 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt215 := factory214.GetProtocol(mbTrans212)
		containerStruct0 := fea_manager.NewAddCrowdTaskGroupArgs()
		err216 := containerStruct0.ReadField1(jsProt215)
		if err216 != nil {
			Usage()
			return
		}
		argvalue0 := containerStruct0.CrowdTaskGroup
		value0 := argvalue0
		fmt.Print(client.AddCrowdTaskGroup(value0))
		fmt.Print("\n")
		break
	case "get_committed_crowd_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCommittedCrowdTask requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCommittedCrowdTask(value0))
		fmt.Print("\n")
		break
	case "get_multi_committed_crowd_task":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetMultiCommittedCrowdTask requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		tmp1, err219 := (strconv.Atoi(flag.Arg(2)))
		if err219 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.GetMultiCommittedCrowdTask(value0, value1))
		fmt.Print("\n")
		break
	case "get_crowd_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCrowdTask requires 1 args")
			flag.Usage()
		}
		argvalue0, err220 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err220 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetCrowdTask(value0))
		fmt.Print("\n")
		break
	case "get_all_crowd_task":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetAllCrowdTask requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetAllCrowdTask())
		fmt.Print("\n")
		break
	case "update_crowd_task":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateCrowdTask requires 1 args")
			flag.Usage()
		}
		arg221 := flag.Arg(1)
		mbTrans222 := thrift.NewTMemoryBufferLen(len(arg221))
		defer mbTrans222.Close()
		_, err223 := mbTrans222.WriteString(arg221)
		if err223 != nil {
			Usage()
			return
		}
		factory224 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt225 := factory224.GetProtocol(mbTrans222)
		argvalue0 := fea_manager.NewCrowdTask()
		err226 := argvalue0.Read(jsProt225)
		if err226 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateCrowdTask(value0))
		fmt.Print("\n")
		break
	case "update_task_status":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateTaskStatus requires 1 args")
			flag.Usage()
		}
		arg227 := flag.Arg(1)
		mbTrans228 := thrift.NewTMemoryBufferLen(len(arg227))
		defer mbTrans228.Close()
		_, err229 := mbTrans228.WriteString(arg227)
		if err229 != nil {
			Usage()
			return
		}
		factory230 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt231 := factory230.GetProtocol(mbTrans228)
		argvalue0 := fea_manager.NewCrowdTaskProfile()
		err232 := argvalue0.Read(jsProt231)
		if err232 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateTaskStatus(value0))
		fmt.Print("\n")
		break
	case "get_groupcrowd_profile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetGroupcrowdProfile requires 1 args")
			flag.Usage()
		}
		argvalue0, err233 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err233 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetGroupcrowdProfile(value0))
		fmt.Print("\n")
		break
	case "get_committed_crowd":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCommittedCrowd requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCommittedCrowd())
		fmt.Print("\n")
		break
	case "get_multi_committed_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetMultiCommittedCrowd requires 1 args")
			flag.Usage()
		}
		tmp0, err234 := (strconv.Atoi(flag.Arg(1)))
		if err234 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetMultiCommittedCrowd(value0))
		fmt.Print("\n")
		break
	case "get_committed_loadedcrowd":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCommittedLoadedcrowd requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCommittedLoadedcrowd())
		fmt.Print("\n")
		break
	case "update_crowd_status":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateCrowdStatus requires 1 args")
			flag.Usage()
		}
		arg235 := flag.Arg(1)
		mbTrans236 := thrift.NewTMemoryBufferLen(len(arg235))
		defer mbTrans236.Close()
		_, err237 := mbTrans236.WriteString(arg235)
		if err237 != nil {
			Usage()
			return
		}
		factory238 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt239 := factory238.GetProtocol(mbTrans236)
		argvalue0 := fea_manager.NewCrowdProfile()
		err240 := argvalue0.Read(jsProt239)
		if err240 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateCrowdStatus(value0))
		fmt.Print("\n")
		break
	case "compose_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "ComposeCrowd requires 1 args")
			flag.Usage()
		}
		arg241 := flag.Arg(1)
		mbTrans242 := thrift.NewTMemoryBufferLen(len(arg241))
		defer mbTrans242.Close()
		_, err243 := mbTrans242.WriteString(arg241)
		if err243 != nil {
			Usage()
			return
		}
		factory244 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt245 := factory244.GetProtocol(mbTrans242)
		argvalue0 := fea_manager.NewCrowdGroup()
		err246 := argvalue0.Read(jsProt245)
		if err246 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.ComposeCrowd(value0))
		fmt.Print("\n")
		break
	case "get_compose_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetComposeCrowd requires 1 args")
			flag.Usage()
		}
		argvalue0, err247 := (strconv.ParseInt(flag.Arg(1), 10, 64))
		if err247 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetComposeCrowd(value0))
		fmt.Print("\n")
		break
	case "update_compose_crowd":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateComposeCrowd requires 1 args")
			flag.Usage()
		}
		arg248 := flag.Arg(1)
		mbTrans249 := thrift.NewTMemoryBufferLen(len(arg248))
		defer mbTrans249.Close()
		_, err250 := mbTrans249.WriteString(arg248)
		if err250 != nil {
			Usage()
			return
		}
		factory251 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt252 := factory251.GetProtocol(mbTrans249)
		argvalue0 := fea_manager.NewCrowdGroup()
		err253 := argvalue0.Read(jsProt252)
		if err253 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateComposeCrowd(value0))
		fmt.Print("\n")
		break
	case "get_committed_crowdgroup":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCommittedCrowdgroup requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCommittedCrowdgroup())
		fmt.Print("\n")
		break
	case "get_multi_committed_crowdgroup":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetMultiCommittedCrowdgroup requires 1 args")
			flag.Usage()
		}
		tmp0, err254 := (strconv.Atoi(flag.Arg(1)))
		if err254 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetMultiCommittedCrowdgroup(value0))
		fmt.Print("\n")
		break
	case "update_crowdgroup_status":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "UpdateCrowdgroupStatus requires 1 args")
			flag.Usage()
		}
		arg255 := flag.Arg(1)
		mbTrans256 := thrift.NewTMemoryBufferLen(len(arg255))
		defer mbTrans256.Close()
		_, err257 := mbTrans256.WriteString(arg255)
		if err257 != nil {
			Usage()
			return
		}
		factory258 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt259 := factory258.GetProtocol(mbTrans256)
		argvalue0 := fea_manager.NewCrowdProfile()
		err260 := argvalue0.Read(jsProt259)
		if err260 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.UpdateCrowdgroupStatus(value0))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err265 := (strconv.Atoi(flag.Arg(1)))
		if err265 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
