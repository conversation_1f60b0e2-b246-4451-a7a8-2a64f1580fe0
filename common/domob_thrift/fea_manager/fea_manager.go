// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package fea_manager

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type FeaManager interface {
	dm303.DomobService

	// Parameters:
	//  - Version
	CheckDataSourceVersion(version int64) (r *DataSourceVersion, fme *FeaManagerException, err error)
	GetAllDataSource() (r []*DataSource, fme *FeaManagerException, err error)
	// Parameters:
	//  - Name
	GetDataSourceByName(name string) (r *DataSource, fme *FeaManagerException, err error)
	// Parameters:
	//  - DataSourceId
	GetDataSourceById(data_source_id int64) (r *DataSource, fme *FeaManagerException, err error)
	// Parameters:
	//  - DataSource
	AddDataSource(data_source *DataSource) (r int64, fme *FeaManagerException, err error)
	// Parameters:
	//  - DataSource
	UpdateDataSource(data_source *DataSource) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - DataName
	DelDataSource(data_name string) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdId
	GetCrowdProfile(crowd_id int64) (r *CrowdProfile, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdProfile
	AddCrowdProfile(crowd_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdProfile
	UpdateCrowdProfile(crowd_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	GetAllCrowd() (r []*Crowd, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdId
	GetCrowd(crowd_id int64) (r *Crowd, fme *FeaManagerException, err error)
	// Parameters:
	//  - Crowd
	AddCrowd(crowd *Crowd) (r *CrowdProfile, fme *FeaManagerException, err error)
	// Parameters:
	//  - Crowd
	UpdateCrowd(crowd *Crowd) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdId
	DelCrowd(crowd_id int64) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdTask
	AddCrowdTask(crowd_task *CrowdTask) (r int64, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdTaskGroup
	AddCrowdTaskGroup(crowd_task_group []*CrowdTask) (r []int64, fme *FeaManagerException, err error)
	// Parameters:
	//  - DataName
	GetCommittedCrowdTask(data_name string) (r *CrowdTask, fme *FeaManagerException, err error)
	// Parameters:
	//  - DataName
	//  - Limit
	GetMultiCommittedCrowdTask(data_name string, limit int32) (r []*CrowdTask, fme *FeaManagerException, err error)
	// Parameters:
	//  - TaskId
	GetCrowdTask(task_id int64) (r *CrowdTask, fme *FeaManagerException, err error)
	GetAllCrowdTask() (r []*CrowdTask, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdTask
	UpdateCrowdTask(crowd_task *CrowdTask) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - TaskProfile
	UpdateTaskStatus(task_profile *CrowdTaskProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdId
	GetGroupcrowdProfile(crowd_id int64) (r *CrowdProfile, fme *FeaManagerException, err error)
	GetCommittedCrowd() (r *Crowd, fme *FeaManagerException, err error)
	// Parameters:
	//  - Limit
	GetMultiCommittedCrowd(limit int32) (r []*Crowd, fme *FeaManagerException, err error)
	GetCommittedLoadedcrowd() (r *Crowd, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdProfile
	UpdateCrowdStatus(crowd_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdGroup
	ComposeCrowd(crowd_group *CrowdGroup) (r *CrowdProfile, fme *FeaManagerException, err error)
	// Parameters:
	//  - ComposeCrowdId
	GetComposeCrowd(compose_crowd_id int64) (r *CrowdGroup, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdGroup
	UpdateComposeCrowd(crowd_group *CrowdGroup) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
	GetCommittedCrowdgroup() (r *CrowdGroup, fme *FeaManagerException, err error)
	// Parameters:
	//  - Limit
	GetMultiCommittedCrowdgroup(limit int32) (r []*CrowdGroup, fme *FeaManagerException, err error)
	// Parameters:
	//  - CrowdgroupProfile
	UpdateCrowdgroupStatus(crowdgroup_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error)
}

type FeaManagerClient struct {
	*dm303.DomobServiceClient
}

func NewFeaManagerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *FeaManagerClient {
	return &FeaManagerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewFeaManagerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *FeaManagerClient {
	return &FeaManagerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Version
func (p *FeaManagerClient) CheckDataSourceVersion(version int64) (r *DataSourceVersion, fme *FeaManagerException, err error) {
	if err = p.sendCheckDataSourceVersion(version); err != nil {
		return
	}
	return p.recvCheckDataSourceVersion()
}

func (p *FeaManagerClient) sendCheckDataSourceVersion(version int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("check_data_source_version", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args17 := NewCheckDataSourceVersionArgs()
	args17.Version = version
	if err = args17.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvCheckDataSourceVersion() (value *DataSourceVersion, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error19 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error20 error
		error20, err = error19.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error20
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result18 := NewCheckDataSourceVersionResult()
	if err = result18.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result18.Success
	if result18.Fme != nil {
		fme = result18.Fme
	}
	return
}

func (p *FeaManagerClient) GetAllDataSource() (r []*DataSource, fme *FeaManagerException, err error) {
	if err = p.sendGetAllDataSource(); err != nil {
		return
	}
	return p.recvGetAllDataSource()
}

func (p *FeaManagerClient) sendGetAllDataSource() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_all_data_source", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args21 := NewGetAllDataSourceArgs()
	if err = args21.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetAllDataSource() (value []*DataSource, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error23 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error24 error
		error24, err = error23.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error24
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result22 := NewGetAllDataSourceResult()
	if err = result22.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result22.Success
	if result22.Fme != nil {
		fme = result22.Fme
	}
	return
}

// Parameters:
//  - Name
func (p *FeaManagerClient) GetDataSourceByName(name string) (r *DataSource, fme *FeaManagerException, err error) {
	if err = p.sendGetDataSourceByName(name); err != nil {
		return
	}
	return p.recvGetDataSourceByName()
}

func (p *FeaManagerClient) sendGetDataSourceByName(name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_data_source_by_name", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args25 := NewGetDataSourceByNameArgs()
	args25.Name = name
	if err = args25.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetDataSourceByName() (value *DataSource, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error27 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error28 error
		error28, err = error27.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error28
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result26 := NewGetDataSourceByNameResult()
	if err = result26.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result26.Success
	if result26.Fme != nil {
		fme = result26.Fme
	}
	return
}

// Parameters:
//  - DataSourceId
func (p *FeaManagerClient) GetDataSourceById(data_source_id int64) (r *DataSource, fme *FeaManagerException, err error) {
	if err = p.sendGetDataSourceById(data_source_id); err != nil {
		return
	}
	return p.recvGetDataSourceById()
}

func (p *FeaManagerClient) sendGetDataSourceById(data_source_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_data_source_by_id", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args29 := NewGetDataSourceByIdArgs()
	args29.DataSourceId = data_source_id
	if err = args29.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetDataSourceById() (value *DataSource, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error31 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error32 error
		error32, err = error31.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error32
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result30 := NewGetDataSourceByIdResult()
	if err = result30.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result30.Success
	if result30.Fme != nil {
		fme = result30.Fme
	}
	return
}

// Parameters:
//  - DataSource
func (p *FeaManagerClient) AddDataSource(data_source *DataSource) (r int64, fme *FeaManagerException, err error) {
	if err = p.sendAddDataSource(data_source); err != nil {
		return
	}
	return p.recvAddDataSource()
}

func (p *FeaManagerClient) sendAddDataSource(data_source *DataSource) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_data_source", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args33 := NewAddDataSourceArgs()
	args33.DataSource = data_source
	if err = args33.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvAddDataSource() (value int64, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error35 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error36 error
		error36, err = error35.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error36
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result34 := NewAddDataSourceResult()
	if err = result34.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result34.Success
	if result34.Fme != nil {
		fme = result34.Fme
	}
	return
}

// Parameters:
//  - DataSource
func (p *FeaManagerClient) UpdateDataSource(data_source *DataSource) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateDataSource(data_source); err != nil {
		return
	}
	return p.recvUpdateDataSource()
}

func (p *FeaManagerClient) sendUpdateDataSource(data_source *DataSource) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_data_source", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args37 := NewUpdateDataSourceArgs()
	args37.DataSource = data_source
	if err = args37.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateDataSource() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error39 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error40 error
		error40, err = error39.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error40
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result38 := NewUpdateDataSourceResult()
	if err = result38.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result38.Success
	if result38.Fme != nil {
		fme = result38.Fme
	}
	return
}

// Parameters:
//  - DataName
func (p *FeaManagerClient) DelDataSource(data_name string) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendDelDataSource(data_name); err != nil {
		return
	}
	return p.recvDelDataSource()
}

func (p *FeaManagerClient) sendDelDataSource(data_name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("del_data_source", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args41 := NewDelDataSourceArgs()
	args41.DataName = data_name
	if err = args41.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvDelDataSource() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error43 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error44 error
		error44, err = error43.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error44
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result42 := NewDelDataSourceResult()
	if err = result42.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result42.Success
	if result42.Fme != nil {
		fme = result42.Fme
	}
	return
}

// Parameters:
//  - CrowdId
func (p *FeaManagerClient) GetCrowdProfile(crowd_id int64) (r *CrowdProfile, fme *FeaManagerException, err error) {
	if err = p.sendGetCrowdProfile(crowd_id); err != nil {
		return
	}
	return p.recvGetCrowdProfile()
}

func (p *FeaManagerClient) sendGetCrowdProfile(crowd_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_crowd_profile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args45 := NewGetCrowdProfileArgs()
	args45.CrowdId = crowd_id
	if err = args45.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetCrowdProfile() (value *CrowdProfile, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error47 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error48 error
		error48, err = error47.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error48
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result46 := NewGetCrowdProfileResult()
	if err = result46.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result46.Success
	if result46.Fme != nil {
		fme = result46.Fme
	}
	return
}

// Parameters:
//  - CrowdProfile
func (p *FeaManagerClient) AddCrowdProfile(crowd_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendAddCrowdProfile(crowd_profile); err != nil {
		return
	}
	return p.recvAddCrowdProfile()
}

func (p *FeaManagerClient) sendAddCrowdProfile(crowd_profile *CrowdProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_crowd_profile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args49 := NewAddCrowdProfileArgs()
	args49.CrowdProfile = crowd_profile
	if err = args49.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvAddCrowdProfile() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error51 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error52 error
		error52, err = error51.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error52
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result50 := NewAddCrowdProfileResult()
	if err = result50.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result50.Success
	if result50.Fme != nil {
		fme = result50.Fme
	}
	return
}

// Parameters:
//  - CrowdProfile
func (p *FeaManagerClient) UpdateCrowdProfile(crowd_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateCrowdProfile(crowd_profile); err != nil {
		return
	}
	return p.recvUpdateCrowdProfile()
}

func (p *FeaManagerClient) sendUpdateCrowdProfile(crowd_profile *CrowdProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_crowd_profile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args53 := NewUpdateCrowdProfileArgs()
	args53.CrowdProfile = crowd_profile
	if err = args53.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateCrowdProfile() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error55 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error56 error
		error56, err = error55.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error56
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result54 := NewUpdateCrowdProfileResult()
	if err = result54.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result54.Success
	if result54.Fme != nil {
		fme = result54.Fme
	}
	return
}

func (p *FeaManagerClient) GetAllCrowd() (r []*Crowd, fme *FeaManagerException, err error) {
	if err = p.sendGetAllCrowd(); err != nil {
		return
	}
	return p.recvGetAllCrowd()
}

func (p *FeaManagerClient) sendGetAllCrowd() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_all_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args57 := NewGetAllCrowdArgs()
	if err = args57.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetAllCrowd() (value []*Crowd, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error59 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error60 error
		error60, err = error59.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error60
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result58 := NewGetAllCrowdResult()
	if err = result58.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result58.Success
	if result58.Fme != nil {
		fme = result58.Fme
	}
	return
}

// Parameters:
//  - CrowdId
func (p *FeaManagerClient) GetCrowd(crowd_id int64) (r *Crowd, fme *FeaManagerException, err error) {
	if err = p.sendGetCrowd(crowd_id); err != nil {
		return
	}
	return p.recvGetCrowd()
}

func (p *FeaManagerClient) sendGetCrowd(crowd_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args61 := NewGetCrowdArgs()
	args61.CrowdId = crowd_id
	if err = args61.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetCrowd() (value *Crowd, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error63 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error64 error
		error64, err = error63.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error64
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result62 := NewGetCrowdResult()
	if err = result62.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result62.Success
	if result62.Fme != nil {
		fme = result62.Fme
	}
	return
}

// Parameters:
//  - Crowd
func (p *FeaManagerClient) AddCrowd(crowd *Crowd) (r *CrowdProfile, fme *FeaManagerException, err error) {
	if err = p.sendAddCrowd(crowd); err != nil {
		return
	}
	return p.recvAddCrowd()
}

func (p *FeaManagerClient) sendAddCrowd(crowd *Crowd) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args65 := NewAddCrowdArgs()
	args65.Crowd = crowd
	if err = args65.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvAddCrowd() (value *CrowdProfile, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error67 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error68 error
		error68, err = error67.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error68
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result66 := NewAddCrowdResult()
	if err = result66.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result66.Success
	if result66.Fme != nil {
		fme = result66.Fme
	}
	return
}

// Parameters:
//  - Crowd
func (p *FeaManagerClient) UpdateCrowd(crowd *Crowd) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateCrowd(crowd); err != nil {
		return
	}
	return p.recvUpdateCrowd()
}

func (p *FeaManagerClient) sendUpdateCrowd(crowd *Crowd) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args69 := NewUpdateCrowdArgs()
	args69.Crowd = crowd
	if err = args69.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateCrowd() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error71 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error72 error
		error72, err = error71.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error72
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result70 := NewUpdateCrowdResult()
	if err = result70.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result70.Success
	if result70.Fme != nil {
		fme = result70.Fme
	}
	return
}

// Parameters:
//  - CrowdId
func (p *FeaManagerClient) DelCrowd(crowd_id int64) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendDelCrowd(crowd_id); err != nil {
		return
	}
	return p.recvDelCrowd()
}

func (p *FeaManagerClient) sendDelCrowd(crowd_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("del_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args73 := NewDelCrowdArgs()
	args73.CrowdId = crowd_id
	if err = args73.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvDelCrowd() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error75 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error76 error
		error76, err = error75.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error76
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result74 := NewDelCrowdResult()
	if err = result74.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result74.Success
	if result74.Fme != nil {
		fme = result74.Fme
	}
	return
}

// Parameters:
//  - CrowdTask
func (p *FeaManagerClient) AddCrowdTask(crowd_task *CrowdTask) (r int64, fme *FeaManagerException, err error) {
	if err = p.sendAddCrowdTask(crowd_task); err != nil {
		return
	}
	return p.recvAddCrowdTask()
}

func (p *FeaManagerClient) sendAddCrowdTask(crowd_task *CrowdTask) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_crowd_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args77 := NewAddCrowdTaskArgs()
	args77.CrowdTask = crowd_task
	if err = args77.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvAddCrowdTask() (value int64, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error79 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error80 error
		error80, err = error79.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error80
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result78 := NewAddCrowdTaskResult()
	if err = result78.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result78.Success
	if result78.Fme != nil {
		fme = result78.Fme
	}
	return
}

// Parameters:
//  - CrowdTaskGroup
func (p *FeaManagerClient) AddCrowdTaskGroup(crowd_task_group []*CrowdTask) (r []int64, fme *FeaManagerException, err error) {
	if err = p.sendAddCrowdTaskGroup(crowd_task_group); err != nil {
		return
	}
	return p.recvAddCrowdTaskGroup()
}

func (p *FeaManagerClient) sendAddCrowdTaskGroup(crowd_task_group []*CrowdTask) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("add_crowd_task_group", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args81 := NewAddCrowdTaskGroupArgs()
	args81.CrowdTaskGroup = crowd_task_group
	if err = args81.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvAddCrowdTaskGroup() (value []int64, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error83 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error84 error
		error84, err = error83.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error84
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result82 := NewAddCrowdTaskGroupResult()
	if err = result82.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result82.Success
	if result82.Fme != nil {
		fme = result82.Fme
	}
	return
}

// Parameters:
//  - DataName
func (p *FeaManagerClient) GetCommittedCrowdTask(data_name string) (r *CrowdTask, fme *FeaManagerException, err error) {
	if err = p.sendGetCommittedCrowdTask(data_name); err != nil {
		return
	}
	return p.recvGetCommittedCrowdTask()
}

func (p *FeaManagerClient) sendGetCommittedCrowdTask(data_name string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_committed_crowd_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args85 := NewGetCommittedCrowdTaskArgs()
	args85.DataName = data_name
	if err = args85.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetCommittedCrowdTask() (value *CrowdTask, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error87 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error88 error
		error88, err = error87.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error88
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result86 := NewGetCommittedCrowdTaskResult()
	if err = result86.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result86.Success
	if result86.Fme != nil {
		fme = result86.Fme
	}
	return
}

// Parameters:
//  - DataName
//  - Limit
func (p *FeaManagerClient) GetMultiCommittedCrowdTask(data_name string, limit int32) (r []*CrowdTask, fme *FeaManagerException, err error) {
	if err = p.sendGetMultiCommittedCrowdTask(data_name, limit); err != nil {
		return
	}
	return p.recvGetMultiCommittedCrowdTask()
}

func (p *FeaManagerClient) sendGetMultiCommittedCrowdTask(data_name string, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_multi_committed_crowd_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args89 := NewGetMultiCommittedCrowdTaskArgs()
	args89.DataName = data_name
	args89.Limit = limit
	if err = args89.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetMultiCommittedCrowdTask() (value []*CrowdTask, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error91 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error92 error
		error92, err = error91.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error92
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result90 := NewGetMultiCommittedCrowdTaskResult()
	if err = result90.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result90.Success
	if result90.Fme != nil {
		fme = result90.Fme
	}
	return
}

// Parameters:
//  - TaskId
func (p *FeaManagerClient) GetCrowdTask(task_id int64) (r *CrowdTask, fme *FeaManagerException, err error) {
	if err = p.sendGetCrowdTask(task_id); err != nil {
		return
	}
	return p.recvGetCrowdTask()
}

func (p *FeaManagerClient) sendGetCrowdTask(task_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_crowd_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args93 := NewGetCrowdTaskArgs()
	args93.TaskId = task_id
	if err = args93.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetCrowdTask() (value *CrowdTask, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error95 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error96 error
		error96, err = error95.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error96
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result94 := NewGetCrowdTaskResult()
	if err = result94.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result94.Success
	if result94.Fme != nil {
		fme = result94.Fme
	}
	return
}

func (p *FeaManagerClient) GetAllCrowdTask() (r []*CrowdTask, fme *FeaManagerException, err error) {
	if err = p.sendGetAllCrowdTask(); err != nil {
		return
	}
	return p.recvGetAllCrowdTask()
}

func (p *FeaManagerClient) sendGetAllCrowdTask() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_all_crowd_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args97 := NewGetAllCrowdTaskArgs()
	if err = args97.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetAllCrowdTask() (value []*CrowdTask, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error99 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error100 error
		error100, err = error99.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error100
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result98 := NewGetAllCrowdTaskResult()
	if err = result98.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result98.Success
	if result98.Fme != nil {
		fme = result98.Fme
	}
	return
}

// Parameters:
//  - CrowdTask
func (p *FeaManagerClient) UpdateCrowdTask(crowd_task *CrowdTask) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateCrowdTask(crowd_task); err != nil {
		return
	}
	return p.recvUpdateCrowdTask()
}

func (p *FeaManagerClient) sendUpdateCrowdTask(crowd_task *CrowdTask) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_crowd_task", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args101 := NewUpdateCrowdTaskArgs()
	args101.CrowdTask = crowd_task
	if err = args101.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateCrowdTask() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error103 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error104 error
		error104, err = error103.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error104
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result102 := NewUpdateCrowdTaskResult()
	if err = result102.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result102.Success
	if result102.Fme != nil {
		fme = result102.Fme
	}
	return
}

// Parameters:
//  - TaskProfile
func (p *FeaManagerClient) UpdateTaskStatus(task_profile *CrowdTaskProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateTaskStatus(task_profile); err != nil {
		return
	}
	return p.recvUpdateTaskStatus()
}

func (p *FeaManagerClient) sendUpdateTaskStatus(task_profile *CrowdTaskProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_task_status", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args105 := NewUpdateTaskStatusArgs()
	args105.TaskProfile = task_profile
	if err = args105.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateTaskStatus() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error107 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error108 error
		error108, err = error107.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error108
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result106 := NewUpdateTaskStatusResult()
	if err = result106.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result106.Success
	if result106.Fme != nil {
		fme = result106.Fme
	}
	return
}

// Parameters:
//  - CrowdId
func (p *FeaManagerClient) GetGroupcrowdProfile(crowd_id int64) (r *CrowdProfile, fme *FeaManagerException, err error) {
	if err = p.sendGetGroupcrowdProfile(crowd_id); err != nil {
		return
	}
	return p.recvGetGroupcrowdProfile()
}

func (p *FeaManagerClient) sendGetGroupcrowdProfile(crowd_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_groupcrowd_profile", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args109 := NewGetGroupcrowdProfileArgs()
	args109.CrowdId = crowd_id
	if err = args109.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetGroupcrowdProfile() (value *CrowdProfile, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error111 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error112 error
		error112, err = error111.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error112
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result110 := NewGetGroupcrowdProfileResult()
	if err = result110.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result110.Success
	if result110.Fme != nil {
		fme = result110.Fme
	}
	return
}

func (p *FeaManagerClient) GetCommittedCrowd() (r *Crowd, fme *FeaManagerException, err error) {
	if err = p.sendGetCommittedCrowd(); err != nil {
		return
	}
	return p.recvGetCommittedCrowd()
}

func (p *FeaManagerClient) sendGetCommittedCrowd() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_committed_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args113 := NewGetCommittedCrowdArgs()
	if err = args113.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetCommittedCrowd() (value *Crowd, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error115 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error116 error
		error116, err = error115.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error116
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result114 := NewGetCommittedCrowdResult()
	if err = result114.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result114.Success
	if result114.Fme != nil {
		fme = result114.Fme
	}
	return
}

// Parameters:
//  - Limit
func (p *FeaManagerClient) GetMultiCommittedCrowd(limit int32) (r []*Crowd, fme *FeaManagerException, err error) {
	if err = p.sendGetMultiCommittedCrowd(limit); err != nil {
		return
	}
	return p.recvGetMultiCommittedCrowd()
}

func (p *FeaManagerClient) sendGetMultiCommittedCrowd(limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_multi_committed_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args117 := NewGetMultiCommittedCrowdArgs()
	args117.Limit = limit
	if err = args117.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetMultiCommittedCrowd() (value []*Crowd, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error119 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error120 error
		error120, err = error119.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error120
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result118 := NewGetMultiCommittedCrowdResult()
	if err = result118.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result118.Success
	if result118.Fme != nil {
		fme = result118.Fme
	}
	return
}

func (p *FeaManagerClient) GetCommittedLoadedcrowd() (r *Crowd, fme *FeaManagerException, err error) {
	if err = p.sendGetCommittedLoadedcrowd(); err != nil {
		return
	}
	return p.recvGetCommittedLoadedcrowd()
}

func (p *FeaManagerClient) sendGetCommittedLoadedcrowd() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_committed_loadedcrowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args121 := NewGetCommittedLoadedcrowdArgs()
	if err = args121.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetCommittedLoadedcrowd() (value *Crowd, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error123 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error124 error
		error124, err = error123.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error124
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result122 := NewGetCommittedLoadedcrowdResult()
	if err = result122.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result122.Success
	if result122.Fme != nil {
		fme = result122.Fme
	}
	return
}

// Parameters:
//  - CrowdProfile
func (p *FeaManagerClient) UpdateCrowdStatus(crowd_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateCrowdStatus(crowd_profile); err != nil {
		return
	}
	return p.recvUpdateCrowdStatus()
}

func (p *FeaManagerClient) sendUpdateCrowdStatus(crowd_profile *CrowdProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_crowd_status", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args125 := NewUpdateCrowdStatusArgs()
	args125.CrowdProfile = crowd_profile
	if err = args125.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateCrowdStatus() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error127 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error128 error
		error128, err = error127.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error128
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result126 := NewUpdateCrowdStatusResult()
	if err = result126.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result126.Success
	if result126.Fme != nil {
		fme = result126.Fme
	}
	return
}

// Parameters:
//  - CrowdGroup
func (p *FeaManagerClient) ComposeCrowd(crowd_group *CrowdGroup) (r *CrowdProfile, fme *FeaManagerException, err error) {
	if err = p.sendComposeCrowd(crowd_group); err != nil {
		return
	}
	return p.recvComposeCrowd()
}

func (p *FeaManagerClient) sendComposeCrowd(crowd_group *CrowdGroup) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("compose_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args129 := NewComposeCrowdArgs()
	args129.CrowdGroup = crowd_group
	if err = args129.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvComposeCrowd() (value *CrowdProfile, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error131 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error132 error
		error132, err = error131.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error132
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result130 := NewComposeCrowdResult()
	if err = result130.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result130.Success
	if result130.Fme != nil {
		fme = result130.Fme
	}
	return
}

// Parameters:
//  - ComposeCrowdId
func (p *FeaManagerClient) GetComposeCrowd(compose_crowd_id int64) (r *CrowdGroup, fme *FeaManagerException, err error) {
	if err = p.sendGetComposeCrowd(compose_crowd_id); err != nil {
		return
	}
	return p.recvGetComposeCrowd()
}

func (p *FeaManagerClient) sendGetComposeCrowd(compose_crowd_id int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_compose_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args133 := NewGetComposeCrowdArgs()
	args133.ComposeCrowdId = compose_crowd_id
	if err = args133.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetComposeCrowd() (value *CrowdGroup, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error135 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error136 error
		error136, err = error135.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error136
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result134 := NewGetComposeCrowdResult()
	if err = result134.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result134.Success
	if result134.Fme != nil {
		fme = result134.Fme
	}
	return
}

// Parameters:
//  - CrowdGroup
func (p *FeaManagerClient) UpdateComposeCrowd(crowd_group *CrowdGroup) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateComposeCrowd(crowd_group); err != nil {
		return
	}
	return p.recvUpdateComposeCrowd()
}

func (p *FeaManagerClient) sendUpdateComposeCrowd(crowd_group *CrowdGroup) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_compose_crowd", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args137 := NewUpdateComposeCrowdArgs()
	args137.CrowdGroup = crowd_group
	if err = args137.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateComposeCrowd() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error139 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error140 error
		error140, err = error139.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error140
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result138 := NewUpdateComposeCrowdResult()
	if err = result138.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result138.Success
	if result138.Fme != nil {
		fme = result138.Fme
	}
	return
}

func (p *FeaManagerClient) GetCommittedCrowdgroup() (r *CrowdGroup, fme *FeaManagerException, err error) {
	if err = p.sendGetCommittedCrowdgroup(); err != nil {
		return
	}
	return p.recvGetCommittedCrowdgroup()
}

func (p *FeaManagerClient) sendGetCommittedCrowdgroup() (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_committed_crowdgroup", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args141 := NewGetCommittedCrowdgroupArgs()
	if err = args141.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetCommittedCrowdgroup() (value *CrowdGroup, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error143 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error144 error
		error144, err = error143.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error144
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result142 := NewGetCommittedCrowdgroupResult()
	if err = result142.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result142.Success
	if result142.Fme != nil {
		fme = result142.Fme
	}
	return
}

// Parameters:
//  - Limit
func (p *FeaManagerClient) GetMultiCommittedCrowdgroup(limit int32) (r []*CrowdGroup, fme *FeaManagerException, err error) {
	if err = p.sendGetMultiCommittedCrowdgroup(limit); err != nil {
		return
	}
	return p.recvGetMultiCommittedCrowdgroup()
}

func (p *FeaManagerClient) sendGetMultiCommittedCrowdgroup(limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("get_multi_committed_crowdgroup", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args145 := NewGetMultiCommittedCrowdgroupArgs()
	args145.Limit = limit
	if err = args145.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvGetMultiCommittedCrowdgroup() (value []*CrowdGroup, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error147 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error148 error
		error148, err = error147.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error148
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result146 := NewGetMultiCommittedCrowdgroupResult()
	if err = result146.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result146.Success
	if result146.Fme != nil {
		fme = result146.Fme
	}
	return
}

// Parameters:
//  - CrowdgroupProfile
func (p *FeaManagerClient) UpdateCrowdgroupStatus(crowdgroup_profile *CrowdProfile) (r FeaManagerErrorCode, fme *FeaManagerException, err error) {
	if err = p.sendUpdateCrowdgroupStatus(crowdgroup_profile); err != nil {
		return
	}
	return p.recvUpdateCrowdgroupStatus()
}

func (p *FeaManagerClient) sendUpdateCrowdgroupStatus(crowdgroup_profile *CrowdProfile) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("update_crowdgroup_status", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args149 := NewUpdateCrowdgroupStatusArgs()
	args149.CrowdgroupProfile = crowdgroup_profile
	if err = args149.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *FeaManagerClient) recvUpdateCrowdgroupStatus() (value FeaManagerErrorCode, fme *FeaManagerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error151 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error152 error
		error152, err = error151.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error152
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result150 := NewUpdateCrowdgroupStatusResult()
	if err = result150.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result150.Success
	if result150.Fme != nil {
		fme = result150.Fme
	}
	return
}

type FeaManagerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewFeaManagerProcessor(handler FeaManager) *FeaManagerProcessor {
	self153 := &FeaManagerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self153.AddToProcessorMap("check_data_source_version", &feaManagerProcessorCheckDataSourceVersion{handler: handler})
	self153.AddToProcessorMap("get_all_data_source", &feaManagerProcessorGetAllDataSource{handler: handler})
	self153.AddToProcessorMap("get_data_source_by_name", &feaManagerProcessorGetDataSourceByName{handler: handler})
	self153.AddToProcessorMap("get_data_source_by_id", &feaManagerProcessorGetDataSourceById{handler: handler})
	self153.AddToProcessorMap("add_data_source", &feaManagerProcessorAddDataSource{handler: handler})
	self153.AddToProcessorMap("update_data_source", &feaManagerProcessorUpdateDataSource{handler: handler})
	self153.AddToProcessorMap("del_data_source", &feaManagerProcessorDelDataSource{handler: handler})
	self153.AddToProcessorMap("get_crowd_profile", &feaManagerProcessorGetCrowdProfile{handler: handler})
	self153.AddToProcessorMap("add_crowd_profile", &feaManagerProcessorAddCrowdProfile{handler: handler})
	self153.AddToProcessorMap("update_crowd_profile", &feaManagerProcessorUpdateCrowdProfile{handler: handler})
	self153.AddToProcessorMap("get_all_crowd", &feaManagerProcessorGetAllCrowd{handler: handler})
	self153.AddToProcessorMap("get_crowd", &feaManagerProcessorGetCrowd{handler: handler})
	self153.AddToProcessorMap("add_crowd", &feaManagerProcessorAddCrowd{handler: handler})
	self153.AddToProcessorMap("update_crowd", &feaManagerProcessorUpdateCrowd{handler: handler})
	self153.AddToProcessorMap("del_crowd", &feaManagerProcessorDelCrowd{handler: handler})
	self153.AddToProcessorMap("add_crowd_task", &feaManagerProcessorAddCrowdTask{handler: handler})
	self153.AddToProcessorMap("add_crowd_task_group", &feaManagerProcessorAddCrowdTaskGroup{handler: handler})
	self153.AddToProcessorMap("get_committed_crowd_task", &feaManagerProcessorGetCommittedCrowdTask{handler: handler})
	self153.AddToProcessorMap("get_multi_committed_crowd_task", &feaManagerProcessorGetMultiCommittedCrowdTask{handler: handler})
	self153.AddToProcessorMap("get_crowd_task", &feaManagerProcessorGetCrowdTask{handler: handler})
	self153.AddToProcessorMap("get_all_crowd_task", &feaManagerProcessorGetAllCrowdTask{handler: handler})
	self153.AddToProcessorMap("update_crowd_task", &feaManagerProcessorUpdateCrowdTask{handler: handler})
	self153.AddToProcessorMap("update_task_status", &feaManagerProcessorUpdateTaskStatus{handler: handler})
	self153.AddToProcessorMap("get_groupcrowd_profile", &feaManagerProcessorGetGroupcrowdProfile{handler: handler})
	self153.AddToProcessorMap("get_committed_crowd", &feaManagerProcessorGetCommittedCrowd{handler: handler})
	self153.AddToProcessorMap("get_multi_committed_crowd", &feaManagerProcessorGetMultiCommittedCrowd{handler: handler})
	self153.AddToProcessorMap("get_committed_loadedcrowd", &feaManagerProcessorGetCommittedLoadedcrowd{handler: handler})
	self153.AddToProcessorMap("update_crowd_status", &feaManagerProcessorUpdateCrowdStatus{handler: handler})
	self153.AddToProcessorMap("compose_crowd", &feaManagerProcessorComposeCrowd{handler: handler})
	self153.AddToProcessorMap("get_compose_crowd", &feaManagerProcessorGetComposeCrowd{handler: handler})
	self153.AddToProcessorMap("update_compose_crowd", &feaManagerProcessorUpdateComposeCrowd{handler: handler})
	self153.AddToProcessorMap("get_committed_crowdgroup", &feaManagerProcessorGetCommittedCrowdgroup{handler: handler})
	self153.AddToProcessorMap("get_multi_committed_crowdgroup", &feaManagerProcessorGetMultiCommittedCrowdgroup{handler: handler})
	self153.AddToProcessorMap("update_crowdgroup_status", &feaManagerProcessorUpdateCrowdgroupStatus{handler: handler})
	return self153
}

type feaManagerProcessorCheckDataSourceVersion struct {
	handler FeaManager
}

func (p *feaManagerProcessorCheckDataSourceVersion) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewCheckDataSourceVersionArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("check_data_source_version", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewCheckDataSourceVersionResult()
	if result.Success, result.Fme, err = p.handler.CheckDataSourceVersion(args.Version); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing check_data_source_version: "+err.Error())
		oprot.WriteMessageBegin("check_data_source_version", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("check_data_source_version", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetAllDataSource struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetAllDataSource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllDataSourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_all_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllDataSourceResult()
	if result.Success, result.Fme, err = p.handler.GetAllDataSource(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_all_data_source: "+err.Error())
		oprot.WriteMessageBegin("get_all_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_all_data_source", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetDataSourceByName struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetDataSourceByName) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDataSourceByNameArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_data_source_by_name", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDataSourceByNameResult()
	if result.Success, result.Fme, err = p.handler.GetDataSourceByName(args.Name); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_data_source_by_name: "+err.Error())
		oprot.WriteMessageBegin("get_data_source_by_name", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_data_source_by_name", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetDataSourceById struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetDataSourceById) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDataSourceByIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_data_source_by_id", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDataSourceByIdResult()
	if result.Success, result.Fme, err = p.handler.GetDataSourceById(args.DataSourceId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_data_source_by_id: "+err.Error())
		oprot.WriteMessageBegin("get_data_source_by_id", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_data_source_by_id", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorAddDataSource struct {
	handler FeaManager
}

func (p *feaManagerProcessorAddDataSource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddDataSourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddDataSourceResult()
	if result.Success, result.Fme, err = p.handler.AddDataSource(args.DataSource); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_data_source: "+err.Error())
		oprot.WriteMessageBegin("add_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_data_source", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateDataSource struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateDataSource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateDataSourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateDataSourceResult()
	if result.Success, result.Fme, err = p.handler.UpdateDataSource(args.DataSource); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_data_source: "+err.Error())
		oprot.WriteMessageBegin("update_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_data_source", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorDelDataSource struct {
	handler FeaManager
}

func (p *feaManagerProcessorDelDataSource) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDelDataSourceArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("del_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDelDataSourceResult()
	if result.Success, result.Fme, err = p.handler.DelDataSource(args.DataName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing del_data_source: "+err.Error())
		oprot.WriteMessageBegin("del_data_source", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("del_data_source", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetCrowdProfile struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetCrowdProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCrowdProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_crowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCrowdProfileResult()
	if result.Success, result.Fme, err = p.handler.GetCrowdProfile(args.CrowdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_crowd_profile: "+err.Error())
		oprot.WriteMessageBegin("get_crowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_crowd_profile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorAddCrowdProfile struct {
	handler FeaManager
}

func (p *feaManagerProcessorAddCrowdProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCrowdProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_crowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCrowdProfileResult()
	if result.Success, result.Fme, err = p.handler.AddCrowdProfile(args.CrowdProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_crowd_profile: "+err.Error())
		oprot.WriteMessageBegin("add_crowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_crowd_profile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateCrowdProfile struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateCrowdProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateCrowdProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_crowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateCrowdProfileResult()
	if result.Success, result.Fme, err = p.handler.UpdateCrowdProfile(args.CrowdProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_crowd_profile: "+err.Error())
		oprot.WriteMessageBegin("update_crowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_crowd_profile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetAllCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetAllCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_all_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllCrowdResult()
	if result.Success, result.Fme, err = p.handler.GetAllCrowd(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_all_crowd: "+err.Error())
		oprot.WriteMessageBegin("get_all_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_all_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCrowdResult()
	if result.Success, result.Fme, err = p.handler.GetCrowd(args.CrowdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_crowd: "+err.Error())
		oprot.WriteMessageBegin("get_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorAddCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorAddCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCrowdResult()
	if result.Success, result.Fme, err = p.handler.AddCrowd(args.Crowd); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_crowd: "+err.Error())
		oprot.WriteMessageBegin("add_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateCrowdResult()
	if result.Success, result.Fme, err = p.handler.UpdateCrowd(args.Crowd); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_crowd: "+err.Error())
		oprot.WriteMessageBegin("update_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorDelCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorDelCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewDelCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("del_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewDelCrowdResult()
	if result.Success, result.Fme, err = p.handler.DelCrowd(args.CrowdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing del_crowd: "+err.Error())
		oprot.WriteMessageBegin("del_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("del_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorAddCrowdTask struct {
	handler FeaManager
}

func (p *feaManagerProcessorAddCrowdTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCrowdTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCrowdTaskResult()
	if result.Success, result.Fme, err = p.handler.AddCrowdTask(args.CrowdTask); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_crowd_task: "+err.Error())
		oprot.WriteMessageBegin("add_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_crowd_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorAddCrowdTaskGroup struct {
	handler FeaManager
}

func (p *feaManagerProcessorAddCrowdTaskGroup) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddCrowdTaskGroupArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("add_crowd_task_group", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddCrowdTaskGroupResult()
	if result.Success, result.Fme, err = p.handler.AddCrowdTaskGroup(args.CrowdTaskGroup); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing add_crowd_task_group: "+err.Error())
		oprot.WriteMessageBegin("add_crowd_task_group", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("add_crowd_task_group", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetCommittedCrowdTask struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetCommittedCrowdTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCommittedCrowdTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_committed_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCommittedCrowdTaskResult()
	if result.Success, result.Fme, err = p.handler.GetCommittedCrowdTask(args.DataName); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_committed_crowd_task: "+err.Error())
		oprot.WriteMessageBegin("get_committed_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_committed_crowd_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetMultiCommittedCrowdTask struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetMultiCommittedCrowdTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMultiCommittedCrowdTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_multi_committed_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMultiCommittedCrowdTaskResult()
	if result.Success, result.Fme, err = p.handler.GetMultiCommittedCrowdTask(args.DataName, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_multi_committed_crowd_task: "+err.Error())
		oprot.WriteMessageBegin("get_multi_committed_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_multi_committed_crowd_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetCrowdTask struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetCrowdTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCrowdTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCrowdTaskResult()
	if result.Success, result.Fme, err = p.handler.GetCrowdTask(args.TaskId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_crowd_task: "+err.Error())
		oprot.WriteMessageBegin("get_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_crowd_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetAllCrowdTask struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetAllCrowdTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAllCrowdTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_all_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAllCrowdTaskResult()
	if result.Success, result.Fme, err = p.handler.GetAllCrowdTask(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_all_crowd_task: "+err.Error())
		oprot.WriteMessageBegin("get_all_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_all_crowd_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateCrowdTask struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateCrowdTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateCrowdTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateCrowdTaskResult()
	if result.Success, result.Fme, err = p.handler.UpdateCrowdTask(args.CrowdTask); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_crowd_task: "+err.Error())
		oprot.WriteMessageBegin("update_crowd_task", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_crowd_task", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateTaskStatus struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateTaskStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateTaskStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_task_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateTaskStatusResult()
	if result.Success, result.Fme, err = p.handler.UpdateTaskStatus(args.TaskProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_task_status: "+err.Error())
		oprot.WriteMessageBegin("update_task_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_task_status", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetGroupcrowdProfile struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetGroupcrowdProfile) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetGroupcrowdProfileArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_groupcrowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetGroupcrowdProfileResult()
	if result.Success, result.Fme, err = p.handler.GetGroupcrowdProfile(args.CrowdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_groupcrowd_profile: "+err.Error())
		oprot.WriteMessageBegin("get_groupcrowd_profile", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_groupcrowd_profile", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetCommittedCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetCommittedCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCommittedCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_committed_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCommittedCrowdResult()
	if result.Success, result.Fme, err = p.handler.GetCommittedCrowd(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_committed_crowd: "+err.Error())
		oprot.WriteMessageBegin("get_committed_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_committed_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetMultiCommittedCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetMultiCommittedCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMultiCommittedCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_multi_committed_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMultiCommittedCrowdResult()
	if result.Success, result.Fme, err = p.handler.GetMultiCommittedCrowd(args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_multi_committed_crowd: "+err.Error())
		oprot.WriteMessageBegin("get_multi_committed_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_multi_committed_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetCommittedLoadedcrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetCommittedLoadedcrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCommittedLoadedcrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_committed_loadedcrowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCommittedLoadedcrowdResult()
	if result.Success, result.Fme, err = p.handler.GetCommittedLoadedcrowd(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_committed_loadedcrowd: "+err.Error())
		oprot.WriteMessageBegin("get_committed_loadedcrowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_committed_loadedcrowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateCrowdStatus struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateCrowdStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateCrowdStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_crowd_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateCrowdStatusResult()
	if result.Success, result.Fme, err = p.handler.UpdateCrowdStatus(args.CrowdProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_crowd_status: "+err.Error())
		oprot.WriteMessageBegin("update_crowd_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_crowd_status", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorComposeCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorComposeCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewComposeCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("compose_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewComposeCrowdResult()
	if result.Success, result.Fme, err = p.handler.ComposeCrowd(args.CrowdGroup); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing compose_crowd: "+err.Error())
		oprot.WriteMessageBegin("compose_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("compose_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetComposeCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetComposeCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetComposeCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_compose_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetComposeCrowdResult()
	if result.Success, result.Fme, err = p.handler.GetComposeCrowd(args.ComposeCrowdId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_compose_crowd: "+err.Error())
		oprot.WriteMessageBegin("get_compose_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_compose_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateComposeCrowd struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateComposeCrowd) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateComposeCrowdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_compose_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateComposeCrowdResult()
	if result.Success, result.Fme, err = p.handler.UpdateComposeCrowd(args.CrowdGroup); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_compose_crowd: "+err.Error())
		oprot.WriteMessageBegin("update_compose_crowd", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_compose_crowd", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetCommittedCrowdgroup struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetCommittedCrowdgroup) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCommittedCrowdgroupArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_committed_crowdgroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCommittedCrowdgroupResult()
	if result.Success, result.Fme, err = p.handler.GetCommittedCrowdgroup(); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_committed_crowdgroup: "+err.Error())
		oprot.WriteMessageBegin("get_committed_crowdgroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_committed_crowdgroup", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorGetMultiCommittedCrowdgroup struct {
	handler FeaManager
}

func (p *feaManagerProcessorGetMultiCommittedCrowdgroup) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetMultiCommittedCrowdgroupArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("get_multi_committed_crowdgroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetMultiCommittedCrowdgroupResult()
	if result.Success, result.Fme, err = p.handler.GetMultiCommittedCrowdgroup(args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing get_multi_committed_crowdgroup: "+err.Error())
		oprot.WriteMessageBegin("get_multi_committed_crowdgroup", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("get_multi_committed_crowdgroup", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type feaManagerProcessorUpdateCrowdgroupStatus struct {
	handler FeaManager
}

func (p *feaManagerProcessorUpdateCrowdgroupStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateCrowdgroupStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("update_crowdgroup_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateCrowdgroupStatusResult()
	if result.Success, result.Fme, err = p.handler.UpdateCrowdgroupStatus(args.CrowdgroupProfile); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing update_crowdgroup_status: "+err.Error())
		oprot.WriteMessageBegin("update_crowdgroup_status", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("update_crowdgroup_status", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type CheckDataSourceVersionArgs struct {
	Version int64 `thrift:"version,1" json:"version"`
}

func NewCheckDataSourceVersionArgs() *CheckDataSourceVersionArgs {
	return &CheckDataSourceVersionArgs{}
}

func (p *CheckDataSourceVersionArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckDataSourceVersionArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *CheckDataSourceVersionArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("check_data_source_version_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckDataSourceVersionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:version: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Version)); err != nil {
		return fmt.Errorf("%T.version (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:version: %s", p, err)
	}
	return err
}

func (p *CheckDataSourceVersionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckDataSourceVersionArgs(%+v)", *p)
}

type CheckDataSourceVersionResult struct {
	Success *DataSourceVersion   `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewCheckDataSourceVersionResult() *CheckDataSourceVersionResult {
	return &CheckDataSourceVersionResult{}
}

func (p *CheckDataSourceVersionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CheckDataSourceVersionResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDataSourceVersion()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *CheckDataSourceVersionResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *CheckDataSourceVersionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("check_data_source_version_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CheckDataSourceVersionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *CheckDataSourceVersionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *CheckDataSourceVersionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CheckDataSourceVersionResult(%+v)", *p)
}

type GetAllDataSourceArgs struct {
}

func NewGetAllDataSourceArgs() *GetAllDataSourceArgs {
	return &GetAllDataSourceArgs{}
}

func (p *GetAllDataSourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllDataSourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_all_data_source_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllDataSourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllDataSourceArgs(%+v)", *p)
}

type GetAllDataSourceResult struct {
	Success []*DataSource        `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetAllDataSourceResult() *GetAllDataSourceResult {
	return &GetAllDataSourceResult{}
}

func (p *GetAllDataSourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllDataSourceResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*DataSource, 0, size)
	for i := 0; i < size; i++ {
		_elem154 := NewDataSource()
		if err := _elem154.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem154)
		}
		p.Success = append(p.Success, _elem154)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAllDataSourceResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetAllDataSourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_all_data_source_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllDataSourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllDataSourceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetAllDataSourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllDataSourceResult(%+v)", *p)
}

type GetDataSourceByNameArgs struct {
	Name string `thrift:"name,1" json:"name"`
}

func NewGetDataSourceByNameArgs() *GetDataSourceByNameArgs {
	return &GetDataSourceByNameArgs{}
}

func (p *GetDataSourceByNameArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByNameArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *GetDataSourceByNameArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_data_source_by_name_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByNameArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *GetDataSourceByNameArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDataSourceByNameArgs(%+v)", *p)
}

type GetDataSourceByNameResult struct {
	Success *DataSource          `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetDataSourceByNameResult() *GetDataSourceByNameResult {
	return &GetDataSourceByNameResult{}
}

func (p *GetDataSourceByNameResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByNameResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDataSource()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetDataSourceByNameResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetDataSourceByNameResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_data_source_by_name_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByNameResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDataSourceByNameResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetDataSourceByNameResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDataSourceByNameResult(%+v)", *p)
}

type GetDataSourceByIdArgs struct {
	DataSourceId int64 `thrift:"data_source_id,1" json:"data_source_id"`
}

func NewGetDataSourceByIdArgs() *GetDataSourceByIdArgs {
	return &GetDataSourceByIdArgs{}
}

func (p *GetDataSourceByIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByIdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DataSourceId = v
	}
	return nil
}

func (p *GetDataSourceByIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_data_source_by_id_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_source_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:data_source_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DataSourceId)); err != nil {
		return fmt.Errorf("%T.data_source_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:data_source_id: %s", p, err)
	}
	return err
}

func (p *GetDataSourceByIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDataSourceByIdArgs(%+v)", *p)
}

type GetDataSourceByIdResult struct {
	Success *DataSource          `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetDataSourceByIdResult() *GetDataSourceByIdResult {
	return &GetDataSourceByIdResult{}
}

func (p *GetDataSourceByIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDataSource()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetDataSourceByIdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetDataSourceByIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_data_source_by_id_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDataSourceByIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDataSourceByIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetDataSourceByIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDataSourceByIdResult(%+v)", *p)
}

type AddDataSourceArgs struct {
	DataSource *DataSource `thrift:"data_source,1" json:"data_source"`
}

func NewAddDataSourceArgs() *AddDataSourceArgs {
	return &AddDataSourceArgs{}
}

func (p *AddDataSourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddDataSourceArgs) readField1(iprot thrift.TProtocol) error {
	p.DataSource = NewDataSource()
	if err := p.DataSource.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DataSource)
	}
	return nil
}

func (p *AddDataSourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_data_source_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddDataSourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.DataSource != nil {
		if err := oprot.WriteFieldBegin("data_source", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:data_source: %s", p, err)
		}
		if err := p.DataSource.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DataSource)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:data_source: %s", p, err)
		}
	}
	return err
}

func (p *AddDataSourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddDataSourceArgs(%+v)", *p)
}

type AddDataSourceResult struct {
	Success int64                `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewAddDataSourceResult() *AddDataSourceResult {
	return &AddDataSourceResult{}
}

func (p *AddDataSourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddDataSourceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddDataSourceResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *AddDataSourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_data_source_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddDataSourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddDataSourceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *AddDataSourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddDataSourceResult(%+v)", *p)
}

type UpdateDataSourceArgs struct {
	DataSource *DataSource `thrift:"data_source,1" json:"data_source"`
}

func NewUpdateDataSourceArgs() *UpdateDataSourceArgs {
	return &UpdateDataSourceArgs{}
}

func (p *UpdateDataSourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateDataSourceArgs) readField1(iprot thrift.TProtocol) error {
	p.DataSource = NewDataSource()
	if err := p.DataSource.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.DataSource)
	}
	return nil
}

func (p *UpdateDataSourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_data_source_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateDataSourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.DataSource != nil {
		if err := oprot.WriteFieldBegin("data_source", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:data_source: %s", p, err)
		}
		if err := p.DataSource.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.DataSource)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:data_source: %s", p, err)
		}
	}
	return err
}

func (p *UpdateDataSourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateDataSourceArgs(%+v)", *p)
}

type UpdateDataSourceResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateDataSourceResult() *UpdateDataSourceResult {
	return &UpdateDataSourceResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateDataSourceResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateDataSourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateDataSourceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateDataSourceResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateDataSourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_data_source_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateDataSourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateDataSourceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateDataSourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateDataSourceResult(%+v)", *p)
}

type DelDataSourceArgs struct {
	DataName string `thrift:"data_name,1" json:"data_name"`
}

func NewDelDataSourceArgs() *DelDataSourceArgs {
	return &DelDataSourceArgs{}
}

func (p *DelDataSourceArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelDataSourceArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DataName = v
	}
	return nil
}

func (p *DelDataSourceArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("del_data_source_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelDataSourceArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:data_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataName)); err != nil {
		return fmt.Errorf("%T.data_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:data_name: %s", p, err)
	}
	return err
}

func (p *DelDataSourceArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelDataSourceArgs(%+v)", *p)
}

type DelDataSourceResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewDelDataSourceResult() *DelDataSourceResult {
	return &DelDataSourceResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DelDataSourceResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *DelDataSourceResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelDataSourceResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *DelDataSourceResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *DelDataSourceResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("del_data_source_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelDataSourceResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DelDataSourceResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *DelDataSourceResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelDataSourceResult(%+v)", *p)
}

type GetCrowdProfileArgs struct {
	CrowdId int64 `thrift:"crowd_id,1" json:"crowd_id"`
}

func NewGetCrowdProfileArgs() *GetCrowdProfileArgs {
	return &GetCrowdProfileArgs{}
}

func (p *GetCrowdProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdProfileArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CrowdId = v
	}
	return nil
}

func (p *GetCrowdProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_crowd_profile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("crowd_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:crowd_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CrowdId)); err != nil {
		return fmt.Errorf("%T.crowd_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:crowd_id: %s", p, err)
	}
	return err
}

func (p *GetCrowdProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCrowdProfileArgs(%+v)", *p)
}

type GetCrowdProfileResult struct {
	Success *CrowdProfile        `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetCrowdProfileResult() *GetCrowdProfileResult {
	return &GetCrowdProfileResult{}
}

func (p *GetCrowdProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdProfileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdProfile()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCrowdProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetCrowdProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_crowd_profile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCrowdProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetCrowdProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCrowdProfileResult(%+v)", *p)
}

type AddCrowdProfileArgs struct {
	CrowdProfile *CrowdProfile `thrift:"crowd_profile,1" json:"crowd_profile"`
}

func NewAddCrowdProfileArgs() *AddCrowdProfileArgs {
	return &AddCrowdProfileArgs{}
}

func (p *AddCrowdProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdProfile = NewCrowdProfile()
	if err := p.CrowdProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdProfile)
	}
	return nil
}

func (p *AddCrowdProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_profile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdProfile != nil {
		if err := oprot.WriteFieldBegin("crowd_profile", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_profile: %s", p, err)
		}
		if err := p.CrowdProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_profile: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdProfileArgs(%+v)", *p)
}

type AddCrowdProfileResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewAddCrowdProfileResult() *AddCrowdProfileResult {
	return &AddCrowdProfileResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AddCrowdProfileResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *AddCrowdProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdProfileResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *AddCrowdProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *AddCrowdProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_profile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdProfileResult(%+v)", *p)
}

type UpdateCrowdProfileArgs struct {
	CrowdProfile *CrowdProfile `thrift:"crowd_profile,1" json:"crowd_profile"`
}

func NewUpdateCrowdProfileArgs() *UpdateCrowdProfileArgs {
	return &UpdateCrowdProfileArgs{}
}

func (p *UpdateCrowdProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdProfileArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdProfile = NewCrowdProfile()
	if err := p.CrowdProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdProfile)
	}
	return nil
}

func (p *UpdateCrowdProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_profile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdProfile != nil {
		if err := oprot.WriteFieldBegin("crowd_profile", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_profile: %s", p, err)
		}
		if err := p.CrowdProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_profile: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdProfileArgs(%+v)", *p)
}

type UpdateCrowdProfileResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateCrowdProfileResult() *UpdateCrowdProfileResult {
	return &UpdateCrowdProfileResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateCrowdProfileResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateCrowdProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdProfileResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateCrowdProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateCrowdProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_profile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdProfileResult(%+v)", *p)
}

type GetAllCrowdArgs struct {
}

func NewGetAllCrowdArgs() *GetAllCrowdArgs {
	return &GetAllCrowdArgs{}
}

func (p *GetAllCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_all_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllCrowdArgs(%+v)", *p)
}

type GetAllCrowdResult struct {
	Success []*Crowd             `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetAllCrowdResult() *GetAllCrowdResult {
	return &GetAllCrowdResult{}
}

func (p *GetAllCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*Crowd, 0, size)
	for i := 0; i < size; i++ {
		_elem155 := NewCrowd()
		if err := _elem155.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem155)
		}
		p.Success = append(p.Success, _elem155)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAllCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetAllCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_all_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetAllCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllCrowdResult(%+v)", *p)
}

type GetCrowdArgs struct {
	CrowdId int64 `thrift:"crowd_id,1" json:"crowd_id"`
}

func NewGetCrowdArgs() *GetCrowdArgs {
	return &GetCrowdArgs{}
}

func (p *GetCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CrowdId = v
	}
	return nil
}

func (p *GetCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("crowd_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:crowd_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CrowdId)); err != nil {
		return fmt.Errorf("%T.crowd_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:crowd_id: %s", p, err)
	}
	return err
}

func (p *GetCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCrowdArgs(%+v)", *p)
}

type GetCrowdResult struct {
	Success *Crowd               `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetCrowdResult() *GetCrowdResult {
	return &GetCrowdResult{}
}

func (p *GetCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowd()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCrowdResult(%+v)", *p)
}

type AddCrowdArgs struct {
	Crowd *Crowd `thrift:"crowd,1" json:"crowd"`
}

func NewAddCrowdArgs() *AddCrowdArgs {
	return &AddCrowdArgs{}
}

func (p *AddCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdArgs) readField1(iprot thrift.TProtocol) error {
	p.Crowd = NewCrowd()
	if err := p.Crowd.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Crowd)
	}
	return nil
}

func (p *AddCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Crowd != nil {
		if err := oprot.WriteFieldBegin("crowd", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd: %s", p, err)
		}
		if err := p.Crowd.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Crowd)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdArgs(%+v)", *p)
}

type AddCrowdResult struct {
	Success *CrowdProfile        `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewAddCrowdResult() *AddCrowdResult {
	return &AddCrowdResult{}
}

func (p *AddCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdProfile()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AddCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *AddCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdResult(%+v)", *p)
}

type UpdateCrowdArgs struct {
	Crowd *Crowd `thrift:"crowd,1" json:"crowd"`
}

func NewUpdateCrowdArgs() *UpdateCrowdArgs {
	return &UpdateCrowdArgs{}
}

func (p *UpdateCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdArgs) readField1(iprot thrift.TProtocol) error {
	p.Crowd = NewCrowd()
	if err := p.Crowd.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Crowd)
	}
	return nil
}

func (p *UpdateCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Crowd != nil {
		if err := oprot.WriteFieldBegin("crowd", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd: %s", p, err)
		}
		if err := p.Crowd.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Crowd)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdArgs(%+v)", *p)
}

type UpdateCrowdResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateCrowdResult() *UpdateCrowdResult {
	return &UpdateCrowdResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateCrowdResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdResult(%+v)", *p)
}

type DelCrowdArgs struct {
	CrowdId int64 `thrift:"crowd_id,1" json:"crowd_id"`
}

func NewDelCrowdArgs() *DelCrowdArgs {
	return &DelCrowdArgs{}
}

func (p *DelCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelCrowdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CrowdId = v
	}
	return nil
}

func (p *DelCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("del_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("crowd_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:crowd_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CrowdId)); err != nil {
		return fmt.Errorf("%T.crowd_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:crowd_id: %s", p, err)
	}
	return err
}

func (p *DelCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelCrowdArgs(%+v)", *p)
}

type DelCrowdResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewDelCrowdResult() *DelCrowdResult {
	return &DelCrowdResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DelCrowdResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *DelCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DelCrowdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *DelCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *DelCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("del_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DelCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *DelCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *DelCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DelCrowdResult(%+v)", *p)
}

type AddCrowdTaskArgs struct {
	CrowdTask *CrowdTask `thrift:"crowd_task,1" json:"crowd_task"`
}

func NewAddCrowdTaskArgs() *AddCrowdTaskArgs {
	return &AddCrowdTaskArgs{}
}

func (p *AddCrowdTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdTask = NewCrowdTask()
	if err := p.CrowdTask.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdTask)
	}
	return nil
}

func (p *AddCrowdTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdTask != nil {
		if err := oprot.WriteFieldBegin("crowd_task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_task: %s", p, err)
		}
		if err := p.CrowdTask.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdTask)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_task: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdTaskArgs(%+v)", *p)
}

type AddCrowdTaskResult struct {
	Success int64                `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewAddCrowdTaskResult() *AddCrowdTaskResult {
	return &AddCrowdTaskResult{}
}

func (p *AddCrowdTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I64 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddCrowdTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *AddCrowdTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I64, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddCrowdTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdTaskResult(%+v)", *p)
}

type AddCrowdTaskGroupArgs struct {
	CrowdTaskGroup []*CrowdTask `thrift:"crowd_task_group,1" json:"crowd_task_group"`
}

func NewAddCrowdTaskGroupArgs() *AddCrowdTaskGroupArgs {
	return &AddCrowdTaskGroupArgs{}
}

func (p *AddCrowdTaskGroupArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskGroupArgs) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CrowdTaskGroup = make([]*CrowdTask, 0, size)
	for i := 0; i < size; i++ {
		_elem156 := NewCrowdTask()
		if err := _elem156.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem156)
		}
		p.CrowdTaskGroup = append(p.CrowdTaskGroup, _elem156)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddCrowdTaskGroupArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_task_group_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskGroupArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdTaskGroup != nil {
		if err := oprot.WriteFieldBegin("crowd_task_group", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_task_group: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CrowdTaskGroup)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CrowdTaskGroup {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_task_group: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdTaskGroupArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdTaskGroupArgs(%+v)", *p)
}

type AddCrowdTaskGroupResult struct {
	Success []int64              `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewAddCrowdTaskGroupResult() *AddCrowdTaskGroupResult {
	return &AddCrowdTaskGroupResult{}
}

func (p *AddCrowdTaskGroupResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskGroupResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem157 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem157 = v
		}
		p.Success = append(p.Success, _elem157)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddCrowdTaskGroupResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *AddCrowdTaskGroupResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("add_crowd_task_group_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddCrowdTaskGroupResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdTaskGroupResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *AddCrowdTaskGroupResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddCrowdTaskGroupResult(%+v)", *p)
}

type GetCommittedCrowdTaskArgs struct {
	DataName string `thrift:"data_name,1" json:"data_name"`
}

func NewGetCommittedCrowdTaskArgs() *GetCommittedCrowdTaskArgs {
	return &GetCommittedCrowdTaskArgs{}
}

func (p *GetCommittedCrowdTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdTaskArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DataName = v
	}
	return nil
}

func (p *GetCommittedCrowdTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_crowd_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:data_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataName)); err != nil {
		return fmt.Errorf("%T.data_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:data_name: %s", p, err)
	}
	return err
}

func (p *GetCommittedCrowdTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedCrowdTaskArgs(%+v)", *p)
}

type GetCommittedCrowdTaskResult struct {
	Success *CrowdTask           `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetCommittedCrowdTaskResult() *GetCommittedCrowdTaskResult {
	return &GetCommittedCrowdTaskResult{}
}

func (p *GetCommittedCrowdTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdTaskResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdTask()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCommittedCrowdTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetCommittedCrowdTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_crowd_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedCrowdTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedCrowdTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedCrowdTaskResult(%+v)", *p)
}

type GetMultiCommittedCrowdTaskArgs struct {
	DataName string `thrift:"data_name,1" json:"data_name"`
	Limit    int32  `thrift:"limit,2" json:"limit"`
}

func NewGetMultiCommittedCrowdTaskArgs() *GetMultiCommittedCrowdTaskArgs {
	return &GetMultiCommittedCrowdTaskArgs{}
}

func (p *GetMultiCommittedCrowdTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DataName = v
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_multi_committed_crowd_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:data_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataName)); err != nil {
		return fmt.Errorf("%T.data_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:data_name: %s", p, err)
	}
	return err
}

func (p *GetMultiCommittedCrowdTaskArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:limit: %s", p, err)
	}
	return err
}

func (p *GetMultiCommittedCrowdTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMultiCommittedCrowdTaskArgs(%+v)", *p)
}

type GetMultiCommittedCrowdTaskResult struct {
	Success []*CrowdTask         `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetMultiCommittedCrowdTaskResult() *GetMultiCommittedCrowdTaskResult {
	return &GetMultiCommittedCrowdTaskResult{}
}

func (p *GetMultiCommittedCrowdTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*CrowdTask, 0, size)
	for i := 0; i < size; i++ {
		_elem158 := NewCrowdTask()
		if err := _elem158.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem158)
		}
		p.Success = append(p.Success, _elem158)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_multi_committed_crowd_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMultiCommittedCrowdTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetMultiCommittedCrowdTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMultiCommittedCrowdTaskResult(%+v)", *p)
}

type GetCrowdTaskArgs struct {
	TaskId int64 `thrift:"task_id,1" json:"task_id"`
}

func NewGetCrowdTaskArgs() *GetCrowdTaskArgs {
	return &GetCrowdTaskArgs{}
}

func (p *GetCrowdTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdTaskArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *GetCrowdTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_crowd_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:task_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:task_id: %s", p, err)
	}
	return err
}

func (p *GetCrowdTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCrowdTaskArgs(%+v)", *p)
}

type GetCrowdTaskResult struct {
	Success *CrowdTask           `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetCrowdTaskResult() *GetCrowdTaskResult {
	return &GetCrowdTaskResult{}
}

func (p *GetCrowdTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdTaskResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdTask()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCrowdTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetCrowdTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_crowd_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCrowdTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCrowdTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetCrowdTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCrowdTaskResult(%+v)", *p)
}

type GetAllCrowdTaskArgs struct {
}

func NewGetAllCrowdTaskArgs() *GetAllCrowdTaskArgs {
	return &GetAllCrowdTaskArgs{}
}

func (p *GetAllCrowdTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_all_crowd_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllCrowdTaskArgs(%+v)", *p)
}

type GetAllCrowdTaskResult struct {
	Success []*CrowdTask         `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetAllCrowdTaskResult() *GetAllCrowdTaskResult {
	return &GetAllCrowdTaskResult{}
}

func (p *GetAllCrowdTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdTaskResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*CrowdTask, 0, size)
	for i := 0; i < size; i++ {
		_elem159 := NewCrowdTask()
		if err := _elem159.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem159)
		}
		p.Success = append(p.Success, _elem159)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAllCrowdTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetAllCrowdTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_all_crowd_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAllCrowdTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAllCrowdTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetAllCrowdTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAllCrowdTaskResult(%+v)", *p)
}

type UpdateCrowdTaskArgs struct {
	CrowdTask *CrowdTask `thrift:"crowd_task,1" json:"crowd_task"`
}

func NewUpdateCrowdTaskArgs() *UpdateCrowdTaskArgs {
	return &UpdateCrowdTaskArgs{}
}

func (p *UpdateCrowdTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdTask = NewCrowdTask()
	if err := p.CrowdTask.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdTask)
	}
	return nil
}

func (p *UpdateCrowdTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_task_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdTask != nil {
		if err := oprot.WriteFieldBegin("crowd_task", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_task: %s", p, err)
		}
		if err := p.CrowdTask.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdTask)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_task: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdTaskArgs(%+v)", *p)
}

type UpdateCrowdTaskResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateCrowdTaskResult() *UpdateCrowdTaskResult {
	return &UpdateCrowdTaskResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateCrowdTaskResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateCrowdTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdTaskResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateCrowdTaskResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateCrowdTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_task_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdTaskResult(%+v)", *p)
}

type UpdateTaskStatusArgs struct {
	TaskProfile *CrowdTaskProfile `thrift:"task_profile,1" json:"task_profile"`
}

func NewUpdateTaskStatusArgs() *UpdateTaskStatusArgs {
	return &UpdateTaskStatusArgs{}
}

func (p *UpdateTaskStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTaskStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.TaskProfile = NewCrowdTaskProfile()
	if err := p.TaskProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TaskProfile)
	}
	return nil
}

func (p *UpdateTaskStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_task_status_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTaskStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.TaskProfile != nil {
		if err := oprot.WriteFieldBegin("task_profile", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:task_profile: %s", p, err)
		}
		if err := p.TaskProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TaskProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:task_profile: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTaskStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTaskStatusArgs(%+v)", *p)
}

type UpdateTaskStatusResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateTaskStatusResult() *UpdateTaskStatusResult {
	return &UpdateTaskStatusResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateTaskStatusResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateTaskStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateTaskStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateTaskStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateTaskStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_task_status_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateTaskStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTaskStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateTaskStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateTaskStatusResult(%+v)", *p)
}

type GetGroupcrowdProfileArgs struct {
	CrowdId int64 `thrift:"crowd_id,1" json:"crowd_id"`
}

func NewGetGroupcrowdProfileArgs() *GetGroupcrowdProfileArgs {
	return &GetGroupcrowdProfileArgs{}
}

func (p *GetGroupcrowdProfileArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetGroupcrowdProfileArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CrowdId = v
	}
	return nil
}

func (p *GetGroupcrowdProfileArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_groupcrowd_profile_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetGroupcrowdProfileArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("crowd_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:crowd_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CrowdId)); err != nil {
		return fmt.Errorf("%T.crowd_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:crowd_id: %s", p, err)
	}
	return err
}

func (p *GetGroupcrowdProfileArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetGroupcrowdProfileArgs(%+v)", *p)
}

type GetGroupcrowdProfileResult struct {
	Success *CrowdProfile        `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetGroupcrowdProfileResult() *GetGroupcrowdProfileResult {
	return &GetGroupcrowdProfileResult{}
}

func (p *GetGroupcrowdProfileResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetGroupcrowdProfileResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdProfile()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetGroupcrowdProfileResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetGroupcrowdProfileResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_groupcrowd_profile_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetGroupcrowdProfileResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetGroupcrowdProfileResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetGroupcrowdProfileResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetGroupcrowdProfileResult(%+v)", *p)
}

type GetCommittedCrowdArgs struct {
}

func NewGetCommittedCrowdArgs() *GetCommittedCrowdArgs {
	return &GetCommittedCrowdArgs{}
}

func (p *GetCommittedCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedCrowdArgs(%+v)", *p)
}

type GetCommittedCrowdResult struct {
	Success *Crowd               `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetCommittedCrowdResult() *GetCommittedCrowdResult {
	return &GetCommittedCrowdResult{}
}

func (p *GetCommittedCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowd()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCommittedCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetCommittedCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedCrowdResult(%+v)", *p)
}

type GetMultiCommittedCrowdArgs struct {
	Limit int32 `thrift:"limit,1" json:"limit"`
}

func NewGetMultiCommittedCrowdArgs() *GetMultiCommittedCrowdArgs {
	return &GetMultiCommittedCrowdArgs{}
}

func (p *GetMultiCommittedCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *GetMultiCommittedCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_multi_committed_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:limit: %s", p, err)
	}
	return err
}

func (p *GetMultiCommittedCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMultiCommittedCrowdArgs(%+v)", *p)
}

type GetMultiCommittedCrowdResult struct {
	Success []*Crowd             `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetMultiCommittedCrowdResult() *GetMultiCommittedCrowdResult {
	return &GetMultiCommittedCrowdResult{}
}

func (p *GetMultiCommittedCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*Crowd, 0, size)
	for i := 0; i < size; i++ {
		_elem160 := NewCrowd()
		if err := _elem160.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem160)
		}
		p.Success = append(p.Success, _elem160)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetMultiCommittedCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_multi_committed_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMultiCommittedCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetMultiCommittedCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMultiCommittedCrowdResult(%+v)", *p)
}

type GetCommittedLoadedcrowdArgs struct {
}

func NewGetCommittedLoadedcrowdArgs() *GetCommittedLoadedcrowdArgs {
	return &GetCommittedLoadedcrowdArgs{}
}

func (p *GetCommittedLoadedcrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedLoadedcrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_loadedcrowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedLoadedcrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedLoadedcrowdArgs(%+v)", *p)
}

type GetCommittedLoadedcrowdResult struct {
	Success *Crowd               `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetCommittedLoadedcrowdResult() *GetCommittedLoadedcrowdResult {
	return &GetCommittedLoadedcrowdResult{}
}

func (p *GetCommittedLoadedcrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedLoadedcrowdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowd()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCommittedLoadedcrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetCommittedLoadedcrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_loadedcrowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedLoadedcrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedLoadedcrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedLoadedcrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedLoadedcrowdResult(%+v)", *p)
}

type UpdateCrowdStatusArgs struct {
	CrowdProfile *CrowdProfile `thrift:"crowd_profile,1" json:"crowd_profile"`
}

func NewUpdateCrowdStatusArgs() *UpdateCrowdStatusArgs {
	return &UpdateCrowdStatusArgs{}
}

func (p *UpdateCrowdStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdProfile = NewCrowdProfile()
	if err := p.CrowdProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdProfile)
	}
	return nil
}

func (p *UpdateCrowdStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_status_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdProfile != nil {
		if err := oprot.WriteFieldBegin("crowd_profile", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_profile: %s", p, err)
		}
		if err := p.CrowdProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_profile: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdStatusArgs(%+v)", *p)
}

type UpdateCrowdStatusResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateCrowdStatusResult() *UpdateCrowdStatusResult {
	return &UpdateCrowdStatusResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateCrowdStatusResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateCrowdStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateCrowdStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateCrowdStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowd_status_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdStatusResult(%+v)", *p)
}

type ComposeCrowdArgs struct {
	CrowdGroup *CrowdGroup `thrift:"crowd_group,1" json:"crowd_group"`
}

func NewComposeCrowdArgs() *ComposeCrowdArgs {
	return &ComposeCrowdArgs{}
}

func (p *ComposeCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ComposeCrowdArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdGroup = NewCrowdGroup()
	if err := p.CrowdGroup.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdGroup)
	}
	return nil
}

func (p *ComposeCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("compose_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ComposeCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdGroup != nil {
		if err := oprot.WriteFieldBegin("crowd_group", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_group: %s", p, err)
		}
		if err := p.CrowdGroup.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdGroup)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_group: %s", p, err)
		}
	}
	return err
}

func (p *ComposeCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ComposeCrowdArgs(%+v)", *p)
}

type ComposeCrowdResult struct {
	Success *CrowdProfile        `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewComposeCrowdResult() *ComposeCrowdResult {
	return &ComposeCrowdResult{}
}

func (p *ComposeCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ComposeCrowdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdProfile()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ComposeCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *ComposeCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("compose_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ComposeCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ComposeCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *ComposeCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ComposeCrowdResult(%+v)", *p)
}

type GetComposeCrowdArgs struct {
	ComposeCrowdId int64 `thrift:"compose_crowd_id,1" json:"compose_crowd_id"`
}

func NewGetComposeCrowdArgs() *GetComposeCrowdArgs {
	return &GetComposeCrowdArgs{}
}

func (p *GetComposeCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetComposeCrowdArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ComposeCrowdId = v
	}
	return nil
}

func (p *GetComposeCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_compose_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetComposeCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("compose_crowd_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:compose_crowd_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ComposeCrowdId)); err != nil {
		return fmt.Errorf("%T.compose_crowd_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:compose_crowd_id: %s", p, err)
	}
	return err
}

func (p *GetComposeCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetComposeCrowdArgs(%+v)", *p)
}

type GetComposeCrowdResult struct {
	Success *CrowdGroup          `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetComposeCrowdResult() *GetComposeCrowdResult {
	return &GetComposeCrowdResult{}
}

func (p *GetComposeCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetComposeCrowdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdGroup()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetComposeCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetComposeCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_compose_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetComposeCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetComposeCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetComposeCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetComposeCrowdResult(%+v)", *p)
}

type UpdateComposeCrowdArgs struct {
	CrowdGroup *CrowdGroup `thrift:"crowd_group,1" json:"crowd_group"`
}

func NewUpdateComposeCrowdArgs() *UpdateComposeCrowdArgs {
	return &UpdateComposeCrowdArgs{}
}

func (p *UpdateComposeCrowdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateComposeCrowdArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdGroup = NewCrowdGroup()
	if err := p.CrowdGroup.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdGroup)
	}
	return nil
}

func (p *UpdateComposeCrowdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_compose_crowd_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateComposeCrowdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdGroup != nil {
		if err := oprot.WriteFieldBegin("crowd_group", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowd_group: %s", p, err)
		}
		if err := p.CrowdGroup.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdGroup)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowd_group: %s", p, err)
		}
	}
	return err
}

func (p *UpdateComposeCrowdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateComposeCrowdArgs(%+v)", *p)
}

type UpdateComposeCrowdResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateComposeCrowdResult() *UpdateComposeCrowdResult {
	return &UpdateComposeCrowdResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateComposeCrowdResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateComposeCrowdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateComposeCrowdResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateComposeCrowdResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateComposeCrowdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_compose_crowd_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateComposeCrowdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateComposeCrowdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateComposeCrowdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateComposeCrowdResult(%+v)", *p)
}

type GetCommittedCrowdgroupArgs struct {
}

func NewGetCommittedCrowdgroupArgs() *GetCommittedCrowdgroupArgs {
	return &GetCommittedCrowdgroupArgs{}
}

func (p *GetCommittedCrowdgroupArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdgroupArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_crowdgroup_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdgroupArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedCrowdgroupArgs(%+v)", *p)
}

type GetCommittedCrowdgroupResult struct {
	Success *CrowdGroup          `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetCommittedCrowdgroupResult() *GetCommittedCrowdgroupResult {
	return &GetCommittedCrowdgroupResult{}
}

func (p *GetCommittedCrowdgroupResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdgroupResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewCrowdGroup()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCommittedCrowdgroupResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetCommittedCrowdgroupResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_committed_crowdgroup_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCommittedCrowdgroupResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedCrowdgroupResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetCommittedCrowdgroupResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCommittedCrowdgroupResult(%+v)", *p)
}

type GetMultiCommittedCrowdgroupArgs struct {
	Limit int32 `thrift:"limit,1" json:"limit"`
}

func NewGetMultiCommittedCrowdgroupArgs() *GetMultiCommittedCrowdgroupArgs {
	return &GetMultiCommittedCrowdgroupArgs{}
}

func (p *GetMultiCommittedCrowdgroupArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdgroupArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *GetMultiCommittedCrowdgroupArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_multi_committed_crowdgroup_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdgroupArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:limit: %s", p, err)
	}
	return err
}

func (p *GetMultiCommittedCrowdgroupArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMultiCommittedCrowdgroupArgs(%+v)", *p)
}

type GetMultiCommittedCrowdgroupResult struct {
	Success []*CrowdGroup        `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewGetMultiCommittedCrowdgroupResult() *GetMultiCommittedCrowdgroupResult {
	return &GetMultiCommittedCrowdgroupResult{}
}

func (p *GetMultiCommittedCrowdgroupResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdgroupResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*CrowdGroup, 0, size)
	for i := 0; i < size; i++ {
		_elem161 := NewCrowdGroup()
		if err := _elem161.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem161)
		}
		p.Success = append(p.Success, _elem161)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdgroupResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *GetMultiCommittedCrowdgroupResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("get_multi_committed_crowdgroup_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetMultiCommittedCrowdgroupResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetMultiCommittedCrowdgroupResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *GetMultiCommittedCrowdgroupResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetMultiCommittedCrowdgroupResult(%+v)", *p)
}

type UpdateCrowdgroupStatusArgs struct {
	CrowdgroupProfile *CrowdProfile `thrift:"crowdgroup_profile,1" json:"crowdgroup_profile"`
}

func NewUpdateCrowdgroupStatusArgs() *UpdateCrowdgroupStatusArgs {
	return &UpdateCrowdgroupStatusArgs{}
}

func (p *UpdateCrowdgroupStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdgroupStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.CrowdgroupProfile = NewCrowdProfile()
	if err := p.CrowdgroupProfile.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CrowdgroupProfile)
	}
	return nil
}

func (p *UpdateCrowdgroupStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowdgroup_status_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdgroupStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CrowdgroupProfile != nil {
		if err := oprot.WriteFieldBegin("crowdgroup_profile", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:crowdgroup_profile: %s", p, err)
		}
		if err := p.CrowdgroupProfile.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CrowdgroupProfile)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:crowdgroup_profile: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdgroupStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdgroupStatusArgs(%+v)", *p)
}

type UpdateCrowdgroupStatusResult struct {
	Success FeaManagerErrorCode  `thrift:"success,0" json:"success"`
	Fme     *FeaManagerException `thrift:"fme,1" json:"fme"`
}

func NewUpdateCrowdgroupStatusResult() *UpdateCrowdgroupStatusResult {
	return &UpdateCrowdgroupStatusResult{
		Success: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *UpdateCrowdgroupStatusResult) IsSetSuccess() bool {
	return int64(p.Success) != math.MinInt32-1
}

func (p *UpdateCrowdgroupStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdgroupStatusResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *UpdateCrowdgroupStatusResult) readField1(iprot thrift.TProtocol) error {
	p.Fme = NewFeaManagerException()
	if err := p.Fme.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Fme)
	}
	return nil
}

func (p *UpdateCrowdgroupStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("update_crowdgroup_status_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Fme != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateCrowdgroupStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.IsSetSuccess() {
		if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Success)); err != nil {
			return fmt.Errorf("%T.success (0) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdgroupStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Fme != nil {
		if err := oprot.WriteFieldBegin("fme", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:fme: %s", p, err)
		}
		if err := p.Fme.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Fme)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:fme: %s", p, err)
		}
	}
	return err
}

func (p *UpdateCrowdgroupStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateCrowdgroupStatusResult(%+v)", *p)
}
