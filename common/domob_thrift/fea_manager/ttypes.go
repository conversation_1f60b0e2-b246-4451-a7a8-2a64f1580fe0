// Autogenerated by Thr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package fea_manager

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

type AggregationType int64

const (
	AggregationType_UNION        AggregationType = 1
	AggregationType_INTERSECTION AggregationType = 2
	AggregationType_NoneType     AggregationType = 11
)

func (p AggregationType) String() string {
	switch p {
	case AggregationType_UNION:
		return "AggregationType_UNION"
	case AggregationType_INTERSECTION:
		return "AggregationType_INTERSECTION"
	case AggregationType_NoneType:
		return "AggregationType_NoneType"
	}
	return "<UNSET>"
}

func AggregationTypeFromString(s string) (AggregationType, error) {
	switch s {
	case "AggregationType_UNION":
		return AggregationType_UNION, nil
	case "AggregationType_INTERSECTION":
		return AggregationType_INTERSECTION, nil
	case "AggregationType_NoneType":
		return AggregationType_NoneType, nil
	}
	return AggregationType(math.MinInt32 - 1), fmt.Errorf("not a valid AggregationType string")
}

type DataSourceStatus int64

const (
	DataSourceStatus_INVALID DataSourceStatus = 101
	DataSourceStatus_USING   DataSourceStatus = 1
	DataSourceStatus_VALID   DataSourceStatus = 2
)

func (p DataSourceStatus) String() string {
	switch p {
	case DataSourceStatus_INVALID:
		return "DataSourceStatus_INVALID"
	case DataSourceStatus_USING:
		return "DataSourceStatus_USING"
	case DataSourceStatus_VALID:
		return "DataSourceStatus_VALID"
	}
	return "<UNSET>"
}

func DataSourceStatusFromString(s string) (DataSourceStatus, error) {
	switch s {
	case "DataSourceStatus_INVALID":
		return DataSourceStatus_INVALID, nil
	case "DataSourceStatus_USING":
		return DataSourceStatus_USING, nil
	case "DataSourceStatus_VALID":
		return DataSourceStatus_VALID, nil
	}
	return DataSourceStatus(math.MinInt32 - 1), fmt.Errorf("not a valid DataSourceStatus string")
}

type DataSourceType int64

const (
	DataSourceType_CROWD_BEHAVIOR   DataSourceType = 1
	DataSourceType_CROWD_AD_EFFECT  DataSourceType = 2
	DataSourceType_CROWD_FIRSTPARTY DataSourceType = 3
)

func (p DataSourceType) String() string {
	switch p {
	case DataSourceType_CROWD_BEHAVIOR:
		return "DataSourceType_CROWD_BEHAVIOR"
	case DataSourceType_CROWD_AD_EFFECT:
		return "DataSourceType_CROWD_AD_EFFECT"
	case DataSourceType_CROWD_FIRSTPARTY:
		return "DataSourceType_CROWD_FIRSTPARTY"
	}
	return "<UNSET>"
}

func DataSourceTypeFromString(s string) (DataSourceType, error) {
	switch s {
	case "DataSourceType_CROWD_BEHAVIOR":
		return DataSourceType_CROWD_BEHAVIOR, nil
	case "DataSourceType_CROWD_AD_EFFECT":
		return DataSourceType_CROWD_AD_EFFECT, nil
	case "DataSourceType_CROWD_FIRSTPARTY":
		return DataSourceType_CROWD_FIRSTPARTY, nil
	}
	return DataSourceType(math.MinInt32 - 1), fmt.Errorf("not a valid DataSourceType string")
}

type CrowdTaskStatus int64

const (
	CrowdTaskStatus_NEW_CREATED CrowdTaskStatus = 1
	CrowdTaskStatus_COMMITED    CrowdTaskStatus = 2
	CrowdTaskStatus_RUNNING     CrowdTaskStatus = 3
	CrowdTaskStatus_SUCESS      CrowdTaskStatus = 4
	CrowdTaskStatus_FAILURE     CrowdTaskStatus = 5
	CrowdTaskStatus_WITHDRAWED  CrowdTaskStatus = 6
	CrowdTaskStatus_STOPED      CrowdTaskStatus = 101
)

func (p CrowdTaskStatus) String() string {
	switch p {
	case CrowdTaskStatus_NEW_CREATED:
		return "CrowdTaskStatus_NEW_CREATED"
	case CrowdTaskStatus_COMMITED:
		return "CrowdTaskStatus_COMMITED"
	case CrowdTaskStatus_RUNNING:
		return "CrowdTaskStatus_RUNNING"
	case CrowdTaskStatus_SUCESS:
		return "CrowdTaskStatus_SUCESS"
	case CrowdTaskStatus_FAILURE:
		return "CrowdTaskStatus_FAILURE"
	case CrowdTaskStatus_WITHDRAWED:
		return "CrowdTaskStatus_WITHDRAWED"
	case CrowdTaskStatus_STOPED:
		return "CrowdTaskStatus_STOPED"
	}
	return "<UNSET>"
}

func CrowdTaskStatusFromString(s string) (CrowdTaskStatus, error) {
	switch s {
	case "CrowdTaskStatus_NEW_CREATED":
		return CrowdTaskStatus_NEW_CREATED, nil
	case "CrowdTaskStatus_COMMITED":
		return CrowdTaskStatus_COMMITED, nil
	case "CrowdTaskStatus_RUNNING":
		return CrowdTaskStatus_RUNNING, nil
	case "CrowdTaskStatus_SUCESS":
		return CrowdTaskStatus_SUCESS, nil
	case "CrowdTaskStatus_FAILURE":
		return CrowdTaskStatus_FAILURE, nil
	case "CrowdTaskStatus_WITHDRAWED":
		return CrowdTaskStatus_WITHDRAWED, nil
	case "CrowdTaskStatus_STOPED":
		return CrowdTaskStatus_STOPED, nil
	}
	return CrowdTaskStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CrowdTaskStatus string")
}

type ComputationType int64

const (
	ComputationType_COMPUTE_ONCE     ComputationType = 1
	ComputationType_COMPUTE_CONTINUE ComputationType = 2
)

func (p ComputationType) String() string {
	switch p {
	case ComputationType_COMPUTE_ONCE:
		return "ComputationType_COMPUTE_ONCE"
	case ComputationType_COMPUTE_CONTINUE:
		return "ComputationType_COMPUTE_CONTINUE"
	}
	return "<UNSET>"
}

func ComputationTypeFromString(s string) (ComputationType, error) {
	switch s {
	case "ComputationType_COMPUTE_ONCE":
		return ComputationType_COMPUTE_ONCE, nil
	case "ComputationType_COMPUTE_CONTINUE":
		return ComputationType_COMPUTE_CONTINUE, nil
	}
	return ComputationType(math.MinInt32 - 1), fmt.Errorf("not a valid ComputationType string")
}

type CrowdTaskType int64

const (
	CrowdTaskType_FEA_DIRECT_TARGET CrowdTaskType = 1
	CrowdTaskType_LOOKALIKE         CrowdTaskType = 2
)

func (p CrowdTaskType) String() string {
	switch p {
	case CrowdTaskType_FEA_DIRECT_TARGET:
		return "CrowdTaskType_FEA_DIRECT_TARGET"
	case CrowdTaskType_LOOKALIKE:
		return "CrowdTaskType_LOOKALIKE"
	}
	return "<UNSET>"
}

func CrowdTaskTypeFromString(s string) (CrowdTaskType, error) {
	switch s {
	case "CrowdTaskType_FEA_DIRECT_TARGET":
		return CrowdTaskType_FEA_DIRECT_TARGET, nil
	case "CrowdTaskType_LOOKALIKE":
		return CrowdTaskType_LOOKALIKE, nil
	}
	return CrowdTaskType(math.MinInt32 - 1), fmt.Errorf("not a valid CrowdTaskType string")
}

type LookalikePlatform int64

const (
	LookalikePlatform_ANDROID LookalikePlatform = 1
	LookalikePlatform_IOS     LookalikePlatform = 2
	LookalikePlatform_ALL     LookalikePlatform = 3
)

func (p LookalikePlatform) String() string {
	switch p {
	case LookalikePlatform_ANDROID:
		return "LookalikePlatform_ANDROID"
	case LookalikePlatform_IOS:
		return "LookalikePlatform_IOS"
	case LookalikePlatform_ALL:
		return "LookalikePlatform_ALL"
	}
	return "<UNSET>"
}

func LookalikePlatformFromString(s string) (LookalikePlatform, error) {
	switch s {
	case "LookalikePlatform_ANDROID":
		return LookalikePlatform_ANDROID, nil
	case "LookalikePlatform_IOS":
		return LookalikePlatform_IOS, nil
	case "LookalikePlatform_ALL":
		return LookalikePlatform_ALL, nil
	}
	return LookalikePlatform(math.MinInt32 - 1), fmt.Errorf("not a valid LookalikePlatform string")
}

type ExpandPurpose int64

const (
	ExpandPurpose_COST    ExpandPurpose = 1
	ExpandPurpose_PAYMENT ExpandPurpose = 2
)

func (p ExpandPurpose) String() string {
	switch p {
	case ExpandPurpose_COST:
		return "ExpandPurpose_COST"
	case ExpandPurpose_PAYMENT:
		return "ExpandPurpose_PAYMENT"
	}
	return "<UNSET>"
}

func ExpandPurposeFromString(s string) (ExpandPurpose, error) {
	switch s {
	case "ExpandPurpose_COST":
		return ExpandPurpose_COST, nil
	case "ExpandPurpose_PAYMENT":
		return ExpandPurpose_PAYMENT, nil
	}
	return ExpandPurpose(math.MinInt32 - 1), fmt.Errorf("not a valid ExpandPurpose string")
}

type CrowdType int64

const (
	CrowdType_PEOPLECOMPUTED CrowdType = 1
	CrowdType_PEOPLEUPLOADED CrowdType = 2
)

func (p CrowdType) String() string {
	switch p {
	case CrowdType_PEOPLECOMPUTED:
		return "CrowdType_PEOPLECOMPUTED"
	case CrowdType_PEOPLEUPLOADED:
		return "CrowdType_PEOPLEUPLOADED"
	}
	return "<UNSET>"
}

func CrowdTypeFromString(s string) (CrowdType, error) {
	switch s {
	case "CrowdType_PEOPLECOMPUTED":
		return CrowdType_PEOPLECOMPUTED, nil
	case "CrowdType_PEOPLEUPLOADED":
		return CrowdType_PEOPLEUPLOADED, nil
	}
	return CrowdType(math.MinInt32 - 1), fmt.Errorf("not a valid CrowdType string")
}

type CrowdStatus int64

const (
	CrowdStatus_NEW_CREATED            CrowdStatus = 1
	CrowdStatus_RUNNING                CrowdStatus = 2
	CrowdStatus_SUCESS                 CrowdStatus = 3
	CrowdStatus_FAILURE                CrowdStatus = 4
	CrowdStatus_COMMITED               CrowdStatus = 5
	CrowdStatus_DELAY                  CrowdStatus = 6
	CrowdStatus_WITHDRAWED             CrowdStatus = 7
	CrowdStatus_CROWD_TASK_RUNNING     CrowdStatus = 21
	CrowdStatus_CROWD_TASK_FINISHED    CrowdStatus = 22
	CrowdStatus_CROWD_TASK_AGGREGATION CrowdStatus = 23
	CrowdStatus_STOPED                 CrowdStatus = 101
)

func (p CrowdStatus) String() string {
	switch p {
	case CrowdStatus_NEW_CREATED:
		return "CrowdStatus_NEW_CREATED"
	case CrowdStatus_RUNNING:
		return "CrowdStatus_RUNNING"
	case CrowdStatus_SUCESS:
		return "CrowdStatus_SUCESS"
	case CrowdStatus_FAILURE:
		return "CrowdStatus_FAILURE"
	case CrowdStatus_COMMITED:
		return "CrowdStatus_COMMITED"
	case CrowdStatus_DELAY:
		return "CrowdStatus_DELAY"
	case CrowdStatus_WITHDRAWED:
		return "CrowdStatus_WITHDRAWED"
	case CrowdStatus_CROWD_TASK_RUNNING:
		return "CrowdStatus_CROWD_TASK_RUNNING"
	case CrowdStatus_CROWD_TASK_FINISHED:
		return "CrowdStatus_CROWD_TASK_FINISHED"
	case CrowdStatus_CROWD_TASK_AGGREGATION:
		return "CrowdStatus_CROWD_TASK_AGGREGATION"
	case CrowdStatus_STOPED:
		return "CrowdStatus_STOPED"
	}
	return "<UNSET>"
}

func CrowdStatusFromString(s string) (CrowdStatus, error) {
	switch s {
	case "CrowdStatus_NEW_CREATED":
		return CrowdStatus_NEW_CREATED, nil
	case "CrowdStatus_RUNNING":
		return CrowdStatus_RUNNING, nil
	case "CrowdStatus_SUCESS":
		return CrowdStatus_SUCESS, nil
	case "CrowdStatus_FAILURE":
		return CrowdStatus_FAILURE, nil
	case "CrowdStatus_COMMITED":
		return CrowdStatus_COMMITED, nil
	case "CrowdStatus_DELAY":
		return CrowdStatus_DELAY, nil
	case "CrowdStatus_WITHDRAWED":
		return CrowdStatus_WITHDRAWED, nil
	case "CrowdStatus_CROWD_TASK_RUNNING":
		return CrowdStatus_CROWD_TASK_RUNNING, nil
	case "CrowdStatus_CROWD_TASK_FINISHED":
		return CrowdStatus_CROWD_TASK_FINISHED, nil
	case "CrowdStatus_CROWD_TASK_AGGREGATION":
		return CrowdStatus_CROWD_TASK_AGGREGATION, nil
	case "CrowdStatus_STOPED":
		return CrowdStatus_STOPED, nil
	}
	return CrowdStatus(math.MinInt32 - 1), fmt.Errorf("not a valid CrowdStatus string")
}

type CrowdIdType int64

const (
	CrowdIdType_MIXED     CrowdIdType = 0
	CrowdIdType_IMEI      CrowdIdType = 1
	CrowdIdType_IDFA      CrowdIdType = 2
	CrowdIdType_AAID      CrowdIdType = 3
	CrowdIdType_MAC       CrowdIdType = 4
	CrowdIdType_USERIDMD5 CrowdIdType = 5
)

func (p CrowdIdType) String() string {
	switch p {
	case CrowdIdType_MIXED:
		return "CrowdIdType_MIXED"
	case CrowdIdType_IMEI:
		return "CrowdIdType_IMEI"
	case CrowdIdType_IDFA:
		return "CrowdIdType_IDFA"
	case CrowdIdType_AAID:
		return "CrowdIdType_AAID"
	case CrowdIdType_MAC:
		return "CrowdIdType_MAC"
	case CrowdIdType_USERIDMD5:
		return "CrowdIdType_USERIDMD5"
	}
	return "<UNSET>"
}

func CrowdIdTypeFromString(s string) (CrowdIdType, error) {
	switch s {
	case "CrowdIdType_MIXED":
		return CrowdIdType_MIXED, nil
	case "CrowdIdType_IMEI":
		return CrowdIdType_IMEI, nil
	case "CrowdIdType_IDFA":
		return CrowdIdType_IDFA, nil
	case "CrowdIdType_AAID":
		return CrowdIdType_AAID, nil
	case "CrowdIdType_MAC":
		return CrowdIdType_MAC, nil
	case "CrowdIdType_USERIDMD5":
		return CrowdIdType_USERIDMD5, nil
	}
	return CrowdIdType(math.MinInt32 - 1), fmt.Errorf("not a valid CrowdIdType string")
}

type FeaManagerErrorCode int64

const (
	FeaManagerErrorCode_SUCC                   FeaManagerErrorCode = 0
	FeaManagerErrorCode_NODATASOURCEEXIST      FeaManagerErrorCode = 101
	FeaManagerErrorCode_DATASOURCEEXIST        FeaManagerErrorCode = 102
	FeaManagerErrorCode_NOVALIDPRIMARYKEYEXIST FeaManagerErrorCode = 103
	FeaManagerErrorCode_NOTASKSTATUSEXIST      FeaManagerErrorCode = 201
	FeaManagerErrorCode_TASKSTATUSEXIST        FeaManagerErrorCode = 202
	FeaManagerErrorCode_NOTASKEXIST            FeaManagerErrorCode = 301
	FeaManagerErrorCode_TASKEXIST              FeaManagerErrorCode = 302
	FeaManagerErrorCode_NULLCROWDTASK          FeaManagerErrorCode = 303
	FeaManagerErrorCode_NULLCROWDIDS           FeaManagerErrorCode = 304
	FeaManagerErrorCode_NOCOMMITEDCROWDTASK    FeaManagerErrorCode = 305
	FeaManagerErrorCode_NOCROWDPARAMETER       FeaManagerErrorCode = 306
	FeaManagerErrorCode_TASKLOCKED             FeaManagerErrorCode = 307
	FeaManagerErrorCode_NOCROWDEXIST           FeaManagerErrorCode = 401
	FeaManagerErrorCode_CROWDEXIST             FeaManagerErrorCode = 402
	FeaManagerErrorCode_ERRORINPUT             FeaManagerErrorCode = 501
	FeaManagerErrorCode_DB_ERROR               FeaManagerErrorCode = 1001
	FeaManagerErrorCode_ERROR                  FeaManagerErrorCode = 1002
	FeaManagerErrorCode_MAX_ERRCODE            FeaManagerErrorCode = 10000
)

func (p FeaManagerErrorCode) String() string {
	switch p {
	case FeaManagerErrorCode_SUCC:
		return "FeaManagerErrorCode_SUCC"
	case FeaManagerErrorCode_NODATASOURCEEXIST:
		return "FeaManagerErrorCode_NODATASOURCEEXIST"
	case FeaManagerErrorCode_DATASOURCEEXIST:
		return "FeaManagerErrorCode_DATASOURCEEXIST"
	case FeaManagerErrorCode_NOVALIDPRIMARYKEYEXIST:
		return "FeaManagerErrorCode_NOVALIDPRIMARYKEYEXIST"
	case FeaManagerErrorCode_NOTASKSTATUSEXIST:
		return "FeaManagerErrorCode_NOTASKSTATUSEXIST"
	case FeaManagerErrorCode_TASKSTATUSEXIST:
		return "FeaManagerErrorCode_TASKSTATUSEXIST"
	case FeaManagerErrorCode_NOTASKEXIST:
		return "FeaManagerErrorCode_NOTASKEXIST"
	case FeaManagerErrorCode_TASKEXIST:
		return "FeaManagerErrorCode_TASKEXIST"
	case FeaManagerErrorCode_NULLCROWDTASK:
		return "FeaManagerErrorCode_NULLCROWDTASK"
	case FeaManagerErrorCode_NULLCROWDIDS:
		return "FeaManagerErrorCode_NULLCROWDIDS"
	case FeaManagerErrorCode_NOCOMMITEDCROWDTASK:
		return "FeaManagerErrorCode_NOCOMMITEDCROWDTASK"
	case FeaManagerErrorCode_NOCROWDPARAMETER:
		return "FeaManagerErrorCode_NOCROWDPARAMETER"
	case FeaManagerErrorCode_TASKLOCKED:
		return "FeaManagerErrorCode_TASKLOCKED"
	case FeaManagerErrorCode_NOCROWDEXIST:
		return "FeaManagerErrorCode_NOCROWDEXIST"
	case FeaManagerErrorCode_CROWDEXIST:
		return "FeaManagerErrorCode_CROWDEXIST"
	case FeaManagerErrorCode_ERRORINPUT:
		return "FeaManagerErrorCode_ERRORINPUT"
	case FeaManagerErrorCode_DB_ERROR:
		return "FeaManagerErrorCode_DB_ERROR"
	case FeaManagerErrorCode_ERROR:
		return "FeaManagerErrorCode_ERROR"
	case FeaManagerErrorCode_MAX_ERRCODE:
		return "FeaManagerErrorCode_MAX_ERRCODE"
	}
	return "<UNSET>"
}

func FeaManagerErrorCodeFromString(s string) (FeaManagerErrorCode, error) {
	switch s {
	case "FeaManagerErrorCode_SUCC":
		return FeaManagerErrorCode_SUCC, nil
	case "FeaManagerErrorCode_NODATASOURCEEXIST":
		return FeaManagerErrorCode_NODATASOURCEEXIST, nil
	case "FeaManagerErrorCode_DATASOURCEEXIST":
		return FeaManagerErrorCode_DATASOURCEEXIST, nil
	case "FeaManagerErrorCode_NOVALIDPRIMARYKEYEXIST":
		return FeaManagerErrorCode_NOVALIDPRIMARYKEYEXIST, nil
	case "FeaManagerErrorCode_NOTASKSTATUSEXIST":
		return FeaManagerErrorCode_NOTASKSTATUSEXIST, nil
	case "FeaManagerErrorCode_TASKSTATUSEXIST":
		return FeaManagerErrorCode_TASKSTATUSEXIST, nil
	case "FeaManagerErrorCode_NOTASKEXIST":
		return FeaManagerErrorCode_NOTASKEXIST, nil
	case "FeaManagerErrorCode_TASKEXIST":
		return FeaManagerErrorCode_TASKEXIST, nil
	case "FeaManagerErrorCode_NULLCROWDTASK":
		return FeaManagerErrorCode_NULLCROWDTASK, nil
	case "FeaManagerErrorCode_NULLCROWDIDS":
		return FeaManagerErrorCode_NULLCROWDIDS, nil
	case "FeaManagerErrorCode_NOCOMMITEDCROWDTASK":
		return FeaManagerErrorCode_NOCOMMITEDCROWDTASK, nil
	case "FeaManagerErrorCode_NOCROWDPARAMETER":
		return FeaManagerErrorCode_NOCROWDPARAMETER, nil
	case "FeaManagerErrorCode_TASKLOCKED":
		return FeaManagerErrorCode_TASKLOCKED, nil
	case "FeaManagerErrorCode_NOCROWDEXIST":
		return FeaManagerErrorCode_NOCROWDEXIST, nil
	case "FeaManagerErrorCode_CROWDEXIST":
		return FeaManagerErrorCode_CROWDEXIST, nil
	case "FeaManagerErrorCode_ERRORINPUT":
		return FeaManagerErrorCode_ERRORINPUT, nil
	case "FeaManagerErrorCode_DB_ERROR":
		return FeaManagerErrorCode_DB_ERROR, nil
	case "FeaManagerErrorCode_ERROR":
		return FeaManagerErrorCode_ERROR, nil
	case "FeaManagerErrorCode_MAX_ERRCODE":
		return FeaManagerErrorCode_MAX_ERRCODE, nil
	}
	return FeaManagerErrorCode(math.MinInt32 - 1), fmt.Errorf("not a valid FeaManagerErrorCode string")
}

type BidWordTargetStatus int64

const (
	BidWordTargetStatus_NEW_CREATED BidWordTargetStatus = 1
	BidWordTargetStatus_RUNNING     BidWordTargetStatus = 2
)

func (p BidWordTargetStatus) String() string {
	switch p {
	case BidWordTargetStatus_NEW_CREATED:
		return "BidWordTargetStatus_NEW_CREATED"
	case BidWordTargetStatus_RUNNING:
		return "BidWordTargetStatus_RUNNING"
	}
	return "<UNSET>"
}

func BidWordTargetStatusFromString(s string) (BidWordTargetStatus, error) {
	switch s {
	case "BidWordTargetStatus_NEW_CREATED":
		return BidWordTargetStatus_NEW_CREATED, nil
	case "BidWordTargetStatus_RUNNING":
		return BidWordTargetStatus_RUNNING, nil
	}
	return BidWordTargetStatus(math.MinInt32 - 1), fmt.Errorf("not a valid BidWordTargetStatus string")
}

type RequestHeader struct {
	Client  string `thrift:"client,1" json:"client"`
	TrackId string `thrift:"track_id,2" json:"track_id"`
}

func NewRequestHeader() *RequestHeader {
	return &RequestHeader{}
}

func (p *RequestHeader) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Client = v
	}
	return nil
}

func (p *RequestHeader) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TrackId = v
	}
	return nil
}

func (p *RequestHeader) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RequestHeader"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RequestHeader) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:client: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Client)); err != nil {
		return fmt.Errorf("%T.client (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:client: %s", p, err)
	}
	return err
}

func (p *RequestHeader) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("track_id", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:track_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TrackId)); err != nil {
		return fmt.Errorf("%T.track_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:track_id: %s", p, err)
	}
	return err
}

func (p *RequestHeader) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RequestHeader(%+v)", *p)
}

type DmpTag struct {
	TagId          int64           `thrift:"tag_id,1" json:"tag_id"`
	TagName        string          `thrift:"tag_name,2" json:"tag_name"`
	ParentId       int32           `thrift:"parent_id,3" json:"parent_id"`
	ParentName     string          `thrift:"parent_name,4" json:"parent_name"`
	TagDescription string          `thrift:"tag_description,5" json:"tag_description"`
	TagValue       []string        `thrift:"tag_value,6" json:"tag_value"`
	At             AggregationType `thrift:"at,7" json:"at"`
	IosUv          int32           `thrift:"ios_uv,8" json:"ios_uv"`
	AndroidUv      int32           `thrift:"android_uv,9" json:"android_uv"`
}

func NewDmpTag() *DmpTag {
	return &DmpTag{
		At: math.MinInt32 - 1, // unset sentinal value

		IosUv: 0,

		AndroidUv: 0,
	}
}

func (p *DmpTag) IsSetAt() bool {
	return int64(p.At) != math.MinInt32-1
}

func (p *DmpTag) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpTag) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TagId = v
	}
	return nil
}

func (p *DmpTag) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TagName = v
	}
	return nil
}

func (p *DmpTag) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ParentId = v
	}
	return nil
}

func (p *DmpTag) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ParentName = v
	}
	return nil
}

func (p *DmpTag) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TagDescription = v
	}
	return nil
}

func (p *DmpTag) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TagValue = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.TagValue = append(p.TagValue, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DmpTag) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.At = AggregationType(v)
	}
	return nil
}

func (p *DmpTag) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.IosUv = v
	}
	return nil
}

func (p *DmpTag) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AndroidUv = v
	}
	return nil
}

func (p *DmpTag) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpTag"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpTag) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:tag_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TagId)); err != nil {
		return fmt.Errorf("%T.tag_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:tag_id: %s", p, err)
	}
	return err
}

func (p *DmpTag) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tag_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagName)); err != nil {
		return fmt.Errorf("%T.tag_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tag_name: %s", p, err)
	}
	return err
}

func (p *DmpTag) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:parent_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ParentId)); err != nil {
		return fmt.Errorf("%T.parent_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:parent_id: %s", p, err)
	}
	return err
}

func (p *DmpTag) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("parent_name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:parent_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ParentName)); err != nil {
		return fmt.Errorf("%T.parent_name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:parent_name: %s", p, err)
	}
	return err
}

func (p *DmpTag) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag_description", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:tag_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TagDescription)); err != nil {
		return fmt.Errorf("%T.tag_description (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:tag_description: %s", p, err)
	}
	return err
}

func (p *DmpTag) writeField6(oprot thrift.TProtocol) (err error) {
	if p.TagValue != nil {
		if err := oprot.WriteFieldBegin("tag_value", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:tag_value: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.TagValue)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TagValue {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:tag_value: %s", p, err)
		}
	}
	return err
}

func (p *DmpTag) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetAt() {
		if err := oprot.WriteFieldBegin("at", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:at: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.At)); err != nil {
			return fmt.Errorf("%T.at (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:at: %s", p, err)
		}
	}
	return err
}

func (p *DmpTag) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_uv", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ios_uv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosUv)); err != nil {
		return fmt.Errorf("%T.ios_uv (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ios_uv: %s", p, err)
	}
	return err
}

func (p *DmpTag) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_uv", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:android_uv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidUv)); err != nil {
		return fmt.Errorf("%T.android_uv (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:android_uv: %s", p, err)
	}
	return err
}

func (p *DmpTag) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpTag(%+v)", *p)
}

type DataAbility struct {
	Name          string `thrift:"name,1" json:"name"`
	DataAbilityId int64  `thrift:"data_ability_id,2" json:"data_ability_id"`
	Description   string `thrift:"description,3" json:"description"`
	Version       string `thrift:"version,4" json:"version"`
	ResultSchema  string `thrift:"result_schema,5" json:"result_schema"`
}

func NewDataAbility() *DataAbility {
	return &DataAbility{}
}

func (p *DataAbility) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DataAbility) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DataAbility) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DataAbilityId = v
	}
	return nil
}

func (p *DataAbility) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *DataAbility) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *DataAbility) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ResultSchema = v
	}
	return nil
}

func (p *DataAbility) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DataAbility"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DataAbility) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *DataAbility) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_ability_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:data_ability_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DataAbilityId)); err != nil {
		return fmt.Errorf("%T.data_ability_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:data_ability_id: %s", p, err)
	}
	return err
}

func (p *DataAbility) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:description: %s", p, err)
	}
	return err
}

func (p *DataAbility) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:version: %s", p, err)
	}
	return err
}

func (p *DataAbility) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result_schema", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:result_schema: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResultSchema)); err != nil {
		return fmt.Errorf("%T.result_schema (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:result_schema: %s", p, err)
	}
	return err
}

func (p *DataAbility) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataAbility(%+v)", *p)
}

type DataSource struct {
	Name             string            `thrift:"name,1" json:"name"`
	DataSourceId     int64             `thrift:"data_source_id,2" json:"data_source_id"`
	Description      string            `thrift:"description,3" json:"description"`
	Version          string            `thrift:"version,4" json:"version"`
	UvDaily          int32             `thrift:"uv_daily,5" json:"uv_daily"`
	DataSourceStatus DataSourceStatus  `thrift:"data_source_status,6" json:"data_source_status"`
	DataAbility      []*DataAbility    `thrift:"data_ability,7" json:"data_ability"`
	DataSourceType   DataSourceType    `thrift:"data_source_type,8" json:"data_source_type"`
	Tags             []*DmpTag         `thrift:"tags,9" json:"tags"`
	DataBusinessInfo map[string]string `thrift:"data_business_info,10" json:"data_business_info"`
}

func NewDataSource() *DataSource {
	return &DataSource{
		DataSourceStatus: math.MinInt32 - 1, // unset sentinal value

		DataSourceType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DataSource) IsSetDataSourceStatus() bool {
	return int64(p.DataSourceStatus) != math.MinInt32-1
}

func (p *DataSource) IsSetDataSourceType() bool {
	return int64(p.DataSourceType) != math.MinInt32-1
}

func (p *DataSource) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.MAP {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DataSource) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DataSource) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DataSourceId = v
	}
	return nil
}

func (p *DataSource) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *DataSource) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *DataSource) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UvDaily = v
	}
	return nil
}

func (p *DataSource) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DataSourceStatus = DataSourceStatus(v)
	}
	return nil
}

func (p *DataSource) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DataAbility = make([]*DataAbility, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewDataAbility()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.DataAbility = append(p.DataAbility, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DataSource) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DataSourceType = DataSourceType(v)
	}
	return nil
}

func (p *DataSource) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tags = make([]*DmpTag, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewDmpTag()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.Tags = append(p.Tags, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DataSource) readField10(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.DataBusinessInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key3 = v
		}
		var _val4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val4 = v
		}
		p.DataBusinessInfo[_key3] = _val4
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *DataSource) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DataSource"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DataSource) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *DataSource) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_source_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:data_source_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DataSourceId)); err != nil {
		return fmt.Errorf("%T.data_source_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:data_source_id: %s", p, err)
	}
	return err
}

func (p *DataSource) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:description: %s", p, err)
	}
	return err
}

func (p *DataSource) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:version: %s", p, err)
	}
	return err
}

func (p *DataSource) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uv_daily", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:uv_daily: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UvDaily)); err != nil {
		return fmt.Errorf("%T.uv_daily (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:uv_daily: %s", p, err)
	}
	return err
}

func (p *DataSource) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataSourceStatus() {
		if err := oprot.WriteFieldBegin("data_source_status", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:data_source_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DataSourceStatus)); err != nil {
			return fmt.Errorf("%T.data_source_status (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:data_source_status: %s", p, err)
		}
	}
	return err
}

func (p *DataSource) writeField7(oprot thrift.TProtocol) (err error) {
	if p.DataAbility != nil {
		if err := oprot.WriteFieldBegin("data_ability", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:data_ability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.DataAbility)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DataAbility {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:data_ability: %s", p, err)
		}
	}
	return err
}

func (p *DataSource) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetDataSourceType() {
		if err := oprot.WriteFieldBegin("data_source_type", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:data_source_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.DataSourceType)); err != nil {
			return fmt.Errorf("%T.data_source_type (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:data_source_type: %s", p, err)
		}
	}
	return err
}

func (p *DataSource) writeField9(oprot thrift.TProtocol) (err error) {
	if p.Tags != nil {
		if err := oprot.WriteFieldBegin("tags", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:tags: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tags)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tags {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:tags: %s", p, err)
		}
	}
	return err
}

func (p *DataSource) writeField10(oprot thrift.TProtocol) (err error) {
	if p.DataBusinessInfo != nil {
		if err := oprot.WriteFieldBegin("data_business_info", thrift.MAP, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:data_business_info: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.DataBusinessInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.DataBusinessInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:data_business_info: %s", p, err)
		}
	}
	return err
}

func (p *DataSource) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataSource(%+v)", *p)
}

type CrowdTask struct {
	Name               string            `thrift:"name,1" json:"name"`
	Description        string            `thrift:"description,2" json:"description"`
	TaskId             int64             `thrift:"task_id,3" json:"task_id"`
	Version            string            `thrift:"version,4" json:"version"`
	DataName           string            `thrift:"data_name,5" json:"data_name"`
	DataVersion        string            `thrift:"data_version,6" json:"data_version"`
	DataAbilityName    string            `thrift:"data_ability_name,7" json:"data_ability_name"`
	DataAbilityVersion string            `thrift:"data_ability_version,8" json:"data_ability_version"`
	TaskStatus         CrowdTaskStatus   `thrift:"task_status,9" json:"task_status"`
	HandleCount        int32             `thrift:"handle_count,10" json:"handle_count"`
	DataSpan           int32             `thrift:"data_span,11" json:"data_span"`
	SelectParameters   []*DmpTag         `thrift:"select_parameters,12" json:"select_parameters"`
	ResultSchema       string            `thrift:"result_schema,13" json:"result_schema"`
	DataSpanBeg        int32             `thrift:"data_span_beg,14" json:"data_span_beg"`
	ComType            ComputationType   `thrift:"com_type,15" json:"com_type"`
	CtType             CrowdTaskType     `thrift:"ct_type,16" json:"ct_type"`
	SeedPath           []string          `thrift:"seed_path,17" json:"seed_path"`
	SeedCrowd          []int64           `thrift:"seed_crowd,18" json:"seed_crowd"`
	LookalikePlatform  LookalikePlatform `thrift:"lookalike_platform,19" json:"lookalike_platform"`
	ExpandPurpose      ExpandPurpose     `thrift:"expand_purpose,20" json:"expand_purpose"`
	ExpandNumber       int32             `thrift:"expand_number,21" json:"expand_number"`
	KeepSeed           bool              `thrift:"keep_seed,22" json:"keep_seed"`
}

func NewCrowdTask() *CrowdTask {
	return &CrowdTask{
		TaskStatus: math.MinInt32 - 1, // unset sentinal value

		ComType: math.MinInt32 - 1, // unset sentinal value

		CtType: 1,

		LookalikePlatform: math.MinInt32 - 1, // unset sentinal value

		ExpandPurpose: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CrowdTask) IsSetTaskStatus() bool {
	return int64(p.TaskStatus) != math.MinInt32-1
}

func (p *CrowdTask) IsSetComType() bool {
	return int64(p.ComType) != math.MinInt32-1
}

func (p *CrowdTask) IsSetCtType() bool {
	return int64(p.CtType) != math.MinInt32-1
}

func (p *CrowdTask) IsSetLookalikePlatform() bool {
	return int64(p.LookalikePlatform) != math.MinInt32-1
}

func (p *CrowdTask) IsSetExpandPurpose() bool {
	return int64(p.ExpandPurpose) != math.MinInt32-1
}

func (p *CrowdTask) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.LIST {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdTask) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CrowdTask) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *CrowdTask) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *CrowdTask) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *CrowdTask) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DataName = v
	}
	return nil
}

func (p *CrowdTask) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DataVersion = v
	}
	return nil
}

func (p *CrowdTask) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.DataAbilityName = v
	}
	return nil
}

func (p *CrowdTask) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DataAbilityVersion = v
	}
	return nil
}

func (p *CrowdTask) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.TaskStatus = CrowdTaskStatus(v)
	}
	return nil
}

func (p *CrowdTask) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.HandleCount = v
	}
	return nil
}

func (p *CrowdTask) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DataSpan = v
	}
	return nil
}

func (p *CrowdTask) readField12(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SelectParameters = make([]*DmpTag, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewDmpTag()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.SelectParameters = append(p.SelectParameters, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdTask) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ResultSchema = v
	}
	return nil
}

func (p *CrowdTask) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DataSpanBeg = v
	}
	return nil
}

func (p *CrowdTask) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ComType = ComputationType(v)
	}
	return nil
}

func (p *CrowdTask) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.CtType = CrowdTaskType(v)
	}
	return nil
}

func (p *CrowdTask) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SeedPath = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem6 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem6 = v
		}
		p.SeedPath = append(p.SeedPath, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdTask) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SeedCrowd = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem7 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem7 = v
		}
		p.SeedCrowd = append(p.SeedCrowd, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdTask) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.LookalikePlatform = LookalikePlatform(v)
	}
	return nil
}

func (p *CrowdTask) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.ExpandPurpose = ExpandPurpose(v)
	}
	return nil
}

func (p *CrowdTask) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.ExpandNumber = v
	}
	return nil
}

func (p *CrowdTask) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.KeepSeed = v
	}
	return nil
}

func (p *CrowdTask) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdTask"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:description: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:task_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:task_id: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:version: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:data_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataName)); err != nil {
		return fmt.Errorf("%T.data_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:data_name: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_version", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:data_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataVersion)); err != nil {
		return fmt.Errorf("%T.data_version (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:data_version: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_ability_name", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:data_ability_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataAbilityName)); err != nil {
		return fmt.Errorf("%T.data_ability_name (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:data_ability_name: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_ability_version", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:data_ability_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DataAbilityVersion)); err != nil {
		return fmt.Errorf("%T.data_ability_version (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:data_ability_version: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskStatus() {
		if err := oprot.WriteFieldBegin("task_status", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:task_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
			return fmt.Errorf("%T.task_status (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:task_status: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("handle_count", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:handle_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.HandleCount)); err != nil {
		return fmt.Errorf("%T.handle_count (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:handle_count: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_span", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:data_span: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DataSpan)); err != nil {
		return fmt.Errorf("%T.data_span (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:data_span: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField12(oprot thrift.TProtocol) (err error) {
	if p.SelectParameters != nil {
		if err := oprot.WriteFieldBegin("select_parameters", thrift.LIST, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:select_parameters: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SelectParameters)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SelectParameters {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:select_parameters: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result_schema", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:result_schema: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResultSchema)); err != nil {
		return fmt.Errorf("%T.result_schema (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:result_schema: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_span_beg", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:data_span_beg: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DataSpanBeg)); err != nil {
		return fmt.Errorf("%T.data_span_beg (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:data_span_beg: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField15(oprot thrift.TProtocol) (err error) {
	if p.IsSetComType() {
		if err := oprot.WriteFieldBegin("com_type", thrift.I32, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:com_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ComType)); err != nil {
			return fmt.Errorf("%T.com_type (15) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:com_type: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField16(oprot thrift.TProtocol) (err error) {
	if p.IsSetCtType() {
		if err := oprot.WriteFieldBegin("ct_type", thrift.I32, 16); err != nil {
			return fmt.Errorf("%T write field begin error 16:ct_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CtType)); err != nil {
			return fmt.Errorf("%T.ct_type (16) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 16:ct_type: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField17(oprot thrift.TProtocol) (err error) {
	if p.SeedPath != nil {
		if err := oprot.WriteFieldBegin("seed_path", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:seed_path: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.SeedPath)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SeedPath {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:seed_path: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField18(oprot thrift.TProtocol) (err error) {
	if p.SeedCrowd != nil {
		if err := oprot.WriteFieldBegin("seed_crowd", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:seed_crowd: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.SeedCrowd)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SeedCrowd {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:seed_crowd: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField19(oprot thrift.TProtocol) (err error) {
	if p.IsSetLookalikePlatform() {
		if err := oprot.WriteFieldBegin("lookalike_platform", thrift.I32, 19); err != nil {
			return fmt.Errorf("%T write field begin error 19:lookalike_platform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.LookalikePlatform)); err != nil {
			return fmt.Errorf("%T.lookalike_platform (19) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 19:lookalike_platform: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField20(oprot thrift.TProtocol) (err error) {
	if p.IsSetExpandPurpose() {
		if err := oprot.WriteFieldBegin("expand_purpose", thrift.I32, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:expand_purpose: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ExpandPurpose)); err != nil {
			return fmt.Errorf("%T.expand_purpose (20) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:expand_purpose: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTask) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("expand_number", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:expand_number: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExpandNumber)); err != nil {
		return fmt.Errorf("%T.expand_number (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:expand_number: %s", p, err)
	}
	return err
}

func (p *CrowdTask) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keep_seed", thrift.BOOL, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:keep_seed: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.KeepSeed)); err != nil {
		return fmt.Errorf("%T.keep_seed (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:keep_seed: %s", p, err)
	}
	return err
}

func (p *CrowdTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdTask(%+v)", *p)
}

type CrowdTaskProfile struct {
	Name       string          `thrift:"name,1" json:"name"`
	TaskId     int64           `thrift:"task_id,2" json:"task_id"`
	TaskStatus CrowdTaskStatus `thrift:"task_status,3" json:"task_status"`
	StatusTs   int32           `thrift:"status_ts,4" json:"status_ts"`
}

func NewCrowdTaskProfile() *CrowdTaskProfile {
	return &CrowdTaskProfile{
		TaskStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CrowdTaskProfile) IsSetTaskStatus() bool {
	return int64(p.TaskStatus) != math.MinInt32-1
}

func (p *CrowdTaskProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdTaskProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CrowdTaskProfile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *CrowdTaskProfile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TaskStatus = CrowdTaskStatus(v)
	}
	return nil
}

func (p *CrowdTaskProfile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.StatusTs = v
	}
	return nil
}

func (p *CrowdTaskProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdTaskProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdTaskProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *CrowdTaskProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:task_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:task_id: %s", p, err)
	}
	return err
}

func (p *CrowdTaskProfile) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskStatus() {
		if err := oprot.WriteFieldBegin("task_status", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:task_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
			return fmt.Errorf("%T.task_status (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:task_status: %s", p, err)
		}
	}
	return err
}

func (p *CrowdTaskProfile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status_ts", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:status_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatusTs)); err != nil {
		return fmt.Errorf("%T.status_ts (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:status_ts: %s", p, err)
	}
	return err
}

func (p *CrowdTaskProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdTaskProfile(%+v)", *p)
}

type CrowdPath struct {
	ResultPath string      `thrift:"result_path,1" json:"result_path"`
	CidType    CrowdIdType `thrift:"cid_type,2" json:"cid_type"`
}

func NewCrowdPath() *CrowdPath {
	return &CrowdPath{
		CidType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CrowdPath) IsSetCidType() bool {
	return int64(p.CidType) != math.MinInt32-1
}

func (p *CrowdPath) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdPath) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ResultPath = v
	}
	return nil
}

func (p *CrowdPath) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CidType = CrowdIdType(v)
	}
	return nil
}

func (p *CrowdPath) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdPath"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdPath) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result_path", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:result_path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResultPath)); err != nil {
		return fmt.Errorf("%T.result_path (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:result_path: %s", p, err)
	}
	return err
}

func (p *CrowdPath) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetCidType() {
		if err := oprot.WriteFieldBegin("cid_type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:cid_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CidType)); err != nil {
			return fmt.Errorf("%T.cid_type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:cid_type: %s", p, err)
		}
	}
	return err
}

func (p *CrowdPath) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdPath(%+v)", *p)
}

type Crowd struct {
	Name         string          `thrift:"name,1" json:"name"`
	Description  string          `thrift:"description,2" json:"description"`
	PeopleId     int64           `thrift:"people_id,3" json:"people_id"`
	Version      string          `thrift:"version,4" json:"version"`
	Pct          CrowdType       `thrift:"pct,5" json:"pct"`
	At           AggregationType `thrift:"at,6" json:"at"`
	Tasks        []*CrowdTask    `thrift:"tasks,7" json:"tasks"`
	CrowdPath    []*CrowdPath    `thrift:"crowd_path,8" json:"crowd_path"`
	ResultSchema string          `thrift:"result_schema,9" json:"result_schema"`
	DataSpan     int32           `thrift:"data_span,10" json:"data_span"`
	CrowdStatus  CrowdStatus     `thrift:"crowd_status,11" json:"crowd_status"`
	ComType      ComputationType `thrift:"com_type,12" json:"com_type"`
	DataSpanBeg  int32           `thrift:"data_span_beg,13" json:"data_span_beg"`
}

func NewCrowd() *Crowd {
	return &Crowd{
		Pct: math.MinInt32 - 1, // unset sentinal value

		At: math.MinInt32 - 1, // unset sentinal value

		CrowdStatus: math.MinInt32 - 1, // unset sentinal value

		ComType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Crowd) IsSetPct() bool {
	return int64(p.Pct) != math.MinInt32-1
}

func (p *Crowd) IsSetAt() bool {
	return int64(p.At) != math.MinInt32-1
}

func (p *Crowd) IsSetCrowdStatus() bool {
	return int64(p.CrowdStatus) != math.MinInt32-1
}

func (p *Crowd) IsSetComType() bool {
	return int64(p.ComType) != math.MinInt32-1
}

func (p *Crowd) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Crowd) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *Crowd) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *Crowd) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PeopleId = v
	}
	return nil
}

func (p *Crowd) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *Crowd) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Pct = CrowdType(v)
	}
	return nil
}

func (p *Crowd) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.At = AggregationType(v)
	}
	return nil
}

func (p *Crowd) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tasks = make([]*CrowdTask, 0, size)
	for i := 0; i < size; i++ {
		_elem8 := NewCrowdTask()
		if err := _elem8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem8)
		}
		p.Tasks = append(p.Tasks, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Crowd) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CrowdPath = make([]*CrowdPath, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewCrowdPath()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.CrowdPath = append(p.CrowdPath, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Crowd) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ResultSchema = v
	}
	return nil
}

func (p *Crowd) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.DataSpan = v
	}
	return nil
}

func (p *Crowd) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.CrowdStatus = CrowdStatus(v)
	}
	return nil
}

func (p *Crowd) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ComType = ComputationType(v)
	}
	return nil
}

func (p *Crowd) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.DataSpanBeg = v
	}
	return nil
}

func (p *Crowd) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Crowd"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Crowd) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:description: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("people_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:people_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PeopleId)); err != nil {
		return fmt.Errorf("%T.people_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:people_id: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:version: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetPct() {
		if err := oprot.WriteFieldBegin("pct", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:pct: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Pct)); err != nil {
			return fmt.Errorf("%T.pct (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:pct: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAt() {
		if err := oprot.WriteFieldBegin("at", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:at: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.At)); err != nil {
			return fmt.Errorf("%T.at (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:at: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Tasks != nil {
		if err := oprot.WriteFieldBegin("tasks", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:tasks: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tasks {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:tasks: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField8(oprot thrift.TProtocol) (err error) {
	if p.CrowdPath != nil {
		if err := oprot.WriteFieldBegin("crowd_path", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:crowd_path: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CrowdPath)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CrowdPath {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:crowd_path: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result_schema", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:result_schema: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResultSchema)); err != nil {
		return fmt.Errorf("%T.result_schema (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:result_schema: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_span", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:data_span: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DataSpan)); err != nil {
		return fmt.Errorf("%T.data_span (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:data_span: %s", p, err)
	}
	return err
}

func (p *Crowd) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetCrowdStatus() {
		if err := oprot.WriteFieldBegin("crowd_status", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:crowd_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CrowdStatus)); err != nil {
			return fmt.Errorf("%T.crowd_status (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:crowd_status: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetComType() {
		if err := oprot.WriteFieldBegin("com_type", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:com_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ComType)); err != nil {
			return fmt.Errorf("%T.com_type (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:com_type: %s", p, err)
		}
	}
	return err
}

func (p *Crowd) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("data_span_beg", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:data_span_beg: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DataSpanBeg)); err != nil {
		return fmt.Errorf("%T.data_span_beg (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:data_span_beg: %s", p, err)
	}
	return err
}

func (p *Crowd) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Crowd(%+v)", *p)
}

type CrowdGroup struct {
	Name         string          `thrift:"name,1" json:"name"`
	Description  string          `thrift:"description,2" json:"description"`
	PeopleId     int64           `thrift:"people_id,3" json:"people_id"`
	Version      string          `thrift:"version,4" json:"version"`
	At           AggregationType `thrift:"at,5" json:"at"`
	PeopleIds    []int64         `thrift:"people_ids,6" json:"people_ids"`
	CrowdPath    []*CrowdPath    `thrift:"crowd_path,7" json:"crowd_path"`
	ResultSchema string          `thrift:"result_schema,8" json:"result_schema"`
	CrowdStatus  CrowdStatus     `thrift:"crowd_status,9" json:"crowd_status"`
	ComType      ComputationType `thrift:"com_type,10" json:"com_type"`
}

func NewCrowdGroup() *CrowdGroup {
	return &CrowdGroup{
		At: math.MinInt32 - 1, // unset sentinal value

		CrowdStatus: math.MinInt32 - 1, // unset sentinal value

		ComType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CrowdGroup) IsSetAt() bool {
	return int64(p.At) != math.MinInt32-1
}

func (p *CrowdGroup) IsSetCrowdStatus() bool {
	return int64(p.CrowdStatus) != math.MinInt32-1
}

func (p *CrowdGroup) IsSetComType() bool {
	return int64(p.ComType) != math.MinInt32-1
}

func (p *CrowdGroup) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdGroup) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *CrowdGroup) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Description = v
	}
	return nil
}

func (p *CrowdGroup) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PeopleId = v
	}
	return nil
}

func (p *CrowdGroup) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *CrowdGroup) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.At = AggregationType(v)
	}
	return nil
}

func (p *CrowdGroup) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PeopleIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = v
		}
		p.PeopleIds = append(p.PeopleIds, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdGroup) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CrowdPath = make([]*CrowdPath, 0, size)
	for i := 0; i < size; i++ {
		_elem11 := NewCrowdPath()
		if err := _elem11.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem11)
		}
		p.CrowdPath = append(p.CrowdPath, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *CrowdGroup) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ResultSchema = v
	}
	return nil
}

func (p *CrowdGroup) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.CrowdStatus = CrowdStatus(v)
	}
	return nil
}

func (p *CrowdGroup) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.ComType = ComputationType(v)
	}
	return nil
}

func (p *CrowdGroup) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdGroup"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdGroup) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *CrowdGroup) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("description", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Description)); err != nil {
		return fmt.Errorf("%T.description (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:description: %s", p, err)
	}
	return err
}

func (p *CrowdGroup) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("people_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:people_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PeopleId)); err != nil {
		return fmt.Errorf("%T.people_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:people_id: %s", p, err)
	}
	return err
}

func (p *CrowdGroup) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:version: %s", p, err)
	}
	return err
}

func (p *CrowdGroup) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetAt() {
		if err := oprot.WriteFieldBegin("at", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:at: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.At)); err != nil {
			return fmt.Errorf("%T.at (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:at: %s", p, err)
		}
	}
	return err
}

func (p *CrowdGroup) writeField6(oprot thrift.TProtocol) (err error) {
	if p.PeopleIds != nil {
		if err := oprot.WriteFieldBegin("people_ids", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:people_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.PeopleIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PeopleIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:people_ids: %s", p, err)
		}
	}
	return err
}

func (p *CrowdGroup) writeField7(oprot thrift.TProtocol) (err error) {
	if p.CrowdPath != nil {
		if err := oprot.WriteFieldBegin("crowd_path", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:crowd_path: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CrowdPath)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CrowdPath {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:crowd_path: %s", p, err)
		}
	}
	return err
}

func (p *CrowdGroup) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("result_schema", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:result_schema: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ResultSchema)); err != nil {
		return fmt.Errorf("%T.result_schema (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:result_schema: %s", p, err)
	}
	return err
}

func (p *CrowdGroup) writeField9(oprot thrift.TProtocol) (err error) {
	if p.IsSetCrowdStatus() {
		if err := oprot.WriteFieldBegin("crowd_status", thrift.I32, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:crowd_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CrowdStatus)); err != nil {
			return fmt.Errorf("%T.crowd_status (9) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:crowd_status: %s", p, err)
		}
	}
	return err
}

func (p *CrowdGroup) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetComType() {
		if err := oprot.WriteFieldBegin("com_type", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:com_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ComType)); err != nil {
			return fmt.Errorf("%T.com_type (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:com_type: %s", p, err)
		}
	}
	return err
}

func (p *CrowdGroup) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdGroup(%+v)", *p)
}

type CrowdProfile struct {
	CrowdName      string                    `thrift:"crowd_name,1" json:"crowd_name"`
	PeopleId       int64                     `thrift:"people_id,2" json:"people_id"`
	CrowdStatus    CrowdStatus               `thrift:"crowd_status,3" json:"crowd_status"`
	Progress       int32                     `thrift:"progress,4" json:"progress"`
	TaskstatusList map[int64]CrowdTaskStatus `thrift:"taskstatus_list,5" json:"taskstatus_list"`
	StatusTs       int32                     `thrift:"status_ts,6" json:"status_ts"`
	Uv             int32                     `thrift:"uv,7" json:"uv"`
	Rate           int32                     `thrift:"rate,8" json:"rate"`
	IosUv          int32                     `thrift:"ios_uv,9" json:"ios_uv"`
	AndroidUv      int32                     `thrift:"android_uv,10" json:"android_uv"`
	Md5Uv          int32                     `thrift:"md5_uv,11" json:"md5_uv"`
}

func NewCrowdProfile() *CrowdProfile {
	return &CrowdProfile{
		CrowdStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *CrowdProfile) IsSetCrowdStatus() bool {
	return int64(p.CrowdStatus) != math.MinInt32-1
}

func (p *CrowdProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.MAP {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CrowdProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CrowdName = v
	}
	return nil
}

func (p *CrowdProfile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PeopleId = v
	}
	return nil
}

func (p *CrowdProfile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.CrowdStatus = CrowdStatus(v)
	}
	return nil
}

func (p *CrowdProfile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Progress = v
	}
	return nil
}

func (p *CrowdProfile) readField5(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.TaskstatusList = make(map[int64]CrowdTaskStatus, size)
	for i := 0; i < size; i++ {
		var _key12 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key12 = v
		}
		var _val13 CrowdTaskStatus
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val13 = CrowdTaskStatus(v)
		}
		p.TaskstatusList[_key12] = _val13
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *CrowdProfile) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.StatusTs = v
	}
	return nil
}

func (p *CrowdProfile) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Uv = v
	}
	return nil
}

func (p *CrowdProfile) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Rate = v
	}
	return nil
}

func (p *CrowdProfile) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.IosUv = v
	}
	return nil
}

func (p *CrowdProfile) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AndroidUv = v
	}
	return nil
}

func (p *CrowdProfile) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Md5Uv = v
	}
	return nil
}

func (p *CrowdProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CrowdProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CrowdProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("crowd_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:crowd_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CrowdName)); err != nil {
		return fmt.Errorf("%T.crowd_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:crowd_name: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("people_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:people_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PeopleId)); err != nil {
		return fmt.Errorf("%T.people_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:people_id: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetCrowdStatus() {
		if err := oprot.WriteFieldBegin("crowd_status", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:crowd_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.CrowdStatus)); err != nil {
			return fmt.Errorf("%T.crowd_status (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:crowd_status: %s", p, err)
		}
	}
	return err
}

func (p *CrowdProfile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("progress", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:progress: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Progress)); err != nil {
		return fmt.Errorf("%T.progress (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:progress: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField5(oprot thrift.TProtocol) (err error) {
	if p.TaskstatusList != nil {
		if err := oprot.WriteFieldBegin("taskstatus_list", thrift.MAP, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:taskstatus_list: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I64, thrift.I32, len(p.TaskstatusList)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.TaskstatusList {
			if err := oprot.WriteI64(int64(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:taskstatus_list: %s", p, err)
		}
	}
	return err
}

func (p *CrowdProfile) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status_ts", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:status_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatusTs)); err != nil {
		return fmt.Errorf("%T.status_ts (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:status_ts: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uv", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:uv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uv)); err != nil {
		return fmt.Errorf("%T.uv (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:uv: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rate", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:rate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rate)); err != nil {
		return fmt.Errorf("%T.rate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:rate: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_uv", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ios_uv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosUv)); err != nil {
		return fmt.Errorf("%T.ios_uv (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ios_uv: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_uv", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:android_uv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidUv)); err != nil {
		return fmt.Errorf("%T.android_uv (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:android_uv: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("md5_uv", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:md5_uv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Md5Uv)); err != nil {
		return fmt.Errorf("%T.md5_uv (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:md5_uv: %s", p, err)
	}
	return err
}

func (p *CrowdProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CrowdProfile(%+v)", *p)
}

type FeaManagerException struct {
	Code    FeaManagerErrorCode `thrift:"code,1" json:"code"`
	Message string              `thrift:"message,2" json:"message"`
}

func NewFeaManagerException() *FeaManagerException {
	return &FeaManagerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *FeaManagerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *FeaManagerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *FeaManagerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = FeaManagerErrorCode(v)
	}
	return nil
}

func (p *FeaManagerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *FeaManagerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("FeaManagerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *FeaManagerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *FeaManagerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *FeaManagerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("FeaManagerException(%+v)", *p)
}

type BidWordTarget struct {
	BtName      string              `thrift:"bt_name,1" json:"bt_name"`
	BtId        int64               `thrift:"bt_id,2" json:"bt_id"`
	Desc        string              `thrift:"desc,3" json:"desc"`
	BidWords    []string            `thrift:"bid_words,4" json:"bid_words"`
	PeopleId    int64               `thrift:"people_id,5" json:"people_id"`
	TaskIds     []int64             `thrift:"task_ids,6" json:"task_ids"`
	BtComStatus BidWordTargetStatus `thrift:"bt_com_status,7" json:"bt_com_status"`
	BtComTs     int32               `thrift:"bt_com_ts,8" json:"bt_com_ts"`
}

func NewBidWordTarget() *BidWordTarget {
	return &BidWordTarget{
		BtComStatus: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BidWordTarget) IsSetBtComStatus() bool {
	return int64(p.BtComStatus) != math.MinInt32-1
}

func (p *BidWordTarget) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BidWordTarget) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BtName = v
	}
	return nil
}

func (p *BidWordTarget) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.BtId = v
	}
	return nil
}

func (p *BidWordTarget) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *BidWordTarget) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.BidWords = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = v
		}
		p.BidWords = append(p.BidWords, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *BidWordTarget) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.PeopleId = v
	}
	return nil
}

func (p *BidWordTarget) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TaskIds = make([]int64, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 int64
		if v, err := iprot.ReadI64(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = v
		}
		p.TaskIds = append(p.TaskIds, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *BidWordTarget) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.BtComStatus = BidWordTargetStatus(v)
	}
	return nil
}

func (p *BidWordTarget) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.BtComTs = v
	}
	return nil
}

func (p *BidWordTarget) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BidWordTarget"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BidWordTarget) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bt_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bt_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BtName)); err != nil {
		return fmt.Errorf("%T.bt_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bt_name: %s", p, err)
	}
	return err
}

func (p *BidWordTarget) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bt_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:bt_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.BtId)); err != nil {
		return fmt.Errorf("%T.bt_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:bt_id: %s", p, err)
	}
	return err
}

func (p *BidWordTarget) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:desc: %s", p, err)
	}
	return err
}

func (p *BidWordTarget) writeField4(oprot thrift.TProtocol) (err error) {
	if p.BidWords != nil {
		if err := oprot.WriteFieldBegin("bid_words", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:bid_words: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.BidWords)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.BidWords {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:bid_words: %s", p, err)
		}
	}
	return err
}

func (p *BidWordTarget) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("people_id", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:people_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PeopleId)); err != nil {
		return fmt.Errorf("%T.people_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:people_id: %s", p, err)
	}
	return err
}

func (p *BidWordTarget) writeField6(oprot thrift.TProtocol) (err error) {
	if p.TaskIds != nil {
		if err := oprot.WriteFieldBegin("task_ids", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:task_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I64, len(p.TaskIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TaskIds {
			if err := oprot.WriteI64(int64(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:task_ids: %s", p, err)
		}
	}
	return err
}

func (p *BidWordTarget) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetBtComStatus() {
		if err := oprot.WriteFieldBegin("bt_com_status", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:bt_com_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BtComStatus)); err != nil {
			return fmt.Errorf("%T.bt_com_status (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:bt_com_status: %s", p, err)
		}
	}
	return err
}

func (p *BidWordTarget) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bt_com_ts", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:bt_com_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BtComTs)); err != nil {
		return fmt.Errorf("%T.bt_com_ts (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:bt_com_ts: %s", p, err)
	}
	return err
}

func (p *BidWordTarget) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BidWordTarget(%+v)", *p)
}

type WordProfile struct {
	BidWord    string `thrift:"BidWord,1" json:"BidWord"`
	ExchangeId int32  `thrift:"exchange_id,2" json:"exchange_id"`
	PV         int32  `thrift:"PV,3" json:"PV"`
	UV         int32  `thrift:"UV,4" json:"UV"`
	CTR        int32  `thrift:"CTR,5" json:"CTR"`
	ATR        int32  `thrift:"ATR,6" json:"ATR"`
	Ts         int32  `thrift:"ts,7" json:"ts"`
}

func NewWordProfile() *WordProfile {
	return &WordProfile{}
}

func (p *WordProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *WordProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BidWord = v
	}
	return nil
}

func (p *WordProfile) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *WordProfile) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PV = v
	}
	return nil
}

func (p *WordProfile) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.UV = v
	}
	return nil
}

func (p *WordProfile) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CTR = v
	}
	return nil
}

func (p *WordProfile) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ATR = v
	}
	return nil
}

func (p *WordProfile) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ts = v
	}
	return nil
}

func (p *WordProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("WordProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *WordProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("BidWord", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:BidWord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BidWord)); err != nil {
		return fmt.Errorf("%T.BidWord (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:BidWord: %s", p, err)
	}
	return err
}

func (p *WordProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:exchange_id: %s", p, err)
	}
	return err
}

func (p *WordProfile) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("PV", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:PV: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PV)); err != nil {
		return fmt.Errorf("%T.PV (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:PV: %s", p, err)
	}
	return err
}

func (p *WordProfile) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("UV", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:UV: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UV)); err != nil {
		return fmt.Errorf("%T.UV (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:UV: %s", p, err)
	}
	return err
}

func (p *WordProfile) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("CTR", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:CTR: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CTR)); err != nil {
		return fmt.Errorf("%T.CTR (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:CTR: %s", p, err)
	}
	return err
}

func (p *WordProfile) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ATR", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ATR: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ATR)); err != nil {
		return fmt.Errorf("%T.ATR (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ATR: %s", p, err)
	}
	return err
}

func (p *WordProfile) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ts", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ts)); err != nil {
		return fmt.Errorf("%T.ts (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ts: %s", p, err)
	}
	return err
}

func (p *WordProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("WordProfile(%+v)", *p)
}

type BtProfile struct {
	BtId      int64          `thrift:"bt_id,1" json:"bt_id"`
	WsProfile []*WordProfile `thrift:"ws_profile,2" json:"ws_profile"`
}

func NewBtProfile() *BtProfile {
	return &BtProfile{}
}

func (p *BtProfile) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BtProfile) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.BtId = v
	}
	return nil
}

func (p *BtProfile) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.WsProfile = make([]*WordProfile, 0, size)
	for i := 0; i < size; i++ {
		_elem16 := NewWordProfile()
		if err := _elem16.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem16)
		}
		p.WsProfile = append(p.WsProfile, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *BtProfile) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BtProfile"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BtProfile) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bt_id", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:bt_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.BtId)); err != nil {
		return fmt.Errorf("%T.bt_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:bt_id: %s", p, err)
	}
	return err
}

func (p *BtProfile) writeField2(oprot thrift.TProtocol) (err error) {
	if p.WsProfile != nil {
		if err := oprot.WriteFieldBegin("ws_profile", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ws_profile: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.WsProfile)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.WsProfile {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ws_profile: %s", p, err)
		}
	}
	return err
}

func (p *BtProfile) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BtProfile(%+v)", *p)
}

type DataSourceVersion struct {
	LatestVersion    int64 `thrift:"latest_version,1" json:"latest_version"`
	IsVersionUpdated bool  `thrift:"is_version_updated,2" json:"is_version_updated"`
}

func NewDataSourceVersion() *DataSourceVersion {
	return &DataSourceVersion{}
}

func (p *DataSourceVersion) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DataSourceVersion) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.LatestVersion = v
	}
	return nil
}

func (p *DataSourceVersion) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IsVersionUpdated = v
	}
	return nil
}

func (p *DataSourceVersion) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DataSourceVersion"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DataSourceVersion) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latest_version", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:latest_version: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LatestVersion)); err != nil {
		return fmt.Errorf("%T.latest_version (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:latest_version: %s", p, err)
	}
	return err
}

func (p *DataSourceVersion) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_version_updated", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:is_version_updated: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsVersionUpdated)); err != nil {
		return fmt.Errorf("%T.is_version_updated (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:is_version_updated: %s", p, err)
	}
	return err
}

func (p *DataSourceVersion) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataSourceVersion(%+v)", *p)
}
