// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package aow_ui_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/user_profile"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = user_profile.GoUnusedProtection__
var GoUnusedProtection__ int

type AowUIRequest struct {
	Ipb     string `thrift:"ipb,1" json:"ipb"`
	PkgName string `thrift:"pkg_name,2" json:"pkg_name"`
	PkgVn   string `thrift:"pkg_vn,3" json:"pkg_vn"`
	PkgVo   string `thrift:"pkg_vo,4" json:"pkg_vo"`
	AppName string `thrift:"app_name,5" json:"app_name"`
	Userid  string `thrift:"userid,6" json:"userid"`
	Orid    string `thrift:"orid,7" json:"orid"`
	// unused field # 8
	// unused field # 9
	Idv         string `thrift:"idv,10" json:"idv"`
	Timestamp   int32  `thrift:"timestamp,11" json:"timestamp"`
	Ov          string `thrift:"ov,12" json:"ov"`
	Hwmodal     string `thrift:"hwmodal,13" json:"hwmodal"`
	Carrier     string `thrift:"carrier,14" json:"carrier"`
	Network     string `thrift:"network,15" json:"network"`
	Sw          string `thrift:"sw,16" json:"sw"`
	Sh          string `thrift:"sh,17" json:"sh"`
	Sd          string `thrift:"sd,18" json:"sd"`
	So          string `thrift:"so,19" json:"so"`
	Coord       string `thrift:"coord,20" json:"coord"`
	CoordAcc    string `thrift:"coord_acc,21" json:"coord_acc"`
	CoordStatus string `thrift:"coord_status,22" json:"coord_status"`
	CoordTs     string `thrift:"coord_ts,23" json:"coord_ts"`
	Ma          string `thrift:"ma,24" json:"ma"`
	Odin        string `thrift:"odin,25" json:"odin"`
	Ama         string `thrift:"ama,26" json:"ama"`
	An          string `thrift:"an,27" json:"an"`
	// unused field # 28
	// unused field # 29
	Sv  string `thrift:"sv,30" json:"sv"`
	C   string `thrift:"c,31" json:"c"`
	Lrd int32  `thrift:"lrd,32" json:"lrd"`
	Dsv int32  `thrift:"dsv,33" json:"dsv"`
	Jsv int32  `thrift:"jsv,34" json:"jsv"`
	// unused field # 35
	Cids          []int32 `thrift:"cids,36" json:"cids"`
	Id            int32   `thrift:"id,37" json:"id"`
	ConsumePoint  int64   `thrift:"consume_point,38" json:"consume_point"`
	DconsumePoint float64 `thrift:"dconsume_point,39" json:"dconsume_point"`
	Cpuinfo       string  `thrift:"cpuinfo,40" json:"cpuinfo"`
	Siminfo       string  `thrift:"siminfo,41" json:"siminfo"`
	IsRoot        bool    `thrift:"is_root,42" json:"is_root"`
	Memoryinfo    string  `thrift:"memoryinfo,43" json:"memoryinfo"`
	Cis           string  `thrift:"cis,44" json:"cis"`
	Batteryinfo   string  `thrift:"batteryinfo,45" json:"batteryinfo"`
	BootTime      string  `thrift:"boot_time,46" json:"boot_time"`
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	Sid       string `thrift:"sid,51" json:"sid"`
	DebugMode bool   `thrift:"debug_mode,52" json:"debug_mode"`
}

func NewAowUIRequest() *AowUIRequest {
	return &AowUIRequest{}
}

func (p *AowUIRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I32 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.I32 {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.LIST {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.STRING {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.STRING {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.STRING {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowUIRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *AowUIRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *AowUIRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PkgVn = v
	}
	return nil
}

func (p *AowUIRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PkgVo = v
	}
	return nil
}

func (p *AowUIRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *AowUIRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *AowUIRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Orid = v
	}
	return nil
}

func (p *AowUIRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Idv = v
	}
	return nil
}

func (p *AowUIRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *AowUIRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ov = v
	}
	return nil
}

func (p *AowUIRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Hwmodal = v
	}
	return nil
}

func (p *AowUIRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *AowUIRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *AowUIRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Sw = v
	}
	return nil
}

func (p *AowUIRequest) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Sh = v
	}
	return nil
}

func (p *AowUIRequest) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Sd = v
	}
	return nil
}

func (p *AowUIRequest) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.So = v
	}
	return nil
}

func (p *AowUIRequest) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Coord = v
	}
	return nil
}

func (p *AowUIRequest) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CoordAcc = v
	}
	return nil
}

func (p *AowUIRequest) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.CoordStatus = v
	}
	return nil
}

func (p *AowUIRequest) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.CoordTs = v
	}
	return nil
}

func (p *AowUIRequest) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Ma = v
	}
	return nil
}

func (p *AowUIRequest) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.Odin = v
	}
	return nil
}

func (p *AowUIRequest) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Ama = v
	}
	return nil
}

func (p *AowUIRequest) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.An = v
	}
	return nil
}

func (p *AowUIRequest) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *AowUIRequest) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.C = v
	}
	return nil
}

func (p *AowUIRequest) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.Lrd = v
	}
	return nil
}

func (p *AowUIRequest) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.Dsv = v
	}
	return nil
}

func (p *AowUIRequest) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.Jsv = v
	}
	return nil
}

func (p *AowUIRequest) readField36(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Cids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Cids = append(p.Cids, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AowUIRequest) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AowUIRequest) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.ConsumePoint = v
	}
	return nil
}

func (p *AowUIRequest) readField39(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 39: %s", err)
	} else {
		p.DconsumePoint = v
	}
	return nil
}

func (p *AowUIRequest) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Cpuinfo = v
	}
	return nil
}

func (p *AowUIRequest) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Siminfo = v
	}
	return nil
}

func (p *AowUIRequest) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.IsRoot = v
	}
	return nil
}

func (p *AowUIRequest) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Memoryinfo = v
	}
	return nil
}

func (p *AowUIRequest) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.Cis = v
	}
	return nil
}

func (p *AowUIRequest) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.Batteryinfo = v
	}
	return nil
}

func (p *AowUIRequest) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.BootTime = v
	}
	return nil
}

func (p *AowUIRequest) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *AowUIRequest) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.DebugMode = v
	}
	return nil
}

func (p *AowUIRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowUIRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowUIRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ipb: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkg_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pkg_name: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_vn", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pkg_vn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgVn)); err != nil {
		return fmt.Errorf("%T.pkg_vn (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pkg_vn: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_vo", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pkg_vo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgVo)); err != nil {
		return fmt.Errorf("%T.pkg_vo (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pkg_vo: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.app_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:app_name: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:userid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:userid: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orid", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:orid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Orid)); err != nil {
		return fmt.Errorf("%T.orid (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:orid: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idv", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:idv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idv)); err != nil {
		return fmt.Errorf("%T.idv (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:idv: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:timestamp: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:timestamp: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ov", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ov: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ov)); err != nil {
		return fmt.Errorf("%T.ov (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ov: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hwmodal", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:hwmodal: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hwmodal)); err != nil {
		return fmt.Errorf("%T.hwmodal (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:hwmodal: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:carrier: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:network: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sw", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sw: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sw)); err != nil {
		return fmt.Errorf("%T.sw (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sw: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sh", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:sh: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sh)); err != nil {
		return fmt.Errorf("%T.sh (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:sh: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:sd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sd)); err != nil {
		return fmt.Errorf("%T.sd (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:sd: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("so", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:so: %s", p, err)
	}
	if err := oprot.WriteString(string(p.So)); err != nil {
		return fmt.Errorf("%T.so (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:so: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:coord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Coord)); err != nil {
		return fmt.Errorf("%T.coord (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:coord: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_acc", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:coord_acc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordAcc)); err != nil {
		return fmt.Errorf("%T.coord_acc (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:coord_acc: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_status", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:coord_status: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordStatus)); err != nil {
		return fmt.Errorf("%T.coord_status (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:coord_status: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_ts", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:coord_ts: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordTs)); err != nil {
		return fmt.Errorf("%T.coord_ts (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:coord_ts: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ma", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:ma: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ma)); err != nil {
		return fmt.Errorf("%T.ma (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:ma: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("odin", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:odin: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Odin)); err != nil {
		return fmt.Errorf("%T.odin (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:odin: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ama", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:ama: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ama)); err != nil {
		return fmt.Errorf("%T.ama (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:ama: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("an", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:an: %s", p, err)
	}
	if err := oprot.WriteString(string(p.An)); err != nil {
		return fmt.Errorf("%T.an (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:an: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:sv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:sv: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("c", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:c: %s", p, err)
	}
	if err := oprot.WriteString(string(p.C)); err != nil {
		return fmt.Errorf("%T.c (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:c: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lrd", thrift.I32, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:lrd: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Lrd)); err != nil {
		return fmt.Errorf("%T.lrd (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:lrd: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dsv", thrift.I32, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:dsv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dsv)); err != nil {
		return fmt.Errorf("%T.dsv (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:dsv: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jsv", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:jsv: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Jsv)); err != nil {
		return fmt.Errorf("%T.jsv (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:jsv: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField36(oprot thrift.TProtocol) (err error) {
	if p.Cids != nil {
		if err := oprot.WriteFieldBegin("cids", thrift.LIST, 36); err != nil {
			return fmt.Errorf("%T write field begin error 36:cids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Cids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Cids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 36:cids: %s", p, err)
		}
	}
	return err
}

func (p *AowUIRequest) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:id: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consume_point", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:consume_point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConsumePoint)); err != nil {
		return fmt.Errorf("%T.consume_point (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:consume_point: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField39(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dconsume_point", thrift.DOUBLE, 39); err != nil {
		return fmt.Errorf("%T write field begin error 39:dconsume_point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.DconsumePoint)); err != nil {
		return fmt.Errorf("%T.dconsume_point (39) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 39:dconsume_point: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpuinfo", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:cpuinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cpuinfo)); err != nil {
		return fmt.Errorf("%T.cpuinfo (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:cpuinfo: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("siminfo", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:siminfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Siminfo)); err != nil {
		return fmt.Errorf("%T.siminfo (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:siminfo: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_root", thrift.BOOL, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:is_root: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsRoot)); err != nil {
		return fmt.Errorf("%T.is_root (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:is_root: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memoryinfo", thrift.STRING, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:memoryinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Memoryinfo)); err != nil {
		return fmt.Errorf("%T.memoryinfo (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:memoryinfo: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cis", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:cis: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cis)); err != nil {
		return fmt.Errorf("%T.cis (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:cis: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("batteryinfo", thrift.STRING, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:batteryinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Batteryinfo)); err != nil {
		return fmt.Errorf("%T.batteryinfo (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:batteryinfo: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("boot_time", thrift.STRING, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:boot_time: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BootTime)); err != nil {
		return fmt.Errorf("%T.boot_time (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:boot_time: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:sid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:sid: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("debug_mode", thrift.BOOL, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:debug_mode: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.DebugMode)); err != nil {
		return fmt.Errorf("%T.debug_mode (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:debug_mode: %s", p, err)
	}
	return err
}

func (p *AowUIRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowUIRequest(%+v)", *p)
}

type AowConfig struct {
	UnitName  string `thrift:"unit_name,1" json:"unit_name"`
	PntEnable bool   `thrift:"pnt_enable,2" json:"pnt_enable"`
}

func NewAowConfig() *AowConfig {
	return &AowConfig{}
}

func (p *AowConfig) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowConfig) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.UnitName = v
	}
	return nil
}

func (p *AowConfig) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PntEnable = v
	}
	return nil
}

func (p *AowConfig) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowConfig"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("unit_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:unit_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UnitName)); err != nil {
		return fmt.Errorf("%T.unit_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:unit_name: %s", p, err)
	}
	return err
}

func (p *AowConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pnt_enable", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pnt_enable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.PntEnable)); err != nil {
		return fmt.Errorf("%T.pnt_enable (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pnt_enable: %s", p, err)
	}
	return err
}

func (p *AowConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowConfig(%+v)", *p)
}

type Task struct {
	Id                int32   `thrift:"id,1" json:"id"`
	Action            int16   `thrift:"action,2" json:"action"`
	Rate              int16   `thrift:"rate,3" json:"rate"`
	Desc              string  `thrift:"desc,4" json:"desc"`
	IsDone            bool    `thrift:"is_done,5" json:"is_done"`
	Point             int32   `thrift:"point,6" json:"point"`
	ExecutableDate    string  `thrift:"executable_date,7" json:"executable_date"`
	TimeFragment      int32   `thrift:"time_fragment,8" json:"time_fragment"`
	DisPoint          float64 `thrift:"dis_point,9" json:"dis_point"`
	Price             int32   `thrift:"price,10" json:"price"`
	StartTimeInterval int32   `thrift:"start_time_interval,11" json:"start_time_interval"`
	Duration          int32   `thrift:"duration,12" json:"duration"`
	StayTimeInterval  int32   `thrift:"stay_time_interval,13" json:"stay_time_interval"`
	MediaShare        int32   `thrift:"media_share,14" json:"media_share"`
}

func NewTask() *Task {
	return &Task{}
}

func (p *Task) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I16 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I16 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Task) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *Task) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *Task) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Rate = v
	}
	return nil
}

func (p *Task) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *Task) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IsDone = v
	}
	return nil
}

func (p *Task) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *Task) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ExecutableDate = v
	}
	return nil
}

func (p *Task) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.TimeFragment = v
	}
	return nil
}

func (p *Task) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.DisPoint = v
	}
	return nil
}

func (p *Task) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *Task) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.StartTimeInterval = v
	}
	return nil
}

func (p *Task) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Duration = v
	}
	return nil
}

func (p *Task) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.StayTimeInterval = v
	}
	return nil
}

func (p *Task) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.MediaShare = v
	}
	return nil
}

func (p *Task) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Task"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Task) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *Task) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I16, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:action: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Action)); err != nil {
		return fmt.Errorf("%T.action (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:action: %s", p, err)
	}
	return err
}

func (p *Task) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rate", thrift.I16, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:rate: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.Rate)); err != nil {
		return fmt.Errorf("%T.rate (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:rate: %s", p, err)
	}
	return err
}

func (p *Task) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:desc: %s", p, err)
	}
	return err
}

func (p *Task) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_done", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:is_done: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsDone)); err != nil {
		return fmt.Errorf("%T.is_done (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:is_done: %s", p, err)
	}
	return err
}

func (p *Task) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:point: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Point)); err != nil {
		return fmt.Errorf("%T.point (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:point: %s", p, err)
	}
	return err
}

func (p *Task) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("executable_date", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:executable_date: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExecutableDate)); err != nil {
		return fmt.Errorf("%T.executable_date (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:executable_date: %s", p, err)
	}
	return err
}

func (p *Task) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time_fragment", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:time_fragment: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TimeFragment)); err != nil {
		return fmt.Errorf("%T.time_fragment (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:time_fragment: %s", p, err)
	}
	return err
}

func (p *Task) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dis_point", thrift.DOUBLE, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:dis_point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.DisPoint)); err != nil {
		return fmt.Errorf("%T.dis_point (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:dis_point: %s", p, err)
	}
	return err
}

func (p *Task) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:price: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Price)); err != nil {
		return fmt.Errorf("%T.price (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:price: %s", p, err)
	}
	return err
}

func (p *Task) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time_interval", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:start_time_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StartTimeInterval)); err != nil {
		return fmt.Errorf("%T.start_time_interval (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:start_time_interval: %s", p, err)
	}
	return err
}

func (p *Task) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("duration", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:duration: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Duration)); err != nil {
		return fmt.Errorf("%T.duration (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:duration: %s", p, err)
	}
	return err
}

func (p *Task) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stay_time_interval", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:stay_time_interval: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StayTimeInterval)); err != nil {
		return fmt.Errorf("%T.stay_time_interval (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:stay_time_interval: %s", p, err)
	}
	return err
}

func (p *Task) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_share", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:media_share: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MediaShare)); err != nil {
		return fmt.Errorf("%T.media_share (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:media_share: %s", p, err)
	}
	return err
}

func (p *Task) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Task(%+v)", *p)
}

type AowBriefOffer struct {
	Id         int32         `thrift:"id,1" json:"id"`
	Logo       string        `thrift:"logo,2" json:"logo"`
	Texts      string        `thrift:"texts,3" json:"texts"`
	Name       string        `thrift:"name,4" json:"name"`
	Size       int64         `thrift:"size,5" json:"size"`
	Point      common.Amount `thrift:"point,6" json:"point"`
	OpenDetail bool          `thrift:"open_detail,7" json:"open_detail"`
	ActionUrl  string        `thrift:"action_url,8" json:"action_url"`
	TaskNum    int32         `thrift:"task_num,9" json:"task_num"`
	DisPoint   float64       `thrift:"dis_point,10" json:"dis_point"`
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Appid      string        `thrift:"appid,20" json:"appid"`
	Developer  string        `thrift:"developer,21" json:"developer"`
	Pkg        string        `thrift:"pkg,22" json:"pkg"`
	Ver        string        `thrift:"ver,23" json:"ver"`
	Tasks      []*Task       `thrift:"tasks,24" json:"tasks"`
	ButtonText string        `thrift:"button_text,25" json:"button_text"`
	Screenshot []string      `thrift:"screenshot,26" json:"screenshot"`
	Desc       string        `thrift:"desc,27" json:"desc"`
	TaskTag    string        `thrift:"task_tag,28" json:"task_tag"`
	Ms         common.Amount `thrift:"ms,29" json:"ms"`
	BrifeDesc  string        `thrift:"brife_desc,30" json:"brife_desc"`
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	Tr          string `thrift:"tr,40" json:"tr"`
	Display     bool   `thrift:"display,41" json:"display"`
	FilterName  string `thrift:"filter_name,42" json:"filter_name"`
	Executable  bool   `thrift:"executable,43" json:"executable"`
	Notice      string `thrift:"notice,44" json:"notice"`
	Prompt      string `thrift:"prompt,45" json:"prompt"`
	IsSuperTask bool   `thrift:"is_super_task,46" json:"is_super_task"`
	Tag         string `thrift:"tag,47" json:"tag"`
}

func NewAowBriefOffer() *AowBriefOffer {
	return &AowBriefOffer{}
}

func (p *AowBriefOffer) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.LIST {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.STRING {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.LIST {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.STRING {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 29:
			if fieldTypeId == thrift.I64 {
				if err := p.readField29(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.STRING {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.STRING {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 47:
			if fieldTypeId == thrift.STRING {
				if err := p.readField47(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowBriefOffer) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AowBriefOffer) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *AowBriefOffer) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Texts = v
	}
	return nil
}

func (p *AowBriefOffer) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AowBriefOffer) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *AowBriefOffer) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Point = common.Amount(v)
	}
	return nil
}

func (p *AowBriefOffer) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.OpenDetail = v
	}
	return nil
}

func (p *AowBriefOffer) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *AowBriefOffer) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.TaskNum = v
	}
	return nil
}

func (p *AowBriefOffer) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.DisPoint = v
	}
	return nil
}

func (p *AowBriefOffer) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowBriefOffer) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Developer = v
	}
	return nil
}

func (p *AowBriefOffer) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.Pkg = v
	}
	return nil
}

func (p *AowBriefOffer) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Ver = v
	}
	return nil
}

func (p *AowBriefOffer) readField24(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tasks = make([]*Task, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewTask()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Tasks = append(p.Tasks, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AowBriefOffer) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.ButtonText = v
	}
	return nil
}

func (p *AowBriefOffer) readField26(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Screenshot = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.Screenshot = append(p.Screenshot, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AowBriefOffer) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *AowBriefOffer) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.TaskTag = v
	}
	return nil
}

func (p *AowBriefOffer) readField29(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 29: %s", err)
	} else {
		p.Ms = common.Amount(v)
	}
	return nil
}

func (p *AowBriefOffer) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.BrifeDesc = v
	}
	return nil
}

func (p *AowBriefOffer) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Tr = v
	}
	return nil
}

func (p *AowBriefOffer) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Display = v
	}
	return nil
}

func (p *AowBriefOffer) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.FilterName = v
	}
	return nil
}

func (p *AowBriefOffer) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Executable = v
	}
	return nil
}

func (p *AowBriefOffer) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.Notice = v
	}
	return nil
}

func (p *AowBriefOffer) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.Prompt = v
	}
	return nil
}

func (p *AowBriefOffer) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.IsSuperTask = v
	}
	return nil
}

func (p *AowBriefOffer) readField47(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 47: %s", err)
	} else {
		p.Tag = v
	}
	return nil
}

func (p *AowBriefOffer) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowBriefOffer"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField29(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField47(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowBriefOffer) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:logo: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("texts", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:texts: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Texts)); err != nil {
		return fmt.Errorf("%T.texts (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:texts: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:size: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Size)); err != nil {
		return fmt.Errorf("%T.size (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:size: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:point: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("open_detail", thrift.BOOL, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:open_detail: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.OpenDetail)); err != nil {
		return fmt.Errorf("%T.open_detail (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:open_detail: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action_url", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:action_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.action_url (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:action_url: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_num", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:task_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskNum)); err != nil {
		return fmt.Errorf("%T.task_num (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:task_num: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dis_point", thrift.DOUBLE, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:dis_point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.DisPoint)); err != nil {
		return fmt.Errorf("%T.dis_point (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:dis_point: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:appid: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("developer", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:developer: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Developer)); err != nil {
		return fmt.Errorf("%T.developer (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:developer: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:pkg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Pkg)); err != nil {
		return fmt.Errorf("%T.pkg (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:pkg: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ver", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:ver: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ver)); err != nil {
		return fmt.Errorf("%T.ver (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:ver: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField24(oprot thrift.TProtocol) (err error) {
	if p.Tasks != nil {
		if err := oprot.WriteFieldBegin("tasks", thrift.LIST, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:tasks: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tasks {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:tasks: %s", p, err)
		}
	}
	return err
}

func (p *AowBriefOffer) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("button_text", thrift.STRING, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:button_text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ButtonText)); err != nil {
		return fmt.Errorf("%T.button_text (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:button_text: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField26(oprot thrift.TProtocol) (err error) {
	if p.Screenshot != nil {
		if err := oprot.WriteFieldBegin("screenshot", thrift.LIST, 26); err != nil {
			return fmt.Errorf("%T write field begin error 26:screenshot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Screenshot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Screenshot {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 26:screenshot: %s", p, err)
		}
	}
	return err
}

func (p *AowBriefOffer) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:desc: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_tag", thrift.STRING, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:task_tag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskTag)); err != nil {
		return fmt.Errorf("%T.task_tag (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:task_tag: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField29(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ms", thrift.I64, 29); err != nil {
		return fmt.Errorf("%T write field begin error 29:ms: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ms)); err != nil {
		return fmt.Errorf("%T.ms (29) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 29:ms: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brife_desc", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:brife_desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BrifeDesc)); err != nil {
		return fmt.Errorf("%T.brife_desc (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:brife_desc: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tr", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:tr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tr)); err != nil {
		return fmt.Errorf("%T.tr (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:tr: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display", thrift.BOOL, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:display: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Display)); err != nil {
		return fmt.Errorf("%T.display (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:display: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("filter_name", thrift.STRING, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:filter_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.FilterName)); err != nil {
		return fmt.Errorf("%T.filter_name (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:filter_name: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("executable", thrift.BOOL, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:executable: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Executable)); err != nil {
		return fmt.Errorf("%T.executable (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:executable: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("notice", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:notice: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Notice)); err != nil {
		return fmt.Errorf("%T.notice (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:notice: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("prompt", thrift.STRING, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:prompt: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Prompt)); err != nil {
		return fmt.Errorf("%T.prompt (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:prompt: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_super_task", thrift.BOOL, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:is_super_task: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsSuperTask)); err != nil {
		return fmt.Errorf("%T.is_super_task (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:is_super_task: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) writeField47(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tag", thrift.STRING, 47); err != nil {
		return fmt.Errorf("%T write field begin error 47:tag: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tag)); err != nil {
		return fmt.Errorf("%T.tag (47) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 47:tag: %s", p, err)
	}
	return err
}

func (p *AowBriefOffer) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowBriefOffer(%+v)", *p)
}

type AowAccumulatedBonus struct {
	ActNum            int32  `thrift:"act_num,1" json:"act_num"`
	ReopenNum         int32  `thrift:"reopen_num,2" json:"reopen_num"`
	FinishedActNum    int32  `thrift:"finished_act_num,3" json:"finished_act_num"`
	FinishedReopenNum int32  `thrift:"finished_reopen_num,4" json:"finished_reopen_num"`
	StatusCode        int32  `thrift:"status_code,5" json:"status_code"`
	Status            int32  `thrift:"status,6" json:"status"`
	Bonus             int32  `thrift:"bonus,7" json:"bonus"`
	Url               string `thrift:"url,8" json:"url"`
	BonusDescription  string `thrift:"bonus_description,9" json:"bonus_description"`
	TaskDescription   string `thrift:"task_description,10" json:"task_description"`
}

func NewAowAccumulatedBonus() *AowAccumulatedBonus {
	return &AowAccumulatedBonus{}
}

func (p *AowAccumulatedBonus) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowAccumulatedBonus) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ActNum = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ReopenNum = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.FinishedActNum = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.FinishedReopenNum = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StatusCode = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Bonus = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.BonusDescription = v
	}
	return nil
}

func (p *AowAccumulatedBonus) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.TaskDescription = v
	}
	return nil
}

func (p *AowAccumulatedBonus) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowAccumulatedBonus"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowAccumulatedBonus) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act_num", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:act_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActNum)); err != nil {
		return fmt.Errorf("%T.act_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:act_num: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("reopen_num", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:reopen_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReopenNum)); err != nil {
		return fmt.Errorf("%T.reopen_num (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:reopen_num: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("finished_act_num", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:finished_act_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FinishedActNum)); err != nil {
		return fmt.Errorf("%T.finished_act_num (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:finished_act_num: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("finished_reopen_num", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:finished_reopen_num: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FinishedReopenNum)); err != nil {
		return fmt.Errorf("%T.finished_reopen_num (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:finished_reopen_num: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status_code", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:status_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StatusCode)); err != nil {
		return fmt.Errorf("%T.status_code (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:status_code: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:status: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:bonus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Bonus)); err != nil {
		return fmt.Errorf("%T.bonus (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:bonus: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:url: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus_description", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:bonus_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BonusDescription)); err != nil {
		return fmt.Errorf("%T.bonus_description (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:bonus_description: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_description", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:task_description: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskDescription)); err != nil {
		return fmt.Errorf("%T.task_description (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:task_description: %s", p, err)
	}
	return err
}

func (p *AowAccumulatedBonus) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowAccumulatedBonus(%+v)", *p)
}

type AowUIProccessedRequest struct {
	Req      *AowUIRequest `thrift:"req,1" json:"req"`
	DeverUid int32         `thrift:"dever_uid,2" json:"dever_uid"`
	Mid      int32         `thrift:"mid,3" json:"mid"`
	Imei     string        `thrift:"imei,4" json:"imei"`
	// unused field # 5
	Uuid        string                  `thrift:"uuid,6" json:"uuid"`
	ClientIp    string                  `thrift:"client_ip,7" json:"client_ip"`
	AccessCode  common.AccessTypeCode   `thrift:"access_code,8" json:"access_code"`
	OsCode      common.OSCode           `thrift:"os_code,9" json:"os_code"`
	Geo         *user_profile.GeoResult `thrift:"geo,10" json:"geo"`
	ServerTime  common.TimeInt          `thrift:"server_time,11" json:"server_time"`
	CarrierCode common.CarrierCode      `thrift:"carrier_code,12" json:"carrier_code"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Capability []string `thrift:"capability,20" json:"capability"`
}

func NewAowUIProccessedRequest() *AowUIProccessedRequest {
	return &AowUIProccessedRequest{
		AccessCode: math.MinInt32 - 1, // unset sentinal value

		CarrierCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AowUIProccessedRequest) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *AowUIProccessedRequest) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *AowUIProccessedRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.LIST {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowUIProccessedRequest) readField1(iprot thrift.TProtocol) error {
	p.Req = NewAowUIRequest()
	if err := p.Req.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Req)
	}
	return nil
}

func (p *AowUIProccessedRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DeverUid = v
	}
	return nil
}

func (p *AowUIProccessedRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Mid = v
	}
	return nil
}

func (p *AowUIProccessedRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *AowUIProccessedRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Uuid = v
	}
	return nil
}

func (p *AowUIProccessedRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *AowUIProccessedRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AccessCode = common.AccessTypeCode(v)
	}
	return nil
}

func (p *AowUIProccessedRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.OsCode = common.OSCode(v)
	}
	return nil
}

func (p *AowUIProccessedRequest) readField10(iprot thrift.TProtocol) error {
	p.Geo = user_profile.NewGeoResult()
	if err := p.Geo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Geo)
	}
	return nil
}

func (p *AowUIProccessedRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ServerTime = common.TimeInt(v)
	}
	return nil
}

func (p *AowUIProccessedRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.CarrierCode = common.CarrierCode(v)
	}
	return nil
}

func (p *AowUIProccessedRequest) readField20(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Capability = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem3 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem3 = v
		}
		p.Capability = append(p.Capability, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AowUIProccessedRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowUIProccessedRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowUIProccessedRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Req != nil {
		if err := oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:req: %s", p, err)
		}
		if err := p.Req.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Req)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:req: %s", p, err)
		}
	}
	return err
}

func (p *AowUIProccessedRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dever_uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:dever_uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeverUid)); err != nil {
		return fmt.Errorf("%T.dever_uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:dever_uid: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:mid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Mid)); err != nil {
		return fmt.Errorf("%T.mid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:mid: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:imei: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uuid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:uuid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Uuid)); err != nil {
		return fmt.Errorf("%T.uuid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:uuid: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client_ip", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:client_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.client_ip (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:client_ip: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:access_code: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:os_code: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Geo != nil {
		if err := oprot.WriteFieldBegin("geo", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:geo: %s", p, err)
		}
		if err := p.Geo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Geo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:geo: %s", p, err)
		}
	}
	return err
}

func (p *AowUIProccessedRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("server_time", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:server_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ServerTime)); err != nil {
		return fmt.Errorf("%T.server_time (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:server_time: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:carrier_code: %s", p, err)
	}
	return err
}

func (p *AowUIProccessedRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Capability != nil {
		if err := oprot.WriteFieldBegin("capability", thrift.LIST, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:capability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Capability)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Capability {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:capability: %s", p, err)
		}
	}
	return err
}

func (p *AowUIProccessedRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowUIProccessedRequest(%+v)", *p)
}

type AowTracker struct {
	Preq      *AowUIProccessedRequest `thrift:"preq,1" json:"preq"`
	Sponsorid int32                   `thrift:"sponsorid,2" json:"sponsorid"`
	Planid    int32                   `thrift:"planid,3" json:"planid"`
	Cid       int64                   `thrift:"cid,4" json:"cid"`
	Point     common.Amount           `thrift:"point,5" json:"point"`
	Price     common.Amount           `thrift:"price,6" json:"price"`
	Ms        common.Amount           `thrift:"ms,7" json:"ms"`
	SpPrice   common.Amount           `thrift:"sp_price,8" json:"sp_price"`
	Rank      int32                   `thrift:"rank,9" json:"rank"`
	Action    int32                   `thrift:"action,10" json:"action"`
	ExpId     int32                   `thrift:"exp_id,11" json:"exp_id"`
	OfferType int16                   `thrift:"offer_type,12" json:"offer_type"`
	PkgName   string                  `thrift:"pkg_name,13" json:"pkg_name"`
	Appid     string                  `thrift:"appid,14" json:"appid"`
	IsVaild   bool                    `thrift:"is_vaild,15" json:"is_vaild"`
	Dpoint    float64                 `thrift:"dpoint,16" json:"dpoint"`
}

func NewAowTracker() *AowTracker {
	return &AowTracker{}
}

func (p *AowTracker) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I16 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AowTracker) readField1(iprot thrift.TProtocol) error {
	p.Preq = NewAowUIProccessedRequest()
	if err := p.Preq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Preq)
	}
	return nil
}

func (p *AowTracker) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sponsorid = v
	}
	return nil
}

func (p *AowTracker) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Planid = v
	}
	return nil
}

func (p *AowTracker) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *AowTracker) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Point = common.Amount(v)
	}
	return nil
}

func (p *AowTracker) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Price = common.Amount(v)
	}
	return nil
}

func (p *AowTracker) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Ms = common.Amount(v)
	}
	return nil
}

func (p *AowTracker) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SpPrice = common.Amount(v)
	}
	return nil
}

func (p *AowTracker) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Rank = v
	}
	return nil
}

func (p *AowTracker) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *AowTracker) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ExpId = v
	}
	return nil
}

func (p *AowTracker) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI16(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.OfferType = v
	}
	return nil
}

func (p *AowTracker) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *AowTracker) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AowTracker) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.IsVaild = v
	}
	return nil
}

func (p *AowTracker) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Dpoint = v
	}
	return nil
}

func (p *AowTracker) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AowTracker"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AowTracker) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Preq != nil {
		if err := oprot.WriteFieldBegin("preq", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:preq: %s", p, err)
		}
		if err := p.Preq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Preq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:preq: %s", p, err)
		}
	}
	return err
}

func (p *AowTracker) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sponsorid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sponsorid)); err != nil {
		return fmt.Errorf("%T.sponsorid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sponsorid: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("planid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:planid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Planid)); err != nil {
		return fmt.Errorf("%T.planid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:planid: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cid: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:point: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:point: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:price: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ms", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ms: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ms)); err != nil {
		return fmt.Errorf("%T.ms (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ms: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sp_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sp_price: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("rank", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:rank: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Rank)); err != nil {
		return fmt.Errorf("%T.rank (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:rank: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:action: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exp_id", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:exp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExpId)); err != nil {
		return fmt.Errorf("%T.exp_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:exp_id: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offer_type", thrift.I16, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:offer_type: %s", p, err)
	}
	if err := oprot.WriteI16(int16(p.OfferType)); err != nil {
		return fmt.Errorf("%T.offer_type (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:offer_type: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_name", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkg_name (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:pkg_name: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:appid: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_vaild", thrift.BOOL, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:is_vaild: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsVaild)); err != nil {
		return fmt.Errorf("%T.is_vaild (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:is_vaild: %s", p, err)
	}
	return err
}

func (p *AowTracker) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dpoint", thrift.DOUBLE, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:dpoint: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Dpoint)); err != nil {
		return fmt.Errorf("%T.dpoint (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:dpoint: %s", p, err)
	}
	return err
}

func (p *AowTracker) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AowTracker(%+v)", *p)
}
