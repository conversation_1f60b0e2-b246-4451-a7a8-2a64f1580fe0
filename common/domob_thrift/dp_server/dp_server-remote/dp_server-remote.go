// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"dp_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addApp(RequestHeader header, App app)")
	fmt.Fprintln(os.Stderr, "  bool editApp(RequestHeader header, App app)")
	fmt.Fprintln(os.<PERSON>, "  QueryResult searchAppByParams(RequestHeader header, AppParams params)")
	fmt.Fprintln(os.Stderr, "   getAppsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addPromotion(RequestHeader header, Promotion promotion)")
	fmt.Fprintln(os.Stderr, "  bool editPromotion(RequestHeader header, Promotion promotion)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchPromotionByParams(RequestHeader header, PromotionParams params)")
	fmt.Fprintln(os.Stderr, "   getPromotionsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addPromotionLink(RequestHeader header, PromotionLink link)")
	fmt.Fprintln(os.Stderr, "  bool editPromotionLink(RequestHeader header, PromotionLink link)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchPromotionLinkByParams(RequestHeader header, PromotionLinkParams params)")
	fmt.Fprintln(os.Stderr, "   getPromotionLinksByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addTask(RequestHeader header, Task task)")
	fmt.Fprintln(os.Stderr, "  bool editTask(RequestHeader header, Task task)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchTaskByParams(RequestHeader header, TaskParams params)")
	fmt.Fprintln(os.Stderr, "   getTasksByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void pauseTasksByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeTasksByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addStrategy(RequestHeader header, Strategy strategy)")
	fmt.Fprintln(os.Stderr, "  bool editStrategy(RequestHeader header, Strategy strategy)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchStrategyByParams(RequestHeader header, StrategyParams params)")
	fmt.Fprintln(os.Stderr, "   getStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void pauseStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "  bool editCreative(RequestHeader header, Creative creative)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchCreativeByParams(RequestHeader header, CreativeParams params)")
	fmt.Fprintln(os.Stderr, "   getCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void pauseCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := dp_server.NewDpServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addApp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddApp requires 2 args")
			flag.Usage()
		}
		arg146 := flag.Arg(1)
		mbTrans147 := thrift.NewTMemoryBufferLen(len(arg146))
		defer mbTrans147.Close()
		_, err148 := mbTrans147.WriteString(arg146)
		if err148 != nil {
			Usage()
			return
		}
		factory149 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt150 := factory149.GetProtocol(mbTrans147)
		argvalue0 := dp_server.NewRequestHeader()
		err151 := argvalue0.Read(jsProt150)
		if err151 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg152 := flag.Arg(2)
		mbTrans153 := thrift.NewTMemoryBufferLen(len(arg152))
		defer mbTrans153.Close()
		_, err154 := mbTrans153.WriteString(arg152)
		if err154 != nil {
			Usage()
			return
		}
		factory155 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt156 := factory155.GetProtocol(mbTrans153)
		argvalue1 := dp_server.NewApp()
		err157 := argvalue1.Read(jsProt156)
		if err157 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddApp(value0, value1))
		fmt.Print("\n")
		break
	case "editApp":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditApp requires 2 args")
			flag.Usage()
		}
		arg158 := flag.Arg(1)
		mbTrans159 := thrift.NewTMemoryBufferLen(len(arg158))
		defer mbTrans159.Close()
		_, err160 := mbTrans159.WriteString(arg158)
		if err160 != nil {
			Usage()
			return
		}
		factory161 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt162 := factory161.GetProtocol(mbTrans159)
		argvalue0 := dp_server.NewRequestHeader()
		err163 := argvalue0.Read(jsProt162)
		if err163 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg164 := flag.Arg(2)
		mbTrans165 := thrift.NewTMemoryBufferLen(len(arg164))
		defer mbTrans165.Close()
		_, err166 := mbTrans165.WriteString(arg164)
		if err166 != nil {
			Usage()
			return
		}
		factory167 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt168 := factory167.GetProtocol(mbTrans165)
		argvalue1 := dp_server.NewApp()
		err169 := argvalue1.Read(jsProt168)
		if err169 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditApp(value0, value1))
		fmt.Print("\n")
		break
	case "searchAppByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAppByParams requires 2 args")
			flag.Usage()
		}
		arg170 := flag.Arg(1)
		mbTrans171 := thrift.NewTMemoryBufferLen(len(arg170))
		defer mbTrans171.Close()
		_, err172 := mbTrans171.WriteString(arg170)
		if err172 != nil {
			Usage()
			return
		}
		factory173 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt174 := factory173.GetProtocol(mbTrans171)
		argvalue0 := dp_server.NewRequestHeader()
		err175 := argvalue0.Read(jsProt174)
		if err175 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg176 := flag.Arg(2)
		mbTrans177 := thrift.NewTMemoryBufferLen(len(arg176))
		defer mbTrans177.Close()
		_, err178 := mbTrans177.WriteString(arg176)
		if err178 != nil {
			Usage()
			return
		}
		factory179 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt180 := factory179.GetProtocol(mbTrans177)
		argvalue1 := dp_server.NewAppParams()
		err181 := argvalue1.Read(jsProt180)
		if err181 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAppByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getAppsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAppsByIds requires 2 args")
			flag.Usage()
		}
		arg182 := flag.Arg(1)
		mbTrans183 := thrift.NewTMemoryBufferLen(len(arg182))
		defer mbTrans183.Close()
		_, err184 := mbTrans183.WriteString(arg182)
		if err184 != nil {
			Usage()
			return
		}
		factory185 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt186 := factory185.GetProtocol(mbTrans183)
		argvalue0 := dp_server.NewRequestHeader()
		err187 := argvalue0.Read(jsProt186)
		if err187 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg188 := flag.Arg(2)
		mbTrans189 := thrift.NewTMemoryBufferLen(len(arg188))
		defer mbTrans189.Close()
		_, err190 := mbTrans189.WriteString(arg188)
		if err190 != nil {
			Usage()
			return
		}
		factory191 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt192 := factory191.GetProtocol(mbTrans189)
		containerStruct1 := dp_server.NewGetAppsByIdsArgs()
		err193 := containerStruct1.ReadField2(jsProt192)
		if err193 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAppsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPromotion requires 2 args")
			flag.Usage()
		}
		arg194 := flag.Arg(1)
		mbTrans195 := thrift.NewTMemoryBufferLen(len(arg194))
		defer mbTrans195.Close()
		_, err196 := mbTrans195.WriteString(arg194)
		if err196 != nil {
			Usage()
			return
		}
		factory197 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt198 := factory197.GetProtocol(mbTrans195)
		argvalue0 := dp_server.NewRequestHeader()
		err199 := argvalue0.Read(jsProt198)
		if err199 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg200 := flag.Arg(2)
		mbTrans201 := thrift.NewTMemoryBufferLen(len(arg200))
		defer mbTrans201.Close()
		_, err202 := mbTrans201.WriteString(arg200)
		if err202 != nil {
			Usage()
			return
		}
		factory203 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt204 := factory203.GetProtocol(mbTrans201)
		argvalue1 := dp_server.NewPromotion()
		err205 := argvalue1.Read(jsProt204)
		if err205 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "editPromotion":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPromotion requires 2 args")
			flag.Usage()
		}
		arg206 := flag.Arg(1)
		mbTrans207 := thrift.NewTMemoryBufferLen(len(arg206))
		defer mbTrans207.Close()
		_, err208 := mbTrans207.WriteString(arg206)
		if err208 != nil {
			Usage()
			return
		}
		factory209 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt210 := factory209.GetProtocol(mbTrans207)
		argvalue0 := dp_server.NewRequestHeader()
		err211 := argvalue0.Read(jsProt210)
		if err211 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg212 := flag.Arg(2)
		mbTrans213 := thrift.NewTMemoryBufferLen(len(arg212))
		defer mbTrans213.Close()
		_, err214 := mbTrans213.WriteString(arg212)
		if err214 != nil {
			Usage()
			return
		}
		factory215 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt216 := factory215.GetProtocol(mbTrans213)
		argvalue1 := dp_server.NewPromotion()
		err217 := argvalue1.Read(jsProt216)
		if err217 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditPromotion(value0, value1))
		fmt.Print("\n")
		break
	case "searchPromotionByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchPromotionByParams requires 2 args")
			flag.Usage()
		}
		arg218 := flag.Arg(1)
		mbTrans219 := thrift.NewTMemoryBufferLen(len(arg218))
		defer mbTrans219.Close()
		_, err220 := mbTrans219.WriteString(arg218)
		if err220 != nil {
			Usage()
			return
		}
		factory221 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt222 := factory221.GetProtocol(mbTrans219)
		argvalue0 := dp_server.NewRequestHeader()
		err223 := argvalue0.Read(jsProt222)
		if err223 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg224 := flag.Arg(2)
		mbTrans225 := thrift.NewTMemoryBufferLen(len(arg224))
		defer mbTrans225.Close()
		_, err226 := mbTrans225.WriteString(arg224)
		if err226 != nil {
			Usage()
			return
		}
		factory227 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt228 := factory227.GetProtocol(mbTrans225)
		argvalue1 := dp_server.NewPromotionParams()
		err229 := argvalue1.Read(jsProt228)
		if err229 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchPromotionByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPromotionsByIds requires 2 args")
			flag.Usage()
		}
		arg230 := flag.Arg(1)
		mbTrans231 := thrift.NewTMemoryBufferLen(len(arg230))
		defer mbTrans231.Close()
		_, err232 := mbTrans231.WriteString(arg230)
		if err232 != nil {
			Usage()
			return
		}
		factory233 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt234 := factory233.GetProtocol(mbTrans231)
		argvalue0 := dp_server.NewRequestHeader()
		err235 := argvalue0.Read(jsProt234)
		if err235 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg236 := flag.Arg(2)
		mbTrans237 := thrift.NewTMemoryBufferLen(len(arg236))
		defer mbTrans237.Close()
		_, err238 := mbTrans237.WriteString(arg236)
		if err238 != nil {
			Usage()
			return
		}
		factory239 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt240 := factory239.GetProtocol(mbTrans237)
		containerStruct1 := dp_server.NewGetPromotionsByIdsArgs()
		err241 := containerStruct1.ReadField2(jsProt240)
		if err241 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPromotionsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addPromotionLink":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddPromotionLink requires 2 args")
			flag.Usage()
		}
		arg242 := flag.Arg(1)
		mbTrans243 := thrift.NewTMemoryBufferLen(len(arg242))
		defer mbTrans243.Close()
		_, err244 := mbTrans243.WriteString(arg242)
		if err244 != nil {
			Usage()
			return
		}
		factory245 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt246 := factory245.GetProtocol(mbTrans243)
		argvalue0 := dp_server.NewRequestHeader()
		err247 := argvalue0.Read(jsProt246)
		if err247 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg248 := flag.Arg(2)
		mbTrans249 := thrift.NewTMemoryBufferLen(len(arg248))
		defer mbTrans249.Close()
		_, err250 := mbTrans249.WriteString(arg248)
		if err250 != nil {
			Usage()
			return
		}
		factory251 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt252 := factory251.GetProtocol(mbTrans249)
		argvalue1 := dp_server.NewPromotionLink()
		err253 := argvalue1.Read(jsProt252)
		if err253 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddPromotionLink(value0, value1))
		fmt.Print("\n")
		break
	case "editPromotionLink":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditPromotionLink requires 2 args")
			flag.Usage()
		}
		arg254 := flag.Arg(1)
		mbTrans255 := thrift.NewTMemoryBufferLen(len(arg254))
		defer mbTrans255.Close()
		_, err256 := mbTrans255.WriteString(arg254)
		if err256 != nil {
			Usage()
			return
		}
		factory257 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt258 := factory257.GetProtocol(mbTrans255)
		argvalue0 := dp_server.NewRequestHeader()
		err259 := argvalue0.Read(jsProt258)
		if err259 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg260 := flag.Arg(2)
		mbTrans261 := thrift.NewTMemoryBufferLen(len(arg260))
		defer mbTrans261.Close()
		_, err262 := mbTrans261.WriteString(arg260)
		if err262 != nil {
			Usage()
			return
		}
		factory263 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt264 := factory263.GetProtocol(mbTrans261)
		argvalue1 := dp_server.NewPromotionLink()
		err265 := argvalue1.Read(jsProt264)
		if err265 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditPromotionLink(value0, value1))
		fmt.Print("\n")
		break
	case "searchPromotionLinkByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchPromotionLinkByParams requires 2 args")
			flag.Usage()
		}
		arg266 := flag.Arg(1)
		mbTrans267 := thrift.NewTMemoryBufferLen(len(arg266))
		defer mbTrans267.Close()
		_, err268 := mbTrans267.WriteString(arg266)
		if err268 != nil {
			Usage()
			return
		}
		factory269 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt270 := factory269.GetProtocol(mbTrans267)
		argvalue0 := dp_server.NewRequestHeader()
		err271 := argvalue0.Read(jsProt270)
		if err271 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg272 := flag.Arg(2)
		mbTrans273 := thrift.NewTMemoryBufferLen(len(arg272))
		defer mbTrans273.Close()
		_, err274 := mbTrans273.WriteString(arg272)
		if err274 != nil {
			Usage()
			return
		}
		factory275 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt276 := factory275.GetProtocol(mbTrans273)
		argvalue1 := dp_server.NewPromotionLinkParams()
		err277 := argvalue1.Read(jsProt276)
		if err277 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchPromotionLinkByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getPromotionLinksByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetPromotionLinksByIds requires 2 args")
			flag.Usage()
		}
		arg278 := flag.Arg(1)
		mbTrans279 := thrift.NewTMemoryBufferLen(len(arg278))
		defer mbTrans279.Close()
		_, err280 := mbTrans279.WriteString(arg278)
		if err280 != nil {
			Usage()
			return
		}
		factory281 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt282 := factory281.GetProtocol(mbTrans279)
		argvalue0 := dp_server.NewRequestHeader()
		err283 := argvalue0.Read(jsProt282)
		if err283 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg284 := flag.Arg(2)
		mbTrans285 := thrift.NewTMemoryBufferLen(len(arg284))
		defer mbTrans285.Close()
		_, err286 := mbTrans285.WriteString(arg284)
		if err286 != nil {
			Usage()
			return
		}
		factory287 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt288 := factory287.GetProtocol(mbTrans285)
		containerStruct1 := dp_server.NewGetPromotionLinksByIdsArgs()
		err289 := containerStruct1.ReadField2(jsProt288)
		if err289 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetPromotionLinksByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addTask":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddTask requires 2 args")
			flag.Usage()
		}
		arg290 := flag.Arg(1)
		mbTrans291 := thrift.NewTMemoryBufferLen(len(arg290))
		defer mbTrans291.Close()
		_, err292 := mbTrans291.WriteString(arg290)
		if err292 != nil {
			Usage()
			return
		}
		factory293 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt294 := factory293.GetProtocol(mbTrans291)
		argvalue0 := dp_server.NewRequestHeader()
		err295 := argvalue0.Read(jsProt294)
		if err295 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg296 := flag.Arg(2)
		mbTrans297 := thrift.NewTMemoryBufferLen(len(arg296))
		defer mbTrans297.Close()
		_, err298 := mbTrans297.WriteString(arg296)
		if err298 != nil {
			Usage()
			return
		}
		factory299 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt300 := factory299.GetProtocol(mbTrans297)
		argvalue1 := dp_server.NewTask()
		err301 := argvalue1.Read(jsProt300)
		if err301 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddTask(value0, value1))
		fmt.Print("\n")
		break
	case "editTask":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditTask requires 2 args")
			flag.Usage()
		}
		arg302 := flag.Arg(1)
		mbTrans303 := thrift.NewTMemoryBufferLen(len(arg302))
		defer mbTrans303.Close()
		_, err304 := mbTrans303.WriteString(arg302)
		if err304 != nil {
			Usage()
			return
		}
		factory305 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt306 := factory305.GetProtocol(mbTrans303)
		argvalue0 := dp_server.NewRequestHeader()
		err307 := argvalue0.Read(jsProt306)
		if err307 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg308 := flag.Arg(2)
		mbTrans309 := thrift.NewTMemoryBufferLen(len(arg308))
		defer mbTrans309.Close()
		_, err310 := mbTrans309.WriteString(arg308)
		if err310 != nil {
			Usage()
			return
		}
		factory311 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt312 := factory311.GetProtocol(mbTrans309)
		argvalue1 := dp_server.NewTask()
		err313 := argvalue1.Read(jsProt312)
		if err313 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditTask(value0, value1))
		fmt.Print("\n")
		break
	case "searchTaskByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchTaskByParams requires 2 args")
			flag.Usage()
		}
		arg314 := flag.Arg(1)
		mbTrans315 := thrift.NewTMemoryBufferLen(len(arg314))
		defer mbTrans315.Close()
		_, err316 := mbTrans315.WriteString(arg314)
		if err316 != nil {
			Usage()
			return
		}
		factory317 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt318 := factory317.GetProtocol(mbTrans315)
		argvalue0 := dp_server.NewRequestHeader()
		err319 := argvalue0.Read(jsProt318)
		if err319 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg320 := flag.Arg(2)
		mbTrans321 := thrift.NewTMemoryBufferLen(len(arg320))
		defer mbTrans321.Close()
		_, err322 := mbTrans321.WriteString(arg320)
		if err322 != nil {
			Usage()
			return
		}
		factory323 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt324 := factory323.GetProtocol(mbTrans321)
		argvalue1 := dp_server.NewTaskParams()
		err325 := argvalue1.Read(jsProt324)
		if err325 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchTaskByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getTasksByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetTasksByIds requires 2 args")
			flag.Usage()
		}
		arg326 := flag.Arg(1)
		mbTrans327 := thrift.NewTMemoryBufferLen(len(arg326))
		defer mbTrans327.Close()
		_, err328 := mbTrans327.WriteString(arg326)
		if err328 != nil {
			Usage()
			return
		}
		factory329 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt330 := factory329.GetProtocol(mbTrans327)
		argvalue0 := dp_server.NewRequestHeader()
		err331 := argvalue0.Read(jsProt330)
		if err331 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg332 := flag.Arg(2)
		mbTrans333 := thrift.NewTMemoryBufferLen(len(arg332))
		defer mbTrans333.Close()
		_, err334 := mbTrans333.WriteString(arg332)
		if err334 != nil {
			Usage()
			return
		}
		factory335 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt336 := factory335.GetProtocol(mbTrans333)
		containerStruct1 := dp_server.NewGetTasksByIdsArgs()
		err337 := containerStruct1.ReadField2(jsProt336)
		if err337 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetTasksByIds(value0, value1))
		fmt.Print("\n")
		break
	case "pauseTasksByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseTasksByIds requires 2 args")
			flag.Usage()
		}
		arg338 := flag.Arg(1)
		mbTrans339 := thrift.NewTMemoryBufferLen(len(arg338))
		defer mbTrans339.Close()
		_, err340 := mbTrans339.WriteString(arg338)
		if err340 != nil {
			Usage()
			return
		}
		factory341 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt342 := factory341.GetProtocol(mbTrans339)
		argvalue0 := dp_server.NewRequestHeader()
		err343 := argvalue0.Read(jsProt342)
		if err343 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg344 := flag.Arg(2)
		mbTrans345 := thrift.NewTMemoryBufferLen(len(arg344))
		defer mbTrans345.Close()
		_, err346 := mbTrans345.WriteString(arg344)
		if err346 != nil {
			Usage()
			return
		}
		factory347 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt348 := factory347.GetProtocol(mbTrans345)
		containerStruct1 := dp_server.NewPauseTasksByIdsArgs()
		err349 := containerStruct1.ReadField2(jsProt348)
		if err349 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseTasksByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeTasksByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeTasksByIds requires 2 args")
			flag.Usage()
		}
		arg350 := flag.Arg(1)
		mbTrans351 := thrift.NewTMemoryBufferLen(len(arg350))
		defer mbTrans351.Close()
		_, err352 := mbTrans351.WriteString(arg350)
		if err352 != nil {
			Usage()
			return
		}
		factory353 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt354 := factory353.GetProtocol(mbTrans351)
		argvalue0 := dp_server.NewRequestHeader()
		err355 := argvalue0.Read(jsProt354)
		if err355 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg356 := flag.Arg(2)
		mbTrans357 := thrift.NewTMemoryBufferLen(len(arg356))
		defer mbTrans357.Close()
		_, err358 := mbTrans357.WriteString(arg356)
		if err358 != nil {
			Usage()
			return
		}
		factory359 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt360 := factory359.GetProtocol(mbTrans357)
		containerStruct1 := dp_server.NewResumeTasksByIdsArgs()
		err361 := containerStruct1.ReadField2(jsProt360)
		if err361 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeTasksByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddStrategy requires 2 args")
			flag.Usage()
		}
		arg362 := flag.Arg(1)
		mbTrans363 := thrift.NewTMemoryBufferLen(len(arg362))
		defer mbTrans363.Close()
		_, err364 := mbTrans363.WriteString(arg362)
		if err364 != nil {
			Usage()
			return
		}
		factory365 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt366 := factory365.GetProtocol(mbTrans363)
		argvalue0 := dp_server.NewRequestHeader()
		err367 := argvalue0.Read(jsProt366)
		if err367 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg368 := flag.Arg(2)
		mbTrans369 := thrift.NewTMemoryBufferLen(len(arg368))
		defer mbTrans369.Close()
		_, err370 := mbTrans369.WriteString(arg368)
		if err370 != nil {
			Usage()
			return
		}
		factory371 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt372 := factory371.GetProtocol(mbTrans369)
		argvalue1 := dp_server.NewStrategy()
		err373 := argvalue1.Read(jsProt372)
		if err373 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "editStrategy":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditStrategy requires 2 args")
			flag.Usage()
		}
		arg374 := flag.Arg(1)
		mbTrans375 := thrift.NewTMemoryBufferLen(len(arg374))
		defer mbTrans375.Close()
		_, err376 := mbTrans375.WriteString(arg374)
		if err376 != nil {
			Usage()
			return
		}
		factory377 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt378 := factory377.GetProtocol(mbTrans375)
		argvalue0 := dp_server.NewRequestHeader()
		err379 := argvalue0.Read(jsProt378)
		if err379 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg380 := flag.Arg(2)
		mbTrans381 := thrift.NewTMemoryBufferLen(len(arg380))
		defer mbTrans381.Close()
		_, err382 := mbTrans381.WriteString(arg380)
		if err382 != nil {
			Usage()
			return
		}
		factory383 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt384 := factory383.GetProtocol(mbTrans381)
		argvalue1 := dp_server.NewStrategy()
		err385 := argvalue1.Read(jsProt384)
		if err385 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditStrategy(value0, value1))
		fmt.Print("\n")
		break
	case "searchStrategyByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchStrategyByParams requires 2 args")
			flag.Usage()
		}
		arg386 := flag.Arg(1)
		mbTrans387 := thrift.NewTMemoryBufferLen(len(arg386))
		defer mbTrans387.Close()
		_, err388 := mbTrans387.WriteString(arg386)
		if err388 != nil {
			Usage()
			return
		}
		factory389 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt390 := factory389.GetProtocol(mbTrans387)
		argvalue0 := dp_server.NewRequestHeader()
		err391 := argvalue0.Read(jsProt390)
		if err391 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg392 := flag.Arg(2)
		mbTrans393 := thrift.NewTMemoryBufferLen(len(arg392))
		defer mbTrans393.Close()
		_, err394 := mbTrans393.WriteString(arg392)
		if err394 != nil {
			Usage()
			return
		}
		factory395 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt396 := factory395.GetProtocol(mbTrans393)
		argvalue1 := dp_server.NewStrategyParams()
		err397 := argvalue1.Read(jsProt396)
		if err397 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchStrategyByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg398 := flag.Arg(1)
		mbTrans399 := thrift.NewTMemoryBufferLen(len(arg398))
		defer mbTrans399.Close()
		_, err400 := mbTrans399.WriteString(arg398)
		if err400 != nil {
			Usage()
			return
		}
		factory401 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt402 := factory401.GetProtocol(mbTrans399)
		argvalue0 := dp_server.NewRequestHeader()
		err403 := argvalue0.Read(jsProt402)
		if err403 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg404 := flag.Arg(2)
		mbTrans405 := thrift.NewTMemoryBufferLen(len(arg404))
		defer mbTrans405.Close()
		_, err406 := mbTrans405.WriteString(arg404)
		if err406 != nil {
			Usage()
			return
		}
		factory407 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt408 := factory407.GetProtocol(mbTrans405)
		containerStruct1 := dp_server.NewGetStrategiesByIdsArgs()
		err409 := containerStruct1.ReadField2(jsProt408)
		if err409 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "pauseStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg410 := flag.Arg(1)
		mbTrans411 := thrift.NewTMemoryBufferLen(len(arg410))
		defer mbTrans411.Close()
		_, err412 := mbTrans411.WriteString(arg410)
		if err412 != nil {
			Usage()
			return
		}
		factory413 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt414 := factory413.GetProtocol(mbTrans411)
		argvalue0 := dp_server.NewRequestHeader()
		err415 := argvalue0.Read(jsProt414)
		if err415 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg416 := flag.Arg(2)
		mbTrans417 := thrift.NewTMemoryBufferLen(len(arg416))
		defer mbTrans417.Close()
		_, err418 := mbTrans417.WriteString(arg416)
		if err418 != nil {
			Usage()
			return
		}
		factory419 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt420 := factory419.GetProtocol(mbTrans417)
		containerStruct1 := dp_server.NewPauseStrategiesByIdsArgs()
		err421 := containerStruct1.ReadField2(jsProt420)
		if err421 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg422 := flag.Arg(1)
		mbTrans423 := thrift.NewTMemoryBufferLen(len(arg422))
		defer mbTrans423.Close()
		_, err424 := mbTrans423.WriteString(arg422)
		if err424 != nil {
			Usage()
			return
		}
		factory425 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt426 := factory425.GetProtocol(mbTrans423)
		argvalue0 := dp_server.NewRequestHeader()
		err427 := argvalue0.Read(jsProt426)
		if err427 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg428 := flag.Arg(2)
		mbTrans429 := thrift.NewTMemoryBufferLen(len(arg428))
		defer mbTrans429.Close()
		_, err430 := mbTrans429.WriteString(arg428)
		if err430 != nil {
			Usage()
			return
		}
		factory431 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt432 := factory431.GetProtocol(mbTrans429)
		containerStruct1 := dp_server.NewResumeStrategiesByIdsArgs()
		err433 := containerStruct1.ReadField2(jsProt432)
		if err433 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddCreative requires 2 args")
			flag.Usage()
		}
		arg434 := flag.Arg(1)
		mbTrans435 := thrift.NewTMemoryBufferLen(len(arg434))
		defer mbTrans435.Close()
		_, err436 := mbTrans435.WriteString(arg434)
		if err436 != nil {
			Usage()
			return
		}
		factory437 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt438 := factory437.GetProtocol(mbTrans435)
		argvalue0 := dp_server.NewRequestHeader()
		err439 := argvalue0.Read(jsProt438)
		if err439 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg440 := flag.Arg(2)
		mbTrans441 := thrift.NewTMemoryBufferLen(len(arg440))
		defer mbTrans441.Close()
		_, err442 := mbTrans441.WriteString(arg440)
		if err442 != nil {
			Usage()
			return
		}
		factory443 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt444 := factory443.GetProtocol(mbTrans441)
		argvalue1 := dp_server.NewCreative()
		err445 := argvalue1.Read(jsProt444)
		if err445 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddCreative(value0, value1))
		fmt.Print("\n")
		break
	case "editCreative":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditCreative requires 2 args")
			flag.Usage()
		}
		arg446 := flag.Arg(1)
		mbTrans447 := thrift.NewTMemoryBufferLen(len(arg446))
		defer mbTrans447.Close()
		_, err448 := mbTrans447.WriteString(arg446)
		if err448 != nil {
			Usage()
			return
		}
		factory449 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt450 := factory449.GetProtocol(mbTrans447)
		argvalue0 := dp_server.NewRequestHeader()
		err451 := argvalue0.Read(jsProt450)
		if err451 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg452 := flag.Arg(2)
		mbTrans453 := thrift.NewTMemoryBufferLen(len(arg452))
		defer mbTrans453.Close()
		_, err454 := mbTrans453.WriteString(arg452)
		if err454 != nil {
			Usage()
			return
		}
		factory455 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt456 := factory455.GetProtocol(mbTrans453)
		argvalue1 := dp_server.NewCreative()
		err457 := argvalue1.Read(jsProt456)
		if err457 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditCreative(value0, value1))
		fmt.Print("\n")
		break
	case "searchCreativeByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchCreativeByParams requires 2 args")
			flag.Usage()
		}
		arg458 := flag.Arg(1)
		mbTrans459 := thrift.NewTMemoryBufferLen(len(arg458))
		defer mbTrans459.Close()
		_, err460 := mbTrans459.WriteString(arg458)
		if err460 != nil {
			Usage()
			return
		}
		factory461 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt462 := factory461.GetProtocol(mbTrans459)
		argvalue0 := dp_server.NewRequestHeader()
		err463 := argvalue0.Read(jsProt462)
		if err463 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg464 := flag.Arg(2)
		mbTrans465 := thrift.NewTMemoryBufferLen(len(arg464))
		defer mbTrans465.Close()
		_, err466 := mbTrans465.WriteString(arg464)
		if err466 != nil {
			Usage()
			return
		}
		factory467 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt468 := factory467.GetProtocol(mbTrans465)
		argvalue1 := dp_server.NewCreativeParams()
		err469 := argvalue1.Read(jsProt468)
		if err469 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchCreativeByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg470 := flag.Arg(1)
		mbTrans471 := thrift.NewTMemoryBufferLen(len(arg470))
		defer mbTrans471.Close()
		_, err472 := mbTrans471.WriteString(arg470)
		if err472 != nil {
			Usage()
			return
		}
		factory473 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt474 := factory473.GetProtocol(mbTrans471)
		argvalue0 := dp_server.NewRequestHeader()
		err475 := argvalue0.Read(jsProt474)
		if err475 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg476 := flag.Arg(2)
		mbTrans477 := thrift.NewTMemoryBufferLen(len(arg476))
		defer mbTrans477.Close()
		_, err478 := mbTrans477.WriteString(arg476)
		if err478 != nil {
			Usage()
			return
		}
		factory479 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt480 := factory479.GetProtocol(mbTrans477)
		containerStruct1 := dp_server.NewGetCreativesByIdsArgs()
		err481 := containerStruct1.ReadField2(jsProt480)
		if err481 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "pauseCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "PauseCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg482 := flag.Arg(1)
		mbTrans483 := thrift.NewTMemoryBufferLen(len(arg482))
		defer mbTrans483.Close()
		_, err484 := mbTrans483.WriteString(arg482)
		if err484 != nil {
			Usage()
			return
		}
		factory485 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt486 := factory485.GetProtocol(mbTrans483)
		argvalue0 := dp_server.NewRequestHeader()
		err487 := argvalue0.Read(jsProt486)
		if err487 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg488 := flag.Arg(2)
		mbTrans489 := thrift.NewTMemoryBufferLen(len(arg488))
		defer mbTrans489.Close()
		_, err490 := mbTrans489.WriteString(arg488)
		if err490 != nil {
			Usage()
			return
		}
		factory491 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt492 := factory491.GetProtocol(mbTrans489)
		containerStruct1 := dp_server.NewPauseCreativesByIdsArgs()
		err493 := containerStruct1.ReadField2(jsProt492)
		if err493 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.PauseCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "resumeCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ResumeCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg494 := flag.Arg(1)
		mbTrans495 := thrift.NewTMemoryBufferLen(len(arg494))
		defer mbTrans495.Close()
		_, err496 := mbTrans495.WriteString(arg494)
		if err496 != nil {
			Usage()
			return
		}
		factory497 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt498 := factory497.GetProtocol(mbTrans495)
		argvalue0 := dp_server.NewRequestHeader()
		err499 := argvalue0.Read(jsProt498)
		if err499 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg500 := flag.Arg(2)
		mbTrans501 := thrift.NewTMemoryBufferLen(len(arg500))
		defer mbTrans501.Close()
		_, err502 := mbTrans501.WriteString(arg500)
		if err502 != nil {
			Usage()
			return
		}
		factory503 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt504 := factory503.GetProtocol(mbTrans501)
		containerStruct1 := dp_server.NewResumeCreativesByIdsArgs()
		err505 := containerStruct1.ReadField2(jsProt504)
		if err505 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.ResumeCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
