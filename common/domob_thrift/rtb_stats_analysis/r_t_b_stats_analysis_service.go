// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_stats_analysis

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__

type RTBStatsAnalysisService interface {
	dm303.DomobService

	// 添加DMP人群分析任务
	// 返回值: 任务ID
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Task: 人群分析任务结构体
	AddDmpAnalysisTask(header *common.RequestHeader, task *DmpAnalysisTask) (r int32, e *RTBStatsAnalysisException, err error)
	// 根据任务ID批量获取任务信息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Ids: 人群分析任务ID列表
	GetDmpAnalysisTasksByIds(header *common.RequestHeader, ids []int32) (r map[int32]*DmpAnalysisTask, e *RTBStatsAnalysisException, err error)
	// 根据任务状态分页获取任务信息, 默认按修改时间降序排序
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Status: 任务状态,包含多个状态，结果取并集;当为null或空列表时表示不限定状态
	//  - Offset: 查询偏移量，最小为0，例如offset=0,limit=10 则查询1-10条数据
	//  - Limit: 最多返回多少条ID, 最大为100
	SearchDmpAnalysisTasksByStatus(header *common.RequestHeader, status []int32, offset int32, limit int32) (r *DmpAnalysisTaskQueryResult, e *RTBStatsAnalysisException, err error)
	// 更新任务状态
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskId: 任务ID
	//  - NewStatus: 目标状态
	UpdateDmpAnalysisTaskStatus(header *common.RequestHeader, task_id int32, new_status int32) (e *RTBStatsAnalysisException, err error)
	// 添加任务人群分析信息结果
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskId: 任务ID
	//  - Result: 任务结果, 支持多个以便保留扩展性, 方便以后DMP计算多个人群
	AddDmpAnalysisTaskResult(header *common.RequestHeader, task_id int32, result []*DmpAnalysisInfo) (e *RTBStatsAnalysisException, err error)
	// 根据任务ID获取当前任务的人群分析信息结果
	// 结果必须包含任务指定的人群, 也包含部分满足条件的关联人群, 一般是当前任务关联App投放数据中定向的其他人群
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - TaskId: 任务ID
	SearchDmpAnalysisInfoByTaskId(header *common.RequestHeader, task_id int32) (r *DmpAnalysisInfoQueryResult, e *RTBStatsAnalysisException, err error)
	// 查询人群分析信息
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - AppId: AppID, 为0表示全部App
	//  - ExchangeId: 交易所ID, 为0表示不限制交易所
	//  - TypeA1: 人群类型,目前支持0: 交易所人群分析任务, 默认值0
	//  - StartTime: 统计起始时间
	//  - EndTime: 统计结束时间
	//  - Offset: 查询偏移量，最小为0，例如offset=0,limit=10 则查询1-10条数据
	//  - Limit: 最多返回多少条ID, 最大为100
	SearchDmpAnalysisInfoByParams(header *common.RequestHeader, app_id int32, exchange_id int32, type_a1 int32, start_time int64, end_time int64, offset int32, limit int32) (r *DmpAnalysisInfoQueryResult, e *RTBStatsAnalysisException, err error)
	// 执行clickhouse的sql语句
	// 参数 完整可执行sql
	// 返回值 查询结果转为json后返回
	//
	// Parameters:
	//  - Header: 请求消息头结构体
	//  - Sql: sql语句
	ExecClickhouseSQL(header *common.RequestHeader, sql string) (r string, e *RTBStatsAnalysisException, err error)
}

type RTBStatsAnalysisServiceClient struct {
	*dm303.DomobServiceClient
}

func NewRTBStatsAnalysisServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *RTBStatsAnalysisServiceClient {
	return &RTBStatsAnalysisServiceClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewRTBStatsAnalysisServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *RTBStatsAnalysisServiceClient {
	return &RTBStatsAnalysisServiceClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// 添加DMP人群分析任务
// 返回值: 任务ID
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Task: 人群分析任务结构体
func (p *RTBStatsAnalysisServiceClient) AddDmpAnalysisTask(header *common.RequestHeader, task *DmpAnalysisTask) (r int32, e *RTBStatsAnalysisException, err error) {
	if err = p.sendAddDmpAnalysisTask(header, task); err != nil {
		return
	}
	return p.recvAddDmpAnalysisTask()
}

func (p *RTBStatsAnalysisServiceClient) sendAddDmpAnalysisTask(header *common.RequestHeader, task *DmpAnalysisTask) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addDmpAnalysisTask", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args3 := NewAddDmpAnalysisTaskArgs()
	args3.Header = header
	args3.Task = task
	if err = args3.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvAddDmpAnalysisTask() (value int32, e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error5 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error6 error
		error6, err = error5.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error6
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result4 := NewAddDmpAnalysisTaskResult()
	if err = result4.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result4.Success
	if result4.E != nil {
		e = result4.E
	}
	return
}

// 根据任务ID批量获取任务信息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Ids: 人群分析任务ID列表
func (p *RTBStatsAnalysisServiceClient) GetDmpAnalysisTasksByIds(header *common.RequestHeader, ids []int32) (r map[int32]*DmpAnalysisTask, e *RTBStatsAnalysisException, err error) {
	if err = p.sendGetDmpAnalysisTasksByIds(header, ids); err != nil {
		return
	}
	return p.recvGetDmpAnalysisTasksByIds()
}

func (p *RTBStatsAnalysisServiceClient) sendGetDmpAnalysisTasksByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getDmpAnalysisTasksByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args7 := NewGetDmpAnalysisTasksByIdsArgs()
	args7.Header = header
	args7.Ids = ids
	if err = args7.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvGetDmpAnalysisTasksByIds() (value map[int32]*DmpAnalysisTask, e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error9 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error10 error
		error10, err = error9.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error10
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result8 := NewGetDmpAnalysisTasksByIdsResult()
	if err = result8.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result8.Success
	if result8.E != nil {
		e = result8.E
	}
	return
}

// 根据任务状态分页获取任务信息, 默认按修改时间降序排序
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Status: 任务状态,包含多个状态，结果取并集;当为null或空列表时表示不限定状态
//  - Offset: 查询偏移量，最小为0，例如offset=0,limit=10 则查询1-10条数据
//  - Limit: 最多返回多少条ID, 最大为100
func (p *RTBStatsAnalysisServiceClient) SearchDmpAnalysisTasksByStatus(header *common.RequestHeader, status []int32, offset int32, limit int32) (r *DmpAnalysisTaskQueryResult, e *RTBStatsAnalysisException, err error) {
	if err = p.sendSearchDmpAnalysisTasksByStatus(header, status, offset, limit); err != nil {
		return
	}
	return p.recvSearchDmpAnalysisTasksByStatus()
}

func (p *RTBStatsAnalysisServiceClient) sendSearchDmpAnalysisTasksByStatus(header *common.RequestHeader, status []int32, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchDmpAnalysisTasksByStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args11 := NewSearchDmpAnalysisTasksByStatusArgs()
	args11.Header = header
	args11.Status = status
	args11.Offset = offset
	args11.Limit = limit
	if err = args11.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvSearchDmpAnalysisTasksByStatus() (value *DmpAnalysisTaskQueryResult, e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error13 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error14 error
		error14, err = error13.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error14
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result12 := NewSearchDmpAnalysisTasksByStatusResult()
	if err = result12.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result12.Success
	if result12.E != nil {
		e = result12.E
	}
	return
}

// 更新任务状态
//
// Parameters:
//  - Header: 请求消息头结构体
//  - TaskId: 任务ID
//  - NewStatus: 目标状态
func (p *RTBStatsAnalysisServiceClient) UpdateDmpAnalysisTaskStatus(header *common.RequestHeader, task_id int32, new_status int32) (e *RTBStatsAnalysisException, err error) {
	if err = p.sendUpdateDmpAnalysisTaskStatus(header, task_id, new_status); err != nil {
		return
	}
	return p.recvUpdateDmpAnalysisTaskStatus()
}

func (p *RTBStatsAnalysisServiceClient) sendUpdateDmpAnalysisTaskStatus(header *common.RequestHeader, task_id int32, new_status int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("updateDmpAnalysisTaskStatus", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args15 := NewUpdateDmpAnalysisTaskStatusArgs()
	args15.Header = header
	args15.TaskId = task_id
	args15.NewStatus = new_status
	if err = args15.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvUpdateDmpAnalysisTaskStatus() (e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error17 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error18 error
		error18, err = error17.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error18
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result16 := NewUpdateDmpAnalysisTaskStatusResult()
	if err = result16.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result16.E != nil {
		e = result16.E
	}
	return
}

// 添加任务人群分析信息结果
//
// Parameters:
//  - Header: 请求消息头结构体
//  - TaskId: 任务ID
//  - Result: 任务结果, 支持多个以便保留扩展性, 方便以后DMP计算多个人群
func (p *RTBStatsAnalysisServiceClient) AddDmpAnalysisTaskResult(header *common.RequestHeader, task_id int32, result []*DmpAnalysisInfo) (e *RTBStatsAnalysisException, err error) {
	if err = p.sendAddDmpAnalysisTaskResult(header, task_id, result); err != nil {
		return
	}
	return p.recvAddDmpAnalysisTaskResult()
}

func (p *RTBStatsAnalysisServiceClient) sendAddDmpAnalysisTaskResult(header *common.RequestHeader, task_id int32, result []*DmpAnalysisInfo) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("addDmpAnalysisTaskResult", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args19 := NewAddDmpAnalysisTaskResultArgs()
	args19.Header = header
	args19.TaskId = task_id
	args19.Result = result
	if err = args19.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvAddDmpAnalysisTaskResult() (e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error21 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error22 error
		error22, err = error21.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error22
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result20 := NewAddDmpAnalysisTaskResultResult()
	if err = result20.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result20.E != nil {
		e = result20.E
	}
	return
}

// 根据任务ID获取当前任务的人群分析信息结果
// 结果必须包含任务指定的人群, 也包含部分满足条件的关联人群, 一般是当前任务关联App投放数据中定向的其他人群
//
// Parameters:
//  - Header: 请求消息头结构体
//  - TaskId: 任务ID
func (p *RTBStatsAnalysisServiceClient) SearchDmpAnalysisInfoByTaskId(header *common.RequestHeader, task_id int32) (r *DmpAnalysisInfoQueryResult, e *RTBStatsAnalysisException, err error) {
	if err = p.sendSearchDmpAnalysisInfoByTaskId(header, task_id); err != nil {
		return
	}
	return p.recvSearchDmpAnalysisInfoByTaskId()
}

func (p *RTBStatsAnalysisServiceClient) sendSearchDmpAnalysisInfoByTaskId(header *common.RequestHeader, task_id int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchDmpAnalysisInfoByTaskId", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args23 := NewSearchDmpAnalysisInfoByTaskIdArgs()
	args23.Header = header
	args23.TaskId = task_id
	if err = args23.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvSearchDmpAnalysisInfoByTaskId() (value *DmpAnalysisInfoQueryResult, e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error25 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error26 error
		error26, err = error25.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error26
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result24 := NewSearchDmpAnalysisInfoByTaskIdResult()
	if err = result24.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result24.Success
	if result24.E != nil {
		e = result24.E
	}
	return
}

// 查询人群分析信息
//
// Parameters:
//  - Header: 请求消息头结构体
//  - AppId: AppID, 为0表示全部App
//  - ExchangeId: 交易所ID, 为0表示不限制交易所
//  - TypeA1: 人群类型,目前支持0: 交易所人群分析任务, 默认值0
//  - StartTime: 统计起始时间
//  - EndTime: 统计结束时间
//  - Offset: 查询偏移量，最小为0，例如offset=0,limit=10 则查询1-10条数据
//  - Limit: 最多返回多少条ID, 最大为100
func (p *RTBStatsAnalysisServiceClient) SearchDmpAnalysisInfoByParams(header *common.RequestHeader, app_id int32, exchange_id int32, type_a1 int32, start_time int64, end_time int64, offset int32, limit int32) (r *DmpAnalysisInfoQueryResult, e *RTBStatsAnalysisException, err error) {
	if err = p.sendSearchDmpAnalysisInfoByParams(header, app_id, exchange_id, type_a1, start_time, end_time, offset, limit); err != nil {
		return
	}
	return p.recvSearchDmpAnalysisInfoByParams()
}

func (p *RTBStatsAnalysisServiceClient) sendSearchDmpAnalysisInfoByParams(header *common.RequestHeader, app_id int32, exchange_id int32, type_a1 int32, start_time int64, end_time int64, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("searchDmpAnalysisInfoByParams", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args27 := NewSearchDmpAnalysisInfoByParamsArgs()
	args27.Header = header
	args27.AppId = app_id
	args27.ExchangeId = exchange_id
	args27.TypeA1 = type_a1
	args27.StartTime = start_time
	args27.EndTime = end_time
	args27.Offset = offset
	args27.Limit = limit
	if err = args27.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvSearchDmpAnalysisInfoByParams() (value *DmpAnalysisInfoQueryResult, e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error29 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error30 error
		error30, err = error29.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error30
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result28 := NewSearchDmpAnalysisInfoByParamsResult()
	if err = result28.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result28.Success
	if result28.E != nil {
		e = result28.E
	}
	return
}

// 执行clickhouse的sql语句
// 参数 完整可执行sql
// 返回值 查询结果转为json后返回
//
// Parameters:
//  - Header: 请求消息头结构体
//  - Sql: sql语句
func (p *RTBStatsAnalysisServiceClient) ExecClickhouseSQL(header *common.RequestHeader, sql string) (r string, e *RTBStatsAnalysisException, err error) {
	if err = p.sendExecClickhouseSQL(header, sql); err != nil {
		return
	}
	return p.recvExecClickhouseSQL()
}

func (p *RTBStatsAnalysisServiceClient) sendExecClickhouseSQL(header *common.RequestHeader, sql string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("execClickhouseSQL", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args31 := NewExecClickhouseSQLArgs()
	args31.Header = header
	args31.Sql = sql
	if err = args31.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *RTBStatsAnalysisServiceClient) recvExecClickhouseSQL() (value string, e *RTBStatsAnalysisException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error33 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error34 error
		error34, err = error33.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error34
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result32 := NewExecClickhouseSQLResult()
	if err = result32.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result32.Success
	if result32.E != nil {
		e = result32.E
	}
	return
}

type RTBStatsAnalysisServiceProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewRTBStatsAnalysisServiceProcessor(handler RTBStatsAnalysisService) *RTBStatsAnalysisServiceProcessor {
	self35 := &RTBStatsAnalysisServiceProcessor{dm303.NewDomobServiceProcessor(handler)}
	self35.AddToProcessorMap("addDmpAnalysisTask", &rTBStatsAnalysisServiceProcessorAddDmpAnalysisTask{handler: handler})
	self35.AddToProcessorMap("getDmpAnalysisTasksByIds", &rTBStatsAnalysisServiceProcessorGetDmpAnalysisTasksByIds{handler: handler})
	self35.AddToProcessorMap("searchDmpAnalysisTasksByStatus", &rTBStatsAnalysisServiceProcessorSearchDmpAnalysisTasksByStatus{handler: handler})
	self35.AddToProcessorMap("updateDmpAnalysisTaskStatus", &rTBStatsAnalysisServiceProcessorUpdateDmpAnalysisTaskStatus{handler: handler})
	self35.AddToProcessorMap("addDmpAnalysisTaskResult", &rTBStatsAnalysisServiceProcessorAddDmpAnalysisTaskResult{handler: handler})
	self35.AddToProcessorMap("searchDmpAnalysisInfoByTaskId", &rTBStatsAnalysisServiceProcessorSearchDmpAnalysisInfoByTaskId{handler: handler})
	self35.AddToProcessorMap("searchDmpAnalysisInfoByParams", &rTBStatsAnalysisServiceProcessorSearchDmpAnalysisInfoByParams{handler: handler})
	self35.AddToProcessorMap("execClickhouseSQL", &rTBStatsAnalysisServiceProcessorExecClickhouseSQL{handler: handler})
	return self35
}

type rTBStatsAnalysisServiceProcessorAddDmpAnalysisTask struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorAddDmpAnalysisTask) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddDmpAnalysisTaskArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addDmpAnalysisTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddDmpAnalysisTaskResult()
	if result.Success, result.E, err = p.handler.AddDmpAnalysisTask(args.Header, args.Task); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addDmpAnalysisTask: "+err.Error())
		oprot.WriteMessageBegin("addDmpAnalysisTask", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addDmpAnalysisTask", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type rTBStatsAnalysisServiceProcessorGetDmpAnalysisTasksByIds struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorGetDmpAnalysisTasksByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetDmpAnalysisTasksByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getDmpAnalysisTasksByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetDmpAnalysisTasksByIdsResult()
	if result.Success, result.E, err = p.handler.GetDmpAnalysisTasksByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getDmpAnalysisTasksByIds: "+err.Error())
		oprot.WriteMessageBegin("getDmpAnalysisTasksByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getDmpAnalysisTasksByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type rTBStatsAnalysisServiceProcessorSearchDmpAnalysisTasksByStatus struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorSearchDmpAnalysisTasksByStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchDmpAnalysisTasksByStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchDmpAnalysisTasksByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchDmpAnalysisTasksByStatusResult()
	if result.Success, result.E, err = p.handler.SearchDmpAnalysisTasksByStatus(args.Header, args.Status, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchDmpAnalysisTasksByStatus: "+err.Error())
		oprot.WriteMessageBegin("searchDmpAnalysisTasksByStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchDmpAnalysisTasksByStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type rTBStatsAnalysisServiceProcessorUpdateDmpAnalysisTaskStatus struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorUpdateDmpAnalysisTaskStatus) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewUpdateDmpAnalysisTaskStatusArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("updateDmpAnalysisTaskStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewUpdateDmpAnalysisTaskStatusResult()
	if result.E, err = p.handler.UpdateDmpAnalysisTaskStatus(args.Header, args.TaskId, args.NewStatus); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing updateDmpAnalysisTaskStatus: "+err.Error())
		oprot.WriteMessageBegin("updateDmpAnalysisTaskStatus", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("updateDmpAnalysisTaskStatus", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type rTBStatsAnalysisServiceProcessorAddDmpAnalysisTaskResult struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorAddDmpAnalysisTaskResult) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAddDmpAnalysisTaskResultArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("addDmpAnalysisTaskResult", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAddDmpAnalysisTaskResultResult()
	if result.E, err = p.handler.AddDmpAnalysisTaskResult(args.Header, args.TaskId, args.Result); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing addDmpAnalysisTaskResult: "+err.Error())
		oprot.WriteMessageBegin("addDmpAnalysisTaskResult", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("addDmpAnalysisTaskResult", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type rTBStatsAnalysisServiceProcessorSearchDmpAnalysisInfoByTaskId struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorSearchDmpAnalysisInfoByTaskId) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchDmpAnalysisInfoByTaskIdArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchDmpAnalysisInfoByTaskId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchDmpAnalysisInfoByTaskIdResult()
	if result.Success, result.E, err = p.handler.SearchDmpAnalysisInfoByTaskId(args.Header, args.TaskId); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchDmpAnalysisInfoByTaskId: "+err.Error())
		oprot.WriteMessageBegin("searchDmpAnalysisInfoByTaskId", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchDmpAnalysisInfoByTaskId", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type rTBStatsAnalysisServiceProcessorSearchDmpAnalysisInfoByParams struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorSearchDmpAnalysisInfoByParams) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSearchDmpAnalysisInfoByParamsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("searchDmpAnalysisInfoByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSearchDmpAnalysisInfoByParamsResult()
	if result.Success, result.E, err = p.handler.SearchDmpAnalysisInfoByParams(args.Header, args.AppId, args.ExchangeId, args.TypeA1, args.StartTime, args.EndTime, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing searchDmpAnalysisInfoByParams: "+err.Error())
		oprot.WriteMessageBegin("searchDmpAnalysisInfoByParams", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("searchDmpAnalysisInfoByParams", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type rTBStatsAnalysisServiceProcessorExecClickhouseSQL struct {
	handler RTBStatsAnalysisService
}

func (p *rTBStatsAnalysisServiceProcessorExecClickhouseSQL) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewExecClickhouseSQLArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("execClickhouseSQL", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewExecClickhouseSQLResult()
	if result.Success, result.E, err = p.handler.ExecClickhouseSQL(args.Header, args.Sql); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing execClickhouseSQL: "+err.Error())
		oprot.WriteMessageBegin("execClickhouseSQL", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("execClickhouseSQL", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AddDmpAnalysisTaskArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Task   *DmpAnalysisTask      `thrift:"task,2" json:"task"`
}

func NewAddDmpAnalysisTaskArgs() *AddDmpAnalysisTaskArgs {
	return &AddDmpAnalysisTaskArgs{}
}

func (p *AddDmpAnalysisTaskArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddDmpAnalysisTaskArgs) readField2(iprot thrift.TProtocol) error {
	p.Task = NewDmpAnalysisTask()
	if err := p.Task.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Task)
	}
	return nil
}

func (p *AddDmpAnalysisTaskArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addDmpAnalysisTask_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddDmpAnalysisTaskArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Task != nil {
		if err := oprot.WriteFieldBegin("task", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:task: %s", p, err)
		}
		if err := p.Task.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Task)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:task: %s", p, err)
		}
	}
	return err
}

func (p *AddDmpAnalysisTaskArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddDmpAnalysisTaskArgs(%+v)", *p)
}

type AddDmpAnalysisTaskResult struct {
	Success int32                      `thrift:"success,0" json:"success"`
	E       *RTBStatsAnalysisException `thrift:"e,1" json:"e"`
}

func NewAddDmpAnalysisTaskResult() *AddDmpAnalysisTaskResult {
	return &AddDmpAnalysisTaskResult{}
}

func (p *AddDmpAnalysisTaskResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.I32 {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *AddDmpAnalysisTaskResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addDmpAnalysisTask_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.I32, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *AddDmpAnalysisTaskResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddDmpAnalysisTaskResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddDmpAnalysisTaskResult(%+v)", *p)
}

type GetDmpAnalysisTasksByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetDmpAnalysisTasksByIdsArgs() *GetDmpAnalysisTasksByIdsArgs {
	return &GetDmpAnalysisTasksByIdsArgs{}
}

func (p *GetDmpAnalysisTasksByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem36 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem36 = v
		}
		p.Ids = append(p.Ids, _elem36)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDmpAnalysisTasksByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetDmpAnalysisTasksByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetDmpAnalysisTasksByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDmpAnalysisTasksByIdsArgs(%+v)", *p)
}

type GetDmpAnalysisTasksByIdsResult struct {
	Success map[int32]*DmpAnalysisTask `thrift:"success,0" json:"success"`
	E       *RTBStatsAnalysisException `thrift:"e,1" json:"e"`
}

func NewGetDmpAnalysisTasksByIdsResult() *GetDmpAnalysisTasksByIdsResult {
	return &GetDmpAnalysisTasksByIdsResult{}
}

func (p *GetDmpAnalysisTasksByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*DmpAnalysisTask, size)
	for i := 0; i < size; i++ {
		var _key37 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key37 = v
		}
		_val38 := NewDmpAnalysisTask()
		if err := _val38.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val38)
		}
		p.Success[_key37] = _val38
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getDmpAnalysisTasksByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetDmpAnalysisTasksByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetDmpAnalysisTasksByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetDmpAnalysisTasksByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetDmpAnalysisTasksByIdsResult(%+v)", *p)
}

type SearchDmpAnalysisTasksByStatusArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Status []int32               `thrift:"status,2" json:"status"`
	Offset int32                 `thrift:"offset,3" json:"offset"`
	Limit  int32                 `thrift:"limit,4" json:"limit"`
}

func NewSearchDmpAnalysisTasksByStatusArgs() *SearchDmpAnalysisTasksByStatusArgs {
	return &SearchDmpAnalysisTasksByStatusArgs{}
}

func (p *SearchDmpAnalysisTasksByStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Status = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem39 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem39 = v
		}
		p.Status = append(p.Status, _elem39)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDmpAnalysisTasksByStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisTasksByStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Status != nil {
		if err := oprot.WriteFieldBegin("status", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:status: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Status)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Status {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:status: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisTasksByStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisTasksByStatusArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisTasksByStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDmpAnalysisTasksByStatusArgs(%+v)", *p)
}

type SearchDmpAnalysisTasksByStatusResult struct {
	Success *DmpAnalysisTaskQueryResult `thrift:"success,0" json:"success"`
	E       *RTBStatsAnalysisException  `thrift:"e,1" json:"e"`
}

func NewSearchDmpAnalysisTasksByStatusResult() *SearchDmpAnalysisTasksByStatusResult {
	return &SearchDmpAnalysisTasksByStatusResult{}
}

func (p *SearchDmpAnalysisTasksByStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDmpAnalysisTaskQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDmpAnalysisTasksByStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisTasksByStatusResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisTasksByStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisTasksByStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDmpAnalysisTasksByStatusResult(%+v)", *p)
}

type UpdateDmpAnalysisTaskStatusArgs struct {
	Header    *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskId    int32                 `thrift:"task_id,2" json:"task_id"`
	NewStatus int32                 `thrift:"new_status,3" json:"new_status"`
}

func NewUpdateDmpAnalysisTaskStatusArgs() *UpdateDmpAnalysisTaskStatusArgs {
	return &UpdateDmpAnalysisTaskStatusArgs{}
}

func (p *UpdateDmpAnalysisTaskStatusArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NewStatus = v
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateDmpAnalysisTaskStatus_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *UpdateDmpAnalysisTaskStatusArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:task_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:task_id: %s", p, err)
	}
	return err
}

func (p *UpdateDmpAnalysisTaskStatusArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("new_status", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:new_status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NewStatus)); err != nil {
		return fmt.Errorf("%T.new_status (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:new_status: %s", p, err)
	}
	return err
}

func (p *UpdateDmpAnalysisTaskStatusArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateDmpAnalysisTaskStatusArgs(%+v)", *p)
}

type UpdateDmpAnalysisTaskStatusResult struct {
	E *RTBStatsAnalysisException `thrift:"e,1" json:"e"`
}

func NewUpdateDmpAnalysisTaskStatusResult() *UpdateDmpAnalysisTaskStatusResult {
	return &UpdateDmpAnalysisTaskStatusResult{}
}

func (p *UpdateDmpAnalysisTaskStatusResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("updateDmpAnalysisTaskStatus_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *UpdateDmpAnalysisTaskStatusResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *UpdateDmpAnalysisTaskStatusResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("UpdateDmpAnalysisTaskStatusResult(%+v)", *p)
}

type AddDmpAnalysisTaskResultArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskId int32                 `thrift:"task_id,2" json:"task_id"`
	Result []*DmpAnalysisInfo    `thrift:"result,3" json:"result"`
}

func NewAddDmpAnalysisTaskResultArgs() *AddDmpAnalysisTaskResultArgs {
	return &AddDmpAnalysisTaskResultArgs{}
}

func (p *AddDmpAnalysisTaskResultArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*DmpAnalysisInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem40 := NewDmpAnalysisInfo()
		if err := _elem40.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem40)
		}
		p.Result = append(p.Result, _elem40)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addDmpAnalysisTaskResult_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AddDmpAnalysisTaskResultArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:task_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:task_id: %s", p, err)
	}
	return err
}

func (p *AddDmpAnalysisTaskResultArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:result: %s", p, err)
		}
	}
	return err
}

func (p *AddDmpAnalysisTaskResultArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddDmpAnalysisTaskResultArgs(%+v)", *p)
}

type AddDmpAnalysisTaskResultResult struct {
	E *RTBStatsAnalysisException `thrift:"e,1" json:"e"`
}

func NewAddDmpAnalysisTaskResultResult() *AddDmpAnalysisTaskResultResult {
	return &AddDmpAnalysisTaskResultResult{}
}

func (p *AddDmpAnalysisTaskResultResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("addDmpAnalysisTaskResult_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AddDmpAnalysisTaskResultResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AddDmpAnalysisTaskResultResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AddDmpAnalysisTaskResultResult(%+v)", *p)
}

type SearchDmpAnalysisInfoByTaskIdArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	TaskId int32                 `thrift:"task_id,2" json:"task_id"`
}

func NewSearchDmpAnalysisInfoByTaskIdArgs() *SearchDmpAnalysisInfoByTaskIdArgs {
	return &SearchDmpAnalysisInfoByTaskIdArgs{}
}

func (p *SearchDmpAnalysisInfoByTaskIdArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDmpAnalysisInfoByTaskId_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisInfoByTaskIdArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:task_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:task_id: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByTaskIdArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDmpAnalysisInfoByTaskIdArgs(%+v)", *p)
}

type SearchDmpAnalysisInfoByTaskIdResult struct {
	Success *DmpAnalysisInfoQueryResult `thrift:"success,0" json:"success"`
	E       *RTBStatsAnalysisException  `thrift:"e,1" json:"e"`
}

func NewSearchDmpAnalysisInfoByTaskIdResult() *SearchDmpAnalysisInfoByTaskIdResult {
	return &SearchDmpAnalysisInfoByTaskIdResult{}
}

func (p *SearchDmpAnalysisInfoByTaskIdResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDmpAnalysisInfoQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDmpAnalysisInfoByTaskId_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByTaskIdResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisInfoByTaskIdResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisInfoByTaskIdResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDmpAnalysisInfoByTaskIdResult(%+v)", *p)
}

type SearchDmpAnalysisInfoByParamsArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	AppId      int32                 `thrift:"app_id,2" json:"app_id"`
	ExchangeId int32                 `thrift:"exchange_id,3" json:"exchange_id"`
	TypeA1     int32                 `thrift:"type,4" json:"type"`
	StartTime  int64                 `thrift:"start_time,5" json:"start_time"`
	EndTime    int64                 `thrift:"end_time,6" json:"end_time"`
	Offset     int32                 `thrift:"offset,7" json:"offset"`
	Limit      int32                 `thrift:"limit,8" json:"limit"`
}

func NewSearchDmpAnalysisInfoByParamsArgs() *SearchDmpAnalysisInfoByParamsArgs {
	return &SearchDmpAnalysisInfoByParamsArgs{}
}

func (p *SearchDmpAnalysisInfoByParamsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDmpAnalysisInfoByParams_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:app_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:app_id: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exchange_id: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:type: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:start_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:start_time: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:end_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:end_time: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:offset: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:limit: %s", p, err)
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDmpAnalysisInfoByParamsArgs(%+v)", *p)
}

type SearchDmpAnalysisInfoByParamsResult struct {
	Success *DmpAnalysisInfoQueryResult `thrift:"success,0" json:"success"`
	E       *RTBStatsAnalysisException  `thrift:"e,1" json:"e"`
}

func NewSearchDmpAnalysisInfoByParamsResult() *SearchDmpAnalysisInfoByParamsResult {
	return &SearchDmpAnalysisInfoByParamsResult{}
}

func (p *SearchDmpAnalysisInfoByParamsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewDmpAnalysisInfoQueryResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("searchDmpAnalysisInfoByParams_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SearchDmpAnalysisInfoByParamsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SearchDmpAnalysisInfoByParamsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SearchDmpAnalysisInfoByParamsResult(%+v)", *p)
}

type ExecClickhouseSQLArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Sql    string                `thrift:"sql,2" json:"sql"`
}

func NewExecClickhouseSQLArgs() *ExecClickhouseSQLArgs {
	return &ExecClickhouseSQLArgs{}
}

func (p *ExecClickhouseSQLArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExecClickhouseSQLArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ExecClickhouseSQLArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sql = v
	}
	return nil
}

func (p *ExecClickhouseSQLArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("execClickhouseSQL_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExecClickhouseSQLArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ExecClickhouseSQLArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sql", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sql: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sql)); err != nil {
		return fmt.Errorf("%T.sql (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sql: %s", p, err)
	}
	return err
}

func (p *ExecClickhouseSQLArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecClickhouseSQLArgs(%+v)", *p)
}

type ExecClickhouseSQLResult struct {
	Success string                     `thrift:"success,0" json:"success"`
	E       *RTBStatsAnalysisException `thrift:"e,1" json:"e"`
}

func NewExecClickhouseSQLResult() *ExecClickhouseSQLResult {
	return &ExecClickhouseSQLResult{}
}

func (p *ExecClickhouseSQLResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRING {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ExecClickhouseSQLResult) readField0(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 0: %s", err)
	} else {
		p.Success = v
	}
	return nil
}

func (p *ExecClickhouseSQLResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewRTBStatsAnalysisException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *ExecClickhouseSQLResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("execClickhouseSQL_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ExecClickhouseSQLResult) writeField0(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("success", thrift.STRING, 0); err != nil {
		return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Success)); err != nil {
		return fmt.Errorf("%T.success (0) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 0:success: %s", p, err)
	}
	return err
}

func (p *ExecClickhouseSQLResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *ExecClickhouseSQLResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ExecClickhouseSQLResult(%+v)", *p)
}
