// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"rtb_stats_analysis"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addDmpAnalysisTask(RequestHeader header, DmpAnalysisTask task)")
	fmt.Fprintln(os.Stderr, "   getDmpAnalysisTasksByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  DmpAnalysisTaskQueryResult searchDmpAnalysisTasksByStatus(RequestHeader header,  status, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  void updateDmpAnalysisTaskStatus(RequestHeader header, i32 task_id, i32 new_status)")
	fmt.Fprintln(os.Stderr, "  void addDmpAnalysisTaskResult(RequestHeader header, i32 task_id,  result)")
	fmt.Fprintln(os.Stderr, "  DmpAnalysisInfoQueryResult searchDmpAnalysisInfoByTaskId(RequestHeader header, i32 task_id)")
	fmt.Fprintln(os.Stderr, "  DmpAnalysisInfoQueryResult searchDmpAnalysisInfoByParams(RequestHeader header, i32 app_id, i32 exchange_id, i32 type, i64 start_time, i64 end_time, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  string execClickhouseSQL(RequestHeader header, string sql)")
	fmt.Fprintln(os.Stderr, "  string getName()")
	fmt.Fprintln(os.Stderr, "  string getVersion()")
	fmt.Fprintln(os.Stderr, "  dm_status getStatus()")
	fmt.Fprintln(os.Stderr, "  string getStatusDetails()")
	fmt.Fprintln(os.Stderr, "   getCounters()")
	fmt.Fprintln(os.Stderr, "   getMapCounters()")
	fmt.Fprintln(os.Stderr, "  i64 getCounter(string key)")
	fmt.Fprintln(os.Stderr, "  void setOption(string key, string value)")
	fmt.Fprintln(os.Stderr, "  string getOption(string key)")
	fmt.Fprintln(os.Stderr, "   getOptions()")
	fmt.Fprintln(os.Stderr, "  string getCpuProfile(i32 profileDurationInSec)")
	fmt.Fprintln(os.Stderr, "  i64 aliveSince()")
	fmt.Fprintln(os.Stderr, "  void reinitialize()")
	fmt.Fprintln(os.Stderr, "  void shutdown()")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := rtb_stats_analysis.NewRTBStatsAnalysisServiceClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addDmpAnalysisTask":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddDmpAnalysisTask requires 2 args")
			flag.Usage()
		}
		arg41 := flag.Arg(1)
		mbTrans42 := thrift.NewTMemoryBufferLen(len(arg41))
		defer mbTrans42.Close()
		_, err43 := mbTrans42.WriteString(arg41)
		if err43 != nil {
			Usage()
			return
		}
		factory44 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt45 := factory44.GetProtocol(mbTrans42)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err46 := argvalue0.Read(jsProt45)
		if err46 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg47 := flag.Arg(2)
		mbTrans48 := thrift.NewTMemoryBufferLen(len(arg47))
		defer mbTrans48.Close()
		_, err49 := mbTrans48.WriteString(arg47)
		if err49 != nil {
			Usage()
			return
		}
		factory50 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt51 := factory50.GetProtocol(mbTrans48)
		argvalue1 := rtb_stats_analysis.NewDmpAnalysisTask()
		err52 := argvalue1.Read(jsProt51)
		if err52 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddDmpAnalysisTask(value0, value1))
		fmt.Print("\n")
		break
	case "getDmpAnalysisTasksByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDmpAnalysisTasksByIds requires 2 args")
			flag.Usage()
		}
		arg53 := flag.Arg(1)
		mbTrans54 := thrift.NewTMemoryBufferLen(len(arg53))
		defer mbTrans54.Close()
		_, err55 := mbTrans54.WriteString(arg53)
		if err55 != nil {
			Usage()
			return
		}
		factory56 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt57 := factory56.GetProtocol(mbTrans54)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err58 := argvalue0.Read(jsProt57)
		if err58 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg59 := flag.Arg(2)
		mbTrans60 := thrift.NewTMemoryBufferLen(len(arg59))
		defer mbTrans60.Close()
		_, err61 := mbTrans60.WriteString(arg59)
		if err61 != nil {
			Usage()
			return
		}
		factory62 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt63 := factory62.GetProtocol(mbTrans60)
		containerStruct1 := rtb_stats_analysis.NewGetDmpAnalysisTasksByIdsArgs()
		err64 := containerStruct1.ReadField2(jsProt63)
		if err64 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDmpAnalysisTasksByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchDmpAnalysisTasksByStatus":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "SearchDmpAnalysisTasksByStatus requires 4 args")
			flag.Usage()
		}
		arg65 := flag.Arg(1)
		mbTrans66 := thrift.NewTMemoryBufferLen(len(arg65))
		defer mbTrans66.Close()
		_, err67 := mbTrans66.WriteString(arg65)
		if err67 != nil {
			Usage()
			return
		}
		factory68 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt69 := factory68.GetProtocol(mbTrans66)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err70 := argvalue0.Read(jsProt69)
		if err70 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg71 := flag.Arg(2)
		mbTrans72 := thrift.NewTMemoryBufferLen(len(arg71))
		defer mbTrans72.Close()
		_, err73 := mbTrans72.WriteString(arg71)
		if err73 != nil {
			Usage()
			return
		}
		factory74 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt75 := factory74.GetProtocol(mbTrans72)
		containerStruct1 := rtb_stats_analysis.NewSearchDmpAnalysisTasksByStatusArgs()
		err76 := containerStruct1.ReadField2(jsProt75)
		if err76 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Status
		value1 := argvalue1
		tmp2, err77 := (strconv.Atoi(flag.Arg(3)))
		if err77 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err78 := (strconv.Atoi(flag.Arg(4)))
		if err78 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.SearchDmpAnalysisTasksByStatus(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "updateDmpAnalysisTaskStatus":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "UpdateDmpAnalysisTaskStatus requires 3 args")
			flag.Usage()
		}
		arg79 := flag.Arg(1)
		mbTrans80 := thrift.NewTMemoryBufferLen(len(arg79))
		defer mbTrans80.Close()
		_, err81 := mbTrans80.WriteString(arg79)
		if err81 != nil {
			Usage()
			return
		}
		factory82 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt83 := factory82.GetProtocol(mbTrans80)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err84 := argvalue0.Read(jsProt83)
		if err84 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err85 := (strconv.Atoi(flag.Arg(2)))
		if err85 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err86 := (strconv.Atoi(flag.Arg(3)))
		if err86 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.UpdateDmpAnalysisTaskStatus(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addDmpAnalysisTaskResult":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddDmpAnalysisTaskResult requires 3 args")
			flag.Usage()
		}
		arg87 := flag.Arg(1)
		mbTrans88 := thrift.NewTMemoryBufferLen(len(arg87))
		defer mbTrans88.Close()
		_, err89 := mbTrans88.WriteString(arg87)
		if err89 != nil {
			Usage()
			return
		}
		factory90 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt91 := factory90.GetProtocol(mbTrans88)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err92 := argvalue0.Read(jsProt91)
		if err92 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err93 := (strconv.Atoi(flag.Arg(2)))
		if err93 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg94 := flag.Arg(3)
		mbTrans95 := thrift.NewTMemoryBufferLen(len(arg94))
		defer mbTrans95.Close()
		_, err96 := mbTrans95.WriteString(arg94)
		if err96 != nil {
			Usage()
			return
		}
		factory97 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt98 := factory97.GetProtocol(mbTrans95)
		containerStruct2 := rtb_stats_analysis.NewAddDmpAnalysisTaskResultArgs()
		err99 := containerStruct2.ReadField3(jsProt98)
		if err99 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Result
		value2 := argvalue2
		fmt.Print(client.AddDmpAnalysisTaskResult(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchDmpAnalysisInfoByTaskId":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchDmpAnalysisInfoByTaskId requires 2 args")
			flag.Usage()
		}
		arg100 := flag.Arg(1)
		mbTrans101 := thrift.NewTMemoryBufferLen(len(arg100))
		defer mbTrans101.Close()
		_, err102 := mbTrans101.WriteString(arg100)
		if err102 != nil {
			Usage()
			return
		}
		factory103 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt104 := factory103.GetProtocol(mbTrans101)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err105 := argvalue0.Read(jsProt104)
		if err105 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err106 := (strconv.Atoi(flag.Arg(2)))
		if err106 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		fmt.Print(client.SearchDmpAnalysisInfoByTaskId(value0, value1))
		fmt.Print("\n")
		break
	case "searchDmpAnalysisInfoByParams":
		if flag.NArg()-1 != 8 {
			fmt.Fprintln(os.Stderr, "SearchDmpAnalysisInfoByParams requires 8 args")
			flag.Usage()
		}
		arg107 := flag.Arg(1)
		mbTrans108 := thrift.NewTMemoryBufferLen(len(arg107))
		defer mbTrans108.Close()
		_, err109 := mbTrans108.WriteString(arg107)
		if err109 != nil {
			Usage()
			return
		}
		factory110 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt111 := factory110.GetProtocol(mbTrans108)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err112 := argvalue0.Read(jsProt111)
		if err112 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err113 := (strconv.Atoi(flag.Arg(2)))
		if err113 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err114 := (strconv.Atoi(flag.Arg(3)))
		if err114 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err115 := (strconv.Atoi(flag.Arg(4)))
		if err115 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		argvalue4, err116 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err116 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		argvalue5, err117 := (strconv.ParseInt(flag.Arg(6), 10, 64))
		if err117 != nil {
			Usage()
			return
		}
		value5 := argvalue5
		tmp6, err118 := (strconv.Atoi(flag.Arg(7)))
		if err118 != nil {
			Usage()
			return
		}
		argvalue6 := int32(tmp6)
		value6 := argvalue6
		tmp7, err119 := (strconv.Atoi(flag.Arg(8)))
		if err119 != nil {
			Usage()
			return
		}
		argvalue7 := int32(tmp7)
		value7 := argvalue7
		fmt.Print(client.SearchDmpAnalysisInfoByParams(value0, value1, value2, value3, value4, value5, value6, value7))
		fmt.Print("\n")
		break
	case "execClickhouseSQL":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "ExecClickhouseSQL requires 2 args")
			flag.Usage()
		}
		arg120 := flag.Arg(1)
		mbTrans121 := thrift.NewTMemoryBufferLen(len(arg120))
		defer mbTrans121.Close()
		_, err122 := mbTrans121.WriteString(arg120)
		if err122 != nil {
			Usage()
			return
		}
		factory123 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt124 := factory123.GetProtocol(mbTrans121)
		argvalue0 := rtb_stats_analysis.NewRequestHeader()
		err125 := argvalue0.Read(jsProt124)
		if err125 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.ExecClickhouseSQL(value0, value1))
		fmt.Print("\n")
		break
	case "getName":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetName requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetName())
		fmt.Print("\n")
		break
	case "getVersion":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetVersion requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetVersion())
		fmt.Print("\n")
		break
	case "getStatus":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatus requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatus())
		fmt.Print("\n")
		break
	case "getStatusDetails":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetStatusDetails requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetStatusDetails())
		fmt.Print("\n")
		break
	case "getCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetCounters())
		fmt.Print("\n")
		break
	case "getMapCounters":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetMapCounters requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetMapCounters())
		fmt.Print("\n")
		break
	case "getCounter":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCounter requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetCounter(value0))
		fmt.Print("\n")
		break
	case "setOption":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SetOption requires 2 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		argvalue1 := flag.Arg(2)
		value1 := argvalue1
		fmt.Print(client.SetOption(value0, value1))
		fmt.Print("\n")
		break
	case "getOption":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetOption requires 1 args")
			flag.Usage()
		}
		argvalue0 := flag.Arg(1)
		value0 := argvalue0
		fmt.Print(client.GetOption(value0))
		fmt.Print("\n")
		break
	case "getOptions":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "GetOptions requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.GetOptions())
		fmt.Print("\n")
		break
	case "getCpuProfile":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetCpuProfile requires 1 args")
			flag.Usage()
		}
		tmp0, err131 := (strconv.Atoi(flag.Arg(1)))
		if err131 != nil {
			Usage()
			return
		}
		argvalue0 := int32(tmp0)
		value0 := argvalue0
		fmt.Print(client.GetCpuProfile(value0))
		fmt.Print("\n")
		break
	case "aliveSince":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "AliveSince requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.AliveSince())
		fmt.Print("\n")
		break
	case "reinitialize":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Reinitialize requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Reinitialize())
		fmt.Print("\n")
		break
	case "shutdown":
		if flag.NArg()-1 != 0 {
			fmt.Fprintln(os.Stderr, "Shutdown requires 0 args")
			flag.Usage()
		}
		fmt.Print(client.Shutdown())
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
