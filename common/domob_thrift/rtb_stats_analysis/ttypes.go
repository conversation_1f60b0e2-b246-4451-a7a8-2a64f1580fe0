// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package rtb_stats_analysis

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var GoUnusedProtection__ int

//统计分析服务代码
type RTBStatsAnalysisServiceCode int64

const (
	RTBStatsAnalysisServiceCode_SASC_OK                  RTBStatsAnalysisServiceCode = 10000
	RTBStatsAnalysisServiceCode_SASC_ERROR               RTBStatsAnalysisServiceCode = 20000
	RTBStatsAnalysisServiceCode_SASC_ERROR_PARAM_INVALID RTBStatsAnalysisServiceCode = 20100
	RTBStatsAnalysisServiceCode_SASC_ERROR_SYSTEM_ERROR  RTBStatsAnalysisServiceCode = 20200
)

func (p RTBStatsAnalysisServiceCode) String() string {
	switch p {
	case RTBStatsAnalysisServiceCode_SASC_OK:
		return "RTBStatsAnalysisServiceCode_SASC_OK"
	case RTBStatsAnalysisServiceCode_SASC_ERROR:
		return "RTBStatsAnalysisServiceCode_SASC_ERROR"
	case RTBStatsAnalysisServiceCode_SASC_ERROR_PARAM_INVALID:
		return "RTBStatsAnalysisServiceCode_SASC_ERROR_PARAM_INVALID"
	case RTBStatsAnalysisServiceCode_SASC_ERROR_SYSTEM_ERROR:
		return "RTBStatsAnalysisServiceCode_SASC_ERROR_SYSTEM_ERROR"
	}
	return "<UNSET>"
}

func RTBStatsAnalysisServiceCodeFromString(s string) (RTBStatsAnalysisServiceCode, error) {
	switch s {
	case "RTBStatsAnalysisServiceCode_SASC_OK":
		return RTBStatsAnalysisServiceCode_SASC_OK, nil
	case "RTBStatsAnalysisServiceCode_SASC_ERROR":
		return RTBStatsAnalysisServiceCode_SASC_ERROR, nil
	case "RTBStatsAnalysisServiceCode_SASC_ERROR_PARAM_INVALID":
		return RTBStatsAnalysisServiceCode_SASC_ERROR_PARAM_INVALID, nil
	case "RTBStatsAnalysisServiceCode_SASC_ERROR_SYSTEM_ERROR":
		return RTBStatsAnalysisServiceCode_SASC_ERROR_SYSTEM_ERROR, nil
	}
	return RTBStatsAnalysisServiceCode(math.MinInt32 - 1), fmt.Errorf("not a valid RTBStatsAnalysisServiceCode string")
}

type DmpAnalysisTask struct {
	Id                  int32    `thrift:"id,1" json:"id"`
	Name                string   `thrift:"name,2" json:"name"`
	TypeA1              int32    `thrift:"type,3" json:"type"`
	AppId               int32    `thrift:"app_id,4" json:"app_id"`
	ExchangeId          int32    `thrift:"exchange_id,5" json:"exchange_id"`
	ExchangeDmpId       int32    `thrift:"exchange_dmp_id,6" json:"exchange_dmp_id"`
	ExchangeAudienceIds []string `thrift:"exchange_audience_ids,7" json:"exchange_audience_ids"`
	StartTime           int64    `thrift:"start_time,8" json:"start_time"`
	EndTime             int64    `thrift:"end_time,9" json:"end_time"`
	Status              int32    `thrift:"status,10" json:"status"`
	Message             string   `thrift:"message,11" json:"message"`
	OperatorUid         int32    `thrift:"operator_uid,12" json:"operator_uid"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	CreateTime int64 `thrift:"create_time,20" json:"create_time"`
	LastUpdate int64 `thrift:"last_update,21" json:"last_update"`
}

func NewDmpAnalysisTask() *DmpAnalysisTask {
	return &DmpAnalysisTask{}
}

func (p *DmpAnalysisTask) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.STRING {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisTask) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TypeA1 = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ExchangeDmpId = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ExchangeAudienceIds = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.ExchangeAudienceIds = append(p.ExchangeAudienceIds, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DmpAnalysisTask) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.OperatorUid = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.CreateTime = v
	}
	return nil
}

func (p *DmpAnalysisTask) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.LastUpdate = v
	}
	return nil
}

func (p *DmpAnalysisTask) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpAnalysisTask"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisTask) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:name: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:type: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:app_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:app_id: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:exchange_id: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_dmp_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:exchange_dmp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeDmpId)); err != nil {
		return fmt.Errorf("%T.exchange_dmp_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:exchange_dmp_id: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField7(oprot thrift.TProtocol) (err error) {
	if p.ExchangeAudienceIds != nil {
		if err := oprot.WriteFieldBegin("exchange_audience_ids", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:exchange_audience_ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.ExchangeAudienceIds)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ExchangeAudienceIds {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:exchange_audience_ids: %s", p, err)
		}
	}
	return err
}

func (p *DmpAnalysisTask) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("start_time", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:start_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.start_time (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:start_time: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("end_time", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:end_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.end_time (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:end_time: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:status: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:message: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("operator_uid", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:operator_uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OperatorUid)); err != nil {
		return fmt.Errorf("%T.operator_uid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:operator_uid: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_time", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:create_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.create_time (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:create_time: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("last_update", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:last_update: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.last_update (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:last_update: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTask) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpAnalysisTask(%+v)", *p)
}

type DmpAnalysisInfo struct {
	ExchangeDmpId int32 `thrift:"exchange_dmp_id,1" json:"exchange_dmp_id"`
	AppId         int32 `thrift:"app_id,2" json:"app_id"`
	DailyUv       int64 `thrift:"daily_uv,3" json:"daily_uv"`
	TotalUv       int64 `thrift:"total_uv,4" json:"total_uv"`
	ImpUv         int64 `thrift:"imp_uv,5" json:"imp_uv"`
	Imp           int64 `thrift:"imp,6" json:"imp"`
	Clk           int64 `thrift:"clk,7" json:"clk"`
	Act           int64 `thrift:"act,8" json:"act"`
	Price         int64 `thrift:"price,9" json:"price"`
	Ctr           int32 `thrift:"ctr,10" json:"ctr"`
	Atr           int32 `thrift:"atr,11" json:"atr"`
	Pvr           int32 `thrift:"pvr,12" json:"pvr"`
	Cpa           int64 `thrift:"cpa,13" json:"cpa"`
}

func NewDmpAnalysisInfo() *DmpAnalysisInfo {
	return &DmpAnalysisInfo{}
}

func (p *DmpAnalysisInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ExchangeDmpId = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DailyUv = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TotalUv = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ImpUv = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Imp = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Clk = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Act = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Ctr = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Atr = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Pvr = v
	}
	return nil
}

func (p *DmpAnalysisInfo) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Cpa = v
	}
	return nil
}

func (p *DmpAnalysisInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpAnalysisInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_dmp_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:exchange_dmp_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeDmpId)); err != nil {
		return fmt.Errorf("%T.exchange_dmp_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:exchange_dmp_id: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:app_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:app_id: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("daily_uv", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:daily_uv: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyUv)); err != nil {
		return fmt.Errorf("%T.daily_uv (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:daily_uv: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_uv", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:total_uv: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalUv)); err != nil {
		return fmt.Errorf("%T.total_uv (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:total_uv: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp_uv", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:imp_uv: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ImpUv)); err != nil {
		return fmt.Errorf("%T.imp_uv (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:imp_uv: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imp", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:imp: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Imp)); err != nil {
		return fmt.Errorf("%T.imp (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:imp: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clk", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:clk: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Clk)); err != nil {
		return fmt.Errorf("%T.clk (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:clk: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("act", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:act: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Act)); err != nil {
		return fmt.Errorf("%T.act (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:act: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:price: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctr", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:ctr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Ctr)); err != nil {
		return fmt.Errorf("%T.ctr (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:ctr: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("atr", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:atr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Atr)); err != nil {
		return fmt.Errorf("%T.atr (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:atr: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pvr", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:pvr: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pvr)); err != nil {
		return fmt.Errorf("%T.pvr (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:pvr: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpa", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:cpa: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cpa)); err != nil {
		return fmt.Errorf("%T.cpa (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:cpa: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpAnalysisInfo(%+v)", *p)
}

type DmpAnalysisInfoQueryResult struct {
	TotalCount int32              `thrift:"total_count,1" json:"total_count"`
	MaxLimit   int32              `thrift:"max_limit,2" json:"max_limit"`
	Offset     int32              `thrift:"offset,3" json:"offset"`
	Limit      int32              `thrift:"limit,4" json:"limit"`
	Result     []*DmpAnalysisInfo `thrift:"result,5" json:"result"`
	Summary    *DmpAnalysisInfo   `thrift:"summary,6" json:"summary"`
}

func NewDmpAnalysisInfoQueryResult() *DmpAnalysisInfoQueryResult {
	return &DmpAnalysisInfoQueryResult{}
}

func (p *DmpAnalysisInfoQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*DmpAnalysisInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewDmpAnalysisInfo()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Result = append(p.Result, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) readField6(iprot thrift.TProtocol) error {
	p.Summary = NewDmpAnalysisInfo()
	if err := p.Summary.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Summary)
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpAnalysisInfoQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisInfoQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total_count: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfoQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:max_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.max_limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:max_limit: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfoQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfoQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisInfoQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *DmpAnalysisInfoQueryResult) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Summary != nil {
		if err := oprot.WriteFieldBegin("summary", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:summary: %s", p, err)
		}
		if err := p.Summary.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Summary)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:summary: %s", p, err)
		}
	}
	return err
}

func (p *DmpAnalysisInfoQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpAnalysisInfoQueryResult(%+v)", *p)
}

type DmpAnalysisTaskQueryResult struct {
	TotalCount int32              `thrift:"total_count,1" json:"total_count"`
	MaxLimit   int32              `thrift:"max_limit,2" json:"max_limit"`
	Offset     int32              `thrift:"offset,3" json:"offset"`
	Limit      int32              `thrift:"limit,4" json:"limit"`
	Result     []*DmpAnalysisTask `thrift:"result,5" json:"result"`
}

func NewDmpAnalysisTaskQueryResult() *DmpAnalysisTaskQueryResult {
	return &DmpAnalysisTaskQueryResult{}
}

func (p *DmpAnalysisTaskQueryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisTaskQueryResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *DmpAnalysisTaskQueryResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *DmpAnalysisTaskQueryResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *DmpAnalysisTaskQueryResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *DmpAnalysisTaskQueryResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*DmpAnalysisTask, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewDmpAnalysisTask()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.Result = append(p.Result, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *DmpAnalysisTaskQueryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DmpAnalysisTaskQueryResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DmpAnalysisTaskQueryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:total_count: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTaskQueryResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("max_limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:max_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.max_limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:max_limit: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTaskQueryResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTaskQueryResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *DmpAnalysisTaskQueryResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *DmpAnalysisTaskQueryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DmpAnalysisTaskQueryResult(%+v)", *p)
}

type RTBStatsAnalysisException struct {
	Code    RTBStatsAnalysisServiceCode `thrift:"code,1" json:"code"`
	Message string                      `thrift:"message,2" json:"message"`
}

func NewRTBStatsAnalysisException() *RTBStatsAnalysisException {
	return &RTBStatsAnalysisException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RTBStatsAnalysisException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *RTBStatsAnalysisException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RTBStatsAnalysisException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = RTBStatsAnalysisServiceCode(v)
	}
	return nil
}

func (p *RTBStatsAnalysisException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *RTBStatsAnalysisException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RTBStatsAnalysisException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RTBStatsAnalysisException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *RTBStatsAnalysisException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *RTBStatsAnalysisException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RTBStatsAnalysisException(%+v)", *p)
}
