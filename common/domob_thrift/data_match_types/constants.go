// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package data_match_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appinfo_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/rtb_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = rtb_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = appinfo_types.GoUnusedProtection__
var MatchLevelExchangeValid []int32

func init() {
	MatchLevelExchangeValid = []int32{
		107, 111, 202}

}
