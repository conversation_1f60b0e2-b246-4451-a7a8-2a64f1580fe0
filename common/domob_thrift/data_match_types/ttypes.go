// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package data_match_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/appinfo_types"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/rtb_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = rtb_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = dm303.GoUnusedProtection__
var _ = appinfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

type MatchType int64

const (
	MatchType_FULL_MATCH       MatchType = 0
	MatchType_ESTIMATION_MATCH MatchType = 1
)

func (p MatchType) String() string {
	switch p {
	case MatchType_FULL_MATCH:
		return "MatchType_FULL_MATCH"
	case MatchType_ESTIMATION_MATCH:
		return "MatchType_ESTIMATION_MATCH"
	}
	return "<UNSET>"
}

func MatchTypeFromString(s string) (MatchType, error) {
	switch s {
	case "MatchType_FULL_MATCH":
		return MatchType_FULL_MATCH, nil
	case "MatchType_ESTIMATION_MATCH":
		return MatchType_ESTIMATION_MATCH, nil
	}
	return MatchType(math.MinInt32 - 1), fmt.Errorf("not a valid MatchType string")
}

type StatusCode int64

const (
	StatusCode_SUCCESS               StatusCode = 0
	StatusCode_COMMIT_TASK_FAILURE   StatusCode = 101
	StatusCode_GET_TASK_RET_FAILURE  StatusCode = 102
	StatusCode_GET_TASK_MASK_FAILURE StatusCode = 103
	StatusCode_GET_TASK_FAILURE      StatusCode = 104
	StatusCode_GET_TASK_ID_NONE      StatusCode = 105
)

func (p StatusCode) String() string {
	switch p {
	case StatusCode_SUCCESS:
		return "StatusCode_SUCCESS"
	case StatusCode_COMMIT_TASK_FAILURE:
		return "StatusCode_COMMIT_TASK_FAILURE"
	case StatusCode_GET_TASK_RET_FAILURE:
		return "StatusCode_GET_TASK_RET_FAILURE"
	case StatusCode_GET_TASK_MASK_FAILURE:
		return "StatusCode_GET_TASK_MASK_FAILURE"
	case StatusCode_GET_TASK_FAILURE:
		return "StatusCode_GET_TASK_FAILURE"
	case StatusCode_GET_TASK_ID_NONE:
		return "StatusCode_GET_TASK_ID_NONE"
	}
	return "<UNSET>"
}

func StatusCodeFromString(s string) (StatusCode, error) {
	switch s {
	case "StatusCode_SUCCESS":
		return StatusCode_SUCCESS, nil
	case "StatusCode_COMMIT_TASK_FAILURE":
		return StatusCode_COMMIT_TASK_FAILURE, nil
	case "StatusCode_GET_TASK_RET_FAILURE":
		return StatusCode_GET_TASK_RET_FAILURE, nil
	case "StatusCode_GET_TASK_MASK_FAILURE":
		return StatusCode_GET_TASK_MASK_FAILURE, nil
	case "StatusCode_GET_TASK_FAILURE":
		return StatusCode_GET_TASK_FAILURE, nil
	case "StatusCode_GET_TASK_ID_NONE":
		return StatusCode_GET_TASK_ID_NONE, nil
	}
	return StatusCode(math.MinInt32 - 1), fmt.Errorf("not a valid StatusCode string")
}

type TaskStatus int64

const (
	TaskStatus_NEW_CREATE       TaskStatus = 1
	TaskStatus_RUNNING          TaskStatus = 2
	TaskStatus_COMPLETE_SUCESS  TaskStatus = 3
	TaskStatus_COMPLETE_FAILURE TaskStatus = 4
)

func (p TaskStatus) String() string {
	switch p {
	case TaskStatus_NEW_CREATE:
		return "TaskStatus_NEW_CREATE"
	case TaskStatus_RUNNING:
		return "TaskStatus_RUNNING"
	case TaskStatus_COMPLETE_SUCESS:
		return "TaskStatus_COMPLETE_SUCESS"
	case TaskStatus_COMPLETE_FAILURE:
		return "TaskStatus_COMPLETE_FAILURE"
	}
	return "<UNSET>"
}

func TaskStatusFromString(s string) (TaskStatus, error) {
	switch s {
	case "TaskStatus_NEW_CREATE":
		return TaskStatus_NEW_CREATE, nil
	case "TaskStatus_RUNNING":
		return TaskStatus_RUNNING, nil
	case "TaskStatus_COMPLETE_SUCESS":
		return TaskStatus_COMPLETE_SUCESS, nil
	case "TaskStatus_COMPLETE_FAILURE":
		return TaskStatus_COMPLETE_FAILURE, nil
	}
	return TaskStatus(math.MinInt32 - 1), fmt.Errorf("not a valid TaskStatus string")
}

type TaskPriority int64

const (
	TaskPriority_IMPORTANT TaskPriority = 1
	TaskPriority_NORMAL    TaskPriority = 2
)

func (p TaskPriority) String() string {
	switch p {
	case TaskPriority_IMPORTANT:
		return "TaskPriority_IMPORTANT"
	case TaskPriority_NORMAL:
		return "TaskPriority_NORMAL"
	}
	return "<UNSET>"
}

func TaskPriorityFromString(s string) (TaskPriority, error) {
	switch s {
	case "TaskPriority_IMPORTANT":
		return TaskPriority_IMPORTANT, nil
	case "TaskPriority_NORMAL":
		return TaskPriority_NORMAL, nil
	}
	return TaskPriority(math.MinInt32 - 1), fmt.Errorf("not a valid TaskPriority string")
}

type TaskDataType int64

const (
	TaskDataType_FROM_CROWD  TaskDataType = 1
	TaskDataType_FROM_UPLOAD TaskDataType = 2
)

func (p TaskDataType) String() string {
	switch p {
	case TaskDataType_FROM_CROWD:
		return "TaskDataType_FROM_CROWD"
	case TaskDataType_FROM_UPLOAD:
		return "TaskDataType_FROM_UPLOAD"
	}
	return "<UNSET>"
}

func TaskDataTypeFromString(s string) (TaskDataType, error) {
	switch s {
	case "TaskDataType_FROM_CROWD":
		return TaskDataType_FROM_CROWD, nil
	case "TaskDataType_FROM_UPLOAD":
		return TaskDataType_FROM_UPLOAD, nil
	}
	return TaskDataType(math.MinInt32 - 1), fmt.Errorf("not a valid TaskDataType string")
}

type TaskDataProper int64

const (
	TaskDataProper_UNKNOWN  TaskDataProper = 0
	TaskDataProper_POSITIVE TaskDataProper = 1
	TaskDataProper_NEGATIVE TaskDataProper = 2
)

func (p TaskDataProper) String() string {
	switch p {
	case TaskDataProper_UNKNOWN:
		return "TaskDataProper_UNKNOWN"
	case TaskDataProper_POSITIVE:
		return "TaskDataProper_POSITIVE"
	case TaskDataProper_NEGATIVE:
		return "TaskDataProper_NEGATIVE"
	}
	return "<UNSET>"
}

func TaskDataProperFromString(s string) (TaskDataProper, error) {
	switch s {
	case "TaskDataProper_UNKNOWN":
		return TaskDataProper_UNKNOWN, nil
	case "TaskDataProper_POSITIVE":
		return TaskDataProper_POSITIVE, nil
	case "TaskDataProper_NEGATIVE":
		return TaskDataProper_NEGATIVE, nil
	}
	return TaskDataProper(math.MinInt32 - 1), fmt.Errorf("not a valid TaskDataProper string")
}

type TaskDataRelateProductType int64

const (
	TaskDataRelateProductType_UNKNOWN       TaskDataRelateProductType = 0
	TaskDataRelateProductType_INNER_PRODUCT TaskDataRelateProductType = 1
	TaskDataRelateProductType_OUTER_PRODUCT TaskDataRelateProductType = 2
)

func (p TaskDataRelateProductType) String() string {
	switch p {
	case TaskDataRelateProductType_UNKNOWN:
		return "TaskDataRelateProductType_UNKNOWN"
	case TaskDataRelateProductType_INNER_PRODUCT:
		return "TaskDataRelateProductType_INNER_PRODUCT"
	case TaskDataRelateProductType_OUTER_PRODUCT:
		return "TaskDataRelateProductType_OUTER_PRODUCT"
	}
	return "<UNSET>"
}

func TaskDataRelateProductTypeFromString(s string) (TaskDataRelateProductType, error) {
	switch s {
	case "TaskDataRelateProductType_UNKNOWN":
		return TaskDataRelateProductType_UNKNOWN, nil
	case "TaskDataRelateProductType_INNER_PRODUCT":
		return TaskDataRelateProductType_INNER_PRODUCT, nil
	case "TaskDataRelateProductType_OUTER_PRODUCT":
		return TaskDataRelateProductType_OUTER_PRODUCT, nil
	}
	return TaskDataRelateProductType(math.MinInt32 - 1), fmt.Errorf("not a valid TaskDataRelateProductType string")
}

type MatchDataType int64

const (
	MatchDataType_MATCH_RTB_REQ MatchDataType = 1
	MatchDataType_MATCH_RTB_ACT MatchDataType = 2
	MatchDataType_MATCH_DOS_ACT MatchDataType = 3
	MatchDataType_MATCH_RTB_CLK MatchDataType = 4
)

func (p MatchDataType) String() string {
	switch p {
	case MatchDataType_MATCH_RTB_REQ:
		return "MatchDataType_MATCH_RTB_REQ"
	case MatchDataType_MATCH_RTB_ACT:
		return "MatchDataType_MATCH_RTB_ACT"
	case MatchDataType_MATCH_DOS_ACT:
		return "MatchDataType_MATCH_DOS_ACT"
	case MatchDataType_MATCH_RTB_CLK:
		return "MatchDataType_MATCH_RTB_CLK"
	}
	return "<UNSET>"
}

func MatchDataTypeFromString(s string) (MatchDataType, error) {
	switch s {
	case "MatchDataType_MATCH_RTB_REQ":
		return MatchDataType_MATCH_RTB_REQ, nil
	case "MatchDataType_MATCH_RTB_ACT":
		return MatchDataType_MATCH_RTB_ACT, nil
	case "MatchDataType_MATCH_DOS_ACT":
		return MatchDataType_MATCH_DOS_ACT, nil
	case "MatchDataType_MATCH_RTB_CLK":
		return MatchDataType_MATCH_RTB_CLK, nil
	}
	return MatchDataType(math.MinInt32 - 1), fmt.Errorf("not a valid MatchDataType string")
}

type DataMatchException struct {
	Code        StatusCode `thrift:"code,1" json:"code"`
	CodeMessage string     `thrift:"code_message,2" json:"code_message"`
}

func NewDataMatchException() *DataMatchException {
	return &DataMatchException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DataMatchException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *DataMatchException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DataMatchException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = StatusCode(v)
	}
	return nil
}

func (p *DataMatchException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CodeMessage = v
	}
	return nil
}

func (p *DataMatchException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DataMatchException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DataMatchException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *DataMatchException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("code_message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:code_message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CodeMessage)); err != nil {
		return fmt.Errorf("%T.code_message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:code_message: %s", p, err)
	}
	return err
}

func (p *DataMatchException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DataMatchException(%+v)", *p)
}

type TaskDataRelateProduct struct {
	ProductType TaskDataRelateProductType `thrift:"product_type,1" json:"product_type"`
	Appid       int64                     `thrift:"appid,2" json:"appid"`
	Info        string                    `thrift:"info,3" json:"info"`
}

func NewTaskDataRelateProduct() *TaskDataRelateProduct {
	return &TaskDataRelateProduct{
		ProductType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TaskDataRelateProduct) IsSetProductType() bool {
	return int64(p.ProductType) != math.MinInt32-1
}

func (p *TaskDataRelateProduct) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskDataRelateProduct) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ProductType = TaskDataRelateProductType(v)
	}
	return nil
}

func (p *TaskDataRelateProduct) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *TaskDataRelateProduct) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Info = v
	}
	return nil
}

func (p *TaskDataRelateProduct) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskDataRelateProduct"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskDataRelateProduct) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetProductType() {
		if err := oprot.WriteFieldBegin("product_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:product_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ProductType)); err != nil {
			return fmt.Errorf("%T.product_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:product_type: %s", p, err)
		}
	}
	return err
}

func (p *TaskDataRelateProduct) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:appid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:appid: %s", p, err)
	}
	return err
}

func (p *TaskDataRelateProduct) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("info", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:info: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Info)); err != nil {
		return fmt.Errorf("%T.info (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:info: %s", p, err)
	}
	return err
}

func (p *TaskDataRelateProduct) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskDataRelateProduct(%+v)", *p)
}

type TaskDataBasicInfo struct {
	ActType        appinfo_types.ActType    `thrift:"act_type,1" json:"act_type"`
	Proper         TaskDataProper           `thrift:"proper,2" json:"proper"`
	RelateProducts []*TaskDataRelateProduct `thrift:"relate_products,3" json:"relate_products"`
}

func NewTaskDataBasicInfo() *TaskDataBasicInfo {
	return &TaskDataBasicInfo{
		ActType: math.MinInt32 - 1, // unset sentinal value

		Proper: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TaskDataBasicInfo) IsSetActType() bool {
	return int64(p.ActType) != math.MinInt32-1
}

func (p *TaskDataBasicInfo) IsSetProper() bool {
	return int64(p.Proper) != math.MinInt32-1
}

func (p *TaskDataBasicInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskDataBasicInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ActType = appinfo_types.ActType(v)
	}
	return nil
}

func (p *TaskDataBasicInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Proper = TaskDataProper(v)
	}
	return nil
}

func (p *TaskDataBasicInfo) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RelateProducts = make([]*TaskDataRelateProduct, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewTaskDataRelateProduct()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.RelateProducts = append(p.RelateProducts, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *TaskDataBasicInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskDataBasicInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskDataBasicInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetActType() {
		if err := oprot.WriteFieldBegin("act_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:act_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ActType)); err != nil {
			return fmt.Errorf("%T.act_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:act_type: %s", p, err)
		}
	}
	return err
}

func (p *TaskDataBasicInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetProper() {
		if err := oprot.WriteFieldBegin("proper", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:proper: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Proper)); err != nil {
			return fmt.Errorf("%T.proper (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:proper: %s", p, err)
		}
	}
	return err
}

func (p *TaskDataBasicInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if p.RelateProducts != nil {
		if err := oprot.WriteFieldBegin("relate_products", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:relate_products: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.RelateProducts)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RelateProducts {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:relate_products: %s", p, err)
		}
	}
	return err
}

func (p *TaskDataBasicInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskDataBasicInfo(%+v)", *p)
}

type TaskData struct {
	TaskdataType TaskDataType       `thrift:"taskdata_type,1" json:"taskdata_type"`
	PeopleId     int64              `thrift:"people_id,2" json:"people_id"`
	IdfaPath     string             `thrift:"idfa_path,3" json:"idfa_path"`
	ImeiPath     string             `thrift:"imei_path,4" json:"imei_path"`
	Md5Path      string             `thrift:"md5_path,5" json:"md5_path"`
	BasicInfo    *TaskDataBasicInfo `thrift:"basic_info,6" json:"basic_info"`
}

func NewTaskData() *TaskData {
	return &TaskData{
		TaskdataType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *TaskData) IsSetTaskdataType() bool {
	return int64(p.TaskdataType) != math.MinInt32-1
}

func (p *TaskData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *TaskData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TaskdataType = TaskDataType(v)
	}
	return nil
}

func (p *TaskData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PeopleId = v
	}
	return nil
}

func (p *TaskData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IdfaPath = v
	}
	return nil
}

func (p *TaskData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ImeiPath = v
	}
	return nil
}

func (p *TaskData) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Md5Path = v
	}
	return nil
}

func (p *TaskData) readField6(iprot thrift.TProtocol) error {
	p.BasicInfo = NewTaskDataBasicInfo()
	if err := p.BasicInfo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.BasicInfo)
	}
	return nil
}

func (p *TaskData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("TaskData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *TaskData) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskdataType() {
		if err := oprot.WriteFieldBegin("taskdata_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:taskdata_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TaskdataType)); err != nil {
			return fmt.Errorf("%T.taskdata_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:taskdata_type: %s", p, err)
		}
	}
	return err
}

func (p *TaskData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("people_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:people_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PeopleId)); err != nil {
		return fmt.Errorf("%T.people_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:people_id: %s", p, err)
	}
	return err
}

func (p *TaskData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idfa_path", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:idfa_path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.IdfaPath)); err != nil {
		return fmt.Errorf("%T.idfa_path (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:idfa_path: %s", p, err)
	}
	return err
}

func (p *TaskData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei_path", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:imei_path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImeiPath)); err != nil {
		return fmt.Errorf("%T.imei_path (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:imei_path: %s", p, err)
	}
	return err
}

func (p *TaskData) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("md5_path", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:md5_path: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Md5Path)); err != nil {
		return fmt.Errorf("%T.md5_path (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:md5_path: %s", p, err)
	}
	return err
}

func (p *TaskData) writeField6(oprot thrift.TProtocol) (err error) {
	if p.BasicInfo != nil {
		if err := oprot.WriteFieldBegin("basic_info", thrift.STRUCT, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:basic_info: %s", p, err)
		}
		if err := p.BasicInfo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.BasicInfo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:basic_info: %s", p, err)
		}
	}
	return err
}

func (p *TaskData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("TaskData(%+v)", *p)
}

type MatchData struct {
	MatchdataType MatchDataType `thrift:"matchdata_type,1" json:"matchdata_type"`
	SponsorId     int32         `thrift:"sponsor_id,2" json:"sponsor_id"`
	Account       string        `thrift:"account,3" json:"account"`
	DosPlanId     int32         `thrift:"dos_plan_id,4" json:"dos_plan_id"`
}

func NewMatchData() *MatchData {
	return &MatchData{
		MatchdataType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *MatchData) IsSetMatchdataType() bool {
	return int64(p.MatchdataType) != math.MinInt32-1
}

func (p *MatchData) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *MatchData) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.MatchdataType = MatchDataType(v)
	}
	return nil
}

func (p *MatchData) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *MatchData) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *MatchData) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DosPlanId = v
	}
	return nil
}

func (p *MatchData) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("MatchData"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *MatchData) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchdataType() {
		if err := oprot.WriteFieldBegin("matchdata_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:matchdata_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MatchdataType)); err != nil {
			return fmt.Errorf("%T.matchdata_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:matchdata_type: %s", p, err)
		}
	}
	return err
}

func (p *MatchData) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:sponsor_id: %s", p, err)
	}
	return err
}

func (p *MatchData) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:account: %s", p, err)
	}
	return err
}

func (p *MatchData) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dos_plan_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dos_plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DosPlanId)); err != nil {
		return fmt.Errorf("%T.dos_plan_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dos_plan_id: %s", p, err)
	}
	return err
}

func (p *MatchData) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("MatchData(%+v)", *p)
}

type DateSpan struct {
	DateBegin int32 `thrift:"date_begin,1" json:"date_begin"`
	DateEnd   int32 `thrift:"date_end,2" json:"date_end"`
}

func NewDateSpan() *DateSpan {
	return &DateSpan{}
}

func (p *DateSpan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DateSpan) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.DateBegin = v
	}
	return nil
}

func (p *DateSpan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DateEnd = v
	}
	return nil
}

func (p *DateSpan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DateSpan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DateSpan) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("date_begin", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:date_begin: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DateBegin)); err != nil {
		return fmt.Errorf("%T.date_begin (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:date_begin: %s", p, err)
	}
	return err
}

func (p *DateSpan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("date_end", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:date_end: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DateEnd)); err != nil {
		return fmt.Errorf("%T.date_end (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:date_end: %s", p, err)
	}
	return err
}

func (p *DateSpan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DateSpan(%+v)", *p)
}

type Task struct {
	TaskName     string       `thrift:"task_name,1" json:"task_name"`
	TaskId       int32        `thrift:"task_id,2" json:"task_id"`
	TaskDatespan *DateSpan    `thrift:"task_datespan,3" json:"task_datespan"`
	TaskDatas    []*TaskData  `thrift:"task_datas,4" json:"task_datas"`
	MatchDatas   []*MatchData `thrift:"match_datas,5" json:"match_datas"`
	TaskStatus   TaskStatus   `thrift:"task_status,6" json:"task_status"`
	CreateTs     int32        `thrift:"create_ts,7" json:"create_ts"`
	TaskPriority TaskPriority `thrift:"task_priority,8" json:"task_priority"`
	Creator      string       `thrift:"creator,9" json:"creator"`
	MatchType    MatchType    `thrift:"match_type,10" json:"match_type"`
}

func NewTask() *Task {
	return &Task{
		TaskStatus: math.MinInt32 - 1, // unset sentinal value

		TaskPriority: math.MinInt32 - 1, // unset sentinal value

		MatchType: 0,
	}
}

func (p *Task) IsSetTaskStatus() bool {
	return int64(p.TaskStatus) != math.MinInt32-1
}

func (p *Task) IsSetTaskPriority() bool {
	return int64(p.TaskPriority) != math.MinInt32-1
}

func (p *Task) IsSetMatchType() bool {
	return int64(p.MatchType) != math.MinInt32-1
}

func (p *Task) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Task) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TaskName = v
	}
	return nil
}

func (p *Task) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *Task) readField3(iprot thrift.TProtocol) error {
	p.TaskDatespan = NewDateSpan()
	if err := p.TaskDatespan.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.TaskDatespan)
	}
	return nil
}

func (p *Task) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TaskDatas = make([]*TaskData, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewTaskData()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.TaskDatas = append(p.TaskDatas, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Task) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MatchDatas = make([]*MatchData, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewMatchData()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.MatchDatas = append(p.MatchDatas, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Task) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TaskStatus = TaskStatus(v)
	}
	return nil
}

func (p *Task) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CreateTs = v
	}
	return nil
}

func (p *Task) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.TaskPriority = TaskPriority(v)
	}
	return nil
}

func (p *Task) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Creator = v
	}
	return nil
}

func (p *Task) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.MatchType = MatchType(v)
	}
	return nil
}

func (p *Task) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Task"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Task) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:task_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.TaskName)); err != nil {
		return fmt.Errorf("%T.task_name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:task_name: %s", p, err)
	}
	return err
}

func (p *Task) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:task_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:task_id: %s", p, err)
	}
	return err
}

func (p *Task) writeField3(oprot thrift.TProtocol) (err error) {
	if p.TaskDatespan != nil {
		if err := oprot.WriteFieldBegin("task_datespan", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:task_datespan: %s", p, err)
		}
		if err := p.TaskDatespan.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.TaskDatespan)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:task_datespan: %s", p, err)
		}
	}
	return err
}

func (p *Task) writeField4(oprot thrift.TProtocol) (err error) {
	if p.TaskDatas != nil {
		if err := oprot.WriteFieldBegin("task_datas", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:task_datas: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.TaskDatas)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TaskDatas {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:task_datas: %s", p, err)
		}
	}
	return err
}

func (p *Task) writeField5(oprot thrift.TProtocol) (err error) {
	if p.MatchDatas != nil {
		if err := oprot.WriteFieldBegin("match_datas", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:match_datas: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.MatchDatas)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MatchDatas {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:match_datas: %s", p, err)
		}
	}
	return err
}

func (p *Task) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskStatus() {
		if err := oprot.WriteFieldBegin("task_status", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:task_status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TaskStatus)); err != nil {
			return fmt.Errorf("%T.task_status (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:task_status: %s", p, err)
		}
	}
	return err
}

func (p *Task) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("create_ts", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:create_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreateTs)); err != nil {
		return fmt.Errorf("%T.create_ts (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:create_ts: %s", p, err)
	}
	return err
}

func (p *Task) writeField8(oprot thrift.TProtocol) (err error) {
	if p.IsSetTaskPriority() {
		if err := oprot.WriteFieldBegin("task_priority", thrift.I32, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:task_priority: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TaskPriority)); err != nil {
			return fmt.Errorf("%T.task_priority (8) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:task_priority: %s", p, err)
		}
	}
	return err
}

func (p *Task) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creator", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:creator: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Creator)); err != nil {
		return fmt.Errorf("%T.creator (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:creator: %s", p, err)
	}
	return err
}

func (p *Task) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetMatchType() {
		if err := oprot.WriteFieldBegin("match_type", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:match_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.MatchType)); err != nil {
			return fmt.Errorf("%T.match_type (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:match_type: %s", p, err)
		}
	}
	return err
}

func (p *Task) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Task(%+v)", *p)
}

type Tasks struct {
	Tasks []*Task `thrift:"tasks,1" json:"tasks"`
}

func NewTasks() *Tasks {
	return &Tasks{}
}

func (p *Tasks) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Tasks) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Tasks = make([]*Task, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewTask()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Tasks = append(p.Tasks, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *Tasks) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Tasks"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Tasks) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Tasks != nil {
		if err := oprot.WriteFieldBegin("tasks", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:tasks: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Tasks)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Tasks {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:tasks: %s", p, err)
		}
	}
	return err
}

func (p *Tasks) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Tasks(%+v)", *p)
}

type CounterExchange struct {
	TaskId            int32  `thrift:"task_id,1" json:"task_id"`
	ExchangeId        int32  `thrift:"exchange_id,2" json:"exchange_id"`
	ExchangeName      string `thrift:"exchange_name,3" json:"exchange_name"`
	TotalCount        int32  `thrift:"total_count,4" json:"total_count"`
	MatchCount        int32  `thrift:"match_count,5" json:"match_count"`
	IosTotalCount     int32  `thrift:"ios_total_count,6" json:"ios_total_count"`
	IosMatchCount     int32  `thrift:"ios_match_count,7" json:"ios_match_count"`
	AndroidTotalCount int32  `thrift:"android_total_count,8" json:"android_total_count"`
	AndroidMatchCount int32  `thrift:"android_match_count,9" json:"android_match_count"`
}

func NewCounterExchange() *CounterExchange {
	return &CounterExchange{}
}

func (p *CounterExchange) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CounterExchange) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *CounterExchange) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *CounterExchange) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExchangeName = v
	}
	return nil
}

func (p *CounterExchange) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *CounterExchange) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MatchCount = v
	}
	return nil
}

func (p *CounterExchange) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IosTotalCount = v
	}
	return nil
}

func (p *CounterExchange) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.IosMatchCount = v
	}
	return nil
}

func (p *CounterExchange) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AndroidTotalCount = v
	}
	return nil
}

func (p *CounterExchange) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AndroidMatchCount = v
	}
	return nil
}

func (p *CounterExchange) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CounterExchange"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CounterExchange) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:task_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:task_id: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:exchange_id: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exchange_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExchangeName)); err != nil {
		return fmt.Errorf("%T.exchange_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exchange_name: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:total_count: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("match_count", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MatchCount)); err != nil {
		return fmt.Errorf("%T.match_count (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:match_count: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_total_count", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ios_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosTotalCount)); err != nil {
		return fmt.Errorf("%T.ios_total_count (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ios_total_count: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_match_count", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ios_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosMatchCount)); err != nil {
		return fmt.Errorf("%T.ios_match_count (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ios_match_count: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_total_count", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:android_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidTotalCount)); err != nil {
		return fmt.Errorf("%T.android_total_count (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:android_total_count: %s", p, err)
	}
	return err
}

func (p *CounterExchange) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_match_count", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:android_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidMatchCount)); err != nil {
		return fmt.Errorf("%T.android_match_count (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:android_match_count: %s", p, err)
	}
	return err
}

func (p *CounterExchange) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CounterExchange(%+v)", *p)
}

type RetExchange struct {
	CounterList []*CounterExchange `thrift:"counter_list,1" json:"counter_list"`
}

func NewRetExchange() *RetExchange {
	return &RetExchange{}
}

func (p *RetExchange) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetExchange) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CounterList = make([]*CounterExchange, 0, size)
	for i := 0; i < size; i++ {
		_elem4 := NewCounterExchange()
		if err := _elem4.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem4)
		}
		p.CounterList = append(p.CounterList, _elem4)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RetExchange) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RetExchange"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetExchange) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CounterList != nil {
		if err := oprot.WriteFieldBegin("counter_list", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:counter_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CounterList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CounterList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:counter_list: %s", p, err)
		}
	}
	return err
}

func (p *RetExchange) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetExchange(%+v)", *p)
}

type CounterMedia struct {
	TaskId            int32  `thrift:"task_id,1" json:"task_id"`
	ExchangeId        int32  `thrift:"exchange_id,2" json:"exchange_id"`
	ExchangeName      string `thrift:"exchange_name,3" json:"exchange_name"`
	DmMediaId         int32  `thrift:"dm_media_id,4" json:"dm_media_id"`
	MediaName         string `thrift:"media_name,5" json:"media_name"`
	TotalCount        int32  `thrift:"total_count,6" json:"total_count"`
	MatchCount        int32  `thrift:"match_count,7" json:"match_count"`
	IosTotalCount     int32  `thrift:"ios_total_count,8" json:"ios_total_count"`
	IosMatchCount     int32  `thrift:"ios_match_count,9" json:"ios_match_count"`
	AndroidTotalCount int32  `thrift:"android_total_count,10" json:"android_total_count"`
	AndroidMatchCount int32  `thrift:"android_match_count,11" json:"android_match_count"`
}

func NewCounterMedia() *CounterMedia {
	return &CounterMedia{}
}

func (p *CounterMedia) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CounterMedia) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *CounterMedia) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *CounterMedia) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ExchangeName = v
	}
	return nil
}

func (p *CounterMedia) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DmMediaId = v
	}
	return nil
}

func (p *CounterMedia) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MediaName = v
	}
	return nil
}

func (p *CounterMedia) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *CounterMedia) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.MatchCount = v
	}
	return nil
}

func (p *CounterMedia) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.IosTotalCount = v
	}
	return nil
}

func (p *CounterMedia) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.IosMatchCount = v
	}
	return nil
}

func (p *CounterMedia) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AndroidTotalCount = v
	}
	return nil
}

func (p *CounterMedia) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AndroidMatchCount = v
	}
	return nil
}

func (p *CounterMedia) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CounterMedia"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CounterMedia) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:task_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:task_id: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:exchange_id: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:exchange_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExchangeName)); err != nil {
		return fmt.Errorf("%T.exchange_name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:exchange_name: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dm_media_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dm_media_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DmMediaId)); err != nil {
		return fmt.Errorf("%T.dm_media_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dm_media_id: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("media_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:media_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.MediaName)); err != nil {
		return fmt.Errorf("%T.media_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:media_name: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:total_count: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("match_count", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MatchCount)); err != nil {
		return fmt.Errorf("%T.match_count (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:match_count: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_total_count", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:ios_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosTotalCount)); err != nil {
		return fmt.Errorf("%T.ios_total_count (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:ios_total_count: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_match_count", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:ios_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosMatchCount)); err != nil {
		return fmt.Errorf("%T.ios_match_count (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:ios_match_count: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_total_count", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:android_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidTotalCount)); err != nil {
		return fmt.Errorf("%T.android_total_count (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:android_total_count: %s", p, err)
	}
	return err
}

func (p *CounterMedia) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_match_count", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:android_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidMatchCount)); err != nil {
		return fmt.Errorf("%T.android_match_count (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:android_match_count: %s", p, err)
	}
	return err
}

func (p *CounterMedia) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CounterMedia(%+v)", *p)
}

type RetMedia struct {
	CounterList []*CounterMedia `thrift:"counter_list,1" json:"counter_list"`
}

func NewRetMedia() *RetMedia {
	return &RetMedia{}
}

func (p *RetMedia) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetMedia) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CounterList = make([]*CounterMedia, 0, size)
	for i := 0; i < size; i++ {
		_elem5 := NewCounterMedia()
		if err := _elem5.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem5)
		}
		p.CounterList = append(p.CounterList, _elem5)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RetMedia) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RetMedia"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetMedia) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CounterList != nil {
		if err := oprot.WriteFieldBegin("counter_list", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:counter_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CounterList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CounterList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:counter_list: %s", p, err)
		}
	}
	return err
}

func (p *RetMedia) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetMedia(%+v)", *p)
}

type CounterDos struct {
	TaskId            int32 `thrift:"task_id,1" json:"task_id"`
	PlanId            int32 `thrift:"plan_id,2" json:"plan_id"`
	TotalCount        int32 `thrift:"total_count,3" json:"total_count"`
	MatchCount        int32 `thrift:"match_count,4" json:"match_count"`
	IosTotalCount     int32 `thrift:"ios_total_count,5" json:"ios_total_count"`
	IosMatchCount     int32 `thrift:"ios_match_count,6" json:"ios_match_count"`
	AndroidTotalCount int32 `thrift:"android_total_count,7" json:"android_total_count"`
	AndroidMatchCount int32 `thrift:"android_match_count,8" json:"android_match_count"`
}

func NewCounterDos() *CounterDos {
	return &CounterDos{}
}

func (p *CounterDos) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CounterDos) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TaskId = v
	}
	return nil
}

func (p *CounterDos) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PlanId = v
	}
	return nil
}

func (p *CounterDos) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *CounterDos) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MatchCount = v
	}
	return nil
}

func (p *CounterDos) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IosTotalCount = v
	}
	return nil
}

func (p *CounterDos) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IosMatchCount = v
	}
	return nil
}

func (p *CounterDos) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AndroidTotalCount = v
	}
	return nil
}

func (p *CounterDos) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AndroidMatchCount = v
	}
	return nil
}

func (p *CounterDos) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CounterDos"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CounterDos) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("task_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:task_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskId)); err != nil {
		return fmt.Errorf("%T.task_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:task_id: %s", p, err)
	}
	return err
}

func (p *CounterDos) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("plan_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:plan_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlanId)); err != nil {
		return fmt.Errorf("%T.plan_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:plan_id: %s", p, err)
	}
	return err
}

func (p *CounterDos) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:total_count: %s", p, err)
	}
	return err
}

func (p *CounterDos) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("match_count", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MatchCount)); err != nil {
		return fmt.Errorf("%T.match_count (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:match_count: %s", p, err)
	}
	return err
}

func (p *CounterDos) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_total_count", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ios_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosTotalCount)); err != nil {
		return fmt.Errorf("%T.ios_total_count (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ios_total_count: %s", p, err)
	}
	return err
}

func (p *CounterDos) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_match_count", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ios_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosMatchCount)); err != nil {
		return fmt.Errorf("%T.ios_match_count (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ios_match_count: %s", p, err)
	}
	return err
}

func (p *CounterDos) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_total_count", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:android_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidTotalCount)); err != nil {
		return fmt.Errorf("%T.android_total_count (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:android_total_count: %s", p, err)
	}
	return err
}

func (p *CounterDos) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_match_count", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:android_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidMatchCount)); err != nil {
		return fmt.Errorf("%T.android_match_count (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:android_match_count: %s", p, err)
	}
	return err
}

func (p *CounterDos) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CounterDos(%+v)", *p)
}

type RetDos struct {
	CounterList []*CounterDos `thrift:"counter_list,1" json:"counter_list"`
}

func NewRetDos() *RetDos {
	return &RetDos{}
}

func (p *RetDos) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetDos) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CounterList = make([]*CounterDos, 0, size)
	for i := 0; i < size; i++ {
		_elem6 := NewCounterDos()
		if err := _elem6.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem6)
		}
		p.CounterList = append(p.CounterList, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RetDos) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RetDos"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetDos) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CounterList != nil {
		if err := oprot.WriteFieldBegin("counter_list", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:counter_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CounterList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CounterList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:counter_list: %s", p, err)
		}
	}
	return err
}

func (p *RetDos) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetDos(%+v)", *p)
}

type RetCreative struct {
	CreativeId        int32  `thrift:"creative_id,1" json:"creative_id"`
	CreativeName      string `thrift:"creative_name,2" json:"creative_name"`
	AdMatchType       int64  `thrift:"ad_match_type,3" json:"ad_match_type"`
	TotalCount        int32  `thrift:"total_count,4" json:"total_count"`
	MatchCount        int32  `thrift:"match_count,5" json:"match_count"`
	IosTotalCount     int32  `thrift:"ios_total_count,6" json:"ios_total_count"`
	IosMatchCount     int32  `thrift:"ios_match_count,7" json:"ios_match_count"`
	AndroidTotalCount int32  `thrift:"android_total_count,8" json:"android_total_count"`
	AndroidMatchCount int32  `thrift:"android_match_count,9" json:"android_match_count"`
}

func NewRetCreative() *RetCreative {
	return &RetCreative{}
}

func (p *RetCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *RetCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CreativeName = v
	}
	return nil
}

func (p *RetCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AdMatchType = v
	}
	return nil
}

func (p *RetCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *RetCreative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.MatchCount = v
	}
	return nil
}

func (p *RetCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IosTotalCount = v
	}
	return nil
}

func (p *RetCreative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.IosMatchCount = v
	}
	return nil
}

func (p *RetCreative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AndroidTotalCount = v
	}
	return nil
}

func (p *RetCreative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AndroidMatchCount = v
	}
	return nil
}

func (p *RetCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RetCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:creative_id: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:creative_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CreativeName)); err != nil {
		return fmt.Errorf("%T.creative_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:creative_name: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_match_type", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ad_match_type: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdMatchType)); err != nil {
		return fmt.Errorf("%T.ad_match_type (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ad_match_type: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:total_count: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("match_count", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MatchCount)); err != nil {
		return fmt.Errorf("%T.match_count (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:match_count: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_total_count", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ios_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosTotalCount)); err != nil {
		return fmt.Errorf("%T.ios_total_count (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ios_total_count: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_match_count", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ios_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosMatchCount)); err != nil {
		return fmt.Errorf("%T.ios_match_count (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ios_match_count: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_total_count", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:android_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidTotalCount)); err != nil {
		return fmt.Errorf("%T.android_total_count (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:android_total_count: %s", p, err)
	}
	return err
}

func (p *RetCreative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_match_count", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:android_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidMatchCount)); err != nil {
		return fmt.Errorf("%T.android_match_count (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:android_match_count: %s", p, err)
	}
	return err
}

func (p *RetCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetCreative(%+v)", *p)
}

type RetStrategy struct {
	StrategyId        int32          `thrift:"strategy_id,1" json:"strategy_id"`
	StrategyName      string         `thrift:"strategy_name,2" json:"strategy_name"`
	TotalCount        int32          `thrift:"total_count,3" json:"total_count"`
	MatchCount        int32          `thrift:"match_count,4" json:"match_count"`
	IosTotalCount     int32          `thrift:"ios_total_count,5" json:"ios_total_count"`
	IosMatchCount     int32          `thrift:"ios_match_count,6" json:"ios_match_count"`
	AndroidTotalCount int32          `thrift:"android_total_count,7" json:"android_total_count"`
	AndroidMatchCount int32          `thrift:"android_match_count,8" json:"android_match_count"`
	CreativeList      []*RetCreative `thrift:"creative_list,9" json:"creative_list"`
}

func NewRetStrategy() *RetStrategy {
	return &RetStrategy{}
}

func (p *RetStrategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetStrategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *RetStrategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.StrategyName = v
	}
	return nil
}

func (p *RetStrategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *RetStrategy) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MatchCount = v
	}
	return nil
}

func (p *RetStrategy) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IosTotalCount = v
	}
	return nil
}

func (p *RetStrategy) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IosMatchCount = v
	}
	return nil
}

func (p *RetStrategy) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AndroidTotalCount = v
	}
	return nil
}

func (p *RetStrategy) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AndroidMatchCount = v
	}
	return nil
}

func (p *RetStrategy) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CreativeList = make([]*RetCreative, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := NewRetCreative()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.CreativeList = append(p.CreativeList, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RetStrategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RetStrategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:strategy_id: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:strategy_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.StrategyName)); err != nil {
		return fmt.Errorf("%T.strategy_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:strategy_name: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:total_count: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("match_count", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MatchCount)); err != nil {
		return fmt.Errorf("%T.match_count (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:match_count: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_total_count", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ios_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosTotalCount)); err != nil {
		return fmt.Errorf("%T.ios_total_count (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ios_total_count: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_match_count", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ios_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosMatchCount)); err != nil {
		return fmt.Errorf("%T.ios_match_count (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ios_match_count: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_total_count", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:android_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidTotalCount)); err != nil {
		return fmt.Errorf("%T.android_total_count (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:android_total_count: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_match_count", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:android_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidMatchCount)); err != nil {
		return fmt.Errorf("%T.android_match_count (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:android_match_count: %s", p, err)
	}
	return err
}

func (p *RetStrategy) writeField9(oprot thrift.TProtocol) (err error) {
	if p.CreativeList != nil {
		if err := oprot.WriteFieldBegin("creative_list", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:creative_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CreativeList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CreativeList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:creative_list: %s", p, err)
		}
	}
	return err
}

func (p *RetStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetStrategy(%+v)", *p)
}

type RetCampaign struct {
	CampaignId        int32          `thrift:"campaign_id,1" json:"campaign_id"`
	CampaignName      string         `thrift:"campaign_name,2" json:"campaign_name"`
	TotalCount        int32          `thrift:"total_count,3" json:"total_count"`
	MatchCount        int32          `thrift:"match_count,4" json:"match_count"`
	IosTotalCount     int32          `thrift:"ios_total_count,5" json:"ios_total_count"`
	IosMatchCount     int32          `thrift:"ios_match_count,6" json:"ios_match_count"`
	AndroidTotalCount int32          `thrift:"android_total_count,7" json:"android_total_count"`
	AndroidMatchCount int32          `thrift:"android_match_count,8" json:"android_match_count"`
	StrategyList      []*RetStrategy `thrift:"strategy_list,9" json:"strategy_list"`
}

func NewRetCampaign() *RetCampaign {
	return &RetCampaign{}
}

func (p *RetCampaign) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetCampaign) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *RetCampaign) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CampaignName = v
	}
	return nil
}

func (p *RetCampaign) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *RetCampaign) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.MatchCount = v
	}
	return nil
}

func (p *RetCampaign) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.IosTotalCount = v
	}
	return nil
}

func (p *RetCampaign) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.IosMatchCount = v
	}
	return nil
}

func (p *RetCampaign) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AndroidTotalCount = v
	}
	return nil
}

func (p *RetCampaign) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AndroidMatchCount = v
	}
	return nil
}

func (p *RetCampaign) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.StrategyList = make([]*RetStrategy, 0, size)
	for i := 0; i < size; i++ {
		_elem8 := NewRetStrategy()
		if err := _elem8.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem8)
		}
		p.StrategyList = append(p.StrategyList, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RetCampaign) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RetCampaign"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetCampaign) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:campaign_id: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:campaign_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CampaignName)); err != nil {
		return fmt.Errorf("%T.campaign_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:campaign_name: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("total_count", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.total_count (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:total_count: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("match_count", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MatchCount)); err != nil {
		return fmt.Errorf("%T.match_count (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:match_count: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_total_count", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:ios_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosTotalCount)); err != nil {
		return fmt.Errorf("%T.ios_total_count (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:ios_total_count: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_match_count", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:ios_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosMatchCount)); err != nil {
		return fmt.Errorf("%T.ios_match_count (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:ios_match_count: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_total_count", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:android_total_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidTotalCount)); err != nil {
		return fmt.Errorf("%T.android_total_count (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:android_total_count: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_match_count", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:android_match_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidMatchCount)); err != nil {
		return fmt.Errorf("%T.android_match_count (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:android_match_count: %s", p, err)
	}
	return err
}

func (p *RetCampaign) writeField9(oprot thrift.TProtocol) (err error) {
	if p.StrategyList != nil {
		if err := oprot.WriteFieldBegin("strategy_list", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:strategy_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.StrategyList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.StrategyList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:strategy_list: %s", p, err)
		}
	}
	return err
}

func (p *RetCampaign) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetCampaign(%+v)", *p)
}

type CounterEstimation struct {
	ExchangeId   int32  `thrift:"exchange_id,1" json:"exchange_id"`
	ExchangeName string `thrift:"exchange_name,2" json:"exchange_name"`
	IosCount     int32  `thrift:"ios_count,3" json:"ios_count"`
	AndroidCount int32  `thrift:"android_count,4" json:"android_count"`
}

func NewCounterEstimation() *CounterEstimation {
	return &CounterEstimation{}
}

func (p *CounterEstimation) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *CounterEstimation) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *CounterEstimation) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ExchangeName = v
	}
	return nil
}

func (p *CounterEstimation) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.IosCount = v
	}
	return nil
}

func (p *CounterEstimation) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AndroidCount = v
	}
	return nil
}

func (p *CounterEstimation) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("CounterEstimation"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *CounterEstimation) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:exchange_id: %s", p, err)
	}
	return err
}

func (p *CounterEstimation) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:exchange_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ExchangeName)); err != nil {
		return fmt.Errorf("%T.exchange_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:exchange_name: %s", p, err)
	}
	return err
}

func (p *CounterEstimation) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ios_count", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ios_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IosCount)); err != nil {
		return fmt.Errorf("%T.ios_count (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ios_count: %s", p, err)
	}
	return err
}

func (p *CounterEstimation) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("android_count", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:android_count: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AndroidCount)); err != nil {
		return fmt.Errorf("%T.android_count (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:android_count: %s", p, err)
	}
	return err
}

func (p *CounterEstimation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("CounterEstimation(%+v)", *p)
}

type RetEstimation struct {
	CounterList []*CounterEstimation `thrift:"counter_list,1" json:"counter_list"`
}

func NewRetEstimation() *RetEstimation {
	return &RetEstimation{}
}

func (p *RetEstimation) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RetEstimation) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CounterList = make([]*CounterEstimation, 0, size)
	for i := 0; i < size; i++ {
		_elem9 := NewCounterEstimation()
		if err := _elem9.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem9)
		}
		p.CounterList = append(p.CounterList, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *RetEstimation) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RetEstimation"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RetEstimation) writeField1(oprot thrift.TProtocol) (err error) {
	if p.CounterList != nil {
		if err := oprot.WriteFieldBegin("counter_list", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:counter_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CounterList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CounterList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:counter_list: %s", p, err)
		}
	}
	return err
}

func (p *RetEstimation) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RetEstimation(%+v)", *p)
}
