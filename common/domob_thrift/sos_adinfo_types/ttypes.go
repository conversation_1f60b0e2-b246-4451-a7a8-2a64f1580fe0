// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_adinfo_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type PackageStatus int64

const (
	PackageStatus_WELL     PackageStatus = 0
	PackageStatus_DELETEED PackageStatus = 1
)

func (p PackageStatus) String() string {
	switch p {
	case PackageStatus_WELL:
		return "PackageStatus_WELL"
	case PackageStatus_DELETEED:
		return "PackageStatus_DELETEED"
	}
	return "<UNSET>"
}

func PackageStatusFromString(s string) (PackageStatus, error) {
	switch s {
	case "PackageStatus_WELL":
		return PackageStatus_WELL, nil
	case "PackageStatus_DELETEED":
		return PackageStatus_DELETEED, nil
	}
	return PackageStatus(math.MinInt32 - 1), fmt.<PERSON><PERSON><PERSON>("not a valid PackageStatus string")
}

type OperationType int64

const (
	OperationType_UPDATE OperationType = 1
	OperationType_CREATE OperationType = 2
	OperationType_DELETE OperationType = 3
)

func (p OperationType) String() string {
	switch p {
	case OperationType_UPDATE:
		return "OperationType_UPDATE"
	case OperationType_CREATE:
		return "OperationType_CREATE"
	case OperationType_DELETE:
		return "OperationType_DELETE"
	}
	return "<UNSET>"
}

func OperationTypeFromString(s string) (OperationType, error) {
	switch s {
	case "OperationType_UPDATE":
		return OperationType_UPDATE, nil
	case "OperationType_CREATE":
		return OperationType_CREATE, nil
	case "OperationType_DELETE":
		return OperationType_DELETE, nil
	}
	return OperationType(math.MinInt32 - 1), fmt.Errorf("not a valid OperationType string")
}

type AndriodPackage struct {
	Id           int32         `thrift:"id,1" json:"id"`
	DisplayName  string        `thrift:"display_name,2" json:"display_name"`
	Size         int32         `thrift:"size,3" json:"size"`
	AdminId      int32         `thrift:"admin_id,4" json:"admin_id"`
	AppVersion   string        `thrift:"app_version,5" json:"app_version"`
	Logo         string        `thrift:"logo,6" json:"logo"`
	PkgUrl       string        `thrift:"pkg_url,7" json:"pkg_url"`
	SalesChannel int8          `thrift:"sales_channel,8" json:"sales_channel"`
	SpPrice      int32         `thrift:"sp_price,9" json:"sp_price"`
	VersionCode  int32         `thrift:"version_code,10" json:"version_code"`
	SponsorId    int32         `thrift:"sponsor_id,11" json:"sponsor_id"`
	PkgName      string        `thrift:"pkg_name,12" json:"pkg_name"`
	Appid        string        `thrift:"appid,13" json:"appid"`
	Status       PackageStatus `thrift:"status,14" json:"status"`
	ScreenPics   string        `thrift:"screen_pics,15" json:"screen_pics"`
	ShortDesc    string        `thrift:"short_desc,16" json:"short_desc"`
	Desc         string        `thrift:"desc,17" json:"desc"`
	Starttime    int32         `thrift:"starttime,18" json:"starttime"`
	Endtime      int32         `thrift:"endtime,19" json:"endtime"`
	Budget       int32         `thrift:"budget,20" json:"budget"`
	Dailybudget  int32         `thrift:"dailybudget,21" json:"dailybudget"`
	AppName      string        `thrift:"app_name,22" json:"app_name"`
}

func NewAndriodPackage() *AndriodPackage {
	return &AndriodPackage{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AndriodPackage) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AndriodPackage) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.BYTE {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I32 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I32 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AndriodPackage) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AndriodPackage) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.DisplayName = v
	}
	return nil
}

func (p *AndriodPackage) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Size = v
	}
	return nil
}

func (p *AndriodPackage) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdminId = v
	}
	return nil
}

func (p *AndriodPackage) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AppVersion = v
	}
	return nil
}

func (p *AndriodPackage) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Logo = v
	}
	return nil
}

func (p *AndriodPackage) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PkgUrl = v
	}
	return nil
}

func (p *AndriodPackage) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadByte(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SalesChannel = int8(v)
	}
	return nil
}

func (p *AndriodPackage) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *AndriodPackage) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.VersionCode = v
	}
	return nil
}

func (p *AndriodPackage) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AndriodPackage) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *AndriodPackage) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *AndriodPackage) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Status = PackageStatus(v)
	}
	return nil
}

func (p *AndriodPackage) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.ScreenPics = v
	}
	return nil
}

func (p *AndriodPackage) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ShortDesc = v
	}
	return nil
}

func (p *AndriodPackage) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Desc = v
	}
	return nil
}

func (p *AndriodPackage) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Starttime = v
	}
	return nil
}

func (p *AndriodPackage) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Endtime = v
	}
	return nil
}

func (p *AndriodPackage) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Budget = v
	}
	return nil
}

func (p *AndriodPackage) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Dailybudget = v
	}
	return nil
}

func (p *AndriodPackage) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *AndriodPackage) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AndriodPackage"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AndriodPackage) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("display_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:display_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayName)); err != nil {
		return fmt.Errorf("%T.display_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:display_name: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:size: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("admin_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:admin_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdminId)); err != nil {
		return fmt.Errorf("%T.admin_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:admin_id: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_version", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:app_version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppVersion)); err != nil {
		return fmt.Errorf("%T.app_version (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:app_version: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("logo", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:logo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Logo)); err != nil {
		return fmt.Errorf("%T.logo (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:logo: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_url", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:pkg_url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgUrl)); err != nil {
		return fmt.Errorf("%T.pkg_url (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:pkg_url: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sales_channel", thrift.BYTE, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sales_channel: %s", p, err)
	}
	if err := oprot.WriteByte(byte(p.SalesChannel)); err != nil {
		return fmt.Errorf("%T.sales_channel (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sales_channel: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:sp_price: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:sp_price: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:version_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.version_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:version_code: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:sponsor_id: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_name", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkg_name (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:pkg_name: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:appid: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField14(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (14) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:status: %s", p, err)
		}
	}
	return err
}

func (p *AndriodPackage) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("screen_pics", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:screen_pics: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ScreenPics)); err != nil {
		return fmt.Errorf("%T.screen_pics (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:screen_pics: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("short_desc", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:short_desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ShortDesc)); err != nil {
		return fmt.Errorf("%T.short_desc (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:short_desc: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("desc", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:desc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Desc)); err != nil {
		return fmt.Errorf("%T.desc (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:desc: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("starttime", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:starttime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Starttime)); err != nil {
		return fmt.Errorf("%T.starttime (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:starttime: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endtime", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:endtime: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Endtime)); err != nil {
		return fmt.Errorf("%T.endtime (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:endtime: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I32, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:budget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:budget: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailybudget", thrift.I32, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:dailybudget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Dailybudget)); err != nil {
		return fmt.Errorf("%T.dailybudget (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:dailybudget: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_name", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.app_name (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:app_name: %s", p, err)
	}
	return err
}

func (p *AndriodPackage) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AndriodPackage(%+v)", *p)
}

type PackageNotify struct {
	PackageBody *AndriodPackage `thrift:"package_body,1" json:"package_body"`
	TypeA1      OperationType   `thrift:"type,2" json:"type"`
	OpTime      int32           `thrift:"op_time,3" json:"op_time"`
}

func NewPackageNotify() *PackageNotify {
	return &PackageNotify{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PackageNotify) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *PackageNotify) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PackageNotify) readField1(iprot thrift.TProtocol) error {
	p.PackageBody = NewAndriodPackage()
	if err := p.PackageBody.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PackageBody)
	}
	return nil
}

func (p *PackageNotify) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = OperationType(v)
	}
	return nil
}

func (p *PackageNotify) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.OpTime = v
	}
	return nil
}

func (p *PackageNotify) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PackageNotify"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PackageNotify) writeField1(oprot thrift.TProtocol) (err error) {
	if p.PackageBody != nil {
		if err := oprot.WriteFieldBegin("package_body", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:package_body: %s", p, err)
		}
		if err := p.PackageBody.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PackageBody)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:package_body: %s", p, err)
		}
	}
	return err
}

func (p *PackageNotify) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *PackageNotify) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("op_time", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:op_time: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OpTime)); err != nil {
		return fmt.Errorf("%T.op_time (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:op_time: %s", p, err)
	}
	return err
}

func (p *PackageNotify) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PackageNotify(%+v)", *p)
}
