// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package creative_assembly_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/programmatic_creative_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var _ = enums.GoUnusedProtection__
var _ = programmatic_creative_types.GoUnusedProtection__

type CreativeAssemblyServer interface {
	dm303.DomobService

	// Parameters:
	//  - Header
	//  - AppMaterialRequest
	//  - CreativeFilter
	GetCreatives(header *common.RequestHeader, app_material_request *programmatic_creative_types.AppMaterialRequest, creative_filter *programmatic_creative_types.CreativeFilter) (r *programmatic_creative_types.CreativeResultList, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - HtmlCode
	//  - Width
	//  - Height
	//  - FileFormat
	//  - ResultType
	//  - Params
	ConvertHtml2Image(header *common.RequestHeader, html_code string, width int32, height int32, file_format programmatic_creative_types.PCHTML2ImageFileFormat, result_type programmatic_creative_types.PCHTML2ImageResultType, params map[string]string) (r *programmatic_creative_types.Html2ImageResult, rae *programmatic_creative_types.PCSException, err error)
	// Parameters:
	//  - Header
	//  - CanvasSize
	//  - EleList
	//  - Params
	AssembleImageCreative(header *common.RequestHeader, canvas_size *programmatic_creative_types.PCSSize, ele_list []*programmatic_creative_types.PCAssemblyImageElementInfo, params map[string]string) (r *programmatic_creative_types.PCAssemblyImageResult, rae *programmatic_creative_types.PCSException, err error)
}

type CreativeAssemblyServerClient struct {
	*dm303.DomobServiceClient
}

func NewCreativeAssemblyServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *CreativeAssemblyServerClient {
	return &CreativeAssemblyServerClient{DomobServiceClient: dm303.NewDomobServiceClientFactory(t, f)}
}

func NewCreativeAssemblyServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *CreativeAssemblyServerClient {
	return &CreativeAssemblyServerClient{DomobServiceClient: dm303.NewDomobServiceClientProtocol(t, iprot, oprot)}
}

// Parameters:
//  - Header
//  - AppMaterialRequest
//  - CreativeFilter
func (p *CreativeAssemblyServerClient) GetCreatives(header *common.RequestHeader, app_material_request *programmatic_creative_types.AppMaterialRequest, creative_filter *programmatic_creative_types.CreativeFilter) (r *programmatic_creative_types.CreativeResultList, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendGetCreatives(header, app_material_request, creative_filter); err != nil {
		return
	}
	return p.recvGetCreatives()
}

func (p *CreativeAssemblyServerClient) sendGetCreatives(header *common.RequestHeader, app_material_request *programmatic_creative_types.AppMaterialRequest, creative_filter *programmatic_creative_types.CreativeFilter) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getCreatives", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetCreativesArgs()
	args0.Header = header
	args0.AppMaterialRequest = app_material_request
	args0.CreativeFilter = creative_filter
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CreativeAssemblyServerClient) recvGetCreatives() (value *programmatic_creative_types.CreativeResultList, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetCreativesResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.Rae != nil {
		rae = result1.Rae
	}
	return
}

// Parameters:
//  - Header
//  - HtmlCode
//  - Width
//  - Height
//  - FileFormat
//  - ResultType
//  - Params
func (p *CreativeAssemblyServerClient) ConvertHtml2Image(header *common.RequestHeader, html_code string, width int32, height int32, file_format programmatic_creative_types.PCHTML2ImageFileFormat, result_type programmatic_creative_types.PCHTML2ImageResultType, params map[string]string) (r *programmatic_creative_types.Html2ImageResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendConvertHtml2Image(header, html_code, width, height, file_format, result_type, params); err != nil {
		return
	}
	return p.recvConvertHtml2Image()
}

func (p *CreativeAssemblyServerClient) sendConvertHtml2Image(header *common.RequestHeader, html_code string, width int32, height int32, file_format programmatic_creative_types.PCHTML2ImageFileFormat, result_type programmatic_creative_types.PCHTML2ImageResultType, params map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("convertHtml2Image", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewConvertHtml2ImageArgs()
	args4.Header = header
	args4.HtmlCode = html_code
	args4.Width = width
	args4.Height = height
	args4.FileFormat = file_format
	args4.ResultType = result_type
	args4.Params = params
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CreativeAssemblyServerClient) recvConvertHtml2Image() (value *programmatic_creative_types.Html2ImageResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewConvertHtml2ImageResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.Rae != nil {
		rae = result5.Rae
	}
	return
}

// Parameters:
//  - Header
//  - CanvasSize
//  - EleList
//  - Params
func (p *CreativeAssemblyServerClient) AssembleImageCreative(header *common.RequestHeader, canvas_size *programmatic_creative_types.PCSSize, ele_list []*programmatic_creative_types.PCAssemblyImageElementInfo, params map[string]string) (r *programmatic_creative_types.PCAssemblyImageResult, rae *programmatic_creative_types.PCSException, err error) {
	if err = p.sendAssembleImageCreative(header, canvas_size, ele_list, params); err != nil {
		return
	}
	return p.recvAssembleImageCreative()
}

func (p *CreativeAssemblyServerClient) sendAssembleImageCreative(header *common.RequestHeader, canvas_size *programmatic_creative_types.PCSSize, ele_list []*programmatic_creative_types.PCAssemblyImageElementInfo, params map[string]string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("assembleImageCreative", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewAssembleImageCreativeArgs()
	args8.Header = header
	args8.CanvasSize = canvas_size
	args8.EleList = ele_list
	args8.Params = params
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *CreativeAssemblyServerClient) recvAssembleImageCreative() (value *programmatic_creative_types.PCAssemblyImageResult, rae *programmatic_creative_types.PCSException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewAssembleImageCreativeResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result9.Success
	if result9.Rae != nil {
		rae = result9.Rae
	}
	return
}

type CreativeAssemblyServerProcessor struct {
	*dm303.DomobServiceProcessor
}

func NewCreativeAssemblyServerProcessor(handler CreativeAssemblyServer) *CreativeAssemblyServerProcessor {
	self12 := &CreativeAssemblyServerProcessor{dm303.NewDomobServiceProcessor(handler)}
	self12.AddToProcessorMap("getCreatives", &creativeAssemblyServerProcessorGetCreatives{handler: handler})
	self12.AddToProcessorMap("convertHtml2Image", &creativeAssemblyServerProcessorConvertHtml2Image{handler: handler})
	self12.AddToProcessorMap("assembleImageCreative", &creativeAssemblyServerProcessorAssembleImageCreative{handler: handler})
	return self12
}

type creativeAssemblyServerProcessorGetCreatives struct {
	handler CreativeAssemblyServer
}

func (p *creativeAssemblyServerProcessorGetCreatives) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetCreativesArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getCreatives", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetCreativesResult()
	if result.Success, result.Rae, err = p.handler.GetCreatives(args.Header, args.AppMaterialRequest, args.CreativeFilter); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getCreatives: "+err.Error())
		oprot.WriteMessageBegin("getCreatives", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getCreatives", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type creativeAssemblyServerProcessorConvertHtml2Image struct {
	handler CreativeAssemblyServer
}

func (p *creativeAssemblyServerProcessorConvertHtml2Image) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewConvertHtml2ImageArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("convertHtml2Image", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewConvertHtml2ImageResult()
	if result.Success, result.Rae, err = p.handler.ConvertHtml2Image(args.Header, args.HtmlCode, args.Width, args.Height, args.FileFormat, args.ResultType, args.Params); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing convertHtml2Image: "+err.Error())
		oprot.WriteMessageBegin("convertHtml2Image", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("convertHtml2Image", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type creativeAssemblyServerProcessorAssembleImageCreative struct {
	handler CreativeAssemblyServer
}

func (p *creativeAssemblyServerProcessorAssembleImageCreative) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAssembleImageCreativeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("assembleImageCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAssembleImageCreativeResult()
	if result.Success, result.Rae, err = p.handler.AssembleImageCreative(args.Header, args.CanvasSize, args.EleList, args.Params); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing assembleImageCreative: "+err.Error())
		oprot.WriteMessageBegin("assembleImageCreative", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("assembleImageCreative", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetCreativesArgs struct {
	Header             *common.RequestHeader                           `thrift:"header,1" json:"header"`
	AppMaterialRequest *programmatic_creative_types.AppMaterialRequest `thrift:"app_material_request,2" json:"app_material_request"`
	CreativeFilter     *programmatic_creative_types.CreativeFilter     `thrift:"creative_filter,3" json:"creative_filter"`
}

func NewGetCreativesArgs() *GetCreativesArgs {
	return &GetCreativesArgs{}
}

func (p *GetCreativesArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetCreativesArgs) readField2(iprot thrift.TProtocol) error {
	p.AppMaterialRequest = programmatic_creative_types.NewAppMaterialRequest()
	if err := p.AppMaterialRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AppMaterialRequest)
	}
	return nil
}

func (p *GetCreativesArgs) readField3(iprot thrift.TProtocol) error {
	p.CreativeFilter = programmatic_creative_types.NewCreativeFilter()
	if err := p.CreativeFilter.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CreativeFilter)
	}
	return nil
}

func (p *GetCreativesArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCreatives_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.AppMaterialRequest != nil {
		if err := oprot.WriteFieldBegin("app_material_request", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:app_material_request: %s", p, err)
		}
		if err := p.AppMaterialRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AppMaterialRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:app_material_request: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.CreativeFilter != nil {
		if err := oprot.WriteFieldBegin("creative_filter", thrift.STRUCT, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:creative_filter: %s", p, err)
		}
		if err := p.CreativeFilter.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CreativeFilter)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:creative_filter: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCreativesArgs(%+v)", *p)
}

type GetCreativesResult struct {
	Success *programmatic_creative_types.CreativeResultList `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException       `thrift:"rae,1" json:"rae"`
}

func NewGetCreativesResult() *GetCreativesResult {
	return &GetCreativesResult{}
}

func (p *GetCreativesResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewCreativeResultList()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *GetCreativesResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *GetCreativesResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getCreatives_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetCreativesResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *GetCreativesResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetCreativesResult(%+v)", *p)
}

type ConvertHtml2ImageArgs struct {
	Header     *common.RequestHeader                              `thrift:"header,1" json:"header"`
	HtmlCode   string                                             `thrift:"html_code,2" json:"html_code"`
	Width      int32                                              `thrift:"width,3" json:"width"`
	Height     int32                                              `thrift:"height,4" json:"height"`
	FileFormat programmatic_creative_types.PCHTML2ImageFileFormat `thrift:"file_format,5" json:"file_format"`
	ResultType programmatic_creative_types.PCHTML2ImageResultType `thrift:"result_type,6" json:"result_type"`
	Params     map[string]string                                  `thrift:"params,7" json:"params"`
}

func NewConvertHtml2ImageArgs() *ConvertHtml2ImageArgs {
	return &ConvertHtml2ImageArgs{
		FileFormat: math.MinInt32 - 1, // unset sentinal value

		ResultType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *ConvertHtml2ImageArgs) IsSetFileFormat() bool {
	return int64(p.FileFormat) != math.MinInt32-1
}

func (p *ConvertHtml2ImageArgs) IsSetResultType() bool {
	return int64(p.ResultType) != math.MinInt32-1
}

func (p *ConvertHtml2ImageArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.MAP {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.HtmlCode = v
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Width = v
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Height = v
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.FileFormat = programmatic_creative_types.PCHTML2ImageFileFormat(v)
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ResultType = programmatic_creative_types.PCHTML2ImageResultType(v)
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) readField7(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Params = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key13 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key13 = v
		}
		var _val14 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val14 = v
		}
		p.Params[_key13] = _val14
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("convertHtml2Image_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConvertHtml2ImageArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtml2ImageArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("html_code", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:html_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.HtmlCode)); err != nil {
		return fmt.Errorf("%T.html_code (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:html_code: %s", p, err)
	}
	return err
}

func (p *ConvertHtml2ImageArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("width", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:width: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Width)); err != nil {
		return fmt.Errorf("%T.width (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:width: %s", p, err)
	}
	return err
}

func (p *ConvertHtml2ImageArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("height", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:height: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Height)); err != nil {
		return fmt.Errorf("%T.height (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:height: %s", p, err)
	}
	return err
}

func (p *ConvertHtml2ImageArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetFileFormat() {
		if err := oprot.WriteFieldBegin("file_format", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:file_format: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.FileFormat)); err != nil {
			return fmt.Errorf("%T.file_format (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:file_format: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtml2ImageArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetResultType() {
		if err := oprot.WriteFieldBegin("result_type", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:result_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.ResultType)); err != nil {
			return fmt.Errorf("%T.result_type (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:result_type: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtml2ImageArgs) writeField7(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.MAP, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:params: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Params)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Params {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:params: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtml2ImageArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertHtml2ImageArgs(%+v)", *p)
}

type ConvertHtml2ImageResult struct {
	Success *programmatic_creative_types.Html2ImageResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException     `thrift:"rae,1" json:"rae"`
}

func NewConvertHtml2ImageResult() *ConvertHtml2ImageResult {
	return &ConvertHtml2ImageResult{}
}

func (p *ConvertHtml2ImageResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *ConvertHtml2ImageResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewHtml2ImageResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *ConvertHtml2ImageResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *ConvertHtml2ImageResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("convertHtml2Image_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *ConvertHtml2ImageResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtml2ImageResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *ConvertHtml2ImageResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("ConvertHtml2ImageResult(%+v)", *p)
}

type AssembleImageCreativeArgs struct {
	Header     *common.RequestHeader                                     `thrift:"header,1" json:"header"`
	CanvasSize *programmatic_creative_types.PCSSize                      `thrift:"canvas_size,2" json:"canvas_size"`
	EleList    []*programmatic_creative_types.PCAssemblyImageElementInfo `thrift:"ele_list,3" json:"ele_list"`
	Params     map[string]string                                         `thrift:"params,4" json:"params"`
}

func NewAssembleImageCreativeArgs() *AssembleImageCreativeArgs {
	return &AssembleImageCreativeArgs{}
}

func (p *AssembleImageCreativeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.MAP {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AssembleImageCreativeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AssembleImageCreativeArgs) readField2(iprot thrift.TProtocol) error {
	p.CanvasSize = programmatic_creative_types.NewPCSSize()
	if err := p.CanvasSize.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.CanvasSize)
	}
	return nil
}

func (p *AssembleImageCreativeArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.EleList = make([]*programmatic_creative_types.PCAssemblyImageElementInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem15 := programmatic_creative_types.NewPCAssemblyImageElementInfo()
		if err := _elem15.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem15)
		}
		p.EleList = append(p.EleList, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AssembleImageCreativeArgs) readField4(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Params = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key16 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key16 = v
		}
		var _val17 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val17 = v
		}
		p.Params[_key16] = _val17
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AssembleImageCreativeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("assembleImageCreative_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AssembleImageCreativeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AssembleImageCreativeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.CanvasSize != nil {
		if err := oprot.WriteFieldBegin("canvas_size", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:canvas_size: %s", p, err)
		}
		if err := p.CanvasSize.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.CanvasSize)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:canvas_size: %s", p, err)
		}
	}
	return err
}

func (p *AssembleImageCreativeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.EleList != nil {
		if err := oprot.WriteFieldBegin("ele_list", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:ele_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.EleList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.EleList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:ele_list: %s", p, err)
		}
	}
	return err
}

func (p *AssembleImageCreativeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if p.Params != nil {
		if err := oprot.WriteFieldBegin("params", thrift.MAP, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:params: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.Params)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Params {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:params: %s", p, err)
		}
	}
	return err
}

func (p *AssembleImageCreativeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssembleImageCreativeArgs(%+v)", *p)
}

type AssembleImageCreativeResult struct {
	Success *programmatic_creative_types.PCAssemblyImageResult `thrift:"success,0" json:"success"`
	Rae     *programmatic_creative_types.PCSException          `thrift:"rae,1" json:"rae"`
}

func NewAssembleImageCreativeResult() *AssembleImageCreativeResult {
	return &AssembleImageCreativeResult{}
}

func (p *AssembleImageCreativeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AssembleImageCreativeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = programmatic_creative_types.NewPCAssemblyImageResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AssembleImageCreativeResult) readField1(iprot thrift.TProtocol) error {
	p.Rae = programmatic_creative_types.NewPCSException()
	if err := p.Rae.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Rae)
	}
	return nil
}

func (p *AssembleImageCreativeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("assembleImageCreative_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.Rae != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AssembleImageCreativeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AssembleImageCreativeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Rae != nil {
		if err := oprot.WriteFieldBegin("rae", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:rae: %s", p, err)
		}
		if err := p.Rae.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Rae)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:rae: %s", p, err)
		}
	}
	return err
}

func (p *AssembleImageCreativeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AssembleImageCreativeResult(%+v)", *p)
}
