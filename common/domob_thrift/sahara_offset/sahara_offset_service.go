// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sahara_offset

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

type SaharaOffsetService interface { ///kafka/consumers/$group/offsets/$topic/$brokerid-$partitionId

	// get offset from zookeeper
	//
	// Parameters:
	//  - Topic
	//  - Group
	GetConsumerOffset(topic string, group string) (r []*KafkaOffset, e *SaharaException, err error)
	// get offset from kafka
	//
	// Parameters:
	//  - Topic
	//  - Time
	GetTopicOffset(topic string, time int64) (r []*KafkaOffset, e *SaharaException, err error)
	// set offset on zookeeper
	//
	// Parameters:
	//  - Topic
	//  - Group
	//  - Offsets
	SetConsumerOffset(topic string, group string, offsets []*KafkaOffset) (e *SaharaException, err error)
}

///kafka/consumers/$group/offsets/$topic/$brokerid-$partitionId
type SaharaOffsetServiceClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewSaharaOffsetServiceClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *SaharaOffsetServiceClient {
	return &SaharaOffsetServiceClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewSaharaOffsetServiceClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *SaharaOffsetServiceClient {
	return &SaharaOffsetServiceClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// get offset from zookeeper
//
// Parameters:
//  - Topic
//  - Group
func (p *SaharaOffsetServiceClient) GetConsumerOffset(topic string, group string) (r []*KafkaOffset, e *SaharaException, err error) {
	if err = p.sendGetConsumerOffset(topic, group); err != nil {
		return
	}
	return p.recvGetConsumerOffset()
}

func (p *SaharaOffsetServiceClient) sendGetConsumerOffset(topic string, group string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getConsumerOffset", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args0 := NewGetConsumerOffsetArgs()
	args0.Topic = topic
	args0.Group = group
	if err = args0.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *SaharaOffsetServiceClient) recvGetConsumerOffset() (value []*KafkaOffset, e *SaharaException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error2 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error3 error
		error3, err = error2.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error3
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result1 := NewGetConsumerOffsetResult()
	if err = result1.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result1.Success
	if result1.E != nil {
		e = result1.E
	}
	return
}

// get offset from kafka
//
// Parameters:
//  - Topic
//  - Time
func (p *SaharaOffsetServiceClient) GetTopicOffset(topic string, time int64) (r []*KafkaOffset, e *SaharaException, err error) {
	if err = p.sendGetTopicOffset(topic, time); err != nil {
		return
	}
	return p.recvGetTopicOffset()
}

func (p *SaharaOffsetServiceClient) sendGetTopicOffset(topic string, time int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getTopicOffset", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args4 := NewGetTopicOffsetArgs()
	args4.Topic = topic
	args4.Time = time
	if err = args4.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *SaharaOffsetServiceClient) recvGetTopicOffset() (value []*KafkaOffset, e *SaharaException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error6 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error7 error
		error7, err = error6.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error7
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result5 := NewGetTopicOffsetResult()
	if err = result5.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result5.Success
	if result5.E != nil {
		e = result5.E
	}
	return
}

// set offset on zookeeper
//
// Parameters:
//  - Topic
//  - Group
//  - Offsets
func (p *SaharaOffsetServiceClient) SetConsumerOffset(topic string, group string, offsets []*KafkaOffset) (e *SaharaException, err error) {
	if err = p.sendSetConsumerOffset(topic, group, offsets); err != nil {
		return
	}
	return p.recvSetConsumerOffset()
}

func (p *SaharaOffsetServiceClient) sendSetConsumerOffset(topic string, group string, offsets []*KafkaOffset) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("setConsumerOffset", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args8 := NewSetConsumerOffsetArgs()
	args8.Topic = topic
	args8.Group = group
	args8.Offsets = offsets
	if err = args8.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *SaharaOffsetServiceClient) recvSetConsumerOffset() (e *SaharaException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error10 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error11 error
		error11, err = error10.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error11
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result9 := NewSetConsumerOffsetResult()
	if err = result9.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result9.E != nil {
		e = result9.E
	}
	return
}

type SaharaOffsetServiceProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      SaharaOffsetService
}

func (p *SaharaOffsetServiceProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *SaharaOffsetServiceProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *SaharaOffsetServiceProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewSaharaOffsetServiceProcessor(handler SaharaOffsetService) *SaharaOffsetServiceProcessor {

	self12 := &SaharaOffsetServiceProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self12.processorMap["getConsumerOffset"] = &saharaOffsetServiceProcessorGetConsumerOffset{handler: handler}
	self12.processorMap["getTopicOffset"] = &saharaOffsetServiceProcessorGetTopicOffset{handler: handler}
	self12.processorMap["setConsumerOffset"] = &saharaOffsetServiceProcessorSetConsumerOffset{handler: handler}
	return self12
}

func (p *SaharaOffsetServiceProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x13 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x13.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x13

}

type saharaOffsetServiceProcessorGetConsumerOffset struct {
	handler SaharaOffsetService
}

func (p *saharaOffsetServiceProcessorGetConsumerOffset) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetConsumerOffsetArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getConsumerOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetConsumerOffsetResult()
	if result.Success, result.E, err = p.handler.GetConsumerOffset(args.Topic, args.Group); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getConsumerOffset: "+err.Error())
		oprot.WriteMessageBegin("getConsumerOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getConsumerOffset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type saharaOffsetServiceProcessorGetTopicOffset struct {
	handler SaharaOffsetService
}

func (p *saharaOffsetServiceProcessorGetTopicOffset) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetTopicOffsetArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getTopicOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetTopicOffsetResult()
	if result.Success, result.E, err = p.handler.GetTopicOffset(args.Topic, args.Time); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getTopicOffset: "+err.Error())
		oprot.WriteMessageBegin("getTopicOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getTopicOffset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type saharaOffsetServiceProcessorSetConsumerOffset struct {
	handler SaharaOffsetService
}

func (p *saharaOffsetServiceProcessorSetConsumerOffset) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewSetConsumerOffsetArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("setConsumerOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewSetConsumerOffsetResult()
	if result.E, err = p.handler.SetConsumerOffset(args.Topic, args.Group, args.Offsets); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing setConsumerOffset: "+err.Error())
		oprot.WriteMessageBegin("setConsumerOffset", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("setConsumerOffset", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type GetConsumerOffsetArgs struct {
	Topic string `thrift:"topic,1" json:"topic"`
	Group string `thrift:"group,2" json:"group"`
}

func NewGetConsumerOffsetArgs() *GetConsumerOffsetArgs {
	return &GetConsumerOffsetArgs{}
}

func (p *GetConsumerOffsetArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetConsumerOffsetArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Topic = v
	}
	return nil
}

func (p *GetConsumerOffsetArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Group = v
	}
	return nil
}

func (p *GetConsumerOffsetArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getConsumerOffset_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetConsumerOffsetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topic", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topic: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Topic)); err != nil {
		return fmt.Errorf("%T.topic (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topic: %s", p, err)
	}
	return err
}

func (p *GetConsumerOffsetArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("group", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:group: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Group)); err != nil {
		return fmt.Errorf("%T.group (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:group: %s", p, err)
	}
	return err
}

func (p *GetConsumerOffsetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetConsumerOffsetArgs(%+v)", *p)
}

type GetConsumerOffsetResult struct {
	Success []*KafkaOffset   `thrift:"success,0" json:"success"`
	E       *SaharaException `thrift:"e,1" json:"e"`
}

func NewGetConsumerOffsetResult() *GetConsumerOffsetResult {
	return &GetConsumerOffsetResult{}
}

func (p *GetConsumerOffsetResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetConsumerOffsetResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*KafkaOffset, 0, size)
	for i := 0; i < size; i++ {
		_elem14 := NewKafkaOffset()
		if err := _elem14.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem14)
		}
		p.Success = append(p.Success, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetConsumerOffsetResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewSaharaException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetConsumerOffsetResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getConsumerOffset_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetConsumerOffsetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetConsumerOffsetResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetConsumerOffsetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetConsumerOffsetResult(%+v)", *p)
}

type GetTopicOffsetArgs struct {
	Topic string `thrift:"topic,1" json:"topic"`
	Time  int64  `thrift:"time,2" json:"time"`
}

func NewGetTopicOffsetArgs() *GetTopicOffsetArgs {
	return &GetTopicOffsetArgs{}
}

func (p *GetTopicOffsetArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicOffsetArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Topic = v
	}
	return nil
}

func (p *GetTopicOffsetArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *GetTopicOffsetArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopicOffset_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicOffsetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topic", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topic: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Topic)); err != nil {
		return fmt.Errorf("%T.topic (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topic: %s", p, err)
	}
	return err
}

func (p *GetTopicOffsetArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:time: %s", p, err)
	}
	return err
}

func (p *GetTopicOffsetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicOffsetArgs(%+v)", *p)
}

type GetTopicOffsetResult struct {
	Success []*KafkaOffset   `thrift:"success,0" json:"success"`
	E       *SaharaException `thrift:"e,1" json:"e"`
}

func NewGetTopicOffsetResult() *GetTopicOffsetResult {
	return &GetTopicOffsetResult{}
}

func (p *GetTopicOffsetResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.LIST {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetTopicOffsetResult) readField0(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Success = make([]*KafkaOffset, 0, size)
	for i := 0; i < size; i++ {
		_elem15 := NewKafkaOffset()
		if err := _elem15.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem15)
		}
		p.Success = append(p.Success, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetTopicOffsetResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewSaharaException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetTopicOffsetResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getTopicOffset_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetTopicOffsetResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.LIST, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Success {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicOffsetResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetTopicOffsetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetTopicOffsetResult(%+v)", *p)
}

type SetConsumerOffsetArgs struct {
	Topic   string         `thrift:"topic,1" json:"topic"`
	Group   string         `thrift:"group,2" json:"group"`
	Offsets []*KafkaOffset `thrift:"offsets,3" json:"offsets"`
}

func NewSetConsumerOffsetArgs() *SetConsumerOffsetArgs {
	return &SetConsumerOffsetArgs{}
}

func (p *SetConsumerOffsetArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetConsumerOffsetArgs) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Topic = v
	}
	return nil
}

func (p *SetConsumerOffsetArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Group = v
	}
	return nil
}

func (p *SetConsumerOffsetArgs) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Offsets = make([]*KafkaOffset, 0, size)
	for i := 0; i < size; i++ {
		_elem16 := NewKafkaOffset()
		if err := _elem16.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem16)
		}
		p.Offsets = append(p.Offsets, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SetConsumerOffsetArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setConsumerOffset_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetConsumerOffsetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topic", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:topic: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Topic)); err != nil {
		return fmt.Errorf("%T.topic (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:topic: %s", p, err)
	}
	return err
}

func (p *SetConsumerOffsetArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("group", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:group: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Group)); err != nil {
		return fmt.Errorf("%T.group (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:group: %s", p, err)
	}
	return err
}

func (p *SetConsumerOffsetArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if p.Offsets != nil {
		if err := oprot.WriteFieldBegin("offsets", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:offsets: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Offsets)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Offsets {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:offsets: %s", p, err)
		}
	}
	return err
}

func (p *SetConsumerOffsetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetConsumerOffsetArgs(%+v)", *p)
}

type SetConsumerOffsetResult struct {
	E *SaharaException `thrift:"e,1" json:"e"`
}

func NewSetConsumerOffsetResult() *SetConsumerOffsetResult {
	return &SetConsumerOffsetResult{}
}

func (p *SetConsumerOffsetResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SetConsumerOffsetResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewSaharaException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *SetConsumerOffsetResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("setConsumerOffset_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SetConsumerOffsetResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *SetConsumerOffsetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SetConsumerOffsetResult(%+v)", *p)
}
