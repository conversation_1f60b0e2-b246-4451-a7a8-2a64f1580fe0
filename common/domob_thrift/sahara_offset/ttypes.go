// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sahara_offset

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type SaharaException struct {
	Message string `thrift:"message,1" json:"message"`
}

func NewSaharaException() *SaharaException {
	return &SaharaException{}
}

func (p *SaharaException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SaharaException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *SaharaException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SaharaException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SaharaException) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:message: %s", p, err)
	}
	return err
}

func (p *SaharaException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SaharaException(%+v)", *p)
}

type KafkaOffset struct {
	Brokerid      int32  `thrift:"brokerid,1" json:"brokerid"`
	PartitionId   int32  `thrift:"partitionId,2" json:"partitionId"`
	Topic         string `thrift:"topic,3" json:"topic"`
	ConsumerGroup string `thrift:"consumer_group,4" json:"consumer_group"`
	Offset        int64  `thrift:"offset,5" json:"offset"`
}

func NewKafkaOffset() *KafkaOffset {
	return &KafkaOffset{}
}

func (p *KafkaOffset) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *KafkaOffset) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Brokerid = v
	}
	return nil
}

func (p *KafkaOffset) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PartitionId = v
	}
	return nil
}

func (p *KafkaOffset) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Topic = v
	}
	return nil
}

func (p *KafkaOffset) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.ConsumerGroup = v
	}
	return nil
}

func (p *KafkaOffset) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *KafkaOffset) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("KafkaOffset"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *KafkaOffset) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brokerid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:brokerid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Brokerid)); err != nil {
		return fmt.Errorf("%T.brokerid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:brokerid: %s", p, err)
	}
	return err
}

func (p *KafkaOffset) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("partitionId", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:partitionId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PartitionId)); err != nil {
		return fmt.Errorf("%T.partitionId (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:partitionId: %s", p, err)
	}
	return err
}

func (p *KafkaOffset) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("topic", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:topic: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Topic)); err != nil {
		return fmt.Errorf("%T.topic (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:topic: %s", p, err)
	}
	return err
}

func (p *KafkaOffset) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consumer_group", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:consumer_group: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ConsumerGroup)); err != nil {
		return fmt.Errorf("%T.consumer_group (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:consumer_group: %s", p, err)
	}
	return err
}

func (p *KafkaOffset) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *KafkaOffset) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("KafkaOffset(%+v)", *p)
}
